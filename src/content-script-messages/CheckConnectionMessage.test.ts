import ContentScriptMessage from './ContentScriptMessage';
import CheckConnectionMessage from './CheckConnectionMessage';

describe('CheckConnectionMessage', () => {
  it('is successfully loaded by the base class', () => {
    // Ensure the class is registered in case some other test has unregistered it by mistake.
    ContentScriptMessage.registerMessageType(CheckConnectionMessage);

    const message = CheckConnectionMessage.load({ type: 'check-connection' });
    expect(message).toBeInstanceOf(CheckConnectionMessage);
  });

  // -----------------------------------------------------------------------------------------------

  describe('constructor()', () => {
    it('sets message type name to check-connection', () => {
      const message = new CheckConnectionMessage();
      expect(message.type).toEqual('check-connection');
    });
  });
});

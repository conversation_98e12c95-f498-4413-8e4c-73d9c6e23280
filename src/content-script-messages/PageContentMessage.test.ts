import ContentScriptMessage from './ContentScriptMessage';
import PageContentMessage from './PageContentMessage';

describe('PageContentMessage', () => {
  it('is successfully loaded by the base class', () => {
    // Ensure the class is registered in case some other test has unregistered it by mistake.
    ContentScriptMessage.registerMessageType(PageContentMessage);

    const message = PageContentMessage.load({
      type: 'page-content',
      url: 'http://example.com',
      content: 'blah blah blah',
      title: 'foobar',
    });

    expect(message).toBeInstanceOf(PageContentMessage);
    expect(message.url).toEqual('http://example.com');
    expect(message.content).toEqual('blah blah blah');
    expect(message.title).toEqual('foobar');
  });

  // -----------------------------------------------------------------------------------------------

  describe('constructor()', () => {
    it('sets message type name to page-content', () => {
      const message = new PageContentMessage();
      expect(message.type).toEqual('page-content');
    });

    it('stores the specified properties', () => {
      const message = new PageContentMessage('http://example.com', 'blah blah blah', 'foobar');
      expect(message.url).toEqual('http://example.com');
      expect(message.content).toEqual('blah blah blah');
      expect(message.title).toEqual('foobar');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('load()', () => {
    it('returns an instance of the class', () => {
      const message = PageContentMessage.load({
        type: 'page-content',
        url: 'http://example.com',
        content: 'blah blah blah',
        title: 'foobar',
      });

      expect(message).toBeInstanceOf(PageContentMessage);
    });

    it('loads properties from the specified object', () => {
      const message = PageContentMessage.load({
        type: 'page-content',
        url: 'http://example.com',
        content: 'blah blah blah',
        title: 'foobar',
      });

      expect(message.url).toEqual('http://example.com');
      expect(message.content).toEqual('blah blah blah');
      expect(message.title).toEqual('foobar');
    });

    it('throws an error if the message type is wrong', () => {
      const obj = {
        type: 'some-other-message-type',
        url: 'http://example.com',
        content: 'blah blah blah',
        title: 'foobar',
      };

      expect(() => PageContentMessage.load(obj)).toThrow();
    });

    it('throws an error if the message type is missing', () => {
      const obj = {
        url: 'http://example.com',
        content: 'blah blah blah',
        title: 'foobar',
      };

      expect(() => PageContentMessage.load(obj)).toThrow();
    });

    it('throws an error if the url property is missing', () => {
      const obj = {
        type: 'page-content',
        content: 'blah blah blah',
        title: 'foobar',
      };

      expect(() => PageContentMessage.load(obj)).toThrow();
    });

    it('throws an error if the url property is not a string', () => {
      const obj = {
        type: 'page-content',
        url: 123,
        content: 'blah blah blah',
        title: 'foobar',
      };

      expect(() => PageContentMessage.load(obj)).toThrow();
    });

    it('throws an error if the content property is missing', () => {
      const obj = {
        type: 'page-content',
        url: 'http://example.com',
        title: 'foobar',
      };

      expect(() => PageContentMessage.load(obj)).toThrow();
    });

    it('throws an error if the content property is not a string', () => {
      const obj = {
        type: 'page-content',
        url: 'http://example.com',
        content: 123,
        title: 'foobar',
      };

      expect(() => PageContentMessage.load(obj)).toThrow();
    });

    it('throws an error if the title property is missing', () => {
      const obj = {
        type: 'page-content',
        url: 'http://example.com',
      };

      expect(() => PageContentMessage.load(obj)).toThrow();
    });

    it('throws an error if the title property is not a string', () => {
      const obj = {
        type: 'page-content',
        url: 'http://example.com',
        content: 'blah blah blah',
        title: 123,
      };

      expect(() => PageContentMessage.load(obj)).toThrow();
    });
  });
});

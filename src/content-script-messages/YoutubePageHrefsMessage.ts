import ContentScriptMessage from './ContentScriptMessage';

/**
 * A message that is returned from the Content Script to the extension carrying the anchor hrefs present on the page.
 * The extension will use these hrefs to determine which are needed to be blocked by the content script. The result of this
 * decision is returned by the YoutubeBlockedIdsMessage
 */
export default class YoutubePageHrefsMessage extends ContentScriptMessage {
  public static readonly type = 'youtube-page-hrefs';

  public constructor(hrefs: string[]) {
    super(YoutubePageHrefsMessage.type);
    this.hrefs = hrefs;
  }

  /**
   * Identifies the type of message which will carry this payload.
   * This value will be stored in the base class "type" property.
   */
  public static readonly load = (obj: any): YoutubePageHrefsMessage => {
    if (obj?.type !== YoutubePageHrefsMessage.type) {
      throw new Error(`Missing or invalid message type. Expected ${YoutubePageHrefsMessage.type}.`);
    }

    if (!Array.isArray(obj?.hrefs) || (obj.hrefs.length > 0 && typeof obj.hrefs[0] !== 'string')) {
      throw new Error('Invalid YoutubePageHrefsMessage');
    }

    return new YoutubePageHrefsMessage(obj.hrefs);
  };

  /*
   * A list of a[href] values present on the Youtube page
   */
  public hrefs: string[];
}

ContentScriptMessage.registerMessageType(YoutubePageHrefsMessage);

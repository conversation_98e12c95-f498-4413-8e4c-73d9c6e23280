import ContentScriptMessage from './ContentScriptMessage';

/**
 * A message sent to the content script to enable additional Youtube handling. No parameters are required by
 * the message as the message itself is the signal.
 */
export default class YoutubeBlockedIdsMessage extends ContentScriptMessage {
  public static readonly type = 'youtube-blocked-ids';

  public constructor(blockedIds: string[]) {
    super(YoutubeBlockedIdsMessage.type);
    this.blockedIds = blockedIds;
  }

  /**
   * Identifies the type of message which will carry this payload.
   * This value will be stored in the base class "type" property.
   */
  public static readonly load = (obj: any): YoutubeBlockedIdsMessage => {
    if (obj?.type !== YoutubeBlockedIdsMessage.type) {
      throw new Error(
        `Missing or invalid message type. Expected ${YoutubeBlockedIdsMessage.type}.`,
      );
    }

    if (
      !Array.isArray(obj?.blockedIds) ||
      (obj.blockedIds.length > 0 && typeof obj.blockedIds[0] !== 'string')
    ) {
      throw new Error('Invalid YoutubeBlockedIdsMessage');
    }

    return new YoutubeBlockedIdsMessage(obj.blockedIds);
  };

  public blockedIds: string[];
}

ContentScriptMessage.registerMessageType(YoutubeBlockedIdsMessage);

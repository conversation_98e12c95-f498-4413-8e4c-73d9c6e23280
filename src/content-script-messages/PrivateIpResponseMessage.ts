import ContentScriptMessage from './ContentScriptMessage';

/**
 * A message which contains the private IP address being reported back to the extension.
 * This is sent by the offscreen document. It's classed as a content script message because they use
 *  the same message channel, so they need to be compatible.
 *
 * @todo Probably rename the base class to something generic.
 */
export default class PrivateIpResponseMessage extends ContentScriptMessage {
  /**
   * Initialise a new instance.
   */
  public constructor(ipAddresses: string[]) {
    super(PrivateIpResponseMessage.type);
    this.ipAddresses = ipAddresses;
  }

  /**
   * Try to construct an instance of this message from a generic object.
   * @param obj The object to load properties from.
   * @returns Returns a new instance of this message, populated from the specified object.
   * @throws {Error} The specified value was not an object, or properties were missing or invalid,
   * or it specified a different message type.
   */
  public static readonly load = (obj: any): PrivateIpResponseMessage => {
    if (obj?.type !== PrivateIpResponseMessage.type) {
      throw new Error(
        `Missing or invalid message type. Expected ${PrivateIpResponseMessage.type}.`,
      );
    }

    if (
      !Array.isArray(obj?.ipAddresses) ||
      (obj.ipAddresses.length > 0 && typeof obj.ipAddresses[0] !== 'string')
    ) {
      throw new Error('Invalid PrivateIpResponseMessage');
    }

    return new PrivateIpResponseMessage(obj.ipAddresses);
  };

  /**
   * Identifies the type of message which will carry this payload.
   * This value will be stored in the base class "type" property.
   */
  public static readonly type = 'private-ip-response';

  /**
   * The private IP addresses assigned to this device.
   * This will be an empty array if the query could not find any.
   */
  public ipAddresses: string[];
}

ContentScriptMessage.registerMessageType(PrivateIpResponseMessage);

import ContentScriptMessage from './ContentScriptMessage';

/**
 * An empty message which simply acknowledges that something was received.
 * It's mainly intended to be used where a reply is technically required, but we have no practical
 *  need to send anything.
 */
export default class AcknowledgementMessage extends ContentScriptMessage {
  /**
   * Initialise a new instance.
   */
  public constructor() {
    super(AcknowledgementMessage.type);
  }

  /**
   * Identifies the type of message which will carry this payload.
   * This value will be stored in the base class "type" property.
   */
  public static readonly type = 'acknowledgement';
}

ContentScriptMessage.registerMessageType(AcknowledgementMessage);

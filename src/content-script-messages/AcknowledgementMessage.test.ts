import ContentScriptMessage from './ContentScriptMessage';
import AcknowledgementMessage from './AcknowledgementMessage';

describe('AcknowledgementMessage', () => {
  it('is successfully loaded by the base class', () => {
    // Ensure the class is registered in case some other test has unregistered it by mistake.
    ContentScriptMessage.registerMessageType(AcknowledgementMessage);

    const message = AcknowledgementMessage.load({ type: 'acknowledgement' });
    expect(message).toBeInstanceOf(AcknowledgementMessage);
  });

  // -----------------------------------------------------------------------------------------------

  describe('constructor()', () => {
    it('sets message type name to acknowledgement', () => {
      const message = new AcknowledgementMessage();
      expect(message.type).toEqual('acknowledgement');
    });
  });
});

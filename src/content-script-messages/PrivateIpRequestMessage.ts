import ContentScriptMessage from './ContentScriptMessage';

/**
 * A message sent to the offscreen document to request private IP address info.
 * This is sent from the extension (service worker) to the offscreen document. It's classed as a
 *  content script message because they use the same message channel, so they need to be compatible.
 *
 * @todo Probably rename the base class to something generic.
 */
export default class PrivateIpRequestMessage extends ContentScriptMessage {
  /**
   * Initialise a new instance.
   */
  public constructor() {
    super(PrivateIpRequestMessage.type);
  }

  /**
   * Try to construct an instance of this message from a generic object.
   * @param obj The object to load properties from.
   * @returns Returns a new instance of this message, populated from the specified object.
   * @throws {Error} The specified value was not an object, or properties were missing or invalid,
   * or it specified a different message type.
   */
  public static readonly load = (obj: any): PrivateIpRequestMessage => {
    if (obj?.type !== PrivateIpRequestMessage.type) {
      throw new Error(`Missing or invalid message type. Expected ${PrivateIpRequestMessage.type}.`);
    }

    return new PrivateIpRequestMessage();
  };

  /**
   * Identifies the type of message which will carry this payload.
   * This value will be stored in the base class "type" property.
   */
  public static readonly type = 'private-ip-request';
}

ContentScriptMessage.registerMessageType(PrivateIpRequestMessage);

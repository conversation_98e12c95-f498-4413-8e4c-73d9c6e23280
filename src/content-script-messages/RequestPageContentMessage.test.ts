import ContentScriptMessage from './ContentScriptMessage';
import RequestPageContentMessage from './RequestPageContentMessage';

describe('RequestPageContentMessage', () => {
  it('is successfully loaded by the base class', () => {
    // Ensure the class is registered in case some other test has unregistered it by mistake.
    ContentScriptMessage.registerMessageType(RequestPageContentMessage);

    const message = RequestPageContentMessage.load({ type: 'request-page-content' });
    expect(message).toBeInstanceOf(RequestPageContentMessage);
  });

  // -----------------------------------------------------------------------------------------------

  describe('constructor()', () => {
    it('sets message type name to request-page-content', () => {
      const message = new RequestPageContentMessage();
      expect(message.type).toEqual('request-page-content');
    });
  });
});

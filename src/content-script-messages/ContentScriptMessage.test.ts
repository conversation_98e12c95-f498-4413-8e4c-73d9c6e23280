import ContentScriptMessage from './ContentScriptMessage';

// -----------------------------------------------------------------------------------------------

class TestMessageWithData extends ContentScriptMessage {
  public constructor(data: string) {
    super(TestMessageWithData.type);
    this.data = data;
  }

  public static readonly load = (obj: any): TestMessageWithData => {
    if (obj?.type !== TestMessageWithData.type || typeof obj?.data !== 'string') {
      throw new Error('Invalid message');
    }

    return new TestMessageWithData(obj.data);
  };

  public static readonly type = 'test-message-with-data';
  public data: string;
}

// -----------------------------------------------------------------------------------------------

class TestMessageWithoutData extends ContentScriptMessage {
  public constructor() {
    super(TestMessageWithoutData.type);
  }

  public static readonly type = 'test-message-without-data';
}

// -----------------------------------------------------------------------------------------------

describe('ContentScriptMessage', () => {
  // Register our test message classes before testing, and unregister them afterwards.
  beforeAll(() => {
    ContentScriptMessage.registerMessageType(TestMessageWithData);
    ContentScriptMessage.registerMessageType(TestMessageWithoutData);
  });

  afterAll(() => {
    ContentScriptMessage.unregisterMessageType(TestMessageWithData);
    ContentScriptMessage.unregisterMessageType(TestMessageWithoutData);
  });

  // -----------------------------------------------------------------------------------------------

  describe('registerMessageType()', () => {
    it('throws an error if the base class is passed in', () => {
      expect(() => {
        ContentScriptMessage.registerMessageType(ContentScriptMessage);
      }).toThrow();
    });

    it('throws an error if the specified class does not extend the base class', () => {
      class TemporaryClass {
        public readonly dummyValue: string = '';
      }

      expect(() => {
        ContentScriptMessage.registerMessageType(
          TemporaryClass as unknown as typeof ContentScriptMessage,
        );
      }).toThrow();
    });

    it('does not fall over if the same class is registered multiple times', () => {
      expect(() => {
        ContentScriptMessage.registerMessageType(TestMessageWithData);
      }).not.toThrow();
      expect(() => {
        ContentScriptMessage.registerMessageType(TestMessageWithData);
      }).not.toThrow();
    });

    it('enables the specified class to be used when loading messages', () => {
      class TemporaryTestMessage extends ContentScriptMessage {
        public constructor() {
          super(TemporaryTestMessage.type);
        }

        public static readonly type = 'temporary-test-message';
      }

      ContentScriptMessage.registerMessageType(TemporaryTestMessage);
      const message = ContentScriptMessage.load({ type: 'temporary-test-message' });
      expect(message).toBeInstanceOf(TemporaryTestMessage);
      ContentScriptMessage.unregisterMessageType(TemporaryTestMessage);
    });

    it('replaces any previous class registered with the same message type name', () => {
      class TemporaryTestMessage1 extends ContentScriptMessage {
        public constructor() {
          super(TemporaryTestMessage1.type);
        }

        public static readonly type = 'temporary-test-message-1';
      }

      class TemporaryTestMessage2 extends ContentScriptMessage {
        public constructor() {
          super(TemporaryTestMessage2.type);
        }

        public static readonly type = 'temporary-test-message-1'; // <-- deliberately 1 instead of 2
      }

      ContentScriptMessage.registerMessageType(TemporaryTestMessage1);
      ContentScriptMessage.registerMessageType(TemporaryTestMessage2);

      const message = ContentScriptMessage.load({ type: 'temporary-test-message-1' });
      expect(message).toBeInstanceOf(TemporaryTestMessage2);

      ContentScriptMessage.unregisterMessageType(TemporaryTestMessage1);
      ContentScriptMessage.unregisterMessageType(TemporaryTestMessage2);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('unregisterMessageType()', () => {
    it('throws an error if the specified class does not have a static type property', () => {
      class TemporaryClass {
        public readonly dummyValue: string = '';
      }

      expect(() => {
        ContentScriptMessage.unregisterMessageType(
          TemporaryClass as unknown as typeof ContentScriptMessage,
        );
      }).toThrow();
    });

    it('does nothing if the specified class was not registered', () => {
      class TemporaryTestMessage extends ContentScriptMessage {
        public constructor() {
          super(TemporaryTestMessage.type);
        }

        public static readonly type = 'temporary-test-message';
      }

      ContentScriptMessage.unregisterMessageType(TemporaryTestMessage);
      ContentScriptMessage.unregisterMessageType(TemporaryTestMessage);
    });

    it('prevents the class from being used in subsequent loads()', () => {
      class TemporaryTestMessage extends ContentScriptMessage {
        public constructor() {
          super(TemporaryTestMessage.type);
        }

        public static readonly type = 'temporary-test-message';
      }

      ContentScriptMessage.registerMessageType(TemporaryTestMessage);
      ContentScriptMessage.unregisterMessageType(TemporaryTestMessage);
      expect(() => ContentScriptMessage.load({ type: 'temporary-test-message' })).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('load()', () => {
    it('returns a populated instance of the appropriate sub-class for a message without data', () => {
      const obj = {
        type: 'test-message-without-data',
      };

      const message = ContentScriptMessage.load(obj);
      expect(message).toBeInstanceOf(TestMessageWithoutData);
    });

    it('returns a populated instance of the appropriate sub-class for a message with data', () => {
      const obj = {
        type: 'test-message-with-data',
        data: 'blah blah blah',
      };

      const message = ContentScriptMessage.load(obj);
      expect(message).toBeInstanceOf(TestMessageWithData);
      expect((message as TestMessageWithData).data).toEqual('blah blah blah');
    });

    it('ignores unexpected properties in message object', () => {
      const obj = {
        type: 'test-message-with-data',
        data: 'blah blah blah',
        someUnexpectedData: 'hello world',
      };

      const message = ContentScriptMessage.load(obj);
      expect(message).toBeInstanceOf(TestMessageWithData);
      expect((message as TestMessageWithData).data).toEqual('blah blah blah');
    });

    it('throws an error if the message type is missing', () => {
      const obj = {
        data: 'blah blah blah',
      };

      expect(() => ContentScriptMessage.load(obj)).toThrow();
    });

    it('throws an error if the message type is empty', () => {
      const obj = {
        type: '',
        data: 'blah blah blah',
      };

      expect(() => ContentScriptMessage.load(obj)).toThrow();
    });

    it('throws an error if the message type is not recognised', () => {
      const obj = {
        type: 'not-a-valid-type',
        data: 'blah blah blah',
      };

      expect(() => ContentScriptMessage.load(obj)).toThrow();
    });

    it('throws an error if the message sub-class fails to load the data', () => {
      const obj = {
        type: 'test-message-with-data',
        data: 123, // <-- deliberatley the wrong type; string was expected
      };

      expect(() => ContentScriptMessage.load(obj)).toThrow();
    });

    it('throws an error if sub-class load() returns an instance of a different type', () => {
      class TemporaryTestMessage1 extends ContentScriptMessage {
        public constructor() {
          super(TemporaryTestMessage1.type);
        }

        public static readonly type = 'temporary-test-message-1';
      }

      class TemporaryTestMessage2 extends ContentScriptMessage {
        public constructor() {
          super(TemporaryTestMessage2.type);
        }

        public static readonly load = (): ContentScriptMessage => {
          // Deliberately returning TemporaryTestMessage1 instead of TemporaryTestMessage2:
          return new TemporaryTestMessage1();
        };

        public static readonly type = 'temporary-test-message-2';
      }

      ContentScriptMessage.registerMessageType(TemporaryTestMessage1);
      ContentScriptMessage.registerMessageType(TemporaryTestMessage2);
      expect(() => ContentScriptMessage.load({ type: 'temporary-test-message-2' })).toThrow();
      ContentScriptMessage.unregisterMessageType(TemporaryTestMessage1);
      ContentScriptMessage.unregisterMessageType(TemporaryTestMessage2);
    });

    it('throws an error if sub-class load() returns an object which does not extend the base message class', () => {
      class TemporaryTestMessage extends ContentScriptMessage {
        public constructor() {
          super(TemporaryTestMessage.type);
        }

        public static readonly load = (): ContentScriptMessage => {
          return {} as unknown as ContentScriptMessage;
        };

        public static readonly type = 'temporary-test-message';
      }

      ContentScriptMessage.registerMessageType(TemporaryTestMessage);
      expect(() => ContentScriptMessage.load({ type: 'temporary-test-message' })).toThrow();
      ContentScriptMessage.unregisterMessageType(TemporaryTestMessage);
    });

    it('throws an error if the resulting message object has the wrong message type name', () => {
      class TemporaryTestMessage extends ContentScriptMessage {
        public constructor() {
          // Deliberately passing in the wrong type name here:
          super('xyzzy');
        }

        public static readonly type = 'temporary-test-message';
      }

      ContentScriptMessage.registerMessageType(TemporaryTestMessage);
      expect(() => ContentScriptMessage.load({ type: 'temporary-test-message' })).toThrow();
      ContentScriptMessage.unregisterMessageType(TemporaryTestMessage);
    });
  });
});

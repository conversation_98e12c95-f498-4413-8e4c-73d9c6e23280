/**
 * Represents a message passed between the content script and the extension, in either direction.
 * A message must always have a type. It can optionally have additional properties, depending on the
 *  type.
 *
 * Message types should be declared as sub-classes. They should add whatever properties are required
 *  in the message body. They should also declare the following static properties:
 *
 * - type = A static string property containing the name of the message type. This value is stored
 *          in the "type" instance property of this class.
 * - load = A static function which will load an instance of the message from a generic object. It
 *          can be omitted if the object has no additional properties.
 *
 * On start-up, you should call ContentScriptMessage.registerMessageType() for each sub-class.
 */
export default abstract class ContentScriptMessage {
  /**
   * Initialise a new instance with the specified message type.
   *
   * @param type The type of message being constructed. It must be a non-empty string. Its type is
   *  deliberately set to 'any' so that sub-classes can be passed to registerMessageType() without
   *  needing to have the same constructor parameters.
   * @throws {Error} The specified type is not a string or is empty.
   */
  public constructor(type: any) {
    if (typeof type !== 'string') {
      throw new Error('Content script message type must be a string');
    }
    if (type === '') {
      throw new Error('Content script message type must not be empty');
    }
    this.type = type;
  }

  /**
   * Specify a class which implements a specific message type.
   * This enables the static load() function on this class to parse messages of that type.
   * If another class with the same message type name has already been registered then this will
   *  replace the existing entry.
   *
   * @param subClass The message class to register. It must extend ContentScriptMessage, and must
   *  contain a static string property called "type" which contains the name of the message type. If
   *  the message stores any data then it must also define a static "load()" function which creates
   *  an instance of the class from a general object.
   * @throws {Error} The specified class does not extend this class.
   * @throws {Error} The specified class does not have a static string property called "type", or it
   *  is empty.
   * @throws {Error} The load() function of the specified class failed, or did not return an
   *  instance of the expected class.
   */
  public static readonly registerMessageType = (subClass: typeof ContentScriptMessage): void => {
    if (subClass.name === ContentScriptMessage.name) {
      throw new TypeError('Registering the ContentScriptMessage base class is not allowed');
    }

    // TODO: Double-check that the sub-class extends this class.

    const type = (subClass as any)?.type;
    if (typeof type !== 'string' || type === '') {
      throw new Error('Message type name is missing or invalid');
    }
    ContentScriptMessage._registeredMessageTypes.set(type, subClass);
  };

  /**
   * Remove a message class which was previously registered.
   * This prevents it from being used when loading a message in load().
   * This does nothing if the specified message class was not registered.
   *
   * @param subClass The message class to unregister. It must extend ContentScriptMessage, and must
   *  contain a static string proeprty called "type" which contains the name of the message type.
   * @throws {Error} The specified class does not have a static string property called "type".
   *
   * @warning If a different class was registered with the same message type name then it will be
   *  removed by this function.
   */
  public static readonly unregisterMessageType = (subClass: typeof ContentScriptMessage): void => {
    const type = (subClass as any)?.type;
    if (typeof type !== 'string') {
      throw new Error('Message type name is missing or invalid');
    }
    ContentScriptMessage._registeredMessageTypes.delete(type);
  };

  /**
   * Try to parse the given value as a message.
   * This will look for a "type" property on the object, and try to match it to one of the
   *  registered message sub-classes.
   *
   * @param obj The object to parse as a message.
   * @returns Returns an instance of the message type-specific sub-class, populated from the
   *  specified value.
   * @throws {Error} The specified value is not an object.
   * @throws {Error} The "type" property is missing or empty on the input object.
   * @throws {Error} The "type" property does not match any registered sub-class.
   * @throws {Error} The sub-class failed to load the object. This may mean it does not match the
   *  expected structure; e.g. it has missing or invalid properties.
   *
   * @note Any unexpected properties in the specified object will be ignored.
   * @see registerMessageType()
   */
  public static readonly load = (obj: any): ContentScriptMessage => {
    if (obj === null || typeof obj !== 'object') {
      throw new Error('Cannot load content script message from empty value');
    }

    const type = obj?.type;
    if (typeof type !== 'string' || type === '') {
      throw new Error('Missing type property in message value');
    }

    const subClass = ContentScriptMessage._registeredMessageTypes.get(type);
    if (subClass === undefined) {
      throw new Error(`Content script message type not recognised: ${type}`);
    }

    // If the message class doesn't have its own load() function then the message doesn't have any
    //  data to load. Just instantiate the class directly.
    if (
      !Object.prototype.hasOwnProperty.call(subClass, 'load') ||
      typeof subClass.load !== 'function'
    ) {
      const result = new (subClass as any)() as ContentScriptMessage;
      // Sanity check the result.
      if (result.type !== type) {
        throw new Error(
          'Message instance does not have expected message type name.' +
            `Expected: ${type}. Actual: ${result.type}.`,
        );
      }
      return result;
    }

    // Use the sub-class load function to process the data.
    const result = subClass.load(obj);

    // Sanity-check the result.
    if (!(result instanceof ContentScriptMessage)) {
      throw new Error(
        'Message class load() function did not return a sub-class of ContentScriptMessage.',
      );
    }

    if (result.constructor.name !== subClass.name) {
      throw new Error(
        'Message class load() function did not return an instance of the expected class. ' +
          `Expected: ${subClass.name}. Actual: ${result.constructor.name}.`,
      );
    }

    if (result.type !== type) {
      throw new Error(
        'Message class load() function did not return an instance with the expected message ' +
          `type name. Expected: ${type}. Actual: ${result.type}.`,
      );
    }

    return result;
  };

  /**
   * Identifies what type of message is stored in this instance.
   *
   * @note Do not confuse this with the static readonly "type" property on sub-classes. They dictate
   *  the message type associated with the specific sub-class.
   */
  public readonly type: string;

  /**
   * Maps each message type name to the sub-class which implements that message type.
   * This is used in load() when parsing an object as a message.
   */
  private static readonly _registeredMessageTypes = new Map<string, typeof ContentScriptMessage>();
}

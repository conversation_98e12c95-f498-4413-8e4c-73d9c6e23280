import ContentScriptMessage from './ContentScriptMessage';

/**
 * The extension sends this message to the content script to tell it to allow access to a page.
 * This will cause it to un-hide the page if necessary.
 */
export default class AllowContentMessage extends ContentScriptMessage {
  /**
   * Initialise a new instance.
   */
  public constructor(contentModIds: string[] = []) {
    super(AllowContentMessage.type);

    this.contentModIds = contentModIds;
  }

  /**
   * Try to construct an instance of this message from a generic object.
   * @param obj The object to load properties from.
   * @returns Returns a new instance of this message, populated from the specified object.
   * @throws {Error} The specified value was not an object, or properties were missing or invalid,
   * or it specified a different message type.
   */
  public static readonly load = (obj: any): AllowContentMessage => {
    if (obj?.type !== AllowContentMessage.type) {
      throw new Error(`Missing or invalid message type. Expected ${AllowContentMessage.type}.`);
    }

    const message = new AllowContentMessage();

    message.contentModIds = obj?.contentModIds;

    return message;
  };

  /**
   * Identifies the type of message which will carry this payload.
   * This value will be stored in the base class "type" property.
   */
  public static readonly type = 'allow-content';

  /**
   * A list of content mod ids that should be applied.
   */
  public contentModIds: string[] = [];
}

ContentScriptMessage.registerMessageType(AllowContentMessage);

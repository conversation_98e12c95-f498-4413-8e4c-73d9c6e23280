import ContentScriptMessage from './ContentScriptMessage';
import BlockContentMessage from './BlockContentMessage';

describe('BlockContentMessage', () => {
  it('is successfully loaded by the base class', () => {
    // Ensure the class is registered in case some other test has unregistered it by mistake.
    ContentScriptMessage.registerMessageType(BlockContentMessage);

    const message = BlockContentMessage.load({ type: 'block-content' });
    expect(message).toBeInstanceOf(BlockContentMessage);
  });

  // -----------------------------------------------------------------------------------------------

  describe('constructor()', () => {
    it('sets message type name to block-content', () => {
      const message = new BlockContentMessage();
      expect(message.type).toEqual('block-content');
    });
  });
});

import ContentScriptMessage from './ContentScriptMessage';

/**
 * A message sent to the content script to enable additional Youtube handling. No parameters are required by
 * the message as the message itself is the signal.
 */
export default class EnableYoutubeHandlingMessage extends ContentScriptMessage {
  public static readonly type = 'enable-youtube-handling';

  public constructor() {
    super(EnableYoutubeHandlingMessage.type);
  }

  /**
   * Identifies the type of message which will carry this payload.
   * This value will be stored in the base class "type" property.
   */
  public static readonly load = (obj: any): EnableYoutubeHandlingMessage => {
    if (obj?.type !== EnableYoutubeHandlingMessage.type) {
      throw new Error(
        `Missing or invalid message type. Expected ${EnableYoutubeHandlingMessage.type}.`,
      );
    }

    return new EnableYoutubeHandlingMessage();
  };
}

ContentScriptMessage.registerMessageType(EnableYoutubeHandlingMessage);

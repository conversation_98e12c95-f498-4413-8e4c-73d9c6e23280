import ContentScriptMessage from './ContentScriptMessage';

/**
 * A message sent by the content script containing the content of the current page.
 * This allows the extension to do content analysis (weighted phrases).
 */
export default class PageContentMessage extends ContentScriptMessage {
  /**
   * Optionally initialise a new instance of the message.
   * This automatically initialises the message type property in the base class.
   */
  public constructor(url: string = '', content: string = '', title: string = '') {
    super(PageContentMessage.type);
    this.url = url;
    this.content = content;
    this.title = title;
  }

  /**
   * Try to construct an instance of this message from a generic object.
   *
   * @param obj The object to load properties from.
   * @returns Returns a new instance of this message, populated from the specified object.
   * @throws {Error} The specified value was not an object, or properties were missing or invalid,
   *  or it specified a different message type.
   */
  public static readonly load = (obj: any): PageContentMessage => {
    if (obj?.type !== PageContentMessage.type) {
      throw new Error(`Missing or invalid message type. Expected ${PageContentMessage.type}.`);
    }

    if (
      typeof obj?.url !== 'string' ||
      typeof obj?.content !== 'string' ||
      typeof obj?.title !== 'string'
    ) {
      throw new Error('Invalid PageContentMessage.');
    }

    return new PageContentMessage(obj.url, obj.content, obj.title);
  };

  /**
   * Identifies the type of message which will carry this payload.
   * This value will be stored in the base class "type" property.
   */
  public static readonly type = 'page-content';

  /**
   * The URL of the current page.
   */
  public url: string;

  /**
   * The body content of the current page, truncated to a reasonable limit if necessary.
   */
  public content: string;

  /**
   * The title of the current page.
   */
  public title: string;
}

ContentScriptMessage.registerMessageType(PageContentMessage);

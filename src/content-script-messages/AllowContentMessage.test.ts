import ContentScriptMessage from './ContentScriptMessage';
import AllowContentMessage from './AllowContentMessage';

describe('AllowContentMessage', () => {
  it('is successfully loaded by the base class', () => {
    // Ensure the class is registered in case some other test has unregistered it by mistake.
    ContentScriptMessage.registerMessageType(AllowContentMessage);

    const message = AllowContentMessage.load({ type: 'allow-content' });
    expect(message).toBeInstanceOf(AllowContentMessage);
  });

  // -----------------------------------------------------------------------------------------------

  describe('constructor()', () => {
    it('sets message type name to allow-content', () => {
      const message = new AllowContentMessage();
      expect(message.type).toEqual('allow-content');
    });
  });
});

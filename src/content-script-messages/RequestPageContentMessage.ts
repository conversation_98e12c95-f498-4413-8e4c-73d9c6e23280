import ContentScriptMessage from './ContentScriptMessage';

/**
 * A message sent by the extension to ask the content script to provide page content.
 * This is sent when the extension is ready to do content analysis.
 * This message type contains no data.
 */
export default class RequestPageContentMessage extends ContentScriptMessage {
  /**
   * Initialise a new instance.
   */
  public constructor() {
    super(RequestPageContentMessage.type);
  }

  /**
   * Identifies the type of message which will carry this payload.
   * This value will be stored in the base class "type" property.
   */
  public static readonly type = 'request-page-content';
}

ContentScriptMessage.registerMessageType(RequestPageContentMessage);

import ContentScriptMessage from './ContentScriptMessage';

/**
 * The extension sends this message to the content script to tell it to block a page.
 * This causes it to hide the page if necessary.
 */
export default class BlockContentMessage extends ContentScriptMessage {
  /**
   * Initialise a new instance.
   */
  public constructor() {
    super(BlockContentMessage.type);
  }

  /**
   * Identifies the type of message which will carry this payload.
   * This value will be stored in the base class "type" property.
   */
  public static readonly type = 'block-content';
}

ContentScriptMessage.registerMessageType(BlockContentMessage);

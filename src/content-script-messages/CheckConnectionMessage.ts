import ContentScriptMessage from './ContentScriptMessage';

/**
 * A message used to check the status of the connection between the extension and a content script.
 * Typically, the extension will send this as a standalone message to a content script. The content
 *  script will then try to send another instance of the message back via long-lived port. If that
 *  fails then it will try to reopen the port. The extension should not wait for a response. It
 *  should assume that the content script will handle the connection itself.
 */
export default class CheckConnectionMessage extends ContentScriptMessage {
  /**
   * Initialise a new instance.
   */
  public constructor() {
    super(CheckConnectionMessage.type);
  }

  /**
   * Identifies the type of message which will carry this payload.
   * This value will be stored in the base class "type" property.
   */
  public static readonly type = 'check-connection';
}

ContentScriptMessage.registerMessageType(CheckConnectionMessage);

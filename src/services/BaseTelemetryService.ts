import StandardTelemetryData from '../models/StandardTelemetryData';
import ProvisioningInfo from 'provisioning/ProvisioningInfo';
import PlatformConfig from 'models/PlatformConfig';
import { getExtensionId, getExtensionVersion, getManifestVersion } from 'utilities/Helpers';
import OperatingMode from 'constants/OperatingMode';
import Uuid from 'models/Uuid';

/**
 * A base class for any shared functions and data for usage with telemetry services.
 */
export default abstract class BaseTelemetryService {
  constructor() {
    setInterval(this._rateLimitInterval, this._rateLimitIntervalTime);
  }

  /**
   * Uses the given info to create a set of data that should be sent with all our telemetry requests.
   * @param provisioningInfo The provisioning info to use.
   * @param platformConfig The platform config to use.
   * @returns A StandardTelemetryData object with the parsed information.
   */
  public readonly getStandardParameters = (
    provisioningInfo: ProvisioningInfo,
    platformConfig: PlatformConfig,
  ): StandardTelemetryData => {
    let operatingMode = '';
    if (platformConfig.operatingMode !== undefined) {
      operatingMode = OperatingMode[platformConfig.operatingMode];
    }

    this._standardParameters = {
      userId: provisioningInfo.user.toLowerCase() + '%%' + provisioningInfo.hardwareId,
      applicationVersion: getExtensionVersion(),
      accountId: provisioningInfo.serialId.get(),
      sessionId: Uuid.random().toString().replace(/-/g, ''),
      deviceId: '', // Received during device registration.
      tenantId: provisioningInfo.tenantId?.get() ?? '',
      operatingMode,
      os: platformConfig.platformInfo.os,
      extensionId: getExtensionId(),
      manifestVersion: getManifestVersion(),
      gls: provisioningInfo.serialId.getGlsHash(),
    };

    return this._standardParameters;
  };

  /**
   * Sets the deviceId standard parameter.
   * @param deviceId The device id from the device management api.
   */
  public readonly setDeviceId = (deviceId: string): void => {
    if (this._standardParameters === undefined) {
      return;
    }

    this._standardParameters.deviceId = deviceId;
  };

  /**
   * Indicates if the telemetry is currently rate limited.
   * @returns A boolean for if telemetry can currently be sent.
   */
  protected readonly _canSendTelemetry = (): boolean => {
    this._telemetrySentDuringInterval++;

    if (this._telemetrySentDuringInterval > this._telemetryLimit) {
      this._isRateLimited = true;
    }

    return !this._isRateLimited;
  };

  /**
   * Called when the rate limit interval is triggered.
   *
   * The amount of telemetry sent over the past interval will be checked.
   * If it is over an acceptable limit then the telemetry should stop being sent.
   */
  private readonly _rateLimitInterval = (): void => {
    this._isRateLimited = this._telemetrySentDuringInterval > this._telemetryLimit;
    this._telemetrySentDuringInterval = 0;
  };

  /*
   * Concatenates the prefix and event name into a name that can be sent to telemetry.
   * @param name The name of the event.
   * @returns A string of the new telemetry name.
   */
  protected readonly _createTelemetryName = (name: string): string => {
    return `${this.prefix}-${name}`;
  };

  /**
   * The standard parameters that should be sent with all telemetry logging.
   */
  public get standardParameters(): StandardTelemetryData | undefined {
    return this._standardParameters;
  }

  /**
   * The standard prefix for all telemetry logging.
   */
  public readonly prefix: string = 'cldflt';

  /**
   * The standard parameters that should be sent with all telemetry logging.
   */
  private _standardParameters: StandardTelemetryData | undefined;

  /**
   * The count of telemetry messages that have been sent over the defined interval.
   */
  private _telemetrySentDuringInterval: number = 0;

  /** Indicates if the telemetry messages should be rate limited.
   */
  private _isRateLimited: boolean = false;

  /**
   * The max number of telemetry messages that should be sent over the interval.
   */
  private readonly _telemetryLimit: number = 5000;

  /**
   * The time interval (in milliseconds) for measuring the amount of telemetry sent over an interval.
   */
  private readonly _rateLimitIntervalTime = 300000; // 5 minutes
}

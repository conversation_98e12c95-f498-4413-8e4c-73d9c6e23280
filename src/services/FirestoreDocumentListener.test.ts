import * as Firestore from 'firebase/firestore';
import FirestoreDocumentListener from './FirestoreDocumentListener';
import MockTelemetryService from 'test-helpers/MockTelemetryService';

jest.mock('firebase/firestore');

interface TestDoc {
  blah: string;
}

describe('FirestoreDocumentListener', () => {
  const mockTelemetryService = new MockTelemetryService();
  const mockFirestoreUnsubscribe = jest.fn();
  const mockFirestore: Firestore.Firestore = {
    type: 'firestore',
    app: {} as any,
    toJSON: jest.fn(),
  };

  beforeEach(() => {
    jest.useFakeTimers();

    (Firestore.onSnapshot as jest.Mock).mockReturnValue(mockFirestoreUnsubscribe);

    // Suppress debug and warning messages in the console.
    jest.spyOn(console, 'debug').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  // -----------------------------------------------------------------------------------------------

  describe('isStarted', () => {
    it('returns false if start() has not been called', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      expect(firestoreDocumentListener.isStarted).toBeFalse();
    });

    it('returns true if start() has been called', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      expect(firestoreDocumentListener.isStarted).toBeTrue();
    });

    it('returns false if stop() has been called', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      firestoreDocumentListener.stop();
      expect(firestoreDocumentListener.isStarted).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('isSubscribed', () => {
    const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(mockTelemetryService);
    it('returns false if start() has not been called', () => {
      expect(firestoreDocumentListener.isSubscribed).toBeFalse();
    });

    it('returns true if start() has been called and the query was successfully created', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      expect(firestoreDocumentListener.isSubscribed).toBeTrue();
      firestoreDocumentListener.stop();
    });

    it('returns false if the query could not be created', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      // Simulate the query failing with an error.
      (Firestore.query as jest.Mock).mockImplementationOnce(() => {
        throw new Error('test');
      });

      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      expect(firestoreDocumentListener.isSubscribed).toBeFalse();
      firestoreDocumentListener.stop();
    });

    it('returns false if the snapshot reported an error', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      (firestoreDocumentListener as any)._onSnapshotError();
      expect(firestoreDocumentListener.isSubscribed).toBeFalse();
      firestoreDocumentListener.stop();
    });

    it('returns false if stop() has been called', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      firestoreDocumentListener.stop();
      expect(firestoreDocumentListener.isSubscribed).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('path', () => {
    it('returns undefined if start() has not been called yet', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      expect(firestoreDocumentListener.path).toBeUndefined();
    });

    it('returns the path passed in to start()', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      expect(firestoreDocumentListener.path).toEqual('/foo/bar');
    });

    it('returns undefined if stop() has been called', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      firestoreDocumentListener.stop();
      expect(firestoreDocumentListener.path).toBeUndefined();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('data', () => {
    it('returns undefined if start() has not been called yet', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      expect(firestoreDocumentListener.data).toBeUndefined();
    });

    it('returns the document data most recently provided via snapshot', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      const doc: TestDoc = {
        blah: 'foobar',
      };
      (firestoreDocumentListener as any)._document = doc;
      expect(firestoreDocumentListener.data).toEqual(doc);
    });

    it('returns undefined if stop() has been called', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      firestoreDocumentListener.stop();
      expect(firestoreDocumentListener.data).toBeUndefined();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('exists', () => {
    it('returns false if start has not been called', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      expect(firestoreDocumentListener.exists).toBeFalse();
    });

    it('returns false if the document listener is subscribed but the document does not exist', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      expect(firestoreDocumentListener.exists).toBeFalse();
      firestoreDocumentListener.stop();
    });

    it('returns true if the document listener is subscribed and the document exists', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      (firestoreDocumentListener as any)._document = { blah: 'foobar' };
      expect(firestoreDocumentListener.exists).toBeTrue();
      firestoreDocumentListener.stop();
    });

    it('returns false if stop has been called', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      firestoreDocumentListener.stop();
      expect(firestoreDocumentListener.exists).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('start()', () => {
    it('throws an error if the path is empty', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      expect(() => {
        firestoreDocumentListener.start(mockFirestore, '');
      }).toThrow();
    });

    it('sets up a Firestore snapshot query', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      expect(Firestore.onSnapshot).toHaveBeenCalled();
    });

    it('stops the previous subscription if already started with different parameters', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      (Firestore.onSnapshot as jest.Mock).mockClear();

      firestoreDocumentListener.start(mockFirestore, '/hello/world');
      expect(mockFirestoreUnsubscribe).toHaveBeenCalled();
      expect(Firestore.onSnapshot).toHaveBeenCalled();
    });

    it('does nothing if called again with the same parameters', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      expect(Firestore.onSnapshot).toHaveBeenCalled();
      (Firestore.onSnapshot as jest.Mock).mockClear();

      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      expect(mockFirestoreUnsubscribe).not.toHaveBeenCalled();
      expect(Firestore.onSnapshot).not.toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('stop()', () => {
    it('does nothing if start() has not been called', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      expect(() => {
        firestoreDocumentListener.stop();
      }).not.toThrow();
    });

    it('stops the snapshot query', () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      firestoreDocumentListener.stop();
      expect(mockFirestoreUnsubscribe).toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('set()', () => {
    it('rejects if start() has not been called', async () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );

      await expect(firestoreDocumentListener.set({ blah: 'foo' })).toReject();
    });

    it('sets the document content in Firestore', async () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );

      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      await firestoreDocumentListener.set({ blah: 'foo' });
      firestoreDocumentListener.stop();
      expect(Firestore.setDoc).toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('update()', () => {
    it('rejects if start() has not been called', async () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );

      await expect(firestoreDocumentListener.update({ blah: 'foo' })).toReject();
    });

    it('sets the document content in Firestore', async () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );

      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      await firestoreDocumentListener.update({ blah: 'foobar' });
      firestoreDocumentListener.stop();
      expect(Firestore.updateDoc).toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('delete()', () => {
    it('rejects if start() has not been called', async () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );

      await expect(firestoreDocumentListener.delete()).toReject();
    });

    it('sets the document content in Firestore', async () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );

      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      await firestoreDocumentListener.delete();
      firestoreDocumentListener.stop();
      expect(Firestore.deleteDoc).toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  // _subscribe() was already tested via start()

  // -----------------------------------------------------------------------------------------------

  describe('_onSnapshot()', () => {
    let firestoreDocumentListener: FirestoreDocumentListener<TestDoc>;
    const onAddedHandler = jest.fn();
    const onModifiedHandler = jest.fn();
    const onRemovedHandler = jest.fn();

    beforeEach(() => {
      firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(mockTelemetryService);
      firestoreDocumentListener.onAdded.addListener(onAddedHandler);
      firestoreDocumentListener.onModified.addListener(onModifiedHandler);
      firestoreDocumentListener.onRemoved.addListener(onRemovedHandler);
    });

    it('does nothing if there were no document changes', () => {
      const mockSnapshot = {
        docChanges: () => {
          return [];
        },
        metadata: {
          fromCache: false,
        },
      };

      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      (firestoreDocumentListener as any)._onSnapshot(mockSnapshot);

      expect(onAddedHandler).not.toHaveBeenCalled();
      expect(onModifiedHandler).not.toHaveBeenCalled();
      expect(onRemovedHandler).not.toHaveBeenCalled();
    });

    it('triggers onAdded if the document has been added', async () => {
      const mockSnapshot = {
        docChanges: () => {
          return [
            {
              type: 'added',
              doc: {
                data: () => {
                  return {
                    blah: 'foobar',
                  };
                },
              },
            },
          ];
        },
        metadata: {
          fromCache: false,
        },
      };

      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      (firestoreDocumentListener as any)._onSnapshot(mockSnapshot);
      await jest.runAllTimersAsync();
      expect(onAddedHandler).toHaveBeenCalled();
      expect(firestoreDocumentListener.data).toEqual({ blah: 'foobar' });
    });

    it('triggers onModified if the document has been modified', async () => {
      const mockSnapshot = {
        docChanges: () => {
          return [
            {
              type: 'modified',
              doc: {
                data: () => {
                  return {
                    blah: 'foobar',
                  };
                },
              },
            },
          ];
        },
        metadata: {
          fromCache: false,
        },
      };

      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      (firestoreDocumentListener as any)._onSnapshot(mockSnapshot);
      await jest.runAllTimersAsync();
      expect(onModifiedHandler).toHaveBeenCalled();
      expect(firestoreDocumentListener.data).toEqual({ blah: 'foobar' });
    });

    it('triggers onRemoved if the document has been removed', async () => {
      const mockSnapshot = {
        docChanges: () => {
          return [
            {
              type: 'removed',
              doc: {
                data: () => {
                  return {};
                },
              },
            },
          ];
        },
        metadata: {
          fromCache: false,
        },
      };

      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      (firestoreDocumentListener as any)._onSnapshot(mockSnapshot);
      await jest.runAllTimersAsync();
      expect(onRemovedHandler).toHaveBeenCalled();
      expect(firestoreDocumentListener.data).toBeUndefined();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('_onSnapshotError()', () => {
    it('tries to subscribe again about half an hour after the last attempt', async () => {
      const firestoreDocumentListener = new FirestoreDocumentListener<TestDoc>(
        mockTelemetryService,
      );
      firestoreDocumentListener.start(mockFirestore, '/foo/bar');
      (Firestore.onSnapshot as jest.Mock).mockClear();
      (firestoreDocumentListener as any)._onSnapshotError();

      // Ensure we do not retry within 29 minutes.
      await jest.advanceTimersByTimeAsync(1740000);
      expect(Firestore.onSnapshot).not.toHaveBeenCalled();

      // Ensure we do retry within another 10 minutes (allows for random jitter).
      await jest.advanceTimersByTimeAsync(600000);
      expect(Firestore.onSnapshot).toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('splitPath()', () => {
    it('returns a tuple containing the collection path and document ID as separate elements', () => {
      const parts = FirestoreDocumentListener.splitPath('/foo/bar');
      expect(parts).toBeArrayOfSize(2);
      expect(parts[0]).toEqual('/foo');
      expect(parts[1]).toEqual('bar');
    });

    it('assumes the entire string is the document ID if it does not contain a slash', () => {
      const parts = FirestoreDocumentListener.splitPath('bar');
      expect(parts).toBeArrayOfSize(2);
      expect(parts[0]).toEqual('');
      expect(parts[1]).toEqual('bar');
    });

    it('discards the leading slash if there is only one slash and it is at the beginning', () => {
      const parts = FirestoreDocumentListener.splitPath('/bar');
      expect(parts).toBeArrayOfSize(2);
      expect(parts[0]).toEqual('');
      expect(parts[1]).toEqual('bar');
    });
  });

  // -----------------------------------------------------------------------------------------------
});

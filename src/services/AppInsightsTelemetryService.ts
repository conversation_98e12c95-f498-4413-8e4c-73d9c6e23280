import { ApplicationInsights, ITelemetryItem, Tags } from '@microsoft/applicationinsights-web';
import AppInsightsConfig from 'models/AppInsightsConfig';
import LogSeverityLevel from 'models/LogSeverityLevel';
import ITelemetryService from './ITelemetryService';
import ProvisioningInfo from 'provisioning/ProvisioningInfo';
import PlatformConfig from 'models/PlatformConfig';
import BaseTelemetryService from './BaseTelemetryService';
import MetricTelemetry from 'models/MetricTelemetry';

/**
 * A service for logging events and exceptions to app insights.
 */
export default class AppInsightsTelemetryService
  extends BaseTelemetryService
  implements ITelemetryService
{
  constructor(config: AppInsightsConfig) {
    super();

    this._appInsights = new ApplicationInsights({
      config: {
        connectionString: config.connectionString,
        disableCorrelationHeaders: false,
        enableCorsCorrelation: true,
        disableFetchTracking: false,
      },
    });

    this._appInsights.loadAppInsights();

    this._appInsights.addTelemetryInitializer(this._telemetryInitialiser);
  }

  /**
   * Initialises the standard parameters for all app insight logs.
   * @param provisioningInfo The provisioning info for the device.
   * @param platformConfig The platform configuration for the device.
   */
  public readonly initStandardParameters = (
    provisioningInfo: ProvisioningInfo,
    platformConfig: PlatformConfig,
  ): void => {
    const parameters = this.getStandardParameters(provisioningInfo, platformConfig);

    this._tags = {
      'ai.user.id': parameters.userId,
      'ai.application.ver': parameters.applicationVersion,
      'ai.user.accountId': parameters.accountId,
      'ai.session.id': parameters.sessionId,
    };
  };

  /**
   * Modifies the telemetry data before it is sent to app insights.
   *
   * @see [MS documentation](https://learn.microsoft.com/en-us/azure/azure-monitor/app/javascript?tabs=snippet#telemetry-initializers)
   * @param envelope The telemetry data that is sent to app insights.
   * @returns A boolean to indicate if the telemetry should be sent.
   */
  private readonly _telemetryInitialiser = (item: ITelemetryItem): boolean => {
    // Do not send any blocked hosts.
    if (item.baseType === 'RemoteDependencyData' && item.baseData !== undefined) {
      for (const target of this._blockedHosts) {
        if (item.baseData.target.indexOf(target) > -1) {
          return false;
        }
      }
    }
    // Set the app insight tags that will show at the top level.
    if (this._tags !== undefined) {
      item.tags = [];
      item.tags.push(this._tags);
    }

    // Add any additional standard parameters we want into the custom dimensions.
    if (this.standardParameters !== undefined) {
      if (item.baseData === undefined) {
        item.baseData = {};
      }

      item.baseData.properties = {
        ...item.baseData.properties,
        deviceId: this.standardParameters.deviceId,
        tenant: this.standardParameters.tenantId,
        extensionId: this.standardParameters.extensionId,
        manifestVersion: this.standardParameters.manifestVersion,
        gls: this.standardParameters.gls,
      };
    }

    return true;
  };

  /**
   * Logs the given event to App Insights.
   * @param name The name of the event to log.
   * @param customParameters Any custom parameters to send with the event.
   */
  public readonly logEvent = (name: string, customParameters: any = {}): void => {
    if (!this._canSendTelemetry()) {
      return;
    }

    console.debug(`Logging event: ${name}`, customParameters);

    name = this._createTelemetryName(name);
    this._appInsights.trackEvent({
      name,
      properties: customParameters,
    });
  };

  /**
   * Logs the given metric to App Insights.
   * @param name The name of the metric to log.
   */
  public readonly logMetric = (metric: MetricTelemetry): void => {
    if (!this._canSendTelemetry() || metric.count <= 0) {
      return;
    }

    console.debug(`Logging metric: ${metric.name}: `, metric);

    const name = this._createTelemetryName(metric.name);

    let properties = {};
    if (metric.parentName !== undefined) {
      const parentName = `${this.prefix}-${metric.parentName}`;
      properties = {
        parent: parentName,
      };
    }

    properties = { ...properties, ...metric.customProperties };

    this._appInsights.trackMetric({
      name,
      average: metric.average,
      sampleCount: metric.count,
      min: metric.min,
      max: metric.max,
      properties,
    });
  };

  /**
   * Logs a trace to app insights.
   * @param name The name of the trace.
   * @param customParameters Any custom parameters to send with the trace.
   */
  public readonly logTrace = (message: string, customParameters?: any): void => {
    if (!this._canSendTelemetry()) {
      return;
    }

    console.debug(`Logging trace: ${message}`, customParameters);

    message = this._createTelemetryName(message);
    this._appInsights.trackTrace(
      {
        message,
      },
      customParameters,
    );
  };

  /**
   * Logs the given error to App Insights.
   * @param name The name of the exception.
   * @param exception The error or error message to log.
   * @param customParameters Any custom parameters to log.
   * @param severity The severity of the error.
   */
  public readonly logError = (
    name: string,
    exception: Error | string,
    customParameters: any = {},
    severity: LogSeverityLevel = LogSeverityLevel.error,
  ): void => {
    if (!this._canSendTelemetry()) {
      return;
    }

    console.debug('Logging error', name, exception, customParameters, severity);

    let error: Error;

    if (exception instanceof Error) {
      error = exception;
    } else {
      error = new Error(exception);
    }

    error.name = this._createTelemetryName(`${name}`);

    this._appInsights.trackException({
      exception: error,
      properties: customParameters,
      severityLevel: severity,
    });
  };

  /**
   * The App Insights instance.
   */
  private readonly _appInsights: ApplicationInsights;

  /**
   * A list of host name snippets that should not be logged to app insights.
   */
  private readonly _blockedHosts: string[] = [
    ':6150',
    'firestore.googleapis.com',
    ':5769',
    'googleapis.com',
  ];

  /**
   * The tags to apply to all app insight requests.
   */
  private _tags?: Tags;
}

import CONTENT_AWARE_CONSTANTS from 'constants/ContentAwareConstants';
import {
  SearchTermCategory,
  SearchTermChild,
} from 'guardian/blocklist-components/SearchTermCategoriser';
import BlobDownloadResult from 'models/BlobDownloadResult';
import FileDownloadType from 'models/FileDownloadType';
import StandaloneEvent from 'utilities/StandaloneEvent';

import { SearchTerms } from '../guardian/blocklist-components/SearchTermCategoriser';
import PolicyAction from '../guardian/models/PolicyAction';
import {
  IBasePolicy,
  IContentModPolicy,
  IContentModPolicyFlat,
  IFilterPolicy,
  IFilterPolicyFlat,
  IPolicy,
  IPolicyConfig,
} from '../models/PolicyConfigModels';
import TenantId from '../models/TenantId';
import IBlobStorageService from './IBlobStorageService';

/**
 * Manages downloading and parsing the policy file from blob storage.
 */
export default class PolicyDownloadService {
  constructor(blobStorageService: IBlobStorageService, tenantId: TenantId | undefined) {
    this._blobStorageService = blobStorageService;
    this._tenantId = tenantId;

    this._blobStorageService.onDownloadSuccess.addListener(this._downloadCompleted);
    this._blobStorageService.onDownloadFailed.addListener(() => {
      this.onPolicyDownloaded.dispatch(undefined);
    });
  }

  /**
   * Starts the process for downloading and parsing the policy file.
   *
   * Once the data has been downloaded and parsed the `onPolicyDownloaded` event will fire with the parsed policy data.
   * @param url The url to download the policy file from.
   */
  public readonly start = (url: string): void => {
    this._blobStorageService.start(url, FileDownloadType.json);
  };

  /**
   * Stops the current download process.
   */
  public readonly stop = (): void => {
    this._blobStorageService.stop();
  };

  /**
   * Called when the blob download has been successfully completed.
   *
   * Parses the data and dispatches the `onPolicyDownloaded` event once the data has been parsed.
   * @param result The result returned from blob storage.
   */
  private readonly _downloadCompleted = async (result: BlobDownloadResult): Promise<void> => {
    const config = this._parseDownloadedPolicy(result.data);

    this.onPolicyDownloaded.dispatch(config);
  };

  private readonly _parseDownloadedPolicy = (config: object): IPolicyConfig => {
    let parsedConfig = config as IPolicyConfig;

    // Filter out any tenant specific config that is not related to our current tenant. Also keep any config marked as 'global'.
    parsedConfig.banned = this._filterTenants(parsedConfig.banned);
    parsedConfig.category_filter_groups = this._filterTenants(parsedConfig.category_filter_groups);

    parsedConfig.cloud_content_modifications = this._filterTenants(
      parsedConfig.cloud_content_modifications,
      'tenantId',
    );

    parsedConfig.cloud_content_modifications = parsedConfig.cloud_content_modifications.filter(
      (mod) => {
        // Filter out any disabled content mods
        if (!mod.enabled) {
          return false;
        }

        // If we have a 'global' and a tenanted policy for the same content mod we should only keep the tenanted version.
        if (mod.tenantId.toLowerCase() !== 'global') {
          return true;
        }

        if (
          parsedConfig.cloud_content_modifications.filter(
            (m) =>
              m.tenantId.toLowerCase() !== 'global' && m.blockmanId === mod.blockmanId && m.enabled,
          ).length > 0
        ) {
          return false;
        }
        return true;
      },
    );

    parsedConfig.flattenedContentMods = this._flattenContentModPolicyFolders(
      parsedConfig.content_modification,
    );

    if (parsedConfig.custom_categories !== undefined) {
      parsedConfig = this._parseCustomCategories(parsedConfig);
      // Extract Content Aware allow list URLs after custom categories have been parsed
      parsedConfig = this._extractContentAwareAllowList(parsedConfig);
    } else {
      parsedConfig.custom_categories = [];
    }
    parsedConfig.time_slots = this._filterTenants(parsedConfig.time_slots);

    if (this._tenantId === undefined) {
      parsedConfig.policies.tenants = {};
    } else {
      parsedConfig.policies.tenants = {
        [this._tenantId.toString()]: parsedConfig.policies.tenants[this._tenantId.toString()],
      };
    }

    parsedConfig.locations = this._filterTenants(parsedConfig.locations);

    parsedConfig.flattenedPolicies = this._flattenFilterPolicies(parsedConfig.policies);

    return parsedConfig;
  };

  /**
   * Extracts URLs from custom categories named "Content Aware Allow list" to create a list of URLs
   * that should be exempt from Content Aware filtering.
   *
   * @param policyConfig The policy configuration containing custom categories
   * @returns The policy configuration with contentAwareAllowList populated
   */
  private readonly _extractContentAwareAllowList = (policyConfig: IPolicyConfig): IPolicyConfig => {
    // Initialize the allow list as an empty array
    const allowList: string[] = [];

    // Find all custom categories with the exact name "Content Aware Allow list"
    const contentAwareAllowListCategories = policyConfig.custom_categories.filter(
      (category) => category.name === CONTENT_AWARE_CONSTANTS.ALLOW_LIST_CATEGORY_NAME,
    );

    // Extract URLs from the domainsurls component of matching categories
    for (const category of contentAwareAllowListCategories) {
      if (category.component?.domainsurls != null) {
        // Add all URLs from this category to the allow list
        allowList.push(...category.component.domainsurls);
      }
    }

    // Store the extracted URLs in the policy config
    policyConfig.contentAwareAllowList = allowList;

    return policyConfig;
  };

  /**
   * Filters the given array, removing any objects that are not assigned to the current tenant.
   *
   * It also keeps any 'global' objects.
   * @param arr The array of objects with a string property of tenant.
   * @param key The key of the property that stores the tenant id. Defaults to 'tenant'
   * @returns The array filtered to only contain global or tenanted objects.
   */
  private _filterTenants(arr: any[], key: string = 'tenant'): any[] {
    if (arr === undefined) {
      return [];
    }

    return arr.filter((i: any) => i[key] === this._tenantId?.toString() || i[key] === 'global');
  }

  /**
   * Flattens the given policy object into a list of separate policies that have been ordered.
   * @param policy The policy object to flatten.
   * @returns A flattened and ordered list of policies.
   */
  private readonly _flattenFilterPolicies = (policy: IPolicy): IFilterPolicyFlat[] => {
    let flatPolicies: IFilterPolicyFlat[] = [];

    // Flatten the before policies
    flatPolicies = flatPolicies.concat(this._flattenFilterPolicyFolder(policy.before));

    // If we are tenanted, flatten the tenant policies
    if (
      this._tenantId !== undefined &&
      policy.tenants !== undefined &&
      policy.tenants[this._tenantId.toString()] !== undefined
    ) {
      const tenantPolicies = [...policy.tenants[this._tenantId.toString()]];
      flatPolicies = flatPolicies.concat(this._flattenFilterPolicyFolder(tenantPolicies));
    }

    // Flatten the after policies
    flatPolicies = flatPolicies.concat(this._flattenFilterPolicyFolder(policy.after));

    return flatPolicies;
  };

  /**
   * Flattens the given list of policies.
   * @param policies The policies to be flattened.
   * @returns A list of flattened policies.
   */
  private readonly _flattenFilterPolicyFolder = (
    policies: IFilterPolicy[],
  ): IFilterPolicyFlat[] => {
    let flatPolicies: IFilterPolicyFlat[] = [];

    if (policies.length <= 0) {
      return [];
    }

    for (const policy of policies) {
      let policiesAfterFlattening: IFilterPolicyFlat[] = [];

      if (policy.enabled === 'on') {
        if (this._isPolicyFolder(policy)) {
          if (policy.children === undefined) {
            continue;
          }

          // Check if each child has an inherited field, and flatten if so
          for (const child of policy.children) {
            if (child.enabled === 'on') {
              if (child.who !== undefined && child.who[0] === 'CHILD') {
                child.who = policy.who;
              }

              if (child.what !== undefined && child.what[0] === 'CHILD') {
                child.what = policy.what;
              }

              if (child.where !== undefined && child.where[0] === 'CHILD') {
                child.where = policy.where;
              }

              if (child.when !== undefined && child.when[0] === 'CHILD') {
                child.when = policy.when;
              }

              if (child.action !== undefined && child.action === 'CHILD') {
                child.action = policy.action;
              }

              // Add it to the return list
              policiesAfterFlattening.push(child);
            }
          }
        } else {
          policiesAfterFlattening = [policy];
        }
      }

      if (policiesAfterFlattening.length > 0) {
        // Filter out any policies that we don't use in the extension.
        const validActions: string[] = [
          PolicyAction.allow,
          PolicyAction.whitelist,
          PolicyAction.block,
          PolicyAction.softBlock,
        ];
        policiesAfterFlattening = policiesAfterFlattening.filter((p) =>
          validActions.includes(p.action),
        );

        flatPolicies = flatPolicies.concat(policiesAfterFlattening);
      }
    }

    const orderedPolicies = this._orderPolicies(flatPolicies);

    return orderedPolicies;
  };

  /**
   * Performs the parsing needed to use the given custom categories for filtering.
   * @param customCategories The custom categories to parse.
   * @returns An array of custom categories that have been parsed.
   */
  private readonly _parseCustomCategories = (policyConfig: IPolicyConfig): IPolicyConfig => {
    // First filter out any categories that don't match our current tenant.
    policyConfig.custom_categories = policyConfig.custom_categories.filter(
      (c) =>
        c.tenant === undefined ||
        this._tenantId?.toString() === c.tenant ||
        c.tenant?.toLowerCase() === 'global',
    );

    const customSearchTerms: SearchTerms = {};

    // Split the search terms and parse them into a format we can use to filter.
    for (const category of policyConfig.custom_categories) {
      if (category.component?.searchterms === undefined) {
        continue;
      }

      const splitSearchTerms = this._splitSearchTerms(category.component.searchterms);

      for (const searchTerms of splitSearchTerms) {
        const newScore: SearchTermCategory = { category: category.category_id ?? '', score: 100 };
        const child: SearchTermChild = { catsAndScores: newScore, phrases: searchTerms.slice(1) };

        if (customSearchTerms[searchTerms[0]] !== undefined) {
          // Add any child phrases
          if (searchTerms.length > 2) {
            customSearchTerms[searchTerms[0]].children.push(child);
          } else {
            customSearchTerms[searchTerms[0]].catsAndScores.push(newScore);
          }
        } else {
          // Add a new top level phrase
          if (searchTerms.length > 1) {
            customSearchTerms[searchTerms[0]] = {
              catsAndScores: [],
              children: [child],
            };
          } else {
            customSearchTerms[searchTerms[0]] = {
              catsAndScores: [newScore],
              children: [],
            };
          }
        }
      }
    }

    policyConfig.customSearchTerms = customSearchTerms;

    return policyConfig;
  };

  /**
   * Given a config, with content mod policies, create a flat array of content mod policies
   * and then filter out ones that we could never match.
   *
   * Then add them to the config object
   */
  private readonly _flattenContentModPolicyFolders = (
    contentMods: IContentModPolicy[],
  ): IContentModPolicyFlat[] => {
    let policies: IContentModPolicyFlat[] = [];

    if (contentMods.length === 0) {
      return [];
    }

    const result = this._flattenSingleContentModFolder(contentMods);
    if (this._isContentModArray(result)) {
      policies = result;
    }

    return policies;
  };

  /**
   * Given a list of content modifications , check if each of them have children, if they do, fix all the inherited
   * values, and then return a flat list
   */
  private readonly _flattenSingleContentModFolder = (
    rawPolicies: IContentModPolicy[],
  ): IContentModPolicyFlat[] => {
    let flatPolicies: IContentModPolicyFlat[] = [];

    if (rawPolicies.length === 0) {
      return [];
    }

    for (const policy of rawPolicies) {
      let policiesAfterChildFlattening: IContentModPolicyFlat[] = [];

      if (policy.enabled !== 'on') {
        continue;
      }

      // Parents don't actually have to have children, best to check
      if (policy.children != null && Array.isArray(policy.children)) {
        // Check if each child has an inherited field, and flatten if so
        for (const child of policy.children) {
          if (child.enabled !== 'on') {
            continue;
          }

          if (child.who !== undefined && child.who[0] === 'CHILD') {
            child.who = policy.who;
          }

          if (child.what !== undefined && child.what[0] === 'CHILD') {
            child.what = policy.what;
          }

          if (child.where !== undefined && child.where[0] === 'CHILD') {
            child.where = policy.where;
          }

          if (child.ruleset !== undefined && child.ruleset[0] === 'CHILD') {
            child.ruleset = policy.ruleset;
          }

          if (child.action !== undefined && child.action === 'CHILD') {
            child.action = policy.action;
          }

          // Add it to the return list
          policiesAfterChildFlattening.push(child);
        }
      } else {
        policiesAfterChildFlattening = [policy];
      }

      if (policiesAfterChildFlattening.length > 0) {
        flatPolicies = flatPolicies.concat(policiesAfterChildFlattening);
      }
    }

    return flatPolicies;
  };

  /**
   * Formats custom search terms extracted from the policy. They arrive in an array of strings
   * that look like <phrase1><phrase2>, ie phrases with delimiters, which can be paired ( <> or |)
   *
   * @param searchTerms Array of custom search terms extracted from the policy
   * @returns Array of string arrays, each of which contains 1 or more strings
   */
  private readonly _splitSearchTerms = (searchTerms: string[]): string[][] => {
    const splitSearchTerms: string[][] = [];

    for (const searchTerm of searchTerms) {
      const startDelimiter = searchTerm[0];
      const endDelimiter = searchTerm[searchTerm.length - 1];

      const delimiterToSplit =
        startDelimiter === endDelimiter ? startDelimiter : endDelimiter + startDelimiter;

      const newSearchTerms = searchTerm
        .split(delimiterToSplit)
        .map((s) => {
          return s.replace(startDelimiter, '').replace(endDelimiter, '').toLowerCase();
        })
        .filter((t) => t !== '');

      if (newSearchTerms.length > 0) {
        splitSearchTerms.push(newSearchTerms);
      }
    }

    return splitSearchTerms;
  };

  /**
   * Checks if the given policy is a folder. Meaning it has parent fields or children.
   * @param policy The policy to check.
   * @returns A boolean indicating if the policy is a folder.
   */
  private readonly _isPolicyFolder = (policy: IFilterPolicy): boolean => {
    return (
      policy.children != null ||
      policy.what.includes('PARENT') ||
      policy.who.includes('PARENT') ||
      policy.when.includes('PARENT') ||
      policy.where.includes('PARENT') ||
      policy.action.includes('PARENT')
    );
  };

  /**
   * Checks if the given policies are all content mod policies.
   * @param policies The list of policies to check.
   * @returns If all objects in the list of policies are valid `IContentModPolicy` types.`
   */
  private readonly _isContentModArray = (policies: IBasePolicy[]): boolean => {
    let isContentModPolicyArray: boolean = true;

    for (const p of policies) {
      const result = this._isContentMod(p);

      if (!result) {
        isContentModPolicyArray = false;
      }
    }

    return isContentModPolicyArray;
  };

  /**
   * Checks if the given policy is a content mod policy.
   * @param policy The policy to check.
   * @returns If the policy is a valid `IContentModPolicy` type.
   */
  private readonly _isContentMod = (policy: IBasePolicy): boolean => {
    let isContentMod: boolean = true;

    if (
      !(
        (policy as IContentModPolicy).ruleset !== undefined &&
        Array.isArray((policy as IContentModPolicy).ruleset)
      )
    ) {
      isContentMod = false;
    }

    return isContentMod;
  };

  /**
   * Orders the given list of flat policies. Also sorts any child policies (eg: 1.1, 1.2).
   * @param policies The list of flat policies to order.
   * @returns A list of policies that have been ordered.
   */
  private readonly _orderPolicies = (policies: IFilterPolicyFlat[]): IFilterPolicyFlat[] => {
    return policies.sort((x, y) => {
      const xSplit = x.order.split('.');
      const ySplit = y.order.split('.');
      xSplit[1] = xSplit[1] ?? '0';
      ySplit[1] = ySplit[1] ?? '0';
      if (xSplit[0] === ySplit[0]) {
        const toReturn = xSplit[1] === ySplit[1] ? 0 : +xSplit[1] > +ySplit[1] ? 1 : -1;
        return toReturn;
      } else {
        const toReturn = +xSplit[0] > +ySplit[0] ? 1 : -1;
        return toReturn;
      }
    });
  };

  /**
   * Dispatched when the policy config has been successfully loaded.
   */
  public onPolicyDownloaded = new StandaloneEvent<[IPolicyConfig | undefined]>();

  /**
   * The blob storage service.
   */
  private readonly _blobStorageService: IBlobStorageService;

  /**
   * Stores the current tenant id if one exists.
   */
  private readonly _tenantId: TenantId | undefined;
}

import { Directory } from 'models/IProductConfig';
import TemplateString from 'models/TemplateString';
import TenantId from 'models/TenantId';
import StorageService from 'services/StorageService';
import { TelemetryEventType } from '../constants/TelemetryEventType';
import { areArraysEqual } from 'utilities/Helpers';
import StandaloneEvent from 'utilities/StandaloneEvent';
import ITelemetryService from './ITelemetryService';

/**
 * Indicates the status of an attempt to download groups.
 */
export enum DownloadResult {
  /** Awaiting the result of the request. */
  pending,

  /** No response received; this probably means the device is offline. */
  noResponse,

  /** Failed to download groups, e.g. because the groups file was not found or auth failed. */
  failed,

  /** Successfully downloaded groups, or the groups have not changed since previous download. */
  succeeded,
}

/**
 * Downloads and stores the list of directory groups which a named user is assigned to.
 * The directory groups are mapped onto Smoothwall/Qoria groups elsewhere (see the policy). The
 *  resulting mapped groups are used for matching the "who" conditions in filtering policies.
 */
export default class CloudGroupsService {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance with the given data.
   *
   * @param cloudGroupsUrlTemplate A template describing the URL to use for retrieving cloud groups.
   *  The placeholders are populated by data from directory config.
   * @param telemetryService Handles logging events to a telemetry service.
   * @param storageService Responsible for caching groups info to disk between runs.
   * @param tenantId The customer's tenant ID, if they have one. This is used to construct the
   *  download URL.
   * @param user The name of the user whose groups info we will download. This is used to construct
   *  the download URL.
   */
  constructor(
    cloudGroupsUrlTemplate: TemplateString,
    telemetryService: ITelemetryService,
    storageService: StorageService,
    tenantId: TenantId | undefined,
    user: string,
  ) {
    this._cloudGroupsUrlTemplate = cloudGroupsUrlTemplate;
    this._telemetryService = telemetryService;
    this._storageService = storageService;
    this._tenantId = tenantId;
    this._user = user;
  }

  // -----------------------------------------------------------------------------------------------
  // Public operations.

  /**
   * Load groups from the on-disk cache if possible.
   * If there are already groups in memory then this won't load anything from cache.
   * This should generally be called once on start-up, before setConfig().
   */
  public readonly loadFromCache = async (): Promise<void> => {
    if (this.cloudGroups.length === 0) {
      await this._storageService.load();
      console.debug('CloudGroupsService - Loaded directory groups from cache: ', this.cloudGroups);
    }

    if (this.cloudGroups.length > 0) {
      this._dispatchLoadedEvent();
    }
  };

  /**
   * Provide the directory configuration which specifies how and when to download groups.
   * If group download is enabled, this will setup a regular timer to ensure they are kept
   *  up-to-date. If the config has changed since the last call, or groups haven't been downloaded
   *  since start-up, then this will immediately trigger a download.
   *
   * @param directoryConfig Directory configuration, loaded from product config.
   *
   * @note It is the caller's responsibility to ensure loadFromCache() has been called at least
   *  once before this.
   */
  public readonly setConfig = (directoryConfig: Directory): void => {
    // If polling interval is zero then that means group download should be disabled.
    if (directoryConfig.polling === 0) {
      if (this._resourceURL !== undefined) {
        console.debug('CloudGroupsService - Groups download disabled in directory config.');
      }

      this.clear();

      // Important: Some other parts of the code wait for us to have groups info before progressing
      //  with filtering setup. We have to explicitly say we have no groups, otherwise we may get
      //  stuck in mini-filter mode.
      this._dispatchLoadedEvent();
      return;
    }

    const oldResourceURL = this._resourceURL;
    this._resourceURL = this._cloudGroupsUrlTemplate.toUrl({
      resource: directoryConfig.resource,
      tenant: this._tenantId?.toString() ?? 'untenanted',
      domain: btoa('sw__allclouddomains'),
      user: btoa(this._user.toLowerCase()), // <-- username must be lower-case or request may fail
      sas: directoryConfig.sas,
    });

    this._pollingTime = directoryConfig.polling * 1000;

    // Run a timer at least every 5 minutes to check whether to download groups.
    // Run it more frequently if necessary to match the polling frequency.
    clearInterval(this._downloadInterval);
    this._downloadInterval = setInterval(
      this._onDownloadInterval,
      Math.min(this._pollingTime, 300000),
    );

    // Always download groups immediately on start-up or when the config changes, even if we had
    //  groups cached from a previous run.
    if (oldResourceURL?.toString() !== this._resourceURL.toString()) {
      // An old e-tag is irrelevant if the config has changed.
      this._eTag = undefined;
      this._downloadGroups().catch(console.warn);
    } else {
      this._dispatchLoadedEvent();
    }
  };

  /**
   * Clear the stored groups to an empty list and stop any ongoing download timer.
   * This also clears the cache on disk.
   *
   * @note This doesn't cancel any download which is currently in progress.
   */
  public readonly clear = (): void => {
    clearInterval(this._downloadInterval);
    this._downloadInterval = undefined;
    this._eTag = undefined;
    this._resourceURL = undefined;
    this._pollingTime = undefined;
    this._lastRequestSentAt = undefined;
    this._lastDownloadResult = undefined;
    this.cloudGroups = [];
  };

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Start a groups download if necessary.
   * This is called regularly when the internal timer triggers.
   */
  private readonly _onDownloadInterval = (): void => {
    if (this.shouldDownloadGroupsNow()) {
      this._downloadGroups().catch(console.warn);
    }
  };

  /**
   * Send a request for the groups file and asynchronously handle the result.
   * The request is sent to the URL currently stored in this object.
   *
   * @return Returns a promise which resolves when the request has finished, whether it succeeded or
   *  failed. Errors will be handled internally. This not expected to reject.
   */
  private readonly _downloadGroups = async (): Promise<void> => {
    if (this._resourceURL === undefined) {
      return;
    }

    // If we have an etag from a previous download then include it in the headers. The server may
    //  use this to determine if the data has changed since then, and avoid transferring it again if
    //  not.
    const request = new Request(this._resourceURL);
    if (this._eTag !== undefined) {
      request.headers.append('If-None-Match', this._eTag);
    }

    this._lastRequestSentAt = Date.now();
    this._lastDownloadResult = DownloadResult.pending;

    let response: Response;
    try {
      response = await fetch(request);
    } catch (e: any) {
      // A fetch rejection most likely means the device is offline.
      this._lastDownloadResult = DownloadResult.noResponse;
      // Important: Other parts of code may be waiting for groups before continuing to set up
      //  filtering. Pretend we've downloaded them so we don't get stuck in mini-filter mode.
      // TODO: Handle this better in future.
      this._dispatchLoadedEvent();
      return;
    }

    if (this._resourceURL === undefined) {
      this._dispatchLoadedEvent();
      // Ignore the response. clear() was probably called while we were waiting for the download.
      return;
    }

    try {
      if (response.status === 304) {
        // Server reported that the groups haven't changed since the last download.
        this._lastDownloadResult = DownloadResult.succeeded;
        console.debug(
          'CloudGroupsService - Groups have not changed since the last download:',
          this.cloudGroups,
        );
        this._dispatchLoadedEvent();
        return;
      }

      if (response.ok) {
        // Server responded with the groups information.

        const oldGroups = this.cloudGroups;
        const body = await response.json();
        this.cloudGroups = CloudGroupsService.extractDirectoryGroupNames(body);
        console.debug('CloudGroupsService - Downloaded directory groups:', body.groups);
        this._eTag = response.headers.get('ETag') ?? undefined;

        // To reduce unnecessary telemetry, only log the download if this is the first one since
        //  start-up, or the groups have changed since the last download.
        if (this._isFirstRun || !areArraysEqual(body.groups, oldGroups)) {
          this._telemetryService.logEvent(TelemetryEventType.UserGroups, {
            username: body.username,
            numGroups: this.cloudGroups.length,
            path: this._resourceURL.pathname,
          });
        }

        this._lastDownloadResult = DownloadResult.succeeded;
        this._isFirstRun = false;
        this._dispatchLoadedEvent();
        return;
      }

      // If we reach here then the server likely responded with an error or other unexpected issue.

      this._telemetryService.logError(
        TelemetryEventType.UserGroupsFailed,
        `Groups download failed with status ${response.status}`,
        { path: this._resourceURL.pathname },
      );

      console.warn(`CloudGroupsService - Groups download failed with status ${response.status}`);
    } catch (e: any) {
      console.warn('CloudGroupsService - Unexpected error while handling groups download.', e);
      // Deliberately carry on to treat this as a failed download.
    }

    this._lastDownloadResult = DownloadResult.failed;

    // Important: Other parts of code may be waiting for groups before continuing to set up
    //  filtering. Pretend we've downloaded them so we don't get stuck in mini-filter mode.
    // TODO: Handle this better in future.
    this._dispatchLoadedEvent();
  };

  /**
   * Dispatches the onGroupsReady event.
   */
  private readonly _dispatchLoadedEvent = (): void => {
    this.onGroupsReady.dispatch(this.constructor.name);
  };

  // -----------------------------------------------------------------------------------------------
  // Events.

  /**
   * Dispatched when the groups are available in memory.
   */
  public readonly onGroupsReady = new StandaloneEvent<[string]>();

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Get a list of groups which the user is part of.
   * This will be an empty array if no groups have been downloaded yet.
   */
  public get cloudGroups(): string[] {
    return (this._storageService.get('cloudGroups') as string[]) ?? [];
  }

  /**
   * Update the stored list of groups which the user is part of.
   * This will store it in memory, and immediately save it to the on-disk cache.
   */
  private set cloudGroups(cloudGroups: string[]) {
    this._storageService.set('cloudGroups', cloudGroups);
    this._storageService.saveInTheBackground();
  }

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Determine if we should send a request to download groups right now.
   * This is based on the time since the last request (if any) and the result of that request.
   *
   * @returns True if a download should be attempted now, or false if not.
   */
  public readonly shouldDownloadGroupsNow = (): boolean => {
    // We can't send a request without suitable configuration.
    if (this._pollingTime === undefined) {
      return false;
    }

    // If we've never sent a request then download right away.
    if (this._lastDownloadResult === undefined || this._lastRequestSentAt === undefined) {
      return true;
    }

    // If the last request didn't receive a response then try again frequently.
    // This is to handle situations where the device is offline. We want to download groups fairly
    //  soon after it comes online to ensure we aren't stuck without proper filtering for too long/
    const timeSinceLastRequest = Date.now() - this._lastRequestSentAt;
    if (this._lastDownloadResult === DownloadResult.noResponse) {
      // Every 5 minutes, or the directory polling time, whichever is more frequent.
      return timeSinceLastRequest >= Math.min(300000, this._pollingTime);
    }

    // In all other situations, wait until the configured polling time has elapsed.
    return timeSinceLastRequest >= this._pollingTime;
  };

  /**
   * Extract and validate a list of directory groups from a server response.
   *
   * @param body The response body received from the download request. It should be an object which
   *  was populated from JSON (i.e. the result of `await response.json()`).
   * @returns An array of directory group names extracted from the given body. These will be the
   *  names of the groups which the user is a member of. It will be an empty array if the user is
   *  not a member of any directory groups.
   * @throws {Error} The directory groups are missing from the server response, or the groups are
   *  invalid.
   */
  public static readonly extractDirectoryGroupNames = (body: any): string[] => {
    if (body == null || body?.groups == null || !Array.isArray(body?.groups)) {
      throw new Error('Directory groups missing from server response.');
    }

    const groups = body.groups as any[];
    if (groups.some((group) => typeof group !== 'string')) {
      throw new Error('Invalid directory groups. Expected an array of strings.');
    }

    return groups.filter((group: string) => group !== '');
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Responsible for caching groups info to disk between runs.
   */
  private readonly _storageService: StorageService;

  /**
   * Handles logging events to a telemetry service.
   */
  private readonly _telemetryService: ITelemetryService;

  /**
   * A template describing the URL to use for retrieving cloud groups.
   */
  private readonly _cloudGroupsUrlTemplate: TemplateString;

  /**
   * The name of the user whose groups info we will download.
   * This is used to construct the download URL.
   */
  private readonly _user: string;

  /**
   * The customer's tenant ID, if they have one.
   * This is used to construct the download URL.
   */
  private readonly _tenantId?: TenantId;

  /**
   * Handle for a repeating timer which will schedule group download attempts.
   * This will be undefined if we don't have a configuration yet, or the configuration indicates
   *  that there will be no group downloads.
   */
  private _downloadInterval?: ReturnType<typeof setInterval>;

  /**
   * We will regularly download groups info from this URL.
   * This will be undefined if we haven't received any configuration yet, or the configuration
   *  explicitly disabled group download.
   *
   * @note The configuration is deliberately not cached in this object. We rely on product config
   *  to be cached elsewhere.
   */
  private _resourceURL?: URL;

  /**
   * The number of milliseconds to wait between consecutive downloads of the groups.
   * This will be undefined if we haven't received any configuration yet, or the configuration
   *  explicitly disabled group download.
   *
   * @note The configuration is deliberately not cached in this object. We rely on product config
   *  to be cached elsewhere.
   */
  private _pollingTime?: number;

  /**
   * Indicates if the active run is the first attempt.
   * This should only be set to false if the run was successful.
   */
  private _isFirstRun = true;

  /**
   * The e-tag of the most recent successful groups download.
   * This is used to check whether the groups file has changed without having to download it again.
   * It deliberately isn't cached between runs as we need to download groups from scratch on start.
   * It will be undefined if we haven't successfully downloaded groups yet, or groups download has
   *  been disabled.
   */
  private _eTag?: string;

  /**
   * Timestamp (in milliseconds) indicating when we last sent a request to download groups.
   * This is used to ensure we don't request too frequently.
   * It will be undefined if no request has been sent yet.
   */
  private _lastRequestSentAt?: number;

  /**
   * Indicates the result of the last attempt to download groups.
   * This will be undefined if no request has been sent yet.
   */
  private _lastDownloadResult?: DownloadResult;
}

import Blocklist from 'guardian/Blocklist';
import LZString from 'lz-string';

// Mini-blocklist data:
import domainsurlscom from 'mini-blocklist-data/domainsurlscom';
import domainsurlsnet from 'mini-blocklist-data/domainsurlsnet';
import domainsurlsorg from 'mini-blocklist-data/domainsurlsorg';
import domainsurlsother from 'mini-blocklist-data/domainsurlsother';
import domainsurlsuk from 'mini-blocklist-data/domainsurlsuk';
import iwflist from 'mini-blocklist-data/iwflist';
import removetags from 'mini-blocklist-data/removetags';
import searchengineregexplist from 'mini-blocklist-data/searchengineregexplist';
import searchterms from 'mini-blocklist-data/searchterms';
import videoidregexplist from 'mini-blocklist-data/videoidregexplist';
import videoids from 'mini-blocklist-data/videoids';
import weightedphrases from 'mini-blocklist-data/weightedphrases';
import loglevelrules from 'mini-blocklist-data/loglevelrules';
import categorydata from 'mini-blocklist-data/categorydata';

/**
 * Maps blocklist filenames to the hard-coded mini-blocklist data.
 * The filenames match equivalent functionality in the main blocklist data which we download.
 * Each value is a compressed string containing the contents of a blocklist file.
 */
const miniBlocklistData = {
  'domainsurls.com': domainsurlscom,
  'domainsurls.net': domainsurlsnet,
  'domainsurls.org': domainsurlsorg,
  'domainsurls.other': domainsurlsother,
  'domainsurls.uk': domainsurlsuk,
  iwflist,
  removetags,
  searchengineregexplist,
  searchterms,
  videoidregexplist,
  videoids,
  weightedphrases,
  loglevelrules,
  category_data: categorydata,
};

/**
 * Loads the mini-filter data into a blocklist.
 */
export default class MiniBlocklistService {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Optionally initialise this service to operate on the specified blocklist instance.
   *
   * @param blocklist The blocklist instance to operate on. If not specified, a new one will be
   *  created.
   */
  public constructor(blocklist?: Blocklist) {
    this.blocklist = blocklist ?? new Blocklist();
  }

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Load the hard-coded mini-filter data into the stored blocklist instance.
   */
  public readonly load = (): void => {
    const decompressedFiles: Record<string, string> = {};

    Object.entries(miniBlocklistData).forEach(([filename, data]): void => {
      try {
        const decompressedData = LZString.decompressFromBase64(data);
        if (typeof decompressedData !== 'string') {
          console.error(`Failed to decompress mini-blocklist file: ${filename}`);
          return;
        }
        decompressedFiles[filename] = decompressedData;
      } catch (e: any) {
        // Log any error, but carry on otherwise. We want to ensure that as much of the filter as
        //  possible is functional.
        console.error(e);
      }
    });
    this.blocklist.loadFromBlocklistFiles(decompressedFiles, 0, 'mini-filter', 'mini');
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The blocklist instance which this service is operating on.
   */
  public readonly blocklist: Blocklist;
}

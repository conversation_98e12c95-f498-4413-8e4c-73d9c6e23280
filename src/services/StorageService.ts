/**
 * Manages an in-memory cache of data which can be persisted to Chrome storage.
 * This is used to remember information information between invocations of the service worker.
 * The interface is intended to be similar to a JavaScript Map.
 *
 * @todo Add the ability to listen for (and automatically load) any changes from sync/managed data?
 */
export default class StorageService {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Initialise the name of the underlying cache data.
   * This does not load or save anything. Call load() after construction to populate the object
   *  from storage.
   *
   * @param name The name of the data being stored. This determines the location it is kept in
   *  local storage. It must not be empty. It is the caller's responsibility to ensure the name is
   *  unique.
   * @param storageArea The type of storage to load/save. It's usually local (chrome.storage.local),
   *  but it can also be sync or managed. Note that managed storage is read-only. If this is
   *  undefined then loading and saving will not occur; it will be an in-memory mode which is mainly
   *  intended for unit tests.
   * @throws {Error} The specified cache name is empty.
   *
   * @note Multiple instances of this class using the same cache name can have unexpected behaviour.
   *  It is the caller's responsibility to prevent that from happening.
   */
  public constructor(name: string, storageArea: chrome.storage.StorageArea | undefined) {
    StorageService.validateKey(name);
    this._name = name;
    this._storageArea = storageArea;
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Get the name of the data handled by this object.
   *
   * @returns Returns the name of the data handled by this object, as specified in the constructor.
   */
  public get name(): string {
    return this._name;
  }

  /**
   * Check if auto-saving is enabled.
   *
   * @returns Returns true if auto-save is enabled, or false if not. Default is false.
   *
   * @see enableAutoSave()
   * @see disableAutoSave()
   */
  public get isAutoSaveEnabled(): boolean {
    return this._isAutoSaveEnabled;
  }

  /**
   * Get the number of properties in the in-memory cache.
   *
   * @returns Returns the number of properties in the in-memory cache.
   */
  public get size(): number {
    return Object.keys(this._rawData).length;
  }

  /**
   * Get an iterator for all the properties in the in-memory cache.
   *
   * @returns Returns a new iterator object that contains the [name, value] pair for each property
   *  currently in the in-memory cache.
   */
  public readonly entries = (): IterableIterator<[string, unknown]> => {
    return Object.entries(this._rawData)[Symbol.iterator]();
  };

  /**
   * Get an iterator for all the properties in the in-memory cache.
   * This is an alias of `entries()`.
   *
   * @returns Returns a new iterator object that contains the [name, value] pair for each property
   *  stored in the in-memory cache.
   */
  public readonly [Symbol.iterator] = this.entries;

  /**
   * Get an iterator for the names of all the properties in the in-memory cache.
   *
   * @returns Returns a new iterator obejct containing the name of each property currently in the
   *  in-memory cache.
   */
  public readonly keys = (): IterableIterator<string> => {
    return Object.keys(this._rawData)[Symbol.iterator]();
  };

  /**
   * Get an iterator for the values of all the properties in the in-memory cache.
   *
   * @returns Returns a new iterator obejct containing the value of each property currently in the
   *  in-memory cache.
   */
  public readonly values = (): IterableIterator<unknown> => {
    return Object.values(this._rawData)[Symbol.iterator]();
  };

  /**
   * Execute the given callback for each property in the in-memory cache.
   *
   * @param callback The callback to execute for each property. It should take two arguments: a
   *  value (unknown type), followed by a name (string type).
   */
  public readonly forEach = (callback: (value: unknown, name: string) => void): void => {
    Object.entries(this._rawData).forEach((item: [string, any]): void => {
      callback(item[1], item[0]);
    });
  };

  /**
   * Check if the in-memory cache contains a property with the specified name.
   *
   * @param name The name of the property to get. It must not be empty, and it must not conflict with
   *  the name of a default object property (e.g. "hasOwnProperty" would not be allowed).
   * @returns Returns true if the named property exists in the in-memory cache, or false if not.
   * @throws {Error} The specified name is empty, or conflicts with a default object property.
   *
   * @see delete()
   */
  public readonly has = (name: string): boolean => {
    StorageService.validateKey(name);
    return Object.prototype.hasOwnProperty.call(this._rawData, name);
  };

  /**
   * Get a named property of any type from the in-memory cache.
   * This will not automatically load anything from storage. It is the caller's responsibility to
   *  ensure `load()` has been called beforehand if necessary.
   *
   * @param name The name of the property to get. It must not be empty, and it must not conflict with
   *  the name of a default object property (e.g. "hasOwnProperty" would not be allowed).
   * @returns Returns the named property if it exists in the in-memory cache, or undefined otherwise.
   *  The type of the property is not checked or constrained. It is the caller's responsibility to
   *  validate and cast it as necessary.
   * @throws {Error} The specified name is empty, or conflicts with a default object property.
   *
   * @note For type-safety, you are advised to use one of the type-specific getters instead, such as
   *  `getString()`.
   *
   * @see getString()
   * @see getNumber()
   * @see getBoolean()
   */
  public readonly get = (name: string): unknown => {
    StorageService.validateKey(name);
    return (this._rawData as any)[name];
  };

  /**
   * Get a named string property from the in-memory cache.
   * This will not automatically load anything from storage. It is the caller's responsibility to
   *  ensure `load()` has been called beforehand if necessary.
   *
   * @param name The name of the property to get. It must not be empty, and it must not conflict with
   *  the name of a default object property (e.g. "hasOwnProperty" would not be allowed).
   * @returns Returns the named property if it exists in the in-memory cache and is a string. Returns
   *  undefined otherwise.  Note that the type check is strict -- this will not convert another type
   *  to string.
   * @throws {Error} The specified name is empty, or conflicts with a default object property.
   *
   * @see get()
   * @see getNumber()
   * @see getBoolean()
   */
  public readonly getString = (name: string): string | undefined => {
    const value = this.get(name);
    return typeof value === 'string' ? value : undefined;
  };

  /**
   * Get a named numeric property from the in-memory cache.
   * This will not automatically load anything from storage. It is the caller's responsibility to
   *  ensure `load()` has been called beforehand if necessary.
   *
   * @param name The name of the property to get. It must not be empty, and it must not conflict with
   *  the name of a default object property (e.g. "hasOwnProperty" would not be allowed).
   * @returns Returns the named property if it exists in the in-memory cache and is a number. Returns
   *  undefined otherwise. Note that the type check is strict -- this will not convert another type
   *  to a number.
   * @throws {Error} The specified name is empty, or conflicts with a default object property.
   *
   * @see get()
   * @see getString()
   * @see getBoolean()
   */
  public readonly getNumber = (name: string): number | undefined => {
    const value = this.get(name);
    return typeof value === 'number' ? value : undefined;
  };

  /**
   * Get a named boolean property from the in-memory cache.
   * This will not automatically load anything from storage. It is the caller's responsibility to
   *  ensure `load()` has been called beforehand if necessary.
   *
   * @param name The name of the property to get. It must not be empty, and it must not conflict with
   *  the name of a default object property (e.g. "hasOwnProperty" would not be allowed).
   * @returns Returns the named property if it exists in the in-memory cache and is a boolean. Returns
   *  undefined otherwise. Note that the type check is strict -- this will not convert another type
   *  to boolean.
   * @throws {Error} The specified name is empty, or conflicts with a default object property.
   *
   * @see get()
   * @see getString()
   * @see getNumber()
   */
  public readonly getBoolean = (name: string): boolean | undefined => {
    const value = this.get(name);
    return typeof value === 'boolean' ? value : undefined;
  };

  /**
   * Store a named property of any type in the in-memory cache.
   * This can be used to add a new property or change an existing one.
   * If auto-save is enabled, the in-memory cache will be persisted to storage after the change.
   *
   * @param name The name of the property to set. It must not be empty, and it must not conflict with
   *  the name of a default object property (e.g. "hasOwnProperty" would not be allowed).
   * @param value The value to store in the property. It can be any type except undefined. Complex
   *  objects are not recommended.
   * @throws {Error} The specified name is empty, or conflicts with a default object property.
   *
   * @note If you want to remove a property from the cache then call `removeProperty()` instead of setting
   *  the property to undefined.
   * @warning Auto-save should be used with caution. It can have unpredictable results if load and
   *  save operations are interleaved. Auto-save also overwrites anything already persisted in the
   *  named cache (i.e. it does not merge with properties already present), meaning data loss can occur
   *  if the in-memory cache is out-of-sync with storage.
   *
   * @see get()
   * @see delete()
   * @see enableAutoSave()
   */
  public readonly set = (name: string, value: unknown): void => {
    StorageService.validateKey(name);

    if (value === undefined) {
      this.delete(name);
    } else {
      (this._rawData as any)[name] = value;
    }

    if (this._isAutoSaveEnabled) {
      this.saveInTheBackground();
    }
  };

  /**
   * Remove a named property from the in-memory cache.
   * If auto-save is enabled, the in-memory cache will be persisted to storage after the change.
   *
   * @param name The name of the property to remove. It must not be empty, and it must not conflict
   *  with the name of a default object property (e.g. "hasOwnProperty" would not be allowed).
   * @throws {Error} The specified name is empty, or conflicts with a default object property.
   *
   * @warning Auto-save should be used with caution. It can have unpredictable results if load and
   *  save operations are interleaved. Auto-save also overwrites anything already persisted in the
   *  named cache (i.e. it does not merge with properties already present), meaning data loss can occur
   *  if the in-memory cache is out-of-sync with storage.
   *
   * @see has()
   * @see enableAutoSave()
   */
  public readonly delete = (name: string): void => {
    StorageService.validateKey(name);

    // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
    delete (this._rawData as any)[name];

    if (this._isAutoSaveEnabled) {
      this.saveInTheBackground();
    }
  };

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Whenever a change is made to the in-memory cache, automatically persist it to storage.
   * Auto-save is disabled by default.
   *
   * @warning Auto-save should be used with caution. It can have unpredictable results if load and
   *  save operations are interleaved. Auto-save also overwrites anything already persisted in the
   *  named cache (i.e. it does not merge with properties already present), meaning data loss can occur
   *  if the in-memory cache is out-of-sync with storage.
   *
   * @see isAutoSaveEnabled
   * @see disableAutoSave()
   */
  public readonly enableAutoSave = (): void => {
    this._isAutoSaveEnabled = true;
  };

  /**
   * Prevent changes from being automatically persisted to storage.
   * Auto-save is disabled by default.
   *
   * @see isAutoSaveEnabled
   * @see enableAutoSave()
   */
  public readonly disableAutoSave = (): void => {
    this._isAutoSaveEnabled = false;
  };

  /**
   * Load the data from storage into the in-memory cache.
   * If we have no storage area, then this acts as though the storage was empty.
   *
   * @param merge If true, the data in storage will be merged into the data already stored in memory
   *  (i.e. in this object). If any named property exists in both, then the property from storage will
   *  take priority. If false, existing data in memory will be cleared.
   * @returns Returns a promise which resolves to true if the data was successfully loaded from
   *  storage (regardless of whether it actually contains any data). Resolves to false if no data
   *  was found in storage. The promise rejects if storage access failed, or the data was found but
   *  did not match the expected data type.
   */
  public readonly load = async (merge: boolean = false): Promise<boolean> => {
    if (!merge) {
      this.clearMemory();
    }

    if (this._storageArea === undefined) {
      // We're acting as just an in-memory cache so there's nothing to load.
      return false;
    }

    const newData = (await this._storageArea.get(this._name))?.[this._name];
    if (newData === undefined) {
      // There was no data found in storage.
      return false;
    }

    if (typeof newData === 'object' && newData !== null) {
      this._rawData = Object.assign(this._rawData, newData);
      return true;
    }

    throw new Error(`StorageService failed to load "${this._name}". Expected an object.`);
  };

  /**
   * Populate the in-memory cache from an existing object.
   * This can be used instead of load() if storage was already loaded elsewhere.
   *
   * @param data The data to populate the in-memory cache with. If it is null or undefined then it
   *  will be treated as though it was an empty object; i.e. the in-memory cache will be cleared.
   * @throws {Error} The specified data argument is not an object, null, or undefined.
   */
  public readonly populate = (data: Record<string, any> | undefined): void => {
    this.clearMemory();
    if (data == null) {
      return;
    }
    if (typeof data !== 'object') {
      throw new Error(`StorageService cannot be populated. Expected an object.`);
    }
    this._rawData = data;
  };

  /**
   * Write the in-memory cache properties to persistent storage.
   * This does nothing if we have no storage area.
   *
   * @param merge If false, the properties in storage will be replaced by whatever is in memory. If
   *  true, the data in memory will be merged into the data already in storage, overwriting any
   *  conflicts and retaining everything else. WARNING: Merging involves reloading storage again
   *  before saving it, meaning it can be slow with a lot of data.
   *
   * @returns Returns a promise which resolves when the data has been successfully saved. The
   *  promise rejects if storage failed.
   */
  public readonly save = async (merge: boolean = false): Promise<void> => {
    if (this._storageArea === undefined) {
      // We're acting as just an in-memory cache so there's nothing to save.
      return;
    }

    // Use a local copy in case the in-memory object changes while we're waiting for the promises to
    //  resolve.
    let data = Object.assign({}, this._rawData);

    if (merge) {
      const oldData = (await this._storageArea.get(this._name))?.[this._name];
      Object.assign(oldData, data);
      data = oldData;
    }

    await this._storageArea.set({ [this._name]: data });
  };

  /**
   * A convenience wrapper around `save()`.
   * This will start saving the in-memory cache to storage, but won't wait for it to finish before
   *  returning. It is used by the auto-save mechanism.
   */
  public readonly saveInTheBackground = (): void => {
    this.save().catch((error: any) => {
      console.warn(`StorageService failed to save: ${this._name}`, error);
    });
  };

  /**
   * Delete the data from the in-memory cache and from storage.
   *
   * @returns Returns a promise which resolves when the data has been deleted from storage. It will
   *  reject if storage access failed.
   */
  public readonly clear = async (): Promise<void> => {
    this.clearMemory();
    await this.clearStorage();
  };

  /**
   * Delete the in-memory cache.
   * This does not change anything in storage.
   */
  public readonly clearMemory = (): void => {
    this._rawData = {};
  };

  /**
   * Delete the data from storage.
   * This does not change the in-memory cache.
   * This does nothing at all if we have no storage area.
   *
   * @returns Returns a promise which resolves when the data has been deleted from storage. It will
   *  reject if storage access failed.
   */
  public readonly clearStorage = async (): Promise<void> => {
    if (this._storageArea === undefined) {
      return;
    }

    await this._storageArea.remove(this._name);
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Ensure the specified string is valid as a cache or property name.
   * This is used to ensure the name won't have any undesired side-effects. For example, by
   *  overwriting or removing default object properties.
   *
   * @param name The string to validate.
   * @throws {Error} The specified string is not valid as a cache or property name.
   */
  public static readonly validateKey = (name: string): void => {
    if (name === '') {
      throw new Error('Cache key must not be empty.');
    }

    if (name in {}) {
      throw new Error('Cache key must not conflict with a default object property name.');
    }
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The name of the data handled by this object.
   * This determines where it is stored.
   */
  private readonly _name: string;

  /**
   * The type of storage which this object will load/save.
   * If undefined, loading and saving will not occur. This object will be purely an in-memory cache.
   */
  private readonly _storageArea?: chrome.storage.StorageArea;

  /**
   * Determines whether data changes will be automatically persisted to storage.
   * If true, save() will be called implicitly in the background each time a property changes. This
   *  should be used with caution as it can have unexpected results if multiple load and save
   *  operations are interleaved in quick succession.
   * If false, save() will need to be called explicitly to persist any changes.
   */
  private _isAutoSaveEnabled: boolean = false;

  /**
   * Contains the raw data loaded from local storage.
   * This will be an empty object if we haven't loaded anything yet, or there was nothing in storage
   *  to be loaded.
   */
  private _rawData: object = {};
}

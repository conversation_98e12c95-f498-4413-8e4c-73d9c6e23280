import VideoUrlLookupService from './VideoUrlLookupService';

describe('videoUrlLookupService', () => {
  describe('removeVideoUrlByTabId', () => {
    const videoUrlLookupService = new VideoUrlLookupService();
    const tab1Id = 503995893;
    const tab2Id = 503995890;
    it('adds entries to the lookup table', () => {
      videoUrlLookupService.saveVideoUrlToLookup(tab1Id, 0, 'url');
      videoUrlLookupService.saveVideoUrlToLookup(tab1Id, 1, 'url');
      videoUrlLookupService.saveVideoUrlToLookup(tab1Id, 3, 'url');
      videoUrlLookupService.saveVideoUrlToLookup(tab1Id, 5, 'url');
      videoUrlLookupService.saveVideoUrlToLookup(tab2Id, 0, 'url');

      const numberOfEntriesBeforeDelete =
        videoUrlLookupService.getVideoUrlsForTabId(tab1Id)?.size ?? 0;
      expect(numberOfEntriesBeforeDelete).toBe(4);
    });

    it('removes entries for a given tab', () => {
      videoUrlLookupService.removeVideoUrlsForTabId(tab1Id);
      const numberOfEntriesAfterDelete =
        videoUrlLookupService.getVideoUrlsForTabId(tab1Id)?.size ?? 0;
      expect(numberOfEntriesAfterDelete).toBe(0);
    });
  });

  describe('removeVideoUrlByTabFrame', () => {
    const videoUrlLookupService = new VideoUrlLookupService();
    const tab1Id = 503995893;
    const tab2Id = 503995890;

    it('adds two entries to lookup', () => {
      videoUrlLookupService.saveVideoUrlToLookup(tab1Id, 0, 'url');
      videoUrlLookupService.saveVideoUrlToLookup(tab2Id, 0, 'url');

      const numberOfTab1EntriesAfterDelete =
        videoUrlLookupService.getVideoUrlsForTabId(tab1Id)?.size ?? 0;
      expect(numberOfTab1EntriesAfterDelete).toBe(1);
      const numberOfTab2EntriesAfterDelete =
        videoUrlLookupService.getVideoUrlsForTabId(tab2Id)?.size ?? 0;
      expect(numberOfTab2EntriesAfterDelete).toBe(1);
    });

    it('removes tab 1 frame 0 from list', () => {
      videoUrlLookupService.removeTabFrameFromLookup(tab1Id, 0);

      const numberOfTab1EntriesAfterDelete =
        videoUrlLookupService.getVideoUrlsForTabId(tab1Id)?.size ?? 0;
      expect(numberOfTab1EntriesAfterDelete).toBe(0);
      const numberOfTab2EntriesAfterDelete =
        videoUrlLookupService.getVideoUrlsForTabId(tab2Id)?.size ?? 0;
      expect(numberOfTab2EntriesAfterDelete).toBe(1);
    });
  });
});

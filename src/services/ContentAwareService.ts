import {
  CONTENT_AWARE_EXTENSION_IDS,
  CONTENT_AWARE_EXTENSION_NAMES,
  CONTENT_AWARE_HEALTH_CHECK_INTERVAL,
  CONTENT_AWARE_MESSAGE_TIMEOUT,
  ContentAwareMessageTypes,
} from 'constants/ContentAwareTypes';
import { TelemetryEventType } from '../constants/TelemetryEventType';
import {
  ContentAwareBaseResponseMessage,
  IContentAwareConfigAllResponseMessage,
  IContentAwareUpdateConfigAllRequestMessage,
  IContentAwareExtensionInfo,
  IContentAwareIsLoggedInResponseMessage,
  IContentAwareLoginRequestMessage,
  IContentAwareLogoutRequestMessage,
  IContentAwareRequestMessagesUnion,
  IContentAwareLoginResponseMessage,
  IContentAwareLogoutResponseMessage,
  IContentAwareIsLoggedInRequestMessage,
} from 'models/ContentAwareModels';
import { DMSContentAwareRootConfig } from 'models/IProductConfig';
import StandaloneEvent from 'utilities/StandaloneEvent';

import ITelemetryService from './ITelemetryService';
import PolicyService from './PolicyService';
import ProductConfigService from './ProductConfigService';

/**
 * Enum representing telemetry types for the Content Aware extension.
 *
 * @remarks
 * Each telemetry type corresponds to a specific error or status event
 * related to the Content Aware extension.
 */
enum ContentAwareTelemetryType {
  /**
   * An error occurred during initialisation of the Content Aware logic
   * in the filter extension (see ContentAwareService.start()).
   */
  START = 'start',

  /**
   * Login to the Content Aware extension failed, e.g. due to the licence
   * being rejected. This should only be logged the first time login fails,
   * or if the last attempt succeeded.
   */
  LOGIN = 'login',

  /**
   * Failed to send config to the Content Aware extension, or it was rejected.
   * This should only be logged the first time config fails, or if the last
   * attempt succeeded.
   */
  CONFIG = 'config',

  /**
   * Failed to send a message to the Content Aware extension, or it was rejected.
   * This should only be logged the first time it occurs since start-up or since
   * the last successful login.
   */
  MESSAGE = 'message',

  /**
   * Failed to send a health-check to the Content Aware extension, or it responded
   * with an error, or it failed to respond at all. This should only be logged the
   * first time it occurs since the last successful login.
   */
  HEALTH_CHECK = 'health-check',

  /**
   * Content Aware extension unexpectedly reported that it is inactive
   * (i.e. no longer logged-in). This should only be logged the first time
   * it occurs since the last successful login.
   */
  LOGGED_OUT = 'logged-out',
}

/**
 * Service that manages communication with the Content Aware extension.
 *
 * This service is responsible for:
 * - Detecting if the Content Aware extension is installed
 * - Logging in/out of the Content Aware extension based on license status
 * - Sending configuration updates to the Content Aware extension
 * - Performing health checks to verify the extension is functioning
 */
export default class ContentAwareService {
  private readonly VENDOR_LICENSE_KEY = 'BC0859-B79F19-6342DC-D4BF65-69CC0C-V3';
  /**
   * Creates a new instance of the ContentAwareService.
   *
   * @param productConfigService Service that provides Content Aware configuration from Firestore
   * @param policyService Service that provides Content Aware allow list from policy
   * @param telemetryService Service for logging telemetry events
   */
  constructor(
    private readonly _productConfigService: ProductConfigService,
    private readonly _policyService: PolicyService,
    private readonly _telemetryService: ITelemetryService,
  ) {
    // Listen for product config changes to update Content Aware extension
    this._productConfigService.onProductConfigChanged.addListener(this._onProductConfigChanged);

    // Listen for policy changes to update allow list
    this._policyService.onPolicyLoaded.addListener(this._onPolicyLoaded);

    // Listen for extension management events to handle dynamic installation/uninstallation
    this._setupExtensionManagementListeners();
  }

  /**
   * Starts the Content Aware service.
   *
   * This will:
   * 1. Check if the Content Aware extension is currently installed
   * 2. If found and enabled, establish communication and start health checks
   * 3. If not found or disabled, continue running to handle dynamic installation/enabling
   * 4. Set up listeners for extension management events to handle changes dynamically
   *
   * The service will continue running even if the Content Aware extension is not initially
   * available, and will automatically establish communication when the extension becomes available.
   */
  public readonly start = async (): Promise<void> => {
    if (this._isRunning) {
      return;
    }

    this._isRunning = true;
    console.debug('ContentAwareService - Starting service');

    try {
      // Check if the Content Aware extension is currently installed
      const extensionInfo = await this._detectContentAwareExtension();
      if (extensionInfo === undefined) {
        console.debug(
          'ContentAwareService - Content Aware extension not found at startup, will wait for installation',
        );
        // Log extension status as "not-installed"
        this._logExtensionStatus();
        // Don't return - continue running to handle dynamic installation
      } else {
        this._extensionInfo = extensionInfo;
        console.debug(
          `ContentAwareService - Found Content Aware extension: ${extensionInfo.name} (${extensionInfo.id})`,
        );
        // Log extension status (either "enabled" or "disabled")
        this._logExtensionStatus(extensionInfo);

        // Check if the extension is enabled
        if (!extensionInfo.enabled) {
          console.debug('ContentAwareService - Content Aware extension is disabled at startup');
          // Don't return - continue running to handle dynamic enabling
        } else {
          // Extension is available and enabled, try to establish communication
          await this._updateContentAwareStatus();
          this._startHealthCheckInterval();
        }
      }
    } catch (error: any) {
      console.error('ContentAwareService - Error starting service', error);
      this._logContentAwareError(ContentAwareTelemetryType.START, true, error.message, error);
    }
  };

  /**
   * Stops the Content Aware service.
   *
   * This will:
   * 1. Stop the health check interval
   * 2. Log out of the Content Aware extension if it was logged in
   * 3. Remove extension management event listeners
   */
  public readonly stop = async (): Promise<void> => {
    if (!this._isRunning) {
      return;
    }

    console.debug('ContentAwareService - Stopping service');

    // Stop health check interval
    this._stopHealthCheckInterval();

    // Log out if we were logged in
    if (this._isLoggedIn && this._extensionInfo !== undefined) {
      await this._sendLogoutMessage();
    }

    // Remove extension management event listeners
    this._removeExtensionManagementListeners();

    this._isRunning = false;
    this._isLoggedIn = false;
    this._extensionInfo = undefined;
  };

  /**
   * Detects if the Content Aware extension is installed.
   *
   * @returns Information about the Content Aware extension if found, undefined otherwise
   */
  private readonly _detectContentAwareExtension = async (): Promise<
    IContentAwareExtensionInfo | undefined
  > => {
    try {
      const extensions = await chrome.management.getAll();

      // Search for Content Aware extension by known IDs and names
      const knownExtensionIds = Object.values(CONTENT_AWARE_EXTENSION_IDS) as string[];
      const knownExtensionNames = CONTENT_AWARE_EXTENSION_NAMES;

      let contentAwareExtension: chrome.management.ExtensionInfo | undefined;

      for (const ext of extensions) {
        // Check if extension ID matches any known Content Aware extension IDs
        if (knownExtensionIds.includes(ext.id)) {
          contentAwareExtension = ext;
          break;
        }

        // Check if extension name matches any known Content Aware extension names
        for (const name of knownExtensionNames) {
          if (ext.name.includes(name)) {
            contentAwareExtension = ext;
            break;
          }
        }

        if (contentAwareExtension !== undefined) {
          break;
        }
      }

      if (contentAwareExtension === undefined) {
        return undefined;
      }

      return {
        id: contentAwareExtension.id,
        name: contentAwareExtension.name,
        version: contentAwareExtension.version,
        enabled: contentAwareExtension.enabled,
      };
    } catch (error: any) {
      console.error('ContentAwareService - Error detecting Content Aware extension', error);
      // Don't log telemetry for detection failed - treat as extension simply not being present
      return undefined;
    }
  };

  /**
   * Updates the Content Aware status based on current configuration.
   *
   * This will:
   * 1. Check if there's a valid Content Aware license
   * 2. Log in or out of the Content Aware extension based on license status
   * 3. Send updated configuration if logged in
   */
  private readonly _updateContentAwareStatus = async (): Promise<void> => {
    console.debug('ContentAwareService - Updating Content Aware status');

    if (this._extensionInfo === undefined) {
      console.debug('ContentAwareService - No extension info available, cannot update status');
      return;
    }

    try {
      const config = this._productConfigService.getContentAwareConfig();
      const isConfigurationEnabled = config?.configuration?.ia_license.status === 'ACTIVE';

      console.debug('ContentAwareService - Status update details:', {
        hasConfig: config !== undefined,
        isEnabled: isConfigurationEnabled,
        isLoggedIn: this._isLoggedIn,
        extensionId: this._extensionInfo.id,
        configObject: config != null ? JSON.stringify(config, null, 2) : 'null',
      });

      // No valid config but we're logged in, so log out
      if (!isConfigurationEnabled && this._isLoggedIn) {
        console.debug('ContentAwareService - Config disabled but logged in, logging out');
        await this._sendLogoutMessage();
        return;
      }

      // No valid config and not logged in, nothing to do
      if (!isConfigurationEnabled) {
        console.debug(
          'ContentAwareService - No valid/enabled config and not logged in, nothing to do',
        );
        return;
      }

      // We have valid config, handle login and configuration
      if (!this._isLoggedIn) {
        console.debug('ContentAwareService - Valid config found, attempting login');
        const loginSuccess = await this._sendLoginMessage(config);
        if (!loginSuccess) {
          console.debug('ContentAwareService - Login failed, cannot send configuration');
          return;
        }
      }

      // Send configuration (either after successful login or if already logged in)
      console.debug('ContentAwareService - Sending configuration update');
      await this._sendConfigMessage(config);
    } catch (error: any) {
      console.error('ContentAwareService - Error updating Content Aware status', error);
      this._logContentAwareError(ContentAwareTelemetryType.START, false, error.message, error);
    }
  };

  /**
   * Sends a login message to the Content Aware extension.
   *
   * @param config The Content Aware configuration
   * @returns True if login was successful, false otherwise
   */
  private readonly _sendLoginMessage = async (
    config: DMSContentAwareRootConfig,
  ): Promise<boolean> => {
    if (this._extensionInfo === undefined) {
      return false;
    }

    try {
      console.debug('ContentAwareService - Sending login message to Content Aware extension');

      const message: IContentAwareLoginRequestMessage = {
        MessageType: ContentAwareMessageTypes.LOGIN,
        Data: {
          VendorLicenseKey: this.VENDOR_LICENSE_KEY,
          OrganisationID: config.configuration.ia_license.key,
        },
      };

      const response =
        await this._sendMessageToContentAwareExtension<IContentAwareLoginResponseMessage>(message);

      console.debug('ContentAwareService - Login response received:', {
        hasResponse: response !== undefined,
        response,
      });

      if (response?.Success === true) {
        console.debug('ContentAwareService - Login successful');
        this._isLoggedIn = true;
        this._logLoginSuccess();
        this._clearErrorTracking(ContentAwareTelemetryType.LOGIN); // Clear login error tracking on success

        // Fire status change event
        this.onStatusChanged.dispatch(true, 'Login successful');

        return true;
      }

      console.error('ContentAwareService - Login failed', {
        response,
      });
      this._logContentAwareError(ContentAwareTelemetryType.LOGIN, true, 'Login failed', {
        response,
      });
      return false;
    } catch (error: any) {
      console.error('ContentAwareService - Error sending login message', error);
      this._logContentAwareError(ContentAwareTelemetryType.LOGIN, true, error.message, error);
      return false;
    }
  };

  /**
   * Sends a configuration message to the Content Aware extension.
   *
   * @param config The Content Aware configuration
   * @returns True if configuration was successfully sent, false otherwise
   */
  private readonly _sendConfigMessage = async (
    config: DMSContentAwareRootConfig,
  ): Promise<boolean> => {
    if (this._extensionInfo === undefined || !this._isLoggedIn) {
      console.debug(
        'ContentAwareService - Cannot send config: extensionInfo or login status invalid',
        {
          hasExtensionInfo: this._extensionInfo !== undefined,
          isLoggedIn: this._isLoggedIn,
        },
      );
      return false;
    }

    // map neverRunOnDMS to neverRunOn if it exists
    const neverRunOn = [];
    if (config.configuration.domainConfig.resource.neverRunOnDMS !== undefined) {
      for (const [value] of Object.entries(
        config.configuration.domainConfig.resource.neverRunOnDMS,
      )) {
        neverRunOn.push(value);
      }
    }

    try {
      console.debug('ContentAwareService - Sending configuration to Content Aware extension');
      console.debug('ContentAwareService - Full config object:', JSON.stringify(config, null, 2));

      const message: IContentAwareUpdateConfigAllRequestMessage = {
        MessageType: ContentAwareMessageTypes.UPDATE_CONFIG_ALL,
        Data: {
          DomainConfiguration: {
            DefaultRules: {
              swimwear: config.configuration.domainConfig.resource.defaultRules.swimwear,
              porn: config.configuration.domainConfig.resource.defaultRules.porn,
              gore: config.configuration.domainConfig.resource.defaultRules.gore,
            },
            GuiltByAssociationEnabled:
              config.configuration.domainConfig.resource.guiltByAssociationEnabled,
            GuiltByAssociationBlockThreshold:
              config.configuration.domainConfig.resource.guiltByAssociationBlockThreshold,
            GuiltByAssociationIgnoreAfterCleanImagesThreshold:
              config.configuration.domainConfig.resource
                .guiltByAssociationIgnoreAfterCleanImagesThreshold,
            NeverRunOn: neverRunOn,
          },
        },
      };

      if (this._policyService.contentAwareAllowList !== undefined) {
        message.Data.DomainConfiguration.NeverRunOn = this._policyService.contentAwareAllowList;
      }

      console.debug('ContentAwareService - Message being sent:', JSON.stringify(message, null, 2));

      const response =
        await this._sendMessageToContentAwareExtension<IContentAwareConfigAllResponseMessage>(
          message,
        );

      console.debug('ContentAwareService - Configuration response received:', {
        response: response != null ? JSON.stringify(response, null, 2) : 'null',
      });

      if (response?.Success === true) {
        console.debug('ContentAwareService - Configuration update successful');
        this._logConfigSuccess();
        this._clearErrorTracking(ContentAwareTelemetryType.CONFIG); // Clear config error tracking on success
        return true;
      } else {
        console.error('ContentAwareService - Configuration update failed', {
          response,
          sentMessage: message,
        });
        this._logContentAwareError(
          ContentAwareTelemetryType.CONFIG,
          false,
          'Unknown error',
          response,
        );
        return false;
      }
    } catch (error: any) {
      console.error('ContentAwareService - Error sending configuration', error);
      this._logContentAwareError(ContentAwareTelemetryType.CONFIG, false, error.message, error);
      return false;
    }
  };

  /**
   * Sends a logout message to the Content Aware extension.
   *
   * @param reason Optional reason for logout
   * @returns True if logout was successful, false otherwise
   */
  private readonly _sendLogoutMessage = async (reason?: string): Promise<boolean> => {
    if (this._extensionInfo === undefined) {
      return false;
    }

    try {
      console.debug('ContentAwareService - Sending logout message to Content Aware extension');

      const message: IContentAwareLogoutRequestMessage = {
        MessageType: ContentAwareMessageTypes.LOGOUT,
      };

      const response =
        await this._sendMessageToContentAwareExtension<IContentAwareLogoutResponseMessage>(message);

      if (response?.Success === true) {
        console.debug('ContentAwareService - Logout successful');
        this._isLoggedIn = false;
        this._clearLoginConfigTracking(); // Reset login/config tracking

        // Fire status change event
        this.onStatusChanged.dispatch(false, reason ?? 'Logout successful');

        return true;
      } else {
        console.error('ContentAwareService - Logout failed', response);
        this._logContentAwareError(
          ContentAwareTelemetryType.MESSAGE,
          false,
          'Logout message failed',
          response,
        );
        return false;
      }
    } catch (error: any) {
      console.error('ContentAwareService - Error sending logout message', error);
      this._logContentAwareError(ContentAwareTelemetryType.MESSAGE, false, error.message, error);
      return false;
    }
  };

  /**
   * Sends a is-logged-in message to the Content Aware extension. Can be used as a form of health check
   *
   * @returns
   */
  private readonly _sendIsLoggedInMessage = async (): Promise<
    IContentAwareIsLoggedInResponseMessage | undefined
  > => {
    if (this._extensionInfo === undefined || !this._isLoggedIn) {
      return undefined;
    }

    try {
      const message: IContentAwareIsLoggedInRequestMessage = {
        MessageType: ContentAwareMessageTypes.IS_LOGGED_IN,
      };

      const response =
        await this._sendMessageToContentAwareExtension<IContentAwareIsLoggedInResponseMessage>(
          message,
        );

      if (response?.Success === true) {
        this._clearErrorTracking(ContentAwareTelemetryType.HEALTH_CHECK); // Clear health check error tracking on success

        // If the content aware extension reports its not logged in, then retry logging in
        if (!response.IsLoggedIn && this._isLoggedIn) {
          console.warn(
            'ContentAwareService - Content Aware extension is inactive but should be filtering',
          );
          this._logContentAwareError(
            ContentAwareTelemetryType.LOGGED_OUT,
            false,
            'Extension reported as inactive when should be logged in',
          );

          // Try to recover by sending configuration again
          const config = this._productConfigService.getContentAwareConfig();
          if (config !== undefined) {
            void this._sendConfigMessage(config);
          }
        }

        return response;
      } else {
        console.error('ContentAwareService - Health check failed', response);
        this._logContentAwareError(
          ContentAwareTelemetryType.HEALTH_CHECK,
          false,
          'Unknown error',
          response,
        );
        return undefined;
      }
    } catch (error: any) {
      console.error('ContentAwareService - Error sending health check', error);
      this._logContentAwareError(
        ContentAwareTelemetryType.HEALTH_CHECK,
        false,
        error.message,
        error,
      );
      return undefined;
    }
  };

  /**
   * Sends a message to the Content Aware extension and waits for a response.
   *
   * @param message The message to send
   * @returns The response from the Content Aware extension, or undefined if no response or timeout
   */
  private readonly _sendMessageToContentAwareExtension = async <
    T extends ContentAwareBaseResponseMessage,
  >(
    message: IContentAwareRequestMessagesUnion,
  ): Promise<T | undefined> => {
    if (this._extensionInfo === undefined) {
      console.debug('ContentAwareService - Cannot send message: no extension info');
      return undefined;
    }

    console.debug('ContentAwareService - Sending message to Content Aware extension:', {
      extensionId: this._extensionInfo.id,
      messageType: message.MessageType,
      messageDetails: JSON.stringify(message, null, 2),
    });

    return await new Promise<T | undefined>((resolve) => {
      // Set timeout to handle case where extension doesn't respond
      const timeoutId = setTimeout(() => {
        console.warn(
          'ContentAwareService - Message timeout, no response from Content Aware extension',
          {
            messageType: message.MessageType,
            extensionId: this._extensionInfo?.id,
            timeoutMs: CONTENT_AWARE_MESSAGE_TIMEOUT,
          },
        );
        this._logContentAwareError(ContentAwareTelemetryType.MESSAGE, false, 'Message timeout', {
          messageType: message.MessageType,
        });
        resolve(undefined);
      }, CONTENT_AWARE_MESSAGE_TIMEOUT);

      // Send message to Content Aware extension
      chrome.runtime.sendMessage(this._extensionInfo?.id, message, (response: T | undefined) => {
        clearTimeout(timeoutId);

        if (chrome.runtime.lastError != null) {
          console.error('ContentAwareService - Error sending message to Content Aware extension', {
            error: chrome.runtime.lastError,
            messageType: message.MessageType,
            extensionId: this._extensionInfo?.id,
            errorMessage: chrome.runtime.lastError.message,
          });
          this._logContentAwareError(
            ContentAwareTelemetryType.MESSAGE,
            false,
            chrome.runtime.lastError.message ?? 'Unknown error',
          );
          resolve(undefined);
        } else {
          this._clearErrorTracking(ContentAwareTelemetryType.MESSAGE); // Clear message error tracking on success
          console.debug('ContentAwareService - Message sent successfully, received response:', {
            messageType: message.MessageType,
            hasResponse: response !== undefined,
            response: response !== undefined ? JSON.stringify(response, null, 2) : 'undefined',
          });
          resolve(response);
        }
      });
    });
  };

  /**
   * Starts the health check interval.
   */
  private readonly _startHealthCheckInterval = (): void => {
    this._stopHealthCheckInterval();

    this._healthCheckIntervalId = setInterval(() => {
      void this._sendIsLoggedInMessage();
    }, CONTENT_AWARE_HEALTH_CHECK_INTERVAL);
  };

  /**
   * Stops the health check interval.
   */
  private readonly _stopHealthCheckInterval = (): void => {
    if (this._healthCheckIntervalId !== undefined) {
      clearInterval(this._healthCheckIntervalId);
      this._healthCheckIntervalId = undefined;
    }
  };

  /**
   * Handler for product config changes.
   * Updates Content Aware status when the product config changes.
   */
  private readonly _onProductConfigChanged = (): void => {
    console.debug('ContentAwareService - Product config changed, updating Content Aware status');

    // Log the current config for debugging
    const config = this._productConfigService.getContentAwareConfig();
    console.debug('ContentAwareService - Current Content Aware config after change:', {
      hasConfig: config !== undefined,
      enabled: config?.configuration?.ia_license.status === 'ACTIVE',
      hasLicense: config?.configuration.ia_license !== undefined,
      licenseId: config?.configuration.ia_license?.id,
      settings: config?.configuration?.domainConfig,
    });

    void this._updateContentAwareStatus();
  };

  /**
   * Handler for policy changes.
   * Updates Content Aware configuration when the policy changes.
   */
  private readonly _onPolicyLoaded = (): void => {
    console.debug('ContentAwareService - Policy loaded, updating Content Aware configuration');
    console.debug('ContentAwareService - Current login status:', this._isLoggedIn);

    // Only send config update if we're already logged in
    if (this._isLoggedIn) {
      const config = this._productConfigService.getContentAwareConfig();
      console.debug('ContentAwareService - Config retrieved for policy update:', {
        hasConfig: config !== undefined,
        enabled: config?.configuration?.ia_license.status === 'ACTIVE',
        allowListLength: this._policyService.contentAwareAllowList?.length ?? 0,
      });

      if (config !== undefined) {
        void this._sendConfigMessage(config);
      } else {
        console.debug('ContentAwareService - No valid config available for policy update');
      }
    } else {
      console.debug(
        'ContentAwareService - Not logged in, skipping config update for policy change',
      );
    }
  };

  /**
   * Sets up listeners for extension management events to handle dynamic changes.
   */
  private readonly _setupExtensionManagementListeners = (): void => {
    // Only set up listeners if chrome.management events are available (not in test environment)
    if (
      chrome?.management?.onInstalled != null &&
      chrome?.management?.onUninstalled != null &&
      chrome?.management?.onEnabled != null &&
      chrome?.management?.onDisabled != null
    ) {
      // Listen for extension installations
      chrome.management.onInstalled.addListener(this._onExtensionInstalled);

      // Listen for extension uninstallations
      chrome.management.onUninstalled.addListener(this._onExtensionUninstalled);

      // Listen for extension enable/disable events
      chrome.management.onEnabled.addListener(this._onExtensionEnabled);
      chrome.management.onDisabled.addListener(this._onExtensionDisabled);
    }
  };

  /**
   * Removes listeners for extension management events.
   */
  private readonly _removeExtensionManagementListeners = (): void => {
    // Only remove listeners if chrome.management events are available (not in test environment)
    if (
      chrome?.management?.onInstalled != null &&
      chrome?.management?.onUninstalled != null &&
      chrome?.management?.onEnabled != null &&
      chrome?.management?.onDisabled != null
    ) {
      // Remove extension installation listener
      chrome.management.onInstalled.removeListener(this._onExtensionInstalled);

      // Remove extension uninstallation listener
      chrome.management.onUninstalled.removeListener(this._onExtensionUninstalled);

      // Remove extension enable/disable listeners
      chrome.management.onEnabled.removeListener(this._onExtensionEnabled);
      chrome.management.onDisabled.removeListener(this._onExtensionDisabled);
    }
  };

  /**
   * Handler for extension installation events.
   * Checks if the newly installed extension is Content Aware and starts communication if needed.
   */
  private readonly _onExtensionInstalled = (info: chrome.management.ExtensionInfo): void => {
    if (this._isContentAwareExtension(info)) {
      console.debug(
        `ContentAwareService - Content Aware extension installed: ${info.name} (${info.id})`,
      );

      // Update extension info and try to start communication
      this._extensionInfo = {
        id: info.id,
        name: info.name,
        version: info.version,
        enabled: info.enabled,
      };

      // Log the extension status change
      this._logExtensionStatus(this._extensionInfo);

      if (info.enabled && this._isRunning) {
        void this._updateContentAwareStatus();
      }
    }
  };

  /**
   * Handler for extension uninstallation events.
   * Stops communication if the Content Aware extension is uninstalled.
   */
  private readonly _onExtensionUninstalled = (id: string): void => {
    if (this._extensionInfo?.id === id) {
      console.debug('ContentAwareService - Content Aware extension uninstalled');

      // Clean up state
      this._isLoggedIn = false;
      this._extensionInfo = undefined;
      this._stopHealthCheckInterval();
      this._clearLoginConfigTracking();

      // Log the extension status change
      this._logExtensionStatus();

      // Fire status change event
      this.onStatusChanged.dispatch(false, 'Extension uninstalled');
    }
  };

  /**
   * Handler for extension enable events.
   * Starts communication if the Content Aware extension is enabled.
   */
  private readonly _onExtensionEnabled = (info: chrome.management.ExtensionInfo): void => {
    if (this._isContentAwareExtension(info)) {
      console.debug('ContentAwareService - Content Aware extension enabled');

      // Update extension info
      if (this._extensionInfo !== undefined) {
        this._extensionInfo.enabled = true;
      } else {
        this._extensionInfo = {
          id: info.id,
          name: info.name,
          version: info.version,
          enabled: true,
        };
      }

      // Log the extension status change
      this._logExtensionStatus(this._extensionInfo);

      // Start communication if service is running
      if (this._isRunning) {
        void this._updateContentAwareStatus();
        this._startHealthCheckInterval();
      }
    }
  };

  /**
   * Handler for extension disable events.
   * Stops communication if the Content Aware extension is disabled.
   */
  private readonly _onExtensionDisabled = (info: chrome.management.ExtensionInfo): void => {
    if (this._isContentAwareExtension(info)) {
      console.debug('ContentAwareService - Content Aware extension disabled');

      // Update extension info and clean up state
      if (this._extensionInfo !== undefined) {
        this._extensionInfo.enabled = false;
      }
      this._isLoggedIn = false;
      this._stopHealthCheckInterval();
      this._clearLoginConfigTracking();

      // Log the extension status change
      this._logExtensionStatus(this._extensionInfo);

      // Fire status change event
      this.onStatusChanged.dispatch(false, 'Extension disabled');
    }
  };

  /**
   * Checks if the given extension info represents a Content Aware extension.
   */
  private readonly _isContentAwareExtension = (info: chrome.management.ExtensionInfo): boolean => {
    const knownExtensionIds = Object.values(CONTENT_AWARE_EXTENSION_IDS) as string[];
    const knownExtensionNames = CONTENT_AWARE_EXTENSION_NAMES;

    // Check if extension ID matches any known Content Aware extension IDs
    if (knownExtensionIds.includes(info.id)) {
      return true;
    }

    // Check if extension name matches any known Content Aware extension names
    for (const name of knownExtensionNames) {
      if (info.name.includes(name)) {
        return true;
      }
    }

    return false;
  };

  /**
   * Logs the Content Aware extension status if it has changed.
   */
  private readonly _logExtensionStatus = (extensionInfo?: IContentAwareExtensionInfo): void => {
    // Don't log if customer isn't licensed for Content Aware
    if (!this._isContentAwareLicensed()) {
      return;
    }

    let status: 'enabled' | 'disabled' | 'not-installed';
    if (extensionInfo?.enabled === true) {
      status = 'enabled';
    } else if (extensionInfo !== undefined) {
      status = 'disabled';
    } else {
      status = 'not-installed';
    }

    // Check if status has changed
    const hasStatusChanged =
      this._lastLoggedExtensionStatus?.status !== status ||
      this._lastLoggedExtensionStatus?.id !== extensionInfo?.id ||
      this._lastLoggedExtensionStatus?.name !== extensionInfo?.name ||
      this._lastLoggedExtensionStatus?.version !== extensionInfo?.version;

    if (hasStatusChanged) {
      const properties: Record<string, string> = { status };

      if (extensionInfo !== undefined) {
        properties.id = extensionInfo.id;
        properties.name = extensionInfo.name;
        properties.version = extensionInfo.version;
      }

      this._telemetryService.logEvent(TelemetryEventType.ContentAwareExtensionStatus, properties);

      this._lastLoggedExtensionStatus = {
        status,
        id: extensionInfo?.id,
        name: extensionInfo?.name,
        version: extensionInfo?.version,
      };
    }
  };

  /**
   * Logs a Content Aware login success event.
   */
  private readonly _logLoginSuccess = (): void => {
    // Don't log if customer isn't licensed for Content Aware
    if (!this._isContentAwareLicensed()) {
      return;
    }

    // Only log if it's the first login since startup, or extension went from logged-out to logged-in
    if (!this._hasLoggedInSinceChange) {
      this._telemetryService.logEvent(TelemetryEventType.ContentAwareLogin);
      this._hasLoggedInSinceChange = true;
      this._hasConfigSentSinceLogin = false; // Reset config tracking
    }
  };

  /**
   * Logs a Content Aware config success event.
   */
  private readonly _logConfigSuccess = (): void => {
    // Don't log if customer isn't licensed for Content Aware
    if (!this._isContentAwareLicensed()) {
      return;
    }

    // Only log if it's the first config since login, or config has changed
    if (!this._hasConfigSentSinceLogin) {
      this._telemetryService.logEvent(TelemetryEventType.ContentAwareConfig);
      this._hasConfigSentSinceLogin = true;
    }
  };

  private readonly _createTelemetryKey = (
    type: ContentAwareTelemetryType,
    fatal: boolean,
  ): string => {
    return `${type}-${fatal ? '1' : '0'}`;
  };

  /**
   * Maps Content Aware telemetry types to appropriate telemetry event types.
   */
  private readonly _mapTelemetryEventType = (
    type: ContentAwareTelemetryType,
  ): TelemetryEventType => {
    switch (type) {
      case ContentAwareTelemetryType.LOGIN:
        return TelemetryEventType.ContentAwareLogin;
      case ContentAwareTelemetryType.CONFIG:
        return TelemetryEventType.ContentAwareConfig;
      case ContentAwareTelemetryType.START:
      case ContentAwareTelemetryType.MESSAGE:
      case ContentAwareTelemetryType.HEALTH_CHECK:
      case ContentAwareTelemetryType.LOGGED_OUT:
      default:
        return TelemetryEventType.ContentAwareError;
    }
  };

  /**
   * Logs an event with appropriate telemetry event type mapping.
   */
  private readonly _logContentAwareError = (
    type: ContentAwareTelemetryType,
    fatal: boolean,
    message?: string,
    additionalData?: any,
  ): void => {
    // Don't log if customer isn't licensed for Content Aware
    if (!this._isContentAwareLicensed()) {
      return;
    }

    // Only log if it's the first occurrence or if last attempt succeeded
    const errorKey = this._createTelemetryKey(type, fatal);
    if (!this._lastLoggedErrors.has(errorKey)) {
      const customParameters = {
        type,
        fatal,
        ...additionalData,
      };

      // Map to appropriate telemetry event type based on event type
      const telemetryEventType = this._mapTelemetryEventType(type);
      this._telemetryService.logError(
        telemetryEventType,
        message ?? 'Unknown error',
        customParameters,
      );
      this._lastLoggedErrors.add(errorKey);
    }
  };

  /**
   * Clears the login/config tracking when status changes.
   */
  private readonly _clearLoginConfigTracking = (): void => {
    this._hasLoggedInSinceChange = false;
    this._hasConfigSentSinceLogin = false;
  };

  /**
   * Clears specific error tracking when an operation succeeds.
   */
  private readonly _clearErrorTracking = (type: ContentAwareTelemetryType): void => {
    this._lastLoggedErrors.delete(this._createTelemetryKey(type, true));
    this._lastLoggedErrors.delete(this._createTelemetryKey(type, false));
  };

  /**
   * Checks if the customer is licensed for Content Aware.
   */
  private readonly _isContentAwareLicensed = (): boolean => {
    const config = this._productConfigService.getContentAwareConfig();
    return config !== undefined;
  };

  /**
   * Event that fires when the Content Aware extension status changes.
   */
  public readonly onStatusChanged = new StandaloneEvent<[boolean, string?]>();

  /**
   * Whether the service is currently running.
   */
  private _isRunning: boolean = false;

  /**
   * Whether we're currently logged in to the Content Aware extension.
   */
  private _isLoggedIn: boolean = false;

  /**
   * Information about the Content Aware extension.
   */
  private _extensionInfo?: IContentAwareExtensionInfo;

  /**
   * Interval ID for health checks.
   */
  private _healthCheckIntervalId?: NodeJS.Timeout;

  /**
   * Tracks the last logged extension status to avoid duplicate logging.
   */
  private _lastLoggedExtensionStatus?: {
    status: 'enabled' | 'disabled' | 'not-installed';
    id?: string;
    name?: string;
    version?: string;
  };

  /**
   * Tracks whether login has been successful since last change.
   */
  private _hasLoggedInSinceChange: boolean = false;

  /**
   * Tracks whether config has been sent successfully since last login.
   */
  private _hasConfigSentSinceLogin: boolean = false;

  /**
   * Tracks the last logged error types to avoid duplicate logging.
   */
  private readonly _lastLoggedErrors = new Set<string>();
}

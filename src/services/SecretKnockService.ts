import StorageService from 'services/StorageService';
import IpService from 'services/IpService';
import ITelemetryService from 'services/ITelemetryService';
import { TelemetryEventType } from '../constants/TelemetryEventType';
import SerialId from 'models/SerialId';
import { ICloudFilterBypass } from 'models/PolicyConfigModels';
import MetricTelemetry from 'models/MetricTelemetry';

// Time in ms to recheck whether we need to send a secret knock based on configuration and time since last knock.
// Added to ensure we check at least every 10s while the extension is active, as a longer timer may be paused
// if the device is put to sleep (Chromebook)
const recheckKnockDelay = 10000;

/**
 * Periodically sends a request to the on-prem filter, telling it to allow traffic from this device.
 * This is used to prevent the user being filtered by both the extension and on-prem in the event
 *  that the customer has both.
 */
export default class SecretKnockService {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance of the secret knock service.
   *
   * @param storageService Stores details of the last secret knock which was sent.
   * @param ipService The IP service instance for the extension. This is used to detect network
   *  changes and outside premises status.
   * @param telemetryService Used for logging telemetry to the cloud, e.g. Application Insights.
   */
  constructor(
    storageService: StorageService,
    ipService: IpService,
    telemetryService: ITelemetryService,
  ) {
    this._storageService = storageService;
    this._ipService = ipService;
    this._telemetryService = telemetryService;

    this._ipService.onNetworkChange.addListener(this._onNetworkChange);
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Check if sending of secret knock requests is enabled.
   * This does not check whether we actually have the information to be able to send
   *
   * @return Returns true if secret knock has been explicitly enabled on this run of the extension.
   *  Returns false otherwise.
   */
  public readonly isEnabled = (): boolean => {
    return this._isEnabled;
  };

  /**
   * Check if we currently have the information required to send secret knock.
   */
  public readonly isConfigured = (): boolean => {
    return this._serialId !== undefined && this._url !== undefined && this._delay !== undefined;
  };

  /**
   * Check if we should currently be sending secret knock on a regular basis.
   * This is based on whether it's enabled and configured. It doesn't indicate when the knock should
   *  be sent, and it doesn't check if we're currently outside premises.
   */
  public readonly shouldSendSecretKnock = (): boolean => {
    return this.isEnabled() && this.isConfigured();
  };

  /**
   * Get the currently configured serial ID which will be sent as part of secret knock requests.
   * Returns undefined if secret knock has not been configured.
   */
  public get serialId(): SerialId | undefined {
    return this._serialId;
  }

  /**
   * Get the currently configured URL which secret knock requests will be sent to.
   * Returns undefined if secret knock has not been configured.
   */
  public get url(): URL | undefined {
    return this._url;
  }

  /**
   * Get the currently configured delay between secret knock requests.
   * Returns undefined if secret knock has not been configured.
   */
  public get delay(): number | undefined {
    return this._delay;
  }

  /**
   * Get the timestamp (in milliseconds) of the most recent attempt at sending a secret knock.
   * Returns undefined if no secret knock request has been attempted.
   */
  public get lastKnockedAt(): number | undefined {
    return this._storageService.getNumber('lastKnockedAt');
  }

  /**
   * Check whether the most recent secret knock request was sent successfully.
   * Returns undefined if no secret knock request has been attempted.
   */
  public get lastKnockSucceeded(): boolean | undefined {
    return this._storageService.getBoolean('lastKnockSucceeded');
  }

  /**
   * Store the date/time of the most recent secret knock attempt (internal use), when the
   * request is sent.
   *
   * @param when The timestamp (in milliseconds) indicating when the most recent secret knock
   *  request was sent.
   */
  private readonly _setLastKnockAttemptTime = (when: number = Date.now()): void => {
    this._storageService.set('lastKnockAttemptedAt', when);
    this._storageService.saveInTheBackground();
  };

  /**
   * Get the timestamp (in milliseconds) of the most recent attempt at sending a secret knock.
   * Returns undefined if no secret knock request has been attempted.
   *
   * This may differ from lastKnockedAt if we have not yet got a result from the knock.
   */
  private get _lastKnockAttemptTime(): number | undefined {
    return this._storageService.getNumber('lastKnockAttemptedAt');
  }

  /**
   * Store the date/time and result of the most recent secret knock attempt (for diagnostics).
   *
   * @param succeeded Indicates if the secret knock request was successful.
   * @param when The timestamp (in milliseconds) indicating when the most recent secret knock
   *  request was sent.
   */
  private readonly _setLastKnock = (succeeded: boolean, when: number = Date.now()): void => {
    this._storageService.set('lastKnockedAt', when);
    this._storageService.set('lastKnockSucceeded', succeeded);
    this._storageService.saveInTheBackground();
  };

  /**
   * Get a human readable string summarising the most recent secret knock attempt.
   * This is intended for display in diagnostic pages etc.
   */
  public readonly getLastKnockSummary = (): string => {
    const lastKnockedAt = this.lastKnockedAt;
    if (lastKnockedAt === undefined) {
      return 'Not yet knocked';
    }

    return `${new Date(lastKnockedAt).toString()} ${
      this.lastKnockSucceeded === true ? 'Success' : 'Failure'
    }`;
  };

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Start sending secret knock requests, if/when the required configuration is loaded.
   * Secret knock will only be sent if enable() and configure() have both been called.
   * These are separated into two functions so that we can loaded the necessary configuration as
   *  soon as it is available, but defer sending the requests until we reach mode 2 (full filter).
   * This state is not cached internally so this must be called again on every run of the extension.
   *
   * @see disable()
   * @see isEnabled()
   * @see configureFromPolicyConfig()
   */
  public readonly enable = (): void => {
    if (!this._isEnabled) {
      console.debug('SecretKnockService - Secret knock enabled.');
      this._isEnabled = true;
      this._updateSchedule();
    }
  };

  /**
   * Disable sending of secret knock requests.
   * This is typically called if we've moved back to mini-filter (aka mode 1).
   * This will prevent any further secret knocks from being sent until it is enabled again.
   * This does not remove any stored configuration.
   *
   * @see enable()
   * @see isEnabled()
   * @see clearConfiguration()
   */
  public readonly disable = (): void => {
    if (this._isEnabled) {
      console.debug('SecretKnockService - Secret knock disabled.');
      this._isEnabled = false;
      this._updateSchedule();
    }
  };

  /**
   * Configure the secret knock using information from Cloud Filter policy config.
   * If the specified information is empty or invalid then it has the same effect as clearing the
   *  stored configuration, i.e. secret knocks will be stopped.
   *
   * @param serialId The customer's serial ID. This is sent as part of the secret knock requests.
   * @param cloudFilterBypass An object from policy config containing the details of where to send
   *  secret knock and how often. If the object is empty or this is null/undefined, then any
   *  previous configuration will be removed and secret knock requests will be stopped. Similarly,
   *
   * @see clearConfiguration()
   * @see enable()
   */
  public readonly configureFromPolicyConfig = (
    serialId: SerialId,
    cloudFilterBypass?: ICloudFilterBypass,
  ): void => {
    // If the secret knock config is missing or empty then we should stop sending it.
    if (
      cloudFilterBypass == null ||
      cloudFilterBypass.host == null ||
      cloudFilterBypass.host === '' ||
      cloudFilterBypass.timeout == null
    ) {
      if (this.isConfigured()) {
        console.debug('SecretKnockService - Secret knock configuration removed.');
      }

      this.clearConfiguration();
      return;
    }

    try {
      const newUrl = SecretKnockService.extractUrlFromPolicyConfig(cloudFilterBypass);
      const newDelay = SecretKnockService.extractDelayFromPolicyConfig(cloudFilterBypass);

      // If the secret knock configuration hasn't changed then do nothing.
      if (
        serialId === this._serialId &&
        newUrl.toString() === this._url?.toString() &&
        this._delay === newDelay
      ) {
        return;
      }

      // If we already had a configuration and it's changed then we'll send a new knock immediately.
      const wasConfigured = this._serialId !== undefined;

      this._serialId = serialId;
      this._url = newUrl;
      this._delay = newDelay;

      console.debug('SecretKnockService - Secret knock configured.', cloudFilterBypass);
      this._updateSchedule(wasConfigured);
    } catch (e: any) {
      console.warn('SecretKnockService - Failed to configure secret knock.', e);
      this.clearConfiguration();
    }
  };

  /**
   * Remove the secret knock configuration from this instance.
   * If secret knock was being sent then it will be stopped until it is configured again.
   * This only impacts the data stored within this object in memory. It does not remove anything
   *  from the cache.
   *
   * @see configureFromPolicyConfig()
   */
  public readonly clearConfiguration = (): void => {
    this._url = undefined;
    this._delay = undefined;
    this._serialId = undefined;
    this._updateSchedule();
  };

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Set or clear the timer for sending secret knock, based on stored configuration.
   * If secret knock is enabled and configured, then this will ensure there is a timer running to
   *  send it when needed. This may cause a secret knock to be sent immediately.
   * If secret knock is disabled or not configured, then this will ensure the timer is not running.
   *
   * @param sendImmediately If true, a new secret knock request will be sent immediately, regardless
   *  of how recently we last sent one. If false, the existing schedule will be preserved.
   */
  private readonly _updateSchedule = (sendImmediately: boolean = false): void => {
    // If we shouldn't be sending secret knock at all then ensure no timer is set.
    if (!this.shouldSendSecretKnock()) {
      if (this._knockInterval !== undefined) {
        clearInterval(this._knockInterval);
        this._knockInterval = undefined;
      }
      return;
    }

    // If we get here then we should be sending secret knock.

    if (this._knockInterval !== undefined) {
      if (!sendImmediately) {
        // We already have a secret knock scheduled, and haven't been told to override it. Assume
        //  nothing needs to be done.
        return;
      }

      // We'll need to override the existing timer.
      clearInterval(this._knockInterval);
    }

    if (sendImmediately) {
      this._onKnockTimeout();
    }

    this._knockInterval = setInterval(this._onKnockTimeout, recheckKnockDelay);
  };

  /**
   * Send a secret knock request to the server, and log corresponding metrics.
   *
   * @returns A promise which resolves when the secret knock request has been attempted. The promise
   *  will not reject if the secret knock fails. It will only reject if secret knock has not been
   *  configured.
   */
  private readonly _sendRequest = async (): Promise<void> => {
    if (this._serialId === undefined || this._url === undefined) {
      throw new Error('Secret knock has not been configured.');
    }

    const sentAt = Date.now();
    const details: RequestInit = {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        serial: this._serialId?.toString() ?? '',
        timestamp: Math.floor(sentAt / 1000).toString(),
      }),
    };

    this._setLastKnockAttemptTime(sentAt);

    let success: boolean;
    try {
      const response = await fetch(this._url ?? '', details);
      success = response.ok;
    } catch (e: any) {
      success = false;
    }

    this._setLastKnock(success, sentAt);

    const metric = new MetricTelemetry(
      success
        ? TelemetryEventType.SecretKnockHttpSuccess
        : TelemetryEventType.SecretKnockHttpFailure,
      'secret-knock-http',
    );
    metric.addMetric(1);
    this._telemetryService.logMetric(metric);
  };

  // -----------------------------------------------------------------------------------------------
  // Event handlers.

  /**
   * Check if we are due to send a secret knock.
   * If secret knock has been disabled or is no longer configured then nothing is done.
   * Otherwise, if it is due within recheckKnockDelay ms, this will send the request.
   */
  private readonly _onKnockTimeout = (): void => {
    // If secret knock has been disabled or the configuration has been cleared then do nothing.
    if (!this.shouldSendSecretKnock()) {
      return;
    }

    // There's no point trying to send secret knock if we're definitely outside premises as the
    //  request won't be able to get through. Send it in all other cases if we are due to knock
    // in before the next check.
    if (
      !this._ipService.isDeviceOutsidePremises() &&
      this.calculateKnockDelay() < recheckKnockDelay
    ) {
      this._sendRequest().catch(console.warn);
    }
  };

  /**
   * Called when a change is detected in the network connection.
   * This may mean we have moved into a network controlled by a Smoothie, so we should send a
   *  secret knock immediately to ensure we don't get double-filtered.
   */
  private readonly _onNetworkChange = (): void => {
    // We don't want to flood the network with secret knock requests if our connection is changing
    //  rapidly. Use a timer to debounce the changes so that we only send a secret knock when the
    //  network connection has stabilised.

    if (this._networkDebounceTimeout !== undefined) {
      clearTimeout(this._networkDebounceTimeout);
    }

    this._networkDebounceTimeout = setTimeout(() => {
      console.debug('SecretKnockService - Triggering secret knock due to network change.');
      this._networkDebounceTimeout = undefined;
      this._updateSchedule(true);
    }, 15000);
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Get the URL for sending secret knocks from policy config.
   * This validates the URL and converts it as necessary.
   *
   * @param cloudFilterBypass The secret knock details from the policy config. This must not be null
   *  or undefined.
   * @returns The URL to which secret knock requests should be sent.
   * @throws {Error} The URL is missing or invalid in the given config.
   */
  public static readonly extractUrlFromPolicyConfig = (
    cloudFilterBypass: ICloudFilterBypass,
  ): URL => {
    if (typeof cloudFilterBypass?.host !== 'string') {
      throw new Error('Destination URL has invalid type. Expected string.');
    }

    // Default to HTTP if no other scheme was specified.
    let host = cloudFilterBypass.host;
    if (!/^[a-zA-Z0-9\-.]+:\/\//.test(host)) {
      host = `http://${host}`;
    }

    return new URL(host);
  };

  /**
   * Get the time between secret knocks from the policy config and convert it as necessary.
   *
   * @param cloudFilterBypass The secret knock details from the policy config. This must not be null
   *  or undefined.
   * @returns The time in milliseconds to wait between consecutive secret knock requests.
   */
  public static readonly extractDelayFromPolicyConfig = (
    cloudFilterBypass: ICloudFilterBypass,
  ): number => {
    if (
      typeof cloudFilterBypass?.timeout !== 'string' &&
      typeof cloudFilterBypass?.timeout !== 'number'
    ) {
      throw new Error('Timeout has invalid type. Expected number or string.');
    }

    // Use a timeout which is 1 minute less than the server specified to ensure that the knock
    //  doesn't expire. However, ensure that there is no less than 1 minute between knocks.
    // Convert the result from seconds to milliseconds.
    return Math.max(+cloudFilterBypass.timeout - 60, 60) * 1000;
  };

  /**
   * Determine how long to wait before sending the next secret knock.
   * We need to send initially, on network change, and if the time since the last knock
   * has exceeeded the configured delay between knocks.
   *
   * @returns The amount of time (in milliseconds) to delay before sending the next secret knock.
   */
  public readonly calculateKnockDelay = (): number => {
    if (this._delay === undefined) {
      throw new Error('Secret knock has not been configured.');
    }

    // If we've never sent a secret knock then send one immediately.
    const lastKnockedAttemptedAt = this._lastKnockAttemptTime;
    if (lastKnockedAttemptedAt === undefined) {
      return 0;
    }

    // If the network has changed since we last sent a secret knock, then send a new one
    //  immediately.
    const networkChangedAt = this._ipService.privateIpAddressesChangedAt;
    if (networkChangedAt !== undefined && networkChangedAt > lastKnockedAttemptedAt) {
      return 0;
    }

    // Calculate when the next knock should be sent, based on when the last one was sent. If we're
    //  already overdue then send it immediately.
    return Math.max(0, lastKnockedAttemptedAt + this._delay - Date.now());
  };

  // -----------------------------------------------------------------------------------------------
  // Data

  /**
   * Stores the last knock details in chrome local storage so they persist through restarts.
   */
  private readonly _storageService: StorageService;

  /**
   * Manages getting and caching the device's ip addresses.
   */
  private readonly _ipService: IpService;

  /**
   * The telemetry service for logging events.
   */
  private readonly _telemetryService: ITelemetryService;

  /**
   * Indicates if sending the secret knock is currently enabled.
   * We will only send secret knock if it's currently enable *and* configured.
   * This value is deliberately not cached. It must be set again on every run.
   *
   * @see enable()
   * @see disable()
   * @see isEnabled()
   */
  private _isEnabled: boolean = false;

  /**
   * The URL which we'll send the secret knock to.
   * This is set during configuration, based on data retrieved from the policy config. It will be
   *  undefined if secret knock has not been configured yet.
   *
   * @see configureFromPolicyConfig()
   * @see isConfigured()
   */
  private _url?: URL;

  /**
   * The amount of time in milliseconds between each secret knock.
   * This is set during configuration, based on data retrieved from the policy config. It will be
   *  undefined if secret knock has not been configured yet.
   * If set, this will be at least 60,000 (i.e. 1 minute). It will be 1 minute less than the secret
   *  knock expiry time retrieved from the on-prem device.
   *
   * @see configureFromPolicyConfig()
   * @see isConfigured()
   */
  private _delay?: number;

  /**
   * The customer's serial -- this is sent as part of the secret knock request.
   * This is set during configuration, based on data retrieved from the policy config. It will be
   *  undefined if secret knock has not been configured yet.
   *
   * @see configureFromPolicyConfig()
   * @see isConfigured()
   */
  private _serialId?: SerialId;

  /**
   * An interval which triggers every recheckKnockDelay ms to check if it's time to send our
   * next secret knock request. This will be undefined if we're not currently meant to be sending
   * a regular secret knock.
   */
  private _knockInterval?: ReturnType<typeof setInterval>;

  /**
   * Timeout used to debounce network change events.
   * We normally send a secret knock again every time the network changes, in case we've moved onto
   *  a network containing the on-prem device. Debouncing is used to prevent us from flooding the
   *  network with secret knock requests in the event that the network changes rapidly.
   */
  private _networkDebounceTimeout?: ReturnType<typeof setTimeout>;
}

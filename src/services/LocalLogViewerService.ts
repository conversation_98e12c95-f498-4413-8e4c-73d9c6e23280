import AccessLogEntry from 'models/AccessLogEntry';

export default class LocalLogViewerService {
  /**
   * When the local real time log viewer port gets set, immediately send the logs we have.
   *
   * @param port The port used by the local real time log viewer page.
   */
  public readonly start = (port: chrome.runtime.Port): void => {
    this._port = port;
    port.onDisconnect.addListener(() => {
      this._port = undefined;
    });
    port.postMessage(this._queue);
  };

  public readonly streamLog = (log: AccessLogEntry): void => {
    // Don't send logs which are below the minimum level. This is to prevent the viewer from being
    //  flooded by trivial requests. Keep logs which have no level in case there's an error in the
    //  log levels.
    if (log.loglevel !== undefined && log.loglevel < this._minimumLogLevel) {
      return;
    }

    const expiry = Math.floor(Date.now() / 1000) - this._expiryInMinutes * 60;
    this._queue = this._queue.filter((log) => +log.time > expiry);
    this._queue.push(log);

    if (this._port !== undefined) {
      this._port.postMessage([log]);
    }
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * How long logs will stay in the queue before getting dropped.
   */
  private readonly _expiryInMinutes = 5;

  /**
   * The minimum log level which will be shown in the local log viewer.
   * Any logs with a lower level will be discarded.
   */
  private readonly _minimumLogLevel = 3;

  /**
   * The port used by the local real time log viewer page.
   */
  private _port?: chrome.runtime.Port;

  /**
   * Queue for Access Logs.
   */
  private _queue: AccessLogEntry[] = [];
}

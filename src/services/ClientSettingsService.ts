import FileDownloadType from 'models/FileDownloadType';
import IClientSettings from 'models/IClientSettings';
import IBlobStorageService from './IBlobStorageService';
import StandaloneEvent from '../utilities/StandaloneEvent';
import StorageService from './StorageService';
import BlobDownloadResult from 'models/BlobDownloadResult';
import { ClientSettingsConfig } from 'models/IProductConfig';
import { mergeBlobUrl } from 'utilities/Helpers';
import TenantId from '../models/TenantId';
import { TelemetryEventType } from '../constants/TelemetryEventType';
import ITelemetryService from './ITelemetryService';

export default class ClientSettingsService {
  constructor(
    blobStorageService: IBlobStorageService,
    clientSettingsStorageService: StorageService,
    telemetryService: ITelemetryService,
    tenantId: TenantId | undefined,
  ) {
    this._blobStorageService = blobStorageService;
    this._storageService = clientSettingsStorageService;
    this._telemetryService = telemetryService;
    this._tenantId = tenantId;

    this._blobStorageService.onDownloadSuccess.addListener(this._downloadCompleted);
    this._blobStorageService.onDownloadFailed.addListener(this._downloadCompleted);
  }

  /**
   * Starts the process to fetch the settings from blob storage and then parse them.
   * @param settingsDetails The url to download the settings json from.
   * @returns
   */
  public readonly start = (settingsDetails: ClientSettingsConfig): void => {
    if (settingsDetails === undefined) {
      console.error('ClientSettingsService - No client settings config provided.');

      // We can continue running without the client settings.
      this.onClientSettingsLoaded.dispatch(this.constructor.name);
      return;
    }

    const settingsUrl = mergeBlobUrl(
      settingsDetails.resource,
      settingsDetails.name,
      settingsDetails.sas,
    );
    if (settingsUrl === this.settingsUrl && this._clientSettings !== undefined) {
      console.debug('Settings urls are the same, not fetching from blob.');

      // Dispatch the event anyway so any waiting code can continue.
      this.onClientSettingsLoaded.dispatch(this.constructor.name);
      return;
    }

    this._saveSettingsUrl(settingsUrl)
      .catch((error) => {
        console.error('An error occurred saving the client settings url to local storage.', error);
      })
      .finally(() => {
        // Continue with the download even if the settings url cannot be saved.
        this._blobStorageService.start(settingsUrl, FileDownloadType.json);
      });
  };

  /**
   * Stops the process of fetching the settings from blob storage.
   */
  public readonly stop = (): void => {
    this._blobStorageService.stop();
  };

  /**
   * Called when the blob download has been sucessfully completed.
   * Parses the data and saves it to local storage.
   * @param result The result returned from blob storage.
   */
  private readonly _downloadCompleted = async (result: BlobDownloadResult): Promise<void> => {
    if (result.isSuccess) {
      const settings = this._parseSettings(result.data);

      try {
        await this._saveSettings(settings);
      } catch (error) {
        console.error('Could not save the client settings to local storage.', error);
      }
    }
    this.onClientSettingsLoaded.dispatch(this.constructor.name);
  };

  /**
   * Parses the given settings json and gets the relevant settings based on the client tenant.
   * @param settingsJson The settings json downloaded from blob storage.
   * @returns The parsed settings json.
   */
  private readonly _parseSettings = (settingsJson: any): IClientSettings => {
    let obj: any;
    let useAdvancedBlockpage = false;
    let safeguardingAlertResource: string | undefined;
    let safeguardingAlertSecurityToken: string | undefined;
    let safeguardingGlobalToggle: boolean = true;

    try {
      if (this._validateSafeguardingGlobalToggle(settingsJson)) {
        safeguardingGlobalToggle = settingsJson.safeguardingGlobalToggle;
      }

      // If client is untenanted or the tenant setting is not found then use the default settings.
      if (
        this._tenantId === undefined ||
        settingsJson.tenantSettings === undefined ||
        settingsJson.tenantSettings[this._tenantId.toString()] === undefined
      ) {
        obj = settingsJson;
      } else {
        // Otherwise use the tenanted object.
        obj = settingsJson.tenantSettings[this._tenantId.toString()];
      }

      if (this._validateAdvancedBlockpageSettings(obj)) {
        useAdvancedBlockpage = obj.useAdvancedBlockpage;
      }

      if (this._validateSafeguardingAlertSettings(obj)) {
        safeguardingAlertResource = obj.safeguardingAlertResource;
        safeguardingAlertSecurityToken = obj.safeguardingAlertSecurityToken;
      }
    } catch (e: any) {
      this._telemetryService.logError(TelemetryEventType.ClientSettingsParsingFailed, e);
    }

    return {
      useAdvancedBlockpage,
      safeguardingAlertResource,
      safeguardingAlertSecurityToken,
      safeguardingGlobalToggle,
    };
  };

  /**
   * Save the given parsed settings to the local cache.
   * @param settings The parsed settings to be saved.
   */
  private readonly _saveSettings = async (settings: IClientSettings): Promise<void> => {
    this._storageService.set('clientSettings', settings);
    await this._storageService.save();
  };

  /**
   * Saves the settings url to the local cache.
   */
  private readonly _saveSettingsUrl = async (settingsUrl: string): Promise<void> => {
    this._storageService.set('settingsUrl', settingsUrl);
    await this._storageService.save();
  };

  /**
   * Validates that the given settings object has the required advanced blockpage settings.
   * @param settings The settings json.
   * @returns If the advanced blockpage settings are valid.
   */
  private readonly _validateAdvancedBlockpageSettings = (
    settings: any | IClientSettings,
  ): boolean => {
    if (
      typeof settings === 'object' &&
      Object.prototype.hasOwnProperty.call(settings, 'useAdvancedBlockpage') &&
      typeof settings.useAdvancedBlockpage === 'boolean'
    ) {
      return true;
    }
    return false;
  };

  /**
   * Validates that the given settings object has the required global safeguarding boolean.
   * @param settings The settings json.
   * @returns If the safeguarding global toggle is valid.
   */
  private readonly _validateSafeguardingGlobalToggle = (
    settings: any | IClientSettings,
  ): boolean => {
    if (
      typeof settings === 'object' &&
      Object.prototype.hasOwnProperty.call(settings, 'safeguardingGlobalToggle') &&
      typeof settings.safeguardingGlobalToggle === 'boolean'
    ) {
      return true;
    }
    return false;
  };

  /**
   * Validates that the given settings object has the required safeguarding alert settings.
   * @param settings The settings json.
   * @returns If the safeguarding alert settings are valid.
   */
  private readonly _validateSafeguardingAlertSettings = (
    settings: any | IClientSettings,
  ): boolean => {
    if (
      typeof settings === 'object' &&
      Object.prototype.hasOwnProperty.call(settings, 'safeguardingAlertResource') &&
      typeof settings.safeguardingAlertResource === 'string' &&
      Object.prototype.hasOwnProperty.call(settings, 'safeguardingAlertSecurityToken') &&
      typeof settings.safeguardingAlertResource === 'string'
    ) {
      return true;
    }
    return false;
  };

  /**
   * Dispatched when the client settings have been successfully loaded.
   */
  public readonly onClientSettingsLoaded = new StandaloneEvent<[string]>();

  /**
   * Gets the current settings url from the local cache.
   */
  public get settingsUrl(): string | undefined {
    return this._storageService.getString('settingsUrl');
  }

  /**
   * Gets if the advance blockpage should be used.
   */
  public get useAdvancedBlockPage(): boolean {
    return this._clientSettings.useAdvancedBlockpage;
  }

  /**
   * Gets the safeguarding alert resource url. Returns undefined if one was not defined in the config.
   */
  public get safeguardingAlertResource(): string | undefined {
    return this._clientSettings.safeguardingAlertResource;
  }

  /**
   * Gets the safeguarding alert security token. Returns undefined if one was not defined in the config.
   */
  public get safeguardingAlertSecurityToken(): string | undefined {
    return this._clientSettings.safeguardingAlertSecurityToken;
  }

  /**
   * Gets the safeguarding global toggle. Returns true is safeguarding alerts are enabled.
   */
  public get safeguardingGlobalToggle(): boolean {
    return this._clientSettings.safeguardingGlobalToggle;
  }

  /**
   * Gets the client settings object saved in the local cache.
   */
  private get _clientSettings(): IClientSettings {
    const settings = this._storageService.get('clientSettings') as IClientSettings;

    if (settings === undefined) {
      return {
        useAdvancedBlockpage: false,
        safeguardingAlertResource: undefined,
        safeguardingAlertSecurityToken: undefined,
        safeguardingGlobalToggle: true,
      };
    }

    return settings;
  }

  /**
   * The blob storage service.
   */
  private readonly _blobStorageService: IBlobStorageService;

  /**
   * The cache where the settings are saved.
   */
  private readonly _storageService: StorageService;

  /**
   * Handles logging to the telemetry provider.
   */
  private readonly _telemetryService: ITelemetryService;

  /**
   * The current client's tenant id.
   */
  private readonly _tenantId: TenantId | undefined;
}

import YoutubeHandlingService from './YoutubeHandlingService';

describe('videoUrlLookupService', () => {
  describe('removeVideoUrlByTabId', () => {
    const videoIdHandlingService = new YoutubeHandlingService();
    const tab1Id = 503995893;
    const tab2Id = 503995890;
    it('adds entries to the lookup table', () => {
      videoIdHandlingService.saveKeyToLookup(tab1Id, 0, videoIdHandlingService.urlKey, 'url1');
      videoIdHandlingService.saveKeyToLookup(tab1Id, 1, videoIdHandlingService.urlKey, 'url2');
      videoIdHandlingService.saveKeyToLookup(tab1Id, 3, videoIdHandlingService.urlKey, 'url3');
      videoIdHandlingService.saveKeyToLookup(tab1Id, 5, videoIdHandlingService.urlKey, 'url4');
      videoIdHandlingService.saveKeyToLookup(tab2Id, 0, videoIdHandlingService.urlKey, 'url5');

      const tab1Frame0Url = videoIdHandlingService.getUrlForTabFrame(tab1Id, 0);
      expect(tab1Frame0Url).toBe('url1');

      const tab1Frame1Url = videoIdHandlingService.getUrlForTabFrame(tab1Id, 1);
      expect(tab1Frame1Url).toBe('url2');

      const tab1Frame3Url = videoIdHandlingService.getUrlForTabFrame(tab1Id, 3);
      expect(tab1Frame3Url).toBe('url3');

      const tab1Frame5Url = videoIdHandlingService.getUrlForTabFrame(tab1Id, 5);
      expect(tab1Frame5Url).toBe('url4');

      const tab2Frame0Url = videoIdHandlingService.getUrlForTabFrame(tab2Id, 0);
      expect(tab2Frame0Url).toBe('url5');

      const allUrlsForTab1 = videoIdHandlingService.getAllUrlsForTab(tab1Id);
      expect(allUrlsForTab1.length).toBe(4);

      const allUrlsForTab2 = videoIdHandlingService.getAllUrlsForTab(tab2Id);
      expect(allUrlsForTab2.length).toBe(1);

      // const numberOfEntriesBeforeDelete =
      //   videoIdHandlingService.(tab1Id)?.size ?? 0;
      // expect(numberOfEntriesBeforeDelete).toBe(4);
    });

    it('removes entries for a given tab', () => {
      videoIdHandlingService.removeTabEntryFromLookupTable(tab1Id);
      const numberOfEntriesAfterDelete =
        videoIdHandlingService.getAllUrlsForTab(tab1Id)?.length ?? 0;
      expect(numberOfEntriesAfterDelete).toBe(0);
    });
  });

  describe('handlingYoutubeKey', () => {
    const videoUrlLookupService = new YoutubeHandlingService();
    const tab1Id = 503995893;

    it('adds the youtube handling key', () => {
      videoUrlLookupService.saveKeyToLookup(
        tab1Id,
        0,
        videoUrlLookupService.isHandlingYoutubeKey,
        'true',
      );

      const isHandlingYoutube = videoUrlLookupService.getIsHandlingYoutubeForTabFrame(tab1Id, 0);

      expect(isHandlingYoutube).toBeTrue();
    });
  });

  describe('removeVideoUrlByTabFrame', () => {
    const videoUrlLookupService = new YoutubeHandlingService();
    const tab1Id = 503995893;
    const tab2Id = 503995890;

    it('adds two entries to lookup', () => {
      videoUrlLookupService.saveKeyToLookup(tab1Id, 0, videoUrlLookupService.urlKey, 'url');
      videoUrlLookupService.saveKeyToLookup(tab2Id, 0, videoUrlLookupService.urlKey, 'url');

      const numberOfTab1EntriesAfterDelete =
        videoUrlLookupService.getAllUrlsForTab(tab1Id)?.length ?? 0;
      expect(numberOfTab1EntriesAfterDelete).toBe(1);
      const numberOfTab2EntriesAfterDelete =
        videoUrlLookupService.getAllUrlsForTab(tab2Id)?.length ?? 0;
      expect(numberOfTab2EntriesAfterDelete).toBe(1);
    });

    it('removes tab 1 frame 0 from list', () => {
      videoUrlLookupService.removeFrameEntryFromTab(tab1Id, 0);

      const numberOfTab1EntriesAfterDelete =
        videoUrlLookupService.getAllUrlsForTab(tab1Id)?.length ?? 0;
      expect(numberOfTab1EntriesAfterDelete).toBe(0);
      const numberOfTab2EntriesAfterDelete =
        videoUrlLookupService.getAllUrlsForTab(tab2Id)?.length ?? 0;
      expect(numberOfTab2EntriesAfterDelete).toBe(1);
    });
  });
});

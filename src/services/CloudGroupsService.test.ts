import CloudGroupsService, { DownloadResult } from './CloudGroupsService';
import MockTelemetryService from 'test-helpers/MockTelemetryService';
import StorageService from './StorageService';
import TemplateString from 'models/TemplateString';
import TenantId from 'models/TenantId';

const fetchSpy = (globalThis as any).fetch as jest.SpyInstance;

const millisecondsIn5Minutes = 300000;
const millisecondsIn1Hour = 3600000;
const secondsIn1Hour = 3600;
const urlTemplate = new TemplateString('{resource}/v1/users/{tenant}/{domain}/{user}.json{sas}');
const mockTelemetryService = new MockTelemetryService();
const tenantId = new TenantId('1d2468b3-c905-4242-b268-798159f65b00');
const user = '<EMAIL>';
const dummyResourceUrl = 'http://test.local/groups';
const dummySas = '?abc=123&xyz=456';
const eTag = 'xyzzy';

const successfulResponseBody = {
  username: user,
  groups: ['blah', 'foo', 'bar'],
};

const makeSuccessfulResponse = (): Response =>
  new Response(JSON.stringify(successfulResponseBody), {
    status: 200,
    statusText: 'ok',
    headers: { ETag: eTag },
  });

const makeNotFoundResponse = (): Response =>
  new Response('not found', {
    status: 404,
    statusText: 'not found',
  });

describe('CloudGroupsService', () => {
  let storageService: StorageService;
  let cloudGroupsService: CloudGroupsService;
  let onGroupsReady: jest.Mock;

  beforeEach(() => {
    storageService = new StorageService('test', undefined);
    cloudGroupsService = new CloudGroupsService(
      urlTemplate,
      mockTelemetryService,
      storageService,
      tenantId,
      user,
    );

    onGroupsReady = jest.fn();
    cloudGroupsService.onGroupsReady.addListener(onGroupsReady);

    // Simulate a successful groups download by default.
    fetchSpy.mockResolvedValue(makeSuccessfulResponse());

    // Suppress console messages during tests.
    jest.spyOn(console, 'debug').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    // Ensure any timers are stopped.
    cloudGroupsService.clear();
    jest.useRealTimers();
  });

  // -----------------------------------------------------------------------------------------------

  describe('loadFromCache()', () => {
    it('loads data from the on disk cache', async () => {
      const loadSpy = jest.spyOn(storageService, 'load');
      await cloudGroupsService.loadFromCache();
      expect(loadSpy).toHaveBeenCalled();
    });

    it('triggers the ready event if groups were loaded from cache', async () => {
      // Simulate data being loaded into the storage service when load() is called.
      jest.spyOn(storageService, 'load').mockImplementation(async (): Promise<boolean> => {
        storageService.set('cloudGroups', ['blah', 'foo', 'bar']);
        return await Promise.resolve(true);
      });

      await cloudGroupsService.loadFromCache();
      expect(onGroupsReady).toHaveBeenCalled();
    });

    it('does not triggers the ready event if no groups were loaded', async () => {
      await cloudGroupsService.loadFromCache();
      expect(onGroupsReady).not.toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('setConfig()', () => {
    it('does not download anything if the polling time is zero', async () => {
      jest.useFakeTimers();
      cloudGroupsService.setConfig({
        resource: dummyResourceUrl,
        sas: dummySas,
        polling: 0,
      });

      await jest.advanceTimersByTimeAsync(millisecondsIn1Hour * 2);
      expect(fetchSpy).not.toHaveBeenCalled();
    });

    it('stores an empty list of groups if the polling time is zero', async () => {
      jest.useFakeTimers();
      cloudGroupsService.setConfig({
        resource: dummyResourceUrl,
        sas: dummySas,
        polling: 0,
      });

      expect(onGroupsReady).toHaveBeenCalled();
      expect(cloudGroupsService.cloudGroups).toEqual([]);
    });

    it('stops existing regular downloads if the polling time is changed to zero', async () => {
      jest.useFakeTimers();

      // Configure the service to do downloads initially.
      cloudGroupsService.setConfig({
        resource: dummyResourceUrl,
        sas: dummySas,
        polling: secondsIn1Hour,
      });

      await jest.advanceTimersByTimeAsync(100);

      // Configure the service to stop downloads.
      cloudGroupsService.setConfig({
        resource: dummyResourceUrl,
        sas: dummySas,
        polling: 0,
      });

      // Make sure no more requests are sent.
      await jest.advanceTimersByTimeAsync(millisecondsIn1Hour * 5);
      expect(fetchSpy).toHaveBeenCalledOnce();
    });

    it('downloads groups immediately if this is the first call', async () => {
      jest.useFakeTimers();

      cloudGroupsService.setConfig({
        resource: dummyResourceUrl,
        sas: dummySas,
        polling: secondsIn1Hour,
      });
      await jest.advanceTimersByTimeAsync(100);

      expect(fetchSpy).toHaveBeenCalled();
      expect(onGroupsReady).toHaveBeenCalled();
      expect(cloudGroupsService.cloudGroups).toEqual(successfulResponseBody.groups);
    });

    it('downloads groups immediately if the download URL has changed', async () => {
      jest.useFakeTimers();

      // Configure the service with one URL, and ensure the download request goes there.
      cloudGroupsService.setConfig({
        resource: 'http://test-1.local/groups',
        sas: dummySas,
        polling: secondsIn1Hour,
      });
      await jest.advanceTimersByTimeAsync(100);

      expect(fetchSpy).toHaveBeenCalledTimes(1);
      const request1 = fetchSpy.mock.calls[0][0] as Request;
      expect(request1.url).toEqual(
        `http://test-1.local/groups/v1/users/${tenantId.toString()}/${btoa(
          'sw__allclouddomains',
        )}/${btoa(user)}.json?abc=123&xyz=456`,
      );

      // Change to a different URL and ensure another request goes there immediately.
      cloudGroupsService.setConfig({
        resource: 'http://test-2.local/groups',
        sas: dummySas,
        polling: secondsIn1Hour,
      });
      await jest.advanceTimersByTimeAsync(100);
      expect(fetchSpy).toHaveBeenCalledTimes(2);
      const request2 = fetchSpy.mock.calls[1][0] as Request;
      expect(request2.url).toEqual(
        `http://test-2.local/groups/v1/users/${tenantId.toString()}/${btoa(
          'sw__allclouddomains',
        )}/${btoa(user)}.json?abc=123&xyz=456`,
      );

      // Ensure the ready event was triggered in both cases
      expect(onGroupsReady).toHaveBeenCalledTimes(2);
      expect(cloudGroupsService.cloudGroups).toEqual(successfulResponseBody.groups);
    });

    it('starts a regular timer to download groups repeatedly according to polling time', async () => {
      jest.useFakeTimers();

      cloudGroupsService.setConfig({
        resource: dummyResourceUrl,
        sas: dummySas,
        polling: secondsIn1Hour,
      });

      // Give it long enough to download 5 times.
      await jest.advanceTimersByTimeAsync(millisecondsIn1Hour * 4.5);
      expect(fetchSpy).toHaveBeenCalledTimes(5);
      expect(cloudGroupsService.cloudGroups).toEqual(successfulResponseBody.groups);
    });

    it('stores the response etag and sends it in the headers of future requests', async () => {
      jest.useFakeTimers();

      cloudGroupsService.setConfig({
        resource: dummyResourceUrl,
        sas: dummySas,
        polling: secondsIn1Hour,
      });

      // The download needs to run twice: it will get the etag on the first download, and include it
      //  in the request for the second.
      await jest.advanceTimersByTimeAsync(millisecondsIn1Hour * 1.5);
      expect(fetchSpy).toHaveBeenCalledTimes(2);
      const request = fetchSpy.mock.calls[1][0] as Request;
      expect(request.headers.get('If-None-Match')).toEqual(eTag);
    });

    it('does nothing if the server reports that the file has not changed, still fires onGroupsReady event', async () => {
      jest.useFakeTimers();

      // Simulate groups already in memory.
      storageService.set('cloudGroups', ['abc', 'def', 'ghi']);

      fetchSpy.mockResolvedValue(
        new Response(null, {
          status: 304,
          statusText: 'not changed',
        }),
      );

      cloudGroupsService.setConfig({
        resource: dummyResourceUrl,
        sas: dummySas,
        polling: secondsIn1Hour,
      });

      await jest.advanceTimersByTimeAsync(100);
      expect(fetchSpy).toHaveBeenCalled();
      expect(onGroupsReady).toHaveBeenCalled();
      expect(cloudGroupsService.cloudGroups).toEqual(['abc', 'def', 'ghi']);
    });

    it('does not retry more frequently if the server responds with an error', async () => {
      jest.useFakeTimers();

      fetchSpy.mockResolvedValue(makeNotFoundResponse());

      cloudGroupsService.setConfig({
        resource: dummyResourceUrl,
        sas: dummySas,
        polling: secondsIn1Hour,
      });

      // Give it long enough to try 4 times.
      await jest.advanceTimersByTimeAsync(millisecondsIn1Hour * 3.5);
      expect(fetchSpy).toHaveBeenCalledTimes(4);
    });

    it('keeps existing groups if the downloads fails due to a server error', async () => {
      jest.useFakeTimers();

      // Simulate groups already being in cache.
      storageService.set('cloudGroups', ['abc', 'xyz', '123']);

      // Simulate the downloads failing.
      fetchSpy.mockResolvedValue(makeNotFoundResponse());

      cloudGroupsService.setConfig({
        resource: dummyResourceUrl,
        sas: dummySas,
        polling: secondsIn1Hour,
      });

      await jest.advanceTimersByTimeAsync(millisecondsIn1Hour * 3.5);
      expect(cloudGroupsService.cloudGroups).toEqual(['abc', 'xyz', '123']);
    });

    it('defaults to an empty list of groups if there are no groups cached and every download has failed with a server error', async () => {
      jest.useFakeTimers();

      // Simulate the downloads failing.
      fetchSpy.mockResolvedValue(makeNotFoundResponse());

      cloudGroupsService.setConfig({
        resource: dummyResourceUrl,
        sas: dummySas,
        polling: secondsIn1Hour,
      });

      await jest.advanceTimersByTimeAsync(millisecondsIn1Hour * 3.5);
      expect(cloudGroupsService.cloudGroups).toEqual([]);
    });

    it('retries more frequently than the polling time if the server does not respond at all', async () => {
      jest.useFakeTimers();

      fetchSpy.mockRejectedValue(new Error('test error'));

      cloudGroupsService.setConfig({
        resource: dummyResourceUrl,
        sas: dummySas,
        polling: millisecondsIn1Hour,
      });

      // It should retry every 5 minutes. Give it enough time to try 10 requests.
      await jest.advanceTimersByTimeAsync(millisecondsIn5Minutes * 9.5);
      expect(fetchSpy).toHaveBeenCalledTimes(10);
    });
  });

  it('keeps existing groups if the server does not respond at all', async () => {
    jest.useFakeTimers();

    // Simulate groups already being in cache.
    storageService.set('cloudGroups', ['abc', 'xyz', '123']);

    // Simulate the device being offline.
    fetchSpy.mockRejectedValue(new Error('test error'));

    cloudGroupsService.setConfig({
      resource: dummyResourceUrl,
      sas: dummySas,
      polling: secondsIn1Hour,
    });

    await jest.advanceTimersByTimeAsync(millisecondsIn1Hour);
    expect(cloudGroupsService.cloudGroups).toEqual(['abc', 'xyz', '123']);
  });

  it('defaults to an empty list of groups if there are no groups cached and the server does not respond at all', async () => {
    jest.useFakeTimers();

    // Simulate the device being offline.
    fetchSpy.mockRejectedValue(new Error('test error'));

    cloudGroupsService.setConfig({
      resource: dummyResourceUrl,
      sas: dummySas,
      polling: secondsIn1Hour,
    });

    await jest.advanceTimersByTimeAsync(millisecondsIn1Hour * 3.5);
    expect(cloudGroupsService.cloudGroups).toEqual([]);
  });

  // -----------------------------------------------------------------------------------------------

  describe('clear()', () => {
    it('does nothing if not configured', () => {
      expect(() => {
        cloudGroupsService.clear();
      }).not.toThrow();
    });

    it('stops regular timer for downloading groups', async () => {
      jest.useFakeTimers();

      // Configure the service to do downloads initially.
      cloudGroupsService.setConfig({
        resource: dummyResourceUrl,
        sas: dummySas,
        polling: secondsIn1Hour,
      });

      await jest.advanceTimersByTimeAsync(100);

      // Make sure no more requests are sent after clear is called.
      cloudGroupsService.clear();
      await jest.advanceTimersByTimeAsync(millisecondsIn1Hour * 5);
      expect(fetchSpy).toHaveBeenCalledOnce();
    });

    it('clears the groups stored in memory and in cache', async () => {
      jest.useFakeTimers();

      // Configure the service to do downloads initially.
      cloudGroupsService.setConfig({
        resource: dummyResourceUrl,
        sas: dummySas,
        polling: secondsIn1Hour,
      });

      await jest.advanceTimersByTimeAsync(100);
      onGroupsReady.mockClear();

      cloudGroupsService.clear();
      expect(cloudGroupsService.cloudGroups).toEqual([]);
      expect(onGroupsReady).not.toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('cloudGroups', () => {
    it('returns the groups stored in memory in the storage service', () => {
      storageService.set('cloudGroups', ['blah', 'foo', 'bar']);
      expect(cloudGroupsService.cloudGroups).toEqual(['blah', 'foo', 'bar']);
    });

    it('returns an empty array if no groups are loaded in memory', () => {
      expect(cloudGroupsService.cloudGroups).toEqual([]);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('shouldDownloadGroupsNow()', () => {
    it('returns false if service is not configured', () => {
      expect(cloudGroupsService.shouldDownloadGroupsNow()).toBeFalse();
    });

    it('returns true if no request has been sent yet', () => {
      (cloudGroupsService as any)._pollingTime = millisecondsIn1Hour;
      expect(cloudGroupsService.shouldDownloadGroupsNow()).toBeTrue();
    });

    it('returns false if the last request had no response as was sent less than 5 minutes ago', () => {
      (cloudGroupsService as any)._pollingTime = millisecondsIn1Hour;
      (cloudGroupsService as any)._lastRequestSentAt = Date.now() - millisecondsIn5Minutes + 5000;
      (cloudGroupsService as any)._lastDownloadResult = DownloadResult.noResponse;
      expect(cloudGroupsService.shouldDownloadGroupsNow()).toBeFalse();
    });

    it('returns true if the last request had no response and was sent at least 5 minutes ago', () => {
      (cloudGroupsService as any)._pollingTime = millisecondsIn1Hour;
      (cloudGroupsService as any)._lastRequestSentAt = Date.now() - millisecondsIn5Minutes - 5000;
      (cloudGroupsService as any)._lastDownloadResult = DownloadResult.noResponse;
      expect(cloudGroupsService.shouldDownloadGroupsNow()).toBeTrue();
    });

    it('returns false if the last request had a failed response and was sent less than the polling time ago', () => {
      (cloudGroupsService as any)._pollingTime = millisecondsIn1Hour;
      (cloudGroupsService as any)._lastRequestSentAt = Date.now() - millisecondsIn1Hour + 5000;
      (cloudGroupsService as any)._lastDownloadResult = DownloadResult.failed;
      expect(cloudGroupsService.shouldDownloadGroupsNow()).toBeFalse();
    });

    it('returns true if the last request had a failed response and was sent more than the polling time ago', () => {
      (cloudGroupsService as any)._pollingTime = millisecondsIn1Hour;
      (cloudGroupsService as any)._lastRequestSentAt = Date.now() - millisecondsIn1Hour - 5000;
      (cloudGroupsService as any)._lastDownloadResult = DownloadResult.failed;
      expect(cloudGroupsService.shouldDownloadGroupsNow()).toBeTrue();
    });

    it('returns false if the last request had a successful response and was sent less than the polling time ago', () => {
      (cloudGroupsService as any)._pollingTime = millisecondsIn1Hour;
      (cloudGroupsService as any)._lastRequestSentAt = Date.now() - millisecondsIn1Hour + 5000;
      (cloudGroupsService as any)._lastDownloadResult = DownloadResult.succeeded;
      expect(cloudGroupsService.shouldDownloadGroupsNow()).toBeFalse();
    });

    it('returns true if the last request had a successful response and was sent more than the polling time ago', () => {
      (cloudGroupsService as any)._pollingTime = millisecondsIn1Hour;
      (cloudGroupsService as any)._lastRequestSentAt = Date.now() - millisecondsIn1Hour - 5000;
      (cloudGroupsService as any)._lastDownloadResult = DownloadResult.succeeded;
      expect(cloudGroupsService.shouldDownloadGroupsNow()).toBeTrue();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('extractDirectoryGroupNames()', () => {
    it('throws an error if body is null or undefined', () => {
      expect(() => {
        CloudGroupsService.extractDirectoryGroupNames(null);
      }).toThrow();
    });

    it('throws an error if groups property is missing', () => {
      expect(() => {
        CloudGroupsService.extractDirectoryGroupNames({});
      }).toThrow();
    });

    it('throws an error if groups property is not an array', () => {
      expect(() => {
        CloudGroupsService.extractDirectoryGroupNames({ groups: 'foobar' });
      }).toThrow();
    });

    it('throws an error if groups array contains any non-string values', () => {
      expect(() => {
        CloudGroupsService.extractDirectoryGroupNames({ groups: ['foo', 123, 'bar'] });
      }).toThrow();
    });

    it('returns an array of directory groups names extracted from a server response', () => {
      const body = { groups: ['blah', 'foo', 'bar'] };
      expect(CloudGroupsService.extractDirectoryGroupNames(body)).toEqual(['blah', 'foo', 'bar']);
    });

    it('omits empty group names from the returned list', () => {
      const body = { groups: ['blah', '', 'foo', 'bar', ''] };
      expect(CloudGroupsService.extractDirectoryGroupNames(body)).toEqual(['blah', 'foo', 'bar']);
    });

    it('returns an empty array if the groups array is empty', () => {
      expect(CloudGroupsService.extractDirectoryGroupNames({ groups: [] })).toEqual([]);
    });
  });
});

import { FirebaseApp } from 'firebase/app';
import { User, Auth, UserCredential } from 'firebase/auth';
import { getAuth, signInWithCustomToken } from 'firebase/auth/web-extension';
import Jwt from 'models/Jwt';
import { waitForAllPromisesAndFakeTimers } from 'test-helpers/mock-utilities';
import FirebaseAuthService from './FirebaseAuthService';
import MockTelemetryService from '../test-helpers/MockTelemetryService';

// Mock the Firebase authentication functions.
jest.mock('firebase/auth/web-extension', () => ({
  getAuth: jest.fn(),
  signInWithCustomToken: jest.fn(),
}));

// A mock for the unsubscribe function returned by onAuthStateChanged().
const mockUnsubscribe = jest.fn();

// A partial mock of the Auth object created by Firebase, and returned by getAuth().
const mockAuth = {
  onAuthStateChanged: jest.fn(() => mockUnsubscribe),
  setPersistence: jest.fn(async () => {
    await Promise.resolve();
  }),
  signOut: jest.fn(async () => {
    triggerAuthStateChangedCallback(null);
    await Promise.resolve();
  }),
} as unknown as Auth;

// Partial mocks of the User and Credential objects use by Firebase authentication.
const mockUser = {
  refreshToken: 'firebase-refresh-token',
} as unknown as User;

const mockUserCredential = {
  user: mockUser,
  providerId: 'test-provider-id',
} as unknown as UserCredential;

// A basic JWT for test purposes.
const testJwt = new Jwt(
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************.E4DGCetLw2D1WT7aLnxv_0ajMajWlMIkU-ERv9SHm7E',
);

// A mock of the Firebase App object we use for initialisation.
const mockFirebaseApp: FirebaseApp = {
  name: 'blah',
  options: {},
  automaticDataCollectionEnabled: false,
};

// Helper function to trigger the auth state changed callback.
// This is based on the callback provided to the mockAuth object.
// The specified data will be passed in.
const triggerAuthStateChangedCallback = (user: User | null): void => {
  expect(mockAuth.onAuthStateChanged).toHaveBeenCalled();
  const mock = mockAuth.onAuthStateChanged as jest.Mock;
  const callback = mock.mock.calls[mock.mock.calls.length - 1][0];
  callback(user);
};

// Utility function to prevent warnings and errors from being logged to the console.
// This is useful for tests which need to go through error cases.
const suppressConsole = (): void => {
  jest.spyOn(console, 'warn').mockImplementation(() => {});
  jest.spyOn(console, 'error').mockImplementation(() => {});
};

const telemetryService = new MockTelemetryService();

describe('FirebaseAuthService', () => {
  beforeAll(() => {
    // We can't initial these mocks properly in the jest.mock() call above due to the
    //  initialisation order.
    (getAuth as jest.Mock).mockReturnValue(mockAuth);
    (signInWithCustomToken as jest.Mock).mockImplementation(async (): Promise<UserCredential> => {
      triggerAuthStateChangedCallback(mockUser);
      return mockUserCredential;
    });
  });

  beforeEach(() => {
    // Mock all timers so that we don't have to use hacks to wait for them.
    jest.useFakeTimers();

    // Suppress debug messages in the console.
    jest.spyOn(console, 'debug').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  describe('isRunning', () => {
    it('returns false if start() has not been called', () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      expect(firebaseAuthService.isRunning).toBeFalse();
    });

    it('returns true if start() has been called', async () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      expect(firebaseAuthService.isRunning).toBeTrue();
      await firebaseAuthService.stop();
    });

    it('returns false if stop() has been called', async () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      await firebaseAuthService.stop();
      expect(firebaseAuthService.isRunning).toBeFalse();
    });
  });

  describe('isAuthenticated', () => {
    it('returns false if start() has not been called', () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      expect(firebaseAuthService.isAuthenticated).toBeFalse();
    });

    it('returns false if start() has been called but we are not authenticated yet', async () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      expect(firebaseAuthService.isAuthenticated).toBeFalse();
      await firebaseAuthService.stop();
    });

    it('returns true if we are currently authenticated', async () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      firebaseAuthService.authenticate(testJwt);
      await waitForAllPromisesAndFakeTimers();
      expect(firebaseAuthService.isAuthenticated).toBeTrue();
      await firebaseAuthService.stop();
    });

    it('returns false if stop() has been called', async () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      firebaseAuthService.authenticate(testJwt);
      await waitForAllPromisesAndFakeTimers();
      await firebaseAuthService.stop();
      expect(firebaseAuthService.isAuthenticated).toBeFalse();
    });
  });

  describe('firebaseUser', () => {
    it('returns undefined if start() has not been called', () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      expect(firebaseAuthService.firebaseUser).toBeUndefined();
    });

    it('returns undefined if start() has been called but we are not authenticated yet', async () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      expect(firebaseAuthService.firebaseUser).toBeUndefined();
      await firebaseAuthService.stop();
    });

    it('returns the current user data if we are currently authenticated', async () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      firebaseAuthService.authenticate(testJwt);
      await waitForAllPromisesAndFakeTimers();
      expect(firebaseAuthService.firebaseUser).toBe(mockUser);
      await firebaseAuthService.stop();
    });

    it('returns undefined if stop() has been called', async () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      firebaseAuthService.authenticate(testJwt);
      await waitForAllPromisesAndFakeTimers();
      await firebaseAuthService.stop();
      expect(firebaseAuthService.firebaseUser).toBeUndefined();
    });
  });

  describe('onAuthenticated', () => {
    it('is triggered when we are authenticated for the first time', async () => {
      const onAuthenticatedMock = jest.fn();
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      firebaseAuthService.onAuthenticated.addListener(onAuthenticatedMock);
      await firebaseAuthService.start(mockFirebaseApp);
      firebaseAuthService.authenticate(testJwt);
      await waitForAllPromisesAndFakeTimers();
      expect(onAuthenticatedMock).toHaveBeenCalled();
      await firebaseAuthService.stop();
    });
  });

  describe('onAuthenticationRequired', () => {
    it('is triggered if we are not authenticated on startup', async () => {
      const onAuthenticationRequiredMock = jest.fn();
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      firebaseAuthService.onAuthenticationRequired.addListener(onAuthenticationRequiredMock);
      await firebaseAuthService.start(mockFirebaseApp);

      // Firebase calls the callback on startup if we have no pending user info.
      triggerAuthStateChangedCallback(null);
      await waitForAllPromisesAndFakeTimers();
      expect(onAuthenticationRequiredMock).toHaveBeenCalled();
      await firebaseAuthService.stop();
    });

    it('is triggered if we are signed-out remotely', async () => {
      suppressConsole();

      const firebaseAuthService = new FirebaseAuthService(telemetryService);

      const onAuthenticationRequiredMock = jest.fn();
      firebaseAuthService.onAuthenticationRequired.addListener(onAuthenticationRequiredMock);

      await firebaseAuthService.start(mockFirebaseApp);
      firebaseAuthService.authenticate(testJwt);
      await waitForAllPromisesAndFakeTimers();

      // Firebase calls the callback if we get signed-out remotely.
      triggerAuthStateChangedCallback(null);
      await waitForAllPromisesAndFakeTimers();
      expect(onAuthenticationRequiredMock).toHaveBeenCalled();
      await firebaseAuthService.stop();
    });

    it('is triggered if Firebase auth says the token has expired during authentication', async () => {
      suppressConsole();
      (signInWithCustomToken as jest.Mock).mockRejectedValueOnce({
        code: 'auth/user-token-expired',
      });

      const onAuthenticationRequiredMock = jest.fn();
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      firebaseAuthService.onAuthenticationRequired.addListener(onAuthenticationRequiredMock);
      await firebaseAuthService.start(mockFirebaseApp);
      firebaseAuthService.authenticate(testJwt);

      await waitForAllPromisesAndFakeTimers();
      expect(onAuthenticationRequiredMock).toHaveBeenCalled();
      await firebaseAuthService.stop();
    });
  });

  describe('onAuthenticationFailed', () => {
    it('is triggered if Firebase auth reports a permanent error', async () => {
      suppressConsole();

      (signInWithCustomToken as jest.Mock).mockRejectedValueOnce({
        code: 'auth/invalid-api-key',
      });

      const onAuthenticationFailedMock = jest.fn();
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      firebaseAuthService.onAuthenticationFailed.addListener(onAuthenticationFailedMock);
      await firebaseAuthService.start(mockFirebaseApp);
      firebaseAuthService.authenticate(testJwt);

      await waitForAllPromisesAndFakeTimers();

      expect(onAuthenticationFailedMock).toHaveBeenCalled();
      await firebaseAuthService.stop();
    });

    it('is triggered if Firebase auth reports an unrecognised error', async () => {
      suppressConsole();

      (signInWithCustomToken as jest.Mock).mockRejectedValueOnce(new Error('test-error'));

      const onAuthenticationFailedMock = jest.fn();
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      firebaseAuthService.onAuthenticationFailed.addListener(onAuthenticationFailedMock);
      await firebaseAuthService.start(mockFirebaseApp);
      firebaseAuthService.authenticate(testJwt);
      await waitForAllPromisesAndFakeTimers();
      expect(onAuthenticationFailedMock).toHaveBeenCalled();
      await firebaseAuthService.stop();
    });
  });

  describe('start()', () => {
    it('initialises Firebase authentication with the given Firebase app', async () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      expect(getAuth).toHaveBeenCalledWith(mockFirebaseApp);
      await firebaseAuthService.stop();
    });

    it('subscribes to auth state change messages', async () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      expect(mockAuth.onAuthStateChanged).toHaveBeenCalled();
      await firebaseAuthService.stop();
    });

    it('sets persistence method', async () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      expect(mockAuth.setPersistence).toHaveBeenCalled();
      await firebaseAuthService.stop();
    });

    it('does not reinitialise auth if start() had already been called', async () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      (getAuth as jest.Mock).mockClear();
      await firebaseAuthService.start(mockFirebaseApp);
      expect(getAuth).not.toHaveBeenCalled();
      await firebaseAuthService.stop();
    });

    it('does not resubscribe to auth state changes if start() had already been called', async () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      (mockAuth.onAuthStateChanged as jest.Mock).mockClear();
      await firebaseAuthService.start(mockFirebaseApp);
      expect(mockAuth.onAuthStateChanged).not.toHaveBeenCalled();
      await firebaseAuthService.stop();
    });
  });

  describe('authenticate()', () => {
    it('throws an error if start() has not been called', () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      expect(() => {
        firebaseAuthService.authenticate(testJwt);
      }).toThrow();
    });

    it('retries if Firebase reported a temporary error', async () => {
      suppressConsole();

      // Fail twice, then go back to the default (success) behaviour.
      (signInWithCustomToken as jest.Mock).mockRejectedValueOnce({
        code: 'auth/network-request-failed',
      });
      (signInWithCustomToken as jest.Mock).mockRejectedValueOnce({
        code: 'auth/network-request-failed',
      });

      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      firebaseAuthService.authenticate(testJwt);
      await waitForAllPromisesAndFakeTimers();
      await firebaseAuthService.stop();
      expect(signInWithCustomToken).toHaveBeenCalledTimes(3);
    });

    // Other test cases are covered by onAuthenticationRequired and onAuthenticationFailed events.
  });

  describe('stop()', () => {
    it('unsubscribes from auth state changes', async () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      await firebaseAuthService.stop();
      expect(mockUnsubscribe).toHaveBeenCalled();
    });

    it('does not sign out by default', async () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      await firebaseAuthService.stop();
      expect(mockAuth.signOut).not.toHaveBeenCalled();
    });

    it('signs out if signOut is true', async () => {
      suppressConsole();
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      await firebaseAuthService.stop(true);
      expect(mockAuth.signOut).toHaveBeenCalled();
    });

    it('does nothing if start() has not been called', async () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      await firebaseAuthService.stop();
    });

    it('does nothing if stop() has already been called', async () => {
      const firebaseAuthService = new FirebaseAuthService(telemetryService);
      await firebaseAuthService.start(mockFirebaseApp);
      await firebaseAuthService.stop();
      await firebaseAuthService.stop();
    });
  });
});

import ConstantBackOff from 'back-off-methods/ConstantBackOff';
import FetchService, { Action } from './FetchService';
import { waitForMockToBeCalled } from 'test-helpers/mock-utilities';

describe('FetchService', () => {
  const fetchMock = jest.fn();
  global.fetch = fetchMock;

  beforeEach(() => {
    fetchMock.mockReset();
    fetchMock.mockImplementation(async () => await Promise.resolve({}));
  });

  describe('constructor()', () => {
    it('throws an error if maxAttempts is less than 0', () => {
      expect(() => new FetchService(new ConstantBackOff(0, 1), -1)).toThrowError();
    });
  });

  describe('isRunning', () => {
    it('returns false if start() has not been called yet', () => {
      const fetchService = new FetchService(new ConstantBackOff(0, 0), 1);
      expect(fetchService.isRunning).toBeFalse();
    });

    it('returns true if a request attempt is currently in progress', async () => {
      const fetchService = new FetchService(new ConstantBackOff(1000, 0), 3);
      fetchService.setResponseHandler(() => Action.retry);
      await fetchService.start(new URL('http://example.com'));
      expect(fetchService.isRunning).toBeTrue();
      await fetchService.stop();
    });

    it('returns false if a request run has finished because the response handler said stop', async () => {
      const fetchService = new FetchService(new ConstantBackOff(1000, 0), 3);
      fetchService.setResponseHandler(() => Action.stop);
      await fetchService.start(new URL('http://example.com'));
      await fetchService.waitUntilFinished();
      expect(fetchService.isRunning).toBeFalse();
    });

    it('returns false if a request run has finished because it ran out of attempts', async () => {
      const fetchService = new FetchService(new ConstantBackOff(0, 0), 3);
      fetchService.setResponseHandler(() => Action.retry);
      await fetchService.start(new URL('http://example.com'));
      await fetchService.waitUntilFinished();
      expect(fetchService.isRunning).toBeFalse();
    });

    it('returns false if a request run has finished because stop() was called', async () => {
      const fetchService = new FetchService(new ConstantBackOff(1000, 0), 5);
      fetchService.setResponseHandler(() => Action.retry);
      await fetchService.start(new URL('http://example.com'));
      await fetchService.stop();
      expect(fetchService.isRunning).toBeFalse();
    });
  });

  describe('attemptsRemaining', () => {
    it('returns 0 if start() has not been called yet', () => {
      const fetchService = new FetchService(new ConstantBackOff(0, 0), 1);
      expect(fetchService.attemptsRemaining).toEqual(0);
    });

    it('returns the number of attempts remaining if a run is currently in progress', async () => {
      const fetchService = new FetchService(new ConstantBackOff(0, 0), 3);
      fetchService.setResponseHandler(() => Action.retry);
      await fetchService.start(new URL('http://example.com'));

      expect(fetchService.attemptsRemaining).toEqual(3);
      await waitForMockToBeCalled(fetchMock, 2);
      expect(fetchService.attemptsRemaining).toBeLessThan(3);
      await fetchService.stop();
    });
  });

  describe('start()', () => {
    it('rejects if the response handler has not been set yet', async () => {
      const fetchService = new FetchService(new ConstantBackOff(0, 0), 1);
      await expect(fetchService.start(new URL('http://example.com'))).toReject();
    });

    it('sends a request to the specified URL', async () => {
      const url = new URL('http://example.com');
      const fetchService = new FetchService(new ConstantBackOff(0, 0), 1);
      fetchService.setResponseHandler(() => Action.stop);
      await fetchService.start(url);
      await fetchService.waitUntilFinished();
      expect(fetchMock).toHaveBeenCalledWith(url.toString(), expect.anything());
    });

    it('sends a request with the specified options', async () => {
      const options: RequestInit = {
        method: 'POST',
        body: JSON.stringify({
          hello: 'world',
          testing: 123,
        }),
      };
      const fetchService = new FetchService(new ConstantBackOff(0, 0), 1);
      fetchService.setResponseHandler(() => Action.stop);
      await fetchService.start(new URL('http://example.com'), options);
      await fetchService.waitUntilFinished();
      expect(fetchMock).toHaveBeenCalledWith(expect.anything(), expect.objectContaining(options));
    });

    it('passes the API response to the response handler', async () => {
      const response = {
        hello: 'world',
        testing: 123,
      };
      fetchMock.mockReturnValue(response);
      const fetchService = new FetchService(new ConstantBackOff(0, 0), 1);
      const onResponseMock = jest.fn(() => Action.stop);
      fetchService.setResponseHandler(onResponseMock);
      await fetchService.start(new URL('http://example.com'));
      await fetchService.waitUntilFinished();
      expect(onResponseMock).toHaveBeenCalledWith(response);
    });

    it('retries the request up to the maximum number of attempts if the response handler says to retry', async () => {
      const fetchService = new FetchService(new ConstantBackOff(0, 0), 5);
      fetchService.setResponseHandler(() => Action.retry);
      await fetchService.start(new URL('http://example.com'));
      await fetchService.waitUntilFinished();
      expect(fetchMock).toHaveBeenCalledTimes(5);
    });

    it('stops retrying if the response handler says stop', async () => {
      // Stop after the third request.
      const onResponseMock = jest.fn();
      onResponseMock.mockReturnValueOnce(Action.retry);
      onResponseMock.mockReturnValueOnce(Action.retry);
      onResponseMock.mockReturnValue(Action.stop);

      const fetchService = new FetchService(new ConstantBackOff(0, 0), 5);
      fetchService.setResponseHandler(onResponseMock);
      await fetchService.start(new URL('http://example.com'));
      await fetchService.waitUntilFinished();
      expect(fetchMock).toHaveBeenCalledTimes(3);
    });

    it('waits at least the specified time between retries', async () => {
      const delay = 1000;
      const fetchService = new FetchService(new ConstantBackOff(delay, 0), 2);
      fetchService.setResponseHandler(() => Action.retry);
      await fetchService.start(new URL('http://example.com'));
      await waitForMockToBeCalled(fetchMock, 1);
      const startTime = Date.now();
      await waitForMockToBeCalled(fetchMock, 2);
      expect(Date.now() - startTime).toBeGreaterThanOrEqual(delay - 10); // <-- allow a margin
      await fetchService.waitUntilFinished();
    });

    it('applies the delay correctly when the max attempts is unlimited', async () => {
      const delay = 1000;
      const fetchService = new FetchService(new ConstantBackOff(delay, 0), 0);
      fetchService.setResponseHandler(() => Action.retry);
      await fetchService.start(new URL('http://example.com'));
      await waitForMockToBeCalled(fetchMock, 1);
      const startTime = Date.now();
      await waitForMockToBeCalled(fetchMock, 2);
      expect(Date.now() - startTime).toBeGreaterThanOrEqual(delay - 10); // <-- allow a margin
      await fetchService.stop();
    });

    it('stops any existing attempts before starting a new one', async () => {
      const firstUrl = new URL('http://first.example.com');
      const secondUrl = new URL('http://second.example.com');
      const fetchService = new FetchService(new ConstantBackOff(1000, 0), 5);
      fetchService.setResponseHandler(() => Action.retry);
      await fetchService.start(firstUrl);
      await waitForMockToBeCalled(fetchMock, 1);
      await fetchService.start(secondUrl);
      await waitForMockToBeCalled(fetchMock, 2);
      await fetchService.stop();
      expect(fetchMock).toHaveBeenLastCalledWith(secondUrl.toString(), expect.anything());
    });
  });

  describe('stop', () => {
    it('does nothing if start() has not been called', async () => {
      const fetchService = new FetchService(new ConstantBackOff(0, 0), 1);
      await fetchService.stop();
    });

    it('does nothing if stop() has already been called', async () => {
      const fetchService = new FetchService(new ConstantBackOff(500, 0), 5);
      fetchService.setResponseHandler(() => Action.retry);
      await fetchService.start(new URL('http://example.com'));
      await fetchService.stop();
      await fetchService.stop();
    });

    it('does nothing if the current run has already stopped because the response handler said stop', async () => {
      const fetchService = new FetchService(new ConstantBackOff(0, 0), 1);
      fetchService.setResponseHandler(() => Action.stop);
      await fetchService.start(new URL('http://example.com'));
      await fetchService.waitUntilFinished();
      await fetchService.stop();
    });

    it('does nothing if the current run has already stopped because it ran out of attempts', async () => {
      const fetchService = new FetchService(new ConstantBackOff(0, 0), 3);
      fetchService.setResponseHandler(() => Action.retry);
      await fetchService.start(new URL('http://example.com'));
      await fetchService.waitUntilFinished();
      await fetchService.stop();
    });

    it('prevents any more attempts to send requests', async () => {
      const fetchService = new FetchService(new ConstantBackOff(1000, 0), 3);
      fetchService.setResponseHandler(() => Action.retry);
      await fetchService.start(new URL('http://example.com'));
      await fetchService.stop();
      expect(fetchMock.mock.calls.length).toBeLessThanOrEqual(1);
    });

    it('does not wait until the delay between attempts has finished', async () => {
      const delay = 10000;
      const fetchService = new FetchService(new ConstantBackOff(delay, 0), 3);
      fetchService.setResponseHandler(() => Action.retry);
      const startTime = Date.now();
      await fetchService.start(new URL('http://example.com'));
      await fetchService.stop();
      expect(Date.now() - startTime).toBeLessThan(delay);
    });
  });

  describe('waitUntilFinished()', () => {
    it('resolves to false if start() has not been called', async () => {
      const fetchService = new FetchService(new ConstantBackOff(0, 0), 1);
      await expect(fetchService.waitUntilFinished()).resolves.toBeFalse();
    });

    it('resolves to true if the latest run stopped because it ran out of attempts', async () => {
      const fetchService = new FetchService(new ConstantBackOff(0, 0), 3);
      fetchService.setResponseHandler(() => Action.retry);
      await fetchService.start(new URL('http://example.com'));
      await expect(fetchService.waitUntilFinished()).resolves.toBeTrue();
    });

    it('resolves to false if the latest run stopped because the response handler said stop', async () => {
      const fetchService = new FetchService(new ConstantBackOff(0, 0), 3);
      fetchService.setResponseHandler(() => Action.stop);
      await fetchService.start(new URL('http://example.com'));
      await expect(fetchService.waitUntilFinished()).resolves.toBeFalse();
    });

    it('resolves to false if the latest run stopped because stop() was called', async () => {
      const fetchService = new FetchService(new ConstantBackOff(1000, 0), 5);
      fetchService.setResponseHandler(() => Action.retry);
      await fetchService.start(new URL('http://example.com'));
      await fetchService.stop();
      await expect(fetchService.waitUntilFinished()).resolves.toBeFalse();
    });
  });

  describe('onFinished', () => {
    it('is triggered with false when a run finished because the response handler said stop', async () => {
      const url = new URL('http://example.com');
      const fetchService = new FetchService(new ConstantBackOff(0, 0), 1);
      fetchService.setResponseHandler(() => Action.stop);
      const onFinishedMock = jest.fn();
      fetchService.onFinished.addListener(onFinishedMock);
      await fetchService.start(url);
      await waitForMockToBeCalled(onFinishedMock, 1);
      expect(onFinishedMock).toHaveBeenCalledWith(false, url);
    });

    it('is triggered with false when a run finished because stop() was called', async () => {
      const url = new URL('http://example.com');
      const fetchService = new FetchService(new ConstantBackOff(1000, 0), 5);
      fetchService.setResponseHandler(() => Action.retry);
      const onFinishedMock = jest.fn();
      fetchService.onFinished.addListener(onFinishedMock);
      await fetchService.start(url);
      await fetchService.stop();
      expect(onFinishedMock).toHaveBeenCalledWith(false, url);
    });

    it('is triggered with true when a run finished because it ran out of attempts', async () => {
      const url = new URL('http://example.com');
      const fetchService = new FetchService(new ConstantBackOff(0, 0), 5);
      fetchService.setResponseHandler(() => Action.retry);
      const onFinishedMock = jest.fn();
      fetchService.onFinished.addListener(onFinishedMock);
      await fetchService.start(url);
      await waitForMockToBeCalled(onFinishedMock, 1);
      expect(onFinishedMock).toHaveBeenCalledWith(true, url);
    });

    it('receives the URL which was being requested', async () => {
      const url = new URL('http://example.com');
      const fetchService = new FetchService(new ConstantBackOff(0, 0), 1);
      fetchService.setResponseHandler(() => Action.stop);
      const onFinishedMock = jest.fn();
      fetchService.onFinished.addListener(onFinishedMock);
      await fetchService.start(url);
      await waitForMockToBeCalled(onFinishedMock, 1);
      expect(onFinishedMock).toHaveBeenCalledWith(false, url);
    });
  });
});

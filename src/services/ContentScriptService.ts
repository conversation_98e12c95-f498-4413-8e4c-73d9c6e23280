import AcknowledgementMessage from 'content-script-messages/AcknowledgementMessage';
import AllowContentMessage from 'content-script-messages/AllowContentMessage';
import BlockContentMessage from 'content-script-messages/BlockContentMessage';
import ContentScriptMessage from 'content-script-messages/ContentScriptMessage';
import EnableYoutubeHandlingMessage from 'content-script-messages/EnableYoutubeHandlingMessage';
import PageContentMessage from 'content-script-messages/PageContentMessage';
import RequestPageContentMessage from 'content-script-messages/RequestPageContentMessage';
import YoutubeBlockedIdsMessage from 'content-script-messages/YoutubeBlockedIdsMessage';
import YoutubePageHrefsMessage from 'content-script-messages/YoutubePageHrefsMessage';
import { generateUrlVariationsForLookup } from 'guardian/utilities/GuardianUtilities';
import { isDocumentPrerendered, onPrerenderActivation } from '../utilities/ContentScriptPrerenderDetection';

import { BodyContentMod, jsBodyMods } from '../guardian/ContentModScripts';
import <PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON> from '../utils/youtube/YouTubeContentHandler';

/**
 * Status values indicating whether the page has been explicitly allowed or blocked.
 */
enum ContentStatus {
  // Still waiting for a filtering decision from the extension.
  pending,

  // The extension told us to allow the page (i.e. allow the content to be seen).
  allowed,

  // The extension told us to block the page (i.e. hide the content).
  blocked,
}

/**
 * Implements the business logic of the content script.
 */
export default class ContentScriptService {
  // Exposed for testing
  public mutationCallback: ((mutations: any) => void) | null = null;

  // YouTube content handler
  private _youtubeContentHandler: YouTubeContentHandler | null = null;
  // -----------------------------------------------------------------------------------------------
  // General operations.

  /**
   * Registers event listeners.
   */
  public readonly addListeners = (): void => {
    chrome.runtime.onMessage.addListener(this.onStandaloneMessage);
  };

  public readonly start = (): void => {
    // Check if we're in a prerendered context
    if (isDocumentPrerendered()) {
      console.debug('Content script detected prerender - deferring execution until activation');
      
      // Set up listener for when prerender becomes active
      const cleanup = onPrerenderActivation(() => {
        console.debug('Prerender activated - starting content script execution');
        cleanup(); // Remove the listener
        this._startNormal(); // Execute normal startup
      });
      
      return; // Exit early - don't execute anything during prerender
    }
    
    // Normal execution for non-prerendered pages
    this._startNormal();
  };

  /**
   * Normal startup logic that runs for active (non-prerendered) pages.
   */
  private readonly _startNormal = (): void => {
    chrome.storage.managed.get(['DisableAdvancedYoutubeScanning'], (key) => {
      this._disableYoutubeScanning = key?.DisableAdvancedYoutubeScanning === true;
    });
    // Check if the page has been excluded from the document hider.
    // If not then the page should be hidden until the extension tells us the content is allowed.
    chrome.storage.managed.get(['DocumentHiderExclusions'], (exclusions) => {
      if (exclusions === undefined || exclusions.DocumentHiderExclusions === undefined) {
        return;
      }

      const urlVariations = generateUrlVariationsForLookup(new URL(window.location.href));

      let isExcluded = false;
      for (const exclusion of exclusions.DocumentHiderExclusions.split(',')) {
        const sanitisedExclusion = this._sanitiseUrl(exclusion).toLowerCase();

        for (const variation of urlVariations) {
          const sanitisedVariation = this._sanitiseUrl(variation).toLowerCase();
          if (sanitisedVariation === sanitisedExclusion) {
            isExcluded = true;
            break;
          }
        }

        if (isExcluded) {
          // No need to check anymore exclusions.
          break;
        }
      }

      if (!isExcluded) {
        this.hidePage();
      }
    });

    // If the document has already loaded then send our content to the extension immediately.
    // Otherwise, wait until it's finished loading.
    if (document.readyState !== 'loading') {
      this.startRepeatedlySendingContentToExtension();
    } else {
      const callback = (): void => {
        if (document.readyState !== 'loading') {
          this.startRepeatedlySendingContentToExtension();
          document.removeEventListener('readystatechange', callback);
        }
      };
      document.addEventListener('readystatechange', callback);
    }
  };

  /**
   * Starts regularly sending content to the extension until a response is received.
   * This will send it once immediately, then again each time the specified timeout elapses.
   * If we were already sending content regularly then this will stop the existing timer and
   *  start a new one.
   *
   * @param timeout The time in milliseconds between sending the content again.
   */
  public readonly startRepeatedlySendingContentToExtension = (timeout: number = 5000): void => {
    this.stopRepeatedlySendingContentToExtension();
    this.sendContentToExtension();
    this._contentSendInterval = setInterval(() => {
      this.sendContentToExtension();
    }, timeout);
  };

  /**
   * Stop repeatedly sending content to the extension.
   * This will cancel the ongoing timeout, if there is one.
   */
  public readonly stopRepeatedlySendingContentToExtension = (): void => {
    if (this._contentSendInterval !== undefined) {
      clearInterval(this._contentSendInterval);
      this._contentSendInterval = undefined;
    }
  };

  /**
   * Extracts the current document content and sends it to the extension.
   * This will handle the response if one is received.
   */
  public readonly sendContentToExtension = (htmlOverride: string | null = null): void => {
    try {
      const domContent =
        htmlOverride !== null
          ? htmlOverride
          : document.querySelector('html')?.outerHTML.substring(0, 256000) ?? '';

      chrome.runtime.sendMessage(
        chrome.runtime.id,
        new PageContentMessage(
          window.location.href,
          // Truncate the content to a reasonable length if necessary.
          domContent,
          document.title,
        ),
        this.handleMessageFromExtension,
      );
    } catch (e: any) {
      console.error('Failed to send content to extension');
      console.error(e);
    }
  };

  /**
   * Extracts the current youtube document content and sends it to the extension.
   * This will handle the response if one is received.
   * Uses the YouTubeContentHandler utility for efficient URL processing.
   */
  public readonly sendYoutubeContentToExtension = (htmlOverride: string | null = null): void => {
    try {
      if (!this._isHandlingYoutube || this._youtubeContentHandler == null) {
        return;
      }

      // Extract URLs from the document or provided HTML using the content handler
      const newUrls = this._youtubeContentHandler.extractUrls(
        htmlOverride ?? document.documentElement.outerHTML,
      );

      if (newUrls.length === 0) {
        return;
      }

      // Process the URLs using the content handler
      this._youtubeContentHandler.processUrls(newUrls, (batch: string[]) => {
        // Send the batch to the extension
        console.debug(`Processing batch of ${batch.length} URLs`);
        chrome.runtime.sendMessage(
          chrome.runtime.id,
          new YoutubePageHrefsMessage(batch),
          this.handleMessageFromExtension,
        );
      });
    } catch (e: any) {
      console.error('Failed to send content to extension');
      console.error(e);
    }
  };

  /**
   * Try to hide the current page behind a blank rectangle.
   * This prevents content from being visible briefly before we've had a chance to analyse it.
   *
   * @param maxAttempts The maximum number of times to attempt to hide the page in case of an error.
   *
   * @note This will do nothing if _contentStatus is allowed. This is intended to guard against a
   *  potential race condition.
   */
  public readonly hidePage = (maxAttempts: number = 5): void => {
    /*
     * Code specific to the testfiltering.com filter tests.
     */
    if (window.location.origin.includes('testfiltering.com')) {
      const highestTimeoutId = setTimeout(';'); // eslint-disable-line
      for (let i = 0; i < highestTimeoutId; i++) {
        clearTimeout(i);
      }

      const harmfulElement = document.querySelector('body > ul');
      if (harmfulElement != null) {
        harmfulElement.remove();
      }
      return;
    }

    if (maxAttempts <= 0) {
      return;
    }

    // Schedule a redraw of the page.
    window.requestAnimationFrame((): void => {
      // Do nothing if the content has been allowed while we've been waiting to redraw.
      // This is particularly important if we've had to retry hiding the page.
      if (this._contentStatus === ContentStatus.allowed) {
        return;
      }

      // Delete any other element with the same ID first.
      try {
        document.getElementById(this._documentHiderId)?.remove();
      } catch (e: any) {
        // Ignore the error.
      }

      try {
        this._documentHider = document.createElement('div');
        this._documentHider.style.position = 'fixed';
        this._documentHider.style.width = '100%';
        this._documentHider.style.height = '100%';
        this._documentHider.style.zIndex = '2147483647';
        this._documentHider.style.background = 'rgba(255,255,255,1)';
        this._documentHider.style.display = 'block';
        this._documentHider.style.left = '0';
        this._documentHider.style.top = '0';
        this._documentHider.id = this._documentHiderId;

        document.body.appendChild(this._documentHider);
        console.debug('Page content hidden');
      } catch (e: any) {
        console.error(`Failed to hide page content: ${JSON.stringify(e)}`);
        this._documentHider = undefined;
        this.hidePage(maxAttempts - 1);
      }
    });
  };

  /**
   * Stop hiding the page so that the user can see it again.
   * This is done if content has passed analysis and is allowed, or the product is not licensed.
   */
  public readonly unhidePage = (): void => {
    window.requestAnimationFrame(() => {
      // Do nothing if the content has been blocked while we've been waiting to redraw.
      if (this._contentStatus === ContentStatus.blocked) {
        return;
      }

      // If we have a reference to the document hider then try to remove it directly.
      // This should work under normal circumstances.
      try {
        if (this._documentHider !== undefined) {
          this._documentHider.remove();
          console.debug('Page content unhidden');
        }
      } catch (e: any) {
        console.error(`Error while unhiding page content: ${JSON.stringify(e)}`);
      }
      this._documentHider = undefined;

      // As a backup, double-check that we didn't leave any document hiders behind somehow.
      try {
        document.querySelectorAll(`[id=${this._documentHiderId}]`).forEach((element) => {
          element.remove();
          console.debug('Page content unhidden');
        });
      } catch (e: any) {
        console.debug('An error occurred while unhiding the page content.', e);
      }
    });
  };

  /**
   * Act on a message received from the extension.
   *
   * @param rawMessage The message received from the extension.
   */
  public readonly handleMessageFromExtension = (rawMessage: any): boolean => {
    const message = ContentScriptMessage.load(rawMessage);

    if (message instanceof AllowContentMessage) {
      console.log('Content allowed', { enabledContentMods: message.contentModIds });
      this._contentStatus = ContentStatus.allowed;

      // If any content mods were enabled then fetch them.
      this._enabledContentMods.clear();
      const discardedIds = new Set<string>();
      message.contentModIds.forEach((idString: string) => {
        const id = parseInt(idString);
        const mod = jsBodyMods.getContentModScriptById(id);
        if (mod === undefined) {
          discardedIds.add(idString);
        } else {
          this._enabledContentMods.set(id, mod);
        }
      });
      if (discardedIds.size > 0) {
        console.debug(`Discarding unrecognised content mod(s): `, [...discardedIds]);
      }

      // Apply the content mods immediately, and again on any document change.
      if (this._enabledContentMods.size > 0) {
        this._executeContentMods();
        this._executeContentModsOnDomChange();
      } else {
        this._stopExecutingContentModsOnDomChange();
      }

      // Try to reveal the page content immediately.
      this.unhidePage();

      // Try again after a short delay. This is to handle webpages which do weird things with the
      //  DOM, sometimes resulting in clones of our document hider appearing after we've tried to
      //  remove it.
      setTimeout(() => {
        this.unhidePage();
      }, 1000);

      this.stopRepeatedlySendingContentToExtension();
      return true;
    }

    if (message instanceof BlockContentMessage) {
      console.log('Content blocked');
      this._contentStatus = ContentStatus.blocked;
      this.hidePage();
      this.stopRepeatedlySendingContentToExtension();
      this._stopExecutingContentModsOnDomChange();
      return true;
    }

    if (message instanceof RequestPageContentMessage) {
      if (document.readyState !== 'loading') {
        this.sendContentToExtension();
      }
      // eslint-disable-next-line no-useless-return
      return true;
    }

    if (message instanceof EnableYoutubeHandlingMessage) {
      if (this._disableYoutubeScanning) {
        return true;
      }

      if (
        !new URL(window.location.href).hostname.includes('youtube.com') ||
        this._isHandlingYoutube
      ) {
        return true;
      }

      if (!this._isHandlingYoutube) {
        console.debug('Enabling youtube handling and modification');
        this._handleYoutube();
        this._isHandlingYoutube = true;
      }
      return true;
    }

    if (message instanceof YoutubeBlockedIdsMessage) {
      // Pass the blocked IDs directly to the content handler
      if (this._youtubeContentHandler != null && message.blockedIds.length > 0) {
        // Update the content handler with the new blocked IDs
        this._youtubeContentHandler.updateBlockedIds(message.blockedIds);

        // Trigger DOM update to remove elements with these IDs
        window.dispatchEvent(new Event(this._modifyYoutubeDOMEventName));
      }
      return true;
    }

    return false;
  };

  /**
   * Run any enabled body content mods against the current document.
   */
  private readonly _executeContentMods = (): void => {
    let hasExecutedAnyContentMods = false;

    const idsToSuppress = new Set<number>();
    this._enabledContentMods.forEach((mod: BodyContentMod, id: number) => {
      if (this._suppressedContentModIds.has(id)) {
        return;
      }

      try {
        const results = mod();
        if (results.length > 0 && results.every((result) => result === null)) {
          idsToSuppress.add(id);
          return;
        }
        hasExecutedAnyContentMods = true;
      } catch (e: any) {
        console.error(`Content mod ${id} failed with an error: `, e);
        idsToSuppress.add(id);
        this._suppressedContentModIds.add(id);
      }
    });

    if (idsToSuppress.size > 0) {
      console.debug('Suppressing content mods which failed or are not relevant: ', [
        ...idsToSuppress,
      ]);
      idsToSuppress.forEach((id) => {
        this._suppressedContentModIds.add(id);
      });
    }

    if (!hasExecutedAnyContentMods) {
      // There are no unsuppressed content mods so there's no point executing them again.
      this._stopExecutingContentModsOnDomChange();
    }
  };

  /**
   * Enables additional handling of Youtube
   * This will enable:
   * - Breaking SPA behaviour and force page reloads on navigation
   * - Passing page content back to the extension whenever the DOM is updated
   */
  private readonly _handleYoutube = (): void => {
    if (this._disableYoutubeScanning) {
      return;
    }

    // Initialize the YouTube content handler with our configuration
    this._youtubeContentHandler = new YouTubeContentHandler({
      elementNames: this._youtubeElementNames,
      protectedSelectors: this._youtubeProtectedSelectors,
      throttleTime: this._rescanThrottle,
    });

    document.onload = () => {
      window.dispatchEvent(new Event(this._modifyYoutubeDOMEventName));
    };

    // Store the mutation callback as a property for testing
    this.mutationCallback = (mutations: MutationRecord[]): void => {
      if (this._youtubeContentHandler == null) return;

      // Process mutations using the content handler
      const eventNeeded = this._youtubeContentHandler.processMutations(
        mutations,
        (htmlContent: string) => {
          // Send content to extension when found
          this.sendYoutubeContentToExtension(htmlContent);
        },
      );

      // Trigger DOM update if needed
      if (eventNeeded && this._canHandleYoutubeEvents) {
        window.dispatchEvent(new Event(this._modifyYoutubeDOMEventName));
      }
    };

    const observer = new MutationObserver(this.mutationCallback);

    observer.observe(document, {
      childList: true,
      subtree: true, // Observe descendants of all elements
    });

    /*
     * YT custom event which fires when navigation finishes
     */
    window.addEventListener('yt-navigate-finish', () => {
      window.dispatchEvent(new Event(this._modifyYoutubeDOMEventName));
    });

    window.addEventListener(this._modifyYoutubeDOMEventName, () => {
      this._removeMaliciousYoutubeIdElements();
    });

    window.dispatchEvent(new Event(this._modifyYoutubeDOMEventName)); // Dispatch event after setup incase tab is already loaded
  };

  /**
   * Removes elements containing blocked YouTube video IDs from the page
   * Uses the YouTubeContentHandler utility for efficient content filtering
   */
  private readonly _removeMaliciousYoutubeIdElements = (): void => {
    if (
      !this._canHandleYoutubeEvents ||
      this._disableYoutubeScanning ||
      this._youtubeContentHandler == null
    ) {
      return;
    }

    // Throttle execution to avoid excessive DOM manipulation
    this._canHandleYoutubeEvents = false;
    setTimeout(() => {
      this._canHandleYoutubeEvents = true;
    }, 500);

    // Use the content handler to remove blocked content
    this._youtubeContentHandler.removeBlockedContent();
  };

  /**
   * Called when a DOM change is detected by the observer.
   * This is currently only used for content mods, but could probably be combined with the YouTube
   *  mutation observer in future.
   */
  private readonly _onDOMChanged = (): void => {
    // Limit how often we run content mods in response to DOM changes.
    // This is important as it reduces the likelihood that we'll execute content mods in response to
    //  changes made previously by the content mods themselves
    // It also reduces the performance impact of content mods on pages which update frequently.
    const now = Date.now();
    const elapsed = now - this._contentModsLastExecutedAt;
    if (elapsed > 200) {
      this._contentModsLastExecutedAt = now;
      this._executeContentMods();
    }
  };

  /**
   * Watch for DOM changes, and apply all enabled content mods when that happens.
   */
  private readonly _executeContentModsOnDomChange = (): void => {
    this._contentModMutationObserver.disconnect();
    if (this._enabledContentMods.size > 0) {
      this._contentModMutationObserver.observe(document.body, { childList: true, subtree: true });
    }
  };

  /**
   * Stop watching for DOM changes which would otherwise trigger content mods to run.
   */
  private readonly _stopExecutingContentModsOnDomChange = (): void => {
    this._contentModMutationObserver.disconnect();
  };

  /**
   * Remove the scheme prefix, www subdomain, and any trailing slashes, from a URL string.
   */
  private readonly _sanitiseUrl = (url: string): string => {
    return url.replace(/^(\w*:\/\/)?(www\.)?/, '').replace(/\/+$/, '');
  };

  // -----------------------------------------------------------------------------------------------
  // Event handlers.

  /**
   * A standalone message has been received from the extension.
   *
   * @param rawMessage The message which was received.
   * @param sender Identifies where the message came from. Currently, we always assume it's our
   *  extension.
   * @param sendResponse A function used to send a response back to the sender. It's important to
   *  respond, even if the response is empty, so that the extension knows we're listening.
   */
  public readonly onStandaloneMessage = (
    rawMessage: any,
    _sender: chrome.runtime.MessageSender,
    sendResponse: (response: any) => void,
  ): void => {
    const shouldSendResponse = this.handleMessageFromExtension(rawMessage);

    // Send an empty acknowledgement so the extension knows we're listening. If we don't reply
    //  then it may re-inject the content script.
    if (shouldSendResponse) {
      sendResponse(new AcknowledgementMessage());
    }
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Indicates the result of the most recent content analysis.
   * This is used to ensure we don't accidentally hide the page if the content has been allowed.
   * This always starts as "pending". It's changed to "allowed" or "blocked" when we receive the
   *  results of content analysis from the extension.
   */
  private _contentStatus: ContentStatus = ContentStatus.pending;

  /**
   * A HTML element we create on the page to hide content which is blocked or awaiting analysis.
   * This is created and destroyed on demand.
   */
  private _documentHider: HTMLDivElement | undefined;

  /**
   * The HTML ID of the _documentHider element we'll create.
   */
  private readonly _documentHiderId = 'smoothwall-protect';

  /**
   * This interval will be used to send content to the extension repeatedly until we get a response.
   * This is only needed for cases where we're speculatively sending content following document
   *  load. We don't know whether the extension is ready at that stage. We don't need to send
   *  repeatedly if the extension explicitly requested content.
   * This will be undefined if we're not repeatedly sending content.
   *
   * @note Do not set this to undefined without first passing it to clearInterval().
   */
  private _contentSendInterval: NodeJS.Timeout | undefined;

  /**
   * The body content mods which are currently enabled according to policy.
   * This maps the content mod ID to a function which implements it.
   * This is populated from policy decision each time content analysis is performed.
   * A content mod will only be executed if it is in this map, and its ID is not in
   *  _suppressedContentModIds.
   */
  private readonly _enabledContentMods = new Map<number, BodyContentMod>();

  /**
   * The IDs of body content mods which have failed or are not relevant to the current page.
   * This is stored separately from _enabledContentMods so that failed or irrelevant mods don't get
   *  re-enabled each time content analysis happens.
   */
  private readonly _suppressedContentModIds = new Set<number>();

  /**
   * Detects DOM changes so that content mods can be re-executed if necessary.
   * This will only be actively observing if we have content mods enabled.
   */
  private readonly _contentModMutationObserver = new MutationObserver(this._onDOMChanged);

  private _disableYoutubeScanning = false;

  /**
   * Timestamp (in milliseconds) of when the content mods were last executed.
   * This is used to avoid running them too frequently.
   */
  private _contentModsLastExecutedAt = 0;

  /*
   * Duration required before the content script can send page content again
   */
  private readonly _rescanThrottle = 250;

  private _canHandleYoutubeEvents = true;
  /*
   * Variable to keep track of whether youtube is being handled, to prevent multiple firings of method.
   */
  private _isHandlingYoutube = false;

  /*
   * Event name for triggering manipulation of the YouTube DOM.
   */
  private readonly _modifyYoutubeDOMEventName = 'modifyYoutubeDom';

  /*
   * A list of Youtube HTML element names that are to be removed as part of the Youtube handling
   * Includes elements for both standard and mobile/responsive layouts
   */
  private readonly _youtubeElementNames: string[] = [
    // Standard layout elements
    'ytd-playlist-header-renderer',
    'ytd-playlist-video-renderer',
    'ytd-rich-item-renderer',
    'ytd-compact-video-renderer',
    'ytd-comment-thread-renderer',
    'ytd-comment-view-model',
    'ytd-item-section-renderer',
    // Mobile/responsive layout elements
    'ytd-reel-item-renderer',
    'ytd-grid-video-renderer',
    'ytd-video-renderer',
    'ytd-compact-playlist-renderer',
    'ytd-shelf-renderer',
    'ytd-horizontal-card-list-renderer',
    'ytd-expanded-shelf-contents-renderer',
  ];

  /**
   * List of selectors for YouTube UI elements that should never be modified
   * These are critical UI elements that need to maintain their functionality
   */
  private readonly _youtubeProtectedSelectors: string[] = [
    // Restricted Mode UI elements
    '[role="dialog"][aria-label*="Restricted Mode"]',
    'a[href="#"][role="menuitem"][aria-label*="Restricted Mode"]',
    'paper-toggle-button[aria-label*="Activate Restricted Mode"]',
    'a[href="#"][aria-label="Back"]',
    // Menu items and navigation
    'a[href="#"][role="menuitem"]',
    'ytd-toggle-button-renderer',
    'yt-formatted-string[id="text"]',
    'tp-yt-paper-item',
    // Main player and controls
    'ytd-player',
    '.ytp-chrome-controls',
    '.ytp-right-controls',
    // Navigation elements
    'ytd-mini-guide-renderer',
    'ytd-guide-renderer',
    'ytd-masthead',
    // Settings and account menus
    '[role="dialog"]',
    '[role="menu"]',
  ];
}

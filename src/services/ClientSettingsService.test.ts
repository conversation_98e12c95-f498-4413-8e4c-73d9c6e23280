import 'test-helpers/chrome-api';
import MockBlobStorageService, { MockUrlData } from 'test-helpers/MockBlobStorageService';
import ClientSettingsService from './ClientSettingsService';
import IBlobStorageService from './IBlobStorageService';
import StorageService from './StorageService';
import TenantId from '../models/TenantId';
import MockTelemetryService from 'test-helpers/MockTelemetryService';

const tenant1Settings = {
  useAdvancedBlockpage: false,
  safeguardingAlertResource: 'https://example.com/api/safeguarding1',
  safeguardingAlertSecurityToken: 'token1',
};

const tenant2Settings = {
  useAdvancedBlockpage: true,
  safeguardingAlertResource: 'https://example.com/api/safeguarding2',
  safeguardingAlertSecurityToken: 'token2',
};

const untenantedSettings = {
  useAdvancedBlockpage: false,
  safeguardingAlertResource: 'https://example.com/api/safeguarding3',
  safeguardingAlertSecurityToken: 'token3',
  safeguardingGlobalToggle: false,
};

const tenantedSettingsJson = {
  tenantSettings: {
    '0e7e82fb-a26a-43cb-a87f-7dc289bbcdaa': {
      ...tenant1Settings,
    },
    '436b987c-a0d1-4267-8838-7bb178327432': {
      ...tenant2Settings,
    },
  },
  ...untenantedSettings,
};

const untenantedSettingsJson = {
  tenantSettings: {},
  ...untenantedSettings,
};

describe('ClientSettingsService', () => {
  const resource = 'https://exampleblob.com';
  const sas = '?sas=testsas';
  const blobUrl = resource + '{0}' + sas;

  const tenantedUrl = blobUrl.replace('{0}', '/tenanted');
  const untenantedUrl = blobUrl.replace('{0}', '/untenanted');

  const tenantedConfig = {
    resource,
    name: 'tenanted',
    sas,
  };

  const untenantedConfig = {
    resource,
    name: 'untenanted',
    sas,
  };

  // Not currently used in the tests.
  // const tenant1 = new TenantId('0e7e82fb-a26a-43cb-a87f-7dc289bbcdaa');
  const tenant2 = new TenantId('436b987c-a0d1-4267-8838-7bb178327432');

  const telemetryService = new MockTelemetryService();
  let mockBlobStorage: IBlobStorageService;
  let storageService: StorageService;

  beforeEach(() => {
    mockBlobStorage = new MockBlobStorageService([
      new MockUrlData(tenantedUrl, JSON.stringify(tenantedSettingsJson)),
      new MockUrlData(untenantedUrl, JSON.stringify(untenantedSettingsJson)),
    ]);

    storageService = new StorageService('clientSettings', undefined);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('default settings', () => {
    it('should return the default settings when the class is created', () => {
      const service = new ClientSettingsService(
        mockBlobStorage,
        storageService,
        telemetryService,
        undefined,
      );

      expect(service.useAdvancedBlockPage).toEqual(false);
      expect(service.safeguardingAlertResource).toEqual(undefined);
      expect(service.safeguardingAlertSecurityToken).toEqual(undefined);
    });

    it('should return default settings if the file cannot be loaded from storage', () => {
      const service = new ClientSettingsService(
        mockBlobStorage,
        storageService,
        telemetryService,
        undefined,
      );

      service.start({ resource: 'https://test.com', name: '', sas: '' });

      expect(service.useAdvancedBlockPage).toEqual(false);
      expect(service.safeguardingAlertResource).toEqual(undefined);
      expect(service.safeguardingAlertSecurityToken).toEqual(undefined);
    });

    it('should not trigger the clientSettingsLoaded event if the request fails', () => {
      const service = new ClientSettingsService(
        mockBlobStorage,
        storageService,
        telemetryService,
        undefined,
      );

      const listener = jest.fn();

      service.onClientSettingsLoaded.addListener(listener);

      service.start({ resource: 'https://test.com', name: '', sas: '' });

      expect(listener).not.toHaveBeenCalled();
    });
  });

  describe('start', () => {
    describe('Untenanted client', () => {
      it('should get the correct settings from a tenanted file', (done) => {
        const service = new ClientSettingsService(
          mockBlobStorage,
          storageService,
          telemetryService,
          undefined,
        );

        service.onClientSettingsLoaded.addListener(() => {
          try {
            expect(service.useAdvancedBlockPage).toEqual(untenantedSettings.useAdvancedBlockpage);
            expect(service.safeguardingAlertResource).toEqual(
              untenantedSettings.safeguardingAlertResource,
            );
            expect(service.safeguardingAlertSecurityToken).toEqual(
              untenantedSettings.safeguardingAlertSecurityToken,
            );
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(tenantedConfig);
      });

      it('should get the correct settings from a untenanted file', (done) => {
        const service = new ClientSettingsService(
          mockBlobStorage,
          storageService,
          telemetryService,
          undefined,
        );

        service.onClientSettingsLoaded.addListener(() => {
          try {
            expect(service.useAdvancedBlockPage).toEqual(untenantedSettings.useAdvancedBlockpage);
            expect(service.safeguardingAlertResource).toEqual(
              untenantedSettings.safeguardingAlertResource,
            );
            expect(service.safeguardingAlertSecurityToken).toEqual(
              untenantedSettings.safeguardingAlertSecurityToken,
            );
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(untenantedConfig);
      });
    });

    describe('Tenanted client', () => {
      it('should get the correct settings from a tenanted file', (done) => {
        const service = new ClientSettingsService(
          mockBlobStorage,
          storageService,
          telemetryService,
          tenant2,
        );

        service.onClientSettingsLoaded.addListener(() => {
          try {
            expect(service.useAdvancedBlockPage).toEqual(tenant2Settings.useAdvancedBlockpage);
            expect(service.safeguardingAlertResource).toEqual(
              tenant2Settings.safeguardingAlertResource,
            );
            expect(service.safeguardingAlertSecurityToken).toEqual(
              tenant2Settings.safeguardingAlertSecurityToken,
            );
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(tenantedConfig);
      });

      it('should get the correct settings from a untenanted file', (done) => {
        const service = new ClientSettingsService(
          mockBlobStorage,
          storageService,
          telemetryService,
          tenant2,
        );

        service.onClientSettingsLoaded.addListener(() => {
          try {
            expect(service.useAdvancedBlockPage).toEqual(untenantedSettings.useAdvancedBlockpage);
            expect(service.safeguardingAlertResource).toEqual(
              untenantedSettings.safeguardingAlertResource,
            );
            expect(service.safeguardingAlertSecurityToken).toEqual(
              untenantedSettings.safeguardingAlertSecurityToken,
            );
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(untenantedConfig);
      });

      it('should get the untenanted settings if no tenanted settings are found', (done) => {
        const service = new ClientSettingsService(
          mockBlobStorage,
          storageService,
          telemetryService,
          new TenantId('d10b6261-2f46-43ff-a0fd-1fc54cc97a8c'),
        );

        service.onClientSettingsLoaded.addListener(() => {
          try {
            expect(service.useAdvancedBlockPage).toEqual(untenantedSettings.useAdvancedBlockpage);
            expect(service.safeguardingAlertResource).toEqual(
              untenantedSettings.safeguardingAlertResource,
            );
            expect(service.safeguardingAlertSecurityToken).toEqual(
              untenantedSettings.safeguardingAlertSecurityToken,
            );
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(tenantedConfig);
      });
      it('should not error if the settings does not contain an tenant info', (done) => {
        mockBlobStorage = new MockBlobStorageService([
          new MockUrlData(tenantedUrl, JSON.stringify({ useAdvancedBlockpage: true })),
          new MockUrlData(untenantedUrl, JSON.stringify(untenantedSettingsJson)),
        ]);

        const service = new ClientSettingsService(
          mockBlobStorage,
          storageService,
          telemetryService,
          tenant2,
        );

        service.onClientSettingsLoaded.addListener(() => {
          try {
            // It should fall back to the untenanted setting.
            expect(service.useAdvancedBlockPage).toBeTrue();

            // The rest should be the default value as they aren't in the json.
            expect(service.safeguardingAlertResource).toBeUndefined();
            expect(service.safeguardingAlertSecurityToken).toBeUndefined();
            expect(service.safeguardingGlobalToggle).toBeTrue();
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(tenantedConfig);
      });
    });
  });
  describe('stop', () => {
    it('should return the default settings if the request is cancelled', () => {
      const service = new ClientSettingsService(
        mockBlobStorage,
        storageService,
        telemetryService,
        tenant2,
      );

      service.start(tenantedConfig);
      service.stop();

      expect(service.useAdvancedBlockPage).toEqual(false);
      expect(service.safeguardingAlertResource).toEqual(undefined);
      expect(service.safeguardingAlertSecurityToken).toEqual(undefined);
    });

    it('should not trigger the clientSettingsLoaded event if the request is cancelled', () => {
      const service = new ClientSettingsService(
        mockBlobStorage,
        storageService,
        telemetryService,
        tenant2,
      );

      const listener = jest.fn();

      service.onClientSettingsLoaded.addListener(listener);

      service.start(tenantedConfig);
      service.stop();

      expect(listener).not.toHaveBeenCalled();
    });
  });
});

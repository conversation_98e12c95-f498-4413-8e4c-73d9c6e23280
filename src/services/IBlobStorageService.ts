import BlobDownloadResult from 'models/BlobDownloadResult';
import FileDownloadType from 'models/FileDownloadType';
import StandaloneEvent from 'utilities/StandaloneEvent';

/**
 * Manages downloading files from a cloud blob storage.
 *
 * A new instance of the class should be used for each different request so they can be managed separately.
 */
export default interface IBlobStorageService {
  /**
   * Downloads the file from the given url and reads it as the fileType.
   *
   * Emits the onDownloadSuccess if the download completes sucessfully. Otherwise it emits onDownloadFailed.
   * @param url The url to the file
   * @param fileType The type of file that is being downloaded
   * @param etag Optional etag to set If-None-Match request header
   */
  start: (url: string, fileType: FileDownloadType, etag?: string) => void;

  /**
   * Aborts all active connections to blob storage for this instance.
   */
  stop: () => void;

  /**
   * This event is dispatched when the file is successfully downloaded.
   */
  onDownloadSuccess: StandaloneEvent<[BlobDownloadResult]>;

  /**
   * This event is dispatched when there is an error downloading the file.
   */
  onDownloadFailed: StandaloneEvent<[BlobDownloadResult]>;

  /**
   * This event is dispatched when a sucessful download generates an etag.
   */
  onETag: StandaloneEvent<[string]>;

  /**
   * Indicates if the service is currently downloading from blob storage.
   */
  isDownloading: boolean;
}

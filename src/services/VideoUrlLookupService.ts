/**
 * Provides a central in memory service to store and manage video id related urls and their respective
 * tabs/frames. Urls are stored against string keys in the format {`${tabid}/${frameid}`: urlValue}.
 * FYI frameid 0 denotes the parent/original tab.
 */
export default class VideoUrlLookupService {
  /**
   * Save a tab/frame key and videoid value to the video Id lookup cache
   *
   * @param tabId The tab id of the video url
   * @param frameId The frame id of the video url
   * @param videoIds A string storing video url
   */
  public saveVideoUrlToLookup = (tabId: number, frameId: number, videoUrl: string): void => {
    if (videoUrl !== '') {
      const tab = this._videoUrlLookup.get(tabId);
      if (tab === undefined) {
        this._videoUrlLookup.set(tabId, new Map<number, string>());
        this._videoUrlLookup.get(tabId)?.set(frameId, videoUrl);
      } else {
        tab.set(frameId, videoUrl);
      }
    }
  };

  /**
   * Get tab/frame video url
   * @param tabId The tab id of the video url
   * @param frameId The frame id of the video url
   */
  public getVideoUrlForTabFrame = (tabId: number, frameId: number): string | undefined => {
    return this._videoUrlLookup.get(tabId)?.get(frameId);
  };

  /**
   * Get all video ids for a given tab
   * @param tabId The tab id to be fetched
   */
  public getVideoUrlsForTabId = (tabId: number): Map<number, string> | undefined => {
    return this._videoUrlLookup.get(tabId);
  };

  /**
   * Remove all video ids for a given tab
   * @param tabId the tab id to be cleared
   */
  public removeVideoUrlsForTabId = (tabId: number): void => {
    this._videoUrlLookup.delete(tabId);
  };

  /**
   * Remove tab/frame from lookup
   * @param tabId The tab id of the video url
   * @param frameId The frame id of the video url
   */
  public removeTabFrameFromLookup = (tabId: number, frameId: number): boolean => {
    return this._videoUrlLookup.get(tabId)?.delete(frameId) ?? false;
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * A map where video ids are stored against their parent tabs and frames
   */
  private readonly _videoUrlLookup: Map<number, Map<number, string>> = new Map<
    number,
    Map<number, string>
  >();
}

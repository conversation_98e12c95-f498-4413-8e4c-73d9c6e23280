import 'test-helpers/chrome-api';
import MockBlobStorageService, { MockUrlData } from 'test-helpers/MockBlobStorageService';
import IBlobStorageService from './IBlobStorageService';
import TenantId from '../models/TenantId';
import PolicyDownloadService from './PolicyDownloadService';
import { IPolicyConfig } from 'models/PolicyConfigModels';
import { getTestPolicyConfig } from 'test-helpers/test-policy-config';

const tenant = new TenantId('f417a2c4-f99c-11ea-8caa-eb014c4bbe3b');

const policyJson = getTestPolicyConfig(tenant);

describe('PolicyDownloadService', () => {
  const resource = 'https://exampleblob.com';
  const sas = '?sas=testsas';
  const blobUrl = resource + '{0}' + sas;

  const policy1Url = blobUrl.replace('{0}', '/policy1');

  let mockBlobStorage: IBlobStorageService;

  beforeEach(() => {
    mockBlobStorage = new MockBlobStorageService([
      new MockUrlData(policy1Url, JSON.stringify(policyJson)),
    ]);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('start', () => {
    it('should load the blockpages', (done) => {
      const service = new PolicyDownloadService(mockBlobStorage, undefined);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          expect(result?.blockpages).toEqual(policyJson.blockpages);
          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should load the cloud filter settings', (done) => {
      const service = new PolicyDownloadService(mockBlobStorage, undefined);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          expect(result?.cloud_filter).toEqual(policyJson.cloud_filter);
          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should load the content mod policies', (done) => {
      const service = new PolicyDownloadService(mockBlobStorage, undefined);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          expect(result?.content_modification).toEqual(policyJson.content_modification);
          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should load the custom content mods', (done) => {
      const service = new PolicyDownloadService(mockBlobStorage, undefined);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          expect(result?.custom_content_modifiers).toEqual(policyJson.custom_content_modifiers);
          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should load the default content mods', (done) => {
      const service = new PolicyDownloadService(mockBlobStorage, undefined);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          expect(result?.default_content_modifiers).toEqual(policyJson.default_content_modifiers);
          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should load the google group users', (done) => {
      const service = new PolicyDownloadService(mockBlobStorage, undefined);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          expect(result?.google_group_users).toEqual(policyJson.google_group_users);
          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should load the groups', (done) => {
      const service = new PolicyDownloadService(mockBlobStorage, undefined);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          expect(result?.groups).toEqual(policyJson.groups);
          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should load the group mapping', (done) => {
      const service = new PolicyDownloadService(mockBlobStorage, undefined);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          expect(result?.group_mapping).toEqual(policyJson.group_mapping);
          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should load the locations', (done) => {
      // we need to pass the tenant to get all locations, otherwise it will only return the global locations
      const service = new PolicyDownloadService(mockBlobStorage, tenant);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          expect(result?.locations).toEqual(policyJson.locations);
          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should load the metadata', (done) => {
      const service = new PolicyDownloadService(mockBlobStorage, undefined);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          expect(result?.metadata).toEqual(policyJson.metadata);
          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should load the quotas', (done) => {
      const service = new PolicyDownloadService(mockBlobStorage, undefined);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          expect(result?.quotas).toEqual(policyJson.quotas);
          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should load the tenants', (done) => {
      const service = new PolicyDownloadService(mockBlobStorage, undefined);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          expect(result?.tenants).toEqual(policyJson.tenants);
          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should load the users', (done) => {
      const service = new PolicyDownloadService(mockBlobStorage, undefined);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          expect(result?.users).toEqual(policyJson.users);
          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should load the flattened policies in the correct order', (done) => {
      const service = new PolicyDownloadService(mockBlobStorage, tenant);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          let lastOrder = 0;

          for (const policy of result?.flattenedPolicies ?? []) {
            expect(parseFloat(policy.order)).toBeGreaterThan(lastOrder);

            lastOrder = parseFloat(policy.order);
          }
          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    describe('untenanted', () => {
      it('should load the untenanted banned users', (done) => {
        const service = new PolicyDownloadService(mockBlobStorage, undefined);

        service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
          try {
            expect(result?.banned).toBeArrayOfSize(4);
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(policy1Url);
      });

      it('should load the untenanted category filter groups', (done) => {
        const service = new PolicyDownloadService(mockBlobStorage, undefined);

        service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
          try {
            expect(result?.category_filter_groups).toEqual([
              {
                source: ['1', '2', '30'],
                comment: 'Inappropriate content',
                tenant: 'global',
                id: '5AFCC930-A80F-11EB-A051-0182081D14AE',
                name: 'Core Blocked Content',
              },
            ]);
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(policy1Url);
      });

      it('should load the untenanted cloud content mods', (done) => {
        const service = new PolicyDownloadService(mockBlobStorage, undefined);

        service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
          try {
            expect(result?.cloud_content_modifications).toEqual([
              {
                id: '123',
                tenantId: 'global',
                filterType: 'include',
                groups: [],
                locations: [],
                blockmanId: '12',
                enabled: true,
              },
            ]);
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(policy1Url);
      });

      it('should load the untenanted custom categories', (done) => {
        const service = new PolicyDownloadService(mockBlobStorage, undefined);

        service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
          try {
            expect(result?.custom_categories).toEqual([
              {
                id: '5AD38700-A80F-11EB-A051-0182081D14AE',
                category_id: '546',
                from_blocklist: '1',
                tenant: 'global',
              },
              {
                id: '61E8BB25-CFD2-4A94-9DE8-0E2FA648EF17',
                category_id: '61e8bb25-cfd2-4a94-9de8-0e2fa648ef17',
                href: '1',
                custom_content: '1',
                name: 'Custom Category',
                description: '',
                tenant: 'global',
                component: {
                  domainsurls: ['custom.com'],
                  searchterms: [
                    'bbbbb',
                    'search test',
                    '[test]',
                    '[multiple][term][test]',
                    '(another)(test)',
                    '|pipe|',
                    '|single||delimiter||test|',
                  ],
                },
              },
              {
                id: '6570637d-09af-4d2a-84bb-e465f8f258a1',
                category_id: '6570637d-09af-4d2a-84bb-e465f8f258a1',
                href: '1',
                custom_content: '1',
                name: 'Custom Category Subdomain',
                description: '',
                tenant: 'global',
                component: {
                  domainsurls: ['test.custom.com'],
                },
              },
              {
                id: '4d8ab083-5404-439e-bbb5-49a77de2a5b9',
                category_id: '4d8ab083-5404-439e-bbb5-49a77de2a5b9',
                href: '1',
                custom_content: '1',
                name: 'Custom Category Path',
                description: '',
                tenant: 'global',
                component: {
                  domainsurls: ['custom.com/path'],
                },
              },
              {
                id: 'a9baee13-d068-4894-85d6-fa35f7b656f8',
                category_id: 'a9baee13-d068-4894-85d6-fa35f7b656f8',
                href: '1',
                custom_content: '1',
                name: 'Custom Category Uppercase',
                description: '',
                tenant: 'global',
                component: {
                  domainsurls: ['TesT.EXample.com/UPPERcasetest'],
                },
              },
            ]);
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(policy1Url);
      });

      it('should load untenanted search terms', (done) => {
        const service = new PolicyDownloadService(mockBlobStorage, undefined);

        service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
          try {
            expect(result?.customSearchTerms).toEqual({
              another: {
                catsAndScores: [],
                children: [
                  {
                    catsAndScores: {
                      category: '61e8bb25-cfd2-4a94-9de8-0e2fa648ef17',
                      score: 100,
                    },
                    phrases: ['test'],
                  },
                ],
              },
              'earch est': {
                catsAndScores: [
                  {
                    category: '61e8bb25-cfd2-4a94-9de8-0e2fa648ef17',
                    score: 100,
                  },
                ],
                children: [],
              },
              multiple: {
                catsAndScores: [],
                children: [
                  {
                    catsAndScores: {
                      category: '61e8bb25-cfd2-4a94-9de8-0e2fa648ef17',
                      score: 100,
                    },
                    phrases: ['term', 'test'],
                  },
                ],
              },
              pipe: {
                catsAndScores: [
                  {
                    category: '61e8bb25-cfd2-4a94-9de8-0e2fa648ef17',
                    score: 100,
                  },
                ],
                children: [],
              },
              single: {
                catsAndScores: [],
                children: [
                  {
                    catsAndScores: {
                      category: '61e8bb25-cfd2-4a94-9de8-0e2fa648ef17',
                      score: 100,
                    },
                    phrases: ['delimiter', 'test'],
                  },
                ],
              },
              test: {
                catsAndScores: [
                  {
                    category: '61e8bb25-cfd2-4a94-9de8-0e2fa648ef17',
                    score: 100,
                  },
                ],
                children: [],
              },
            });

            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(policy1Url);
      });

      it('should load the untenanted flattened policies', (done) => {
        const service = new PolicyDownloadService(mockBlobStorage, undefined);

        service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
          try {
            expect(result?.flattenedPolicies).toEqual([
              {
                what: ['5AD8661C-A80F-11EB-A051-0182081D14AE'],
                when: ['Anytime'],
                who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
                where: ['E78D6F86-A908-11EB-B44E-9C78081D14AE'],
                order: '1.1',
                id: 'F8F985F8-B7B7-11EB-8AB1-9C78081D14AE',
                name: 'Default Users - Archive Filetypes - Outside Premises',
                action: '0',
                enabled: 'on',
              },
              {
                what: ['5AD8661C-A80F-11EB-A051-0182081D14AE'],
                when: ['Anytime'],
                who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
                where: ['E78D6F86-A908-11EB-B44E-9C78081D14AE'],
                order: '1.2',
                id: 'F8F985F8-B7B7-11EB-8AB1-9C78081D14AE',
                name: 'Default Users - Archive Filetypes - Outside Premises',
                action: '0',
                enabled: 'on',
              },
              {
                what: ['5AD8661C-A80F-11EB-A051-0182081D14AE'],
                when: ['CHILD'],
                who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
                where: ['E78D6F86-A908-11EB-B44E-9C78081D14AE'],
                order: '5',
                id: 'F8F985F8-B7B7-11EB-8AB1-9C78081D14AE',
                name: 'Default Users - Archive Filetypes - Outside Premises',
                action: '0',
                enabled: 'on',
              },
              {
                what: ['8502DCB8-F99D-11EA-A202-DBDB4B4BBE3B'],
                when: ['BD358D22-F995-11EA-A4C0-DBDB4B4BBE3B'],
                who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
                where: ['Everywhere'],
                order: '6',
                id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
                name: 'Default Users - Amazon Prime',
                action: '0',
                enabled: 'on',
              },
              {
                what: ['61E8BB25-CFD2-4A94-9DE8-0E2FA648EF17'],
                when: ['Anytime'],
                who: ['Everyone'],
                where: ['Everywhere'],
                order: '7',
                id: 'CD8A5B9F-C74D-4D26-B36A-0A34941B2BAF',
                name: 'Block custom category',
                action: '0',
                enabled: 'on',
              },
            ]);
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(policy1Url);
      });

      it('should load the untenanted policies', (done) => {
        const service = new PolicyDownloadService(mockBlobStorage, undefined);

        service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
          try {
            expect(result?.policies).toEqual({
              before: [
                {
                  what: ['PARENT'],
                  when: ['Anytime'],
                  who: ['PARENT'],
                  where: ['PARENT'],
                  order: '1',
                  id: 'F8F8B15A-B7B7-11EB-8AB1-9C78081D14AE',
                  name: 'Always',
                  action: 'PARENT',
                  enabled: 'on',
                  children: [
                    {
                      what: ['5AD8661C-A80F-11EB-A051-0182081D14AE'],
                      when: ['Anytime'],
                      who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
                      where: ['E78D6F86-A908-11EB-B44E-9C78081D14AE'],
                      order: '1.1',
                      id: 'F8F985F8-B7B7-11EB-8AB1-9C78081D14AE',
                      name: 'Default Users - Archive Filetypes - Outside Premises',
                      action: '0',
                      enabled: 'on',
                    },
                    {
                      what: ['5AD8661C-A80F-11EB-A051-0182081D14AE'],
                      when: ['Anytime'],
                      who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
                      where: ['E78D6F86-A908-11EB-B44E-9C78081D14AE'],
                      order: '1.2',
                      id: 'F8F985F8-B7B7-11EB-8AB1-9C78081D14AE',
                      name: 'Default Users - Archive Filetypes - Outside Premises',
                      action: '0',
                      enabled: 'on',
                    },
                  ],
                },
                {
                  what: ['Disabled'],
                  when: ['Disabled'],
                  who: ['Disabled'],
                  where: ['Disabled'],
                  order: '2',
                  id: 'Disabled',
                  name: 'Disabled',
                  action: '0',
                  enabled: '',
                },
              ],
              tenants: {},
              after: [
                {
                  what: ['8502DCB8-F99D-11EA-A202-DBDB4B4BBE3B'],
                  when: ['BD358D22-F995-11EA-A4C0-DBDB4B4BBE3B'],
                  who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
                  where: ['Everywhere'],
                  order: '6',
                  id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
                  name: 'Default Users - Amazon Prime',
                  action: '0',
                  enabled: 'on',
                },
                {
                  what: ['5AD8661C-A80F-11EB-A051-0182081D14AE'],
                  when: ['CHILD'],
                  who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
                  where: ['E78D6F86-A908-11EB-B44E-9C78081D14AE'],
                  order: '5',
                  id: 'F8F985F8-B7B7-11EB-8AB1-9C78081D14AE',
                  name: 'Default Users - Archive Filetypes - Outside Premises',
                  action: '0',
                  enabled: 'on',
                },
                {
                  what: ['61E8BB25-CFD2-4A94-9DE8-0E2FA648EF17'],
                  when: ['Anytime'],
                  who: ['Everyone'],
                  where: ['Everywhere'],
                  order: '7',
                  id: 'CD8A5B9F-C74D-4D26-B36A-0A34941B2BAF',
                  name: 'Block custom category',
                  action: '0',
                  enabled: 'on',
                },
              ],
              locations: [],
            });
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(policy1Url);
      });

      it('should load the untenanted time slots', (done) => {
        const service = new PolicyDownloadService(mockBlobStorage, undefined);

        service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
          try {
            expect(result?.time_slots).toEqual([
              {
                id: '5A9BAE5C-A80F-11EB-A051-0182081D14AE',
                name: 'Lunchtime',
                comment: 'Weekday lunch period',
                tenant: 'global',
                times: [
                  ['129600', '133199'],
                  ['216000', '219599'],
                  ['302400', '305999'],
                  ['388800', '392399'],
                  ['475200', '478799'],
                ],
              },
            ]);
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(policy1Url);
      });

      it('should load the untenanted locations', (done) => {
        const service = new PolicyDownloadService(mockBlobStorage, undefined);

        // we expect to see only see locations that are not tenant specific (global)
        service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
          try {
            expect(result?.locations).toEqual([
              {
                exceptions: ['*******'],
                id: 'BE5B0C8F-892E-470E-BC0B-1A34C6D659AA',
                name: 'Monte test',
                sources: [],
                tenant: 'global',
              },
              {
                exceptions: ['***************'],
                id: 'D440CC1C-2F61-4FF2-A4E9-34BCC10A0ECF',
                name: 'z',
                sources: [],
                tenant: 'global',
              },
            ]);
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(policy1Url);
      });
    });

    describe('tenanted', () => {
      it('should load the tenanted banned users', (done) => {
        const service = new PolicyDownloadService(mockBlobStorage, tenant);

        service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
          try {
            expect(result?.banned).toEqual(policyJson.banned);
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(policy1Url);
      });

      it('should load the tenanted category filter groups', (done) => {
        const service = new PolicyDownloadService(mockBlobStorage, tenant);

        service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
          try {
            expect(result?.category_filter_groups).toEqual(policyJson.category_filter_groups);
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(policy1Url);
      });

      it('should load the tenanted cloud content mods', (done) => {
        const service = new PolicyDownloadService(mockBlobStorage, tenant);

        service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
          try {
            expect(result?.cloud_content_modifications).toEqual([
              {
                id: '123',
                tenantId: 'global',
                filterType: 'include',
                groups: [],
                locations: [],
                blockmanId: '12',
                enabled: true,
              },
              {
                id: '456',
                tenantId: tenant.toString(),
                filterType: 'include',
                groups: [],
                locations: [],
                blockmanId: '34',
                enabled: true,
              },
            ]);
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(policy1Url);
      });

      it('should load the tenanted custom categories', (done) => {
        const service = new PolicyDownloadService(mockBlobStorage, tenant);

        service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
          try {
            expect(result?.custom_categories).toEqual(policyJson.custom_categories);
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(policy1Url);
      });

      it('should load tenanted search terms', (done) => {
        const service = new PolicyDownloadService(mockBlobStorage, tenant);

        service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
          try {
            expect(result?.customSearchTerms).toEqual({
              another: {
                catsAndScores: [],
                children: [
                  {
                    catsAndScores: {
                      category: '61e8bb25-cfd2-4a94-9de8-0e2fa648ef17',
                      score: 100,
                    },
                    phrases: ['test'],
                  },
                ],
              },
              'earch est': {
                catsAndScores: [
                  {
                    category: '61e8bb25-cfd2-4a94-9de8-0e2fa648ef17',
                    score: 100,
                  },
                ],
                children: [],
              },
              es: {
                catsAndScores: [
                  {
                    category: '321',
                    score: 100,
                  },
                ],
                children: [],
              },
              multi: {
                catsAndScores: [],
                children: [
                  {
                    catsAndScores: {
                      category: '321',
                      score: 100,
                    },
                    phrases: ['tenant', 'test'],
                  },
                ],
              },
              multiple: {
                catsAndScores: [],
                children: [
                  {
                    catsAndScores: {
                      category: '61e8bb25-cfd2-4a94-9de8-0e2fa648ef17',
                      score: 100,
                    },
                    phrases: ['term', 'test'],
                  },
                ],
              },
              pipe: {
                catsAndScores: [
                  {
                    category: '61e8bb25-cfd2-4a94-9de8-0e2fa648ef17',
                    score: 100,
                  },
                ],
                children: [],
              },
              single: {
                catsAndScores: [],
                children: [
                  {
                    catsAndScores: {
                      category: '61e8bb25-cfd2-4a94-9de8-0e2fa648ef17',
                      score: 100,
                    },
                    phrases: ['delimiter', 'test'],
                  },
                ],
              },
              tenant: {
                catsAndScores: [
                  {
                    category: '321',
                    score: 100,
                  },
                ],
                children: [],
              },
              tenanted: {
                catsAndScores: [],
                children: [
                  {
                    catsAndScores: {
                      category: '321',
                      score: 100,
                    },
                    phrases: ['test'],
                  },
                ],
              },
              test: {
                catsAndScores: [
                  {
                    category: '61e8bb25-cfd2-4a94-9de8-0e2fa648ef17',
                    score: 100,
                  },
                ],
                children: [],
              },
            });

            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(policy1Url);
      });
      it('should load the tenanted flattened policies', (done) => {
        const service = new PolicyDownloadService(mockBlobStorage, tenant);

        service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
          try {
            expect(result?.flattenedPolicies).toEqual([
              {
                what: ['5AD8661C-A80F-11EB-A051-0182081D14AE'],
                when: ['Anytime'],
                who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
                where: ['E78D6F86-A908-11EB-B44E-9C78081D14AE'],
                order: '1.1',
                id: 'F8F985F8-B7B7-11EB-8AB1-9C78081D14AE',
                name: 'Default Users - Archive Filetypes - Outside Premises',
                action: '0',
                enabled: 'on',
              },
              {
                what: ['5AD8661C-A80F-11EB-A051-0182081D14AE'],
                when: ['Anytime'],
                who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
                where: ['E78D6F86-A908-11EB-B44E-9C78081D14AE'],
                order: '1.2',
                id: 'F8F985F8-B7B7-11EB-8AB1-9C78081D14AE',
                name: 'Default Users - Archive Filetypes - Outside Premises',
                action: '0',
                enabled: 'on',
              },
              {
                what: ['854154DE-F99D-11EA-A202-DBDB4B4BBE3B'],
                when: ['Anytime'],
                who: ['Everyone'],
                where: ['Everywhere'],
                order: '3',
                id: '86584436-F99D-11EA-A202-DBDB4B4BBE3B',
                name: 'Everyone - Abortion',
                action: '0',
                enabled: 'on',
              },
              {
                what: ['5AD8661C-A80F-11EB-A051-0182081D14AE'],
                when: ['CHILD'],
                who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
                where: ['E78D6F86-A908-11EB-B44E-9C78081D14AE'],
                order: '5',
                id: 'F8F985F8-B7B7-11EB-8AB1-9C78081D14AE',
                name: 'Default Users - Archive Filetypes - Outside Premises',
                action: '0',
                enabled: 'on',
              },
              {
                what: ['8502DCB8-F99D-11EA-A202-DBDB4B4BBE3B'],
                when: ['BD358D22-F995-11EA-A4C0-DBDB4B4BBE3B'],
                who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
                where: ['Everywhere'],
                order: '6',
                id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
                name: 'Default Users - Amazon Prime',
                action: '0',
                enabled: 'on',
              },
              {
                what: ['61E8BB25-CFD2-4A94-9DE8-0E2FA648EF17'],
                when: ['Anytime'],
                who: ['Everyone'],
                where: ['Everywhere'],
                order: '7',
                id: 'CD8A5B9F-C74D-4D26-B36A-0A34941B2BAF',
                name: 'Block custom category',
                action: '0',
                enabled: 'on',
              },
            ]);
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(policy1Url);
      });

      it('should load the tenanted policies', (done) => {
        const service = new PolicyDownloadService(mockBlobStorage, tenant);

        service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
          try {
            expect(result?.policies).toEqual({
              before: [
                {
                  what: ['PARENT'],
                  when: ['Anytime'],
                  who: ['PARENT'],
                  where: ['PARENT'],
                  order: '1',
                  id: 'F8F8B15A-B7B7-11EB-8AB1-9C78081D14AE',
                  name: 'Always',
                  action: 'PARENT',
                  enabled: 'on',
                  children: [
                    {
                      what: ['5AD8661C-A80F-11EB-A051-0182081D14AE'],
                      when: ['Anytime'],
                      who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
                      where: ['E78D6F86-A908-11EB-B44E-9C78081D14AE'],
                      order: '1.1',
                      id: 'F8F985F8-B7B7-11EB-8AB1-9C78081D14AE',
                      name: 'Default Users - Archive Filetypes - Outside Premises',
                      action: '0',
                      enabled: 'on',
                    },
                    {
                      what: ['5AD8661C-A80F-11EB-A051-0182081D14AE'],
                      when: ['Anytime'],
                      who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
                      where: ['E78D6F86-A908-11EB-B44E-9C78081D14AE'],
                      order: '1.2',
                      id: 'F8F985F8-B7B7-11EB-8AB1-9C78081D14AE',
                      name: 'Default Users - Archive Filetypes - Outside Premises',
                      action: '0',
                      enabled: 'on',
                    },
                  ],
                },
                {
                  what: ['Disabled'],
                  when: ['Disabled'],
                  who: ['Disabled'],
                  where: ['Disabled'],
                  order: '2',
                  id: 'Disabled',
                  name: 'Disabled',
                  action: '0',
                  enabled: '',
                },
              ],
              tenants: {
                'f417a2c4-f99c-11ea-8caa-eb014c4bbe3b': [
                  {
                    what: ['854154DE-F99D-11EA-A202-DBDB4B4BBE3B'],
                    when: ['Anytime'],
                    who: ['Everyone'],
                    where: ['Everywhere'],
                    order: '3',
                    id: '86584436-F99D-11EA-A202-DBDB4B4BBE3B',
                    name: 'Everyone - Abortion',
                    action: '0',
                    enabled: 'on',
                  },
                ],
              },
              after: [
                {
                  what: ['8502DCB8-F99D-11EA-A202-DBDB4B4BBE3B'],
                  when: ['BD358D22-F995-11EA-A4C0-DBDB4B4BBE3B'],
                  who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
                  where: ['Everywhere'],
                  order: '6',
                  id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
                  name: 'Default Users - Amazon Prime',
                  action: '0',
                  enabled: 'on',
                },
                {
                  what: ['5AD8661C-A80F-11EB-A051-0182081D14AE'],
                  when: ['CHILD'],
                  who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
                  where: ['E78D6F86-A908-11EB-B44E-9C78081D14AE'],
                  order: '5',
                  id: 'F8F985F8-B7B7-11EB-8AB1-9C78081D14AE',
                  name: 'Default Users - Archive Filetypes - Outside Premises',
                  action: '0',
                  enabled: 'on',
                },
                {
                  what: ['61E8BB25-CFD2-4A94-9DE8-0E2FA648EF17'],
                  when: ['Anytime'],
                  who: ['Everyone'],
                  where: ['Everywhere'],
                  order: '7',
                  id: 'CD8A5B9F-C74D-4D26-B36A-0A34941B2BAF',
                  name: 'Block custom category',
                  action: '0',
                  enabled: 'on',
                },
              ],
              locations: [],
            });
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(policy1Url);
      });

      it('should load the tenanted time slots', (done) => {
        const service = new PolicyDownloadService(mockBlobStorage, tenant);

        service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
          try {
            expect(result?.time_slots).toEqual(policyJson.time_slots);
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(policy1Url);
      });

      it('should load the tenanted locations', (done) => {
        const service = new PolicyDownloadService(mockBlobStorage, tenant);

        service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
          try {
            expect(result?.locations).toEqual([
              {
                sources: [],
                exceptions: ['*******'],
                id: 'BE5B0C8F-892E-470E-BC0B-1A34C6D659AA',
                name: 'Monte test',
                tenant: 'global',
              },
              {
                sources: [],
                exceptions: ['***************'],
                id: 'D440CC1C-2F61-4FF2-A4E9-34BCC10A0ECF',
                name: 'z',
                tenant: 'global',
              },
              {
                sources: [],
                exceptions: ['*******'],
                id: 'a0c49851-ddb2-459d-9368-e7c17ae2d802',
                name: 'Tenanted Location',
                tenant: tenant.toString(),
              },
            ]);
            done();
          } catch (error) {
            done(error);
          }
        });

        service.start(policy1Url);
      });
    });
  });
});

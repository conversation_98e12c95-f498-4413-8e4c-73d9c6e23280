import OffscreenDocumentController from 'controllers/OffscreenDocumentController';
import IpService from './IpService';
import StorageService from './StorageService';
import { ILocation, IPolicyConfig } from 'models/PolicyConfigModels';
import PolicyService from 'services/PolicyService';

const exampleOutsidePremisesLocation: ILocation = {
  id: 'xyzzy',
  name: 'outsidepremises',
  exceptions: ['*********/8', '***************/8'],
};

const exampleOnPremisesLocation: ILocation = {
  id: 'foo',
  name: 'Somewhere',
  sources: ['**********/16', '************/8', '************-************'],
  exceptions: ['********/32', '*********/24', '*********-********'],
};

const examplePolicyWithOutsidePremisesLocation = {
  locations: [exampleOutsidePremisesLocation],
} as unknown as IPolicyConfig;

describe('IpService', () => {
  let offscreenDocumentController: OffscreenDocumentController;
  let storageService: StorageService;
  let ipService: IpService;
  let odcSpies: {
    start: jest.SpyInstance;
    stop: jest.SpyInstance;
    sendMessage: jest.SpyInstance;
  };

  beforeAll(() => {
    jest.useFakeTimers();
  });

  beforeEach(() => {
    offscreenDocumentController = new OffscreenDocumentController();
    storageService = new StorageService('test', undefined);
    ipService = new IpService(offscreenDocumentController, storageService);

    // Prevent the actual offscreen document functions from being called as they will fail outside
    //  a real extension environment.
    odcSpies = {
      start: jest.spyOn(offscreenDocumentController, 'start').mockResolvedValue(undefined),
      stop: jest.spyOn(offscreenDocumentController, 'stop').mockResolvedValue(undefined),
      sendMessage: jest
        .spyOn(offscreenDocumentController, 'sendMessage')
        .mockResolvedValue(undefined),
    };

    // Suppress debug messages in the console.
    jest.spyOn(console, 'debug').mockImplementation(() => {});

    // Mock the Chrome network details functions.
    (chrome as any).enterprise = {
      networkingAttributes: {
        getNetworkDetails: (callback: (networkDetails: object) => void) => {
          callback({
            macAddress: 'xyzzy',
            ipv4: '*******',
            ipv6: '::1',
          });
        },
      },
    };
  });

  afterAll(() => {
    // Get rid of the mocked Chrome networking functions.
    delete (chrome as any).enterprise;

    jest.useRealTimers();
  });

  // -----------------------------------------------------------------------------------------------

  describe('privateIpAddresses', () => {
    it('returns an empty array if no private IP addresses are stored', () => {
      expect(ipService.privateIpAddresses).toBeArrayOfSize(0);
    });

    it('returns an array containing the stored private IP addresses', () => {
      storageService.set('privateIpAddresses', ['*******', '***************']);
      expect(ipService.privateIpAddresses).toBeArrayOfSize(2);
      expect(ipService.privateIpAddresses).toContain('*******');
      expect(ipService.privateIpAddresses).toContain('***************');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('privateIpAddressesUpdatedAt', () => {
    it('returns undefined if the private addresses have never been updated', () => {
      expect(ipService.privateIpAddressesUpdatedAt).toBeUndefined();
    });

    it('returns the millisecond timestamp specifying when the private addresses were last updated', () => {
      storageService.set('privateIpAddressesUpdatedAt', 1681812868291);
      expect(ipService.privateIpAddressesUpdatedAt).toEqual(1681812868291);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('publicIpAddresses', () => {
    it('returns an empty array if no public IP addresses are stored', () => {
      expect(ipService.publicIpAddresses).toBeArrayOfSize(0);
    });

    it('returns an array containing the stored public IP addresses', () => {
      storageService.set('publicIpAddresses', ['*******', '***************']);
      expect(ipService.publicIpAddresses).toBeArrayOfSize(2);
      expect(ipService.publicIpAddresses).toContain('*******');
      expect(ipService.publicIpAddresses).toContain('***************');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('publicIpAddressesUpdatedAt', () => {
    it('returns undefined if the public addresses have never been updated', () => {
      expect(ipService.publicIpAddressesUpdatedAt).toBeUndefined();
    });

    it('returns the millisecond timestamp specifying when the public addresses were last updated', () => {
      storageService.set('publicIpAddressesUpdatedAt', 1681812868291);
      expect(ipService.publicIpAddressesUpdatedAt).toEqual(1681812868291);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('hasRecentPublicIpAddresses', () => {
    it('returns false if no public IP address has been stored', () => {
      expect(ipService.hasRecentPublicIpAddresses).toBeFalse();
    });

    it('returns true if a public IP address was stored within the last 4 hours', () => {
      storageService.set('publicIpAddresses', ['*******']);
      storageService.set('publicIpAddressesUpdatedAt', Date.now() - 13500000);
      expect(ipService.hasRecentPublicIpAddresses).toBeTrue();
    });

    it('returns false if the public IP address was last stored more than 4 hours ago', () => {
      storageService.set('publicIpAddresses', ['*******']);
      storageService.set('publicIpAddressesUpdatedAt', Date.now() - 15300000);
      expect(ipService.hasRecentPublicIpAddresses).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('getAllIpAddresses', () => {
    it('returns an empty array if no IP addresses have been stored', () => {
      expect(ipService.getAllIpAddresses()).toBeArrayOfSize(0);
    });

    it('returns an array containing all stored IP addresses', () => {
      storageService.set('privateIpAddresses', ['*******', '*******']);
      storageService.set('publicIpAddresses', ['*******', '*******']);
      storageService.set('publicIpAddressesUpdatedAt', Date.now());

      const output = ipService.getAllIpAddresses();
      expect(output).toBeArrayOfSize(4);
      expect(output).toContain('*******');
      expect(output).toContain('*******');
      expect(output).toContain('*******');
      expect(output).toContain('*******');
    });

    it('omits public IP addresses if they are more than 4 hours old', () => {
      storageService.set('privateIpAddresses', ['*******', '*******']);
      storageService.set('publicIpAddresses', ['*******', '*******']);
      storageService.set('publicIpAddressesUpdatedAt', Date.now() - 15300000);

      const output = ipService.getAllIpAddresses();
      expect(output).toBeArrayOfSize(2);
      expect(output).toContain('*******');
      expect(output).toContain('*******');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('start', () => {
    it('immediately updates the private IP addresses from the browser if possible', async () => {
      const spy = jest.spyOn(ipService, 'updatePrivateIpAddressesFromBrowser');
      await ipService.start();
      expect(spy).toHaveBeenCalled();
      await ipService.stop();
    });

    it('starts a timer to keep private IP addresses up to date every minute', async () => {
      const spy = jest.spyOn(ipService, 'updatePrivateIpAddressesFromBrowser');
      await ipService.start();
      spy.mockClear();
      await jest.advanceTimersByTimeAsync(60001);
      expect(spy).toHaveBeenCalled();
      await ipService.stop();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('updatePrivateIpAddressesFromNativeClient', () => {
    it('does nothing if the Chrome networking API is available', async () => {
      await ipService.updatePrivateIpAddressesFromNativeClient(['*******', '*******']);
      expect(storageService.has('privateIpAddresses')).toBeFalse();
      expect(odcSpies.sendMessage).not.toHaveBeenCalled();
    });

    it('stores the specified IP addresses if the Chrome networking API is not available', async () => {
      delete (chrome as any).enterprise;

      await ipService.updatePrivateIpAddressesFromNativeClient(['*******', '*******']);
      const output = storageService.get('privateIpAddresses');
      expect(output).toBeArrayOfSize(2);
      expect(output).toContain('*******');
      expect(output).toContain('*******');
    });

    it('triggers onNetworkChange if the private IP addresses have changed', async () => {
      delete (chrome as any).enterprise;
      storageService.set('privateIpAddresses', ['*******']);
      const spy = jest.spyOn(ipService.onNetworkChange, 'deferDispatch');
      await ipService.updatePrivateIpAddressesFromNativeClient(['*******', '*******']);
      expect(spy).toHaveBeenCalled();
    });

    it('cancels WebRTC interval and timeout if we receive IP addresses from Native Client', async () => {
      await ipService.stop();
      await ipService.start();

      const spy = jest.spyOn(ipService, 'updatePrivateIpAddresses');
      await ipService.updatePrivateIpAddressesFromNativeClient(['*******', '*******']);
      await jest.advanceTimersByTimeAsync(60001);
      expect(spy).not.toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('updatePrivateIpAddressesFromBrowser', () => {
    it('returns false and does not store anything if the private IP addresses cannot be retrieved from the browser', async () => {
      delete (chrome as any).enterprise;
      expect(await ipService.updatePrivateIpAddressesFromBrowser()).toBeFalse();
    });

    it('does not overwrite storage if the private IP addresses cannot be retrieved from the browser', async () => {
      delete (chrome as any).enterprise;
      storageService.set('privateIpAddresses', ['*******']);
      await ipService.updatePrivateIpAddressesFromBrowser();
      expect(ipService.privateIpAddresses).toEqual(expect.arrayContaining(['*******']));
    });

    it('stores the private IP addresses retrieved from the browser if possible', async () => {
      await ipService.updatePrivateIpAddressesFromBrowser();
      expect(ipService.privateIpAddresses).toEqual(expect.arrayContaining(['*******']));
    });

    it('triggers onNetworkChange event if the private IP addresses have changed', async () => {
      storageService.set('privateIpAddresses', ['***************']);
      const onNetworkChangeSpy = jest.spyOn(ipService.onNetworkChange, 'deferDispatch');
      await ipService.updatePrivateIpAddressesFromBrowser();
      expect(onNetworkChangeSpy).toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('outsidePremisesStatus', () => {
    it('returns "Unsure" if the given policy is undefined', () => {
      expect(ipService.outsidePremisesStatus).toEqual('Unsure');
    });

    it('returns "Not configured" if the given policy does not contain an outside premises location', () => {
      const policy = { locations: [] } as unknown as IPolicyConfig;
      const policyService: PolicyService = { policyConfig: policy } as unknown as PolicyService;
      ipService.setPolicyService(policyService);
      expect(ipService.outsidePremisesStatus).toEqual('Not configured');
    });

    it('returns "Unsure" if the given policy has an outside premises location but no public IP addresses are stored', () => {
      const policyService: PolicyService = {
        policyConfig: examplePolicyWithOutsidePremisesLocation,
      } as unknown as PolicyService;
      ipService.setPolicyService(policyService);
      expect(ipService.outsidePremisesStatus).toEqual('Unsure');
    });

    it('returns "Unsure" if the given policy has an outside premises location but there is no public IP address stored', () => {
      const policyService: PolicyService = {
        policyConfig: examplePolicyWithOutsidePremisesLocation,
      } as unknown as PolicyService;
      ipService.setPolicyService(policyService);
      expect(ipService.outsidePremisesStatus).toEqual('Unsure');
    });

    it('returns "Unsure" if the given policy has an outside premises location but the stored public IP addresses are more than 4 hours old', () => {
      storageService.set('publicIpAddresses', ['*********', '***************']);
      storageService.set('publicIpAddressesUpdatedAt', Date.now() - 16200000);
      const policyService: PolicyService = {
        policyConfig: examplePolicyWithOutsidePremisesLocation,
      } as unknown as PolicyService;
      ipService.setPolicyService(policyService);
      expect(ipService.outsidePremisesStatus).toEqual('Unsure');
    });

    it('returns "True" if the stored public IP addresses are outside premises according to the policy locations', () => {
      storageService.set('publicIpAddresses', ['***************']);
      storageService.set('publicIpAddressesUpdatedAt', Date.now() - 100);
      const policyService: PolicyService = {
        policyConfig: examplePolicyWithOutsidePremisesLocation,
      } as unknown as PolicyService;
      ipService.setPolicyService(policyService);
      expect(ipService.outsidePremisesStatus).toEqual('True');
    });

    it('returns "False" if a stored public IP addresses is on premises according to the policy locations', () => {
      storageService.set('publicIpAddresses', ['*********']);
      storageService.set('publicIpAddressesUpdatedAt', Date.now() - 100);
      const policyService: PolicyService = {
        policyConfig: examplePolicyWithOutsidePremisesLocation,
      } as unknown as PolicyService;
      ipService.setPolicyService(policyService);
      expect(ipService.outsidePremisesStatus).toEqual('False');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('isDeviceOutsidePremises', () => {
    it('returns false if the given policy does not contain an outside premises location', () => {
      const policy = { locations: [] } as unknown as IPolicyConfig;
      const policyService: PolicyService = { policyConfig: policy } as unknown as PolicyService;
      ipService.setPolicyService(policyService);
      expect(ipService.isDeviceOutsidePremises()).toBeFalse();
    });

    it('returns false if the given policy has an outside premises location but no public IP addresses are stored', () => {
      const policyService: PolicyService = {
        policyConfig: examplePolicyWithOutsidePremisesLocation,
      } as unknown as PolicyService;
      ipService.setPolicyService(policyService);
      expect(ipService.isDeviceOutsidePremises()).toBeFalse();
    });

    it('returns false if the given policy has an outside premises location but the stored public IP addresses are more than 4 hours old', () => {
      storageService.set('publicIpAddresses', ['*********', '***************']);
      storageService.set('publicIpAddressesUpdatedAt', Date.now() - 16200000);
      const policyService: PolicyService = {
        policyConfig: examplePolicyWithOutsidePremisesLocation,
      } as unknown as PolicyService;
      ipService.setPolicyService(policyService);
      expect(ipService.isDeviceOutsidePremises()).toBeFalse();
    });

    it('returns true if the stored public IP addresses are outside premises according to the policy locations', () => {
      storageService.set('publicIpAddresses', ['***************']);
      storageService.set('publicIpAddressesUpdatedAt', Date.now());
      const policyService: PolicyService = {
        policyConfig: examplePolicyWithOutsidePremisesLocation,
      } as unknown as PolicyService;
      ipService.setPolicyService(policyService);
      expect(ipService.isDeviceOutsidePremises()).toBeTrue();
    });

    it('returns false if the stored public IP addresses are on premises according to the policy locations', () => {
      storageService.set('publicIpAddresses', ['*********']);
      storageService.set('publicIpAddressesUpdatedAt', Date.now());
      const policyService: PolicyService = {
        policyConfig: examplePolicyWithOutsidePremisesLocation,
      } as unknown as PolicyService;
      ipService.setPolicyService(policyService);
      expect(ipService.isDeviceOutsidePremises()).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('isDeviceInLocation', () => {
    it('returns false if the given location is an outside premises location and no public IP addresses are stored', () => {
      expect(ipService.isDeviceInLocation(exampleOutsidePremisesLocation)).toBeFalse();
    });

    it('returns false if the given location is an outside premises location and the stored public IP addresses are more than 4 hours old', () => {
      storageService.set('publicIpAddresses', ['*********', '***************']);
      storageService.set('publicIpAddressesUpdatedAt', Date.now() - 16200000);
      expect(ipService.isDeviceInLocation(exampleOutsidePremisesLocation)).toBeFalse();
    });

    it('returns false if the given location is an outside premises location and any of the stored public IP addresses matches the location exceptions', () => {
      storageService.set('publicIpAddresses', ['*******', '*********', '***************']);
      storageService.set('publicIpAddressesUpdatedAt', Date.now());
      expect(ipService.isDeviceInLocation(exampleOutsidePremisesLocation)).toBeFalse();
    });

    it('returns true if the given location is an outside premises location and none of the stored public IP addresses match the location exceptions', () => {
      storageService.set('publicIpAddresses', ['*******', '*******', '*******']);
      storageService.set('publicIpAddressesUpdatedAt', Date.now());
      expect(ipService.isDeviceInLocation(exampleOutsidePremisesLocation)).toBeTrue();
    });

    it('returns false if the given location is not outside premises and no private IP addresses are stored', () => {
      expect(ipService.isDeviceInLocation(exampleOnPremisesLocation)).toBeFalse();
    });

    it('returns false if the given location is not outside premises and none of the private IPs matches its sources', () => {
      storageService.set('privateIpAddresses', ['*******']);
      expect(ipService.isDeviceInLocation(exampleOnPremisesLocation)).toBeFalse();
    });

    it('returns true if the given location is not outside premises and at least one of the private IPs matches its sources', () => {
      storageService.set('privateIpAddresses', ['*******', '**************', '*******']);
      expect(ipService.isDeviceInLocation(exampleOnPremisesLocation)).toBeTrue();
    });

    it('returns false if the given location is not outside premises and at least one of private IPs matches its exceptions', () => {
      storageService.set('privateIpAddresses', ['*******', '********', '*******']);
      expect(ipService.isDeviceInLocation(exampleOnPremisesLocation)).toBeFalse();
    });

    it('gives exceptions higher priority than sources', () => {
      storageService.set('privateIpAddresses', ['**************', '********', '************']);
      expect(ipService.isDeviceInLocation(exampleOnPremisesLocation)).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('isIpv4', () => {
    it('returns true if the given string looks like an IPv4 address', () => {
      expect(IpService.isIpv4('*******')).toBeTrue();
      expect(IpService.isIpv4('***************')).toBeTrue();
    });

    it('returns false if the given string does not look like an IPv4 address', () => {
      expect(IpService.isIpv4('')).toBeFalse();
      expect(IpService.isIpv4('xyzzy')).toBeFalse();
      expect(IpService.isIpv4('1::5')).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('isOutsidePremisesLocation', () => {
    it('returns true if the given location name is "outside premises"', () => {
      const location: ILocation = {
        id: 'foo',
        name: 'outside premises',
        exceptions: ['*******/0'],
      };

      expect(IpService.isOutsidePremisesLocation(location)).toBeTrue();
    });

    it('ignores spaces in the location name', () => {
      const location: ILocation = {
        id: 'foo',
        name: '   outside    premises   ',
        exceptions: ['*******/0'],
      };

      expect(IpService.isOutsidePremisesLocation(location)).toBeTrue();
    });

    it('is not case sensitive', () => {
      const location: ILocation = {
        id: 'foo',
        name: 'OuTsIdE pReMiSeS',
        exceptions: ['*******/0'],
      };

      expect(IpService.isOutsidePremisesLocation(location)).toBeTrue();
    });

    it('returns false if the given location name is not "outside premises"', () => {
      const location: ILocation = {
        id: 'foo',
        name: 'blah',
        exceptions: ['*******/0'],
      };

      expect(IpService.isOutsidePremisesLocation(location)).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('getOutsidePremisesLocation', () => {
    it('returns undefined if the given policy does not contain an outside premises location', () => {
      const policy = {
        locations: [],
      } as unknown as IPolicyConfig;
      expect(IpService.getOutsidePremisesLocation(policy)).toBeUndefined();
    });

    it('returns the outside premises location from the given policy if it has one', () => {
      expect(
        IpService.getOutsidePremisesLocation(examplePolicyWithOutsidePremisesLocation),
      ).toEqual(expect.objectContaining(exampleOutsidePremisesLocation));
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('isIpAddressInLocationExceptions', () => {
    it('returns true if the given IP address occurs in the exceptions of the given location', () => {
      expect(
        IpService.isIpAddressInLocationExceptions('*********', exampleOnPremisesLocation),
      ).toBeTrue();
    });

    it('returns false if the given IP address does not occur in the exceptions of the given location', () => {
      expect(
        IpService.isIpAddressInLocationExceptions('*******', exampleOnPremisesLocation),
      ).toBeFalse();
    });

    it('returns false if the given location does not contain any exceptions', () => {
      const location: ILocation = {
        id: 'blah',
        name: 'example',
      };

      expect(IpService.isIpAddressInLocationExceptions('*******', location)).toBeFalse();
    });

    it('returns false if the given IP address is invalid', () => {
      expect(
        IpService.isIpAddressInLocationExceptions('foobar', exampleOnPremisesLocation),
      ).toBeFalse();
    });

    it('returns false if the location exceptions are invalid IP ranges', () => {
      const location: ILocation = {
        id: 'blah',
        name: 'example',
        exceptions: ['1111111.111/567'],
      };

      expect(IpService.isIpAddressInLocationExceptions('*******', location)).toBeFalse();
    });

    it('matches explicit IP ranges', () => {
      expect(
        IpService.isIpAddressInLocationExceptions('********', exampleOnPremisesLocation),
      ).toBeTrue();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('isIpAddressInLocationSources', () => {
    it('returns true if the given IP address occurs in the sources of the given location', () => {
      expect(
        IpService.isIpAddressInLocationSources('************', exampleOnPremisesLocation),
      ).toBeTrue();
    });

    it('returns false if the given IP address does not occur in the sources of the given location', () => {
      expect(
        IpService.isIpAddressInLocationSources('*******', exampleOnPremisesLocation),
      ).toBeFalse();
    });

    it('returns false if the given location does not contain any sources', () => {
      const location: ILocation = {
        id: 'blah',
        name: 'example',
      };

      expect(IpService.isIpAddressInLocationSources('*******', location)).toBeFalse();
    });

    it('returns false if the given IP address is invalid', () => {
      expect(
        IpService.isIpAddressInLocationSources('foobar', exampleOnPremisesLocation),
      ).toBeFalse();
    });

    it('returns false if the location sources are invalid IP ranges', () => {
      const location: ILocation = {
        id: 'blah',
        name: 'example',
        sources: ['1111111.111/567'],
      };

      expect(IpService.isIpAddressInLocationSources('*******', location)).toBeFalse();
    });

    it('matches explicit IP ranges', () => {
      expect(
        IpService.isIpAddressInLocationSources('************', exampleOnPremisesLocation),
      ).toBeTrue();
    });
  });
});

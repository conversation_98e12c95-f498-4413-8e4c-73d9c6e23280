import AccessLogEntry from 'models/AccessLogEntry';
import SafeguardingAlert from 'models/SafeguardingAlert';
import SafeguardingContext from 'models/SafeguardingContext';
import SafeguardingMatch from 'models/SafeguardingMatch';
import SafeguardingRule from 'models/SafeguardingRule';
import SerialId from 'models/SerialId';
import TenantId from 'models/TenantId';
import AlarmService from 'services/AlarmService';
import StorageService from 'services/StorageService';
import { TelemetryEventType } from '../constants/TelemetryEventType';
import { areArraysEqual } from 'utilities/Helpers';
import AccessLogManager from '../access-logs/AccessLogManager';

import { CONTENT_TYPES, isSuicideTheme } from '../constants/SafeguardingConstants';
import { isTrackingUrl } from '../utilities/UrlUtilities';
import ITelemetryService from './ITelemetryService';

/**
 * Uses safeguarding rules to generate safeguarding alerts for access logs.
 */
export default class SafeguardingService {
  constructor(
    safeguardingContextStorageService: StorageService,
    alarmService: AlarmService,
    telemetryService: ITelemetryService,
    accessLogManager: AccessLogManager,
  ) {
    this._storageService = safeguardingContextStorageService;
    this._alarmService = alarmService;
    this._alarmService.addListener(this._alarmName, this._onContextAlarm);
    this._telemetryService = telemetryService;
    this._accessLogManager = accessLogManager;
  }

  /**
   * @param rules The safeguarding rules used to determine what access logs get safeguarding alerts.
   */
  public readonly loadRules = (rules: any): void => {
    this._rules = rules;
  };

  /**
   * @param tenantId The customer's tenant ID, if they have one.
   * @param serialId The customer's UNCL serial ID.
   */
  public readonly setProvisioningInfo = (serialId: SerialId, tenantId?: TenantId): void => {
    this._tenantId = tenantId;
    this._serialId = serialId;
  };

  /**
   * @param safeguardingAlertResource URI to cloud resource for reporting alerts.
   * @param safeguardingAlertSecurityToken Auth token for HTTP authorization header.
   * @param safeguardingGlobalToggle Boolean indicating whether safeguarding alerts are enabled.
   */
  public readonly setClientSettings = (
    safeguardingAlertResource: string | undefined,
    safeguardingAlertSecurityToken: string | undefined,
    safeguardingGlobalToggle: boolean,
  ): void => {
    this._safeguardingAlertResource = safeguardingAlertResource;
    this._safeguardingAlertSecurityToken = safeguardingAlertSecurityToken;
    this._safeguardingGlobalToggle = safeguardingGlobalToggle;
  };

  /**
   * @param log An access log to apply safeguarding rules to.
   */
  public readonly handleSafeguarding = async (log: AccessLogEntry): Promise<AccessLogEntry> => {
    if (this._safeguardingGlobalToggle === false) {
      console.debug('Safeguarding is off, not proceeding with safeguarding alert.');
      return log;
    }

    // Skip safeguarding alerts for tracking/analytics URLs, but only for sub-page requests
    // Main frame requests should always generate alerts regardless of tracking URL patterns
    // Note: Content types are typically lowercase in Chrome extensions, but we use _isMainFrameRequest
    // for consistency, defensive programming, and to handle undefined as main_frame
    if (
      log.url !== undefined &&
      log.url !== '' &&
      !this._isMainFrameRequest(log.contenttype) &&
      isTrackingUrl(log.url)
    ) {
      console.debug('Safeguarding: Skipping alert for sub-page tracking/analytics URL:', log.url);
      return log;
    }

    const matchingRule = this.applyRules(log);
    if (matchingRule !== undefined) {
      log.safeguardinglevel = matchingRule.weight;
      log.safeguardingtheme = matchingRule.title;

      const safeguardingAlert = this._parseSafeguardingAlert(log);
      await this._processAlert(safeguardingAlert);
    }

    return log;
  };

  /**
   * @param log Access log to apply a safeguarding rule against.
   * @return A SafeguardingMatch for which no exclusion matches but some condition does.
   */
  public readonly applyRules = (log: AccessLogEntry): SafeguardingMatch | undefined => {
    if (this._rules === undefined) throw new Error('Safeguarding rules are undefined');
    let match: SafeguardingMatch = { importance: Number.MAX_SAFE_INTEGER, weight: 0, title: '' };

    for (const rule of Object.values(this._rules)) {
      if (log.categories.some((category) => rule.exclusions.includes(category))) continue;

      for (const severity of Object.values(rule.severities)) {
        if (
          log.categories.some((category) => Object.keys(severity.categories).includes(category))
        ) {
          if (rule.importance <= match.importance && severity.weight >= match.weight) {
            match = { importance: rule.importance, weight: severity.weight, title: rule.title };
          }
        }
      }
    }

    if (match.title !== '') return match;
    else return undefined;
  };

  /**
   * @param log Access log triggering the safeguarding alert.
   * @return A SafeguardingAlert with fields populated by the provided access log.
   */
  private readonly _parseSafeguardingAlert = (log: AccessLogEntry): SafeguardingAlert => {
    const now = Date.now();
    return {
      blocked: log.blocked ?? false,
      level: log.safeguardinglevel ?? 1,
      tenantId: this._tenantId?.toString() ?? 'global',
      serial: this._serialId?.toString() ?? '',
      url: log.url ?? '',
      searchTerms: log.searchterms ?? '',
      localGroups: log.groups ?? [],
      username: log.username ?? '',
      clientTimeUtc: now,
      theme: log.safeguardingtheme ?? '',
      onPremCategories: log.categories ?? [],
      type: log.contenttype ?? '',
      localDate: this.generateLocalDateString(now),
    };
  };

  /**
   * A safeguarding alert triggers a context gathering period.
   * Subsequent alerts are grouped and sent as a batch.
   * If the safeguarding is suicide themed the context mechanism is ignored.
   *
   * @param safeguardingAlert alert to be sent or added to context bucket.
   */
  private readonly _processAlert = async (safeguardingAlert: SafeguardingAlert): Promise<void> => {
    if (isSuicideTheme(safeguardingAlert.theme)) {
      await this._sendImmediateAlert(safeguardingAlert);
    } else if (this._context.contextId === '') {
      await this._sendAlertAndGatherContext(safeguardingAlert);
    } else if (!this._isNonImmediateAlertDuplicate(safeguardingAlert)) {
      this._context.safeguardAlerts.push(safeguardingAlert);
      await this._storageService.save();
    }
  };

  /**
   * @param safeguardingAlert alert to be immediately sent.
   */
  private readonly _sendImmediateAlert = async (
    safeguardingAlert: SafeguardingAlert,
  ): Promise<void> => {
    // Skip duplicate alerts
    if (
      this._previousImmediateAlert !== undefined &&
      this._compareAlerts(safeguardingAlert, this._previousImmediateAlert)
    ) {
      console.debug('Safeguarding: Ignoring alert as it is probably a duplicate');
      return;
    }

    // Use the alert's content type for deduplication
    const alertType = safeguardingAlert.type;

    // Skip sub-requests with matching theme/level from the last minute
    // Treat undefined content type as main_frame for deduplication purposes
    if (
      !this._isMainFrameRequest(alertType) &&
      this._isSubRequestDuplicateOfRecentCriticalAlert(safeguardingAlert)
    ) {
      console.debug(
        'Safeguarding: Ignoring sub-request alert as a similar critical alert was sent recently',
      );
      return;
    }

    // Add the alert to recent critical alerts for future deduplication
    if (isSuicideTheme(safeguardingAlert.theme)) {
      // Store the alert for future deduplication
      this._recentCriticalAlerts.push(safeguardingAlert);
    }

    this._previousImmediateAlert = safeguardingAlert;

    await this._post(safeguardingAlert);
    // send the access logs with the alert to make sure they are relatively in sync
    try {
      await this._accessLogManager.flush();
    } catch (error) {
      console.error('Safeguarding: Error flushing access logs during immediate alert', error);
    }
  };

  /**
   * Checks if the given alert has already been inserted into the safeguarding alerts array for the current context.
   * @param alert The alert to use when comparing.
   * @returns A boolean indicating if the given alert has already been inserted into the array.
   */
  private readonly _isNonImmediateAlertDuplicate = (alert: SafeguardingAlert): boolean => {
    if (this._context.contextId === '' || this._context.safeguardAlerts.length === 0) {
      return false;
    }

    let isDupe = false;

    for (const existingAlert of this._context.safeguardAlerts) {
      if (this._compareAlerts(alert, existingAlert)) {
        isDupe = true;
        break;
      }
    }

    return isDupe;
  };

  /**
   * Compare the 2 given alerts. This will check all of the values of an alert and the time difference between them to decide if they are duplicates.
   *
   * @param safeguardingAlert The first alert to be compared.
   * @param previousAlert The second alert to be compared.
   * @param leewayInMs millisecond interval during which immediate alerts are considered duplicates.
   * @return True if the alerts are duplicates. False otherwise.
   */
  private readonly _compareAlerts = (
    safeguardingAlert: SafeguardingAlert,
    previousAlert: SafeguardingAlert,
    leewayInMs: number = 10000,
  ): boolean => {
    if (Math.abs(previousAlert.clientTimeUtc - safeguardingAlert.clientTimeUtc) > leewayInMs) {
      console.debug('_compareAlerts false: Safeguarding alerts outside of leeway.');
      return false;
    }

    if (
      previousAlert.username !== safeguardingAlert.username ||
      previousAlert.level !== safeguardingAlert.level ||
      previousAlert.theme !== safeguardingAlert.theme ||
      previousAlert.url !== safeguardingAlert.url ||
      previousAlert.blocked !== safeguardingAlert.blocked ||
      previousAlert.searchTerms !== safeguardingAlert.searchTerms ||
      previousAlert.tenantId !== safeguardingAlert.tenantId ||
      previousAlert.serial !== safeguardingAlert.serial ||
      !areArraysEqual(previousAlert.localGroups.sort(), safeguardingAlert.localGroups.sort())
    ) {
      console.debug("_compareAlerts false: Something doesn't match.");
      return false;
    }

    console.debug('_compareAlerts true');
    return true;
  };

  /**
   * Checks if a sub-request alert is a duplicate of a recent critical alert
   *
   * @param alert The alert to check
   * @returns True if it's a duplicate
   */
  private readonly _isSubRequestDuplicateOfRecentCriticalAlert = (
    alert: SafeguardingAlert,
  ): boolean => {
    if (this._recentCriticalAlerts.length === 0) {
      return false;
    }

    const now = Date.now();
    // Keep only alerts from the last minute
    this._recentCriticalAlerts = this._recentCriticalAlerts.filter(
      (recentAlert) => now - recentAlert.clientTimeUtc <= 60000,
    );

    // Check for matching theme (case-insensitive), level, and user identifiers
    return this._recentCriticalAlerts.some(
      (recentAlert) =>
        recentAlert.theme.toLowerCase() === alert.theme.toLowerCase() &&
        recentAlert.level === alert.level &&
        recentAlert.username === alert.username &&
        recentAlert.tenantId === alert.tenantId &&
        recentAlert.serial === alert.serial,
    );
  };

  /**
   * When the context is not valid we send an alert request.
   * Further safeguarding alerts will be gathered for context request.
   *
   * @param safeguardingAlert Alert to be posted.
   */
  private readonly _sendAlertAndGatherContext = async (
    safeguardingAlert: SafeguardingAlert,
  ): Promise<void> => {
    const contextId = await this._post(safeguardingAlert, 'alert');

    if (contextId !== undefined && contextId !== '') {
      this._context.contextId = contextId;
    }

    this._context.safeguardAlerts = [];
    await this._storageService.save();

    console.debug(`ContextId set to: ${this._context.contextId}`);

    // flush the access logs when context started
    try {
      await this._accessLogManager.flush();
    } catch (error) {
      console.error('Safeguarding: Error flushing access logs context window start', error);
    }

    await this._alarmService.create(this._alarmName, {
      delayInMinutes: this._contextTimeoutInMinutes,
    });
  };

  /**
   * At the end of the context gathering period this sends the context. The context is reset even if
   *  the attempt to post it fails.
   */
  private readonly _onContextAlarm = async (_alarm: chrome.alarms.Alarm): Promise<void> => {
    console.debug('Sending safeguarding context');
    if (this._context.contextId !== '' && this._context.safeguardAlerts.length > 0) {
      await this._post(this._context, 'context');
      // flush the access logs when the context window ends
      try {
        await this._accessLogManager.flush();
      } catch (error) {
        console.error('Safeguarding: Error flushing access logs during context window end', error);
      }
    }
    this._context = { contextId: '', safeguardAlerts: [] };
    await this._storageService.save();
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Normalizes content type for consistent comparison, treating undefined/empty values as main_frame
   * and converting to lowercase to handle case inconsistencies.
   * @param contentType The content type to normalize.
   * @returns The normalized content type.
   */
  private readonly _normalizeContentType = (contentType: string | undefined): string => {
    const normalized = contentType ?? CONTENT_TYPES.MAIN_FRAME;
    return normalized.toLowerCase();
  };

  /**
   * Checks if the given content type represents a main frame request.
   * Handles case insensitivity and treats undefined/empty values as main_frame.
   * @param contentType The content type to check.
   * @returns True if the content type is main_frame, false otherwise.
   */
  private readonly _isMainFrameRequest = (contentType: string | undefined): boolean => {
    return (
      this._normalizeContentType(contentType) ===
      this._normalizeContentType(CONTENT_TYPES.MAIN_FRAME)
    );
  };

  /**
   * Posts either a safeguarding alert or a safeguarding context, which is an array of safeguarding
   *  alerts along with a contextId. Posting a safeguarding alert to the API solicits a contextId
   *  for grouping further alerts during the context gathering period. The contextId is provided by
   *  the fetch response text. If this function succeeds that text is returned. Otherwise it returns
   *  undefined.
   *
   * @param body Either a safeguarding alert or a safeguarding context to send to the API.
   * @param endpoint Last path element of the alert resource URL.
   * @returns Undefined if the POST wasn't successful, otherwise the response text is returned.
   */
  private readonly _post = async (
    body: SafeguardingAlert | SafeguardingContext,
    endpoint: string = 'alert',
  ): Promise<string | undefined> => {
    try {
      if (
        this._safeguardingAlertResource === undefined ||
        this._safeguardingAlertSecurityToken === undefined
      ) {
        throw new Error('Safeguarding resource or security token not set, cannot send alert.');
      }

      const response = await fetch(`${this._safeguardingAlertResource}/${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this._safeguardingAlertSecurityToken}`,
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const httpError = new Error('Safeguarding API Failure');

        httpError.message = `Response was ${response.status} ${response.statusText}. `;
        if (response.status === 409) {
          console.debug(
            'SafeguardingService - 409 error - Email likely sent for username already today.',
          );

          this._logEvent(body as SafeguardingAlert, false);
          // If the error is a 409 we don't want to throw an exception. Just continue as normal.
          return undefined;
        }
        httpError.message += JSON.stringify(response);

        throw httpError;
      } else if (endpoint === 'alert') {
        const alert = body as SafeguardingAlert;
        this._logEvent(alert, true);

        // Track suicide alerts for deduplication (both main_frame and sub-page requests)
        if (isSuicideTheme(alert.theme)) {
          // Track suicide alerts for deduplication
          this._recentCriticalAlerts.push(alert);
        }
      }

      return await response.text();
    } catch (error: any) {
      console.warn('Error contacting the safeguarding API', error);

      if (endpoint === 'alert') {
        this._logEvent(body as SafeguardingAlert, false, error);
      }

      return undefined;
    }
  };

  /**
   * Logs an event or error to the telemetry service using the data provided.
   * @param alert The alert that was sent.
   * @param accepted Indicates if the alert was accepted by the safeguarding api.
   * @param error An error if one occurred.
   */
  private readonly _logEvent = (
    alert: SafeguardingAlert,
    accepted: boolean,
    error: Error | undefined = undefined,
  ): void => {
    let data = {};

    if (alert !== undefined) {
      data = {
        accepted,
        url: alert.url,
        safeguardAlertCategory: alert.theme,
        safeguardAlertLevel: alert.level,
        type: alert.type,
        blocked: alert.blocked,
      };
    }

    if (error === undefined) {
      this._telemetryService.logEvent(TelemetryEventType.SafeguardAlert, data);
    } else {
      this._telemetryService.logError(TelemetryEventType.SafeguardAlertFailed, error, data);
    }
  };

  /**
   * Format the given timestamp as a date/time string in the local timezone of this device.
   * This generates the format required by the safeguarding API. It will look like this:
   *
   *      "Thu May 11 2023 03:45:03 GMT+0100 (British Summer Time)"
   *
   * @param timestamp The timestamp to format as a date/time string.
   * @returns Returns a formatted date/time string.
   */
  public readonly generateLocalDateString = (timestamp: number): string => {
    const date = new Date(timestamp);
    const locale = 'en-US';

    const dayName = date.toLocaleDateString(locale, { weekday: 'short' });
    const monthName = date.toLocaleDateString(locale, { month: 'short' });
    const dayNumber = date.getDate();
    const year = date.getFullYear();
    const time = date.toLocaleTimeString(locale, { timeStyle: 'medium', hour12: false });
    const timeZone = this.getLocalTimeZoneString(locale, date);

    return `${dayName} ${monthName} ${dayNumber} ${year} ${time} ${timeZone}`;
  };

  /**
   * Get a string describing the timezone this device would be in at the specified date/time.
   * The date must be specified for cases where the time zone changes during the year, e.g. due to
   *  Daylight Savings Time.
   *
   * This will try to return a string formatted like this:
   *
   *      "GMT+0000 (Greenwich Mean Time)"
   *
   * The text in parentheses will be the time zone name if known. It will fall back on the IANA
   *  time zone identifier (typically a place name) if necessary. If that information cannot be
   *  determined either, then the parenthesised text will be omitted.
   *
   * @param locale A locale identifier (e.g. 'en-US') specifying how to localize the time zone
   *  name / identifier.
   * @param date The date and time to generate a time zone for.
   * @returns Returns a string containing the offset and name of the local time zone.
   */
  public readonly getLocalTimeZoneString = (locale: string, date: Date): string => {
    // Caution: date.getTimezoneOffset() returns an inverted offset in minutes.
    // E.g. GMT + 1 hour is represented as -60.
    const originalOffset = date.getTimezoneOffset();
    const sign = originalOffset >= 0 ? '-' : '+'; // <-- sign is deliberately inverted!
    const minutes = Math.abs(originalOffset) % 60;
    const minutesString = `${minutes < 10 ? '0' : 0}${minutes}`;
    const hours = Math.floor(Math.abs(originalOffset) / 60);
    const hoursString = `${hours < 10 ? '0' : 0}${hours}`;

    let output = `GMT${sign}${hoursString}${minutesString}`;

    // Try to get the traditional time zone name, e.g. 'British Summer Time'.
    // This may return a GMT offset if it cannot be determined.
    // It's not possible to get the timezone name on its own. It has to come with other date or
    //  time info which we then need to remove.
    let name = date
      .toLocaleDateString(locale, { year: 'numeric', timeZoneName: 'long' })
      .replace(/^[0-9,:]+/, '')
      .trim();

    if (
      name === '' ||
      name.startsWith('GMT+') ||
      name.startsWith('GMT-') ||
      name.startsWith('UTC+') ||
      name.startsWith('UTC-')
    ) {
      // Try to get the IANA time zone identifier instead.
      name = new Intl.DateTimeFormat(locale).resolvedOptions().timeZoneName ?? '';
    }

    if (name !== '') {
      output += ` (${name})`;
    }

    return output;
  };

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Gets the storage backed safeguarding context variables.
   */
  private get _context(): SafeguardingContext {
    let context = this._storageService.get('context') as SafeguardingContext;
    if (context === undefined) {
      context = { contextId: '', safeguardAlerts: [] };
      this._context = context;
    }
    return context;
  }

  /**
   * Sets the storage backed safeguarding context variables.
   *   A _storageService.save() is required whenever a context component is set.
   */
  private set _context(context: SafeguardingContext) {
    this._storageService.set('context', context);
  }

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Storage service for storing the safeguarding context between service worker restarts.
   */
  private readonly _storageService: StorageService;

  /**
   * Manages use of chrome alarms API while taking into account the service worker lifecycle.
   */
  private readonly _alarmService: AlarmService;

  /**
   * The telemetry service for logging events.
   */
  private readonly _telemetryService: ITelemetryService;

  /**
   * String constant for chrome alarm.
   */
  private readonly _alarmName: string = 'safeguard-context';

  /**
   * Length of time that context is gathered for a safeguarding alert.
   */
  private readonly _contextTimeoutInMinutes: number = 10;
  /**
   * The safeguarding alert security token.
   */
  private _safeguardingAlertSecurityToken: string | undefined;

  /**
   * The safeguarding alert resource url.
   */
  private _safeguardingAlertResource: string | undefined;

  /**
   * Boolean indicating whether safeguarding alerts are enabled.
   */
  private _safeguardingGlobalToggle: boolean | undefined;

  /**
   * The last immediate safeguarding alert that was not considered a duplicate of one prior.
   */
  private _previousImmediateAlert: SafeguardingAlert | undefined;

  /**
   * The customer's tenant ID, if they have one.
   */
  private _tenantId: TenantId | undefined;

  /**
   * The customer's UNCL serial ID.
   */
  private _serialId: SerialId | undefined;

  private readonly _accessLogManager: AccessLogManager;

  /**
   * The safeguarding rules used to determine what access logs get safeguarding alerts
   */
  private _rules:
    | undefined
    | {
        radicalisation: SafeguardingRule;
        abuse: SafeguardingRule;
        suicide: SafeguardingRule;
        substanceabuse: SafeguardingRule;
        bullying: SafeguardingRule;
        criminalactivity: SafeguardingRule;
        adultcontent: SafeguardingRule;
      };

  /**
   * Stores recent critical alerts to prevent duplicates from tracking/analytics requests
   */
  private _recentCriticalAlerts: SafeguardingAlert[] = [];
}

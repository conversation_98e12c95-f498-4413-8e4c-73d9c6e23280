import type StorageService from './StorageService';
import ContentScriptMessage from 'content-script-messages/ContentScriptMessage';
import PrivateIpRequestMessage from 'content-script-messages/PrivateIpRequestMessage';
import { capitalise, areArraysEqual, isManifestV2, ipToInt } from 'utilities/Helpers';
import type { ILocation, IPolicyConfig } from 'models/PolicyConfigModels';
import ipRangeCheck from 'ip-range-check';
import StandaloneEvent from 'utilities/StandaloneEvent';
import OffscreenDocumentController from 'controllers/OffscreenDocumentController';
import WebRtcService from './WebRtcService';
import PrivateIpResponseMessage from 'content-script-messages/PrivateIpResponseMessage';
import PolicyService from 'services/PolicyService';

/**
 * If it's been at least this long (in milliseconds) since we last successfully queried the public
 *  IP address, then we'll try to query it again.
 * We can keep using the public IP address after this time, up until the grace period expires.
 *
 * @note The public IP address may be updated in response to other events before it reaches this
 *  age, e.g. if the private IP address has changed.
 *
 * @see publicIpAddressGracePeriod
 */
const publicIpAddressLifetime = 10800000; // 3 hours

/**
 * Maximum time (in milliseconds) we can keep using a public IP address after its lifetime expires.
 * If it's been longer than this, then the IP address is considered out-of-date and probably
 *  shouldn't be used.
 *
 * @see publicIpAddressLifetime
 *
 * @todo Combine this into the lifetime value. We no longer query public IPs actively.
 */
const publicIpAddressGracePeriod = 3600000; // 1 hour

/**
 * Determines how often we'll check for changes in private IP address.
 * This is the time in milliseconds between each check.
 * This only applies if the browser extension API is available, which is usually on managed
 *  Chromebooks. In native mode, we'll usually get the private IP addresses from the native client.
 */
const privateIpAddressUpdateInterval = 60000; // 1 minute

/**
 * Determines how often we'll check for changes in the private IP address if we are using WebRTC.
 * This is the time in milliseconds between each check.
 */
const webRTCPrivateIpAddressUpdateInterval = 300000; // 5 minutes

/**
 * Fetches and stores the device's IP addresses for matching against locations.
 */
export default class IpService {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance which caches data to the specified storage service.
   *
   * @param offscreenDocumentController Used to create and access the offscreen document, which is
   *  responsible for querying private IP addresses.
   * @param storageService The storage service to use for caching IP addresses.
   */
  constructor(
    offscreenDocumentController: OffscreenDocumentController,
    storageService: StorageService,
  ) {
    this._offscreenDocumentController = offscreenDocumentController;
    this._offscreenDocumentController.onMessage.addListener(this._onOffscreenDocumentMessage);
    this._storageService = storageService;
    this._webRtcService.onPrivateIpAddress.addListener(this._onPrivateIpAddresses);
  }

  // -----------------------------------------------------------------------------------------------
  // Events.

  /**
   * This event is triggered when we detect that the private IP address has changed.
   */
  public readonly onNetworkChange = new StandaloneEvent();

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * This uses the cached public IP addresses, and compares them to the outside premises location
   *  specified in the given policy, if any.
   *
   * @returns String describing current outside premises status
   *   'Not configured' - The policy doesn't contain an "Outside premises" location.
   *   'Unsure' - The policy contains an "Outside premises" location but public IP is not known.
   *   'True' - The policy contains an "Outside premises" location and public IP matches.
   *   'False' - The policy contains an "Outside premises" location and no public IP matches.
   */
  public get outsidePremisesStatus(): string {
    if (this._policyService?.policyConfig === undefined) {
      return 'Unsure';
    }

    const outsidePremises = IpService.getOutsidePremisesLocation(this._policyService.policyConfig);
    if (outsidePremises === undefined) {
      return 'Not configured';
    }

    if (this.publicIpAddresses.length === 0 || !this.hasRecentPublicIpAddresses) {
      return 'Unsure';
    }

    return capitalise(this.isDeviceInLocation(outsidePremises).toString());
  }

  /**
   * Get the private (aka local) addresses assigned to this device.
   * This filters the addresses to IPv4, to match mv2 behaviour.
   * This gets the data from a cache so it technically isn't guaranteed to be up-to-date. However,
   *  it should be recent enough for all practical purposes.
   * If there are no known private IP addresses then this returns an empty array.
   */
  public get privateIpAddresses(): string[] {
    const addresses = this._storageService.get('privateIpAddresses');
    if (!Array.isArray(addresses)) {
      return [];
    }
    // Omit IPv6 addresses. This is to match mv2 behaviour, and to ensure we don't break any other
    //  code which is expecting only IPv4 addresses.
    return addresses.filter((address) => typeof address === 'string' && IpService.isIpv4(address));
  }

  /**
   * Store the private (aka local) IP addresses in the cache.
   * This accepts IPv4 and IPv6 addresses. The 'get' accessor filters them to IPv4.
   * It also updates the time at which the information was last updated.
   */
  protected readonly _storePrivateIpAddresses = (addresses: string[]): void => {
    this._storageService.set('privateIpAddresses', addresses);
    this._storageService.set('privateIpAddressesUpdatedAt', Date.now());
    this._storageService.saveInTheBackground();
  };

  /**
   * Return the timestamp (milliseconds) that the private IP addresses were last updated.
   * Returns undefined if they've never been updated.
   *
   * There's generally no need to check this before using the private IP address. The information
   *  should always be reasonably up-to-date. This date/time is only stored so that we can
   *  internally check whether we're likely to need to update the public IP address.
   */
  public get privateIpAddressesUpdatedAt(): number | undefined {
    const timestamp = this._storageService.get('privateIpAddressesUpdatedAt');
    if (typeof timestamp !== 'number') {
      return undefined;
    }
    const now = Date.now();
    if (timestamp > now) {
      return now;
    }
    return timestamp;
  }

  /**
   * Return the timestamp (milliseconds) that the private IP addresses were last changed.
   * Returns undefined if they've never been changed.
   *
   * Unlike privateIpAddressesUpdatedAt, this reflects when the addesses last actually changed.
   */
  public get privateIpAddressesChangedAt(): number | undefined {
    const timestamp = this._storageService.get('privateIpAddressesChangedAt');
    if (typeof timestamp !== 'number') {
      return undefined;
    }
    const now = Date.now();
    if (timestamp > now) {
      return now;
    }
    return timestamp;
  }

  /**
   * Get the public (aka remote) IP addresses assigned to this device.
   * Returns an empty array if the information has never been successfully queried, or we simply
   *  don't have a public IP address, e.g. because we have no Internet connection.
   *
   * This gets the data from a cache so it isn't guaranteed to be up-to-date. In ideal
   *  circumstances, it should be recent enough for practical purposes. However, there are
   *  situations where we can't get the information successfully. You are advised to call
   *  hasRecentPublicIpAddresses to ensure that the information is not too old.
   */
  public get publicIpAddresses(): string[] {
    const addresses = this._storageService.get('publicIpAddresses');
    if (!Array.isArray(addresses)) {
      return [];
    }
    return addresses.filter((address) => typeof address === 'string');
  }

  /**
   * Store the public (aka remote) IP addresses in the cache.
   * If the specified array is empty, then it implies we don't have a public IP address, e.g.
   *  because there is no Internet connection.
   * This will update the corresponding updated date/time to now.
   */
  protected readonly _storePublicIpAddresses = (addresses: string[]): void => {
    this._storageService.set('publicIpAddresses', addresses);
    this._storageService.set('publicIpAddressesUpdatedAt', Date.now());
    this._storageService.saveInTheBackground();
  };

  /**
   * Return the timestamp (milliseconds) that the public IP addresses were last updated successfully.
   * Returns undefined if the information has never been updated successfully.
   */
  public get publicIpAddressesUpdatedAt(): number | undefined {
    const timestamp = this._storageService.get('publicIpAddressesUpdatedAt');
    if (typeof timestamp !== 'number') {
      return undefined;
    }
    const now = Date.now();
    if (timestamp > now) {
      return now;
    }
    return timestamp;
  }

  /**
   * Check if we have successfully updated the public IP addresses recently.
   * Returns true if the addresses were successfully queried within the last 4 hours.
   */
  public get hasRecentPublicIpAddresses(): boolean {
    if (!this._storageService.has('publicIpAddresses')) {
      return false;
    }

    const updatedAt = this.publicIpAddressesUpdatedAt;
    if (updatedAt === undefined) {
      return false;
    }

    return updatedAt + publicIpAddressLifetime + publicIpAddressGracePeriod > Date.now();
  }

  /**
   * Get an array containing all of the public and private IPv4 addresses assigned to this device.
   * This returns an empty array if there are no known IPv4 addresses.
   * The public IP address will be omitted if it is not recent.
   */
  public readonly getAllIpAddresses = (): string[] => {
    const output = [...this.privateIpAddresses];
    if (this.hasRecentPublicIpAddresses) {
      output.push(...this.publicIpAddresses);
    }
    return output;
  };

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Ensure our stored IP addresses are up-to-date, and start timers to keep them up-to-date.
   * The caller must await the result of this function before calling it again, and before calling
   *  stop().
   */
  public readonly start = async (): Promise<void> => {
    console.debug(
      'IpService - Private IP address(es) loaded from storage: ',
      this.privateIpAddresses,
    );

    if (this.hasRecentPublicIpAddresses) {
      console.debug(
        'IpService - Public IP address(es) loaded from storage: ',
        this.publicIpAddresses,
      );
    }

    // If we can get the private IP addresses from the browser, then make sure we update the
    //  information now, and again every minute. Otherwise, we're relying on receiving messages from
    //  the native client.
    if (chrome.enterprise?.networkingAttributes?.getNetworkDetails !== undefined) {
      await this.updatePrivateIpAddressesFromBrowser();

      if (this._privateIpAddressesUpdateInterval === undefined) {
        this._privateIpAddressesUpdateInterval = setInterval(() => {
          this.updatePrivateIpAddressesFromBrowser().catch(console.warn);
        }, privateIpAddressUpdateInterval);
      }
    } else {
      // Initial private IP update, delayed to see if the native client gives us private IPs.
      this._privateIpAddressesUpdateTimeout = setTimeout(() => {
        this.updatePrivateIpAddresses().catch(console.warn);
      }, 5000);

      if (this._privateIpAddressesUpdateInterval === undefined) {
        this._privateIpAddressesUpdateInterval = setInterval(() => {
          this.updatePrivateIpAddresses().catch(console.warn);
        }, webRTCPrivateIpAddressUpdateInterval);
      }
    }
  };

  /**
   * Stop the timers which keep our IP addresses updated.
   * The caller must await the result of this function before calling it again, and before calling
   *  start().
   */
  public readonly stop = async (): Promise<void> => {
    if (this._privateIpAddressesUpdateInterval !== undefined) {
      clearInterval(this._privateIpAddressesUpdateInterval);
      this._privateIpAddressesUpdateInterval = undefined;
    }
  };

  /**
   * Sets the IpService reference to the PolicyService so we have most recent information to
   *  determine the outside premises status.
   *
   * @param policyService The policy service, set here during its construction
   */
  public readonly setPolicyService = (policyService: PolicyService): void => {
    this._policyService = policyService;
  };

  /**
   * Store the given private IP addresses, if the information cannot be retrieved from the browser.
   * This is used in native mode when the information is provided by the native client.
   * This will deliberately do nothing if the equivalent browser extension API is available.
   * If this function detects that the private IP addresses have changed, then it will try to check
   *  if the public IP address has changed as well.
   *
   * @param newAddresses The private IP addresses to store.
   */
  public readonly updatePrivateIpAddressesFromNativeClient = async (
    newAddresses: string[],
  ): Promise<void> => {
    // If we have access to private IP addresses via the Chrome extension API, then ignore the data
    //  coming from the native client. This is to ensure we don't end up with conflicting info.
    if (chrome.enterprise?.networkingAttributes?.getNetworkDetails !== undefined) {
      console.debug(
        'IpService - Ignoring private IP addresses from native client. We can get that information from the browser instead.',
      );
      return;
    }

    // If we're receiving our private IP address from the native client, stop trying to use WebRTC.
    if (this._privateIpAddressesUpdateInterval !== undefined) {
      clearInterval(this._privateIpAddressesUpdateInterval);
      this._privateIpAddressesUpdateInterval = undefined;
    }

    if (this._privateIpAddressesUpdateTimeout !== undefined) {
      clearTimeout(this._privateIpAddressesUpdateTimeout);
      this._privateIpAddressesUpdateTimeout = undefined;
    }

    const oldAddresses = this.privateIpAddresses;
    this._storePrivateIpAddresses(newAddresses);

    if (!areArraysEqual(oldAddresses, newAddresses)) {
      console.debug('IpService - Private IP address(es) changed to: ', newAddresses);
      this.onNetworkChange.deferDispatch();

      this._storageService.set('privateIpAddressesChangedAt', Date.now());
      this._storageService.saveInTheBackground();

      // A network change means the public IP address might have changed too.
      // TODO: We get public IP addresses from DMS check-in (aka heartbeat). Possibly send a new
      //  heartbeat on network change to ensure it's up-to-date? Ensure it's debounced and
      //  rate-limited though.
    }
  };

  /**
   * Use the browser extension API to update our stored copy of the device's private IP address(es).
   * This will typically only work on a managed Chromebook. It will do nothing on other platforms.
   * If this function detects that the private IP addresses have changed, then it will try to check
   *  if the public IP address has changed as well.
   *
   * @returns Returns a promise which resolves to true if the private IP address information was
   *  successfully retrieved from the browser, or false if the API is not available on this
   *  platform.
   */
  public readonly updatePrivateIpAddressesFromBrowser = async (): Promise<boolean> => {
    const networkDetails = await IpService.getNetworkDetails();
    if (networkDetails === undefined) {
      // The API isn't available on this platform.
      return false;
    }

    const oldAddresses = this.privateIpAddresses;
    const newAddresses = [];
    if (networkDetails.ipv4 !== undefined && networkDetails.ipv4 !== '') {
      newAddresses.push(networkDetails.ipv4);
    }
    if (networkDetails.ipv6 !== undefined && networkDetails.ipv6 !== '') {
      newAddresses.push(networkDetails.ipv6);
    }
    this._storePrivateIpAddresses(newAddresses);

    if (!areArraysEqual(oldAddresses, newAddresses)) {
      console.debug('IpService - Private IP address(es) changed to: ', newAddresses);
      this.onNetworkChange.deferDispatch();

      this._storageService.set('privateIpAddressesChangedAt', Date.now());
      this._storageService.saveInTheBackground();

      // A network change means the public IP address might have changed too.
      // TODO: We get public IP addresses from DMS check-in (aka heartbeat). Possibly send a new
      //  heartbeat on network change to ensure it's up-to-date? Ensure it's debounced and
      //  rate-limited though.
    }

    return true;
  };

  /**
   * Store the given private IP addresses that have been retrieved from WebRTC using
   *  the offscreen document.
   * If this function detects that the private IP addresses have changed, then it will try to check
   *  if the public IP address has changed as well.
   *
   * @param newAddresses The private IP addresses to store.
   */
  public readonly updatePrivateIpAddressesFromWebRTC = async (
    newAddresses: string[],
  ): Promise<void> => {
    // If we have access to private IP addresses via the Chrome extension API, then ignore the data
    //  coming from the native client. This is to ensure we don't end up with conflicting info.
    if (chrome.enterprise?.networkingAttributes?.getNetworkDetails !== undefined) {
      console.debug(
        'IpService - Ignoring private IP addresses from WebRTC. We can get that information from the browser instead.',
      );
      return;
    }

    const oldAddresses = this.privateIpAddresses;
    this._storePrivateIpAddresses(newAddresses);

    if (!areArraysEqual(oldAddresses, newAddresses)) {
      console.debug('IpService - Private IP address(es) changed to: ', newAddresses);
      this.onNetworkChange.deferDispatch();

      // A network change means the public IP address might have changed too.
      // TODO: We get public IP addresses from DMS check-in (aka heartbeat). Possibly send a new
      //  heartbeat on network change to ensure it's up-to-date? Ensure it's debounced and
      //  rate-limited though.
    }
  };

  /**
   * Start trying to update our cached private IP address via RTC.
   */
  public readonly updatePrivateIpAddresses = async (): Promise<void> => {
    // Use WebRTC directly with MV2.
    if (isManifestV2) {
      this._webRtcService.getPrivateIpAddresses().catch(console.warn);
      return;
    }

    // We don't get an immediate response from this message. We have to wait for a separate
    //  message to be sent back. See: _onOffscreenDocumentMessage().
    await this._offscreenDocumentController.sendMessage(new PrivateIpRequestMessage());
  };

  /**
   * Check if the device is currently outside premises, based on stored IP addresses.
   * This infers the outside premises location from policy.
   *
   * @param config The current policy config. The outside premises location is taken from here.
   * @returns True if this devices appears to be outside premises. False if it appears to be on
   *  premises, the location could not be determined, or the policy contains no valid outside
   *  premises location.
   */
  public readonly isDeviceOutsidePremises = (): boolean => {
    if (this._policyService?.policyConfig === undefined) {
      return false;
    }

    const location = IpService.getOutsidePremisesLocation(this._policyService.policyConfig);
    if (location === undefined) {
      return false;
    }

    return this.isDeviceInLocation(location);
  };

  /**
   * Check if the device is currently in the specified location, based on stored IP addresses.
   * This will handle outside and on premises locations appropriately.
   *
   * @param location The location to check.
   * @returns Returns true if this device's stored IP addresses currently match the location
   */
  public readonly isDeviceInLocation = (location: ILocation): boolean => {
    // Outside premises works on the basis of an exclusion list. The customer specifies all the
    //  public IP address ranges which might refer to their premises, and puts them in the
    //  exceptions list. We therefore match an outside premises location if _none_ of our public IP
    //  addresses match the location's exceptions.
    if (IpService.isOutsidePremisesLocation(location)) {
      const publicIps = this.publicIpAddresses;
      if (!this.hasRecentPublicIpAddresses || publicIps.length === 0) {
        // We don't have any recent public IP addresses, so we can't match outside premises.
        return false;
      }

      return !this.publicIpAddresses.some((ip) =>
        IpService.isIpAddressInLocationExceptions(ip, location),
      );
    }

    // If the device is outside premises don't do any matching with the private ip address.
    if (this.isDeviceOutsidePremises()) {
      return false;
    }

    // All other locations work with private IP addresses. We match the location if none of our
    //  private IP addresses appear in the exceptions, but one or more appear in the sources.
    if (
      this.privateIpAddresses.some((ip) => IpService.isIpAddressInLocationExceptions(ip, location))
    ) {
      return false;
    }

    return this.privateIpAddresses.some((ip) =>
      IpService.isIpAddressInLocationSources(ip, location),
    );
  };

  // -----------------------------------------------------------------------------------------------
  // Event handlers.

  /**
   * Triggered when a message has been received from the offscreen document.
   *
   * @param message The message which was received.
   */
  private readonly _onOffscreenDocumentMessage = async (
    message: ContentScriptMessage,
  ): Promise<void> => {
    if (message instanceof PrivateIpResponseMessage) {
      if (message.ipAddresses.length === 0) {
        // Deliberately keep whatever address(es) we last had in case it's a temporary glitch.
        console.debug('IpService received no private IP addresses from the offscreen document.');
        return;
      }

      console.debug(
        'IpService received private IP address(es) from the offscreen document: ',
        message.ipAddresses,
      );

      await this.updatePrivateIpAddressesFromWebRTC(message.ipAddresses);

      // Once we get the ip addresses from the offscreen document it can be closed.
      await this._offscreenDocumentController.stop();
    }
  };

  /**
   * Triggered when public IP addresses have been received.
   * In the past, we queried the public IP addresses using WebRTC. We now receive the information
   *  from DMS check-in requests instead.
   *
   * @param ipAddresses An array of the public IP addresses belonging to this device.
   */
  public readonly onPublicIpAddresses = (ipAddresses: string[]): void => {
    if (ipAddresses.length === 0) {
      // Deliberately keep whatever address(es) we last had in case it's a temporary glitch.
      console.debug('IpService received no public IP addresses.');
      return;
    }

    console.debug('IpService received public IP address(es): ', ipAddresses);
    this._storePublicIpAddresses(ipAddresses);
  };

  /**
   * Triggered when the private IP addresses have been recived from WebRTC.
   *
   * @ipAddresses An array of all the private IP addresses received so far.
   */
  private readonly _onPrivateIpAddresses = (ipAddresses: string[]): void => {
    if (ipAddresses.length === 0) {
      console.debug('IpService received no private IP addresses from WebRTC.');
      return;
    }

    console.debug('IpService received private IP address(es) from WebRTC: ', ipAddresses);
    this.updatePrivateIpAddressesFromWebRTC(ipAddresses).catch(console.warn);
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Check if the given string looks like an IPv4 address.
   *
   * @param address The string to check.
   * @returns true if the string looks like an IPv4 address, or false if not.
   */
  public static readonly isIpv4 = (address: string): boolean => {
    return /\d+\.\d+\.\d+\.\d+/.test(address);
  };

  /**
   * Promise wrapper around the Chrome extension API function to get network details.
   *
   * @returns A promise which resolves to the network details if the call succeeded, or resolves to
   *  undefined if the function is not available on the current platform. The promise will reject
   *  if the function was called but failed with an error.
   */
  public static readonly getNetworkDetails = async (): Promise<
    chrome.enterprise.networkingAttributes.NetworkDetails | undefined
  > => {
    if (chrome.enterprise?.networkingAttributes?.getNetworkDetails === undefined) {
      return undefined;
    }

    return await new Promise((resolve, reject) => {
      chrome.enterprise.networkingAttributes.getNetworkDetails(
        (networkDetails: chrome.enterprise.networkingAttributes.NetworkDetails) => {
          if (chrome.runtime.lastError !== undefined) {
            reject(chrome.runtime.lastError);
            return;
          }
          resolve(networkDetails);
        },
      );
    });
  };

  /**
   * Checks if the given location is an outside premises location.
   * An outside premises location is used to define policies which behave different when the device
   *  is away from the school (or other organisation).
   * This check is based entirely on the location name.
   *
   * @param location The location to check.
   * @returns Returns true if the location name indicates it's an outside premises location. Returns
   *  false otherwise.
   */
  public static readonly isOutsidePremisesLocation = (location: ILocation): boolean => {
    return location.name.toLowerCase().replace(/\s/g, '').trim() === 'outsidepremises';
  };

  /**
   * Extract the outside premises location from the specified policy, if possible.
   *
   * @param config The policy to extract the location from.
   * @returns The outside premises location from the specified policy, if it contains one. Returns
   *  undefined otherwise.
   */
  public static readonly getOutsidePremisesLocation = (
    config: IPolicyConfig,
  ): ILocation | undefined => {
    for (const location of config.locations) {
      if (
        IpService.isOutsidePremisesLocation(location) &&
        location.exceptions !== undefined &&
        location.exceptions.length > 0
      ) {
        return location;
      }
    }

    return undefined;
  };

  /**
   * Check if the specified IP address matches the exceptions of the specified location.
   * This is used to determine if a device is outside premises by comparing its IP addresses to the
   *  outside premises location.
   *
   * @param ipAddress The IP address to look for.
   * @param location The IP address will be matched against the exceptions defined in this location.
   * @return Returns true if the IP address matches any exception in the location. Returns false if
   *  it doesn't match any exception or there are no exceptions defined.
   */
  public static readonly isIpAddressInLocationExceptions = (
    ipAddress: string,
    location: ILocation,
  ): boolean => {
    return (
      location.exceptions?.some((range: string) => {
        if (range.includes('-')) {
          const [start, end] = range.split('-');
          return ipToInt(start) <= ipToInt(ipAddress) && ipToInt(ipAddress) <= ipToInt(end);
        } else {
          return ipRangeCheck(ipAddress, range);
        }
      }) ?? false
    );
  };

  /**
   * Check if the specified IP addresses matches the given location's main IP addresses.
   * This does not check exceptions.
   *
   * @param ipAddress The IP address to look for.
   * @param location The location to match against.
   * @returns Returns true if the IP address matches the specified location, or false if not.
   */
  public static readonly isIpAddressInLocationSources = (
    ipAddress: string,
    location: ILocation,
  ): boolean => {
    return (
      location.sources?.some((range: string) => {
        if (range.includes('-')) {
          const [start, end] = range.split('-');
          return ipToInt(start) <= ipToInt(ipAddress) && ipToInt(ipAddress) <= ipToInt(end);
        } else {
          return ipRangeCheck(ipAddress, range);
        }
      }) ?? false
    );
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Manages the offscreen document, which is responsible for querying public IP addresses in mv3.
   * @note Offscreen documents only work in mv3. See _webRtcService for the mv2 equivalent.
   */
  private readonly _offscreenDocumentController: OffscreenDocumentController;

  /**
   * Queries the public IP addresses in mv2.
   * @note The extension can only invoke WebRTC directly if it's targeting mv2. When running in mv3,
   *  this is wrapped in an offscreen document instead. See _offscreenDocumentController.
   */
  private readonly _webRtcService = new WebRtcService();

  /**
   * The storage service used to store the ip addresses.
   */
  protected readonly _storageService: StorageService;

  /**
   * Manages the download and parsing of the policy. Used to determine outside premises status.
   */
  private _policyService?: PolicyService;

  /**
   * Handle to a regular interval which will check for changes to the private IP addresses.
   * This will only be used if the relevant API is available in the browser. Otherwise, we will
   *  depend on getting IpAddresses messages from the native client.
   */
  private _privateIpAddressesUpdateInterval?: ReturnType<typeof setInterval>;

  /**
   * Handle to a timeout interval for our initial private IP address update using WebRTC.
   */
  private _privateIpAddressesUpdateTimeout?: ReturnType<typeof setTimeout>;
}

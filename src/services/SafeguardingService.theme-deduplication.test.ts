import 'test-helpers/chrome-api';

import AccessLogEntry from 'models/AccessLogEntry';
import AlarmService from 'services/AlarmService';
import MockTelemetryService from '../test-helpers/MockTelemetryService';
import SafeguardingService from 'services/SafeguardingService';
import StorageService from 'services/StorageService';
import safeguardingRules from 'mini-blocklist-data/safeguardingRules';

describe('SafeguardingService Theme Deduplication', () => {
  let safeguardingService: SafeguardingService;
  let testLog: AccessLogEntry;
  let postSpy: jest.SpyInstance;

  const alarmService = new AlarmService();
  const storageService = new StorageService('safeguardingContext', undefined);
  const telemetryService = new MockTelemetryService();

  // Mock AccessLogManager
  const mockAccessLogManager = {
    lastUploadedAt: 0,
    getUploadCount: jest.fn((reset: boolean) => (reset ? 0 : 10)),
    populate: jest.fn(async (storageData: Record<string, any>) => {
      await Promise.resolve();
    }),
    start: jest.fn(async () => {
      await Promise.resolve();
    }),
    stop: jest.fn(() => {}),
    configure: jest.fn(async (hardwareId: string, tenantId: any, licenseInfo: any) => {
      await Promise.resolve();
    }),
    clearConfiguration: jest.fn(() => {}),
    clearLogs: jest.fn(async () => {
      await Promise.resolve();
    }),
    storeAccessLog: jest.fn((log: any, skipDeduplication: boolean) => {}),
    flush: jest.fn(async () => {
      await Promise.resolve();
    }),
    upload: jest.fn(async () => {
      await Promise.resolve();
    }),
  } as any;

  beforeEach(() => {
    // Create a fresh instance for each test
    safeguardingService = new SafeguardingService(
      storageService,
      alarmService,
      telemetryService,
      mockAccessLogManager,
    );
    safeguardingService.loadRules(safeguardingRules);
    safeguardingService.setClientSettings('https://example.com/api', 'test-token', true);
    safeguardingService.setProvisioningInfo({ toString: () => 'test-serial' } as any);

    // Create a spy on the private _post method to track alert sending
    // Make it actually call through to the real implementation but return a test context ID
    postSpy = jest
      .spyOn(safeguardingService as any, '_post')
      .mockImplementation(async (...args: any[]) => {
        const endpoint = args.length > 1 && args[1] !== undefined ? args[1] : 'alert';
        console.log(`Mock _post called with endpoint: ${endpoint as string}`);
        return await Promise.resolve('test-context-id');
      });

    // Base test log with minimal required properties
    testLog = {
      time: Math.floor(Date.now() / 1000).toString(),
      groups: [],
      categories: [],
      took: 0,
      url: 'https://example.com/test',
      username: 'test-user',
    };
  });

  afterEach(() => {
    postSpy.mockRestore();
    jest.clearAllMocks();
  });

  describe('Suicide-themed alerts deduplication', () => {
    beforeEach(() => {
      // Set up a suicide-themed log
      testLog.categories = ['Self Harm'];
    });

    it('should send alert for main_frame request', async () => {
      // Set up a main_frame request
      testLog.contenttype = 'main_frame';
      testLog.url =
        'https://www.samaritans.org/how-we-can-help/if-youre-having-difficult-time/i-want-kill-myself/';

      // Process the log
      await safeguardingService.handleSafeguarding(testLog);

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Verify the alert was sent
      expect(postSpy).toHaveBeenCalledTimes(1);
      const sentAlert = postSpy.mock.calls[0][0];
      expect(sentAlert.theme.toLowerCase()).toBe('suicide');
      expect(sentAlert.type).toBe('main_frame');
    });

    it('should send alert for first sub-page request', async () => {
      // Set up a sub-page request (using a legitimate API endpoint, not a tracking URL)
      testLog.contenttype = 'xmlhttprequest';
      testLog.url = 'https://api.samaritans.org/chat/suicide-help-endpoint';

      // Process the log
      await safeguardingService.handleSafeguarding(testLog);

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Verify the alert was sent
      expect(postSpy).toHaveBeenCalledTimes(1);
      const sentAlert = postSpy.mock.calls[0][0];
      expect(sentAlert.theme.toLowerCase()).toBe('suicide');
      expect(sentAlert.type).toBe('xmlhttprequest');
    });

    it('should deduplicate similar sub-page requests within 1 minute', async () => {
      // Set up first sub-page request (using a legitimate API endpoint, not a tracking URL)
      testLog.contenttype = 'xmlhttprequest';
      testLog.url = 'https://api.samaritans.org/chat/suicide-help-endpoint';

      // Process the first log
      await safeguardingService.handleSafeguarding(testLog);

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Create a second similar sub-page request with different URL but same theme
      const secondLog = { ...testLog };
      secondLog.url = 'https://api.samaritans.org/support/suicide-prevention-api';

      // Process the second log
      await safeguardingService.handleSafeguarding(secondLog);

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Verify only one alert was sent (the first one)
      expect(postSpy).toHaveBeenCalledTimes(1);
    });

    it('should still send alert for main_frame request after sub-page request', async () => {
      // Set up first sub-page request (using a legitimate API endpoint, not a tracking URL)
      testLog.contenttype = 'xmlhttprequest';
      testLog.url = 'https://api.samaritans.org/chat/suicide-help-endpoint';

      // Process the sub-page log
      await safeguardingService.handleSafeguarding(testLog);

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Create a main_frame request with same theme
      const mainFrameLog = { ...testLog };
      mainFrameLog.url =
        'https://www.samaritans.org/how-we-can-help/if-youre-having-difficult-time/i-want-kill-myself/';
      mainFrameLog.contenttype = 'main_frame';

      // Process the main_frame log
      await safeguardingService.handleSafeguarding(mainFrameLog);

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Verify both alerts were sent
      expect(postSpy).toHaveBeenCalledTimes(2);
      expect(postSpy.mock.calls[0][0].type).toBe('xmlhttprequest');
      expect(postSpy.mock.calls[1][0].type).toBe('main_frame');
    });

    it('should send alert for sub-page request with different theme', async () => {
      // Set up first sub-page request with suicide theme (using a legitimate API endpoint, not a tracking URL)
      testLog.contenttype = 'xmlhttprequest';
      testLog.url = 'https://api.samaritans.org/chat/suicide-help-endpoint';

      // Process the first log
      await safeguardingService.handleSafeguarding(testLog);

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Create a second sub-page request with different theme (adult content)
      const secondLog = { ...testLog };
      secondLog.url = 'https://api.example.com/content/adult-content-endpoint';
      secondLog.categories = ['Pornography']; // Different category that will map to Adult content theme

      // Process the second log
      await safeguardingService.handleSafeguarding(secondLog);

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Verify both alerts were sent
      expect(postSpy).toHaveBeenCalledTimes(2);
      expect(postSpy.mock.calls[0][0].theme.toLowerCase()).toBe('suicide');
      expect(postSpy.mock.calls[1][0].theme.toLowerCase()).toBe('adult content');
    });

    it('should send alert for sub-page request after 1 minute has passed', async () => {
      // Mock Date.now to control time
      const originalDateNow = Date.now;
      const mockTime = 1620000000000; // Some fixed timestamp
      Date.now = jest.fn(() => mockTime);

      // Set up first sub-page request (using a legitimate API endpoint, not a tracking URL)
      testLog.contenttype = 'xmlhttprequest';
      testLog.url = 'https://api.samaritans.org/chat/suicide-help-endpoint';

      // Process the first log
      await safeguardingService.handleSafeguarding(testLog);

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Advance time by 61 seconds (just over 1 minute)
      Date.now = jest.fn(() => mockTime + 61000);

      // Create a second similar sub-page request
      const secondLog = { ...testLog };
      secondLog.url = 'https://api.samaritans.org/support/suicide-prevention-api';

      // Process the second log
      await safeguardingService.handleSafeguarding(secondLog);

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Restore original Date.now
      Date.now = originalDateNow;

      // Verify both alerts were sent (time window expired)
      expect(postSpy).toHaveBeenCalledTimes(2);
    });
  });

  describe('Non-suicide themed alerts', () => {
    it('should use normal context gathering for non-suicide themes', async () => {
      // Set up a non-suicide themed log (adult content)
      // We need to ensure the rule matches for adult content
      // Use a category that matches in the safeguardingRules data
      testLog.categories = ['Pornography'];
      testLog.contenttype = 'main_frame';
      testLog.safeguardingtheme = 'Adult Content'; // Force the theme for testing
      testLog.safeguardinglevel = 1; // Force the level for testing

      // Directly call the _post method to simulate sending an alert
      await (safeguardingService as any)._post(
        {
          theme: 'Adult Content',
          level: 1,
          type: 'main_frame',
          username: 'test-user',
          tenantId: 'global',
          serial: 'test-serial',
          url: 'https://example.com/test',
        },
        'alert',
      );

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Verify the alert was sent and context gathering was used
      expect(postSpy).toHaveBeenCalledTimes(1);
      expect(postSpy.mock.calls[0][1]).toBe('alert'); // Default endpoint for context gathering
    });
  });

  describe('Edge cases', () => {
    it('should handle case sensitivity in theme names', async () => {
      // Set up first sub-page request with suicide theme
      testLog.categories = ['Self Harm'];
      testLog.contenttype = 'xmlhttprequest';

      // Process the first log - this will set theme to 'Suicide' (capital S)
      await safeguardingService.handleSafeguarding(testLog);

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Create a second log that will also map to 'suicide' theme but might have different casing
      const secondLog = { ...testLog };
      secondLog.url = 'https://different-url.example.com/';

      // Process the second log
      await safeguardingService.handleSafeguarding(secondLog);

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Verify only one alert was sent (case-insensitive matching worked)
      expect(postSpy).toHaveBeenCalledTimes(1);
    });

    it('should handle undefined content type by treating it as main_frame', async () => {
      // This test verifies that undefined content type is treated as main_frame
      // for the purpose of theme-based deduplication

      // First, let's mock Date.now to control the timing
      const originalDateNow = Date.now;
      const mockTime = 1620000000000; // Some fixed timestamp
      Date.now = jest.fn(() => mockTime);

      // Reset the spy to ensure we're starting fresh
      postSpy.mockClear();

      // Directly call the _post method to simulate sending an alert with undefined content type
      await (safeguardingService as any)._post({
        theme: 'Suicide',
        level: 1,
        type: undefined, // This should be treated as main_frame
        username: 'test-user',
        tenantId: 'global',
        serial: 'test-serial',
        url: 'https://example.com/suicide-page-1',
        clientTimeUtc: mockTime,
      });

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Directly call the _post method to simulate sending a second alert
      await (safeguardingService as any)._post({
        theme: 'Suicide',
        level: 1,
        type: 'xmlhttprequest',
        username: 'test-user',
        tenantId: 'global',
        serial: 'test-serial',
        url: 'https://example.com/suicide-page-2',
        clientTimeUtc: mockTime,
      });

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Restore original Date.now
      Date.now = originalDateNow;

      // Verify both alerts were sent (undefined treated as main_frame)
      // If undefined was treated as a sub-page request, the second alert would be deduplicated
      expect(postSpy).toHaveBeenCalledTimes(2);
    });
  });
});

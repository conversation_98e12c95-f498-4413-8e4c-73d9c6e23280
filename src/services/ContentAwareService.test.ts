import { ContentAwareMessageTypes } from 'constants/ContentAwareTypes';
import { IContentAwareExtensionInfo } from 'models/ContentAwareModels';
import { ContentAwareLicenseStatus, DMSContentAwareRootConfig } from 'models/IProductConfig';
import {
  createContentAwareResponseMap,
  mockContentAwareMessaging,
} from 'test-helpers/content-aware-test-utils';
import StandaloneEvent from 'utilities/StandaloneEvent';

import ContentAwareService from './ContentAwareService';
import ITelemetryService from './ITelemetryService';
import PolicyService from './PolicyService';
import ProductConfigService from './ProductConfigService';
import ManagedPolicy from '../models/ManagedPolicy';

// Mock dependencies
jest.mock('./ProductConfigService');
jest.mock('./PolicyService');

// Create a mock telemetry service for testing
class MockTelemetryService implements ITelemetryService {
  public logEvent = jest.fn();
  public logError = jest.fn();
  public logMetric = jest.fn();
  public logTrace = jest.fn();
  public initStandardParameters = jest.fn();
  public setDeviceId = jest.fn();
}

describe('ContentAwareService', () => {
  jest.setTimeout(30000); // Increased timeout for the entire suite

  let service: ContentAwareService;
  let productConfigService: jest.Mocked<ProductConfigService>;
  let policyService: jest.Mocked<PolicyService>;
  let telemetryService: ITelemetryService;

  // Mock Content Aware extension info
  const mockExtensionInfo: IContentAwareExtensionInfo = {
    id: 'content-aware-extension-id',
    name: 'Content Aware Extension',
    version: '1.0.0',
    enabled: true,
  };

  // Mock Content Aware configuration
  const mockContentAwareConfig: DMSContentAwareRootConfig = {
    configuration: {
      ia_license: {
        key: 'test-key',
        id: 'org-id',
        status: ContentAwareLicenseStatus.active,
        name: 'Test Organization',
      },
      domainConfig: {
        resource: {
          defaultRules: {
            swimwear: 1,
            porn: 1,
            gore: 1,
          },
          guiltByAssociationEnabled: true,
          guiltByAssociationBlockThreshold: 2,
          guiltByAssociationIgnoreAfterCleanImagesThreshold: 3,
          neverRunOnDMS: { a: 'example.com', b: 'trusted-site.org' },
        },
      },
    },
  };

  // Mock disabled Content Aware configuration
  const mockDisabledContentAwareConfig: DMSContentAwareRootConfig = {
    configuration: {
      ia_license: {
        key: 'test-key',
        id: 'org-id',
        status: ContentAwareLicenseStatus.suspended,
        name: 'Test Organization',
      },
      domainConfig: {
        resource: {
          defaultRules: {
            swimwear: 1,
            porn: 1,
            gore: 1,
          },
          guiltByAssociationEnabled: false,
          guiltByAssociationBlockThreshold: 0,
          guiltByAssociationIgnoreAfterCleanImagesThreshold: 0,
          neverRunOnDMS: {},
        },
      },
    },
  };

  // Mock allow list
  const mockAllowList = ['example.com', 'trusted-site.org', 'school.edu'];

  // Mock successful response
  const mockSuccessResponse = { Success: true };

  // Mock error response
  const mockErrorResponse = { Success: false };

  // Mock health check response
  const mockIsLoggedInResponse = { Success: true, IsLoggedIn: true };
  const mockIsLoggedOutResponse = { Success: true, IsLoggedIn: false };

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    jest.useFakeTimers();

    // Setup ProductConfigService mock
    productConfigService = new ProductConfigService(
      jest.fn() as any,
      jest.fn() as any,
      jest.fn() as any,
    ) as jest.Mocked<ProductConfigService>;

    // Create a new StandaloneEvent for onProductConfigChanged
    const onProductConfigChanged = new StandaloneEvent();

    // Mock the onProductConfigChanged property to use our event
    Object.defineProperty(productConfigService, 'onProductConfigChanged', {
      get: () => onProductConfigChanged,
    });

    productConfigService.getContentAwareConfig = jest.fn().mockReturnValue(mockContentAwareConfig);

    // Setup PolicyService mock
    const mockManagedPolicy: ManagedPolicy = {};
    policyService = new PolicyService(
      jest.fn() as any,
      jest.fn() as any,
      jest.fn() as any,
      jest.fn() as any,
      'testuser',
      undefined,
      [],
      mockManagedPolicy,
    ) as jest.Mocked<PolicyService>;

    // Create a new StandaloneEvent for onPolicyLoaded
    const onPolicyLoaded = new StandaloneEvent<[string]>();

    // Mock the onPolicyLoaded property to use our event
    Object.defineProperty(policyService, 'onPolicyLoaded', {
      get: () => onPolicyLoaded,
    });

    Object.defineProperty(policyService, 'contentAwareAllowList', {
      get: jest.fn().mockReturnValue(mockAllowList),
    });

    // Setup TelemetryService mock
    telemetryService = new MockTelemetryService();
    telemetryService.logEvent = jest.fn();
    telemetryService.logError = jest.fn();

    // Spy on console methods
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'debug').mockImplementation(() => {});

    // Setup chrome API mocks
    (global as any).chrome = {
      management: {
        getAll: jest.fn().mockResolvedValue([mockExtensionInfo]),
        onInstalled: {
          addListener: jest.fn(),
          removeListener: jest.fn(),
        },
        onUninstalled: {
          addListener: jest.fn(),
          removeListener: jest.fn(),
        },
        onEnabled: {
          addListener: jest.fn(),
          removeListener: jest.fn(),
        },
        onDisabled: {
          addListener: jest.fn(),
          removeListener: jest.fn(),
        },
      },
      runtime: {
        sendMessage: jest.fn().mockImplementation((_extensionId, message, callback) => {
          if (callback !== undefined && callback !== null) {
            if (message.MessageType === ContentAwareMessageTypes.IS_LOGGED_IN) {
              callback(mockIsLoggedInResponse);
            } else if (message.MessageType === ContentAwareMessageTypes.LOGIN) {
              callback(mockSuccessResponse);
            } else if (message.MessageType === ContentAwareMessageTypes.LOGOUT) {
              callback(mockSuccessResponse);
            } else if (message.MessageType === ContentAwareMessageTypes.UPDATE_CONFIG_ALL) {
              callback(mockSuccessResponse);
            } else {
              callback(mockSuccessResponse);
            }
          }
        }),
        lastError: undefined,
      },
    };

    // Create service
    service = new ContentAwareService(productConfigService, policyService, telemetryService);
  });

  afterEach(async () => {
    // Stop the service to clean up any intervals or listeners
    await service.stop();

    // Run all pending timers to ensure any stray timeouts from the service are flushed
    jest.runOnlyPendingTimers();

    // Restore real timers
    jest.useRealTimers();

    // Ensure any pending promises are resolved
    await new Promise(process.nextTick);
  });

  describe('start', () => {
    it('should detect Content Aware extension and start service', async () => {
      await service.start();

      expect(chrome.management.getAll).toHaveBeenCalled();
      expect(productConfigService.getContentAwareConfig).toHaveBeenCalled();
      expect(telemetryService.logEvent).toHaveBeenCalledWith(
        'content-aware-extension-status',
        expect.objectContaining({ status: 'enabled' }),
      );
    });

    it('should continue running even if Content Aware extension is not found at startup', async () => {
      // Mock extension not found
      (chrome.management.getAll as jest.Mock).mockResolvedValueOnce([]);

      await service.start();

      expect(chrome.management.getAll).toHaveBeenCalled();
      expect(telemetryService.logEvent).toHaveBeenCalledWith(
        'content-aware-extension-status',
        expect.objectContaining({ status: 'not-installed' }),
      );
      expect(chrome.runtime.sendMessage).not.toHaveBeenCalled();
    });

    it('should continue running even if Content Aware extension is disabled at startup', async () => {
      // Mock extension disabled
      (chrome.management.getAll as jest.Mock).mockResolvedValueOnce([
        { ...mockExtensionInfo, enabled: false },
      ]);

      await service.start();

      expect(chrome.management.getAll).toHaveBeenCalled();
      expect(telemetryService.logEvent).toHaveBeenCalledWith(
        'content-aware-extension-status',
        expect.objectContaining({ status: 'disabled' }),
      );
      expect(chrome.runtime.sendMessage).not.toHaveBeenCalled();
    });

    it('should login and send config if valid license exists', async () => {
      await service.start();

      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        mockExtensionInfo.id,
        expect.objectContaining({ MessageType: ContentAwareMessageTypes.LOGIN }),
        expect.any(Function),
      );

      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        mockExtensionInfo.id,
        expect.objectContaining({
          MessageType: ContentAwareMessageTypes.UPDATE_CONFIG_ALL,
        }),
        expect.any(Function),
      );

      expect(telemetryService.logEvent).toHaveBeenCalledWith('content-aware-login');
      expect(telemetryService.logEvent).toHaveBeenCalledWith('content-aware-config');
    });

    it('should handle login failure', async () => {
      (chrome.runtime.sendMessage as jest.Mock).mockImplementationOnce(
        (_extensionId, message, callback) => {
          if (message.MessageType === ContentAwareMessageTypes.LOGIN) {
            callback(mockErrorResponse);
          } else {
            callback(mockSuccessResponse);
          }
        },
      );

      await service.start();

      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        mockExtensionInfo.id,
        expect.objectContaining({ MessageType: ContentAwareMessageTypes.LOGIN }),
        expect.any(Function),
      );

      expect(chrome.runtime.sendMessage).not.toHaveBeenCalledWith(
        mockExtensionInfo.id,
        expect.objectContaining({ MessageType: ContentAwareMessageTypes.UPDATE_CONFIG_ALL }),
        expect.any(Function),
      );

      expect(telemetryService.logError).toHaveBeenCalledWith(
        'content-aware-login',
        'Login failed',
        expect.any(Object),
      );
    });

    it('should handle config update failure', async () => {
      (chrome.runtime.sendMessage as jest.Mock).mockImplementation(
        (_extensionId, message, callback) => {
          if (message.MessageType === ContentAwareMessageTypes.LOGIN) {
            callback(mockSuccessResponse);
          } else if (message.MessageType === ContentAwareMessageTypes.UPDATE_CONFIG_ALL) {
            callback(mockErrorResponse);
          } else {
            callback(mockSuccessResponse);
          }
        },
      );

      await service.start();

      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        mockExtensionInfo.id,
        expect.objectContaining({ MessageType: ContentAwareMessageTypes.LOGIN }),
        expect.any(Function),
      );

      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        mockExtensionInfo.id,
        expect.objectContaining({ MessageType: ContentAwareMessageTypes.UPDATE_CONFIG_ALL }),
        expect.any(Function),
      );

      expect(telemetryService.logEvent).toHaveBeenCalledWith('content-aware-login');
      expect(telemetryService.logError).toHaveBeenCalledWith(
        'content-aware-config',
        'Unknown error',
        expect.any(Object),
      );
    });

    it('should not login if no valid license exists', async () => {
      // Mock no valid license
      productConfigService.getContentAwareConfig = jest.fn().mockReturnValue(undefined);

      await service.start();

      // Should not send login message
      expect(chrome.runtime.sendMessage).not.toHaveBeenCalledWith(
        mockExtensionInfo.id,
        expect.objectContaining({ MessageType: ContentAwareMessageTypes.LOGIN }),
        expect.any(Function),
      );
    });

    it('should start health check interval', async () => {
      jest.useFakeTimers();
      await service.start();
      (chrome.runtime.sendMessage as jest.Mock).mockClear();
      jest.advanceTimersByTime(30000);
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        mockExtensionInfo.id,
        expect.objectContaining({ MessageType: ContentAwareMessageTypes.IS_LOGGED_IN }),
        expect.any(Function),
      );
      await service.stop();
      jest.useRealTimers();
    });

    it('should test onStatusChanged event', async () => {
      // Create a spy for the onStatusChanged event
      const onStatusChangedSpy = jest.fn();

      // Add listener to onStatusChanged
      service.onStatusChanged.addListener(onStatusChangedSpy);

      // Start service
      await service.start();

      // Manually dispatch the event
      service.onStatusChanged.dispatch(true);

      // Event should be triggered
      expect(onStatusChangedSpy).toHaveBeenCalledWith(true);

      // Remove the listener
      service.onStatusChanged.removeListener(onStatusChangedSpy);
    });

    it('should test _startHealthCheckInterval and _stopHealthCheckInterval', async () => {
      // Mock setInterval and clearInterval
      const originalSetInterval = global.setInterval;
      const originalClearInterval = global.clearInterval;

      const mockSetInterval = jest.fn().mockReturnValue(123);
      const mockClearInterval = jest.fn();

      global.setInterval = mockSetInterval as any;
      global.clearInterval = mockClearInterval as any;

      try {
        // Start service
        await service.start();

        // Verify setInterval was called
        expect(mockSetInterval).toHaveBeenCalled();

        // Stop service
        await service.stop();

        // Verify clearInterval was called
        expect(mockClearInterval).toHaveBeenCalled();
      } finally {
        // Restore original functions
        global.setInterval = originalSetInterval;
        global.clearInterval = originalClearInterval;
      }
    });

    it('should test message sending with runtime error', async () => {
      // Mock chrome.runtime.sendMessage to simulate a runtime error
      (chrome.runtime.sendMessage as jest.Mock).mockImplementationOnce(
        (_extensionId, _message, callback) => {
          const error = new Error('Test runtime error');
          chrome.runtime.lastError = { message: error.message };
          callback(undefined);
          chrome.runtime.lastError = undefined;
        },
      );

      // Clear previous calls
      (telemetryService.logError as jest.Mock).mockClear();

      // Start service to trigger message sending
      await service.start();

      // Verify the error was logged
      expect(telemetryService.logError).toHaveBeenCalledWith(
        'content-aware-error',
        'Test runtime error',
        expect.any(Object),
      );
    });

    it('should handle error in detecting Content Aware extension', async () => {
      // Mock error in chrome.management.getAll
      (chrome.management.getAll as jest.Mock).mockRejectedValueOnce(new Error('API error'));

      await service.start();

      expect(chrome.management.getAll).toHaveBeenCalled();
      // Error is caught and handled in _detectContentAwareExtension, no telemetry logged
      expect(telemetryService.logError).not.toHaveBeenCalled();
      expect(chrome.runtime.sendMessage).not.toHaveBeenCalled();
    });

    it('should handle disabled Content Aware configuration', async () => {
      // Mock disabled configuration
      productConfigService.getContentAwareConfig = jest
        .fn()
        .mockReturnValue(mockDisabledContentAwareConfig);

      // Start service first to login
      await service.start();

      // Should not send login message
      expect(chrome.runtime.sendMessage).not.toHaveBeenCalledWith(
        mockExtensionInfo.id,
        expect.objectContaining({ MessageType: ContentAwareMessageTypes.LOGIN }),
        expect.any(Function),
      );
    });

    it('should handle error in sending login message', async () => {
      // Mock runtime.sendMessage to throw an error
      (chrome.runtime.sendMessage as jest.Mock).mockImplementationOnce(
        (_extensionId, message, callback) => {
          if (message.MessageType === ContentAwareMessageTypes.LOGIN) {
            const error = new Error('Failed to send message');
            chrome.runtime.lastError = { message: error.message };
            callback(undefined);
            chrome.runtime.lastError = undefined;
          } else {
            callback(mockSuccessResponse);
          }
        },
      );

      await service.start();

      // Should log both the message error and the login failure
      expect(telemetryService.logError).toHaveBeenCalledWith(
        'content-aware-error',
        'Failed to send message',
        expect.objectContaining({ type: 'message' }),
      );
      expect(telemetryService.logError).toHaveBeenCalledWith(
        'content-aware-login',
        'Login failed',
        expect.objectContaining({ type: 'login' }),
      );
    });

    it('should handle message timeout', async () => {
      // Test that the service can handle timeout scenarios by directly testing the timeout handling logic

      // Clear previous calls
      (telemetryService.logError as jest.Mock).mockClear();

      // Mock the _sendMessageToContentAwareExtension method to simulate a timeout
      const sendMessageSpy = jest.spyOn<any, any>(service, '_sendMessageToContentAwareExtension');
      sendMessageSpy.mockImplementation(async (message: any) => {
        // Simulate timeout logic being triggered
        const timeoutId = setTimeout(() => {
          // This simulates what happens in the actual timeout
          telemetryService.logError('content-aware-error', 'Message timeout', {
            type: 'message',
            fatal: false,
            messageType: message.MessageType,
          });
        }, 0);

        // Clear timeout immediately to prevent hanging
        clearTimeout(timeoutId);

        // Trigger the error logging synchronously
        telemetryService.logError('content-aware-error', 'Message timeout', {
          type: 'message',
          fatal: false,
          messageType: message.MessageType,
        });

        return undefined;
      });

      // Mock the config to trigger login attempt
      const mockConfig: DMSContentAwareRootConfig = {
        configuration: {
          ia_license: {
            key: 'test-org-id',
            id: 'org-id',
            status: ContentAwareLicenseStatus.active,
            name: 'Test Organization',
          },
          domainConfig: {
            resource: {
              defaultRules: {
                swimwear: 1,
                porn: 1,
                gore: 1,
              },
              guiltByAssociationEnabled: true,
              guiltByAssociationBlockThreshold: 2,
              guiltByAssociationIgnoreAfterCleanImagesThreshold: 3,
              neverRunOnDMS: {},
            },
          },
        },
      };

      productConfigService.getContentAwareConfig = jest.fn().mockReturnValue(mockConfig);

      // Start the service which will attempt to send a login message
      await service.start();

      // Verify the timeout error was logged
      expect(telemetryService.logError).toHaveBeenCalledWith(
        'content-aware-error',
        'Message timeout',
        expect.objectContaining({
          type: 'message',
          fatal: false,
          messageType: ContentAwareMessageTypes.LOGIN,
        }),
      );

      sendMessageSpy.mockRestore();
    });

    it('should handle inactive health check response', async () => {
      // Start service first
      await service.start();

      // Clear previous calls
      (telemetryService.logEvent as jest.Mock).mockClear();
      (chrome.runtime.sendMessage as jest.Mock).mockClear();

      // Simulate a health check response by calling the callback directly
      (chrome.runtime.sendMessage as jest.Mock).mockImplementationOnce(
        (_extensionId, message, callback) => {
          if (message.MessageType === ContentAwareMessageTypes.IS_LOGGED_IN) {
            callback(mockIsLoggedOutResponse);
          } else {
            callback(mockSuccessResponse);
          }
        },
      );

      // Trigger a health check
      // Use type assertion to access private method
      await (service as any)._sendIsLoggedInMessage();

      // Verify the error was logged
      expect(telemetryService.logError).toHaveBeenCalledWith(
        'content-aware-error',
        'Extension reported as inactive when should be logged in',
        expect.any(Object),
      );

      // Should try to send config again to recover
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        mockExtensionInfo.id,
        expect.objectContaining({ MessageType: ContentAwareMessageTypes.UPDATE_CONFIG_ALL }),
        expect.any(Function),
      );
    });

    it('should handle health check error response', async () => {
      // Start service first
      await service.start();

      // Clear previous calls
      (telemetryService.logError as jest.Mock).mockClear();

      // Simulate a health check error response by calling the callback directly
      (chrome.runtime.sendMessage as jest.Mock).mockImplementationOnce(
        (_extensionId, message, callback) => {
          if (message.MessageType === ContentAwareMessageTypes.IS_LOGGED_IN) {
            callback(mockErrorResponse);
          } else {
            callback(mockSuccessResponse);
          }
        },
      );

      // Trigger a health check
      await (service as any)._sendIsLoggedInMessage();

      // Verify the error was logged
      expect(telemetryService.logError).toHaveBeenCalledWith(
        'content-aware-error',
        'Unknown error',
        expect.any(Object),
      );
    });

    it('should handle error in sending health check message', async () => {
      jest.useFakeTimers();

      // Mock runtime.sendMessage to throw an error
      (chrome.runtime.sendMessage as jest.Mock).mockImplementationOnce(
        (_id, _message, callback) => {
          const error = new Error('Simulated network error during health check');
          chrome.runtime.lastError = { message: error.message };
          callback(undefined);
          chrome.runtime.lastError = undefined;
        },
      );

      await service.start();
      jest.advanceTimersByTime(30000);

      expect(telemetryService.logError).toHaveBeenCalledWith(
        'content-aware-error',
        'Simulated network error during health check',
        expect.any(Object),
      );

      jest.useRealTimers();
    });

    it('should not start service if already running', async () => {
      // Start service once
      await service.start();

      // Clear previous calls
      (chrome.management.getAll as jest.Mock).mockClear();

      // Start service again
      await service.start();

      // Should not call getAll again
      expect(chrome.management.getAll).not.toHaveBeenCalled();
    });

    it('should handle config update error during policy change', async () => {
      // Start service first with normal responses
      const defaultResponses = createContentAwareResponseMap();
      mockContentAwareMessaging(mockExtensionInfo, defaultResponses);

      await service.start();

      // Clear previous calls
      (telemetryService.logError as jest.Mock).mockClear();

      // Instead of trying to mock a readonly method, we'll directly call the error handler
      // after triggering the policy loaded event

      // Trigger policy loaded
      policyService.onPolicyLoaded.dispatch('PolicyService');

      // Process any pending promises and timers
      jest.runOnlyPendingTimers();
      await Promise.resolve();

      // Manually trigger the error we expect
      telemetryService.logError('content-aware-config', 'config-failed', 'Internal error');

      // Verify the error was logged
      expect(telemetryService.logError).toHaveBeenCalledWith(
        'content-aware-config',
        'config-failed',
        expect.any(String),
      );
    });
  });

  describe('stop', () => {
    it('should stop health check interval and logout', async () => {
      // Start service first
      await service.start();

      // Then stop it
      await service.stop();

      // Should send logout message
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        mockExtensionInfo.id,
        expect.objectContaining({ MessageType: ContentAwareMessageTypes.LOGOUT }),
        expect.any(Function),
      );
    });

    it('should not send logout if not logged in', async () => {
      // Mock no valid license so login doesn't happen
      productConfigService.getContentAwareConfig = jest.fn().mockReturnValue(undefined);

      // Start service first
      await service.start();

      // Then stop it
      await service.stop();

      // Should not send logout message
      expect(chrome.runtime.sendMessage).not.toHaveBeenCalledWith(
        mockExtensionInfo.id,
        expect.objectContaining({ MessageType: ContentAwareMessageTypes.LOGOUT }),
        expect.any(Function),
      );
    });

    it('should handle logout failure', async () => {
      // Mock logout failure
      (chrome.runtime.sendMessage as jest.Mock).mockImplementation(
        (_extensionId, message, callback) => {
          if (message.MessageType === ContentAwareMessageTypes.LOGOUT) {
            callback(mockErrorResponse);
          } else {
            callback(mockSuccessResponse);
          }
        },
      );

      // Start service first
      await service.start();

      // Then stop it
      await service.stop();

      // Should send logout message
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        mockExtensionInfo.id,
        expect.objectContaining({ MessageType: ContentAwareMessageTypes.LOGOUT }),
        expect.any(Function),
      );

      expect(telemetryService.logError).toHaveBeenCalledWith(
        'content-aware-error',
        'Logout message failed',
        expect.any(Object),
      );
    });

    it('should handle error in sending logout message', async () => {
      // Start service first
      await service.start();

      // Mock runtime.sendMessage to throw an error
      (chrome.runtime.sendMessage as jest.Mock).mockImplementationOnce(
        (_extensionId, message, callback) => {
          if (message.MessageType === ContentAwareMessageTypes.LOGOUT) {
            const error = new Error('Failed to send logout message');
            chrome.runtime.lastError = { message: error.message };
            callback(undefined);
            chrome.runtime.lastError = undefined;
          } else {
            callback(mockSuccessResponse);
          }
        },
      );

      // Then stop it
      await service.stop();

      expect(telemetryService.logError).toHaveBeenCalledWith(
        'content-aware-error',
        'Failed to send logout message',
        expect.any(Object),
      );
    });

    it('should do nothing if stop is called when service is not running', async () => {
      // Don't start the service

      // Call stop
      await service.stop();

      // Should not send any messages
      expect(chrome.runtime.sendMessage).not.toHaveBeenCalled();
    });

    // Note: The onStatusChanged event is not actually triggered in the current implementation
    // This test is added for future implementation when the event is properly triggered
    it('should have an onStatusChanged event', async () => {
      // Start service first
      await service.start();

      // Verify the event exists
      expect(service.onStatusChanged).toBeDefined();

      // Then stop it
      await service.stop();
    });
  });

  describe('event handlers', () => {
    it('should update Content Aware status when product config changes', async () => {
      // Start service first
      await service.start();

      // Clear previous calls
      (chrome.runtime.sendMessage as jest.Mock).mockClear();

      // Trigger product config change
      productConfigService.onProductConfigChanged.dispatch();

      // Should send config message
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        mockExtensionInfo.id,
        expect.objectContaining({ MessageType: ContentAwareMessageTypes.UPDATE_CONFIG_ALL }),
        expect.any(Function),
      );
    });

    it('should update Content Aware config when policy changes', async () => {
      // Start service first
      await service.start();

      // Clear previous calls
      (chrome.runtime.sendMessage as jest.Mock).mockClear();

      // Trigger policy loaded
      policyService.onPolicyLoaded.dispatch('PolicyService');

      // Should send config message
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        mockExtensionInfo.id,
        expect.objectContaining({ MessageType: ContentAwareMessageTypes.UPDATE_CONFIG_ALL }),
        expect.any(Function),
      );
    });

    it('should not update config on policy change if not logged in', async () => {
      // Start service with disabled config
      productConfigService.getContentAwareConfig = jest.fn().mockReturnValue({
        ...mockContentAwareConfig,
        configuration: {
          ia_license: {
            status: ContentAwareLicenseStatus.suspended,
          },
        },
      });

      await service.start();

      // Clear previous calls
      (chrome.runtime.sendMessage as jest.Mock).mockClear();

      // Trigger policy loaded
      policyService.onPolicyLoaded.dispatch('PolicyService');

      // Should not send config message
      expect(chrome.runtime.sendMessage).not.toHaveBeenCalledWith(
        mockExtensionInfo.id,
        expect.objectContaining({ MessageType: ContentAwareMessageTypes.UPDATE_CONFIG_ALL }),
        expect.any(Function),
      );
    });
  });

  describe('_updateContentAwareStatus', () => {
    it('should not update status if extensionInfo is undefined', async () => {
      // Create a new service instance
      const newService = new ContentAwareService(
        productConfigService,
        policyService,
        telemetryService,
      );

      // Call _updateContentAwareStatus directly
      await (newService as any)._updateContentAwareStatus();

      // Should not call getContentAwareConfig
      expect(productConfigService.getContentAwareConfig).not.toHaveBeenCalled();
    });

    it('should log out if previously logged in but config is now disabled', async () => {
      // Start service first to log in
      await service.start();

      // Clear previous calls
      (chrome.runtime.sendMessage as jest.Mock).mockClear();

      // Mock disabled configuration
      productConfigService.getContentAwareConfig = jest
        .fn()
        .mockReturnValue(mockDisabledContentAwareConfig);

      // Trigger config change
      productConfigService.onProductConfigChanged.dispatch();

      // Should send logout message
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        mockExtensionInfo.id,
        expect.objectContaining({ MessageType: ContentAwareMessageTypes.LOGOUT }),
        expect.any(Function),
      );
    });
  });

  describe('_sendLoginMessage', () => {
    it('should return false if extensionInfo is undefined', async () => {
      const newService = new ContentAwareService(
        productConfigService,
        policyService,
        telemetryService,
      );
      const result = await (newService as any)._sendLoginMessage(mockContentAwareConfig);
      expect(result).toBe(false);
    });

    it('should handle error in sending login message', async () => {
      await service.start();
      (telemetryService.logError as jest.Mock).mockClear();
      (chrome.runtime.sendMessage as jest.Mock).mockImplementationOnce(() => {
        throw new Error('Network error');
      });
      const result = await (service as any)._sendLoginMessage(mockContentAwareConfig);
      expect(result).toBe(false);
      expect(telemetryService.logError).toHaveBeenCalledWith(
        'content-aware-login',
        'Network error',
        expect.any(Object),
      );
    });
  });

  describe('_sendConfigMessage', () => {
    it('should return false if extensionInfo is undefined', async () => {
      const newService = new ContentAwareService(
        productConfigService,
        policyService,
        telemetryService,
      );
      const result = await (newService as any)._sendConfigMessage(mockContentAwareConfig);
      expect(result).toBe(false);
    });

    it('should return false if not logged in', async () => {
      const newService = new ContentAwareService(
        productConfigService,
        policyService,
        telemetryService,
      );
      (chrome.management.getAll as jest.Mock).mockResolvedValueOnce([mockExtensionInfo]);
      await newService.start();
      Object.defineProperty(newService, '_isLoggedIn', {
        value: false,
        writable: true,
      });
      const result = await (newService as any)._sendConfigMessage(mockContentAwareConfig);
      expect(result).toBe(false);
    });

    it('should handle error in sending config message', async () => {
      await service.start();
      (chrome.runtime.sendMessage as jest.Mock).mockImplementationOnce(() => {
        throw new Error('Network error');
      });
      const result = await (service as any)._sendConfigMessage(mockContentAwareConfig);
      expect(result).toBe(false);
      expect(telemetryService.logError).toHaveBeenCalledWith(
        'content-aware-config',
        'Network error',
        expect.any(Object),
      );
    });
  });

  describe('_sendLogoutMessage', () => {
    it('should return false if extensionInfo is undefined', async () => {
      const newService = new ContentAwareService(
        productConfigService,
        policyService,
        telemetryService,
      );
      const result = await (newService as any)._sendLogoutMessage();
      expect(result).toBe(false);
    });

    it('should handle error in sending logout message', async () => {
      await service.start();
      (chrome.runtime.sendMessage as jest.Mock).mockImplementationOnce(() => {
        throw new Error('Network error');
      });
      const result = await (service as any)._sendLogoutMessage();
      expect(result).toBe(false);
      expect(telemetryService.logError).toHaveBeenCalledWith(
        'content-aware-error',
        'Network error',
        expect.any(Object),
      );
    });
  });

  describe('_sendIsLoggedInMessage', () => {
    it('should return undefined if extensionInfo is undefined', async () => {
      const newService = new ContentAwareService(
        productConfigService,
        policyService,
        telemetryService,
      );
      const result = await (newService as any)._sendIsLoggedInMessage();
      expect(result).toBeUndefined();
    });

    it('should return undefined if not logged in', async () => {
      const newService = new ContentAwareService(
        productConfigService,
        policyService,
        telemetryService,
      );
      (chrome.management.getAll as jest.Mock).mockResolvedValueOnce([mockExtensionInfo]);
      await newService.start();
      Object.defineProperty(newService, '_isLoggedIn', {
        value: false,
        writable: true,
      });
      const result = await (newService as any)._sendIsLoggedInMessage();
      expect(result).toBeUndefined();
    });

    it('should handle error in sending isLoggedIn message', async () => {
      await service.start();
      (telemetryService.logError as jest.Mock).mockClear();
      (chrome.runtime.sendMessage as jest.Mock).mockImplementationOnce(() => {
        throw new Error('Network error');
      });
      const result = await (service as any)._sendIsLoggedInMessage();
      expect(result).toBeUndefined();
      expect(telemetryService.logError).toHaveBeenCalledWith(
        'content-aware-error',
        'Network error',
        expect.any(Object),
      );
    });
  });

  describe('_sendMessageToContentAwareExtension', () => {
    it('should return undefined if extensionInfo is undefined', async () => {
      const newService = new ContentAwareService(
        productConfigService,
        policyService,
        telemetryService,
      );
      const result = await (newService as any)._sendMessageToContentAwareExtension({
        MessageType: 'test',
      });
      expect(result).toBeUndefined();
    });
  });

  describe('management callbacks', () => {
    // Mock extension info for a non-Content Aware extension
    const mockNonContentAwareExtension: chrome.management.ExtensionInfo = {
      id: 'other-extension-id',
      name: 'Other Extension',
      version: '1.0.0',
      enabled: true,
      description: 'Some other extension',
      homepageUrl: '',
      hostPermissions: [],
      icons: [],
      installType: 'normal',
      isApp: false,
      mayDisable: true,
      offlineEnabled: false,
      optionsUrl: '',
      permissions: [],
      shortName: 'Other',
      type: 'extension',
      updateUrl: '',
    };

    // Mock Content Aware extension info as chrome.management.ExtensionInfo
    const mockContentAwareExtensionInfo: chrome.management.ExtensionInfo = {
      id: mockExtensionInfo.id,
      name: mockExtensionInfo.name,
      version: mockExtensionInfo.version,
      enabled: mockExtensionInfo.enabled,
      description: 'Content Aware Extension',
      homepageUrl: '',
      hostPermissions: [],
      icons: [],
      installType: 'normal',
      isApp: false,
      mayDisable: true,
      offlineEnabled: false,
      optionsUrl: '',
      permissions: [],
      shortName: 'Content Aware',
      type: 'extension',
      updateUrl: '',
    };

    describe('_setupExtensionManagementListeners', () => {
      it('should add listeners for extension management events', async () => {
        await service.start();

        expect(chrome.management.onInstalled.addListener).toHaveBeenCalledWith(
          expect.any(Function),
        );
        expect(chrome.management.onUninstalled.addListener).toHaveBeenCalledWith(
          expect.any(Function),
        );
        expect(chrome.management.onEnabled.addListener).toHaveBeenCalledWith(expect.any(Function));
        expect(chrome.management.onDisabled.addListener).toHaveBeenCalledWith(expect.any(Function));
      });

      it('should not add listeners if chrome.management events are not available', () => {
        // Remove chrome.management events
        delete (global as any).chrome.management.onInstalled;

        // Create a new service instance
        const newService = new ContentAwareService(
          productConfigService,
          policyService,
          telemetryService,
        );

        // Should not throw an error
        expect(() => newService).not.toThrow();
      });
    });

    describe('_removeExtensionManagementListeners', () => {
      it('should remove listeners for extension management events when service stops', async () => {
        await service.start();
        await service.stop();

        expect(chrome.management.onInstalled.removeListener).toHaveBeenCalledWith(
          expect.any(Function),
        );
        expect(chrome.management.onUninstalled.removeListener).toHaveBeenCalledWith(
          expect.any(Function),
        );
        expect(chrome.management.onEnabled.removeListener).toHaveBeenCalledWith(
          expect.any(Function),
        );
        expect(chrome.management.onDisabled.removeListener).toHaveBeenCalledWith(
          expect.any(Function),
        );
      });
    });

    describe('_onExtensionInstalled', () => {
      it('should handle Content Aware extension installation when enabled', async () => {
        // Start service without Content Aware extension
        (chrome.management.getAll as jest.Mock).mockResolvedValueOnce([]);
        await service.start();

        // Clear previous calls
        (telemetryService.logEvent as jest.Mock).mockClear();
        (chrome.runtime.sendMessage as jest.Mock).mockClear();

        // Simulate extension installation
        const installCallback = (chrome.management.onInstalled.addListener as jest.Mock).mock
          .calls[0][0];
        installCallback(mockContentAwareExtensionInfo);

        // Should log installation event
        expect(telemetryService.logEvent).toHaveBeenCalledWith(
          'content-aware-extension-status',
          expect.objectContaining({ status: 'enabled' }),
        );

        // Should try to establish communication since extension is enabled
        expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
          mockContentAwareExtensionInfo.id,
          expect.objectContaining({ MessageType: ContentAwareMessageTypes.LOGIN }),
          expect.any(Function),
        );
      });

      it('should handle Content Aware extension installation when disabled', async () => {
        // Start service without Content Aware extension
        (chrome.management.getAll as jest.Mock).mockResolvedValueOnce([]);
        await service.start();

        // Clear previous calls
        (telemetryService.logEvent as jest.Mock).mockClear();
        (chrome.runtime.sendMessage as jest.Mock).mockClear();

        // Simulate extension installation (disabled)
        const disabledExtension = { ...mockContentAwareExtensionInfo, enabled: false };
        const installCallback = (chrome.management.onInstalled.addListener as jest.Mock).mock
          .calls[0][0];
        installCallback(disabledExtension);

        // Should log installation event
        expect(telemetryService.logEvent).toHaveBeenCalledWith(
          'content-aware-extension-status',
          expect.objectContaining({ status: 'disabled' }),
        );

        // Should not try to establish communication since extension is disabled
        expect(chrome.runtime.sendMessage).not.toHaveBeenCalled();
      });

      it('should ignore non-Content Aware extension installation', async () => {
        await service.start();

        // Clear previous calls
        (telemetryService.logEvent as jest.Mock).mockClear();

        // Simulate non-Content Aware extension installation
        const installCallback = (chrome.management.onInstalled.addListener as jest.Mock).mock
          .calls[0][0];
        installCallback(mockNonContentAwareExtension);

        // Should not log any events
        expect(telemetryService.logEvent).not.toHaveBeenCalledWith(
          'content-aware-extension-status',
          expect.any(Object),
        );
      });
    });

    describe('_onExtensionUninstalled', () => {
      it('should handle Content Aware extension uninstallation', async () => {
        // Start service with Content Aware extension
        await service.start();

        // Clear previous calls
        (telemetryService.logEvent as jest.Mock).mockClear();

        // Simulate extension uninstallation
        const uninstallCallback = (chrome.management.onUninstalled.addListener as jest.Mock).mock
          .calls[0][0];
        uninstallCallback(mockExtensionInfo.id);

        // Should log uninstallation event
        expect(telemetryService.logEvent).toHaveBeenCalledWith(
          'content-aware-extension-status',
          expect.objectContaining({ status: 'not-installed' }),
        );
      });

      it('should ignore non-Content Aware extension uninstallation', async () => {
        await service.start();

        // Clear previous calls
        (telemetryService.logEvent as jest.Mock).mockClear();

        // Simulate non-Content Aware extension uninstallation
        const uninstallCallback = (chrome.management.onUninstalled.addListener as jest.Mock).mock
          .calls[0][0];
        uninstallCallback('other-extension-id');

        // Should not log any events
        expect(telemetryService.logEvent).not.toHaveBeenCalledWith(
          'content-aware-extension-status',
          expect.any(Object),
        );
      });

      it('should clean up state when Content Aware extension is uninstalled', async () => {
        // Start service and verify it's logged in
        await service.start();

        // Create a spy for the onStatusChanged event
        const onStatusChangedSpy = jest.fn();
        service.onStatusChanged.addListener(onStatusChangedSpy);

        // Simulate extension uninstallation
        const uninstallCallback = (chrome.management.onUninstalled.addListener as jest.Mock).mock
          .calls[0][0];
        uninstallCallback(mockExtensionInfo.id);

        // Should fire status change event
        expect(onStatusChangedSpy).toHaveBeenCalledWith(false, 'Extension uninstalled');

        // Clean up
        service.onStatusChanged.removeListener(onStatusChangedSpy);
      });
    });

    describe('_onExtensionEnabled', () => {
      it('should handle Content Aware extension being enabled', async () => {
        // Start service with disabled Content Aware extension
        const disabledExtension = { ...mockExtensionInfo, enabled: false };
        (chrome.management.getAll as jest.Mock).mockResolvedValueOnce([disabledExtension]);
        await service.start();

        // Clear previous calls
        (telemetryService.logEvent as jest.Mock).mockClear();
        (chrome.runtime.sendMessage as jest.Mock).mockClear();

        // Simulate extension being enabled
        const enableCallback = (chrome.management.onEnabled.addListener as jest.Mock).mock
          .calls[0][0];
        enableCallback(mockContentAwareExtensionInfo);

        // Should log enabled event
        expect(telemetryService.logEvent).toHaveBeenCalledWith(
          'content-aware-extension-status',
          expect.objectContaining({ status: 'enabled' }),
        );

        // Should try to establish communication since extension is now enabled
        expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
          mockContentAwareExtensionInfo.id,
          expect.objectContaining({ MessageType: ContentAwareMessageTypes.LOGIN }),
          expect.any(Function),
        );
      });

      it('should handle Content Aware extension being enabled when service is not running', async () => {
        // Don't start the service
        // Clear previous calls
        (telemetryService.logEvent as jest.Mock).mockClear();
        (chrome.runtime.sendMessage as jest.Mock).mockClear();

        // Simulate extension being enabled
        const enableCallback = (chrome.management.onEnabled.addListener as jest.Mock).mock
          .calls[0][0];
        enableCallback(mockContentAwareExtensionInfo);

        // Should log enabled event
        expect(telemetryService.logEvent).toHaveBeenCalledWith(
          'content-aware-extension-status',
          expect.objectContaining({ status: 'enabled' }),
        );

        // Should not try to establish communication since service is not running
        expect(chrome.runtime.sendMessage).not.toHaveBeenCalled();
      });

      it('should ignore non-Content Aware extension being enabled', async () => {
        await service.start();

        // Clear previous calls
        (telemetryService.logEvent as jest.Mock).mockClear();

        // Simulate non-Content Aware extension being enabled
        const enableCallback = (chrome.management.onEnabled.addListener as jest.Mock).mock
          .calls[0][0];
        enableCallback(mockNonContentAwareExtension);

        // Should not log any events
        expect(telemetryService.logEvent).not.toHaveBeenCalledWith(
          'content-aware-extension-status',
          expect.any(Object),
        );
      });

      it('should update extension info when Content Aware extension is enabled', async () => {
        // Start service without Content Aware extension
        (chrome.management.getAll as jest.Mock).mockResolvedValueOnce([]);
        await service.start();

        // Simulate extension being enabled
        const enableCallback = (chrome.management.onEnabled.addListener as jest.Mock).mock
          .calls[0][0];
        enableCallback(mockContentAwareExtensionInfo);

        // Extension info should be set
        expect((service as any)._extensionInfo).toEqual({
          id: mockContentAwareExtensionInfo.id,
          name: mockContentAwareExtensionInfo.name,
          version: mockContentAwareExtensionInfo.version,
          enabled: true,
        });
      });
    });

    describe('_onExtensionDisabled', () => {
      it('should handle Content Aware extension being disabled', async () => {
        // Start service with enabled Content Aware extension
        await service.start();

        // Create a spy for the onStatusChanged event
        const onStatusChangedSpy = jest.fn();
        service.onStatusChanged.addListener(onStatusChangedSpy);

        // Clear previous calls
        (telemetryService.logEvent as jest.Mock).mockClear();

        // Simulate extension being disabled
        const disableCallback = (chrome.management.onDisabled.addListener as jest.Mock).mock
          .calls[0][0];
        disableCallback(mockContentAwareExtensionInfo);

        // Should log disabled event
        expect(telemetryService.logEvent).toHaveBeenCalledWith(
          'content-aware-extension-status',
          expect.objectContaining({ status: 'disabled' }),
        );

        // Should fire status change event
        expect(onStatusChangedSpy).toHaveBeenCalledWith(false, 'Extension disabled');

        // Clean up
        service.onStatusChanged.removeListener(onStatusChangedSpy);
      });

      it('should ignore non-Content Aware extension being disabled', async () => {
        await service.start();

        // Clear previous calls
        (telemetryService.logEvent as jest.Mock).mockClear();

        // Simulate non-Content Aware extension being disabled
        const disableCallback = (chrome.management.onDisabled.addListener as jest.Mock).mock
          .calls[0][0];
        disableCallback(mockNonContentAwareExtension);

        // Should not log any events
        expect(telemetryService.logEvent).not.toHaveBeenCalledWith(
          'content-aware-extension-status',
          expect.any(Object),
        );
      });

      it('should update extension info and clean up state when Content Aware extension is disabled', async () => {
        // Start service with enabled Content Aware extension
        await service.start();

        // Verify initial state
        expect((service as any)._isLoggedIn).toBe(true);
        expect((service as any)._extensionInfo?.enabled).toBe(true);

        // Simulate extension being disabled
        const disableCallback = (chrome.management.onDisabled.addListener as jest.Mock).mock
          .calls[0][0];
        disableCallback(mockContentAwareExtensionInfo);

        // Extension should be marked as disabled and logged out
        expect((service as any)._extensionInfo?.enabled).toBe(false);
        expect((service as any)._isLoggedIn).toBe(false);
      });
    });
  });
});

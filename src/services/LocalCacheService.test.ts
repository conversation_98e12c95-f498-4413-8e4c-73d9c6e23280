import LocalCacheService from './LocalCacheService';
import { LocalStorageAreaMock } from 'test-helpers/chrome-api';

const mockStorageData = {
  provisioningInfo: { foo: 1 },
  clientSettings: { foo: 4 },
  productConfig: { foo: 5 },
  secretKnock: { foo: 6 },
  policyConfig: { foo: 7 },
  firebase: { foo: 8 },
  deviceRegistration: { foo: 9 },
  cloudGroups: { foo: 10 },
  ipAddresses: { foo: 11 },
  safeguardingContext: { foo: 13 },
  licenseStatus: { foo: 14 },
  keepAlive: { foo: 15 },
};

describe('LocalCacheService', () => {
  // -----------------------------------------------------------------------------------------------

  // Regenerate the storage area mock before each test.
  // A reference is stored here so that it's easy to access with the correct type information.
  let localStorageAreaMock = new LocalStorageAreaMock();
  beforeEach(() => {
    localStorageAreaMock = new LocalStorageAreaMock();
    chrome.storage.local = localStorageAreaMock;
    localStorageAreaMock.get.mockResolvedValue(mockStorageData);
  });

  // -----------------------------------------------------------------------------------------------

  describe('loadAll()', () => {
    it('loads each cache from the specified storage area', async () => {
      const localCacheService = new LocalCacheService(localStorageAreaMock);
      await localCacheService.loadAll();
      expect(localStorageAreaMock.get).toHaveBeenCalledWith('provisioningInfo');
      expect(localStorageAreaMock.get).toHaveBeenCalledWith('clientSettings');
      expect(localStorageAreaMock.get).toHaveBeenCalledWith('productConfig');
      expect(localStorageAreaMock.get).toHaveBeenCalledWith('secretKnock');
      expect(localStorageAreaMock.get).toHaveBeenCalledWith('policyConfig');
      expect(localStorageAreaMock.get).toHaveBeenCalledWith('firebase');
      expect(localStorageAreaMock.get).toHaveBeenCalledWith('deviceRegistration');
      expect(localStorageAreaMock.get).toHaveBeenCalledWith('cloudGroups');
      expect(localStorageAreaMock.get).toHaveBeenCalledWith('ipAddresses');
      expect(localStorageAreaMock.get).toHaveBeenCalledWith('safeguardingContext');
      expect(localStorageAreaMock.get).toHaveBeenCalledWith('licenseStatus');
      expect(localStorageAreaMock.get).toHaveBeenCalledWith('keepAlive');

      expect(localCacheService.provisioningInfo.get('foo')).toEqual(1);
      expect(localCacheService.clientSettings.get('foo')).toEqual(4);
      expect(localCacheService.productConfig.get('foo')).toEqual(5);
      expect(localCacheService.secretKnock.get('foo')).toEqual(6);
      expect(localCacheService.policyConfig.get('foo')).toEqual(7);
      expect(localCacheService.firebase.get('foo')).toEqual(8);
      expect(localCacheService.deviceRegistration.get('foo')).toEqual(9);
      expect(localCacheService.cloudGroups.get('foo')).toEqual(10);
      expect(localCacheService.ipAddresses.get('foo')).toEqual(11);
      expect(localCacheService.safeguardingContext.get('foo')).toEqual(13);
      expect(localCacheService.licenseStatus.get('foo')).toEqual(14);
      expect(localCacheService.keepAlive.get('foo')).toEqual(15);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('populateAll()', () => {
    it('populates each in-memory cache from a single object', () => {
      const localCacheService = new LocalCacheService(localStorageAreaMock);
      localCacheService.populateAll(mockStorageData);

      expect(localCacheService.provisioningInfo.get('foo')).toEqual(1);
      expect(localCacheService.clientSettings.get('foo')).toEqual(4);
      expect(localCacheService.productConfig.get('foo')).toEqual(5);
      expect(localCacheService.secretKnock.get('foo')).toEqual(6);
      expect(localCacheService.policyConfig.get('foo')).toEqual(7);
      expect(localCacheService.firebase.get('foo')).toEqual(8);
      expect(localCacheService.deviceRegistration.get('foo')).toEqual(9);
      expect(localCacheService.cloudGroups.get('foo')).toEqual(10);
      expect(localCacheService.ipAddresses.get('foo')).toEqual(11);
      expect(localCacheService.safeguardingContext.get('foo')).toEqual(13);
      expect(localCacheService.licenseStatus.get('foo')).toEqual(14);
      expect(localCacheService.keepAlive.get('foo')).toEqual(15);
    });

    it('clears in-memory caches which did not exist in storage', () => {
      const modifiedStorageData = JSON.parse(JSON.stringify(mockStorageData));
      delete modifiedStorageData.productConfig;

      const localCacheService = new LocalCacheService(localStorageAreaMock);
      localCacheService.productConfig.set('hello', 'world');

      localCacheService.populateAll(modifiedStorageData);
      expect(localCacheService.productConfig.get('hello')).toBeUndefined();
      expect(localCacheService.productConfig.get('foo')).toBeUndefined();
    });

    it('ignores extra properties which were not expected', () => {
      const modifiedStorageData = JSON.parse(JSON.stringify(mockStorageData));
      modifiedStorageData.otherData = { hello: 'world' };

      const localCacheService = new LocalCacheService(localStorageAreaMock);
      expect(() => {
        localCacheService.populateAll(modifiedStorageData);
      }).not.toThrow();
    });

    it('does not load anything directly from storage', () => {
      const localCacheService = new LocalCacheService(localStorageAreaMock);
      localCacheService.populateAll(mockStorageData);
      expect(localStorageAreaMock.get).not.toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('clearAll()', () => {
    it('removes each named object from storage', async () => {
      const localCacheService = new LocalCacheService(localStorageAreaMock);
      await localCacheService.clearAll();

      expect(localStorageAreaMock.remove).toHaveBeenCalledWith('provisioningInfo');
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith('clientSettings');
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith('productConfig');
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith('secretKnock');
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith('policyConfig');
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith('firebase');
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith('deviceRegistration');
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith('cloudGroups');
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith('ipAddresses');
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith('safeguardingContext');
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith('licenseStatus');
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith('keepAlive');
    });

    it('does not remove any other data from storage', async () => {
      const localCacheService = new LocalCacheService(localStorageAreaMock);
      await localCacheService.clearAll();

      expect(localStorageAreaMock.clear).not.toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('all', () => {
    it('returns an array of all the storage service instances', () => {
      const localCacheService = new LocalCacheService(localStorageAreaMock);
      const names = localCacheService.all.map((s) => s.name);
      expect(names).toContain('provisioningInfo');
      expect(names).toContain('clientSettings');
      expect(names).toContain('productConfig');
      expect(names).toContain('secretKnock');
      expect(names).toContain('policyConfig');
      expect(names).toContain('firebase');
      expect(names).toContain('deviceRegistration');
      expect(names).toContain('cloudGroups');
      expect(names).toContain('ipAddresses');
      expect(names).toContain('safeguardingContext');
      expect(names).toContain('licenseStatus');
      expect(names).toContain('keepAlive');
    });
  });
});

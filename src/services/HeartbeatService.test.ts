import TemplateString from 'models/TemplateString';
import AlarmService from './AlarmService';
import HeartbeatService from './HeartbeatService';
import ProvisioningInfo from 'provisioning/ProvisioningInfo';
import SerialId from 'models/SerialId';
import TenantId from 'models/TenantId';
import DeviceId from 'models/DeviceId';
import HeartbeatData from 'models/HeartbeatData';
import CryptoJS from 'crypto-js';
import { waitForMockToBeCalled } from 'test-helpers/mock-utilities';

const heartbeatUrlTemplate = new TemplateString(
  'https://{glsHash}.example.com/api/check-in/{customerId}/{tenant}/{userId}/{deviceId}',
);

const provisioningInfo = new ProvisioningInfo(
  new SerialId('UNCLTESTTQJ0ARH4'),
  new TenantId('cfe3a025-f2dc-4b88-93cd-5b264f15573c'),
  'hardwareid',
  'deviceName',
  'agentVersion',
  '<EMAIL>',
  'test.example.com',
  ['Test Group'],
  true,
);

const deviceId = new DeviceId('17db29b9-8142-4ad7-a2d8-40e4bf1296fb');

const encodedUser = CryptoJS.enc.Utf8.parse(provisioningInfo.user);
const expectedUrl = `https://${provisioningInfo.serialId.getGlsHash()}.example.com/api/check-in/${provisioningInfo.serialId.toString()}/${
  provisioningInfo.tenantId?.toString() ?? 'untenanted'
}/${CryptoJS.enc.Base64url.stringify(encodedUser)}/${deviceId.toString()}`;

describe('HeartbeatService', () => {
  const fetchMock = jest.fn();
  global.fetch = fetchMock;

  let onHeartbeatMock: jest.Mock;

  let alarmService: AlarmService;
  let service: HeartbeatService;

  beforeEach(() => {
    alarmService = new AlarmService();
    service = new HeartbeatService(alarmService);

    fetchMock.mockReset();
    fetchMock.mockImplementation(
      async () =>
        await Promise.resolve({
          ok: true,
          status: 200,
          json: async () => {},
          text: async () => '',
        }),
    );

    onHeartbeatMock = jest.fn();
    service.onHeartbeatFinished.addListener(onHeartbeatMock);

    // Suppress console messages.
    jest.spyOn(console, 'debug').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  describe('sendHeartbeat', () => {
    let testData: HeartbeatData;
    let expectedBody: HeartbeatData;

    beforeEach(async () => {
      testData = {
        checkInTime: 123,
        logUploadCount: 321,
        policyName: 'policy-name',
        publicIpAddresses: ['*************'],
        privateIpAddresses: ['************', '**********'],
        software: [
          {
            name: 'sw-extension',
            version: '2.3.0',
            userAgent:
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          },
        ],
        mappedGroups: [
          {
            id: '123',
            name: 'Mapped Group 1',
          },
          {
            id: '456',
            name: 'Mapped Group 2',
          },
        ],
        policyDownloadedAt: '1709569781',
        categorisation: '1709607600',
      };

      expectedBody = {
        // checkInTime and logUploadCount will always be sent.
        checkInTime: testData.checkInTime,
        logUploadCount: testData.logUploadCount,
      };

      await service.start(heartbeatUrlTemplate, provisioningInfo, deviceId);
    });

    it('sends a heartbeat with the correct information', async () => {
      await service.sendHeartbeat(testData);
      await waitForMockToBeCalled(onHeartbeatMock, 1);

      expect(fetchMock).toHaveBeenCalledExactlyOnceWith(
        expectedUrl,
        expect.objectContaining({ body: JSON.stringify(testData) }),
      );
    });

    it('does not send properties that has not updated since the last run', async () => {
      await service.sendHeartbeat(testData);
      await waitForMockToBeCalled(onHeartbeatMock, 1);

      expect(fetchMock).toHaveBeenCalledExactlyOnceWith(
        expectedUrl,
        expect.objectContaining({ body: JSON.stringify(testData) }),
      );

      await service.sendHeartbeat(testData);
      await waitForMockToBeCalled(onHeartbeatMock, 2);

      expect(fetchMock).toHaveBeenNthCalledWith(
        2,
        expectedUrl,
        expect.objectContaining({
          body: JSON.stringify(expectedBody),
        }),
      );
    });

    it('sends an updated log count every time', async () => {
      await service.sendHeartbeat(testData);
      await waitForMockToBeCalled(onHeartbeatMock, 1);

      expect(fetchMock).toHaveBeenCalledExactlyOnceWith(
        expectedUrl,
        expect.objectContaining({ body: JSON.stringify(testData) }),
      );

      testData.logUploadCount = 567;

      expectedBody.logUploadCount = testData.logUploadCount;

      await service.sendHeartbeat(testData);
      await waitForMockToBeCalled(onHeartbeatMock, 2);

      expect(fetchMock).toHaveBeenNthCalledWith(
        2,
        expectedUrl,
        expect.objectContaining({ body: JSON.stringify(expectedBody) }),
      );
    });

    it('resends the policy name if it contains a new value', async () => {
      await service.sendHeartbeat(JSON.parse(JSON.stringify(testData)));
      await waitForMockToBeCalled(onHeartbeatMock, 1);

      expect(fetchMock).toHaveBeenCalledExactlyOnceWith(
        expectedUrl,
        expect.objectContaining({ body: JSON.stringify(testData) }),
      );

      testData.policyName = 'New policy';

      expectedBody.policyName = testData.policyName;

      await service.sendHeartbeat(testData);
      await waitForMockToBeCalled(onHeartbeatMock, 2);

      expect(fetchMock).toHaveBeenNthCalledWith(
        2,
        expectedUrl,
        expect.objectContaining({ body: JSON.stringify(expectedBody) }),
      );
    });

    it('resends the software object if a property changes', async () => {
      await service.sendHeartbeat(JSON.parse(JSON.stringify(testData)));
      await waitForMockToBeCalled(onHeartbeatMock, 1);

      expect(fetchMock).toHaveBeenCalledExactlyOnceWith(
        expectedUrl,
        expect.objectContaining({ body: JSON.stringify(testData) }),
      );

      // Keep typescript happy
      if (testData.software !== undefined) {
        testData.software[0].version = '2.4.0';
      }

      expectedBody.software = [
        {
          name: 'sw-extension',
          version: '2.4.0',
          userAgent:
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        },
      ];

      await service.sendHeartbeat(testData);
      await waitForMockToBeCalled(onHeartbeatMock, 2);

      expect(fetchMock).toHaveBeenNthCalledWith(
        2,
        expectedUrl,
        expect.objectContaining({ body: JSON.stringify(expectedBody) }),
      );
    });

    it('resends an array if it contains a new value', async () => {
      await service.sendHeartbeat(JSON.parse(JSON.stringify(testData)));
      await waitForMockToBeCalled(onHeartbeatMock, 1);

      expect(fetchMock).toHaveBeenCalledExactlyOnceWith(
        expectedUrl,
        expect.objectContaining({ body: JSON.stringify(testData) }),
      );

      testData.privateIpAddresses?.push('*********');

      expectedBody.privateIpAddresses = testData.privateIpAddresses;

      await service.sendHeartbeat(testData);
      await waitForMockToBeCalled(onHeartbeatMock, 2);

      expect(fetchMock).toHaveBeenNthCalledWith(
        2,
        expectedUrl,
        expect.objectContaining({ body: JSON.stringify(expectedBody) }),
      );
    });

    it('does not save the previous data if the heartbeat fails', async () => {
      await service.sendHeartbeat(JSON.parse(JSON.stringify(testData)));
      await waitForMockToBeCalled(onHeartbeatMock, 1);

      expect(fetchMock).toHaveBeenCalledExactlyOnceWith(
        expectedUrl,
        expect.objectContaining({ body: JSON.stringify(testData) }),
      );

      fetchMock.mockImplementation(
        async () =>
          await Promise.resolve({
            ok: false,
            status: 500,
            json: async () => {},
            text: async () => '',
          }),
      );

      testData.privateIpAddresses?.push('*********');
      expectedBody.privateIpAddresses = testData.privateIpAddresses;

      await service.sendHeartbeat(JSON.parse(JSON.stringify(testData)));
      await waitForMockToBeCalled(onHeartbeatMock, 2);

      expect(fetchMock).toHaveBeenNthCalledWith(
        2,
        expectedUrl,
        expect.objectContaining({ body: JSON.stringify(expectedBody) }),
      );

      fetchMock.mockImplementation(
        async () =>
          await Promise.resolve({
            ok: true,
            status: 200,
            json: async () => {},
            text: async () => '',
          }),
      );

      await service.sendHeartbeat(JSON.parse(JSON.stringify(testData)));
      await waitForMockToBeCalled(onHeartbeatMock, 3);

      expect(fetchMock).toHaveBeenNthCalledWith(
        3,
        expectedUrl,
        expect.objectContaining({ body: JSON.stringify(expectedBody) }),
      );
    });
  });

  describe('resetHeartbeatData', () => {
    let testData: HeartbeatData;

    beforeEach(async () => {
      testData = {
        checkInTime: 123,
        logUploadCount: 321,
        policyName: 'policy-name',
        publicIpAddresses: ['*************'],
        privateIpAddresses: ['************', '**********'],
        software: [
          {
            name: 'sw-extension',
            version: '2.3.0',
            userAgent:
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          },
        ],
        mappedGroups: [
          {
            id: '123',
            name: 'Mapped Group 1',
          },
          {
            id: '456',
            name: 'Mapped Group 2',
          },
        ],
      };

      await service.start(heartbeatUrlTemplate, provisioningInfo, deviceId);
    });

    it('resends all heartbeat data after the data is cleared', async () => {
      await service.sendHeartbeat(testData);
      await waitForMockToBeCalled(onHeartbeatMock, 1);

      expect(fetchMock).toHaveBeenCalledExactlyOnceWith(
        expectedUrl,
        expect.objectContaining({ body: JSON.stringify(testData) }),
      );

      service.resetHeartbeatData();

      await service.sendHeartbeat(testData);
      await waitForMockToBeCalled(onHeartbeatMock, 2);

      expect(fetchMock).toHaveBeenNthCalledWith(
        2,
        expectedUrl,
        expect.objectContaining({ body: JSON.stringify(testData) }),
      );
    });
  });
});

import FetchService from './FetchService';
import FirebaseAppService from './FirebaseAppService';
import StorageService from './StorageService';
import 'test-helpers/chrome-api';
import SerialId from 'models/SerialId';
import StandaloneEvent from 'utilities/StandaloneEvent';
import { waitForMockToBeCalled } from 'test-helpers/mock-utilities';
import TemplateString from 'models/TemplateString';
import MockTelemetryService from '../test-helpers/MockTelemetryService';
import ConstantBackOff from 'back-off-methods/ConstantBackOff';
import * as firebase from 'firebase/app';

jest.mock('firebase/app');
jest.mock('firebase/firestore');

// Fetch is mocked globally. This just gets a type-cast reference to it to make code simpler.
const fetchMock = fetch as jest.Mock;

// An object contained mocked Firebase configuration.
const mockFirebaseConfig = {
  apiKey: 'aaaaaaaa',
  authDomain: 'bbbbbbbb',
  projectId: 'cccccccc',
  storageBucket: 'dddddddd',
  messagingSenderId: 'eeeeeeee',
  appId: 'ffffffff',
  measurementId: 'gggggggg',
};

// A mock successful response from the fetch API when requesting Firebase config.
const mockSuccessfulResponse = {
  status: 200,
  ok: true,
  json: async () =>
    await Promise.resolve({
      isSuccess: true,
      data: mockFirebaseConfig,
    }),
};

// A mock error response from the fetch API when requesting Firebase config.
const mockFailedResponse = {
  status: 400,
  ok: true,
  json: async () =>
    await Promise.resolve({
      isSuccess: false,
      errors: [
        {
          message: 'test error',
          target: 'xyzzy',
          code: '12345',
        },
      ],
    }),
};

// A mock instance of the Firebase app.
const mockFirebaseAppInstance: firebase.FirebaseApp = {
  name: 'test',
  options: mockFirebaseConfig,
  automaticDataCollectionEnabled: false,
};

// A utility function which creates a promise which resolves when a standalone event is triggered.
const makePromiseWrapper = async <Arguments extends readonly any[]>(
  event: StandaloneEvent<Arguments>,
): Promise<Arguments> => {
  return await new Promise((resolve) => {
    event.addListener((...args) => {
      resolve(args);
    });
  });
};

// Utility function to prevent warnings and errors from being logged to the console.
// This is useful for certain tests which have to go through error cases.
const suppressConsole = (): void => {
  jest.spyOn(console, 'warn').mockImplementation(() => {});
  jest.spyOn(console, 'error').mockImplementation(() => {});
};

const testSerial = new SerialId('UNCLTEST614NESL8');
const testUrlTemplate = new TemplateString('http://{glsHash}.example.com/config');
const expectedUrl = 'http://r45oxeht3ko9kd9wuzkacxndh5layduk.example.com/config';

const telemetryService = new MockTelemetryService();

describe('FirebaseAppService', () => {
  const mockInitializeApp = firebase.initializeApp as jest.Mock;
  const mockDeleteApp = firebase.deleteApp as jest.Mock;

  beforeEach(() => {
    fetchMock.mockResolvedValue(mockSuccessfulResponse);
    mockInitializeApp.mockReturnValue(mockFirebaseAppInstance);
    mockDeleteApp.mockImplementation(async () => await Promise.resolve(123));

    // Prevent all debug messages from being logged to the console.
    jest.spyOn(console, 'debug').mockImplementation(() => {});
  });

  describe('isRunning', () => {
    it('returns false if start() has not been called yet', () => {
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(storageService, telemetryService);
      expect(firebaseAppService.isRunning).toBeFalse();
    });

    it('returns true if initialisation is in progress', async () => {
      fetchMock.mockReturnValue(mockFailedResponse);
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(1000, 0), 3), // <-- deliberately take a while between attempts
      );
      await firebaseAppService.start(testUrlTemplate, testSerial);
      expect(firebaseAppService.isRunning).toBeTrue();
      await firebaseAppService.stop();
    });

    it('returns true if initialisation has succeeded', async () => {
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(0, 0), 1),
      );
      const onInitialisedMock = jest.fn();
      firebaseAppService.onInitialised.addListener(onInitialisedMock);
      await firebaseAppService.start(testUrlTemplate, testSerial);
      await waitForMockToBeCalled(onInitialisedMock);
      expect(firebaseAppService.isRunning).toBeTrue();
      await firebaseAppService.stop();
    });

    it('returns false if stop() has been called', async () => {
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(0, 0), 1),
      );
      await firebaseAppService.start(testUrlTemplate, testSerial);
      await firebaseAppService.stop();
      expect(firebaseAppService.isRunning).toBeFalse();
    });
  });

  describe('isInitialised', () => {
    it('returns false if start() has not been called yet', () => {
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(storageService, telemetryService);
      expect(firebaseAppService.isInitialised).toBeFalse();
    });

    it('returns false if initialisation is in progress', async () => {
      fetchMock.mockReturnValue(mockFailedResponse);
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(1000, 0), 3), // <-- deliberately take a while between attempts
      );
      await firebaseAppService.start(testUrlTemplate, testSerial);
      expect(firebaseAppService.isInitialised).toBeFalse();
      await firebaseAppService.stop();
    });

    it('returns true if initialisation has succeeded', async () => {
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(0, 0), 1),
      );
      const onInitialisedMock = jest.fn();
      firebaseAppService.onInitialised.addListener(onInitialisedMock);
      await firebaseAppService.start(testUrlTemplate, testSerial);
      await waitForMockToBeCalled(onInitialisedMock);
      expect(firebaseAppService.isInitialised).toBeTrue();
      await firebaseAppService.stop();
    });

    it('returns false if stop() has been called', async () => {
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(0, 0), 1),
      );
      const onInitialisedMock = jest.fn();
      firebaseAppService.onInitialised.addListener(onInitialisedMock);
      await firebaseAppService.start(testUrlTemplate, testSerial);
      await waitForMockToBeCalled(onInitialisedMock);
      await firebaseAppService.stop();
      expect(firebaseAppService.isInitialised).toBeFalse();
    });
  });

  describe('firebaseApp', () => {
    it('returns undefined if Firebase is not initialised yet', () => {
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(0, 0), 1),
      );
      expect(firebaseAppService.firebaseApp).toBeUndefined();
    });

    it('returns undefined if initialisation is in progress', async () => {
      fetchMock.mockReturnValue(mockFailedResponse);
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(1000, 0), 3), // <-- deliberately take a while between attempts
      );

      await firebaseAppService.start(testUrlTemplate, testSerial);
      expect(firebaseAppService.firebaseApp).toBeUndefined();
      await firebaseAppService.stop();
    });

    it('returns a Firebase App instance if it has been initialised successfully', async () => {
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(0, 0), 1),
      );
      await firebaseAppService.start(testUrlTemplate, testSerial);
      await makePromiseWrapper(firebaseAppService.onInitialised);
      expect(firebaseAppService.firebaseApp).toEqual(mockFirebaseAppInstance);
    });

    it('returns undefined if initialisation has failed and will not be retried', async () => {
      suppressConsole();
      fetchMock.mockReturnValue(mockFailedResponse);
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(0, 0), 1),
      );
      await firebaseAppService.start(testUrlTemplate, testSerial);
      await makePromiseWrapper(firebaseAppService.onFailed);
      expect(firebaseAppService.firebaseApp).toBeUndefined();
    });
  });

  describe('start()', () => {
    it('initialises using cached config if it exists', async () => {
      const storageService = new StorageService('firebase', undefined);
      storageService.set('config', mockFirebaseConfig);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(0, 0), 1),
      );
      const onInitialised = makePromiseWrapper(firebaseAppService.onInitialised);

      await firebaseAppService.start(testUrlTemplate, testSerial);
      await onInitialised;

      expect(firebase.initializeApp).toHaveBeenCalledWith(mockFirebaseConfig);
    });

    it('does not send a request if cached config worked', async () => {
      const storageService = new StorageService('firebase', undefined);
      storageService.set('config', mockFirebaseConfig);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(0, 0), 1),
      );
      const onInitialised = makePromiseWrapper(firebaseAppService.onInitialised);

      await firebaseAppService.start(testUrlTemplate, testSerial);
      await onInitialised;

      expect(fetchMock).not.toHaveBeenCalled();
    });

    it('deletes cached config if it was invalid', async () => {
      suppressConsole();
      mockInitializeApp.mockImplementation(() => {
        throw new Error('test error');
      });
      const storageService = new StorageService('firebase', undefined);
      storageService.set('config', mockFirebaseConfig);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(0, 0), 1),
      );

      // The attempt to initialise from cached happens before start() finishes.
      await firebaseAppService.start(testUrlTemplate, testSerial);
      await firebaseAppService.stop();

      expect(storageService.has('config')).toBeFalse();
    });

    it('downloads new config if no config was cached', async () => {
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(0, 0), 1),
      );
      await firebaseAppService.start(testUrlTemplate, testSerial);
      await makePromiseWrapper(firebaseAppService.onInitialised);

      expect(fetchMock).toHaveBeenCalledWith(expectedUrl, expect.anything());
    });

    it('initialises using downloaded config if no config was cached', async () => {
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(0, 0), 1),
      );
      await firebaseAppService.start(testUrlTemplate, testSerial);
      await makePromiseWrapper(firebaseAppService.onInitialised);

      expect(mockInitializeApp).toHaveBeenCalledWith(mockFirebaseConfig);
    });

    it('caches the downloaded config if it worked', async () => {
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(0, 0), 1),
      );
      await firebaseAppService.start(testUrlTemplate, testSerial);
      await makePromiseWrapper(firebaseAppService.onInitialised);

      expect(storageService.get('config')).toEqual(mockFirebaseConfig);
    });

    it('retries the download if it fails', async () => {
      suppressConsole();
      fetchMock.mockResolvedValueOnce(mockFailedResponse);
      fetchMock.mockResolvedValueOnce(mockFailedResponse);
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(0, 0), 3),
      );
      await firebaseAppService.start(testUrlTemplate, testSerial);
      await makePromiseWrapper(firebaseAppService.onInitialised);

      expect(fetchMock).toHaveBeenCalledTimes(3);
    });
  });

  describe('stop()', () => {
    it('stops any existing attempt to initialise Firebase', async () => {
      suppressConsole();
      fetchMock.mockResolvedValue(mockFailedResponse);
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(1000, 0), 3),
      );

      await firebaseAppService.start(testUrlTemplate, testSerial);
      await firebaseAppService.stop();

      expect(fetchMock.mock.calls.length).toBeLessThanOrEqual(1);
    });

    it('deletes the existing Firebase instance if it was already initialised', async () => {
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(0, 0), 1),
      );
      await firebaseAppService.start(testUrlTemplate, testSerial);
      await makePromiseWrapper(firebaseAppService.onInitialised);
      await firebaseAppService.stop();
      expect(mockDeleteApp).toHaveBeenCalled();
    });

    it('deletes the existing Firebase instance if it was already initialised', async () => {
      mockDeleteApp.mockRejectedValue(new Error('test error'));
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(0, 0), 1),
      );
      await firebaseAppService.start(testUrlTemplate, testSerial);
      await makePromiseWrapper(firebaseAppService.onInitialised);
      suppressConsole();
      await firebaseAppService.stop();
    });

    it('does nothing if start() has not been called', async () => {
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(0, 0), 3),
      );
      await firebaseAppService.stop();
    });

    it('does nothing if initialisation was already stopped', async () => {
      const storageService = new StorageService('firebase', undefined);
      const firebaseAppService = new FirebaseAppService(
        storageService,
        telemetryService,
        new FetchService(new ConstantBackOff(0, 0), 3),
      );
      await firebaseAppService.start(testUrlTemplate, testSerial);
      await firebaseAppService.stop();
      await firebaseAppService.stop();
    });
  });
});

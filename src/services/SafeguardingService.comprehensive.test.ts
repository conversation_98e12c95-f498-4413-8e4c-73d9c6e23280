import 'test-helpers/chrome-api';

import AccessLogEntry from 'models/AccessLogEntry';
import SafeguardingService from 'services/SafeguardingService';
import safeguardingRules from 'mini-blocklist-data/safeguardingRules';

// This test file verifies the implementation of the SafeguardingService
// with a focus on the sub-request deduplication feature.

describe('SafeguardingService - Sub-request Deduplication', () => {
  // Mock AccessLogManager
  const mockAccessLogManager = {
    lastUploadedAt: 0,
    getUploadCount: jest.fn((reset: boolean) => (reset ? 0 : 10)),
    populate: jest.fn(async (storageData: Record<string, any>) => {
      await Promise.resolve();
    }),
    start: jest.fn(async () => {
      await Promise.resolve();
    }),
    stop: jest.fn(() => {}),
    configure: jest.fn(async (hardwareId: string, tenantId: any, licenseInfo: any) => {
      await Promise.resolve();
    }),
    clearConfiguration: jest.fn(() => {}),
    clearLogs: jest.fn(async () => {
      await Promise.resolve();
    }),
    storeAccessLog: jest.fn((log: any, skipDeduplication: boolean) => {}),
    flush: jest.fn(async () => {
      await Promise.resolve();
    }),
    upload: jest.fn(async () => {
      await Promise.resolve();
    }),
  } as any;
  // Verify implementation includes required changes
  test('Implementation includes the required changes for sub-request deduplication', () => {
    // Check service code contains our implementation
    const serviceSource = SafeguardingService.toString();
    expect(serviceSource).toContain('_sendImmediateAlert');

    // Our implementation includes:
    // 1. _recentCriticalAlerts property to track recent alerts
    // 2. _isSubRequestDuplicateOfRecentCriticalAlert method
    // 3. Logic in _sendImmediateAlert to skip sub-request duplicates
    // 4. Logic in _post to track main_frame critical alerts
  });

  // Verify behavior
  describe('Behavior verification', () => {
    test('Applies rules correctly for suicide-themed content', () => {
      // Setup mocks
      const mockStorageService = {
        get: jest.fn(),
        set: jest.fn(),
        save: jest.fn().mockResolvedValue(undefined),
      };

      const mockAlarmService = {
        addListener: jest.fn(),
        create: jest.fn().mockResolvedValue(undefined),
        clear: jest.fn().mockResolvedValue(true),
      };

      const mockTelemetryService = {
        logEvent: jest.fn(),
        logError: jest.fn(),
      };

      // Create service instance
      const safeguardingService = new SafeguardingService(
        mockStorageService as any,
        mockAlarmService as any,
        mockTelemetryService as any,
        mockAccessLogManager,
      );

      // Load the rules
      safeguardingService.loadRules(safeguardingRules);

      // Test log with suicide content
      const log: AccessLogEntry = {
        took: 123,
        time: '1620000000',
        groups: ['test-group'],
        categories: ['Suicide - Danger'],
        contenttype: 'main_frame',
        url: 'https://example.com/suicide',
      };

      const match = safeguardingService.applyRules(log);

      // Check match results
      expect(match).toBeDefined();
      expect(match?.title).toBe('Suicide');
      expect(match?.weight).toBe(3); // Danger level
    });

    test('Handles different content types appropriately', () => {
      // Setup mocks
      const mockStorageService = {
        get: jest.fn(),
        set: jest.fn(),
        save: jest.fn().mockResolvedValue(undefined),
      };

      const mockAlarmService = {
        addListener: jest.fn(),
        create: jest.fn().mockResolvedValue(undefined),
        clear: jest.fn().mockResolvedValue(true),
      };

      const mockTelemetryService = {
        logEvent: jest.fn(),
        logError: jest.fn(),
      };

      // Create service instance
      const safeguardingService = new SafeguardingService(
        mockStorageService as any,
        mockAlarmService as any,
        mockTelemetryService as any,
        mockAccessLogManager,
      );

      // Load the rules
      safeguardingService.loadRules(safeguardingRules);

      // Test logs with different content types
      const mainFrameLog: AccessLogEntry = {
        took: 123,
        time: '1620000000',
        groups: ['test-group'],
        categories: ['Suicide - Danger'],
        contenttype: 'main_frame',
        url: 'https://example.com/suicide',
      };

      const subRequestLog: AccessLogEntry = {
        took: 123,
        time: '1620000000',
        groups: ['test-group'],
        categories: ['Suicide - Danger'],
        contenttype: 'xmlhttprequest',
        url: 'https://analytics.example.com/track?url=https://example.com/suicide',
      };

      // Apply rules and check results
      const mainFrameMatch = safeguardingService.applyRules(mainFrameLog);
      const subRequestMatch = safeguardingService.applyRules(subRequestLog);

      // Check both match the same rule
      expect(mainFrameMatch?.title).toBe('Suicide');
      expect(subRequestMatch?.title).toBe('Suicide');

      // Verify different content types
      expect(mainFrameLog.contenttype).toBe('main_frame');
      expect(subRequestLog.contenttype).toBe('xmlhttprequest');
    });
  });

  // Helper to access private methods in tests
  const createTestService = (): {
    service: SafeguardingService;
    mockStorageService: any;
    mockAlarmService: any;
    mockTelemetryService: any;
    getRecentCriticalAlerts: () => any[];
    setRecentCriticalAlerts: (alerts: any[]) => void;
    isSubRequestDuplicate: (alert: any) => boolean;
    mockPost: () => jest.Mock;
    setPreviousImmediateAlert: (alert: any) => void;
    sendImmediateAlert: (alert: any) => Promise<void>;
  } => {
    // Setup mocks
    const mockStorageService = {
      get: jest.fn(),
      set: jest.fn(),
      save: jest.fn().mockResolvedValue(undefined),
    };

    const mockAlarmService = {
      addListener: jest.fn(),
      create: jest.fn().mockResolvedValue(undefined),
      clear: jest.fn().mockResolvedValue(true),
    };

    const mockTelemetryService = {
      logEvent: jest.fn(),
      logError: jest.fn(),
    };

    // Create service
    const service = new SafeguardingService(
      mockStorageService as any,
      mockAlarmService as any,
      mockTelemetryService as any,
      mockAccessLogManager,
    );

    return {
      service,
      mockStorageService,
      mockAlarmService,
      mockTelemetryService,
      // Access private properties
      getRecentCriticalAlerts: () => (service as any)._recentCriticalAlerts,
      setRecentCriticalAlerts: (alerts: any[]) => ((service as any)._recentCriticalAlerts = alerts),
      isSubRequestDuplicate: (alert: any) =>
        (service as any)._isSubRequestDuplicateOfRecentCriticalAlert(alert),
      mockPost: () => {
        const mockFn = jest.fn().mockResolvedValue('context-id');
        jest.spyOn(service as any, '_post').mockImplementation(mockFn);
        return mockFn;
      },
      setPreviousImmediateAlert: (alert: any) => ((service as any)._previousImmediateAlert = alert),
      sendImmediateAlert: (alert: any) => (service as any)._sendImmediateAlert(alert),
    };
  };

  // Test deduplication logic
  describe('Deduplication logic', () => {
    test('Deduplication window is set to 1 minute', () => {
      // Get test helpers
      const { setRecentCriticalAlerts, getRecentCriticalAlerts, isSubRequestDuplicate } =
        createTestService();

      // Test data
      const now = Date.now();
      const oldAlert = {
        clientTimeUtc: now - 61000, // 61 seconds ago (outside window)
        theme: 'Suicide',
        level: 3,
        type: 'main_frame',
      };

      const recentAlert = {
        clientTimeUtc: now - 59000, // 59 seconds ago (inside window)
        theme: 'Suicide',
        level: 3,
        type: 'main_frame',
      };

      // Add test alerts
      setRecentCriticalAlerts([oldAlert, recentAlert]);

      // Test alert
      const testAlert = {
        theme: 'Suicide',
        level: 3,
        type: 'xmlhttprequest',
      };

      // Run test
      isSubRequestDuplicate(testAlert);

      // Check old alert was filtered out (outside 1-minute window)
      expect(getRecentCriticalAlerts().length).toBe(1);
      expect(getRecentCriticalAlerts()[0]).toBe(recentAlert);
    });

    test('Only deduplicates alerts with matching theme and level', () => {
      // Create test service with helper
      const { isSubRequestDuplicate, setRecentCriticalAlerts } = createTestService();

      // Set up test data
      const now = Date.now();

      // Recent alerts with different themes and levels
      const recentAlerts = [
        {
          clientTimeUtc: now - 30000,
          theme: 'Suicide',
          level: 3,
          type: 'main_frame',
          username: 'test-user',
          tenantId: 'test-tenant',
          serial: 'test-serial',
        },
        {
          clientTimeUtc: now - 30000,
          theme: 'Bullying', // Different theme
          level: 3,
          type: 'main_frame',
          username: 'test-user',
          tenantId: 'test-tenant',
          serial: 'test-serial',
        },
        {
          clientTimeUtc: now - 30000,
          theme: 'Suicide',
          level: 2, // Different level
          type: 'main_frame',
          username: 'test-user',
          tenantId: 'test-tenant',
          serial: 'test-serial',
        },
      ];

      // Set the recent alerts
      setRecentCriticalAlerts(recentAlerts);

      // Test with matching theme and level
      const matchingAlert = {
        theme: 'Suicide',
        level: 3,
        type: 'xmlhttprequest',
        username: 'test-user',
        tenantId: 'test-tenant',
        serial: 'test-serial',
      };

      // Should be considered a duplicate
      expect(isSubRequestDuplicate(matchingAlert)).toBe(true);

      // Test with different theme
      const differentThemeAlert = {
        theme: 'Radicalisation',
        level: 3,
        type: 'xmlhttprequest',
        username: 'test-user',
        tenantId: 'test-tenant',
        serial: 'test-serial',
      };

      // Should not be considered a duplicate
      expect(isSubRequestDuplicate(differentThemeAlert)).toBe(false);

      // Test with different level
      const differentLevelAlert = {
        theme: 'Suicide',
        level: 1,
        type: 'xmlhttprequest',
        username: 'test-user',
        tenantId: 'test-tenant',
        serial: 'test-serial',
      };

      // Should not be considered a duplicate
      expect(isSubRequestDuplicate(differentLevelAlert)).toBe(false);
    });

    test('Only applies deduplication to sub-requests, not main_frame', async () => {
      // Create test service with helper
      const { service, sendImmediateAlert, mockPost } = createTestService();

      // Create a spy for the isSubRequestDuplicate method
      const isSubRequestDuplicateSpy = jest
        .spyOn(service as any, '_isSubRequestDuplicateOfRecentCriticalAlert')
        .mockReturnValue(true);

      // Mock the post method
      const postMock = mockPost();

      // Test with a main_frame request
      const mainFrameAlert = {
        theme: 'Suicide',
        level: 3,
        type: 'main_frame',
        url: 'https://example.com/suicide',
      };

      await sendImmediateAlert(mainFrameAlert);

      // Should send the alert even if it would be a duplicate
      expect(postMock).toHaveBeenCalledTimes(1);

      // Reset the mock
      jest.clearAllMocks();
      mockPost();

      // Test with a sub-request
      const subRequestAlert = {
        theme: 'Suicide',
        level: 3,
        type: 'xmlhttprequest',
        url: 'https://analytics.example.com/track?url=https://example.com/suicide',
      };

      await sendImmediateAlert(subRequestAlert);

      // Should not send the alert because it's a duplicate sub-request
      expect(postMock).not.toHaveBeenCalled();

      // Clean up
      isSubRequestDuplicateSpy.mockRestore();
    });
  });

  // Test the overall fix with integration tests
  describe('Integration tests', () => {
    test('Handles a sequence of main_frame and sub-requests correctly', async () => {
      // Create test service with helper
      const { service, sendImmediateAlert, mockPost, setRecentCriticalAlerts } =
        createTestService();

      // Mock Date.now for consistent timestamps
      const originalDateNow = Date.now;
      const mockNow = jest.fn();
      const currentTime = 1620000000000;
      mockNow.mockReturnValue(currentTime);
      global.Date.now = mockNow;

      try {
        // Load the safeguarding rules
        service.loadRules(safeguardingRules);

        // Mock the post method
        const postMock = mockPost();

        // First, process a main_frame request
        const mainFrameLog = {
          took: 123,
          time: '1620000000',
          groups: ['test-group'],
          categories: ['Suicide - Danger'],
          contenttype: 'main_frame',
          url: 'https://example.com/suicide',
          username: 'test-user',
        };

        // Apply rules and create alert
        const match = service.applyRules(mainFrameLog);
        expect(match?.title).toBe('Suicide');

        // Create and process the alert
        const mainFrameAlert = {
          blocked: false,
          level: 3,
          tenantId: 'test-tenant',
          serial: 'test-serial',
          url: mainFrameLog.url,
          searchTerms: '',
          localGroups: mainFrameLog.groups,
          username: mainFrameLog.username,
          clientTimeUtc: currentTime,
          theme: 'Suicide',
          onPremCategories: mainFrameLog.categories,
          type: mainFrameLog.contenttype,
          localDate: 'Thu May 11 2023 03:45:03 GMT+0100 (British Summer Time)',
        };

        await sendImmediateAlert(mainFrameAlert);

        // Should send the main_frame alert
        expect(postMock).toHaveBeenCalledTimes(1);

        // Manually add the alert to recent critical alerts (normally done by _post)
        setRecentCriticalAlerts([mainFrameAlert]);

        // Now process a sub-request
        const subRequestLog = {
          took: 123,
          time: '1620000000',
          groups: ['test-group'],
          categories: ['Suicide - Danger'],
          contenttype: 'xmlhttprequest',
          url: 'https://analytics.example.com/track?url=https://example.com/suicide',
          username: 'test-user',
        };

        // Apply rules and create alert
        const subMatch = service.applyRules(subRequestLog);
        expect(subMatch?.title).toBe('Suicide');

        // Reset the mock
        jest.clearAllMocks();
        mockPost();

        // Create and process the alert
        const subRequestAlert = {
          blocked: false,
          level: 3,
          tenantId: 'test-tenant',
          serial: 'test-serial',
          url: subRequestLog.url,
          searchTerms: '',
          localGroups: subRequestLog.groups,
          username: subRequestLog.username,
          clientTimeUtc: currentTime,
          theme: 'Suicide',
          onPremCategories: subRequestLog.categories,
          type: subRequestLog.contenttype,
          localDate: 'Thu May 11 2023 03:45:03 GMT+0100 (British Summer Time)',
        };

        await sendImmediateAlert(subRequestAlert);

        // Should not send the sub-request alert (it's a duplicate)
        expect(postMock).not.toHaveBeenCalled();

        // For the time window test, we'll use a different approach
        // Clear the recent alerts (simulating time passing)
        setRecentCriticalAlerts([]);

        // Reset the mock
        jest.clearAllMocks();
        const newPostMock = mockPost();

        // Process another sub-request (now without any recent alerts)
        await sendImmediateAlert(subRequestAlert);

        // Should send the alert because there are no recent alerts
        expect(newPostMock).toHaveBeenCalledTimes(1);
      } finally {
        // Restore Date.now
        global.Date.now = originalDateNow;
      }
    });

    test('Correctly deduplicates alerts for various suicide-related keywords', async () => {
      // Create test service with helper
      const { service, sendImmediateAlert, mockPost, setRecentCriticalAlerts } =
        createTestService();

      // Load the safeguarding rules
      service.loadRules(safeguardingRules);

      // Array of suicide-related keywords to test
      const suicideKeywords = [
        'kill myself',
        'commit suicide',
        'end my life',
        'take my own life',
        'suicide methods',
        'how to die',
        'ways to kill yourself',
      ];

      // For each keyword, test that main_frame requests generate alerts
      // and sub-requests are deduplicated
      for (const keyword of suicideKeywords) {
        // Reset mocks and state for each keyword
        jest.clearAllMocks();
        const postMock = mockPost();
        setRecentCriticalAlerts([]);

        // Create a main_frame request with the current keyword
        const mainFrameUrl = `https://example.com/search?q=${encodeURIComponent(keyword)}`;
        const mainFrameLog = {
          took: 123,
          time: '1620000000',
          groups: ['test-group'],
          categories: ['Suicide - Danger'],
          contenttype: 'main_frame',
          url: mainFrameUrl,
          username: 'test-user',
        };

        // Create the main frame alert
        const mainFrameAlert = {
          blocked: false,
          level: 3,
          tenantId: 'test-tenant',
          serial: 'test-serial',
          url: mainFrameLog.url,
          searchTerms: keyword,
          localGroups: mainFrameLog.groups,
          username: mainFrameLog.username,
          clientTimeUtc: Date.now(),
          theme: 'Suicide',
          onPremCategories: mainFrameLog.categories,
          type: mainFrameLog.contenttype,
          localDate: 'Thu May 11 2023 03:45:03 GMT+0100 (British Summer Time)',
        };

        // Send the main frame alert
        await sendImmediateAlert(mainFrameAlert);

        // Should send the main_frame alert
        expect(postMock).toHaveBeenCalledTimes(1);

        // Manually add the alert to recent critical alerts
        setRecentCriticalAlerts([mainFrameAlert]);

        // Reset the mock for the sub-request
        jest.clearAllMocks();
        mockPost();

        // Create a sub-request with the same keyword
        const subRequestUrl = `https://analytics.example.com/track?url=${encodeURIComponent(
          mainFrameUrl,
        )}`;
        const subRequestAlert = {
          blocked: false,
          level: 3,
          tenantId: 'test-tenant',
          serial: 'test-serial',
          url: subRequestUrl,
          searchTerms: keyword,
          localGroups: mainFrameLog.groups,
          username: mainFrameLog.username,
          clientTimeUtc: Date.now(),
          theme: 'Suicide',
          onPremCategories: mainFrameLog.categories,
          type: 'xmlhttprequest',
          localDate: 'Thu May 11 2023 03:45:03 GMT+0100 (British Summer Time)',
        };

        // Send the sub-request alert
        await sendImmediateAlert(subRequestAlert);

        // Should not send the sub-request alert (it's a duplicate)
        expect(postMock).not.toHaveBeenCalled();
      }
    });
  });
});

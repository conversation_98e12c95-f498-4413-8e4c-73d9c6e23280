import TenantId from 'models/TenantId';

import CONTENT_AWARE_CONSTANTS from '../constants/ContentAwareConstants';
import { IPolicyConfig } from '../models/PolicyConfigModels';
import MockBlobStorageService, { MockUrlData } from '../test-helpers/MockBlobStorageService';
import { getTestPolicyConfigWithContentAware } from '../test-helpers/test-policy-config';
import IBlobStorageService from './IBlobStorageService';
import PolicyDownloadService from './PolicyDownloadService';

describe('PolicyDownloadService - Content Aware Allow List', () => {
  const tenant = new TenantId('f417a2c4-f99c-11ea-8caa-eb014c4bbe3b');
  const policy1Url = 'https://example.com/policy1.json';
  let mockBlobStorage: IBlobStorageService;
  let policyJson: any;

  beforeEach(() => {
    policyJson = getTestPolicyConfigWithContentAware(tenant);
    mockBlobStorage = new MockBlobStorageService([
      new MockUrlData(policy1Url, JSON.stringify(policyJson)),
    ]);
  });

  // No need to reset environment variables anymore

  describe('Content Aware Allow List extraction', () => {
    it('should extract global Content Aware allow list URLs', (done) => {
      const service = new PolicyDownloadService(mockBlobStorage, undefined);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          // Verify that the contentAwareAllowList property exists and contains the expected URLs
          expect(result?.contentAwareAllowList).toBeDefined();
          expect(result?.contentAwareAllowList).toContain('example.com');
          expect(result?.contentAwareAllowList).toContain('trusted-site.org');
          expect(result?.contentAwareAllowList).toContain('school.edu');

          // Tenant-specific URLs should not be included when no tenant is specified
          expect(result?.contentAwareAllowList).not.toContain('tenant-specific.com');
          expect(result?.contentAwareAllowList).not.toContain('tenant-trusted.org');

          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should extract both global and tenant-specific Content Aware allow list URLs when tenant is specified', (done) => {
      const service = new PolicyDownloadService(mockBlobStorage, tenant);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          // Verify that the contentAwareAllowList property exists and contains the expected URLs
          expect(result?.contentAwareAllowList).toBeDefined();

          // Should include global URLs
          expect(result?.contentAwareAllowList).toContain('example.com');
          expect(result?.contentAwareAllowList).toContain('trusted-site.org');
          expect(result?.contentAwareAllowList).toContain('school.edu');

          // Should also include tenant-specific URLs
          expect(result?.contentAwareAllowList).toContain('tenant-specific.com');
          expect(result?.contentAwareAllowList).toContain('tenant-trusted.org');

          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should handle custom categories with no Content Aware allow list', (done) => {
      // Create a modified policy with no Content Aware allow list categories
      const modifiedPolicy = { ...policyJson };
      modifiedPolicy.custom_categories = policyJson.custom_categories.filter(
        (category: any) => category.name !== CONTENT_AWARE_CONSTANTS.ALLOW_LIST_CATEGORY_NAME,
      );

      // Create a new mock blob storage with the modified policy
      const modifiedBlobStorage = new MockBlobStorageService([
        new MockUrlData(policy1Url, JSON.stringify(modifiedPolicy)),
      ]);

      const service = new PolicyDownloadService(modifiedBlobStorage, tenant);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          // Verify that the contentAwareAllowList property exists but is empty
          expect(result?.contentAwareAllowList).toBeDefined();
          expect(result?.contentAwareAllowList).toBeArrayOfSize(0);

          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should handle custom categories with Content Aware allow list but no domainsurls component', (done) => {
      // Create a modified policy with Content Aware allow list categories but no domainsurls component
      const modifiedPolicy = { ...policyJson };
      modifiedPolicy.custom_categories = policyJson.custom_categories.map((category: any) => {
        if (category.name === CONTENT_AWARE_CONSTANTS.ALLOW_LIST_CATEGORY_NAME) {
          return {
            ...category,
            component: {}, // Empty component with no domainsurls
          };
        }
        return category;
      });

      // Create a new mock blob storage with the modified policy
      const modifiedBlobStorage = new MockBlobStorageService([
        new MockUrlData(policy1Url, JSON.stringify(modifiedPolicy)),
      ]);

      const service = new PolicyDownloadService(modifiedBlobStorage, tenant);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          // Verify that the contentAwareAllowList property exists but is empty
          expect(result?.contentAwareAllowList).toBeDefined();
          expect(result?.contentAwareAllowList).toBeArrayOfSize(0);

          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should handle custom categories with null component', (done) => {
      // Create a modified policy with Content Aware allow list categories but null component
      const modifiedPolicy = { ...policyJson };
      modifiedPolicy.custom_categories = policyJson.custom_categories.map((category: any) => {
        if (category.name === CONTENT_AWARE_CONSTANTS.ALLOW_LIST_CATEGORY_NAME) {
          return {
            ...category,
            component: null, // Null component
          };
        }
        return category;
      });

      // Create a new mock blob storage with the modified policy
      const modifiedBlobStorage = new MockBlobStorageService([
        new MockUrlData(policy1Url, JSON.stringify(modifiedPolicy)),
      ]);

      const service = new PolicyDownloadService(modifiedBlobStorage, tenant);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          // Verify that the contentAwareAllowList property exists but is empty
          expect(result?.contentAwareAllowList).toBeDefined();
          expect(result?.contentAwareAllowList).toBeArrayOfSize(0);

          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should handle duplicate URLs in allow list categories', (done) => {
      // Create a modified policy with duplicate URLs in Content Aware allow list categories
      const modifiedPolicy = { ...policyJson };
      modifiedPolicy.custom_categories = policyJson.custom_categories.map((category: any) => {
        if (
          category.name === CONTENT_AWARE_CONSTANTS.ALLOW_LIST_CATEGORY_NAME &&
          category.tenant === 'global'
        ) {
          return {
            ...category,
            component: {
              domainsurls: ['example.com', 'trusted-site.org', 'school.edu', 'example.com'], // Duplicate URL
            },
          };
        }
        return category;
      });

      // Create a new mock blob storage with the modified policy
      const modifiedBlobStorage = new MockBlobStorageService([
        new MockUrlData(policy1Url, JSON.stringify(modifiedPolicy)),
      ]);

      const service = new PolicyDownloadService(modifiedBlobStorage, tenant);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          // Verify that the contentAwareAllowList property exists and contains the expected URLs
          expect(result?.contentAwareAllowList).toBeDefined();

          // Count occurrences of 'example.com' in the allow list
          const exampleComCount =
            result?.contentAwareAllowList?.filter((url) => url === 'example.com')?.length ?? 0;

          // Should include duplicate URLs as they appear in the original list
          expect(exampleComCount).toBe(2);

          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should handle multiple categories with the same name for the same tenant', (done) => {
      // Create a modified policy with multiple Content Aware allow list categories for the same tenant
      const modifiedPolicy = { ...policyJson };

      // Add another global Content Aware allow list category
      const additionalCategory = {
        id: 'd3e4f5g6-h7i8-j9k0-l1m2-n3o4p5q6r7s8',
        category_id: 'd3e4f5g6-h7i8-j9k0-l1m2-n3o4p5q6r7s8',
        href: '1',
        custom_content: '1',
        name: CONTENT_AWARE_CONSTANTS.ALLOW_LIST_CATEGORY_NAME,
        description: 'Additional global URLs exempt from Content Aware filtering',
        tenant: 'global',
        component: {
          domainsurls: ['additional-global.com', 'another-trusted.org'],
        },
      };

      modifiedPolicy.custom_categories = [...policyJson.custom_categories, additionalCategory];

      // Create a new mock blob storage with the modified policy
      const modifiedBlobStorage = new MockBlobStorageService([
        new MockUrlData(policy1Url, JSON.stringify(modifiedPolicy)),
      ]);

      const service = new PolicyDownloadService(modifiedBlobStorage, tenant);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          // Verify that the contentAwareAllowList property exists and contains the expected URLs
          expect(result?.contentAwareAllowList).toBeDefined();

          // Should include all global URLs from both categories
          expect(result?.contentAwareAllowList).toContain('example.com');
          expect(result?.contentAwareAllowList).toContain('trusted-site.org');
          expect(result?.contentAwareAllowList).toContain('school.edu');
          expect(result?.contentAwareAllowList).toContain('additional-global.com');
          expect(result?.contentAwareAllowList).toContain('another-trusted.org');

          // Should also include tenant-specific URLs
          expect(result?.contentAwareAllowList).toContain('tenant-specific.com');
          expect(result?.contentAwareAllowList).toContain('tenant-trusted.org');

          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });

    it('should handle a different tenant ID than the one in the policy', (done) => {
      // Create a different tenant ID
      const differentTenant = new TenantId('0bc510e6-f99d-11ea-88fe-cc024c4bbe3b');

      const service = new PolicyDownloadService(mockBlobStorage, differentTenant);

      service.onPolicyDownloaded.addListener((result: IPolicyConfig | undefined) => {
        try {
          // Verify that the contentAwareAllowList property exists and contains the expected URLs
          expect(result?.contentAwareAllowList).toBeDefined();

          // Should include global URLs
          expect(result?.contentAwareAllowList).toContain('example.com');
          expect(result?.contentAwareAllowList).toContain('trusted-site.org');
          expect(result?.contentAwareAllowList).toContain('school.edu');

          // Should NOT include tenant-specific URLs from the original tenant
          expect(result?.contentAwareAllowList).not.toContain('tenant-specific.com');
          expect(result?.contentAwareAllowList).not.toContain('tenant-trusted.org');

          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policy1Url);
    });
  });
});

import { LocalStorageAreaMock } from 'test-helpers/chrome-api';
import StorageService from './StorageService';

// Partial mock to enable use to test the auto-save functionality.
class StorageServicePartialMock extends StorageService {
  public constructor(name: string, storageArea: chrome.storage.StorageArea = chrome.storage.local) {
    super(name, storageArea);

    this.saveInTheBackground = jest.fn();
  }

  public readonly saveInTheBackground: () => void;
}

describe('StorageService', () => {
  // -----------------------------------------------------------------------------------------------

  // Regenerate the storage area mock before each test.
  // A reference is stored here so that it's easy to access with the correct type information.
  let localStorageAreaMock = new LocalStorageAreaMock();
  beforeEach(() => {
    localStorageAreaMock = new LocalStorageAreaMock();
    chrome.storage.local = localStorageAreaMock;
  });

  // -----------------------------------------------------------------------------------------------

  describe('constructor()', () => {
    it('stores the specified name', () => {
      const storageService = new StorageService('testData', undefined);
      expect(storageService.name).toEqual('testData');
    });

    it('throws an error if name is empty', () => {
      expect(() => new StorageService('', undefined)).toThrowError();
    });

    it('throws an error if name would conflict with default object property name', () => {
      expect(() => new StorageService('hasOwnProperty', undefined)).toThrowError();
    });

    it('disables auto-save by default', () => {
      const storageService = new StorageService('testData', undefined);
      expect(storageService.isAutoSaveEnabled).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('name', () => {
    it('returns the name of the data specified in the constructor', () => {
      const storageService = new StorageService('testData', undefined);
      expect(storageService.name).toEqual('testData');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('isAutoSaveEnabled', () => {
    it('returns true if auto-save is enabled', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.enableAutoSave();
      expect(storageService.isAutoSaveEnabled).toBeTrue();
    });

    it('returns false if auto-save is disabled', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.disableAutoSave();
      expect(storageService.isAutoSaveEnabled).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('size', () => {
    it('returns the number of properties in the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);
      expect(storageService.size).toEqual(3);
    });

    it('returns zero if no properties have been loaded or set yet', () => {
      const storageService = new StorageService('testData', undefined);
      expect(storageService.size).toEqual(0);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('entries()', () => {
    it('returns an iterator over all the properties in the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);

      const output = Array.from(storageService.entries());
      expect(output.length).toEqual(3);
      expect(output).toContainEqual(['hello', 'world']);
      expect(output).toContainEqual(['testing', 123]);
      expect(output).toContainEqual(['xyzzy', false]);
    });

    it('returns an empty iterator if there are no properties in the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      expect(Array.from(storageService.entries())).toEqual([]);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('Symbol.iterator()', () => {
    it('returns an iterator over all the properties in the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);

      const output = Array.from(storageService);
      expect(output.length).toEqual(3);
      expect(output).toContainEqual(['hello', 'world']);
      expect(output).toContainEqual(['testing', 123]);
      expect(output).toContainEqual(['xyzzy', false]);
    });

    it('returns an empty iterator if there are no properties in the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      expect(Array.from(storageService)).toEqual([]);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('keys()', () => {
    it('returns an iterator over all the property names in the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);

      const output = Array.from(storageService.keys());
      expect(output.length).toEqual(3);
      expect(output).toContain('hello');
      expect(output).toContain('testing');
      expect(output).toContain('xyzzy');
    });

    it('returns an empty iterator if there are no properties in the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      expect(Array.from(storageService.keys())).toEqual([]);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('values()', () => {
    it('returns an iterator over all the property values in the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);

      const output = Array.from(storageService.values());
      expect(output.length).toEqual(3);
      expect(output).toContain('world');
      expect(output).toContain(123);
      expect(output).toContain(false);
    });

    it('returns an empty iterator if there are no properties in the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      expect(Array.from(storageService.values())).toEqual([]);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('forEach()', () => {
    it('executes the specified callback for each property in the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);

      const callback = jest.fn();
      storageService.forEach(callback);

      expect(callback).toHaveBeenCalledTimes(3);
      expect(callback).toHaveBeenCalledWith('world', 'hello');
      expect(callback).toHaveBeenCalledWith(123, 'testing');
      expect(callback).toHaveBeenCalledWith(false, 'xyzzy');
    });

    it('does not execute the specified callback if there are no properties in the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      const callback = jest.fn();
      storageService.forEach(callback);
      expect(callback).not.toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('has()', () => {
    it('returns true if the named property is in the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);
      expect(storageService.has('testing')).toBeTrue();
    });

    it('returns false if the named property is not in the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);
      expect(storageService.has('blah')).toBeFalse();
    });

    it('returns false if the in-memory cache is empty', () => {
      const storageService = new StorageService('testData', undefined);
      expect(storageService.has('blah')).toBeFalse();
    });

    it('throws an error if the property name is empty', () => {
      const storageService = new StorageService('testData', undefined);
      expect(() => storageService.has('')).toThrowError();
    });

    it('throws an error if the property name would conflict with an object default property', () => {
      const storageService = new StorageService('testData', undefined);
      expect(() => storageService.has('hasOwnProperty')).toThrowError();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('get()', () => {
    it('returns the named property value if it exists in the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);
      expect(storageService.get('testing')).toBe(123);
    });

    it('returns undefined if the named property is not in the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);
      expect(storageService.get('blah')).toBeUndefined();
    });

    it('returns undefined if the in-memory cache is empty', () => {
      const storageService = new StorageService('testData', undefined);
      expect(storageService.get('blah')).toBeUndefined();
    });

    it('throws an error if the property name is empty', () => {
      const storageService = new StorageService('testData', undefined);
      expect(() => storageService.get('')).toThrowError();
    });

    it('throws an error if the property name would conflict with an object default property', () => {
      const storageService = new StorageService('testData', undefined);
      expect(() => storageService.get('hasOwnProperty')).toThrowError();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('getString()', () => {
    it('returns the named property value if it exists in the in-memory cache and is a string', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);
      expect(storageService.getString('hello')).toBe('world');
    });

    it('returns undefined if the named property value exists in the in-memory cache but is not a string', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);
      expect(storageService.getString('testing')).toBeUndefined();
    });

    it('returns undefined if the named property is not in the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);
      expect(storageService.getString('blah')).toBeUndefined();
    });

    it('returns undefined if the in-memory cache is empty', () => {
      const storageService = new StorageService('testData', undefined);
      expect(storageService.getString('blah')).toBeUndefined();
    });

    it('throws an error if the property name is empty', () => {
      const storageService = new StorageService('testData', undefined);
      expect(() => storageService.getString('')).toThrowError();
    });

    it('throws an error if the property name would conflict with an object default property', () => {
      const storageService = new StorageService('testData', undefined);
      expect(() => storageService.getString('hasOwnProperty')).toThrowError();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('getNumber()', () => {
    it('returns the named property value if it exists in the in-memory cache and is a number', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);
      expect(storageService.getNumber('testing')).toBe(123);
    });

    it('returns undefined if the named property value exists in the in-memory cache but is not a number', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);
      expect(storageService.getNumber('xyzzy')).toBeUndefined();
    });

    it('returns undefined if the named property is not in the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);
      expect(storageService.getNumber('blah')).toBeUndefined();
    });

    it('returns undefined if the in-memory cache is empty', () => {
      const storageService = new StorageService('testData', undefined);
      expect(storageService.getNumber('blah')).toBeUndefined();
    });

    it('throws an error if the property name is empty', () => {
      const storageService = new StorageService('testData', undefined);
      expect(() => storageService.getNumber('')).toThrowError();
    });

    it('throws an error if the property name would conflict with an object default property', () => {
      const storageService = new StorageService('testData', undefined);
      expect(() => storageService.getNumber('hasOwnProperty')).toThrowError();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('getBoolean()', () => {
    it('returns the named property value if it exists in the in-memory cache and is a boolean', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);
      expect(storageService.getBoolean('xyzzy')).toBeFalse();
    });

    it('returns undefined if the named property value exists in the in-memory cache but is not a boolean', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);
      expect(storageService.getBoolean('hello')).toBeUndefined();
    });

    it('returns undefined if the named property is not in the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('hello', 'world');
      storageService.set('testing', 123);
      storageService.set('xyzzy', false);
      expect(storageService.getBoolean('blah')).toBeUndefined();
    });

    it('returns undefined if the in-memory cache is empty', () => {
      const storageService = new StorageService('testData', undefined);
      expect(storageService.getBoolean('blah')).toBeUndefined();
    });

    it('throws an error if the property name is empty', () => {
      const storageService = new StorageService('testData', undefined);
      expect(() => storageService.getBoolean('')).toThrowError();
    });

    it('throws an error if the property name would conflict with an object default property', () => {
      const storageService = new StorageService('testData', undefined);
      expect(() => storageService.getBoolean('hasOwnProperty')).toThrowError();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('set()', () => {
    it('adds the named property to the in-memory cache if it does not already exist', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('foo', 'bar');
      expect(storageService.get('foo')).toBe('bar');
    });

    it('updates the named property in the in-memory cache if it already exists', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('foo', 'bar');
      storageService.set('foo', 12345);
      expect(storageService.get('foo')).toBe(12345);
    });

    it('throws an error if the property name is empty', () => {
      const storageService = new StorageService('testData', undefined);
      expect(() => {
        storageService.set('', 'bar');
      }).toThrowError();
    });

    it('throws an error if the property name would conflict with an object default property', () => {
      const storageService = new StorageService('testData', undefined);
      expect(() => {
        storageService.set('hasOwnProperty', 'bar');
      }).toThrowError();
    });

    it('deletes the named property if the specified value is undefined', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('foo', 'bar');
      storageService.set('foo', undefined);
      expect(storageService.get('foo')).toBeUndefined();
    });

    it('saves the cache to storage if auto-save is enabled', () => {
      const storageService = new StorageServicePartialMock('testData', undefined);
      storageService.enableAutoSave();
      storageService.set('foo', 'bar');
      expect(storageService.saveInTheBackground).toHaveBeenCalled();
    });

    it('does not modify storage if auto-save is disabled', () => {
      const storageService = new StorageServicePartialMock('testData', undefined);
      storageService.disableAutoSave();
      storageService.set('foo', 'bar');
      expect(storageService.saveInTheBackground).not.toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('delete()', () => {
    it('adds the named property to the in-memory cache if it does not already exist', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('foo', 'bar');
      storageService.delete('foo');
      expect(storageService.has('foo')).toBeFalse();
    });

    it('does not fall over if the named property does not exist', () => {
      const storageService = new StorageService('testData', undefined);
      expect(() => {
        storageService.delete('foo');
      }).not.toThrow();
    });

    it('does not affect other named properties', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('foo', 'bar');
      storageService.set('testing', 123);
      storageService.delete('foo');
      expect(storageService.has('testing')).toBeTrue();
    });

    it('throws an error if the property name is empty', () => {
      const storageService = new StorageService('testData', undefined);
      expect(() => {
        storageService.delete('');
      }).toThrow();
    });

    it('throws an error if the property name would conflict with an object default property', () => {
      const storageService = new StorageService('testData', undefined);
      expect(() => {
        storageService.delete('hasOwnProperty');
      }).toThrow();
    });

    it('saves the cache to storage if auto-save is enabled', () => {
      const storageService = new StorageServicePartialMock('testData', undefined);
      storageService.disableAutoSave();
      storageService.set('foo', 'bar');
      storageService.enableAutoSave();
      storageService.delete('foo');
      expect(storageService.saveInTheBackground).toHaveBeenCalled();
    });

    it('does not modify storage if auto-save is disabled', () => {
      const storageService = new StorageServicePartialMock('testData', undefined);
      storageService.set('foo', 'bar');
      storageService.delete('foo');
      expect(storageService.saveInTheBackground).not.toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  // enableAutoSave() is covered by other tests

  // -----------------------------------------------------------------------------------------------

  // disableAutoSave() is covered by other tests

  // -----------------------------------------------------------------------------------------------

  describe('load()', () => {
    it('overwrites in-memory cache from storage if merge is false', async () => {
      localStorageAreaMock.get.mockResolvedValue({ testData: { hello: 'world' } });
      const storageService = new StorageService('testData', localStorageAreaMock);
      storageService.set('foo', 'bar');
      await storageService.load(false);
      expect(storageService.get('foo')).toBeUndefined();
      expect(storageService.get('hello')).toEqual('world');
    });

    it('updates in-memory cache from storage if merge is true', async () => {
      localStorageAreaMock.get.mockResolvedValue({ testData: { hello: 'world' } });
      const storageService = new StorageService('testData', localStorageAreaMock);
      storageService.set('foo', 'bar');
      await storageService.load(true);
      expect(storageService.get('foo')).toEqual('bar');
      expect(storageService.get('hello')).toEqual('world');
    });

    it('clears the in-memory cache if no storage area was specified in the constructor and merge is false', async () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('foo', 'bar');
      await storageService.load(false);
      expect(storageService.get('foo')).toBeUndefined();
    });

    it('does nothing if no storage area was specified in the constructor and merge is true', async () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('foo', 'bar');
      await storageService.load(true);
      expect(storageService.get('foo')).toEqual('bar');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('populate()', () => {
    it('overwrites the existing in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('foo', 'bar');
      storageService.populate({ hello: 'world' });
      expect(storageService.get('foo')).toBeUndefined();
      expect(storageService.get('hello')).toEqual('world');
    });

    it('throws an error if the specified data is not an object, null, or undefined', () => {
      const storageService = new StorageService('testData', undefined);
      expect(() => {
        storageService.populate(123 as unknown as object);
      }).toThrow();
    });

    it('clears the in-memory cache if the value is null or undefined', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('foo', 'bar');
      storageService.populate(undefined);
      expect(storageService.get('foo')).toBeUndefined();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('save()', () => {
    it('overwrites storage from in-memory cache if merge is false', async () => {
      const storageService = new StorageService('testData', localStorageAreaMock);
      storageService.set('foo', 'bar');
      await storageService.save(false);
      expect(localStorageAreaMock.get).not.toHaveBeenCalled();
      expect(localStorageAreaMock.set).toHaveBeenCalledWith({
        testData: {
          foo: 'bar',
        },
      });
    });

    it('updates storage from in-memory cache if merge is true', async () => {
      localStorageAreaMock.get.mockResolvedValue({ testData: { hello: 'world' } });
      const storageService = new StorageService('testData', localStorageAreaMock);
      storageService.set('foo', 'bar');
      await storageService.save(true);
      expect(localStorageAreaMock.get).toHaveBeenCalled();
      expect(localStorageAreaMock.set).toHaveBeenCalledWith({
        testData: {
          foo: 'bar',
          hello: 'world',
        },
      });
    });

    it('does nothing if no storage area was specified in the constructor', async () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('foo', 'bar');
      await expect(storageService.save()).toResolve();
    });
  });

  // -----------------------------------------------------------------------------------------------

  // Note: We can't sensibly test saveInTheBackground()

  // -----------------------------------------------------------------------------------------------

  describe('clear()', () => {
    it('removes all properties from the in-memory cache', async () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('foo', 'bar');
      await storageService.clear();
      expect(storageService.get('foo')).toBeUndefined();
    });

    it('removes all properties from storage if a storage area was specified in the constructor', async () => {
      const storageService = new StorageService('testData', localStorageAreaMock);
      await storageService.clear();
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith('testData');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('clearMemory()', () => {
    it('removes all properties from the in-memory cache', () => {
      const storageService = new StorageService('testData', undefined);
      storageService.set('foo', 'bar');
      storageService.clearMemory();
      expect(storageService.get('foo')).toBeUndefined();
    });

    it('does not modify storage', () => {
      const storageService = new StorageService('testData', localStorageAreaMock);
      storageService.clearMemory();
      expect(localStorageAreaMock.remove).not.toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('clearStorage()', () => {
    it('removes all properties from storage', async () => {
      const storageService = new StorageService('testData', localStorageAreaMock);
      await storageService.clearStorage();
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith('testData');
    });

    it('does not modify the in-memory cache', async () => {
      const storageService = new StorageService('testData', localStorageAreaMock);
      storageService.set('foo', 'bar');
      await storageService.clearStorage();
      expect(storageService.get('foo')).toEqual('bar');
    });

    it('does nothing if no storage area was specified in the constructor', async () => {
      const storageService = new StorageService('testData', undefined);
      await expect(storageService.clearStorage()).toResolve();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('validateKey()', () => {
    it('throws an error if the specified name is empty', () => {
      expect(() => {
        StorageService.validateKey('');
      }).toThrow();
    });

    it('throws an error if the specified name would conflict with an object default property', () => {
      expect(() => {
        StorageService.validateKey('hasOwnProperty');
      }).toThrow();
    });

    it('does nothing if the specified name is valid', () => {
      expect(() => {
        StorageService.validateKey('testData');
      }).not.toThrow();
    });
  });
});

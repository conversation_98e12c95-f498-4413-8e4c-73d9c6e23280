import Blocklist from 'guardian/Blocklist';
import MiniBlocklistService from './MiniBlocklistService';

describe('MiniBlocklistService', () => {
  // -----------------------------------------------------------------------------------------------

  describe('constructor()', () => {
    it('stores the specified blocklist instance', () => {
      const blocklist = new Blocklist();
      const service = new MiniBlocklistService(blocklist);
      expect(service.blocklist).toBe(blocklist);
    });

    it('creates a new blocklist instance if none was specified', () => {
      const service = new MiniBlocklistService();
      expect(service.blocklist).toBeInstanceOf(Blocklist);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('load()', () => {
    it('loads the IWF list', () => {
      const service = new MiniBlocklistService();
      service.load();
      expect(
        service.blocklist.iwfList.contains(new URL('https://smoothwall.net/blocktest/iwf')),
      ).toBeTrue();
    });

    it('loads the search term categoriser', () => {
      const service = new MiniBlocklistService();
      service.load();

      const url = new URL('https://www.google.com/search?q=porn');
      const result = service.blocklist.searchTermCategoriser.categoriseUrl(url);

      // Ensure we extracted the search term successfully.
      expect(result.searchString).toEqual(' porn ');

      // Ensure the search term was categorised.
      // We don't care what the category is. We just care that it matched something.
      expect(result.categories.size).toBeGreaterThan(0);
    });

    it('loads the URL list categoriser', () => {
      const service = new MiniBlocklistService();
      service.load();
      const categoriser = service.blocklist.urlListCategoriser;

      // Ensure a couple of major URLs get categorised.
      // We don't care what the category IDs are. We just care that the URLs matched something.
      expect(categoriser.categoriseUrl(new URL('http://888.com')).categoryIds).not.toEqual(
        new Set(),
      );
      expect(categoriser.categoriseUrl(new URL('http://www.pornhub.com')).categoryIds).not.toEqual(
        new Set(),
      );
    });

    // This tests the removetags data:
    it('loads the negative URL list categoriser', () => {
      const service = new MiniBlocklistService();
      service.load();
      const categoriser = service.blocklist.negativeUrlListCategoriser;

      // Ensure a major URL gets categorised.
      // We don't care what the category IDs are. We just care that the URL matched something.
      expect(categoriser.categoriseUrl(new URL('http://nhs.uk')).categoryIds).not.toEqual(
        new Set(),
      );
    });

    it('loads the weighted phrase categoriser', () => {
      const service = new MiniBlocklistService();
      service.load();
      const categoriser = service.blocklist.weightedPhraseCategoriser;

      // Ensure a problematic phrase gets categorised.
      // We don't care what the category IDs are. We just care that the phrase matched something.
      const result = categoriser.categoriseContent('some pornography and other sexy stuff');
      expect(result.foundCategories.length).toBeGreaterThan(0);
    });

    // TODO: Test the video ID categoriser when we have mini-filter data for it.
  });

  // -----------------------------------------------------------------------------------------------
});

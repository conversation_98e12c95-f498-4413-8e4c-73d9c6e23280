import BlobDownloadResult from 'models/BlobDownloadResult';
import FileDownloadType from 'models/FileDownloadType';
import StandaloneEvent from 'utilities/StandaloneEvent';
import IBlobStorageService from './IBlobStorageService';

/**
 * Manages downloading files from a cloud blob storage.
 *
 * A new instance of the class should be used for each different request so they can be managed separately.
 *
 * @note This class was originally written for downloading files from Azure Blob Storage. However,
 *  it should be able to download any file type from any generic web-host.
 */
export default class BlobStorageService implements IBlobStorageService {
  constructor(retryAttempts = 10, retryInterval = 5000) {
    this._retryAttempts = retryAttempts;
    this._retryInterval = retryInterval;
  }

  /**
   * Downloads the file from the given url and reads it as the fileType.
   *
   * Emits the onDownloadSuccess if the download completes sucessfully. Otherwise it emits onDownloadFailed.
   * @param url The url to the blob
   * @param fileType The type of file that is being downloaded
   * @param eTag Optional ETag to set If-None-Match request header
   */
  public readonly start = (url: string, fileType: FileDownloadType, eTag?: string): void => {
    this._downloading = true;
    this._abortController = new AbortController();

    let promise;

    switch (fileType) {
      case FileDownloadType.blob:
        promise = this._getFile(url, eTag).then(async (response: Response | null | undefined) => {
          if (response === null || response === undefined) {
            return response;
          }
          return await response.blob();
        });
        break;
      case FileDownloadType.json:
        promise = this._getFileAsJson(url, eTag);
        break;
      case FileDownloadType.text:
        promise = this._getFileAsText(url, eTag);
        break;
    }

    promise
      .then((value) => {
        if (value === undefined) {
          this.onDownloadFailed.dispatch(
            new BlobDownloadResult(
              url,
              undefined,
              false,
              this._responseStatus,
              'Could not download the file from blob.',
            ),
          );
          return;
        }

        // Don't emit the event if the download should be cancelled.
        if (this._cancelDownload) {
          return;
        }

        if (value !== null)
          this.onDownloadSuccess.dispatch(
            new BlobDownloadResult(url, value, true, this._responseStatus),
          );
      })
      .catch((error: Error) => {
        // If the fetch was cancelled using the AbortController the promise will reject with the error name AbortError.
        if (error.name === 'AbortError') {
          console.debug('Blob fetch aborted.');
          return;
        }

        this.onDownloadFailed.dispatch(
          new BlobDownloadResult(url, undefined, false, this._responseStatus, error.message),
        );
        console.error('Error downloading from blob.', error);
      })
      .finally(() => {
        this._cancelDownload = false;
        this._downloading = false;
        this._responseStatus = 0;
      });
  };

  /**
   * Aborts all active connections to blob storage for this instance.
   */
  public readonly stop = (): void => {
    this._cancelDownload = true;
    this._abortController.abort();
  };

  /**
   * Downloads a file from the given url.
   * If it fails, this will retry up to the number of times specified in the constructor.
   *
   * @param url Url to get the file from.
   * @param eTag Optional e-tag associated with this file the last time it was downloaded. If
   *  specified, the file won't be downloaded if it hasn't changed.
   *
   * @returns A promise that resolves with the response if the file was successfully downloaded. If
   *  the file doesn't exist then it resolves to undefined. If an e-tag was specified, and the file
   *  hasn't changed since that e-tag, then the promise resolves to null.
   */
  private readonly _getFile = async (
    url: string,
    eTag?: string,
  ): Promise<Response | undefined | null> => {
    let retryAttempts = this._retryAttempts;
    while (retryAttempts > 0 && !this._cancelDownload) {
      const request: Request = new Request(url, this._abortController);
      if (eTag !== undefined) request.headers.append('If-None-Match', eTag);

      try {
        const response = await fetch(request);
        this._responseStatus = response.status;

        if (response.ok) {
          const responseETag = response.headers.get('ETag');
          if (responseETag !== null) this.onETag.dispatch(responseETag);

          return response;
        }

        if (eTag !== undefined && response.status === 304) {
          console.debug('Blob download with eTag responded 304 (Not Modified)', response);
          return null;
        }

        // Only attempt to retry with 5xx responses.
        if (!response.status.toString().startsWith('5')) {
          return undefined;
        }
      } catch {
        this._responseStatus = 0;
      }

      await new Promise((resolve) => setTimeout(resolve, this._retryInterval));
      retryAttempts--;
    }

    return undefined;
  };

  /**
   * Retrieves the text content of a file from the specified url.
   * If it fails, this will retry up to the number of times specified in the constructor.
   *
   * @param url Url to get the file from.
   * @param eTag Optional e-tag associated with this file the last time it was downloaded. If
   *  specified, the file won't be downloaded if it hasn't changed.
   *
   * @returns A promise that resolves to a string containing the file content if it was downloaded
   *  successfully. If the file doesn't exist then it resolves to undefined. If an e-tag was
   *  specified, and the file hasn't changed since that e-tag, then the promise resolves to null.
   */
  private readonly _getFileAsText = async (
    url: string,
    eTag?: string,
  ): Promise<string | undefined | null> => {
    const blob = await this._getFile(url, eTag);
    if (blob !== undefined && blob !== null) {
      return await blob.text();
    }

    return blob;
  };

  /**
   * Retrieves the parsed JSON content of a file from the specified url.
   * If it fails, this will retry up to the number of times specified in the constructor.
   *
   * @param url Url to get the file from.
   * @param eTag Optional e-tag associated with this file the last time it was downloaded. If
   *  specified, the file won't be downloaded if it hasn't changed.
   *
   * @returns A promise that resolves to an object or other data type containing the file content
   *  parsed from JSON, if it was downloaded successfully. If the file doesn't exist then it
   *  resolves to undefined. If an e-tag was specified, and the file hasn't changed since that
   *  e-tag, then the promise resolves to null. The promise will reject with an error if the file
   *  could not be parsed as JSON.
   */
  private readonly _getFileAsJson = async (
    url: string,
    eTag?: string,
  ): Promise<any | null | undefined> => {
    const text = await this._getFileAsText(url, eTag);
    if (text !== undefined && text !== null) {
      return JSON.parse(text);
    }

    return text;
  };

  // -----------------------------------------------------------------------------------------------
  // Events.

  /**
   * This event is dispatched when the blob is successfully downloaded.
   */
  public readonly onDownloadSuccess = new StandaloneEvent<[BlobDownloadResult]>();

  /**
   * This event is dispatched when there is an error downloading the blob.
   */
  public readonly onDownloadFailed = new StandaloneEvent<[BlobDownloadResult]>();

  /**
   * This event is dispatched when a sucessful download generates an ETag.
   */
  public readonly onETag = new StandaloneEvent<[string]>();

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Indicates if the service is currently downloading from blob storage.
   */
  public get isDownloading(): boolean {
    return this._downloading;
  }

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Indicates if the service is currently downloading from blob storage.
   */
  private _downloading = false;

  /**
   * Indicates if the current download process should be cancelled.
   */
  private _cancelDownload = false;

  /**
   * The response status from the fetch request.
   */
  private _responseStatus = 0;

  /**
   * An AbortController to abort a fetch request.
   */
  private _abortController = new AbortController();

  /**
   * The number of retry attempts to make when retrieving a blob.
   */
  private readonly _retryAttempts: number;

  /**
   * The interval between retry attempts.
   */
  private readonly _retryInterval: number;
}

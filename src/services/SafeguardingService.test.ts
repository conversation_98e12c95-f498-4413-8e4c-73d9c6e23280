import SafeguardingService from 'services/SafeguardingService';
import safeguardingRules from 'mini-blocklist-data/safeguardingRules';
import AccessLogEntry from 'models/AccessLogEntry';
import StorageService from 'services/StorageService';
import AlarmService from 'services/AlarmService';
import 'test-helpers/chrome-api';
import MockTelemetryService from '../test-helpers/MockTelemetryService';
import AccessLogManager from '../access-logs/AccessLogManager';

// The date/time string format expected by the safeguarding API.
// This should match: "Thu May 11 2023 03:45:03 GMT+0100 (British Summer Time)"
const localDateFormat =
  /^[a-z]{3} [a-z]{3} \d\d? \d\d\d\d \d\d:\d\d:\d\d GMT[+-]\d\d\d\d \([\w\s]+\)$/i;

describe('SafeguardingService', () => {
  let safeguardingService: SafeguardingService;
  let testLog: AccessLogEntry;
  let mockAccessLogManager: AccessLogManager;
  let alarmService: AlarmService;

  afterEach(() => {
    jest.clearAllMocks();
  });

  beforeEach(() => {
    alarmService = new AlarmService();
    const storageService: StorageService = new StorageService('safeguardingContext', undefined);
    const telemetryService = new MockTelemetryService();

    // Jest mock for AccessLogManager
    mockAccessLogManager = {
      lastUploadedAt: 0,
      getUploadCount: jest.fn((reset: boolean) => (reset ? 0 : 10)),
      populate: jest.fn(async (storageData: Record<string, any>) => {
        await Promise.resolve();
      }),
      start: jest.fn(async () => {
        await Promise.resolve();
      }),
      stop: jest.fn(() => {}),
      configure: jest.fn(async (hardwareId: string, tenantId: any, licenseInfo: any) => {
        await Promise.resolve();
      }),
      clearConfiguration: jest.fn(() => {}),
      clearLogs: jest.fn(async () => {
        await Promise.resolve();
      }),
      storeAccessLog: jest.fn((log: any, skipDeduplication: boolean) => {}),
      flush: jest.fn(async () => {
        await Promise.resolve();
      }),
      upload: jest.fn(async () => {
        await Promise.resolve();
      }),
    } as unknown as AccessLogManager;

    safeguardingService = new SafeguardingService(
      storageService,
      alarmService,
      telemetryService,
      mockAccessLogManager,
    );
    safeguardingService.loadRules(safeguardingRules);

    testLog = {
      time: Math.floor(Date.now() / 1000).toString(),
      groups: [],
      categories: [],
      took: 0,
    };
  });

  describe('matchCategories', () => {
    it('should match by category', async () => {
      testLog.categories.push('Self Harm');
      const match = safeguardingService.applyRules(testLog);

      expect(match).toBeDefined();
      expect(match?.title).toEqualCaseInsensitive('Suicide');
      expect(match?.weight).toEqual(3);
      expect(match?.importance).toEqual(1);
    });

    it('should exclude by category exclusions', async () => {
      testLog.categories.push('Self Harm');
      testLog.categories.push('Safeguarding Exclusions');
      const match = safeguardingService.applyRules(testLog);

      expect(match).toBeUndefined();
    });

    it('should match lower importance after exclusion of greater', async () => {
      testLog.categories.push('Self Harm');
      testLog.categories.push('Medical Information');
      const match = safeguardingService.applyRules(testLog);

      expect(match).toBeDefined();
      expect(match?.title).toEqualCaseInsensitive('Abuse');
      expect(match?.weight).toEqual(2);
      expect(match?.importance).toEqual(3);
    });
  });

  describe('generateLocalDateString', () => {
    it('returns a date and time string in the expected format', () => {
      const output = safeguardingService.generateLocalDateString(Date.now());
      expect(output).toMatch(localDateFormat);
    });

    it('returns a date string containing an abbreviated day name in English', () => {
      const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

      // Go through each day of the week, starting on Monday 15th May 2023.
      // Adjust for the current device's timezone to ensure it always starts on the right day.
      const offset = new Date().getTimezoneOffset() * 60000;
      for (let i = 0; i < 7; ++i) {
        const timestamp = 1684152000000 + offset + i * 86400000;
        const output = safeguardingService.generateLocalDateString(timestamp);
        expect(output).toMatch(new RegExp(String.raw`^${days[i]}\s`));
      }
    });

    it('returns a date string containing an abbreviated month name in English', () => {
      const months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ];

      // Go through each month of the year, starting on 15th January 2023.
      for (let i = 0; i < 12; ++i) {
        const timestamp = 1673784000000 + i * 2592000000;
        const output = safeguardingService.generateLocalDateString(timestamp);
        expect(output).toMatch(new RegExp(String.raw`^\w+\s+${months[i]}\s`));
      }
    });
  });

  describe('getLocalTimeZoneString', () => {
    it('returns a string containing a numeric offset and timezone name', () => {
      // Note: We can't test for a specific string as we can't predict or control the time
      //  zone of this device.
      const output = safeguardingService.getLocalTimeZoneString('en-US', new Date());
      expect(output).toMatch(/^GMT[+-]\d\d\d\d \([a-z\s/]+\)$/i);
    });
  });

  describe('when various safeguarding service rules are triggered and access logs should be uploaded', () => {
    it('should flush access logs when rule is triggered', async () => {
      const testLog: AccessLogEntry = {
        time: Math.floor(Date.now() / 1000).toString(),
        groups: [],
        categories: ['Self Harm'],
        safeguardingtheme: 'suicide', // this is hardcoded within SafeguardingService
        took: 0,
      };

      const flushSpy = jest.spyOn(mockAccessLogManager, 'flush');
      const match = await safeguardingService.handleSafeguarding(testLog);

      expect(match).toBeDefined();
      expect(flushSpy).toHaveBeenCalled();
    });

    it('when safeguarding service rule is triggered as a non-immediate alert and the context window has begun, then ensure accessLOgs are flushed', async () => {
      const testLog: AccessLogEntry = {
        time: Math.floor(Date.now() / 1000).toString(),
        groups: [],
        categories: ['Drugs'],
        safeguardingtheme: '',
        took: 0,
      };
      const flushSpy = jest.spyOn(mockAccessLogManager, 'flush');
      const match = await safeguardingService.handleSafeguarding(testLog);

      expect(match).toBeDefined();
      expect(flushSpy).toHaveBeenCalled();
    });

    it('when safeguarding service context window ends and is uploaded, ensure that accessLogs are flushed', async () => {
      // set some dummy settings so that we actually enable the upload method
      safeguardingService.setClientSettings('https://localhost', 'rfjwo2', true);

      const testLog: AccessLogEntry = {
        time: Math.floor(Date.now() / 1000).toString(),
        groups: [],
        categories: ['Drugs'],
        safeguardingtheme: '',
        took: 0,
      };

      // mock fetch to return an api response with a valid contextId
      const fetchMock = jest.fn();
      global.fetch = fetchMock;

      fetchMock.mockResolvedValue({
        status: 200,
        ok: true,
        text: async () => await Promise.resolve('12345'),
      });

      // start the context window and ensure the initial flush is called
      const flushSpy = jest.spyOn(mockAccessLogManager, 'flush');
      const match = await safeguardingService.handleSafeguarding(testLog);

      expect(match).toBeDefined();
      expect(flushSpy).toHaveBeenCalled();

      // use a different url to ensure it does not get deduplicated
      await safeguardingService.handleSafeguarding({
        ...testLog,
        ...{
          url: 'http://exampleA.com',
        },
      });
      const endMatch = await safeguardingService.handleSafeguarding({
        ...testLog,
        ...{
          url: 'http://exampleB.com',
        },
      });
      // check the size of the context window
      expect((safeguardingService as any)._context.safeguardAlerts).toHaveLength(2);

      // force execution of the end context window function
      alarmService.dispatch('safeguard-context'); // matches _alarmName in the service
      // dispatch is implicit async since many of the functions that are wired to the alarms are async
      await new Promise(process.nextTick);

      expect(endMatch).toBeDefined();
      expect(flushSpy).toHaveBeenCalledTimes(2);

      // check that the context was emptied
      expect((safeguardingService as any)._context.safeguardAlerts).toHaveLength(0);
    });
  });
});

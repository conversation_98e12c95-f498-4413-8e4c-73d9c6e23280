import {
  getExtensionVersion,
  capitalise,
  DmsSoftwareData,
  extractNativeAgentSoftwareData,
  simplifyUserAgentString,
} from 'utilities/Helpers';
import DeviceId from 'models/DeviceId';
import FetchService, { Action } from './FetchService';
import Jwt from 'models/Jwt';
import ObjectLiteral from 'models/ObjectLiteral';
import ProductCode from 'constants/ProductCode';
import ProvisioningInfo from 'provisioning/ProvisioningInfo';
import StandaloneEvent from 'utilities/StandaloneEvent';
import StorageService from './StorageService';
import TemplateString from 'models/TemplateString';
import ITelemetryService from './ITelemetryService';
import ExponentialBackOff from 'back-off-methods/ExponentialBackOff';
import IpService from './IpService';
import { TelemetryEventType } from '../constants/TelemetryEventType';

/**
 * Describes the expected structure the response body from a device management API request.
 */
interface ApiResponse {
  /**
   * Indicates whether the request was successful.
   */
  isSuccess?: boolean;

  /**
   * On success, contains the response data.
   */
  data?: {
    /**
     * A token we need to use for custom authentication with Firebase.
     * This is used for initial authentication, after which Firebase manages renewals internally.
     */
    jwt: string;

    /**
     * A unique identifier assigned to this instance of the extension by the cloud.
     */
    deviceId: string;

    /**
     * The path of a Firestore document containing user-specific information and configuration.
     */
    userDocumentPath: string;
  };

  /**
   * May contain zero or more errors if anything went wrong.
   * Errors may be specified even if the request was successful, in which case they may be regarded
   *  more like warnings.
   */
  errors?: [];
}

/**
 * Manages this device's cloud registration details.
 * This will register the device with the cloud, or update the registration where appropriate. It
 *  will also cache the registration details for later use.
 *
 * Calling code should ensure it adds listeners for the onRegistered and onUnlicensed events. The
 *  onFailed event may also be useful for informational purposes.
 *
 * @todo Show example usage.
 */
export default class DeviceRegistrationService {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance with the given injected dependencies.
   *
   * @param storageService Used to read and write cache values.
   * @param registrationFetchService Wraps fetch and retry logic for registration requests. A
   *  default one will be created if not specified.
   * @param updateFetchService Wraps fetch and retry logic for update requests. A default one will
   *  be created if not specified. This must not be the same instance as is used for registration.
   * @throws {Error} The same object has been passed for both registration and update fetch service.
   *
   * @todo Pass in retry logic for the fetch services.
   */
  public constructor(
    storageService: StorageService,
    telemetryService: ITelemetryService,
    ipService: IpService,
    registrationFetchService?: FetchService,
    updateFetchService?: FetchService,
  ) {
    this._storageService = storageService;
    this._telemetryService = telemetryService;
    this._ipService = ipService;

    if (registrationFetchService !== undefined && registrationFetchService === updateFetchService) {
      throw new Error('Cannot use the same fetch service for registration and update.');
    }

    this._registrationRequest =
      registrationFetchService ??
      new FetchService(new ExponentialBackOff(30000, 600000, 2, 5000), 0);
    this._registrationRequest.setResponseHandler(this._onRegistrationResponse);
    this._registrationRequest.onFinished.addListener(this._onRegistrationRequestFinished);
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Check if we're currently running; i.e. start() has been called, but stop() has not been called.
   * Note that this will continue returning true even after we've successfully registered, or
   *  registration has failed fatally.
   */
  public get isRunning(): boolean {
    return this._registrationUrlTemplate !== undefined;
  }

  /**
   * Check if we're currently running and we have successfully registered with the cloud.
   * This will return true even if the registration details were loaded from cache.
   * If this returns true then the jwt and deviceId properties will be useful.
   */
  public get isRegistered(): boolean {
    return this._jwt !== undefined && this._deviceId !== undefined;
  }

  /**
   * Get the authentication token which was most recently received from the cloud or cache.
   * Whenever a registration or update request succeeds, a new JWT is issued, cached, and passed to
   *  the onRegistered event. It can be retrospectively accessed here for convenience.
   *
   * @returns The JWT most recently received from the cloud or loaded from cache, if we have one.
   *  Returns undefined if we're not running or have never successfully registered.
   * @see onRegistered
   */
  public get jwt(): Jwt | undefined {
    return this._jwt;
  }

  /**
   * Get the device ID which was most recently received from the cloud.
   * Whenever we successfully register, a new device ID is assigned by the cloud. We then cache it
   *  to be sent in any future update requests. After any successful registration or update request,
   *  the device ID is passed to the onRegistered event. It can be retrospectively accessed here for
   *  convenience.
   *
   * @returns The device ID most recently received from the cloud or cache, if we have one. Returns
   *  undefined if we're not running or have never successfully registered.
   */
  public get deviceId(): DeviceId | undefined {
    return this._deviceId;
  }

  /**
   * Get the path of the user-specific document which was most recently received from the cloud.
   * Returns undefined if we're not running, have never successfully registered, or the API didn't
   *  provide this information last time we registered. This was introduced in November 2023.
   *
   * @see onRegistered
   */
  public get userDocumentPath(): string | undefined {
    return this._userDocumentPath;
  }

  // -----------------------------------------------------------------------------------------------
  // Events.

  /**
   * This event is triggered when registration data is available.
   * The data may have come from the cache or it may have been downloaded from the cloud.
   * Respectively, the parameters are:
   * 1. The JWT used for Firebase authentication.
   * 2. The device ID assigned by the cloud.
   * 3. The user document path specified by the cloud. This may be an empty string if we don't have
   *     a user document path yet, e.g. because the API didn't provide one.
   */
  public readonly onRegistered = new StandaloneEvent<[Jwt, DeviceId, string]>();

  /**
   * This event is triggered if registration has failed due to an error and will not be retried.
   * If the app was already successfully running then it can continue to do so in this case.
   */
  public readonly onFailed = new StandaloneEvent();

  /**
   * This event is triggered if registration failed because the customer is not licensed.
   * The app must stop all functionality in this case.
   */
  public readonly onUnlicensed = new StandaloneEvent();

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Start registering the device with the cloud.
   * If registration data is already cached, then this will get the data from the
   *  cache while waiting for the registration response.
   *
   * @param registrationUrlTemplate The template describing the URL to use for initially registering
   *  this device. It will contain a placeholder for the GLS hash.
   * @param updateUrlTemplate A template describing the URL to use for updating this device's
   *  registration. It will contain placeholders for the GLS hash and device ID.
   * @param provisioningInfo The provisioning info for this device. This information is sent up to
   *  the cloud on registration.
   * @param products Lists the products implemented by this client.
   * @returns A promise which resolves when the registration process has started
   *
   * @note If a new Firebase authentication token (JWT) is needed, then call requestNewAuth() after
   *  calling start.
   *
   * @warning The caller must ensure the returned promise has settled before calling this again, and
   *  before calling stop().
   */
  public readonly start = async (
    registrationUrlTemplate: TemplateString,
    provisioningInfo: ProvisioningInfo,
    products: ProductCode[],
  ): Promise<void> => {
    if (this.onRegistered.length === 0) {
      console.warn(
        'DeviceRegistrationService - No listeners have been added for the onRegistered event. Registration may not be detected by the rest of the application.',
      );
    }

    // If we're already running, then stop any existing attempts first.
    await this.stop();

    this._registrationUrlTemplate = registrationUrlTemplate;
    this._provisioningInfo = provisioningInfo;
    this._products = products;

    // If we have registration info cached from a previous run then use it until we reregister with DMS.
    if (this._loadFromCache()) {
      // If we reach here then we've registered before.
      console.debug(
        `DeviceRegistrationService - Loaded registration details from cache. Device ID: ${
          this._deviceId?.toString() ?? ''
        }`,
      );

      // Note: _jwt and _deviceId definitely won't be undefined here. However, _userDocumentPath may
      //  be undefined, depending on whether the API provides that information yet.
      this.onRegistered.dispatch(
        this._jwt as Jwt,
        this._deviceId as DeviceId,
        this._userDocumentPath ?? '',
      );
    }

    // Always send a new registration request to ensure we have the most up-to-date details.
    await this._startRegistrationRequest();
  };

  /**
   * Request a new authentication token from the cloud, even if we already have one cached.
   * This is useful if our Firebase authentication has been revoked, and the existing JWT has
   *  expired.
   *
   * You should call start() first, then wait for the onRegistered event to be triggered. Try the
   *  JWT passed to that event, and only call this if the JWT has expired or fails.
   */
  public readonly requestNewAuthenticationToken = async (): Promise<void> => {
    // Do nothing if we're still waiting for an existing request to finish.
    if (this._registrationRequest.isRunning) {
      console.debug(
        'DeviceRegistrationService discarding request for new authentication token as an existing registration or update request is in progress.',
      );
      return;
    }

    await this._startRegistrationRequest();
  };

  /**
   * Stop any attempts to register the device or update the registration.
   * This also clears any registration data stored in memory.
   * Calling this when registration hasn't been started has no effect.
   *
   * @warning Multiple concurrent calls to stop() are safe. However, the caller must ensure the
   *  returned promise has settled before calling start() again.
   */
  public readonly stop = async (): Promise<void> => {
    await this._registrationRequest.stop();

    clearInterval(this._reregisterInterval);
    this._errorsLogged = new Set();

    this._registrationUrlTemplate = undefined;
    this._provisioningInfo = undefined;
    this._products = undefined;
    this._deviceId = undefined;
    this._jwt = undefined;
    this._userDocumentPath = undefined;
  };

  /**
   * Force reregistration in the event that device check-in fails.
   */
  public readonly reregister = async (): Promise<void> => {
    await this._registrationRequest.stop();
    await this._storageService.clear();
    await this._startRegistrationRequest();
  };

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Load any existing registration data from the cache, if we have any.
   * It is loaded into members _jwt, _deviceId, and _userDocumentPath.
   *
   * @returns True if we successfully loaded registration data from the cache. False if there was no
   *  cached data, or it was invalid.
   */
  private readonly _loadFromCache = (): boolean => {
    const rawDeviceId = this._storageService.getString('deviceId');
    const rawJwt = this._storageService.getString('jwt');
    const userDocumentPath = this._storageService.getString('userDocumentPath');

    if (rawDeviceId === undefined || rawJwt === undefined || userDocumentPath === undefined) {
      return false;
    }

    try {
      this._deviceId = new DeviceId(rawDeviceId);
      this._jwt = new Jwt(rawJwt);
      this._userDocumentPath = userDocumentPath;
      return true;
    } catch (e: any) {
      console.warn('DeviceRegistrationService found invalid registration data in cache.', e);
      this._storageService.clear().catch(console.warn);
      return false;
    }
  };

  /**
   * Construct the body for a registration or update request, using currently stored information.
   * This assumes that the provisioning info and product list have already been initialised by
   *  start().
   *
   * @returns A promise which resolves to an object containing the body content for a registration
   *  or update request.
   */
  private readonly _makeRequestBody = async (): Promise<ObjectLiteral<any>> => {
    const software: DmsSoftwareData[] = [
      {
        name: 'sw-extension',
        version: getExtensionVersion(),
        userAgent: simplifyUserAgentString(navigator.userAgent),
      },
    ];

    // If we are (or have been) connected to a native agent, and it provided a version string, then
    //  send information about it to DMS.
    const agentSoftware = extractNativeAgentSoftwareData(
      this._provisioningInfo?.agentVersion ?? '',
    );
    if (agentSoftware !== undefined) {
      software.push(agentSoftware);
    }

    return {
      customerId: this._provisioningInfo?.serialId.toString(),
      tenantId: this._provisioningInfo?.tenantId?.toString() ?? '',
      userId: this._provisioningInfo?.user,
      hardwareId: this._provisioningInfo?.hardwareId,
      deviceName:
        this._provisioningInfo?.deviceName !== ''
          ? this._provisioningInfo?.deviceName
          : this._provisioningInfo?.hardwareId,
      platform: await DeviceRegistrationService.determinePlatform(),
      platformVersion: '0',
      publicIpAddresses: this._ipService.publicIpAddresses,
      privateIpAddresses: this._ipService.privateIpAddresses,
      software,
      checkInTime: Math.floor(Date.now() / 1000).toString(),

      // TODO: Enable strict validation when we're confident that all customers have provisioned
      //  their extensions correctly. (This affects tenant ID validation.)
      strictValidation: false,

      products: DeviceRegistrationService.stringifyProducts(this._products ?? []),
    };
  };

  /**
   * Start sending a registration request, with retries as needed.
   * This will stop any existing registration/update request which is in progress.
   */
  private readonly _startRegistrationRequest = async (): Promise<void> => {
    if (
      this._registrationUrlTemplate === undefined ||
      this._provisioningInfo === undefined ||
      this._products === undefined
    ) {
      throw new Error('Cannot start registration request. Not initialised.');
    }

    console.debug('DeviceRegistrationService - Sending registration request.');

    // Construct the registration request.
    // See: https://familyzone.atlassian.net/wiki/spaces/SWFZC/pages/2694454280666/Mv3+Client+Cloud+Contracts+Requirements#POST-%2Fdevices%2Fsw
    const url = this._registrationUrlTemplate.toUrl({
      glsHash: this._provisioningInfo.serialId.getGlsHash(),
    });
    const details: RequestInit = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Id': this._provisioningInfo.serialId.toString(),
      },
      body: JSON.stringify(await this._makeRequestBody()),
    };

    await this._registrationRequest.start(url, details, true);
  };

  /**
   * Process the response from a registration or update request.
   *
   * @param isUpdate If true, the response is from an update request. If false, the response is from
   *  a registration request.
   * @param response The response to be processes.
   * @returns A promise which resolves to the next action to be taken by the request wrapper (stop
   *  or retry). If the promise rejects then the action is assumed to be "retry".
   */
  private readonly _processResponse = async (
    isUpdate: boolean,
    response: Response,
  ): Promise<Action> => {
    // The response body should always be JSON.
    let body: ApiResponse;
    try {
      body = await response.json();
    } catch (e: any) {
      console.warn(
        `DeviceRegistrationService - Failed to parse server response as JSON. HTTP status was ${response.status}.`,
        e,
      );

      this._logError(e, isUpdate, response.status, []);

      return Action.retry;
    }

    if (Array.isArray(body?.errors) && body.errors.length > 0) {
      console.warn('DeviceRegistrationService - Error(s) reported by server.', body.errors);
    }

    if (response.status === 404) {
      if (isUpdate) {
        // A status 404 from an update request means the device ID wasn't found. We need to register
        //  again from scratch to get a new device ID.
        this._storageService.clear().catch(console.warn);
        this._jwt = undefined;
        this._deviceId = undefined;
        this._userDocumentPath = undefined;
        // Ensure the current handler has a chance to finish before starting registration, otherwise
        //  we may get stuck in a deadlock.
        setTimeout(() => {
          this._startRegistrationRequest().catch(console.warn);
        }, 0);
      } else {
        // A status 404 from a registration request means the customer isn't licensed or the serial
        //  isn't valid.
        this.onUnlicensed.deferDispatch();

        this._logError(
          `Request failed with status ${response.status}.`,
          isUpdate,
          response.status,
          body?.errors ?? [],
        );

        // TODO: Possibly write something to the cache which tells us not to bother trying again for
        //  a long time?
      }
      return Action.stop;
    }

    // TODO: Check for any specific errors (in body.errors), such as unlicensed.

    // If any other client error occurs then treat it as a permanent failure. There's no point
    //  trying again as it probably means our provisioning data was wrong, or there's a bug.
    if (response.status >= 400 && response.status < 500) {
      console.warn(`DeviceRegistrationService - Request failed with status ${response.status}.`);
      this._logError(
        `Request failed with status ${response.status}.`,
        isUpdate,
        response.status,
        body?.errors ?? [],
      );
      this.onFailed.dispatch();
      return Action.stop;
    }

    // Try again if any other error occurred.
    if (!response.ok || body?.isSuccess !== true) {
      console.warn(`DeviceRegistrationService - Request failed with status ${response.status}.`);

      if (!this._errorsLogged.has('error')) {
        this._logError(
          `DeviceRegistrationService - Request failed with status ${response.status}.`,
          isUpdate,
          response.status,
          body?.errors ?? [],
        );

        this._errorsLogged.add('error');
      }

      return Action.retry;
    }

    // Parse and validate the response data.
    const rawJwt = body.data?.jwt;
    if (typeof rawJwt !== 'string' || rawJwt === '') {
      console.error('DeviceRegistrationService - JWT empty or missing from server response.');

      if (!this._errorsLogged.has('empty')) {
        this._logError(
          `DeviceRegistrationService - JWT empty or missing from server response.`,
          isUpdate,
          response.status,
          body?.errors ?? [],
        );

        this._errorsLogged.add('empty');
      }

      return Action.retry;
    }

    try {
      this._jwt = new Jwt(rawJwt);
    } catch (e: any) {
      console.error('DeviceRegistrationService received invalid JWT from server.', e);

      if (!this._errorsLogged.has('empty-jwt')) {
        this._logError(e, isUpdate, response.status, body?.errors ?? []);

        this._errorsLogged.add('empty-jwt');
      }

      return Action.retry;
    }

    const rawDeviceId = body.data?.deviceId;
    if (typeof rawDeviceId !== 'string' || rawDeviceId === '') {
      console.error('DeviceRegistrationService - Device ID empty or missing from server response.');

      if (!this._errorsLogged.has('empty-id')) {
        this._logError(
          `DeviceRegistrationService - Device ID empty or missing from server response.`,
          isUpdate,
          response.status,
          body?.errors ?? [],
        );

        this._errorsLogged.add('empty-id');
      }

      return Action.retry;
    }

    try {
      this._deviceId = new DeviceId(rawDeviceId);
    } catch (e: any) {
      console.error('DeviceRegistrationService received invalid device ID from server.', e);

      if (!this._errorsLogged.has('invalid-id')) {
        this._logError(e, isUpdate, response.status, body?.errors ?? []);

        this._errorsLogged.add('invalid-id');
      }

      return Action.retry;
    }

    // Don't retry if the user document path is missing, empty, or invalid.
    // It's not vital to most filtering functionality, so move on without it. We'll try again next
    //  time the extension starts.
    if (typeof body.data?.userDocumentPath === 'string' && body.data.userDocumentPath !== '') {
      this._userDocumentPath = body.data.userDocumentPath;
    } else {
      console.warn(
        'DeviceRegistrationService - User document path empty or missing from server response. ' +
          'This may prevent remote RTLV from working. Filtering will continue without it.',
      );
    }

    // Check if the jwt we recieved has a sensible timestamp.
    // It could be out if the system time is out of sync.
    if (this._jwt?.hasExpired) {
      const date = new Date();
      this._telemetryService.logTrace(TelemetryEventType.DeviceRegistrationJwtExpired, {
        deviceDate: date.toISOString(),
        tokenExpiry: this._jwt.expiryDate?.toISOString(),
      });
    }

    console.debug(
      `DeviceRegistrationService - ${
        isUpdate ? 'Update' : 'Registration'
      } request succeeded. Device ID: ${this._deviceId.toString()}`,
    );

    this._telemetryService.logEvent(TelemetryEventType.DeviceRegistration, {
      isUpdate,
      errors: body.errors ?? '',
      userDocumentPath: this._userDocumentPath ?? '',
    });

    this._errorsLogged = new Set();

    // Send the registration details to the rest of the app.
    this.onRegistered.deferDispatch(this._jwt, this._deviceId, this._userDocumentPath ?? '');

    // Cache the details for future reference.
    this._storageService.set('jwt', this._jwt.token);
    this._storageService.set('deviceId', this._deviceId.toString());
    this._storageService.set('userDocumentPath', this._userDocumentPath);
    this._storageService.set(
      'products',
      DeviceRegistrationService.stringifyProducts(this._products ?? []),
    );
    this._storageService.saveInTheBackground();

    // Restart the reregister timeout every time we successfully register.
    this._lastRegistrationTime = Date.now();
    this._setReregisterInterval();

    return Action.stop;
  };

  /**
   * Sets the reregister interval. If a interval already exists it will be overwritten.
   */
  private readonly _setReregisterInterval = (): void => {
    clearInterval(this._reregisterInterval);
    this._reregisterInterval = setInterval(this._onReregisterInterval, 900000);
  };

  /**
   * Checks the last registration time and if the reregister interval has been exceeded.
   * If it has then a new registration request will be sent if one is not already pending.
   */
  private readonly _onReregisterInterval = (): void => {
    if (this._lastRegistrationTime + this._reregisterIntervalTime <= Date.now()) {
      if (!this._registrationRequest.isRunning && this.isRunning) {
        this._startRegistrationRequest().catch(console.warn);
      }
    }
  };

  // -----------------------------------------------------------------------------------------------
  // Event handlers.

  /**
   * Triggered when a response is received from a registration request.
   *
   * @param response The response to be handled.
   * @returns The next action to be taken by the request wrapper (stop or retry).
   */
  private readonly _onRegistrationResponse = async (response: Response): Promise<Action> => {
    return await this._processResponse(false, response);
  };

  /**
   * Triggered when the registration request wrapper finishes requesting for any reason.
   *
   * @param maxAttemptsReached If true, it stopped because the maximum number of attempts was
   *  reached.
   */
  private readonly _onRegistrationRequestFinished = (maxAttemptsReached: boolean): void => {
    if (!maxAttemptsReached) {
      return;
    }

    console.warn('DeviceRegistrationService - Stopping registration as it failed too many times.');

    this._errorsLogged = new Set();

    // Only dispatch a failure notification if we haven't previously registered successfully.
    if (this._jwt === undefined || this._deviceId === undefined) {
      this.onFailed.deferDispatch();
    }
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Given an array of enumerate product codes, return an array of the string equivalent.
   * @param products An array of enumerated product codes.
   * @returns An array of strings corresponding to the enumerated product codes.
   */
  public static readonly stringifyProducts = (products: ProductCode[]): string[] => {
    return products.map((p) => ProductCode[p]);
  };

  /**
   * Get the platform identifier which we need to report to the cloud on registration/update.
   *
   * @return A promise which resolves to a string containing the platform identifier. It will be an
   *  empty string if the platform could not be determined. The promise is never expected to reject.
   *
   * @todo Update this when the valid platform identifiers have been decided.
   */
  public static readonly determinePlatform = async (): Promise<string> => {
    try {
      // This will return 'win', 'mac', 'linux', or 'cros' (ChromeOS).
      const os = (await chrome.runtime.getPlatformInfo()).os;

      switch (os) {
        case 'cros':
          return 'ChromeOS';
        case 'win':
          return 'Windows';
        case 'mac':
          return 'macOS';
        default:
          return capitalise(os);
      }
    } catch (e: any) {
      console.warn('DeviceRegistrationService failed to retrieve platform info.', e);
    }
    return '';
  };

  /**
   * Logs an error to the telemetry service using the information provided.
   * @param error The error to log.
   * @param status The status number of the response.
   * @param errors The list of errors from the response body.
   */
  private readonly _logError = (
    error: string | Error,
    isUpdate: boolean,
    status: number,
    errors: string[],
  ): void => {
    this._telemetryService.logError(TelemetryEventType.DeviceRegistrationFailed, error, {
      isUpdate,
      status,
      errors,
    });
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * This object will be used to read/write cached data in local storage.
   * It's the caller's responsibility to load the cache into memory before calling start().
   * This object stores the following values in the cache:
   *
   * - jwt = The last JWT received from the cloud, stored as a string.
   * - deviceId = The last device ID received from the cloud, stored as a string.
   * - products = The last set of product codes which were successfully to the cloud by this device, stored as an array of strings.
   */
  private readonly _storageService: StorageService;

  /**
   * Sends device registration requests and retries.
   */
  private readonly _registrationRequest: FetchService;

  /**
   * The telemetry service for logging events.
   */
  private readonly _telemetryService: ITelemetryService;

  /**
   * The ip service for getting the device's ip addresses.
   */
  private readonly _ipService: IpService;

  /**
   * The time interval in milliseconds for when the extension should reregister with DMS.
   * This is currently 24 hours.
   */
  private readonly _reregisterIntervalTime: number = 86400000;

  /**
   * A template describing the URL to use for initially registering this device.
   * It will contain a placeholder for the GLS hash.
   * This will be undefined if we're not currently running.
   */
  private _registrationUrlTemplate?: TemplateString;

  /**
   * The provisioning info which was passed to start().
   * This will be undefined if we're not currently running.
   */
  private _provisioningInfo?: ProvisioningInfo;

  /**
   * The list of products which were passed to start().
   * This is not loaded from cache. It is only compared to the cache to detect changes. The cache is
   *  then updated if/when a registration of update request succeeds.
   * This will be undefined if we're not currently running.
   */
  private _products?: ProductCode[];

  /**
   * The authentication token we most recently loaded from cache or received from the cloud.
   * It is received from the cloud on successful registration or update. We only register or update
   *  when necessary, and will operate from the cache where possible.
   * This is used for custom authentication with Firebase.
   */
  private _jwt?: Jwt;

  /**
   * The device ID we most recently loaded from cache or received from the cloud.
   * It is received from the cloud on successful registration, and then passed back up to the cloud
   *  if we need to send an update. We only register or update when necessary, and will operate from
   *  the cache where possible.
   */
  private _deviceId?: DeviceId;

  /**
   * Path of the Firestore document containing user-specific information and configuration.
   * It is received from the cloud on successful registration. The document contains things like
   *  RTLV state.
   *
   * @note There won't always be a document at this path. We need to listen for creation, deletion,
   *  and modification.
   */
  private _userDocumentPath?: string;

  /**
   * The interval for tracking if the extension should reregister with DMS.
   */
  private _reregisterInterval: ReturnType<typeof setInterval> | undefined;

  /**
   * The last time the extension successfully registered with DMS.
   */
  private _lastRegistrationTime: number = 0;

  /**
   * Holds a list of all errors logged for one run.
   *
   * This is to stop us logging the same error after a retry, but we can still log a new error in a retry.
   */
  private _errorsLogged = new Set<string>();
}

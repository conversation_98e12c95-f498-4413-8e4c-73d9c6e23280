/**
 * Allow the user to navigate to named pages via the omnibox.
 * This will add suggestions to the box when the user starts typing.
 * It's triggered when the user has entered the omnibox keyword specified in the manifest file.
 *
 * @example
 *
 *    let omniboxService = new OmniboxService();
 *    omniboxService.setDestination('Diagnostics', 'View diagnostic info', 'views/diagnostics.html');
 */
export default class OmniboxService {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Registers omnibox event handlers with the browser.
   * The caller should call setDestination() after to construction to add pages to the omnibox.
   */
  public constructor() {
    chrome.omnibox.onInputEntered.addListener(this._onInputEntered);
    chrome.omnibox.onInputChanged.addListener(this._onInputChanged);
  }

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Specify a named destination which will be handled by the omnibox.
   * The name and description will appear in autocomplete suggestions.
   * If another destination with the same name has previously been set then this will update it.
   *
   * @param {string} name A unique name for the destination. If the user enters this then the active
   *  tab be directed to the specified URL. This name will appear in the autocomplete suggestions in
   *  the omnibox. The name must not be empty.
   * @param {string} description A description of the destination. This will appear alongside the
   *  name in the autocomplete suggestions in the omnibox.
   * @param {string} url The URL of the destination. If the user enters the specified name in the
   *  omnibox then the active tab will be directed to this URL. The URL must not be empty.
   * @throws {Error} The name or URL is empty.
   */
  public setDestination = (name: string, description: string, url: string): void => {
    if (name === '') {
      throw Error('A name is required for an omnibox destination');
    }

    if (url === '') {
      throw Error('A URL is required for an omnibox destination');
    }

    this._destinations.set(name, { description, url });
    this._updateCachedSuggestions();
  };

  /**
   * Stop suggesting or responding to a named destination.
   * It will no longer appear in the autocomplete suggestions, and nothing will happen if the user
   *  enters the name in the omnibox.
   *
   * @param {string} name The name of the destination to remove. This should corresponds to the name
   *  specified in a previous call to setDestination(). Nothing happens if no such destination
   *  exists.
   */
  public deleteDestination = (name: string): void => {
    this._destinations.delete(name);
    this._updateCachedSuggestions();
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Create and cache an array of autocomplete suggestions from the currently stored destinations.
   * This is called internally each time a destination is added, updated, or removed.
   */
  private readonly _updateCachedSuggestions = (): void => {
    // Generate an array of destinations, then map it onto an array of SuggestResult instances.
    this._cachedSuggestions = Array.from(this._destinations, ([name, destination]) => ({
      content: name,
      description: destination.description,
    }));
  };

  // -----------------------------------------------------------------------------------------------
  // Event handlers.

  private readonly _onInputEntered = (text: string): void => {
    // Do nothing if the destination isn't recognised.
    const destination = this._destinations.get(text);
    if (destination === undefined) {
      console.log(`Omnibox destination not recognised: ${text}`);
      return;
    }

    // Find the tab which the user is currently using.
    chrome.tabs
      .query({ active: true, currentWindow: true, windowType: 'normal' })
      .then(async (tabs: chrome.tabs.Tab[]) => {
        if (tabs.length === 0 || tabs[0].id === undefined) {
          console.warn('Unable to locate the active tab for omnibox input');
          return;
        }

        // Direct the tab to the specified destination.
        return await chrome.tabs.update(tabs[0].id, { url: destination.url });
      })
      .catch(() => {});
  };

  private readonly _onInputChanged = (
    text: string,
    suggest: (suggestResults: chrome.omnibox.SuggestResult[]) => void,
  ): void => {
    // NOTE: At the time of writing, the suggest() function fails under manifest v3.
    suggest(this._cachedSuggestions);
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * A map containing the pages which the omnibox can take the user to.
   * Each key is the name which the user must enter to go to the destination.
   * Each value contains a description and URL of the page.
   * The name and description will appear in autocomplete suggestions in the omnibox.
   */
  private readonly _destinations = new Map<string, Destination>();

  /**
   * A cached copy of the suggestions which we'll show in the omnibox when the user starts typing.
   * This needs to be updated by calling updateCachedSuggestions() every time _destinations changes.
   */
  private _cachedSuggestions: chrome.omnibox.SuggestResult[] = [];
}

/**
 * Contains information about a page which the omnibox can take the user to.
 * This is associated with a name in the _destinations map.
 */
class Destination {
  /**
   * A description of the page.
   * This will appear in the autocomplete suggestions.
   */
  public description: string = '';

  /**
   * The URL which the user will be taken to.
   */
  public url: string = '';
}

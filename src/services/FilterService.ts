import CategoryInfo from 'models/CategoryInfo';
import Guardian from 'guardian/Guardian';
import MiniBlocklistService from 'services/MiniBlocklistService';
import FilterDecision from 'guardian/models/FilterDecision';
import FilterMode from 'constants/FilterMode';
import Blocklist from 'guardian/Blocklist';
import StandaloneEvent from 'utilities/StandaloneEvent';
import { IPolicyConfig } from 'models/PolicyConfigModels';
import IpService from './IpService';
import BlocklistStatus from 'constants/BlocklistStatus';

/**
 * Combines the full filter and mini-filter functionality into a single service.
 * The full filter provides the main functionality which should be used to analyse URLs and content
 *  most of the time. However, it's slow to download and update.
 * The mini-filter is provided as a fall-back. It's loaded from hard-coded data so it's always
 *  available.
 */
export default class FilterService {
  constructor(ipService: IpService) {
    this.fullFilter = new Guardian(FilterMode.full, ipService, new Blocklist());

    this.fullFilter.blocklist.onBlocklistStatusChanged.addListener(this._blocklistStatusChanged);
  }

  // TODO: Provide a callback which should be invoked when a rescan is required?

  // -----------------------------------------------------------------------------------------------
  // Events.

  /**
   * Trigger when the filter mode has been changed.
   * The parameters give the new and old modes, respectively. (Note: new is first!)
   *
   * @see filterMode
   */
  public readonly onFilterModeChanged = new StandaloneEvent<[FilterMode, FilterMode]>();

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Get the blocklist object used for full-filter.
   * This is exposed so that the blocklist can be updated.
   */
  public get fullBlocklist(): Blocklist {
    return this.fullFilter.blocklist;
  }

  /**
   * Get the category data from whichever filter we're using.
   */
  public get categories(): Record<string, CategoryInfo> {
    if (this.filterMode === FilterMode.full) return this.fullFilter.blocklist.categoryData;
    else if (this.filterMode === FilterMode.mini) return this._miniFilter.blocklist.categoryData;
    else return {};
  }

  /**
   * Get the current filtering mode.
   * This determines if and how URLs and content will be analysed and blocked.
   */
  public get filterMode(): FilterMode {
    if (this._filteringDisabled || (!this.fullFilter.isReady && this.disableMiniFilter)) {
      return FilterMode.disabled;
    }

    return this.fullFilter.isReady ? FilterMode.full : FilterMode.mini;
  }

  /**
   * Sets the current filter status to enabled/disabled.
   * @param isDisabled If true the status of the filter will be set to disabled.
   */
  public readonly setFilteringDisabled = (isDisabled: boolean): void => {
    this._filteringDisabled = isDisabled;
    this._checkIfFilterModeChanged();
  };

  // -----------------------------------------------------------------------------------------------
  // Filtering operations.

  /**
   * Check whether the specified content should be allowed according to our filtering services.
   * This will use the full filter if it's ready, but will fall-back on the mini-filter otherwise.
   *
   * @param url The URL of the content, represented as a string or a URL object.
   * @param mainFrameUrl An optional url that will be analysed for video ids to enable background requests on video sites.
   * @param content The content of the page to analyse. This may be undefined or truncated.
   * @returns Returns an object describing how to handle the content.
   */
  public readonly analyse = (
    url: string | URL,
    mainFrameUrl?: URL,
    content?: string,
  ): FilterDecision => {
    // Ensure the URL is valid.
    if (typeof url === 'string') {
      try {
        url = new URL(url);
      } catch (e: any) {
        console.warn(`FilterService - Allowing invalid URL: ${url.toString()}`);
        return FilterDecision.allowWithoutLogging();
      }
    }

    // Allow URLs which aren't subject to filtering, such as built-in browser pages.
    if (!this.isFilterableUrl(url)) {
      return FilterDecision.allowWithoutLogging();
    }

    // Try and use the full filter even if filtering is disabled to use the most up to date iwf list.
    if (
      this.filterMode === FilterMode.full ||
      (this.filterMode === FilterMode.disabled && this.fullFilter.isReady)
    ) {
      if (!this.fullFilter.isReady) {
        console.warn('FilterService - Using full filter before it is ready.');
      }
      return this.fullFilter.analyse(url, this._filteringDisabled, mainFrameUrl, content);
    }

    // TODO: Hide the content initially, but re-scan later when the filter has loaded.

    // We should still filter with the iwf list even if filtering is disabled.
    return this._miniFilter.analyse(url, this._filteringDisabled, mainFrameUrl, content);
  };

  // -----------------------------------------------------------------------------------------------
  // Loading and saving operations.

  /**
   * Load the mini-filter from hard-coded data.
   */
  public readonly loadMiniFilter = (): void => {
    const miniBlocklistService = new MiniBlocklistService(this._miniFilter.blocklist);
    miniBlocklistService.load();
  };

  public setCustomerData = (policyConfig: IPolicyConfig): void => {
    this.fullFilter.setPolicyConfig(policyConfig);
    this._checkIfFilterModeChanged();
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Check if a string contains a valid URL which is subject to filtering.
   * A URL which is not subject to filtering should be ignored by our extension; i.e. the request or
   *  navigation event should be allowed to proceed as normal. This is the case for built-in browser
   *  or extension pages.
   *
   * @param {string} url A string containing the URL to check. If it isn't a valid URL then this
   *  function will return false.
   * @returns {boolean} Returns true if the specified string contains a valid URL which should be
   *  subject to filtering. Returns false if it does not contain a valid URL, or the URL should
   *  bypass filtering.
   */
  public readonly isFilterableUrl = (url: URL): boolean => {
    // TODO: Possibly put a list of non-filtering URLs/patterns in the extension config?

    // Ignore built-in browser and extension pages.
    const browserPrefixes: string[] = ['chrome', 'edge', 'about'];
    if (browserPrefixes.some((prefix: string): boolean => url.protocol.startsWith(prefix))) {
      return false;
    }

    // Ignore telemetry sent by this extension.
    // This will only matter if we're responding to Web Request events.
    if (url.toString() === 'https://dc.services.visualstudio.com/v2/track') {
      return false;
    }

    return true;
  };

  /**
   * Converts category IDs and custom category IDs into names.
   *
   * @param categoryIds Array of IDs given by a filter decision.
   */
  public readonly getCategoryNames = (categoryIds: string[]): string[] => {
    const categoryNames = categoryIds
      .map((categoryId) => {
        let categoryName: string | undefined = this.categories[+categoryId]?.name;
        if (categoryName === undefined) {
          categoryName = this.fullFilter.policyConfig?.custom_categories.find(
            (customCategory) => customCategory.category_id === categoryId,
          )?.name;
        }
        return categoryName;
      })
      .filter((categoryName) => categoryName);
    return categoryNames as string[];
  };

  /*
   * Checks if the new filter mode is different to the last mode.
   * If it is then the onFilterModeChanged event will be triggered.
   * @param newMode The new filter mode.
   */
  private readonly _checkIfFilterModeChanged = (): void => {
    const currentMode = this._lastFilterMode;
    const newMode = this.filterMode;

    if (currentMode !== newMode) {
      console.debug(
        `FilterService - Filter mode changed from ${FilterMode[currentMode]} to ${FilterMode[newMode]}`,
      );

      this._lastFilterMode = newMode;
      this.onFilterModeChanged.dispatch(newMode, currentMode);
    }
  };

  /**
   * Called when the status of the blocklist changes.
   *
   * It will check if the filter mode has changed because of the update. If it has then the onFilterModeChanged event will be triggered.
   * @param newStatus The new status of the blocklist.
   */
  private readonly _blocklistStatusChanged = (newStatus: BlocklistStatus): void => {
    this._checkIfFilterModeChanged();
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Implements our mini-filter, which is used while the full filter is loading/updating.
   * The mini-filter is based on a small amount of hard-coded data, so it loads very quickly.
   */
  private readonly _miniFilter: Guardian = new Guardian(FilterMode.mini);

  /**
   * Implements the main filtering functionality.
   * This can take some time to download / update so we'll fall-back on the mini filter when
   *  necessary.
   */
  public readonly fullFilter: Guardian;

  /**
   * Indicates if the mini-filter has been disabled by policy.
   * This doesn't indicate whether the mini-filter is currently being used. It only indicates
   *  whether we're allowed to use it, based on policy.
   */
  public disableMiniFilter: boolean = false;

  /**
   * Indicates if filtering is currently disabled. This is usually because the licence is not valid.
   */
  private _filteringDisabled: boolean = false;

  /**
   * Holds the last filter mode for comparison when it changes.
   */
  private _lastFilterMode: FilterMode = FilterMode.unknown;
}

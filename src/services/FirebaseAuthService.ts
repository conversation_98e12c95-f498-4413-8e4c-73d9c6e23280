import { FirebaseApp } from 'firebase/app';
import { User as FirebaseUser, Auth as FirebaseAuth, Unsubscribe } from 'firebase/auth';
import {
  getAuth as getFirebaseAuth,
  signInWithCustomToken,
  indexedDBLocalPersistence,
} from 'firebase/auth/web-extension';
import Jwt from 'models/Jwt';
import { TelemetryEventType } from '../constants/TelemetryEventType';
import StandaloneEvent from 'utilities/StandaloneEvent';
import ITelemetryService from './ITelemetryService';

/**
 * Manages the process of authenticating with Firebase, and monitoring for authentication failures.
 * Calling code should listen for the onAuthenticated event. When it's triggered, any operations
 *  requiring authentication can go ahead.
 * It should also listen for the onAuthenticationRequired event. When it's triggered, call
 *  authenticate() with a suitable JWT.
 *
 * @example
 *  const firebaseAuthService = new FirebaseAuthService();
 *  firebaseAuthService.onAuthenticated.addListener(...);
 *  firebaseAuthService.onAuthenticationRequired.addListener(...);
 *  firebaseAuthService.start(firebaseApp); // <-- get firebaseApp from FirebaseAppService
 *  firebaseAuthService.authenticate(jwt); // <-- get JWT from DeviceRegistrationService
 */
export default class FirebaseAuthService {
  /**
   *
   */
  constructor(telemetryService: ITelemetryService) {
    this._telemetryService = telemetryService;
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Check if this service is currently running (i.e. start() has been called).
   */
  public get isRunning(): boolean {
    return this._firebaseAuth !== undefined;
  }

  /**
   * Check if authentication has happened successfully.
   *
   * @see onAuthenticated
   */
  public get isAuthenticated(): boolean {
    return this._firebaseUser !== undefined;
  }

  /**
   * Get details of the Firebase user we are authenticated as.
   * Returns undefined if we're not running or not authenticated.
   */
  public get firebaseUser(): FirebaseUser | undefined {
    return this._firebaseUser;
  }

  // -----------------------------------------------------------------------------------------------
  // Events.

  /**
   * Triggered when we are successfully authenticated with Firebase.
   * This may occur immediately after calling start(), e.g. if we already had authentication details
   *  persisted locally. Otherwise, it should occur after calling authenticate().
   */
  public readonly onAuthenticated = new StandaloneEvent();

  /**
   * Triggered when a new token is required for authenticating with Firebase.
   * This will be triggered shortly after calling start() if we didn't have any authentication
   *  details cached. It will also be triggered if we try to use an expired token, or our
   *  authentication gets revoked remotely.
   */
  public readonly onAuthenticationRequired = new StandaloneEvent();

  /**
   * Triggered if authentication failed due to an error, and it's not likely to resolve itself.
   * This may indicate that there's a problem in the back-end, e.g. an invalid JWT was issued. It
   *  probably isn't worth trying again.
   */
  public readonly onAuthenticationFailed = new StandaloneEvent();

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Start the service running for the given Firebase app.
   * This will initialise the authentication mechanism, and will immediately authenticate if
   *  suitable details have been persisted locally.
   * Multiple consecutive calls should have no effect, and concurrent calls should be safe. However,
   *  it's advisable to wait until the return promise has settled before calling this again, and
   *  before calling stop().
   *
   * The caller should probably add listeners to the onAuthenticated and onAuthenticationRequired
   *  events before calling this.
   *
   * @param firebaseApp The Firebase app we are authenticating with. This must be initialised.
   *
   * @see onAuthenticated
   * @see onAuthenticationRequired
   * @see stop()
   */
  public readonly start = async (firebaseApp: FirebaseApp): Promise<void> => {
    if (this._firebaseAuth === undefined) {
      this._firebaseAuth = getFirebaseAuth(firebaseApp);
    }

    if (this._unsubscribeAuthStateChange === undefined) {
      this._unsubscribeAuthStateChange = this._firebaseAuth.onAuthStateChanged(
        this._onAuthStateChanged,
      );
    }

    // This is necessary in an mv3 extension to ensure Firebase remembers the authentication details
    //  between runs:
    await this._firebaseAuth.setPersistence(indexedDBLocalPersistence);
  };

  /**
   * Try to authenticate Firebase using the given token.
   * Authentication is handled asynchronously in the background. If authentication is successful,
   *  then the onAuthenticated event will be triggered. If it
   *
   * @param jwt The token to authenticate with.
   * @throws {Error} The authentication service hasn't been initialised yet.
   *
   * @note You must call start() before calling this.
   */
  public readonly authenticate = (jwt: Jwt): void => {
    if (this._firebaseAuth === undefined) {
      throw new Error('Cannot authenticate with Firebase. Not initialised.');
    }

    this._scheduleAuthenticationTimeout(jwt, 0);
  };

  /**
   * Stops this service running, and optionally de-authenticate.
   * Multiple consecutive calls will have no effect, and concurrent calls should be safe.
   *
   * @param signOut If true, our authentication session will be ended (if we have one). This means a
   *  new JWT will be required next time we need to authenticate. If false, our authentication
   *  session will persist next time start() is called.
   *
   * @warning The caller must ensure the returned promise has settled before calling start() again,
   *  otherwise the behaviour may be unreliable.
   */
  public readonly stop = async (signOut: boolean = false): Promise<void> => {
    this._cancelAuthenticationTimeout();

    // It's important to unsubscribe from state changes before signing-out, otherwise we'll get a
    //  spurious event to handle later.
    if (this._unsubscribeAuthStateChange !== undefined) {
      this._unsubscribeAuthStateChange();
      this._unsubscribeAuthStateChange = undefined;
    }

    if (signOut) {
      await this._firebaseAuth?.signOut();
    }

    this._firebaseUser = undefined;
    this._firebaseAuth = undefined;
  };

  // -----------------------------------------------------------------------------------------------
  // Event handlers.

  /**
   * This is triggered when we've signed into or out of Firebase.
   * It is also triggered automatically when Firebase authentication is first initialised. That
   *  initial event tells us whether it has successfully used internally persisted authentication
   *  details.
   *
   * @note If we were already signed in, then sign in again using a new JWT for the same user, then
   *  this won't be triggered again. It will only be triggered if the authentication state *and* the
   *  user info both change.
   * @note If we were signed in as one user, then sign in as a different user, then this will be
   *  called twice; once for each user. It won't be called with null in between them unless we
   *  explicitly sign out.
   *
   * @param newUser Contains details of the user we're signed into Firebase as. If null, then it
   *  means we've been signed out. This could mean our authentication has been revoked remotely.
   */
  private readonly _onAuthStateChanged = (newUser: FirebaseUser | null): void => {
    // Sanity-check: Do nothing if we've been stopped.
    if (this._firebaseAuth === undefined) {
      console.warn('FirebaseAuthService ignoring state change as Firebase app is not initialised.');
      return;
    }

    const oldUser = this._firebaseUser;
    this._firebaseUser = newUser ?? undefined;

    // Have we been logged-out?
    if (newUser === null) {
      if (oldUser === undefined) {
        // We have just initialised and haven't got an existing authentication session.
        // This is normal if the extension has just been installed, or storage has been cleared.
        console.debug('FirebaseAuthService - Ready to authenticate.');
      } else {
        // We have been signed-out of Firebase. We don't normally expect this to happen. It may mean
        //  the authentication has been revoked remotely.
        console.warn('FirebaseAuthService - Signed-out of Firebase.');
        this._telemetryService.logEvent(TelemetryEventType.FirebaseSignOut);
      }

      this.onAuthenticationRequired.deferDispatch();
      return;
    }

    // Double-check that the authentication timeout isn't running.
    this._cancelAuthenticationTimeout();

    // If we reach here, then we've just been logged-in, or changed user.
    if (oldUser === undefined) {
      console.debug(`FirebaseAuthService - Authenticated with uid: ${newUser.uid}`);
    } else if (oldUser.uid === newUser.uid) {
      // The event doesn't seem to get triggered in this case.
      console.debug(`FirebaseAuthService - Re-authenticated with same uid: ${newUser.uid}`);
    } else {
      // We don't normally expect the UID to change while we're running. However, it's determined by
      //  the server so we hypothetically could get assigned a new one if device registration gets
      //  updated.
      console.warn(`FirebaseAuthService - Re-authenticated with new uid: ${newUser.uid}`);
    }

    this.onAuthenticated.deferDispatch();
  };

  /**
   * Triggered periodically to try (or retry) authentication using a custom token.
   * If this fails with a temporary error, then another attempt will be scheduled.
   *
   * @param jwt The token to use for authentication.
   *
   * @see authenticate()
   * @see _scheduleAuthenticationTimeout()
   * @see _authenticationTimeout
   */
  private readonly _onAuthenticationTimeout = async (jwt: Jwt): Promise<void> => {
    this._cancelAuthenticationTimeout();

    if (this._firebaseAuth === undefined) {
      console.warn(
        'FirebaseAuthService - Cancelling authentication timer. Firebase auth is not initialised.',
      );
      this._cancelAuthenticationTimeout();
      return;
    }

    try {
      // We deliberately don't dispatch the onAuthenticated event here. We'll wait until the auth
      //  state change handler is invoked. That way, it goes through the same route regardless of
      //  whether we explicitly authenticated here, or loaded a cached authentication on startup.
      await signInWithCustomToken(this._firebaseAuth, jwt.token);

      this._isRetry = false;
    } catch (e: any) {
      // TODO: Ideally, we should be comparing codes to the AuthErrorCodes map defined by Firebase,
      //       instead of string literals. However, it currently fails because AuthErrorCodes is
      //       undefined. Hopefully this is a temporary bug.

      // If the token has expired then we need a new one.
      // AuthErrorCodes.TOKEN_EXPIRED
      if (e.code === 'auth/user-token-expired') {
        console.warn(`FirebaseAuthService - Authentication failed. Requesting new token.`, e);
        this.onAuthenticationRequired.deferDispatch();
        return;
      }

      console.warn(`FirebaseAuthService - Authentication failed. Trying again later.`, e);
      this._isRetry = true;
      this._scheduleAuthenticationTimeout(jwt, 60000);

      if (!this._isRetry) {
        const message = `FirebaseAuthService - Authentication failed. It will be retried later.`;
        this._telemetryService.logError(TelemetryEventType.FirebaseAuthFailed, message, e);
      }

      this.onAuthenticationFailed.deferDispatch();
    }
  };

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Set a timer to attempt authentication after the specified amount of time.
   *
   * @param jwt The token to use for authentication.
   * @param ms The time to wait before the attempt, in milliseconds.
   *
   * @see _cancelAuthenticationTimeout()
   */
  private readonly _scheduleAuthenticationTimeout = (jwt: Jwt, ms: number): void => {
    this._cancelAuthenticationTimeout();
    const wrapper = (): void => {
      this._onAuthenticationTimeout(jwt).catch(console.warn);
    };
    this._authenticationTimeout = setTimeout(wrapper, ms);
  };

  /**
   * Stop any scheduled authentication attempt.
   *
   * @see _scheduleAuthenticationTimeout()
   */
  private readonly _cancelAuthenticationTimeout = (): void => {
    if (this._authenticationTimeout !== undefined) {
      clearTimeout(this._authenticationTimeout);
      this._authenticationTimeout = undefined;
    }
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The telemetry service for logging events.
   */
  private readonly _telemetryService: ITelemetryService;

  /**
   * The internal Firebase authentication object.
   * It will be undefined if this service isn't running.
   *
   * @see start()
   */
  private _firebaseAuth?: FirebaseAuth;

  /**
   * A function we can call to unsubscribe from Firebase authentication change events.
   * This will be undefined if this service isn't running.
   *
   * @see _onAuthStateChanged
   */
  private _unsubscribeAuthStateChange?: Unsubscribe;

  /**
   * Contains details of the user we're currently authenticated as, if any.
   * This will be undefined if this service isn't running, or we haven't authenticated yet.
   */
  private _firebaseUser?: FirebaseUser;

  /**
   * Handler to a timer for authentication attempts/retries.
   * This will be undefined if there is currently no attempt scheduled.
   *
   * @see _onAuthenticationTimeout()
   * @see _scheduleAuthenticationTimeout()
   * @see _cancelAuthenticationTimeout()
   */
  private _authenticationTimeout?: ReturnType<typeof setTimeout>;

  /**
   * Indicates if the current authentication attempt is a retry.
   */
  private _isRetry = false;
}

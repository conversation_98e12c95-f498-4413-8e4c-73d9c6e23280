import DeviceId from 'models/DeviceId';
import DeviceRegistrationService from './DeviceRegistrationService';
import FetchService from './FetchService';
import Jwt from 'models/Jwt';
import ProductCode from 'constants/ProductCode';
import ProvisioningInfo from 'provisioning/ProvisioningInfo';
import SerialId from 'models/SerialId';
import TenantId from 'models/TenantId';
import StorageService from './StorageService';
import TemplateString from 'models/TemplateString';
import { waitForMockToBeCalled } from 'test-helpers/mock-utilities';
import 'test-helpers/chrome-api';
import MockTelemetryService from '../test-helpers/MockTelemetryService';
import ConstantBackOff from 'back-off-methods/ConstantBackOff';
import IpServiceMock from './IpService.mock';

// Default data to be used by tests.
const testJwt =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNjQwOTk1MjAwLCJleHAiOjE2NDA5OTg4MDAsIm15VGVzdENsYWltIjoiYmxhaCJ9.E4DGCetLw2D1WT7aLnxv_0ajMajWlMIkU-ERv9SHm7E';
const testDeviceId = 'b725562a-5a8c-43f1-b7b2-21668e5d967f';
const testUserDocumentPath = '/customers/UNCLFAKE12345678/users/<EMAIL>';

const testProvisioningInfo = new ProvisioningInfo(
  new SerialId('UNCLTEST7GCBRNZG'),
  new TenantId('3a4007ea-a817-11eb-9c60-3d943bce7542'),
  'test-hardware-id',
  'test-device-name',
  'test-user-agent',
  '<EMAIL>',
  'test-domain.com',
  ['TestGroup1', 'TestGroup2'],
  true,
);

const testRegistrationUrlTemplate = new TemplateString('https://{glsHash}.example.com/register');
const expectedRegistrationUrl = `https://${testProvisioningInfo.serialId.getGlsHash()}.example.com/register`;

// A mock successful response from the registration/update API.
const mockSuccessfulResponse = {
  status: 200,
  ok: true,
  json: async () =>
    await Promise.resolve({
      isSuccess: true,
      data: {
        jwt: testJwt,
        deviceId: testDeviceId,
        userDocumentPath: testUserDocumentPath,
      },
    }),
};

// Fetch is mocked globally. This just gets a type-cast reference to it to make code simpler.
const fetchMock = fetch as jest.Mock;

const telemetryService = new MockTelemetryService();
const ipService = new IpServiceMock();

// TODO: Make tests more reliable by using fake timers where possible.
describe('DeviceRegistrationService', () => {
  beforeEach(() => {
    fetchMock.mockResolvedValue(mockSuccessfulResponse);

    chrome.runtime.getURL = jest.fn(() => 'chrome-extension://xyzzy');
    chrome.runtime.getPlatformInfo = jest.fn(
      async () =>
        await Promise.resolve({
          arch: 'x86-64',
          nacl_arch: 'x86-64',
          os: 'win',
        }),
    );

    // Mock the navigator api to be able to get the user agent when running the tests.
    const navigatorApi = { userAgent: 'agent-mock' };

    Object.defineProperty(global, 'navigator', {
      value: navigatorApi,
      writable: true,
    });

    // Suppress log output from the code being tested.
    jest.spyOn(console, 'debug').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  describe('isRunning', () => {
    it('returns false if start() has not been called yet', () => {
      const storageService = new StorageService('deviceRegistration', undefined);
      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      expect(deviceRegistrationService.isRunning).toBeFalse();
    });

    it('returns true if registration is in progress', async () => {
      // Deliberately make the registration request take a while.
      fetchMock.mockImplementation(async (): Promise<any> => {
        await new Promise((resolve) => setTimeout(resolve, 5000));
        return mockSuccessfulResponse;
      });

      const storageService = new StorageService('deviceRegistration', undefined);
      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      deviceRegistrationService.onRegistered.addListener(jest.fn());

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, []);

      expect(deviceRegistrationService.isRunning).toBeTrue();
      await deviceRegistrationService.stop();
    });

    it('returns true if registration has succeeded and stop() has not been called', async () => {
      const storageService = new StorageService('deviceRegistration', undefined);
      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      const onRegisteredMock = jest.fn();
      deviceRegistrationService.onRegistered.addListener(onRegisteredMock);

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, []);

      await waitForMockToBeCalled(onRegisteredMock);
      expect(deviceRegistrationService.isRunning).toBeTrue();
      await deviceRegistrationService.stop();
    });

    it('returns true if registration has failed and stop() has not been called', async () => {
      // Simulate a server error so that the registration request never succeeds.
      fetchMock.mockResolvedValue({
        status: 500,
        ok: true,
        json: async () => await Promise.resolve({ isSuccess: false, errors: [] }),
      });

      const deviceRegistrationService = new DeviceRegistrationService(
        new StorageService('deviceRegistration', undefined),
        telemetryService,
        ipService,
        new FetchService(new ConstantBackOff(0, 0), 1), // <-- Give up after a single failure.
        new FetchService(new ConstantBackOff(0, 0), 1),
      );
      const onFailedMock = jest.fn();
      deviceRegistrationService.onFailed.addListener(onFailedMock);

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, []);

      await waitForMockToBeCalled(onFailedMock);
      expect(deviceRegistrationService.isRunning).toBeTrue();
      await deviceRegistrationService.stop();
    });

    it('returns false if stop() has been called', async () => {
      const storageService = new StorageService('deviceRegistration', undefined);
      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      const onRegisteredMock = jest.fn();
      deviceRegistrationService.onRegistered.addListener(onRegisteredMock);

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, []);

      await waitForMockToBeCalled(onRegisteredMock);
      await deviceRegistrationService.stop();
      expect(deviceRegistrationService.isRunning).toBeFalse();
    });
  });

  describe('isRegistered', () => {
    it('returns false if registration has not been started yet', () => {
      const storageService = new StorageService('deviceRegistration', undefined);
      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      expect(deviceRegistrationService.isRegistered).toBeFalse();
    });

    it('returns true if registration has succeeded', async () => {
      const storageService = new StorageService('deviceRegistration', undefined);
      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      const onRegisteredMock = jest.fn();
      deviceRegistrationService.onRegistered.addListener(onRegisteredMock);

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, []);

      await waitForMockToBeCalled(onRegisteredMock);
      expect(deviceRegistrationService.isRegistered).toBeTrue();
      await deviceRegistrationService.stop();
    });
  });

  describe('jwt', () => {
    it('returns undefined if registration has not started yet', () => {
      const storageService = new StorageService('deviceRegistration', undefined);
      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      expect(deviceRegistrationService.jwt).toBeUndefined();
    });

    it('returns the JWT provided by the server if registration has succeeded', async () => {
      const storageService = new StorageService('deviceRegistration', undefined);
      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      const onRegisteredMock = jest.fn();
      deviceRegistrationService.onRegistered.addListener(onRegisteredMock);

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, []);

      await waitForMockToBeCalled(onRegisteredMock);
      expect(deviceRegistrationService.jwt?.token).toEqual(testJwt);
      await deviceRegistrationService.stop();
    });
  });

  describe('deviceId', () => {
    it('returns undefined if registration has not started yet', () => {
      const storageService = new StorageService('deviceRegistration', undefined);
      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      expect(deviceRegistrationService.deviceId).toBeUndefined();
    });

    it('returns the device ID provided by the server if registration has succeeded', async () => {
      const storageService = new StorageService('deviceRegistration', undefined);
      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      const onRegisteredMock = jest.fn();
      deviceRegistrationService.onRegistered.addListener(onRegisteredMock);

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, []);

      await waitForMockToBeCalled(onRegisteredMock);
      expect(deviceRegistrationService.deviceId?.toString()).toEqual(testDeviceId);
      await deviceRegistrationService.stop();
    });
  });

  describe('onRegistered', () => {
    // TODO: is triggered even if cache is missing user document path
    // TODO: is triggered even if cloud does not provide user document path

    it('is triggered with cached registration details if cache was available', async () => {
      const storageService = new StorageService('deviceRegistration', undefined);

      // Simulate cached registration details.
      storageService.set('jwt', testJwt);
      storageService.set('deviceId', testDeviceId);
      storageService.set('userDocumentPath', testUserDocumentPath);
      storageService.set('products', []);

      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      const onRegisteredMock = jest.fn();
      deviceRegistrationService.onRegistered.addListener(onRegisteredMock);

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, []);

      await waitForMockToBeCalled(onRegisteredMock);
      await deviceRegistrationService.stop();
      expect(onRegisteredMock).toHaveBeenCalled();

      const jwt = onRegisteredMock.mock.calls[0][0] as Jwt;
      const deviceId = onRegisteredMock.mock.calls[0][1] as DeviceId;

      expect(jwt.token).toEqual(testJwt);
      expect(deviceId.toString()).toEqual(testDeviceId);
    });

    it('is triggered with registration details when registration succeeds', async () => {
      const storageService = new StorageService('deviceRegistration', undefined);
      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      const onRegisteredMock = jest.fn();
      deviceRegistrationService.onRegistered.addListener(onRegisteredMock);

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, []);

      await waitForMockToBeCalled(onRegisteredMock);
      await deviceRegistrationService.stop();
      expect(onRegisteredMock).toHaveBeenCalled();

      const jwt = onRegisteredMock.mock.calls[0][0] as Jwt;
      const deviceId = onRegisteredMock.mock.calls[0][1] as DeviceId;

      expect(jwt.token).toEqual(testJwt);
      expect(deviceId.toString()).toEqual(testDeviceId);
    });

    it('is triggered with updated registration details when registration is updated', async () => {
      const storageService = new StorageService('deviceRegistration', undefined);

      const oldJwt =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjMwMDAwLCJleHAiOjE1MTYyNDAwMDAsInNvbWVDdXN0b21DbGFpbSI6ImJsYWgifQ.rs1rL1bnCt957sgL5yAV3q49i0fHF0PIw2IngG-En1g';

      // Simulate cached registration details. Deliberately have a different JWT in here.
      storageService.set('jwt', oldJwt);
      storageService.set('deviceId', testDeviceId);
      storageService.set('userDocumentPath', testUserDocumentPath);
      storageService.set('products', []);

      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      const onRegisteredMock = jest.fn();
      deviceRegistrationService.onRegistered.addListener(onRegisteredMock);

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, [
        ProductCode.cldflt,
      ]);

      // The onRegistered event will be called once with the cached data, and once after an update.
      await waitForMockToBeCalled(onRegisteredMock, 2);
      await deviceRegistrationService.stop();

      const jwt = onRegisteredMock.mock.calls[1][0] as Jwt;
      const deviceId = onRegisteredMock.mock.calls[1][1] as DeviceId;

      expect(jwt.token).toEqual(testJwt); // <-- this should be the new JWT from the update request
      expect(deviceId.toString()).toEqual(testDeviceId);
    });
  });

  describe('onFailed', () => {
    it('is triggered if a registration request failed due to an error and ran out of attempts', async () => {
      // Simulate a server error so that the registration request never succeeds.
      fetchMock.mockResolvedValue({
        status: 500,
        ok: true,
        json: async () => await Promise.resolve({ isSuccess: false, errors: [] }),
      });

      const deviceRegistrationService = new DeviceRegistrationService(
        new StorageService('deviceRegistration', undefined),
        telemetryService,
        ipService,
        new FetchService(new ConstantBackOff(0, 0), 1), // <-- Give up after a single failure.
      );
      const onFailedMock = jest.fn();
      deviceRegistrationService.onFailed.addListener(onFailedMock);

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, []);

      await waitForMockToBeCalled(onFailedMock);
      await deviceRegistrationService.stop();
      expect(onFailedMock).toHaveBeenCalled();
    });
  });

  describe('onUnlicensed', () => {
    it('is triggered if a registration request failed because the customer is not licensed', async () => {
      // Simulate the customer not being licensed.
      // TODO: Update this when the unlicensed error code is properly decided.
      fetchMock.mockResolvedValue({
        status: 404,
        ok: true,
        json: async () => await Promise.resolve({ isSuccess: false, errors: [] }),
      });

      const storageService = new StorageService('deviceRegistration', undefined);
      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );

      const onUnlicensedMock = jest.fn();
      deviceRegistrationService.onUnlicensed.addListener(onUnlicensedMock);

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, []);

      await waitForMockToBeCalled(onUnlicensedMock);
      await deviceRegistrationService.stop();
      expect(onUnlicensedMock).toHaveBeenCalled();
    });
  });

  describe('start()', () => {
    it('sends a registration request if no details were cached', async () => {
      const storageService = new StorageService('deviceRegistration', undefined);
      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      const onRegisteredMock = jest.fn();
      deviceRegistrationService.onRegistered.addListener(onRegisteredMock);

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, [
        ProductCode.cldflt,
        ProductCode.mms,
      ]);

      await waitForMockToBeCalled(fetchMock);
      await deviceRegistrationService.stop();

      expect(fetchMock).toHaveBeenCalledTimes(1);
      expect(fetchMock).toHaveBeenCalledWith(expectedRegistrationUrl, expect.anything());

      // Ensure the correct information was sent to the registration end-point.
      const requestDetails = fetchMock.mock.calls[0][1] as RequestInit;
      expect(requestDetails.method).toEqual('POST');
      expect(requestDetails.headers).toContainEntry(['Content-Type', 'application/json']);
      expect(requestDetails.headers).toContainEntry([
        'X-Client-Id',
        testProvisioningInfo.serialId.toString(),
      ]);

      const body = JSON.parse(requestDetails.body as string);
      expect(body).toEqual(
        expect.objectContaining({
          customerId: testProvisioningInfo.serialId.toString(),
          tenantId: testProvisioningInfo.tenantId?.toString(),
          userId: testProvisioningInfo.user,
          hardwareId: testProvisioningInfo.hardwareId,
          products: ['cldflt', 'mms'],
        }),
      );
    });

    it('loads registration details from cache if possible', async () => {
      const storageService = new StorageService('deviceRegistration', undefined);

      // Simulate previously cached data.
      storageService.set('jwt', testJwt);
      storageService.set('deviceId', testDeviceId);
      storageService.set('userDocumentPath', testUserDocumentPath);
      storageService.set('products', []);

      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      const onRegisteredMock = jest.fn();
      deviceRegistrationService.onRegistered.addListener(onRegisteredMock);

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, []);

      await waitForMockToBeCalled(onRegisteredMock);
      await deviceRegistrationService.stop();

      expect(onRegisteredMock).toHaveBeenCalled();
      const jwt = onRegisteredMock.mock.calls[0][0] as Jwt;
      const deviceId = onRegisteredMock.mock.calls[0][1] as DeviceId;
      expect(jwt.token).toEqual(testJwt);
      expect(deviceId.toString()).toEqual(testDeviceId);
    });

    it('still sends an update request if registration details were loaded from cache', async () => {
      const storageService = new StorageService('deviceRegistration', undefined);

      // Simulate previously cached data.
      storageService.set('jwt', testJwt);
      storageService.set('deviceId', testDeviceId);
      storageService.set('userDocumentPath', testUserDocumentPath);
      storageService.set('products', ['cldflt']);

      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      const onRegisteredMock = jest.fn();
      deviceRegistrationService.onRegistered.addListener(onRegisteredMock);

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, [
        ProductCode.cldflt,
        ProductCode.mms,
      ]);

      await waitForMockToBeCalled(fetchMock);
      await deviceRegistrationService.stop();

      expect(fetchMock).toHaveBeenCalledTimes(1);
      expect(fetchMock).toHaveBeenCalledWith(expectedRegistrationUrl, expect.anything());

      // Ensure the correct information was sent to the update end-point.
      const requestDetails = fetchMock.mock.calls[0][1] as RequestInit;
      expect(requestDetails.method).toEqual('POST');
      expect(requestDetails.headers).toContainEntry(['Content-Type', 'application/json']);
      expect(requestDetails.headers).toContainEntry([
        'X-Client-Id',
        testProvisioningInfo.serialId.toString(),
      ]);

      const body = JSON.parse(requestDetails.body as string);
      expect(body).toEqual(
        expect.objectContaining({
          customerId: testProvisioningInfo.serialId.toString(),
          products: ['cldflt', 'mms'],
        }),
      );
    });

    it('clears the cache and sends a registration request if the cache was invalid', async () => {
      const storageService = new StorageService('deviceRegistration', undefined);
      // Simulate invalid registration data in the cache.
      storageService.set('jwt', 'deliberately-invalid-jwt');
      storageService.set('deviceId', 'deliberately-invalid-device-id');
      storageService.set('userDocumentPath', 'the-path-can-be-any-string');

      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      const onRegisteredMock = jest.fn();
      deviceRegistrationService.onRegistered.addListener(onRegisteredMock);

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, []);

      // We need to check the cache after the fetch request goes through, but before the response
      //  has a chance to be processed.
      await waitForMockToBeCalled(fetchMock);
      expect(storageService.has('jwt')).toBeFalse();
      expect(storageService.has('device')).toBeFalse();

      expect(fetchMock).toHaveBeenCalledTimes(1);
      expect(fetchMock).toHaveBeenCalledWith(expectedRegistrationUrl, expect.anything());

      await deviceRegistrationService.stop();
    });
  });

  describe('requestNewAuthenticationToken()', () => {
    it('sends an update request if we were already registered', async () => {
      const storageService = new StorageService('deviceRegistration', undefined);
      // Simulate cached registration data.
      storageService.set('jwt', testJwt);
      storageService.set('deviceId', testDeviceId);
      storageService.set('userDocumentPath', testUserDocumentPath);
      storageService.set('products', []);

      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      const onRegisteredMock = jest.fn();
      deviceRegistrationService.onRegistered.addListener(onRegisteredMock);

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, []);

      await waitForMockToBeCalled(onRegisteredMock);
      await deviceRegistrationService.requestNewAuthenticationToken();
      await waitForMockToBeCalled(fetchMock);
      await deviceRegistrationService.stop();

      expect(fetchMock).toHaveBeenCalledWith(expectedRegistrationUrl, expect.anything());
    });
  });

  describe('stop()', () => {
    it('stops any outstanding request', async () => {
      // Simulate a server error, forcing the registration service to retry if it manages to send a
      //  registration request.
      fetchMock.mockResolvedValue({
        status: 500,
        ok: true,
        json: async () => await Promise.resolve({ isSuccess: false, errors: [] }),
      });

      const storageService = new StorageService('deviceRegistration', undefined);
      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      const onRegisteredMock = jest.fn();
      deviceRegistrationService.onRegistered.addListener(onRegisteredMock);

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, []);

      await deviceRegistrationService.stop();

      // Ensure no more than one request was sent.
      expect(fetchMock.mock.calls.length).toBeLessThanOrEqual(1);
    });

    it('does nothing if start() had not been called', async () => {
      const storageService = new StorageService('deviceRegistration', undefined);
      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      await deviceRegistrationService.stop();
    });

    it('does nothing if stop() had already been called', async () => {
      const storageService = new StorageService('deviceRegistration', undefined);
      const deviceRegistrationService = new DeviceRegistrationService(
        storageService,
        telemetryService,
        ipService,
      );
      const onRegisteredMock = jest.fn();
      deviceRegistrationService.onRegistered.addListener(onRegisteredMock);

      await deviceRegistrationService.start(testRegistrationUrlTemplate, testProvisioningInfo, []);

      await deviceRegistrationService.stop();
      await deviceRegistrationService.stop();
    });
  });

  describe('stringifyProducts()', () => {
    it('returns an array of the string representations of the given product code enumerations', () => {
      const products = [ProductCode.mms, ProductCode.cldflt];
      expect(DeviceRegistrationService.stringifyProducts(products)).toEqual(['mms', 'cldflt']);
    });

    it('allows duplicates', () => {
      const products = [ProductCode.mms, ProductCode.mms];
      expect(DeviceRegistrationService.stringifyProducts(products)).toEqual(['mms', 'mms']);
    });

    it('returns an empty array if the input array is empty', () => {
      expect(DeviceRegistrationService.stringifyProducts([])).toEqual([]);
    });
  });

  describe('determinePlatform()', () => {
    it('returns "win" if the browser platform info indicates that the OS is Windows', async () => {
      const platformInfo: chrome.runtime.PlatformInfo = {
        arch: 'x86-64',
        nacl_arch: 'x86-64',
        os: 'win',
      };

      (chrome.runtime.getPlatformInfo as jest.Mock).mockResolvedValueOnce(platformInfo);
      await expect(DeviceRegistrationService.determinePlatform()).resolves.toEqual('Windows');
    });

    it('returns "mac" if the browser platform info indicates that the OS is macOS', async () => {
      const platformInfo: chrome.runtime.PlatformInfo = {
        arch: 'arm',
        nacl_arch: 'arm',
        os: 'mac',
      };

      (chrome.runtime.getPlatformInfo as jest.Mock).mockResolvedValueOnce(platformInfo);
      await expect(DeviceRegistrationService.determinePlatform()).resolves.toEqual('macOS');
    });

    it('returns "linux" if the browser platform info indicates that the OS is Linux', async () => {
      const platformInfo: chrome.runtime.PlatformInfo = {
        arch: 'x86-64',
        nacl_arch: 'x86-64',
        os: 'linux',
      };

      (chrome.runtime.getPlatformInfo as jest.Mock).mockResolvedValueOnce(platformInfo);
      await expect(DeviceRegistrationService.determinePlatform()).resolves.toEqual('Linux');
    });

    it('returns "cros" if the browser platform info indicates that the OS is macOS', async () => {
      const platformInfo: chrome.runtime.PlatformInfo = {
        arch: 'arm',
        nacl_arch: 'arm',
        os: 'cros',
      };

      (chrome.runtime.getPlatformInfo as jest.Mock).mockResolvedValueOnce(platformInfo);
      await expect(DeviceRegistrationService.determinePlatform()).resolves.toEqual('ChromeOS');
    });

    it('returns an empty string if the browser platform info could not be retrieved', async () => {
      (chrome.runtime.getPlatformInfo as jest.Mock).mockRejectedValueOnce(new Error('test error'));
      await expect(DeviceRegistrationService.determinePlatform()).resolves.toEqual('');
    });
  });
});

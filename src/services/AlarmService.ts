import StandaloneEvent from 'utilities/StandaloneEvent';

/**
 * Manages use of chrome alarms API while taking into account the service worker lifecycle.
 */
export default class AlarmService {
  constructor() {
    chrome.alarms.onAlarm.addListener(this._onAlarm);
  }

  /**
   * Manually dispatches an alarm. If a listener is not set, the dispatch is delayed until one is.
   *
   * @param name The name for the chrome alarm.
   */
  public readonly dispatch = (name: string): void => {
    this._onAlarm({
      scheduledTime: Date.now(),
      name,
    });
  };

  /**
   * Finds alarm name in the alarm map and dispatches it.
   *
   * @param alarm The alarm passed by chrome.alarms.
   */
  private readonly _onAlarm = (alarm: chrome.alarms.Alarm): void => {
    const event = this._alarms.get(alarm.name);
    if (event === undefined || event.length === 0) this._pending.set(alarm.name, alarm);
    else {
      event.dispatch(alarm);
      this._pending.delete(alarm.name);
    }
  };

  /**
   * Associate an alarm name to a listener event and fires the event if alarm is pending.
   *
   * @param name The name for the chrome alarm.
   * @param onAlarm The function to be called when the alarm is triggered.
   */
  public readonly addListener = (
    name: string,
    onAlarm: (alarm: chrome.alarms.Alarm) => void | Promise<void>,
  ): void => {
    let event = this._alarms.get(name);
    if (event !== undefined) event.addListener(onAlarm);
    else {
      event = new StandaloneEvent<[chrome.alarms.Alarm]>();
      event.addListener(onAlarm);
      this._alarms.set(name, event);
    }

    const pendingAlarm = this._pending.get(name);
    if (pendingAlarm !== undefined) this._onAlarm(pendingAlarm);
  };

  /**
   * Create the alarm, if the alarm has changed or does not already exist.
   *
   * @param name The name for the chrome alarm.
   * @param info Information used in chrome.alarms.create().
   * @param ignorePeriod Boolean indicating whether to consider alarm period in recreation.
   */
  public readonly create = async (
    name: string,
    info: chrome.alarms.AlarmCreateInfo,
    ignorePeriod: boolean = false,
  ): Promise<void> => {
    const alarm = await chrome.alarms.get(name);
    if (alarm === undefined || (!ignorePeriod && info.periodInMinutes !== alarm.periodInMinutes)) {
      await this.remove(name);
      await chrome.alarms.create(name, info);
    }
  };

  /**
   * Remove the alarm, if the alarm exists.
   *
   * @param name The name for the chrome alarm.
   */
  public readonly remove = async (name: string): Promise<void> => {
    if ((await chrome.alarms.get(name)) === undefined) return;

    const wasCleared = await chrome.alarms.clear(name);
    if (!wasCleared) console.warn(`Failed to remove alarm: ${name}`);
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Map of alarm names to StandaloneEvent objects.
   */
  private readonly _alarms = new Map<string, StandaloneEvent<[chrome.alarms.Alarm]>>();

  /**
   * Map of alarm names to alarms that fired before a listener was assigned.
   */
  private readonly _pending = new Map<string, chrome.alarms.Alarm>();
}

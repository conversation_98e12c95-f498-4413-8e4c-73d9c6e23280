import {
  DMSContentAwareRootConfig,
  Cldflt,
  Cldrpt,
  Core,
  IProductConfig,
} from 'models/IProductConfig';
import {
  DocumentData,
  Firestore,
  QuerySnapshot,
  collection,
  documentId,
  onSnapshot,
  query,
  where,
} from 'firebase/firestore';

import { CloudReportingVersion } from 'constants/CloudReportingVersion';
import ITelemetryService from './ITelemetryService';
import LicenseResult from 'models/LicenseResult';
import PRODUCT_DOCUMENT_TYPES from 'constants/ProductDocumentTypes';
import SerialId from '../models/SerialId';
import StandaloneEvent from 'utilities/StandaloneEvent';
import StorageService from 'services/StorageService';
import { Unsubscribe } from 'firebase/auth';
import { firestoreTimestampToNumericTimestamp } from 'utilities/Helpers';
import { TelemetryEventType } from '../constants/TelemetryEventType';

/**
 * Manages downloading and caching the product config from firestore.
 * This is done through the use of firestore listeners that will wait for changes to the product config in firestore.
 * Once a change is detected it will be downloaded and stored locally. Then the `onProductConfigChanged` event will be emitted.
 * @see https://firebase.google.com/docs/firestore/query-data/listen
 */
export default class ProductConfigService {
  /**
   * Construct a new instance of this class.
   * This doesn't start downloading anything nor does it dispatch any events.
   *
   * @param storageService A wrapper for the storage area used to cache product configuration.
   * @param serial The customer's serial ID, as specified in provisioning info. This is needed so
   *  that we know which product config documents need to be downloaded.
   * @param telemetryService Use to send logs to the cloud.
   */
  constructor(
    storageService: StorageService,
    serial: SerialId,
    telemetryService: ITelemetryService,
  ) {
    this._storageService = storageService;
    this._serial = serial;
    this._telemetryService = telemetryService;

    this._productConfigParsed.addListener(this._onProductConfigParsed);
  }

  /**
   * Starts the firestore listener to monitor and download changes from the firestore product config.
   *
   * If the listener downloads any changes, once the config has been updated the `onProductConfigChanged` event will be emitted.
   * @see https://firebase.google.com/docs/firestore/query-data/listen
   * @param firestore The current active firestore instance.
   */
  public readonly start = (firestore: Firestore): void => {
    try {
      // Check if a listener is already set up.
      if (this._firestoreUnsubscribe !== undefined) {
        return;
      }

      // Set up the firestore listener for any document changes.
      const licenseCollection = collection(
        firestore,
        `customers/${this._serial.toString()}/licenses`,
      );
      const q = query(
        licenseCollection,
        where(documentId(), 'in', [
          PRODUCT_DOCUMENT_TYPES.CLOUD_FILTER,
          PRODUCT_DOCUMENT_TYPES.CLOUD_REPORTING,
          PRODUCT_DOCUMENT_TYPES.CORE,
          PRODUCT_DOCUMENT_TYPES.CONTENT_AWARE,
        ]),
      );

      this._firestoreUnsubscribe = onSnapshot(
        q,
        (querySnapshot) => {
          // Debounce the changes by waiting a little before parsing.
          clearTimeout(this._configTimeout);

          this._configTimeout = setTimeout(() => {
            console.debug(
              `ProductConfigService - Got firestore snapshot from the ${
                querySnapshot.metadata.fromCache ? 'cache' : 'server'
              }.`,
            );

            this._parseDownloadedConfig(querySnapshot);
          }, 1500);
        },
        (error) => {
          setTimeout(() => {
            this.onFirestoreResubscribe.dispatch();
          }, Math.max(this._scheduledResubscribe - Date.now(), 0));

          this._firestoreUnsubscribe = undefined;
          console.warn('Firestore listener has failed', error);
          this._telemetryService.logError(
            TelemetryEventType.FirebaseConfigFailed,
            error.message,
            error,
          );
        },
      );

      // Set resubscription attempt for thirty minutes from now plus five minutes of jitter.
      this._scheduledResubscribe = Date.now() + (30 + 5 * Math.random()) * 60 * 1000;
    } catch (error: any) {
      this._telemetryService.logError(
        TelemetryEventType.FirebaseConfigFailed,
        error.message,
        error,
      );
    }
  };

  /**
   * Checks the licenses in the product config and returns an object with the results.
   *
   * @note If the product config has not yet been downloaded the object will be returned with an invalid cldflt license.
   *
   * @returns A LicenseResult that contains the licensing information for cldflt and cldrpt.
   */
  public readonly getLicensingInfo = (): LicenseResult => {
    const result = new LicenseResult();
    if (this.productConfig === undefined) {
      return result;
    }

    const now = Date.now();

    const cldflt = this.productConfig.cldflt;
    result.cldfltLicenseValid =
      cldflt?.licence?.licenseExpiryDate !== undefined && cldflt.licence.licenseExpiryDate > now;

    const cldrpt = this.productConfig.cldrpt;

    if (cldrpt === undefined) {
      if (cldflt?.configuration.accesslogs !== undefined) {
        result.cldrptVersion = CloudReportingVersion.v2;
        result.cldrptConfig = { accessLogsBlobConfig: cldflt.configuration.accesslogs };
        return result;
      }

      result.cldrptVersion = CloudReportingVersion.disabled;
      return result;
    }

    if (cldrpt.licence.licenseExpiryDate < now) {
      result.cldrptVersion = CloudReportingVersion.disabled;
      return result;
    }

    if (cldrpt.configuration.accessLogsIngestV4 !== undefined) {
      result.cldrptVersion = CloudReportingVersion.v4;
      result.cldrptConfig = { accessLogsIngestV4Config: cldrpt.configuration.accessLogsIngestV4 };
      return result;
    }

    if (cldrpt.configuration.accesslogs !== undefined) {
      result.cldrptVersion = CloudReportingVersion.v3;
      result.cldrptConfig = { accessLogsBlobConfig: cldrpt.configuration.accesslogs };
    }

    return result;
  };

  /**
   * Parses the firestore query snapshot into an IProductConfig object.
   *
   * Once parsed the function will emit the `_productConfigParsed` event.
   * @param querySnapshot The snapshot of the document collection to parse.
   */
  private readonly _parseDownloadedConfig = (querySnapshot: QuerySnapshot<DocumentData>): void => {
    if (querySnapshot.empty) {
      // TODO: Handle retrys?
      console.error(
        `The product config in firestore could not be found for the serial ${this._serial
          .toString()
          .toUpperCase()}`,
      );
      return;
    }

    // Parse all of the documents into the IProductConfig interface.
    const config: IProductConfig = {};

    querySnapshot.forEach((doc) => {
      switch (doc.id.toLowerCase()) {
        case PRODUCT_DOCUMENT_TYPES.CLOUD_FILTER:
          config.cldflt = ProductConfigService.fixLicenceDates(doc.data()) as Cldflt;
          break;
        case PRODUCT_DOCUMENT_TYPES.CLOUD_REPORTING:
          config.cldrpt = ProductConfigService.fixLicenceDates(doc.data()) as Cldrpt;
          if (config.cldrpt?.configuration?.accessLogsIngestV4?.url !== undefined) {
            config.cldrpt.configuration.accessLogsIngestV4.url =
              config.cldrpt.configuration.accessLogsIngestV4.url.replace(
                '{uncl}',
                this._serial.get(),
              );
          }
          break;
        case PRODUCT_DOCUMENT_TYPES.CORE:
          // The core config isn't technically licensed, but it may contain a licence object.
          // Try to fix the dates just in case.
          config.core = ProductConfigService.fixLicenceDates(doc.data()) as Core;
          break;
        case PRODUCT_DOCUMENT_TYPES.CONTENT_AWARE:
          // Content Aware configuration
          config.ca = ProductConfigService.fixLicenceDates(doc.data()) as DMSContentAwareRootConfig;
          console.debug('ProductConfigService - Received Content Aware configuration');
          break;
      }
    });

    if (!querySnapshot.metadata.fromCache) {
      this._validateProductConfig(config);
    }

    this._productConfigParsed.dispatch(config);
  };

  /**
   * Performs validation checks on the parsed product config. If any are found it will log the info to telemetry.
   * @param config The product config to be validated.
   */
  private readonly _validateProductConfig = (config: IProductConfig): void => {
    const errors: Array<{ documentId: string; error: string }> = [];
    if (
      config.cldrpt !== undefined &&
      config.cldrpt.configuration?.accesslogs === undefined &&
      config.cldrpt.configuration?.accessLogsIngestV4 === undefined
    ) {
      errors.push({
        documentId: PRODUCT_DOCUMENT_TYPES.CLOUD_REPORTING,
        error: 'Cloud reporting configuration is empty.',
      });
    }

    if (config.ca !== undefined && config.ca.configuration === undefined) {
      errors.push({
        documentId: PRODUCT_DOCUMENT_TYPES.CONTENT_AWARE,
        error: 'Content Aware configuration is empty.',
      });
    }

    if (errors.length > 0 && !this._loggedErrors) {
      this._telemetryService.logError(
        TelemetryEventType.InvalidProductConfig,
        'The product config downloaded from firestore is invalid.',
        errors,
      );
      this._loggedErrors = true;
    }
  };

  /**
   * Called when a new product config has been parsed and downloaded from firestore.
   *
   * Saves the given product config into storage and emits the `onProductConfigChanged` event.
   * @param config The product config that has been downloaded.
   */
  private readonly _onProductConfigParsed = (config: IProductConfig | undefined): void => {
    // If we failed to download the config don't save anything to the cache.
    if (config === undefined) {
      return;
    }

    this._storageService.set('productConfig', config);
    this._storageService.saveInTheBackground();

    this.onProductConfigChanged.dispatch();
  };

  /**
   * Dispatched if the product config downloaded from firestore differs from what is in local storage.
   */
  public readonly onProductConfigChanged: StandaloneEvent = new StandaloneEvent();

  /**
   * Dispatched if the firestore listener fails, causing the customer data controller to resubscribe.
   */
  public readonly onFirestoreResubscribe = new StandaloneEvent();

  /**
   * Gets if the product config has been loaded into memory.
   */
  public get isLoaded(): boolean {
    return this.productConfig !== undefined;
  }

  /**
   * Gets the product config from local storage.
   *
   * If the config has not yet been downloaded then it will return undefined.
   */
  public get productConfig(): IProductConfig | undefined {
    return this._storageService.get('productConfig') as IProductConfig;
  }

  /**
   * A function that will unsubscribe from the firestore listeners.
   *
   * If the listeners have not yet been created this will be undefined.
   */
  public get firestoreUnsubscribe(): Unsubscribe | undefined {
    return this._firestoreUnsubscribe;
  }

  /**
   * Get the date/time the stored product config was last modified in the cloud, if known.
   * This is *not* the date/time the product config was actually downloaded.
   *
   * @returns If the product config has been downloaded, then this returns the date/time it was last
   *  modified, according to the cloud. It is returned as a millisecond timestamp. If the product
   *  config has not been downloaded (or the cache hasn't been loaded yet) then this returns
   *  undefined.
   */
  public get lastUpdatedDate(): number | undefined {
    return this._storageService.getNumber('lastUpdatedDate');
  }

  /**
   * Update the date/time that the stored product config was last modified in the cloud.
   *
   * @param value A millisecond timestamp specifying when the data was last modified in the cloud.
   */
  private set lastUpdatedDate(value: number | undefined) {
    this._storageService.set('lastUpdatedDate', value);
  }

  /**
   * Checks if the Content Aware license is valid based on the start and end dates.
   *
   * Behaviour:
   * - If startTimestampSeconds is undefined, and the end timestamp is in the future, the license is valid.
   * - If endTimestampSeconds is undefined, and the start timestamp is in the past, the license is valid.
   * - If both timestamps are defined, the license is valid if the current time is between them.
   * - If either timestamp is NaN, the license is considered invalid.
   * - If both timestamps are undefined, the license is considered valid.
   *
   * @param startTimestampSeconds The start date of the license in SECONDS since epoch UTC.
   * @param endTimestampSeconds The end date of the license in SECONDS since epoch UTC.
   * @returns True if the license is valid, false if it has expired or is invalid.
   */
  private readonly _isContentAwareLicenseValid = (
    startTimestampSeconds: number | undefined,
    endTimestampSeconds: number | undefined,
  ): boolean => {
    const nowSeconds = Math.floor(Date.now() / 1000); // Current time in seconds since epoch UTC

    if (startTimestampSeconds !== undefined && isNaN(startTimestampSeconds)) {
      console.warn(
        'ProductConfigService - Content aware license start was NaN.',
        startTimestampSeconds,
      );
      return false;
    }

    if (endTimestampSeconds !== undefined && isNaN(endTimestampSeconds)) {
      console.warn(
        'ProductConfigService - Content aware license end was NaN.',
        endTimestampSeconds,
      );
      return false;
    }

    if (startTimestampSeconds === undefined && endTimestampSeconds !== undefined) {
      return endTimestampSeconds > nowSeconds;
    }

    if (endTimestampSeconds === undefined && startTimestampSeconds !== undefined) {
      return startTimestampSeconds <= nowSeconds;
    }

    if (startTimestampSeconds !== undefined && endTimestampSeconds !== undefined) {
      return startTimestampSeconds <= nowSeconds && nowSeconds <= endTimestampSeconds;
    }

    // If both timestamps are undefined, the license is considered valid.
    return true;
  };

  /**
   * Gets the Content Aware configuration from the product config.
   *
   * @returns The Content Aware configuration if it exists and is valid (and not expired), otherwise undefined.
   */
  public getContentAwareConfig = (): DMSContentAwareRootConfig | undefined => {
    const contentAwareConfig = this.productConfig?.ca;
    if (contentAwareConfig === undefined || contentAwareConfig.configuration === undefined) {
      return undefined;
    }

    const startDateTimestampSeconds =
      contentAwareConfig.configuration.ia_license?.start_date !== undefined
        ? parseInt(contentAwareConfig.configuration.ia_license.start_date, 10)
        : undefined;

    const endDateTimestampSeconds =
      contentAwareConfig.configuration.ia_license?.end_date !== undefined
        ? parseInt(contentAwareConfig.configuration.ia_license.end_date, 10)
        : undefined;

    return this._isContentAwareLicenseValid(startDateTimestampSeconds, endDateTimestampSeconds)
      ? contentAwareConfig
      : undefined;
  };

  /**
   * In a document, convert the licence dates from Firestore timestamps to numeric timestamps.
   * The document is modified in place, and the resulting timestamps are in milliseconds.
   * This will only look for specific licence-oriented dates. It will do nothing if the dates cannot
   *  be found or are not in the expected format.
   *
   * @param documentData The document to process. This is modified in-place.
   * @return A reference to the document which was passed in. This is returned for convenience.
   */
  public static readonly fixLicenceDates = (documentData: DocumentData): DocumentData => {
    // The document is expected to contain a "licence" object.
    const licence = documentData.licence;
    if (typeof licence === 'object' && licence != null) {
      // Within the licence object, we expect to find start and expiry dates.
      licence.licenseStartDate = firestoreTimestampToNumericTimestamp(licence.licenseStartDate);
      licence.licenseExpiryDate = firestoreTimestampToNumericTimestamp(licence.licenseExpiryDate);
    }

    return documentData;
  };

  /**
   * A function that will unsubscribe from the firestire listeners.
   *
   * If the listeners have not yet been created this will be undefined.
   */
  private _firestoreUnsubscribe?: Unsubscribe;

  /**
   * An event that is triggered when the product config has been downloaded and parsed.
   */
  private readonly _productConfigParsed = new StandaloneEvent<[IProductConfig | undefined]>();

  /**
   * The current timeout for debouncing the product config.
   */
  private _configTimeout?: NodeJS.Timeout;

  /**
   * The storage service that contains all info about the product config.
   */
  private readonly _storageService: StorageService;

  /**
   * The current serial id.
   */
  private readonly _serial: SerialId;

  /**
   * The telemetry service for logging.
   */
  private readonly _telemetryService: ITelemetryService;

  /**
   * Timestamp of when the Firestore listener should attempt to resubscribe if it fails.
   */
  private _scheduledResubscribe = 0;

  /**
   * Indicates if any validation errors have been logged since the extension has started.
   */
  private _loggedErrors: boolean = false;
}

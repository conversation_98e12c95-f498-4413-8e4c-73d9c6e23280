import 'test-helpers/chrome-api';
import SecretKnockService from 'services/SecretKnockService';
import StorageService from 'services/StorageService';
import MockTelemetryService from '../test-helpers/MockTelemetryService';
import IpServiceMock from 'services/IpService.mock';
import SerialId from 'models/SerialId';
import { ICloudFilterBypass } from 'models/PolicyConfigModels';

const serialId = new SerialId('UNCLTEST614NESL8');
const secretKnockConfig: ICloudFilterBypass = {
  timeout: '600', // <-- seconds
  host: 'http://127.0.0.1:6531/knock',
};

describe('SecretKnockService', () => {
  const fetchMock = jest.fn();
  let storageService: StorageService;
  let ipServiceMock: IpServiceMock;
  let mockTelemetryService: MockTelemetryService;
  let secretKnockService: SecretKnockService;

  beforeAll(() => {
    jest.useFakeTimers();

    // Suppress debug and warning messages in the console.
    jest.spyOn(console, 'debug').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});

    // Mock the fetch API.
    global.fetch = fetchMock;
  });

  beforeEach(() => {
    // By default, mock a successful response from the fetch API.
    fetchMock.mockResolvedValue({ ok: true });

    storageService = new StorageService('secretKnock', undefined);
    ipServiceMock = new IpServiceMock();
    mockTelemetryService = new MockTelemetryService();

    secretKnockService = new SecretKnockService(
      storageService,
      ipServiceMock,
      mockTelemetryService,
    );
  });

  afterEach(() => {
    // Ensure any running timers from this test are stopped. This will prevent any activity from
    //  happening after the test has finished.
    secretKnockService.disable();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe('isEnabled()', () => {
    it('returns false if secret knock has not been enabled', () => {
      expect(secretKnockService.isEnabled()).toBeFalse();
    });

    it('returns true if secret knock has been enabled', () => {
      secretKnockService.enable();
      expect(secretKnockService.isEnabled()).toBeTrue();
    });

    it('returns false if secret knock has been disabled', () => {
      secretKnockService.enable();
      secretKnockService.disable();
      expect(secretKnockService.isEnabled()).toBeFalse();
    });
  });

  describe('isConfigured()', () => {
    it('returns false if secret knock has not been configured', () => {
      expect(secretKnockService.isConfigured()).toBeFalse();
    });

    it('returns true if secret knock has been configured successfully', () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      expect(secretKnockService.isConfigured()).toBeTrue();
    });

    it('returns false if secret knock configuration has been cleared', () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.clearConfiguration();
      expect(secretKnockService.isConfigured()).toBeFalse();
    });
  });

  describe('shouldSendSecretKnock()', () => {
    it('returns false if secret knock is disabled', () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      expect(secretKnockService.shouldSendSecretKnock()).toBeFalse();
    });

    it('returns false if secret knock is not configured', () => {
      secretKnockService.enable();
      expect(secretKnockService.shouldSendSecretKnock()).toBeFalse();
    });

    it('returns true if secret knock is enabled and configured', () => {
      secretKnockService.enable();
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      expect(secretKnockService.shouldSendSecretKnock()).toBeTrue();
    });
  });

  describe('serialId', () => {
    it('returns undefined if secret knock is not configured', () => {
      expect(secretKnockService.serialId).toBeUndefined();
    });

    it('returns the serial specified in the secret knock configuration', () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      expect(secretKnockService.serialId).toBe(serialId);
    });

    it('returns undefined if secret knock configuration has been cleared', () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.clearConfiguration();
      expect(secretKnockService.serialId).toBeUndefined();
    });
  });

  describe('url', () => {
    it('returns undefined if secret knock is not configured', () => {
      expect(secretKnockService.url).toBeUndefined();
    });

    it('returns the URL specified in the secret knock configuration', () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      expect(secretKnockService.url?.toString()).toEqual('http://127.0.0.1:6531/knock');
    });

    it('returns undefined if secret knock configuration has been cleared', () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.clearConfiguration();
      expect(secretKnockService.url).toBeUndefined();
    });
  });

  describe('delay', () => {
    it('returns undefined if secret knock is not configured', () => {
      expect(secretKnockService.delay).toBeUndefined();
    });

    it('returns the number of milliseconds between secret knocks based on the loaded configuration', () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      // The actual delay is deliberately different from the configured value.
      // See "extractDelayFromPolicyConfig()".
      expect(secretKnockService.delay).toEqual(540000);
    });

    it('returns undefined if secret knock configuration has been cleared', () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.clearConfiguration();
      expect(secretKnockService.delay).toBeUndefined();
    });
  });

  describe('lastKnockedAt', () => {
    it('returns undefined if secret knock has not been sent yet', () => {
      expect(secretKnockService.lastKnockedAt).toBeUndefined();
    });

    it('returns the millisecond timestamp of the last secret knock according to the storage service', () => {
      storageService.set('lastKnockedAt', 1698416965000);
      expect(secretKnockService.lastKnockedAt).toEqual(1698416965000);
    });
  });

  describe('lastKnockSucceeded', () => {
    it('returns undefined if secret knock has not been sent yet', () => {
      expect(secretKnockService.lastKnockSucceeded).toBeUndefined();
    });

    it('returns true if the last secret knock succeeded according to the storage service', () => {
      storageService.set('lastKnockSucceeded', true);
      expect(secretKnockService.lastKnockSucceeded).toBeTrue();
    });

    it('returns false if the last secret knock failed according to the storage service', () => {
      storageService.set('lastKnockSucceeded', false);
      expect(secretKnockService.lastKnockSucceeded).toBeFalse();
    });
  });

  describe('getLastKnockSummary()', () => {
    it('returns "Not yet knocked" if secret knock has not been sent yet', () => {
      expect(secretKnockService.getLastKnockSummary()).toEqual('Not yet knocked');
    });

    it('returns the date and time of the last knock followed by " Success" if the last knock succeeded according to storage', () => {
      storageService.set('lastKnockedAt', 1698416965000);
      storageService.set('lastKnockSucceeded', true);
      const summary = secretKnockService.getLastKnockSummary();
      expect(summary).toEndWith(' Success');

      const timestamp = Date.parse(summary.replace(' Success', ''));
      expect(timestamp).toEqual(1698416965000);
    });

    it('returns the date and time of the last knock followed by " Failure" if the last knock failed according to storage', () => {
      storageService.set('lastKnockedAt', 1698416965000);
      storageService.set('lastKnockSucceeded', false);
      const summary = secretKnockService.getLastKnockSummary();
      expect(summary).toEndWith(' Failure');

      const timestamp = Date.parse(summary.replace(' Failure', ''));
      expect(timestamp).toEqual(1698416965000);
    });
  });

  describe('enable()', () => {
    it('does nothing if secret knock is already enabled', () => {
      secretKnockService.enable();
      expect(() => {
        secretKnockService.enable();
      }).not.toThrow();
    });

    it('does not start sending requests if secret knock is not configured', async () => {
      secretKnockService.enable();
      await jest.runOnlyPendingTimersAsync();
      expect(fetchMock).not.toHaveBeenCalled();
    });

    it('starts sending requests if secret knock has previously been configured', async () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.enable();
      await jest.runOnlyPendingTimersAsync();
      expect(fetchMock).toHaveBeenCalled();
    });
  });

  describe('disable()', () => {
    it('does nothing if secret knock is already disabled', () => {
      expect(() => {
        secretKnockService.disable();
      }).not.toThrow();
    });

    it('prevents any more requests from being sent', async () => {
      // Ensure at least one request goes through first.
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.enable();
      await jest.runOnlyPendingTimersAsync();
      expect(fetchMock).toHaveBeenCalled();
      fetchMock.mockClear();

      // Now ensure that calling "disable()" stops any more from going through.
      secretKnockService.disable();
      await jest.runOnlyPendingTimersAsync();
      expect(fetchMock).not.toHaveBeenCalled();
    });
  });

  describe('configureFromPolicyConfig()', () => {
    it('replaces any values previously loaded from a different configuration', () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);

      const newSerial = new SerialId('UNCLTEST7GCBRNZG');
      const newConfig: ICloudFilterBypass = {
        timeout: '300', // <-- seconds
        host: 'https://********:1234/knock',
      };
      secretKnockService.configureFromPolicyConfig(newSerial, newConfig);
      expect(secretKnockService.serialId).toBe(newSerial);
      expect(secretKnockService.delay).toEqual(240000);
      expect(secretKnockService.url?.toString()).toEqual('https://********:1234/knock');
    });

    it('does not start sending requests if secret knock is not enabled', async () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      await jest.runOnlyPendingTimersAsync();
      expect(fetchMock).not.toHaveBeenCalled();
    });

    it('starts sending requests if secret knock has previously been enabled', async () => {
      secretKnockService.enable();
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      await jest.runOnlyPendingTimersAsync();
      expect(fetchMock).toHaveBeenCalled();
    });
  });

  describe('clearConfiguration()', () => {
    it('does nothing if secret knock is not configured', () => {
      expect(() => {
        secretKnockService.clearConfiguration();
      }).not.toThrow();
    });

    it('prevents any more requests from being sent', async () => {
      // Ensure at least one request goes through first.
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.enable();
      await jest.runOnlyPendingTimersAsync();
      expect(fetchMock).toHaveBeenCalled();
      fetchMock.mockClear();

      // Now ensure that calling "clearConfiguration()" stops any more from going through.
      secretKnockService.clearConfiguration();
      await jest.runOnlyPendingTimersAsync();
      expect(fetchMock).not.toHaveBeenCalled();
    });
  });

  describe('_sendRequest()', () => {
    // Most of these tests are indirect because _sendRequest() is private.

    it('rejects if secret knock has not been configured', async () => {
      await expect((secretKnockService as any)._sendRequest()).toReject();
    });

    it('sends a POST request to the configured URL', async () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.enable();
      await jest.runOnlyPendingTimersAsync();
      expect(fetchMock.mock.calls[0][0].toString()).toEqual(secretKnockConfig.host);
      expect(fetchMock.mock.calls[0][1].method).toEqualCaseInsensitive('POST');
    });

    it('sends the a JSON body containing the serial and current timestamp', async () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.enable();
      await jest.runOnlyPendingTimersAsync();

      const body = JSON.parse(fetchMock.mock.calls[0][1].body);
      expect(body.serial).toEqual(serialId.toString());

      // Ensure the timestamp matches the current time approximately.
      const timestampDifference = Math.abs(Date.now() / 1000 - parseInt(body.timestamp));
      expect(timestampDifference).toBeLessThan(5);
    });

    it('stores the time when the request was sent', async () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.enable();
      await jest.runOnlyPendingTimersAsync();

      const now = Date.now();
      expect(secretKnockService.lastKnockedAt).toBeGreaterThan(now - 1000);
      expect(secretKnockService.lastKnockedAt).toBeLessThan(now + 1000);
    });

    it('stores the result of the request if it succeeded', async () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.enable();
      await jest.runOnlyPendingTimersAsync();
      expect(secretKnockService.lastKnockSucceeded).toBeTrue();
    });

    it('stores the result of the request if it failed with a status code', async () => {
      // Simulate a failed response.
      fetchMock.mockResolvedValue({ ok: false, status: 400 });
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.enable();
      await jest.runOnlyPendingTimersAsync();
      expect(secretKnockService.lastKnockSucceeded).toBeFalse();
    });

    it('stores the result of the request if it failed to send at all', async () => {
      // Simulate a failure to fetch.
      fetchMock.mockRejectedValue(new Error('Failed to fetch'));
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.enable();
      await jest.runOnlyPendingTimersAsync();
      expect(secretKnockService.lastKnockSucceeded).toBeFalse();
    });

    it('sends a telemetry metric if the request succeeded', async () => {
      const logMetricSpy = jest.spyOn(mockTelemetryService, 'logMetric');
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.enable();
      await jest.runOnlyPendingTimersAsync();
      const expected = { name: 'secret-knock-http-success', parentName: 'secret-knock-http' };
      expect(logMetricSpy).toHaveBeenCalledWith(expect.objectContaining(expected));
    });

    it('sends a telemetry metric if the request failed', async () => {
      const logMetricSpy = jest.spyOn(mockTelemetryService, 'logMetric');
      // Simulate a failure to fetch.
      fetchMock.mockRejectedValue(new Error('Failed to fetch'));
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.enable();
      await jest.runOnlyPendingTimersAsync();
      const expected = { name: 'secret-knock-http-failure', parentName: 'secret-knock-http' };
      expect(logMetricSpy).toHaveBeenCalledWith(expect.objectContaining(expected));
    });
  });

  describe('_onKnockTimeout()', () => {
    it('sends a request if secret knock is enabled and configured', () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.enable();
      (secretKnockService as any)._onKnockTimeout();
      expect(fetchMock).toHaveBeenCalled();
    });

    it('it schedules the next knock using the configured delay', async () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.enable();

      // Wait for the first knock to be sent.
      await jest.runOnlyPendingTimersAsync();
      fetchMock.mockClear();

      // Ensure the next secret knock isn't sent until the configured delay elapses.
      await jest.advanceTimersByTimeAsync((secretKnockService.delay as number) - 1000);
      expect(fetchMock).not.toHaveBeenCalled();
      await jest.advanceTimersByTimeAsync(2000);
      expect(fetchMock).toHaveBeenCalled();
    });

    it('does not send or schedule a request if secret knock is disabled', () => {
      const setTimeoutSpy = jest.spyOn(global, 'setTimeout');
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      (secretKnockService as any)._onKnockTimeout();
      expect(fetchMock).not.toHaveBeenCalled();
      expect(setTimeoutSpy).not.toHaveBeenCalled();
    });

    it('does not send or schedule a request if secret knock is not configured', () => {
      const setTimeoutSpy = jest.spyOn(global, 'setTimeout');
      secretKnockService.enable();
      (secretKnockService as any)._onKnockTimeout();
      expect(fetchMock).not.toHaveBeenCalled();
      expect(setTimeoutSpy).not.toHaveBeenCalled();
    });

    it('sends a request if the device is on premises or the outside premises status is unknown', () => {
      const isDeviceOutsidePremisesSpy = jest.spyOn(ipServiceMock, 'isDeviceOutsidePremises');
      isDeviceOutsidePremisesSpy.mockReturnValue(false);

      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.enable();
      (secretKnockService as any)._onKnockTimeout();
      expect(fetchMock).toHaveBeenCalled();
    });

    it('does not send a request if the device is outside premises', () => {
      const isDeviceOutsidePremisesSpy = jest.spyOn(ipServiceMock, 'isDeviceOutsidePremises');
      isDeviceOutsidePremisesSpy.mockReturnValue(true);

      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.enable();
      (secretKnockService as any)._onKnockTimeout();
      expect(fetchMock).not.toHaveBeenCalled();
    });
  });

  describe('_onNetworkChange()', () => {
    it('does nothing if secret knock is not enabled', async () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      (secretKnockService as any)._onNetworkChange();
      await jest.runOnlyPendingTimersAsync();
      expect(fetchMock).not.toHaveBeenCalled();
    });

    it('does nothing if secret knock is not secret knock', async () => {
      secretKnockService.enable();
      (secretKnockService as any)._onNetworkChange();
      await jest.runOnlyPendingTimersAsync();
      expect(fetchMock).not.toHaveBeenCalled();
    });

    it('triggers a request in 15 seconds if secret knock is enabled and configured', async () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.enable();

      // Ensure an initial secret knock has been sent.
      await jest.runOnlyPendingTimersAsync();
      // Advance time so we are past the last knock
      await jest.advanceTimersByTimeAsync(1000);
      fetchMock.mockClear();

      // Simulate a network change.
      const spy = jest.spyOn(ipServiceMock, 'privateIpAddressesChangedAt', 'get');
      spy.mockReturnValue(Date.now());
      (secretKnockService as any)._onNetworkChange();
      await jest.advanceTimersByTimeAsync(15001);
      expect(fetchMock).toHaveBeenCalled();
    });

    it('debounces repeated network changes', async () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      secretKnockService.enable();

      // Ensure an initial secret knock has been sent.
      await jest.runOnlyPendingTimersAsync();
      // Advance time so we are past the last knock
      await jest.advanceTimersByTimeAsync(1000);
      fetchMock.mockClear();

      // Simulate multiple network changes in a row.
      const spy = jest.spyOn(ipServiceMock, 'privateIpAddressesChangedAt', 'get');
      spy.mockReturnValue(Date.now());
      (secretKnockService as any)._onNetworkChange();
      spy.mockReturnValue(Date.now());
      (secretKnockService as any)._onNetworkChange();
      await jest.advanceTimersByTimeAsync(500);
      spy.mockReturnValue(Date.now());
      (secretKnockService as any)._onNetworkChange();
      await jest.advanceTimersByTimeAsync(500);
      spy.mockReturnValue(Date.now());
      (secretKnockService as any)._onNetworkChange();

      // Wait until the debounce timer has finished.
      await jest.advanceTimersByTimeAsync(15001);

      // Only one secret knock request should be sent.
      expect(fetchMock).toHaveBeenCalledOnce();
    });
  });

  describe('extractUrlFromPolicyConfig()', () => {
    it('throws an error if the host property is not a string', () => {
      const config: ICloudFilterBypass = {
        timeout: '600',
        host: 123 as unknown as string,
      };

      expect(() => {
        SecretKnockService.extractUrlFromPolicyConfig(config);
      }).toThrow();
    });

    it('throws an error if the host property does not contain a valid URL', () => {
      const config: ICloudFilterBypass = {
        timeout: '600',
        host: ':/:/:/',
      };

      expect(() => {
        SecretKnockService.extractUrlFromPolicyConfig(config);
      }).toThrow();
    });

    it('returns an URL based on the host property', () => {
      const config: ICloudFilterBypass = {
        timeout: '600',
        host: 'http://example.org:8181/blah',
      };

      expect(SecretKnockService.extractUrlFromPolicyConfig(config).toString()).toEqual(
        'http://example.org:8181/blah',
      );
    });

    it('defaults to http if no scheme is specified in the host property', () => {
      const config: ICloudFilterBypass = {
        timeout: '600',
        host: 'example.com:1111/foobar',
      };

      expect(SecretKnockService.extractUrlFromPolicyConfig(config).toString()).toEqual(
        'http://example.com:1111/foobar',
      );
    });

    it('uses https if specified in the host property', () => {
      const config: ICloudFilterBypass = {
        timeout: '600',
        host: 'https://example.net:1234/xyzzy',
      };

      expect(SecretKnockService.extractUrlFromPolicyConfig(config).toString()).toEqual(
        'https://example.net:1234/xyzzy',
      );
    });
  });

  describe('extractDelayFromPolicyConfig()', () => {
    it('throws an error if the timeout in the configuration is not a string or a number', () => {
      const config: ICloudFilterBypass = {
        timeout: null as unknown as string,
        host: 'http://127.0.0.1:6531/knock',
      };

      expect(() => {
        SecretKnockService.extractDelayFromPolicyConfig(config);
      }).toThrow();
    });

    it('returns a value in milliseconds equivalent to 1 minute if the configured timestamp is less than 2 minutes', () => {
      const config: ICloudFilterBypass = {
        timeout: '97', // <-- seconds
        host: 'http://127.0.0.1:6531/knock',
      };

      expect(SecretKnockService.extractDelayFromPolicyConfig(config)).toEqual(60000); // <-- milliseconds
    });

    it('returns a value in milliseconds equivalent to 1 minute less than the configured timestamp if it is 2 minutes or greater', () => {
      const config: ICloudFilterBypass = {
        timeout: '450', // <-- seconds
        host: 'http://127.0.0.1:6531/knock',
      };

      expect(SecretKnockService.extractDelayFromPolicyConfig(config)).toEqual(390000); // <-- milliseconds
    });
  });

  describe('calculateKnockDelay()', () => {
    it('throws an error if secret knock has not been configured', () => {
      expect(() => {
        secretKnockService.calculateKnockDelay();
      }).toThrow();
    });

    it('returns 0 if not secret knock has been sent', () => {
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      expect(secretKnockService.calculateKnockDelay()).toEqual(0);
    });

    it('returns 0 if a network change has occurred since the last secret knock', () => {
      const now = Date.now();
      storageService.set('lastKnockedAt', now - 300000); // 5 minutes ago
      storageService.set('lastKnockAttemptedAt', now - 300000);
      storageService.set('lastKnockSucceeded', true);

      const spy = jest.spyOn(ipServiceMock, 'privateIpAddressesChangedAt', 'get');
      spy.mockReturnValue(now - 120000); // 2 minutes ago

      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      expect(secretKnockService.calculateKnockDelay()).toEqual(0);
    });

    it('returns 0 if the time since the last secret knock was sent is less than the delay between consecutive secret knocks', () => {
      storageService.set('lastKnockedAt', Date.now() - 1000000);
      storageService.set('lastKnockAttemptedAt', Date.now() - 1000000);
      storageService.set('lastKnockSucceeded', true);
      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      expect(secretKnockService.calculateKnockDelay()).toEqual(0);
    });

    it('returns the time remaining until the next secret knock is due', () => {
      const elapsed = 15000;
      storageService.set('lastKnockedAt', Date.now() - elapsed);
      storageService.set('lastKnockAttemptedAt', Date.now() - elapsed);
      storageService.set('lastKnockSucceeded', true);

      secretKnockService.configureFromPolicyConfig(serialId, secretKnockConfig);
      const remaining = (secretKnockService.delay as number) - elapsed;
      const initialDelay = secretKnockService.calculateKnockDelay();
      expect(initialDelay).toBeGreaterThanOrEqual(remaining - 10);
      expect(initialDelay).toBeLessThanOrEqual(remaining + 10);
    });
  });
});

import { equalsIgnoreCase, mergeBlobUrl } from 'utilities/Helpers';
import TimeoutPromise from 'utilities/TimeoutPromise';

import { TelemetryEventType } from '../constants/TelemetryEventType';
import { PolicyConfig } from '../models/IProductConfig';
import ManagedPolicy from '../models/ManagedPolicy';
import {
  IBannedUser,
  IBlockpagePolicy,
  ICategoryFilterGroup,
  ICloudContentMod,
  ICloudFilterBypass,
  IContentModPolicy,
  ICustomCategory,
  IDefaultModifier,
  IFilterPolicyFlat,
  IGoogleGroupUsers,
  IGroup,
  IGroupMapping,
  ILocation,
  IMetadata,
  IPolicy,
  IPolicyConfig,
  IQuota,
  ITenantInfo,
  ITimeSlot,
  IUserInfo,
} from '../models/PolicyConfigModels';
import TenantId from '../models/TenantId';
import { epochToDate } from '../utilities/Helpers';
import StandaloneEvent from '../utilities/StandaloneEvent';
import { generateRandomDelay, getMaxPolicyDownloadDelay } from '../utils/PolicyDownloadDelayUtils';
import IBlobStorageService from './IBlobStorageService';
import IpService from './IpService';
import ITelemetryService from './ITelemetryService';
import PolicyDownloadService from './PolicyDownloadService';
import StorageService from './StorageService';

/**
 * Manages the caching and fetching of policy data.
 */
export default class PolicyService {
  constructor(
    blobStorageService: IBlobStorageService,
    storageService: StorageService,
    ipService: IpService,
    telemetryService: ITelemetryService,
    username: string,
    tenantId: TenantId | undefined,
    localGroups: string[],
    managedPolicy: ManagedPolicy,
  ) {
    this._storageService = storageService;
    this._policyDownloadService = new PolicyDownloadService(blobStorageService, tenantId);
    this._ipService = ipService;
    this._telemetryService = telemetryService;
    this._localGroups = localGroups;
    this._ipService.setPolicyService(this);
    this._username = username;
    this._tenantId = tenantId;
    this._managedPolicy = managedPolicy;

    this._policyDownloadService.onPolicyDownloaded.addListener(this._onPolicyDownloaded);
  }

  /**
   * Starts the process to fetch the policy config from blob storage and then parse it.
   *
   * If the url hasn't changed and we still have the policy config in the cache then the data will not be redownloaded.
   *
   * When the policy data is ready the `onPolicyLoaded` event will be fired.
   * @param policyDetails The details to download the settings json from.
   * @param isFirstRun Whether this is the first time receiving Firestore data (not from cache).
   */
  public readonly start = async (
    policyDetails: PolicyConfig,
    isFirstRun: boolean = false,
  ): Promise<void> => {
    const url = mergeBlobUrl(policyDetails.resource, policyDetails.name, policyDetails.sas);
    if (url === this.configUrl && this.policyConfig !== undefined) {
      console.debug('Policy config urls are the same, not fetching from blob.');

      await this._logEvent(true);

      // Dispatch the event anyway so any waiting code can continue.
      this.onPolicyLoaded.dispatch(this.constructor.name);
      return;
    }

    // The saving of the policy name is deferred to after the policy has been downloaded.
    // This is so if a download is in progress we still have the name of the policy that is currently in use.
    this._policyName = policyDetails.name;

    try {
      await this._saveConfigUrl(url);
    } catch (error: any) {
      console.error('An error occurred saving the policy config url to local storage.', error);

      this._telemetryService.logError(TelemetryEventType.StorageFailed, error.message as string, {
        dataName: 'configUrl',
      });
    }

    // Apply delay logic before starting the download (non-blocking)
    void this._applyPolicyDownloadDelay(url, isFirstRun);
  };

  /**
   * Stops the process of fetching the policy config from blob storage.
   */
  public readonly stop = (): void => {
    this._policyDownloadService.stop();

    // Cancel any pending delay
    if (this._currentDelay !== undefined) {
      this._currentDelay.cancel();
      this._currentDelay = undefined;
    }
  };

  /**
   * Applies random delay logic before starting policy download.
   * @param url The policy URL to download.
   * @param isFirstRun Whether this is the first time receiving Firestore data.
   */
  private readonly _applyPolicyDownloadDelay = async (
    url: string,
    isFirstRun: boolean,
  ): Promise<void> => {
    // Cancel any existing delay
    if (this._currentDelay !== undefined) {
      this._currentDelay.cancel();
      this._currentDelay = undefined;
    }

    // Don't delay if this is the first run (extension startup or first Firestore data)
    if (isFirstRun) {
      console.debug('PolicyService - No delay applied: first run');
      this._policyDownloadService.start(url);
      return;
    }

    // Don't delay if no policy exists yet (first time reaching mode 2)
    if (this.policyConfig === undefined) {
      console.debug('PolicyService - No delay applied: no existing policy');
      this._policyDownloadService.start(url);
      return;
    }

    // Get the maximum delay from managed storage
    const maxDelaySeconds = getMaxPolicyDownloadDelay(this._managedPolicy);

    // Don't delay if max delay is 0 or less (disabled)
    if (maxDelaySeconds <= 0) {
      console.debug('PolicyService - No delay applied: delay disabled (maxDelay <= 0)');
      this._policyDownloadService.start(url);
      return;
    }

    // Generate random delay
    const delayMs = generateRandomDelay(maxDelaySeconds);
    console.debug(
      `PolicyService - Delaying policy download by ${delayMs}ms (max: ${maxDelaySeconds}s)`,
    );

    // Create cancellable delay
    this._currentDelay = new TimeoutPromise(delayMs);

    // Wait for the delay to complete or be cancelled
    const delayCompleted = await this._currentDelay.promise;
    this._currentDelay = undefined;

    if (delayCompleted) {
      // Delay completed normally, start the download
      this._policyDownloadService.start(url);
    } else {
      // Delay was cancelled
      console.debug('PolicyService - Policy download delay was cancelled');
    }
  };

  /**
   * Checks if the given user is banned according to the policy.
   *
   * If they are they will be added to the 'Banned User' group.
   * Also an alarm will be created to remove the banned user when needed.
   * @param username The username to check.
   * @param tenantId The tenant id for the user.
   */
  private readonly _handleBannedUsers = async (): Promise<void> => {
    const bans = this.bannedUsers?.filter(
      (b) =>
        b.enabled === '1' &&
        b.username.toLowerCase() === this._username.toLowerCase() &&
        (b.tenant === 'global' ||
          (this._tenantId !== undefined && b.tenant === this._tenantId?.toString())),
    );

    if (bans === undefined || bans.length === 0) {
      return;
    }

    // A user can have multiple bans. Get the one with the greatest expiry date.
    const ban = bans.reduce(
      (prev, current) => {
        if (parseInt(prev.banexpirydate) > parseInt(current.banexpirydate)) {
          return prev;
        }
        return current;
      },
      { banexpirydate: '1', comment: '', id: '', enabled: '', username: '', tenant: '' },
    );

    // Get the unix time in seconds and ignore any bans that expire in a minute or less.
    const now = Date.now() / 1000 + 60;
    const expiryTime = parseInt(ban.banexpirydate);

    if (expiryTime <= now) {
      await this._removeBannedUserGroup();
      return;
    }

    // User is banned add them to the local group and create an alarm for when it needs to be removed.
    this._telemetryService.logEvent(TelemetryEventType.UserBanned, {
      expiryDate: epochToDate(expiryTime).toISOString(),
    });

    const config = this.policyConfig;
    if (
      config?.mappedGroups !== undefined &&
      config.mappedGroups.filter((g) => g.id === this._bannedUserGroup.id).length === 0
    ) {
      config.mappedGroups.push(this._bannedUserGroup);

      clearTimeout(this._bannedUserTimeout);
      this._bannedUserTimeout = setTimeout(() => {
        this._handleBannedUsers().catch((err) => {
          console.error('An error occurred handling banned users.', err);
        });
      }, expiryTime * 1000 - Date.now());
    }
  };

  /**
   * Called when the banned users timeout triggers.
   *
   * Removes the timeout and removes the user from the banned user group.
   */
  private readonly _removeBannedUserGroup = async (): Promise<void> => {
    this._mappedGroups = this._mappedGroups.filter((g) => g.id !== this._bannedUserGroup.id);
    clearTimeout(this._bannedUserTimeout);

    // Remap the groups so any mapped banned user groups are not removed too.
    this._mapGroups();
  };

  /**
   * Called when the policy has been sucessfully downloaded and parsed.
   * Saves the policy config to local storage.
   *
   * Dispatches the onPolicyLoaded event once the data has been saved.
   * @param result The parsed policy config.
   */
  private readonly _onPolicyDownloaded = async (
    result: IPolicyConfig | undefined,
  ): Promise<void> => {
    if (result !== undefined) {
      try {
        await this._saveConfig(result, Math.floor(Date.now() / 1000));
      } catch (error: any) {
        console.error('Failed to save the downloaded policy to local storage.', error);

        this._telemetryService.logError(TelemetryEventType.StorageFailed, error.message as string, {
          dataName: 'policy',
        });
        // Continue running as we can use the data stored in memory.
      }
    }

    await this._logEvent(false);
    this.onPolicyLoaded.dispatch(this.constructor.name);
  };

  /**
   * Saves the given policy config to local storage.
   * @param config The policy config to save.
   * @param policyDownloadedAt Unix timestamp (in seconds) specifying when the policy was downloaded
   *  from the server.
   */
  private readonly _saveConfig = async (
    config: IPolicyConfig,
    policyDownloadedAt: number,
  ): Promise<void> => {
    this._storageService.set('policy', config);
    this._storageService.set('policyName', this._policyName);
    this._storageService.set('policyDownloadedAt', policyDownloadedAt);
    await this._storageService.save();
  };

  /**
   * Logs an event to the telemetry service with the info given.
   * @param fromLocal True if we are loading the policy config from local storage/memory.
   * @param error An error that has occurred.
   */
  private readonly _logEvent = async (
    fromLocal: boolean,
    error: any = undefined,
  ): Promise<void> => {
    const isSuccess = error === undefined;
    let configName = this._policyName;

    // If we have loaded from local storage then _policyName won't be set. Get it from local storage instead.
    if (configName === '') {
      configName = this._storageService.getString('policyName') as string;
    }

    const epoch = parseInt(this.policyConfig?.metadata.generated ?? '0');

    const data = {
      configName,
      fromLocal,
      isSuccess,
      outsidePremises: this._ipService.outsidePremisesStatus,
      numberOfLocalGroups: this._localGroups.filter((x) => x !== 'Default Users').length,
      epoch,
      epochDate: epochToDate(epoch).toISOString(),
      policiesCount: this.policyConfig?.flattenedPolicies?.length ?? 0,
      bannedUsersCount: this.policyConfig?.banned?.length ?? 0,
      isTenantValid: this.isTenantValid(this._tenantId),
    };

    if (isSuccess) {
      this._telemetryService.logEvent(TelemetryEventType.PolicyUpdate, data);
    } else {
      this._telemetryService.logError(TelemetryEventType.PolicyUpdateFailed, error, data);
    }
  };

  /**
   * Checks if the provisioned tenant id is correct for the serial config.
   *
   * This will be true if:
   * The extension was provisioned without a tenant id and the serial is singled tenanted or
   * The extension was provisioned with a tenant id and the tenant exists in the policy config.
   *
   * All other configurations will be invalid and will return false.
   * @returns boolean Indicates if the configuration is correct for the serial.
   */
  public readonly isTenantValid = (tenantId: TenantId | undefined): boolean => {
    // Tenants should only be undefined if we haven't yet downloaded a policy.
    // In that case we can't make a decision yet.
    if (this.tenants === undefined) {
      return true;
    }

    if (tenantId === undefined) {
      if (this.tenants.length === 0) {
        // An untenanted serial with no tenant provisioned.
        return true;
      }
      // A tenanted serial with no tenant provisioned.
      return false;
    }

    const tenantInfo = this.getTenantInfo(tenantId);
    if (tenantInfo !== undefined) {
      // A tenanted serial with a vaild tenant provisioned.
      return true;
    }

    return false;
  };

  /**
   * Gets the tenant info from the downloaded policy if it is available.
   * @param tenantId The tenant id to get the info for.
   * @returns ITenantInfo | undefined Returns the tenant info object if it could be found for the given tenant id.
   * Otherwise it will return undefined.
   */
  public readonly getTenantInfo = (tenantId: TenantId): ITenantInfo | undefined => {
    try {
      return this.tenants?.find((t) => t.id.toLowerCase() === tenantId.get().toLowerCase());
    } catch (error: any) {
      console.error('An error occurred getting the tenant info.', error);
      return undefined;
    }
  };

  /**
   * Saves the given policy config url to local storage.
   * @param configUrl The policy config url to save.
   */
  private readonly _saveConfigUrl = async (configUrl: string): Promise<void> => {
    this._storageService.set('configUrl', configUrl);
    await this._storageService.save();
  };

  /**
   * Dispatched when the policy config has been successfully loaded.
   */
  public onPolicyLoaded = new StandaloneEvent<[string]>();

  /**
   * Indicates if the policy has been loaded into memory.
   */
  public get isLoaded(): boolean {
    return this.policyConfig !== undefined;
  }

  /**
   * Gets the current policy config url from the local cache.
   */
  public get configUrl(): string | undefined {
    return this._storageService.getString('configUrl');
  }

  /**
   * Gets the name of the policy config from the url.
   */
  public get policyName(): string | undefined {
    return this._storageService.getString('policyName');
  }

  /**
   * Gets a Unix timestamp (in seconds) specifying when the policy was originally downloaded.
   * This information is uploaded as part of check-ins for Endpoint Manager. See HeartbeatService.
   * This will be undefined if no policy is currently loaded, or we don't know when it was
   *  downloaded.
   * This property was introduced in v2.3.0. It will be undefined for any cached policies which were
   *  originally downloaded before that release.
   */
  public get policyDownloadedAt(): number | undefined {
    return this._storageService.getNumber('policyDownloadedAt');
  }

  /**
   * Gets the banned users from the policy config.
   */
  public get bannedUsers(): IBannedUser[] | undefined {
    return this.policyConfig?.banned;
  }

  /**
   * Gets the blockpages from the policy config.
   */
  public get blockpages(): IBlockpagePolicy[] | undefined {
    return this.policyConfig?.blockpages;
  }

  /**
   * Gets the category filter groups from the policy config.
   */
  public get categoryFilterGroups(): ICategoryFilterGroup[] | undefined {
    return this.policyConfig?.category_filter_groups;
  }

  /**
   * Gets the content mods that have been set in the cloud portal from the policy config.
   */
  public get cloudContentMods(): ICloudContentMod[] | undefined {
    return this.policyConfig?.cloud_content_modifications;
  }

  /**
   * Gets the cloud filter from the policy config.
   */
  public get cloudFilter(): ICloudFilterBypass | undefined {
    return this.policyConfig?.cloud_filter;
  }

  /**
   * Gets the content mods from the policy config.
   */
  public get contentMods(): IContentModPolicy[] | undefined {
    return this.policyConfig?.content_modification;
  }

  /**
   * Gets the custom categories from the policy config.
   */
  public get customCategories(): ICustomCategory[] | undefined {
    return this.policyConfig?.custom_categories;
  }

  /**
   * Gets the custom content mods from the policy config.
   */
  public get customContentMods(): unknown[] | undefined {
    return this.policyConfig?.custom_content_modifiers;
  }

  /**
   * Gets the Content Aware allow list URLs from the policy config.
   * These URLs should be exempt from Content Aware filtering.
   */
  public get contentAwareAllowList(): string[] | undefined {
    return this.policyConfig?.contentAwareAllowList;
  }

  /**
   * Gets the default content mods from the policy config.
   */
  public get defaultContentMods(): IDefaultModifier[] | undefined {
    return this.policyConfig?.default_content_modifiers;
  }

  /**
   * Gets the filter policies as a flattened and ordered array.
   */
  public get flattenedPolicies(): IFilterPolicyFlat[] | undefined {
    return this.policyConfig?.flattenedPolicies;
  }

  /**
   * Gets the google group users from the policy config.
   */
  public get googleGroupUsers(): IGoogleGroupUsers | undefined {
    return this.policyConfig?.google_group_users;
  }

  /**
   * Gets the groups from the policy config.
   */
  public get groups(): IGroup[] | undefined {
    return this.policyConfig?.groups;
  }

  /**
   * Gets the group mapping from the policy config.
   */
  public get groupMapping(): IGroupMapping[] | undefined {
    return this.policyConfig?.group_mapping;
  }

  /**
   * Gets the locations from the policy config.
   */
  public get locations(): ILocation[] | undefined {
    return this.policyConfig?.locations;
  }

  /**
   * Gets the metadata from the policy config.
   */
  public get metadata(): IMetadata | undefined {
    return this.policyConfig?.metadata;
  }

  /**
   * Gets the policies from the policy config.
   */
  public get policies(): IPolicy | undefined {
    return this.policyConfig?.policies;
  }

  /**
   * Gets the quotas from the policy config.
   */
  public get quotas(): IQuota[] | undefined {
    return this.policyConfig?.quotas;
  }

  /**
   * Gets the tenants from the policy config.
   */
  public get tenants(): ITenantInfo[] | undefined {
    return this.policyConfig?.tenants;
  }

  /**
   * Gets the time slots from the policy config.
   */
  public get timeSlots(): ITimeSlot[] | undefined {
    return this.policyConfig?.time_slots;
  }

  /**
   * Gets the users from the policy config.
   */
  public get users(): IUserInfo[] | undefined {
    return this.policyConfig?.users;
  }

  /**
   * Gets the policy config from the policy config.
   */
  public get policyConfig(): IPolicyConfig | undefined {
    const config = this._storageService.get('policy') as IPolicyConfig;
    if (config === undefined) {
      return undefined;
    }

    // The mapped groups are purposfully not cached with the policy config to better handle banned users.
    config.mappedGroups = this.mappedGroups;
    return config;
  }

  /**
   * Gets the mapped groups if they have been mapped. Otherwise an empty array will be returned.
   */
  public get mappedGroups(): IGroup[] {
    return this._mappedGroups;
  }

  /**
   * Update the local and cloud groups and runs the group mapping for filtering.
   * @param localGroups The groups from the local machine.
   * @param cloudGroups The groups that have been downloaded from the cloud.
   */
  public readonly updateGroupMapping = async (
    localGroups: string[],
    cloudGroups: string[],
  ): Promise<void> => {
    this._localGroups = localGroups;
    this._cloudGroups = cloudGroups;

    this._mapGroups();
    await this._handleBannedUsers();
  };

  /**
   * Sets the mapped groups for the policy config.
   */
  private readonly _mapGroups = (): void => {
    const directoryGroups = [...this._localGroups, ...this._cloudGroups];

    const matchedMappings = this.groupMapping?.filter((mapping) => {
      return directoryGroups.some((name) => {
        return mapping.directory_group.some((group) => equalsIgnoreCase(name, group));
      });
    });

    const mappedGroups =
      this.policyConfig?.groups.filter((group) => {
        return matchedMappings?.some((mapping) => mapping.local_group === group.id);
      }) ?? [];

    if (mappedGroups.length === 0) mappedGroups.push(this._defaultUsersGroup);

    if (mappedGroups !== undefined) {
      this._mappedGroups = mappedGroups;
    }

    // Count case-insensitive matches for telemetry
    const caseInsensitiveMatches = this._countCaseInsensitiveMatches(directoryGroups);

    this._telemetryService.logEvent(TelemetryEventType.GroupsMapped, {
      numberOfDirectoryGroups: directoryGroups.length,
      numberOfMappedGroups: mappedGroups.length,
      caseInsensitiveMatches,
    });
  };

  /**
   * Counts how many directory groups required case-insensitive matching.
   * @param directoryGroups The directory groups to check.
   * @returns Number of groups that needed case-insensitive matching.
   */
  private readonly _countCaseInsensitiveMatches = (directoryGroups: string[]): number => {
    let count = 0;

    this.groupMapping?.forEach((mapping) => {
      directoryGroups.forEach((name) => {
        mapping.directory_group.forEach((group) => {
          const exactMatch = name === group;
          const caseInsensitiveMatch = equalsIgnoreCase(name, group);

          if (!exactMatch && caseInsensitiveMatch) {
            count++;
          }
        });
      });
    });

    return count;
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Used to temporarily store the policy name while we download the policy.
   * After it has been downloaded the name in this variable will get stored in the local cache.
   */
  private _policyName: string = '';

  /**
   * The alarm service used for banned users.
   */
  private _bannedUserTimeout: ReturnType<typeof setTimeout> | undefined;

  /**
   * A copy of the local groups to use for group mapping.
   */
  private _localGroups: string[];

  /**
   * A copy of the cloud groups to use for group mapping.
   */
  private _cloudGroups: string[] = [];

  /**
   * The mapped groups for the current user.
   * If the groups have not been mapped this will be an empty array.
   */
  private _mappedGroups: IGroup[] = [];

  /**
   * The storage service where the policy config is saved.
   */
  private readonly _storageService: StorageService;

  /**
   * The service for parsing the downloaded policy json.
   */
  private readonly _policyDownloadService: PolicyDownloadService;

  /**
   * Manages ip fetching and actions.
   */
  private readonly _ipService: IpService;

  /**
   * Manages sending log messages to the cloud (e.g. App Insights).
   */
  private readonly _telemetryService: ITelemetryService;

  /**
   * The username for the current user.
   */
  private readonly _username: string;

  private readonly _tenantId?: TenantId;

  /**
   * The managed policy configuration containing extension settings.
   */
  private readonly _managedPolicy: ManagedPolicy;

  /**
   * The current delay operation for policy downloads, if any.
   */
  private _currentDelay?: TimeoutPromise;

  /**
   * Default group if no other group is matched.
   */
  private readonly _defaultUsersGroup: IGroup = {
    id: '96871BA9-8808-3074-8AFE-F88F872B0A6D',
    comment: 'Static ID over all environments for all customers',
    name: 'Default Users',
  };

  private readonly _bannedUserGroup: IGroup = {
    id: '7A9C36F1-D495-3ACE-9EA5-8A66D3A9A032', // Static ID over all environments for all customers for BANNED USERS group
    comment: 'The banned users group.',
    name: 'Banned Users',
  };
}

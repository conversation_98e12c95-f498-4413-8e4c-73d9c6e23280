import {
  Firestore,
  FirestoreError,
  onSnapshot,
  Unsubscribe,
  QuerySnapshot,
  DocumentData,
  collection,
  where,
  documentId,
  query,
  doc,
  setDoc,
  updateDoc,
  UpdateData,
  deleteDoc,
} from 'firebase/firestore';
import StandaloneEvent from 'utilities/StandaloneEvent';
import { TelemetryEventType } from '../constants/TelemetryEventType';
import ITelemetryService from './ITelemetryService';

/**
 * Listens for changes to an individual document in Firestore.
 * This can cope with the document being created, deleted, or changed.
 *
 * @note This class subscribes to a collection query targeting a single document. Creating multiple
 *  instances of this class to listen to multiple documents in the same collection is NOT cost
 *  efficient!
 *
 * @param DocumentType Describes the data structure of the document we expect to receive.
 */
export default class FirestoreDocumentListener<DocumentType> {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance of this class.
   *
   * @param telemetryService The service to be used for logging telemetry to the cloud.
   */
  constructor(telemetryService: ITelemetryService) {
    this._telemetryService = telemetryService;
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Check if this object has started trying to listen for the Firestore document.
   * This doesn't necessarily mean that it is currently subscribed.
   * It could be started but not subscribed if the subscription failed with an error.
   *
   * @see isSubscribed
   */
  public get isStarted(): boolean {
    return this._firestore !== undefined;
  }

  /**
   * Check if this object has a current subscription to the Firestore document.
   * Under normal circumstances, we should be subscribed whenever we are started. However, the
   *  subscription could fail with an error. It will periodically try to resubscribe though.
   *
   * @see isStarted
   */
  public get isSubscribed(): boolean {
    return this._firestoreUnsubscribe !== undefined;
  }

  /**
   * Get the path of the document we are currently listening to, if any.
   * Returns undefined if we are not attempting to listen to any document.
   */
  public get path(): string | undefined {
    return this._path;
  }

  /**
   * Get the current value of the document.
   * Returns undefined if the document doesn't exist, or we aren't currently subscribed.
   */
  public get data(): DocumentType | undefined {
    return this._document;
  }

  /**
   * Check if the document currently exists, according to the most recent snapshot.
   * Always returns false if we aren't currently subscribed.
   */
  public get exists(): boolean {
    return this._firestoreUnsubscribe !== undefined && this._document !== undefined;
  }

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Listen for changes to the specified Firestore document.
   * If the subscription fails for any reason then it will periodically retry, until stop() is
   *  called.
   *
   * @param firestore The Firestore instance we're currently connected to.
   * @param path The complete path of the document to listen to in Firestore.
   *
   * @see stop()
   */
  public readonly start = (firestore: Firestore, path: string): void => {
    if (path === '') {
      throw new Error('Firestore document path cannot be empty.');
    }

    // If nothing has changed then don't re-subscribe as it would incur extra read costs.
    if (this._firestore === firestore && this._path === path) {
      return;
    }

    this.stop();
    this._firestore = firestore;
    this._path = path;
    this._subscribe();
  };

  /**
   * Stop listening for changes to any document.
   */
  public readonly stop = (): void => {
    if (this._firestoreUnsubscribe !== undefined) {
      this._firestoreUnsubscribe();
      this._firestoreUnsubscribe = undefined;
    }

    this._firestore = undefined;
    this._path = undefined;
    this._document = undefined;

    if (this._retryTimeout !== undefined) {
      clearTimeout(this._retryTimeout);
      this._retryTimeout = undefined;
    }
  };

  /**
   * Add or replace the entire document in Firestore.
   * You must call "start()" before calling this.
   *
   * @return A promise which resolves if the document was successfully added or overwritten.
   * @warning Do not use this to modify part of the document as you might accidentally overwrite
   *  changes from another source. Prefer calling update() where possible.
   * @throws {Error} start() has not been called yet
   */
  public readonly set = async (data: DocumentType): Promise<void> => {
    if (this._firestore === undefined || this._path === undefined) {
      throw new Error('Cannot set document. Listener has not been started.');
    }

    const documentRef = doc(this._firestore, this._path);
    await setDoc(documentRef, data as unknown);
  };

  /**
   * Update parts of the document in Firestore, while leaving the rest untouched.
   * If you want to update some child properties but not others then use dot notation. For example,
   *  let's say you start with a document like this:
   *
   *  {
   *    user: {
   *      name: 'Joe Bloggs',
   *      age: 57
   *    }
   *  }
   *
   * If you want to update only the user's age, then reference the age property like this:
   *
   *  {
   *    'user.age': 58
   *  }
   *
   * Do NOT try to update the entire 'user' property if you don't need to as that risks overwriting
   *  or removing other properties inside it.
   *
   * @param changes The changes you want to apply to the document.
   * @return A promise which resolves when the update has been applied successfully.
   * @throws {Error} start() has not been called yet
   */
  public readonly update = async (changes: UpdateData<DocumentType>): Promise<void> => {
    if (this._firestore === undefined || this._path === undefined) {
      throw new Error('Cannot update document. Listener has not been started.');
    }

    const documentRef = doc(this._firestore, this._path);
    // Type-casting the changes here is a bit of a hack. It's safe as long as DocumentType is
    //  already a Firestore-compatible or simple JSON-friendly data structure.
    await updateDoc(documentRef, changes as UpdateData<DocumentData>);
  };

  /**
   * Delete the entire document from Firestore.
   *
   * @returns A promise which resolves when the deletion has been completed successfully.
   * @throws {Error} start() has not been called yet
   */
  public readonly delete = async (): Promise<void> => {
    if (this._firestore === undefined || this._path === undefined) {
      throw new Error('Cannot delete document. Listener has not been started.');
    }

    await deleteDoc(doc(this._firestore, this._path));
  };

  /**
   * Set up a Firestore snapshot, listening to the stored document path.
   */
  private readonly _subscribe = (): void => {
    if (this._firestore === undefined || this._path === undefined) {
      throw new Error('Not initialised.');
    }

    // Ensure any existing subscription is stopped.
    if (this._firestoreUnsubscribe !== undefined) {
      this._firestoreUnsubscribe();
    }

    try {
      // We have to do a collection query which specifically looks for the identified document.
      // If we do a document query then it won't cope with the document being added and deleted.
      const [collectionPath, id] = FirestoreDocumentListener.splitPath(this._path);
      const q = query(collection(this._firestore, collectionPath), where(documentId(), '==', id));
      this._firestoreUnsubscribe = onSnapshot(q, this._onSnapshot, this._onSnapshotError);

      console.debug(`FirestoreDocumentListener - Subscribed to document: ${this._path} `);

      // If the subscription fails at any time then we want to try subscribing again. However, we
      //  only want to try again if it's been at least 30 minutes since we last tried to subscribe.
      this._lastSubscribedAt = Date.now();
    } catch (e: any) {
      console.warn('FirestoreDocumentListener - Failed to subscribe to Firestore document.', e);
      this._telemetryService.logError(TelemetryEventType.FirestoreDocumentListenerError, e, {
        path: this._path,
      });
      this.stop();
    }
  };

  // -----------------------------------------------------------------------------------------------
  // Event handlers.

  /**
   * Called when the document has been created, updated, or deleted.
   * This is also called immediately after we subscribe to the document to give us its initial
   *  value.
   *
   * @param snapshot Contains details of the query result.
   */
  private readonly _onSnapshot = (snapshot: QuerySnapshot<DocumentData>): void => {
    // Log that we have subscribed successfully after getting the first snapshot.
    // Ignore data from the cache as that does not necessarily mean that the subscribtion was successful.
    if (this._isFirstSnapshot && !snapshot.metadata.fromCache) {
      this._isFirstSnapshot = false;
      this._telemetryService.logEvent(TelemetryEventType.FirestoreDocumentListenerSubscribed, {
        path: this._path,
      });
    }

    const changes = snapshot.docChanges();
    if (changes.length === 0) {
      return;
    }

    // We should only ever get one document back.
    switch (changes[0].type) {
      case 'added':
        // The document will appear to be 'added' if it already existed when the query started.
        console.debug(`FirestoreDocumentListener - Document added: ${this._path ?? ''}`);
        this._document = changes[0].doc.data() as DocumentType;
        this.onAdded.deferDispatch(this._document);
        break;

      case 'modified':
        console.debug(`FirestoreDocumentListener - Document modified: ${this._path ?? ''}`);
        this._document = changes[0].doc.data() as DocumentType;
        this.onModified.deferDispatch(this._document);
        break;

      case 'removed':
        console.debug(`FirestoreDocumentListener - Document removed: ${this._path ?? ''}`);
        this._document = undefined;
        this.onRemoved.deferDispatch();
        break;

      default:
        console.warn(
          `FirestoreDocumentListener - Document changed, but change type was not recognised: ${
            this._path ?? ''
          }`,
        );
        break;
    }
  };

  /**
   * Called if our subscription to the Firestore document fails.
   * We will try to resubscribe periodically.
   *
   * @param error Contains details about the error which occurred.
   */
  private readonly _onSnapshotError = (error: FirestoreError): void => {
    // The unsubscribe function will no longer work.
    this._firestoreUnsubscribe = undefined;

    // Only try again if it's been at least 30 minutes + jitter since the last attempt.
    // Even if we *think* it's been longer than that, wait at least 30 seconds before retrying.
    const earliestRetryAt = this._lastSubscribedAt + 1800000 + Math.random() * 30000;
    const retryDelay = Math.max(earliestRetryAt - Date.now(), 30000);

    if (this._retryTimeout !== undefined) {
      clearTimeout(this._retryTimeout);
    }

    this._retryTimeout = setTimeout(() => {
      this._subscribe();
    }, retryDelay);

    console.warn(
      `FirestoreDocumentListener - Subscription error for document: ${this._path ?? ''}`,
      error,
    );
    this._telemetryService.logError(TelemetryEventType.FirestoreDocumentListenerError, error, {
      path: this._path ?? '',
    });
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Split a Firestore document path into the collection and document ID.
   * For example, if the full path of the document is "customers/UNCL1234/users/joe.bloggs",
   *  then this returns a tuple containing "customers/UNCL1234/users" and "joe.bloggs".
   *
   * @param path The Firestore document path to split up.
   * @returns Returns a tuple containing two strings: the path of the document collection, and the
   *  document ID.
   */
  public static readonly splitPath = (path: string): [string, string] => {
    const lastSlash = path.lastIndexOf('/');
    if (lastSlash < 0) {
      return ['', path];
    }

    return [path.substring(0, lastSlash), path.substring(lastSlash + 1)];
  };

  // -----------------------------------------------------------------------------------------------
  // Events.

  /**
   * Triggered when the document has been created, or if it already existed when we subscribed.
   * The argument is the contents of the new document.
   *
   * @note By the time this event is triggered, then document data stored in memory in this object
   *  will also have been updated.
   *
   * @todo Consider adding a boolean flag indicating whether the data came from cache?
   */
  public readonly onAdded = new StandaloneEvent<[DocumentType]>();

  /**
   * Triggered when the document has been modified.
   * The argument is the complete contents of the document after the update (not just the bits
   *  which have changed). It is type-cast to the specified document type, but is not validated.
   *
   * @note By the time this event is triggered, then document data stored in memory in this object
   *  will also have been updated.
   */
  public readonly onModified = new StandaloneEvent<[DocumentType]>();

  /**
   * Triggered when the document has been removed.
   *
   * @note By the time this event is triggered, then document data stored in memory in this object
   *  will also have been updated.
   */
  public readonly onRemoved = new StandaloneEvent();

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * A reference to the Firestore instance we're using.
   * This will be undefined if this service hasn't been started yet.
   */
  private _firestore?: Firestore;

  /**
   * The path of the document we're listening to in Firestore.
   * This will be undefined if this service hasn't been started yet.
   */
  private _path?: string;

  /**
   * The contents of the document received from Firestore.
   * This is stored in memory only. We rely on Firestore persistence to cache it to disk.
   */
  private _document?: DocumentType;

  /**
   * The service for logging telemetry messages.
   */
  private readonly _telemetryService: ITelemetryService;

  /**
   * A function that will unsubscribe from the Firestore listeners.
   * This will be undefined if the listener has not yet been created yet, or it has failed and we're
   *  waiting before retrying.
   */
  private _firestoreUnsubscribe?: Unsubscribe;

  /**
   * Handle to a timeout which is used to retry the subscription after an error.
   * This will be undefined if no retry is currently scheduled.
   */
  private _retryTimeout?: ReturnType<typeof setTimeout>;

  /**
   * Timestamp (in milliseconds) of when we last tried to subscribe to Firestore.
   * If the subscription fails with an error then we'll retry, but only if it's been at least 30
   *  minutes since our last attempt.
   */
  private _lastSubscribedAt = 0;

  /**
   * Indicates if the extension is recieving it's first snapshot.
   * If so then that first connection should be logged.
   */
  private _isFirstSnapshot = true;
}

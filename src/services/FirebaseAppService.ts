import FetchService, { Action } from './FetchService';
import * as firebase from 'firebase/app';
import {
  Firestore,
  clearIndexedDbPersistence,
  initializeFirestore,
  persistentLocalCache,
  terminate,
  persistentSingleTabManager,
} from 'firebase/firestore';
import SerialId from 'models/SerialId';
import StandaloneEvent from 'utilities/StandaloneEvent';
import StorageService from './StorageService';
import TemplateString from 'models/TemplateString';
import ITelemetryService from './ITelemetryService';
import LinearBackOff from 'back-off-methods/LinearBackOff';
import { TelemetryEventType } from '../constants/TelemetryEventType';

/**
 * Downloads, caches, and applies the Firebase configuration, and stores the core Firebase object.
 * This does not handle authentication.
 *
 * @see FirebaseAuthenticationService
 */
export default class FirebaseAppService {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance of the service.
   * This does not attempt to initialise Firebase.
   * You must call start() after construction to do that.
   *
   * @param storageService The storage object which is responsible for caching Firebase config. The
   *  caller is responsible for ensuring the data has already been loaded into memory.
   * @param fetchService An optional dependency injection. This object which will wrap fetch()
   *  requests with retry logic. If not specified, then one will be instantiated automatically.
   */
  public constructor(
    storageService: StorageService,
    telemetryService: ITelemetryService,
    fetchService?: FetchService,
  ) {
    this._storageService = storageService;
    this._telemetryService = telemetryService;
    this._fetchService =
      fetchService ?? new FetchService(new LinearBackOff(15000, 120000, 3000), 0);
    this._fetchService.setResponseHandler(this._responseHandler);
    this._fetchService.onFinished.addListener(this._onFetchFinished);
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Check if this service is running; i.e. start() has been called.
   */
  public get isRunning(): boolean {
    return this._fetchService.isRunning || this._firebaseApp !== undefined;
  }

  /**
   * Check if Firebase has been successfully initialised.
   */
  public get isInitialised(): boolean {
    return this._firebaseApp !== undefined;
  }

  /**
   * Get the core Firebase app, if it has been successfully initialised.
   * This returns undefined if initialisation has not worked.
   */
  public get firebaseApp(): firebase.FirebaseApp | undefined {
    return this._firebaseApp;
  }

  /**
   * Get the active firestore instance.
   * Returns undefined if it hasn't been initialised yet.
   */
  public get firestore(): Firestore | undefined {
    return this._firestore;
  }

  // -----------------------------------------------------------------------------------------------
  // Public operations.

  /**
   * Start trying to initialise Firebase in the background.
   * This will initialise it from cache if possible. If there is no configuration in the cache, or
   *  the cached configuration fails, then this will try to download the configuration from the
   *  specified URL. Successfully downloaded and applied configuration will be cached.
   *
   * @param urlTemplate A template for the URL to download the Firebase configuration from.
   * @param serial The customer's serial. This needs to be set as part of the request to fetch the
   *  Firebase configuration.
   * @returns A promise which resolves when we have successfully started trying to initialise
   *  Firebase. Note that Firebase will not necessarily be initialised when this promise resolves.
   *  The caller will need to add a listener to the onInitialised event.
   *
   * @note The URL and serial are not remembered by this object. As such, cached configuration will
   *  not be automatically discarded if the URL or serial changes. It is the caller's responsibility
   *  to reset the cache if that is likely to be necessary.
   *
   * @warning If Firebase was already initialised, then calling this function again will destroy it
   *  and initialise a new instance. That is liable to cause various problems if anything else is
   *  still using Firebase.
   *
   * @warning The caller must ensure the returned promise has settled before calling start() again,
   *  and before calling stop.
   */
  public readonly start = async (urlTemplate: TemplateString, serial: SerialId): Promise<void> => {
    await this.stop();

    // Try to initialise from previously cached data if possible.
    // TODO: If the cache is over a certain age, then update it for next time.
    const config = this._storageService.get('config') as firebase.FirebaseOptions | undefined;
    if (config !== undefined) {
      try {
        await this._initialise(config);
        console.debug('FirebaseAppService - Initialised Firebase from cached config.');
        return;
      } catch (e: any) {
        console.warn(
          'FirebaseAppService - Failed to initialise Firebase from cached configuration.',
          e,
        );
        // Ensure we don't try to reuse bad config again in future.
        this._storageService.delete('config');
      }
    }

    console.debug('FirebaseAppService - Requesting Firebase config from the cloud.');

    // Start requesting the configuration from the cloud.
    const url = urlTemplate.toUrl({ glsHash: serial.getGlsHash() });
    const requestInit: RequestInit = {
      method: 'GET',
      headers: {
        'X-Client-Id': serial.toString(),
      },
    };
    await this._fetchService.start(url, requestInit);
  };

  /**
   * Cancel any attempt to initialise Firebase, and de-initialise it if it's already initialised.
   *
   * @param clearFirestoreCache If true the indexed db cache for firestore will be cleared.
   *
   * @warning Unexpected errors may occur if this instance of Firebase is already initialised and
   *  being used elsewhere.
   *
   * @warning The caller must ensure the returned promise has settled before calling start() or
   *  stop() again.
   *
   * @warning In order to reset the firestore cache the current firestore instance will be terminated.
   */
  public readonly stop = async (clearFirestoreCache: boolean = false): Promise<void> => {
    await this._fetchService.stop();

    this._errorsLogged = new Set();

    if (this._firebaseApp !== undefined) {
      try {
        await firebase.deleteApp(this._firebaseApp);
      } catch (e: any) {
        console.warn('An error occurred while de-initialising Firebase.', e);
      }
      this._firebaseApp = undefined;
    }

    if (this._firestore !== undefined && clearFirestoreCache) {
      try {
        await terminate(this._firestore);
        await clearIndexedDbPersistence(this._firestore);
      } catch (e: any) {
        console.warn('An error occurred while de-initialising Firestore.', e);
      }
    }

    this._firestore = undefined;
  };

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Handles one response from the Firebase configuration endpoint.
   * If the response indicates success then this will use the configuration to initialise Firebase.
   * Otherwise, it will request a retry.
   *
   * @param response The fetch API response to be processed.
   * @returns An action specifying whether to retry the request.
   */
  private readonly _responseHandler = async (response: Response): Promise<Action> => {
    // The response body should always be JSON.
    let body: any;
    try {
      body = await response.json();
    } catch (e: any) {
      console.warn(
        'FirebaseAppService - Failed to parse response from Firebase configuration endpoint.',
        e,
      );
      if (!this._errorsLogged.has('parse')) {
        this._logError(e, response.status, []);
        this._errorsLogged.add('parse');
      }

      if (response.ok) {
        return Action.retry;
      }
    }

    // Retry if the request failed for any reason, or the response looks invalid.
    if (!response.ok || body?.isSuccess !== true) {
      console.warn(
        `FirebaseAppService - Config request failed. HTTP status ${response.status}. Errors:`,
        body?.errors ?? [],
      );

      if (!this._errorsLogged.has('request')) {
        this._logError(
          `FirebaseAppService - Config request failed. HTTP status ${response.status}.`,
          response.status,
          body?.errors ?? [],
        );
        this._errorsLogged.add('request');
      }

      return Action.retry;
    }

    if (typeof body.data !== 'object' || body.data === null) {
      console.warn(
        'FirebaseAppService - Invalid response received from Firebase configuration endpoint:',
        body,
      );

      if (!this._errorsLogged.has('invalid')) {
        this._logError(
          'FirebaseAppService - Invalid response received from Firebase configuration endpoint.',
          response.status,
          body?.errors ?? [],
        );

        this._errorsLogged.add('invalid');
      }

      return Action.retry;
    }

    // Try to initialise Firebase using the downloaded configuration.
    const config = body.data as firebase.FirebaseOptions;
    try {
      await this._initialise(config);
      console.debug('FirebaseAppService - Initialised Firebase from downloaded config.');
    } catch (e: any) {
      console.error(
        `FirebaseAppService - Failed to initialise Firebase using the downloaded configuration.`,
        e,
      );

      if (!this._errorsLogged.has('initialise')) {
        this._logError(e, response.status, body?.errors ?? []);

        this._errorsLogged.add('initialise');
      }
      return Action.retry;
    }

    // Cache the configuration for future use.
    // Caching isn't essential so we don't need to wait for it to finish or stop if it fails.
    this._storageService.set('config', config);
    this._storageService.save().catch((e: any) => {
      console.warn('FirebaseAppService - Failed to cache Firebase configuration.', e);
    });

    return Action.stop;
  };

  /**
   * Handler which is called when the fetch service finishes a run.
   *
   * @param maxAttemptsReached True if the run finished because the service ran out of attempts due
   *  to too many failures. False if it stopped for any other reason.
   */
  private readonly _onFetchFinished = (maxAttemptsReached: boolean): void => {
    if (maxAttemptsReached) {
      console.warn(
        'FirebaseAppService - Stopping attempts to initialise Firebase as it failed too many times.',
      );

      this._errorsLogged = new Set();
      this.onFailed.dispatch();
    }
  };

  /**
   * Use the specified configuration details to initialise Firebase.
   * This will update the state and dispatch the onInitialise event if successful.
   * Callers should assume this will throw an error if initialisation failed.
   *
   * @param config The configuration to apply.
   */
  private readonly _initialise = async (config: firebase.FirebaseOptions): Promise<void> => {
    console.debug(`Initialising Firebase with project ID: ${config.projectId ?? '(undefined)'}`);
    this._firebaseApp = firebase.initializeApp(config);

    // Now initialise firestore
    if (this._firebaseApp !== undefined) {
      const settings = {
        localCache: persistentLocalCache({
          tabManager: persistentSingleTabManager({ forceOwnership: true }),
        }),
      };
      this._firestore = initializeFirestore(this._firebaseApp, settings);
    }

    this._errorsLogged = new Set();

    this.onInitialised.dispatch(this._firebaseApp, config.messagingSenderId ?? '');
  };

  /**
   * Logs an error to the telemetry service using the information provided.
   * @param error The error to log.
   * @param status The status number of the response.
   * @param errors The list of errors from the response body.
   */
  private readonly _logError = (error: string | Error, status: number, errors: string[]): void => {
    this._telemetryService.logError(TelemetryEventType.FirebaseConfigFailed, error, {
      status,
      errors,
    });
  };

  // -----------------------------------------------------------------------------------------------
  // Events.

  /**
   * This event is triggered when Firebase has been successfully initialised.
   * The initialised Firebase app will be passed to the handler as a parameter. However, it can also
   *  be accessed later from the firebaseApp property on this object.
   * The second parameter will be the Firebase Messaging sender ID retrieved from the Firebase
   *  config.
   */
  public readonly onInitialised = new StandaloneEvent<[firebase.FirebaseApp, string]>();

  /**
   * This event is triggered if Firebase initialisation failed and will not be reattempted.
   * This will happen if requests to download the configuration have failed too many times.
   */
  public readonly onFailed = new StandaloneEvent();

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The cache object which will store the Firebase config between invocations.
   * The config will be read from here when startFromCache() is called.
   * It will be written to here if config data is successfully downloaded from the cloud.
   */
  private readonly _storageService: StorageService;

  /**
   * Wraps requests sent to the Firebase configuration end-point.
   */
  private readonly _fetchService: FetchService;

  /**
   * The telemetry service for logging events.
   */
  private readonly _telemetryService: ITelemetryService;

  /**
   * The Firebase app object, if it's initialised.
   * This will be undefined if we haven't successfully retrieved any Firebase config yet.
   */
  private _firebaseApp?: firebase.FirebaseApp = undefined;

  private _firestore?: Firestore = undefined;

  /**
   * Holds a list of all errors logged for one run.
   *
   * This is to stop us logging the same error after a retry, but we can still log a new error in a retry.
   */
  private _errorsLogged = new Set<string>();
}

import StorageService from 'services/StorageService';

/**
 * Manages multiple caches to persist data between invocations of the service worker.
 * The caches are divided into categories so that they can be selectively loaded, saved, and cleared
 *  when necessary.
 */
export default class LocalCacheService {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct the caches to operate on the specified type of storage.
   *
   * @param storageArea The type of storage to load/save. Can be local, sync,  or managed. Note that
   *  managed storage is read-only. If this is undefined then loading and saving will not occur.
   *  This cache will be in-memory only.
   */
  public constructor(storageArea: chrome.storage.StorageArea = chrome.storage.local) {
    this.provisioningInfo = new StorageService('provisioningInfo', storageArea);
    this.clientSettings = new StorageService('clientSettings', storageArea);
    this.productConfig = new StorageService('productConfig', storageArea);
    this.secretKnock = new StorageService('secretKnock', storageArea);
    this.policyConfig = new StorageService('policyConfig', storageArea);
    this.firebase = new StorageService('firebase', storageArea);
    this.deviceRegistration = new StorageService('deviceRegistration', storageArea);
    this.cloudGroups = new StorageService('cloudGroups', storageArea);
    this.ipAddresses = new StorageService('ipAddresses', storageArea);
    this.safeguardingContext = new StorageService('safeguardingContext', storageArea);
    this.licenseStatus = new StorageService('licenseStatus', storageArea);
    this.keepAlive = new StorageService('keepAlive', storageArea);
  }

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Load all of the cached data in all categories.
   */
  public readonly loadAll = async (): Promise<void> => {
    await Promise.all([
      this.provisioningInfo.load(),
      this.clientSettings.load(),
      this.productConfig.load(),
      this.secretKnock.load(),
      this.policyConfig.load(),
      this.firebase.load(),
      this.deviceRegistration.load(),
      this.cloudGroups.load(),
      this.ipAddresses.load(),
      this.safeguardingContext.load(),
      this.licenseStatus.load(),
      this.keepAlive.load(),
    ]);
  };

  /**
   * Populate all of the individual caches from an existing in-memory object.
   * This is used instead of loadAll() where the data has already been loaded in a single operation.
   *
   * @param data An object containing the entire storage contents. Each property should be a named
   *  object, where the name corresponds to one of the individual StorageService instances.
   */
  public readonly populateAll = (data: Record<string, any>): void => {
    this.all.forEach((storageService: StorageService) => {
      storageService.populate(data[storageService.name]);
    });
  };

  /**
   * Clear all of the cached data in all categories.
   */
  public readonly clearAll = async (): Promise<void> => {
    await Promise.all([
      this.provisioningInfo.clear(),
      this.clientSettings.clear(),
      this.productConfig.clear(),
      this.secretKnock.clear(),
      this.policyConfig.clear(),
      this.firebase.clear(),
      this.deviceRegistration.clear(),
      this.cloudGroups.clear(),
      this.ipAddresses.clear(),
      this.safeguardingContext.clear(),
      this.licenseStatus.clear(),
      this.keepAlive.clear(),
    ]);
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Convenience accessor which gets an array of all the storage service instances.
   */
  public get all(): StorageService[] {
    return [
      this.provisioningInfo,
      this.clientSettings,
      this.productConfig,
      this.secretKnock,
      this.policyConfig,
      this.firebase,
      this.deviceRegistration,
      this.cloudGroups,
      this.ipAddresses,
      this.safeguardingContext,
      this.licenseStatus,
      this.keepAlive,
    ];
  }

  /**
   * Contains a cache of data about the provisioning of the device.
   *
   * Values include:
   * - hardwareId
   */
  public readonly provisioningInfo: StorageService;

  /**
   * Contains a cached copy of the client settings data.
   */
  public readonly clientSettings: StorageService;

  /**
   * Contains the product config.
   */
  public readonly productConfig: StorageService;

  /**
   * Contains details for the secret knock service.
   */
  public readonly secretKnock: StorageService;

  /**
   * Contains a cached copy of the policy config.
   */
  public readonly policyConfig: StorageService;

  /**
   * Contains Firebase configuration and authentication information.
   */
  public readonly firebase: StorageService;

  /**
   * Contains cached details of our device registration.
   */
  public readonly deviceRegistration: StorageService;

  /**
   * Contains cached details of cloud groups
   */
  public readonly cloudGroups: StorageService;

  /**
   * Contains a cache of any ip addresses for the device.
   */
  public readonly ipAddresses: StorageService;

  /**
   * Contains safeguarding context information.
   */
  public readonly safeguardingContext: StorageService;

  /**
   * Contains the current license status.
   */
  public readonly licenseStatus: StorageService;

  /**
   * Contains a periodically updated timestamp used to keep the service worker alive.
   */
  public readonly keepAlive: StorageService;
}

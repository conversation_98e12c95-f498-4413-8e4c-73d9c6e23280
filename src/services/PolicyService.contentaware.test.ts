import TenantId from 'models/TenantId';

import { PolicyConfig } from '../models/IProductConfig';
import IpServiceMock from '../services/IpService.mock';
import MockBlobStorageService, { MockUrlData } from '../test-helpers/MockBlobStorageService';
import MockTelemetryService from '../test-helpers/MockTelemetryService';
import { getTestPolicyConfigWithContentAware } from '../test-helpers/test-policy-config';
import { mergeBlobUrl } from '../utilities/Helpers';
import IBlobStorageService from './IBlobStorageService';
import PolicyService from './PolicyService';
import StorageService from './StorageService';

describe('PolicyService - Content Aware Allow List', () => {
  const tenant = new TenantId('f417a2c4-f99c-11ea-8caa-eb014c4bbe3b');
  const policy1Url = 'https://example.com/policy1.json';
  let mockBlobStorage: IBlobStorageService;
  let storageService: StorageService;
  let ipService: IpServiceMock;
  let telemetryService: MockTelemetryService;
  let service: PolicyService;
  let policyJson: any;
  let policyDetails: PolicyConfig;
  const localGroups = ['Default Users'];
  const mockManagedPolicy = { MaxPolicyDownloadDelay: 0 }; // Disable delay for testing

  beforeEach(() => {
    policyJson = getTestPolicyConfigWithContentAware(tenant);

    // Make sure the policy has the contentAwareAllowList property set
    policyJson.contentAwareAllowList = [
      'example.com',
      'trusted-site.org',
      'school.edu',
      'tenant-specific.com',
      'tenant-trusted.org',
    ];

    mockBlobStorage = new MockBlobStorageService([
      new MockUrlData(policy1Url, JSON.stringify(policyJson)),
    ]);

    storageService = new StorageService('policy', undefined);
    ipService = new IpServiceMock();
    telemetryService = new MockTelemetryService();
  });

  // No need to reset environment variables anymore

  beforeEach(() => {
    service = new PolicyService(
      mockBlobStorage,
      storageService,
      ipService,
      telemetryService,
      'testuser',
      tenant,
      localGroups,
      mockManagedPolicy,
    );

    policyDetails = {
      resource: 'https://example.com',
      name: 'policy1.json',
      sas: '',
    };
  });

  describe('contentAwareAllowList getter', () => {
    it('should return undefined if policy is not loaded', () => {
      expect(service.contentAwareAllowList).toBeUndefined();
    });

    it('should return the Content Aware allow list URLs after policy is loaded', (done) => {
      // Create a policy with contentAwareAllowList
      const policyWithAllowList = {
        ...policyJson,
        contentAwareAllowList: [
          'example.com',
          'trusted-site.org',
          'school.edu',
          'tenant-specific.com',
          'tenant-trusted.org',
        ],
      };

      // Store the policy directly
      storageService.set('policy', policyWithAllowList);

      // Verify that the contentAwareAllowList property exists and contains the expected URLs
      expect(service.contentAwareAllowList).toBeDefined();
      const allowList = service.contentAwareAllowList ?? [];

      // Should include global URLs
      expect(allowList).toContain('example.com');
      expect(allowList).toContain('trusted-site.org');
      expect(allowList).toContain('school.edu');

      // Should also include tenant-specific URLs since we're using a tenant
      expect(allowList).toContain('tenant-specific.com');
      expect(allowList).toContain('tenant-trusted.org');

      done();
    });

    it('should return the Content Aware allow list URLs from cached policy', (done) => {
      // Create a policy with contentAwareAllowList
      const policyWithAllowList = {
        ...policyJson,
        contentAwareAllowList: [
          'example.com',
          'trusted-site.org',
          'school.edu',
          'tenant-specific.com',
          'tenant-trusted.org',
        ],
      };

      // Set up the cached policy
      const url = mergeBlobUrl(policyDetails.resource, policyDetails.name, policyDetails.sas);
      storageService.set('configUrl', url);
      storageService.set('policy', policyWithAllowList);

      // Create a new service that will use the cached policy
      const newService = new PolicyService(
        mockBlobStorage,
        storageService,
        ipService,
        telemetryService,
        'testuser',
        tenant,
        localGroups,
        mockManagedPolicy,
      );

      // Verify that the contentAwareAllowList property exists and contains the expected URLs
      expect(newService.contentAwareAllowList).toBeDefined();
      const allowList = newService.contentAwareAllowList ?? [];

      // Should include global URLs
      expect(allowList).toContain('example.com');
      expect(allowList).toContain('trusted-site.org');
      expect(allowList).toContain('school.edu');

      // Should also include tenant-specific URLs since we're using a tenant
      expect(allowList).toContain('tenant-specific.com');
      expect(allowList).toContain('tenant-trusted.org');

      done();
    });

    it('should return an empty array if contentAwareAllowList is empty in the policy', (done) => {
      // Create a policy with an empty contentAwareAllowList
      const policyWithEmptyAllowList = {
        ...policyJson,
        contentAwareAllowList: [],
      };

      // Store the policy directly
      storageService.set('policy', policyWithEmptyAllowList);

      // Verify that the contentAwareAllowList property exists but is empty
      expect(service.contentAwareAllowList).toBeDefined();
      const allowList = service.contentAwareAllowList ?? [];
      expect(allowList).toBeArrayOfSize(0);

      done();
    });
  });

  describe('PolicyService integration with PolicyDownloadService', () => {
    it('should receive and expose the Content Aware allow list after downloading policy', (done) => {
      // Clear any cached policy to ensure a fresh download
      storageService.set('policy', undefined);
      storageService.set('configUrl', undefined);

      // Create a modified policy JSON with contentAwareAllowList
      const modifiedPolicyJson = {
        ...policyJson,
        contentAwareAllowList: [
          'example.com',
          'trusted-site.org',
          'school.edu',
          'tenant-specific.com',
          'tenant-trusted.org',
        ],
      };

      // Create a new mock blob storage with the modified policy
      const modifiedBlobStorage = new MockBlobStorageService([
        new MockUrlData(policy1Url, JSON.stringify(modifiedPolicyJson)),
      ]);

      // Create a new service with the modified blob storage
      const testService = new PolicyService(
        modifiedBlobStorage,
        storageService,
        ipService,
        telemetryService,
        'testuser',
        tenant,
        localGroups,
        mockManagedPolicy,
      );

      // Set up the service to download the policy
      testService.onPolicyLoaded.addListener(() => {
        try {
          // Manually set the policy in the service to simulate download completion
          storageService.set('policy', modifiedPolicyJson);

          // Verify that the contentAwareAllowList property exists and contains the expected URLs
          expect(testService.contentAwareAllowList).toBeDefined();

          const allowList = testService.contentAwareAllowList ?? [];

          // Should include global URLs
          expect(allowList).toContain('example.com');
          expect(allowList).toContain('trusted-site.org');
          expect(allowList).toContain('school.edu');

          // Should also include tenant-specific URLs since we're using a tenant
          expect(allowList).toContain('tenant-specific.com');
          expect(allowList).toContain('tenant-trusted.org');

          done();
        } catch (error) {
          done(error);
        }
      });

      // Start the policy download
      void testService.start(policyDetails);
    });

    it('should not download policy again if URL is the same and policy is cached', (done) => {
      // First, set up a cached policy
      const url = mergeBlobUrl(policyDetails.resource, policyDetails.name, policyDetails.sas);
      const policyWithAllowList = {
        ...policyJson,
        contentAwareAllowList: [
          'example.com',
          'trusted-site.org',
          'school.edu',
          'tenant-specific.com',
          'tenant-trusted.org',
        ],
      };

      storageService.set('configUrl', url);
      storageService.set('policy', policyWithAllowList);

      // Create a spy on the BlobStorageService start method
      const startSpy = jest.spyOn(mockBlobStorage, 'start');

      // Set up the service to download the policy
      service.onPolicyLoaded.addListener(() => {
        try {
          // Verify that the contentAwareAllowList property exists and contains the expected URLs
          expect(service.contentAwareAllowList).toBeDefined();
          const allowList = service.contentAwareAllowList ?? [];

          // Should include all URLs from the cached policy
          expect(allowList).toContain('example.com');
          expect(allowList).toContain('trusted-site.org');
          expect(allowList).toContain('school.edu');
          expect(allowList).toContain('tenant-specific.com');
          expect(allowList).toContain('tenant-trusted.org');

          // Verify that the BlobStorageService start method was not called
          expect(startSpy).not.toHaveBeenCalled();

          done();
        } catch (error) {
          done(error);
        } finally {
          startSpy.mockRestore();
        }
      });

      // Start the policy download with the same URL
      void service.start(policyDetails);
    });

    it('should download policy again if URL is different even if policy is cached', (done) => {
      // First, set up a cached policy
      const url = mergeBlobUrl(policyDetails.resource, policyDetails.name, policyDetails.sas);
      const policyWithAllowList = {
        ...policyJson,
        contentAwareAllowList: [
          'example.com',
          'trusted-site.org',
          'school.edu',
          'tenant-specific.com',
          'tenant-trusted.org',
        ],
      };

      storageService.set('configUrl', url);
      storageService.set('policy', policyWithAllowList);

      // Create a spy on the BlobStorageService start method
      const startSpy = jest.spyOn(mockBlobStorage, 'start');

      // Create a different policy URL
      const differentPolicyDetails = {
        resource: 'https://example.com',
        name: 'different-policy.json',
        sas: '',
      };

      // Set up the service to download the policy
      service.onPolicyLoaded.addListener(() => {
        try {
          // Verify that the BlobStorageService start method was called with the new URL
          expect(startSpy).toHaveBeenCalled();
          const differentUrl = mergeBlobUrl(
            differentPolicyDetails.resource,
            differentPolicyDetails.name,
            differentPolicyDetails.sas,
          );
          expect(startSpy).toHaveBeenCalledWith(differentUrl, expect.anything());

          done();
        } catch (error) {
          done(error);
        } finally {
          startSpy.mockRestore();
        }
      });

      // Start the policy download with a different URL
      void service.start(differentPolicyDetails);
    });

    it('should handle a different tenant ID than the one in the policy', (done) => {
      // Clear any cached policy to ensure a fresh download
      storageService.set('policy', undefined);
      storageService.set('configUrl', undefined);

      // Create a different tenant ID
      const differentTenant = new TenantId('0bc510e6-f99d-11ea-88fe-cc024c4bbe3b');

      // Create a modified policy JSON with contentAwareAllowList
      const modifiedPolicyJson = {
        ...policyJson,
        contentAwareAllowList: [
          'example.com',
          'trusted-site.org',
          'school.edu',
          'tenant-specific.com',
          'tenant-trusted.org',
        ],
      };

      // Create a new mock blob storage with the modified policy
      const modifiedBlobStorage = new MockBlobStorageService([
        new MockUrlData(policy1Url, JSON.stringify(modifiedPolicyJson)),
      ]);

      // Create a service with a different tenant
      const differentTenantService = new PolicyService(
        modifiedBlobStorage,
        storageService,
        ipService,
        telemetryService,
        'testuser',
        differentTenant,
        localGroups,
        mockManagedPolicy,
      );

      // Set up the service to download the policy
      differentTenantService.onPolicyLoaded.addListener(() => {
        try {
          // Manually set the policy in the service to simulate download completion
          // For a different tenant, we need to filter out the tenant-specific URLs from the original tenant
          const filteredPolicyJson = {
            ...modifiedPolicyJson,
            contentAwareAllowList: [
              'example.com',
              'trusted-site.org',
              'school.edu',
              // Tenant-specific URLs are excluded
            ],
          };
          storageService.set('policy', filteredPolicyJson);

          // Verify that the contentAwareAllowList property exists and contains the expected URLs
          expect(differentTenantService.contentAwareAllowList).toBeDefined();
          const allowList = differentTenantService.contentAwareAllowList ?? [];

          // Should include global URLs
          expect(allowList).toContain('example.com');
          expect(allowList).toContain('trusted-site.org');
          expect(allowList).toContain('school.edu');

          // Should NOT include tenant-specific URLs from the original tenant
          expect(allowList).not.toContain('tenant-specific.com');
          expect(allowList).not.toContain('tenant-trusted.org');

          done();
        } catch (error) {
          done(error);
        }
      });

      // Start the policy download
      void differentTenantService.start(policyDetails);
    });
  });
});

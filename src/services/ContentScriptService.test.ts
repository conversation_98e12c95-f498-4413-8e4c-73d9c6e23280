/**
 * @jest-environment jsdom
 */

/* eslint-disable */
/* cspell:ignore prerendered prerendering prerenderingchange onprerenderingchange */
import YoutubeBlockedIdsMessage from 'content-script-messages/YoutubeBlockedIdsMessage';
import type { Listener } from 'utilities/StandaloneEvent';
import * as PrerenderDetection from '../utilities/ContentScriptPrerenderDetection';

import ContentScriptService from './ContentScriptService';

// Mock the prerender detection utilities
jest.mock('../utilities/ContentScriptPrerenderDetection');

// Properly mock the Chrome API before any tests run
beforeAll(() => {
  // Enable fake timers
  jest.useFakeTimers();

  // Create detailed mock of chrome API
  global.chrome = {
    runtime: {
      id: 'mock-extension-id',
      getURL: jest.fn().mockImplementation((path) => {
        return `chrome-extension://mock-extension-id/${path}`;
      }),
      onMessage: {
        addListener: jest.fn(),
        removeListener: jest.fn(),
      },
      sendMessage: jest.fn().mockImplementation((_, __, callback) => {
        if (callback) callback({});
      }),
    },
    tabs: {
      onUpdated: {
        addListener: jest.fn(),
        removeListener: jest.fn(),
      },
      onRemoved: {
        addListener: jest.fn(),
        removeListener: jest.fn(),
      },
    },
    storage: {
      managed: {
        get: jest.fn().mockImplementation((_, callback) => {
          if (callback) callback({});
        }),
      },
    },
  } as unknown as typeof chrome;

  // Create a proper MutationObserver mock
  class MockMutationObserver implements MutationObserver {
    private readonly callback: MutationCallback;

    constructor(callback: MutationCallback) {
      this.callback = callback;
    }

    observe = jest.fn((_target: Node, _options?: MutationObserverInit) => {});
    disconnect = jest.fn(() => {});
    takeRecords = jest.fn(() => [] as MutationRecord[]);

    // Helper for tests to trigger the callback
    triggerMutations(mutations: MutationRecord[]) {
      this.callback(mutations, this);
    }
  }

  // Replace the global MutationObserver with our mock
  global.MutationObserver = MockMutationObserver as unknown as typeof MutationObserver;

  // Create a mock StandaloneEvent class
  const createMockStandaloneEvent = () => {
    const listeners = new Set<Listener<any>>();
    const mock = {
      addListener: jest.fn((listener: Listener<any>) => {
        listeners.add(listener);
      }),
      removeListener: jest.fn((listener: Listener<any>) => {
        listeners.delete(listener);
      }),
      clearListeners: jest.fn(() => {
        listeners.clear();
      }),
      dispatch: jest.fn((...args: any[]) => {
        listeners.forEach(async (listener) => {
          await listener(...args);
        });
      }),
      deferDispatch: jest.fn((...args: any[]) => {
        setTimeout(() => {
          mock.dispatch(...args);
        }, 0);
      }),
      deferDispatchAndWait: jest.fn(async (...args: any[]) => {
        await new Promise<void>((resolve) => {
          setTimeout(() => {
            mock.dispatch(...args);
            resolve();
          }, 0);
        });
      }),
      get length() {
        return listeners.size;
      },
    };
    return mock;
  };

  // Mock StandaloneEvent
  jest.mock('utilities/StandaloneEvent', () => {
    return jest.fn().mockImplementation(() => createMockStandaloneEvent());
  });
});

// Clean up after tests
afterAll(() => {
  jest.useRealTimers();
  jest.restoreAllMocks();
});

describe('ContentScriptService', () => {
  let contentScriptService: ContentScriptService;
  let mockDocument: Document;
  let mockWindow: Window & typeof globalThis;
  let originalDocument: Document;
  let originalWindow: Window & typeof globalThis;
  let lastMutationObserver: any; // Will store the last created MutationObserver

  beforeEach(() => {
    // Reset timers and mocks before each test
    jest.clearAllTimers();
    jest.clearAllMocks();

    // Save original globals
    originalDocument = global.document;
    originalWindow = global.window;

    // Mock document and window
    mockDocument = {
      readyState: 'complete',
      onload: null,
      querySelector: jest.fn(),
      querySelectorAll: jest.fn().mockReturnValue([]),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      body: {
        appendChild: jest.fn(),
      },
      createElement: jest.fn().mockReturnValue({
        style: {},
      }),
      getElementById: jest.fn(),
      dispatchEvent: jest.fn(),
      evaluate: jest.fn(),
    } as unknown as Document;

    // Create mock localStorage with Jest mock functions
    const mockLocalStorage = {
      getItem: jest.fn(),
      setItem: jest.fn(),
      removeItem: jest.fn(),
      clear: jest.fn(),
      key: jest.fn(),
      length: 0,
    };

    mockWindow = {
      location: {
        href: 'https://www.youtube.com/watch?v=123456',
        hostname: 'youtube.com',
        origin: 'https://www.youtube.com',
      },
      requestAnimationFrame: jest.fn().mockImplementation((callback) => {
        callback();
        return 1;
      }),
      addEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
      localStorage: mockLocalStorage,
    } as unknown as Window & typeof globalThis;

    // Replace globals with mocks
    global.document = mockDocument;
    global.window = mockWindow;

    // Save a reference to the original constructor
    const originalMutationObserver = global.MutationObserver;

    // Override MutationObserver to capture the last instance
    global.MutationObserver = function (callback: MutationCallback) {
      const instance = new originalMutationObserver(callback);
      lastMutationObserver = instance;
      return instance;
    } as unknown as typeof MutationObserver;

    // Create a new instance of the service
    contentScriptService = new ContentScriptService();
  });

  afterEach(() => {
    // Restore original globals
    global.document = originalDocument;
    global.window = originalWindow;

    jest.resetAllMocks();
  });

  describe('_handleYoutube', () => {
    // Helper to trigger mutations on the last created observer
    const triggerMutations = (mutations: MutationRecord[]) => {
      if (lastMutationObserver && typeof lastMutationObserver.triggerMutations === 'function') {
        lastMutationObserver.triggerMutations(mutations);
      }
    };

    it('should set up mutation observer for YouTube', () => {
      // Call the private method using type assertion
      (contentScriptService as any)._handleYoutube();

      // Check that observe was called with document and correct options
      expect(lastMutationObserver.observe).toHaveBeenCalledWith(document, {
        childList: true,
        subtree: true,
      });
    });

    it('should no longer override anchor element click handlers', () => {
      // Create a mock anchor with an existing click handler
      const mockAnchor = document.createElement('a');
      const originalClickHandler = jest.fn();
      mockAnchor.onclick = originalClickHandler;

      // Create a mock for document.querySelectorAll to return our test anchor
      (mockDocument.querySelectorAll as jest.Mock).mockReturnValue([mockAnchor]);

      // Call the YouTube handler
      (contentScriptService as any)._handleYoutube();

      // Create mock mutation records
      const mockMutationRecords = [
        {
          type: 'childList',
          addedNodes: [mockAnchor],
        },
      ] as unknown as MutationRecord[];

      // Trigger the mutation observer callback
      triggerMutations(mockMutationRecords);

      // The anchor's original click handler should be preserved
      expect(mockAnchor.onclick).toBe(originalClickHandler);
    });

    it('should set up event listener for YouTube navigation without forcing page reload', () => {
      // This test verifies that the YouTube navigation handler is set up correctly
      // without forcing page reloads (which was a previous behavior)

      // Call the private method
      (contentScriptService as any)._handleYoutube();

      // Verify the method completes without errors
      // The implementation details of event listeners are tested in other tests
      expect((contentScriptService as any)._handleYoutube).not.toThrow();
    });

    it('should properly process added YouTube anchor elements', () => {
      // Initialize the URL cache and batch queue
      (contentScriptService as any)._processedUrlsCache = new Map();
      (contentScriptService as any)._urlBatchQueue = [];
      (contentScriptService as any)._batchProcessingTimer = null;

      // Mock the mightContainVideoId method to return true for our test URL
      (contentScriptService as any).mightContainVideoId = jest.fn().mockReturnValue(true);

      // Call the YouTube handler
      (contentScriptService as any)._handleYoutube();
      (contentScriptService as any)._isHandlingYoutube = true;
      (contentScriptService as any)._canSendPageContent = true;

      // Create a mock anchor element that would be added to the page
      const mockAnchor = {
        nodeType: Node.ELEMENT_NODE,
        nodeName: 'A',
        href: 'https://www.youtube.com/watch?v=new-video',
        outerHTML: '<a href="https://www.youtube.com/watch?v=new-video">New Video</a>',
        onclick: undefined,
        matches: jest.fn().mockReturnValue(false),
        closest: jest.fn().mockReturnValue(null),
        querySelectorAll: jest.fn().mockReturnValue([]),
      };

      // Create mock mutation records
      const mockMutationRecords = [
        {
          type: 'childList',
          addedNodes: [mockAnchor],
        },
      ] as unknown as MutationRecord[];

      // Manually call the mutation callback
      const mutationCallback = (contentScriptService as any).mutationCallback;
      mutationCallback(mockMutationRecords);

      // Run any pending timers
      jest.runAllTimers();

      // Should not add onclick handler (that's what we fixed)
      expect(mockAnchor.onclick).toBeUndefined();
    });

    it('should dispatch event when blocked IDs are received', () => {
      const dispatchEventSpy = jest.spyOn(window, 'dispatchEvent');

      // Create instance of ContentScriptService
      (contentScriptService as any)._handleYoutube();

      // Create a message with blocked IDs
      // cSpell:disable-next-line
      const blockedIdsMessage = new YoutubeBlockedIdsMessage(['dQw4w9WgXcQ', 'jNQXAC9IVRw']);

      // Process the message
      const result = contentScriptService.handleMessageFromExtension(blockedIdsMessage);

      // Run any pending timers
      jest.runAllTimers();

      // Should return true indicating message was handled
      expect(result).toBe(true);

      // Should dispatch event to trigger DOM modification
      expect(dispatchEventSpy).toHaveBeenCalled();
    });

    it('should preserve YouTube menu item click handlers', () => {
      // Set up YouTube element names
      (contentScriptService as any)._youtubeElementNames = [
        'ytd-rich-item-renderer',
        'ytd-compact-video-renderer',
      ];

      // Create a mock menu item with a click handler (simulating Restricted Mode menu item)
      const mockMenuItemAnchor = {
        nodeName: 'A',
        href: '#', // No video ID in the href
        role: 'menuitem',
        'aria-label': 'Restricted Mode',
        onclick: jest.fn(),
        parentElement: {} as any,
        remove: jest.fn(),
      };

      // Create a mock menu container
      const mockMenuContainer = {
        nodeName: 'DIV',
        role: 'menu',
        children: [mockMenuItemAnchor],
        remove: jest.fn(),
      };

      // Set parent reference
      mockMenuItemAnchor.parentElement = mockMenuContainer as any;

      // Mock querySelectorAll to return empty for blocked IDs
      const mockQuerySelectorAll = jest.fn().mockReturnValue([]);
      document.querySelectorAll = mockQuerySelectorAll;

      // Call the YouTube handler
      (contentScriptService as any)._handleYoutube();
      (contentScriptService as any)._isHandlingYoutube = true;

      // Update the content handler with blocked IDs
      if ((contentScriptService as any)._youtubeContentHandler) {
        (contentScriptService as any)._youtubeContentHandler.updateBlockedIds(['dQw4w9WgXcQ']);
      }

      (contentScriptService as any)._canHandleYoutubeEvents = true;

      // Trigger the removeMaliciousYoutubeIdElements method
      (contentScriptService as any)._removeMaliciousYoutubeIdElements();

      // Run any pending timers
      jest.runAllTimers();

      // The menu item's click handler should not be removed
      expect(mockMenuItemAnchor.remove).not.toHaveBeenCalled();

      // The menu container should not be removed
      expect(mockMenuContainer.remove).not.toHaveBeenCalled();
    });

    it('should remove blocked video elements but preserve UI elements', () => {
      // Set up YouTube element names
      (contentScriptService as any)._youtubeElementNames = [
        'ytd-rich-item-renderer',
        'ytd-compact-video-renderer',
      ];

      // Create a mock video anchor with a blocked ID
      const mockVideoAnchor = {
        nodeName: 'A',
        href: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        parentElement: {} as any,
        remove: jest.fn(),
        matches: jest.fn().mockReturnValue(false),
        closest: jest.fn().mockReturnValue(null),
      };

      // Create a mock video element that contains the anchor
      const mockVideoElement = {
        nodeName: 'YTD-RICH-ITEM-RENDERER',
        children: [mockVideoAnchor],
        parentElement: {} as any,
        remove: jest.fn(),
        matches: jest.fn().mockReturnValue(false),
        closest: jest.fn().mockReturnValue(null),
      };

      // Set parent references
      mockVideoAnchor.parentElement = mockVideoElement as any;

      // Create a mock UI element (simulating Restricted Mode dialog)
      const mockUIAnchor = {
        nodeName: 'A',
        href: '#', // No video ID in the href
        onclick: jest.fn(),
        parentElement: {} as any,
        remove: jest.fn(),
        matches: jest.fn().mockReturnValue(false),
        closest: jest.fn().mockReturnValue(null),
      };

      const mockUIElement = {
        nodeName: 'DIV',
        role: 'dialog',
        'aria-label': 'Restricted Mode',
        children: [mockUIAnchor],
        parentElement: {} as any,
        remove: jest.fn(),
        matches: jest.fn().mockReturnValue(true),
        closest: jest.fn().mockReturnValue(null),
      };

      // Set parent references
      mockUIAnchor.parentElement = mockUIElement as any;

      // Mock querySelectorAll to return our video anchor when searching for the blocked ID
      const mockQuerySelectorAll = jest.fn().mockImplementation((selector: string) => {
        if (selector.includes('dQw4w9WgXcQ')) {
          return [mockVideoAnchor];
        }
        return [];
      });
      document.querySelectorAll = mockQuerySelectorAll;

      // Call the YouTube handler
      (contentScriptService as any)._handleYoutube();
      (contentScriptService as any)._isHandlingYoutube = true;

      // Update the content handler with blocked IDs
      if ((contentScriptService as any)._youtubeContentHandler) {
        (contentScriptService as any)._youtubeContentHandler.updateBlockedIds(['dQw4w9WgXcQ']);
      }

      (contentScriptService as any)._canHandleYoutubeEvents = true;

      // Trigger the removeMaliciousYoutubeIdElements method
      (contentScriptService as any)._removeMaliciousYoutubeIdElements();

      // Run any pending timers
      jest.runAllTimers();

      // The video element should be removed
      expect(mockVideoElement.remove).toHaveBeenCalled();

      // The UI element should not be removed
      expect(mockUIElement.remove).not.toHaveBeenCalled();

      // The UI anchor's click handler should be preserved
      expect(mockUIAnchor.onclick).toBeDefined();
    });

    it('should specifically handle the Restricted Mode dialog correctly', () => {
      // Set up YouTube element names
      (contentScriptService as any)._youtubeElementNames = [
        'ytd-rich-item-renderer',
        'ytd-compact-video-renderer',
      ];

      // Create a mock toggle button inside the dialog
      const mockToggleButton = {
        nodeName: 'PAPER-TOGGLE-BUTTON',
        'aria-label': 'Activate Restricted Mode',
        onclick: jest.fn(),
        parentElement: {} as any,
        remove: jest.fn(),
        matches: jest.fn().mockReturnValue(false),
        closest: jest.fn().mockReturnValue(null),
      };

      // Create a mock back button
      const mockBackButton = {
        nodeName: 'A',
        href: '#',
        'aria-label': 'Back',
        onclick: jest.fn(),
        parentElement: {} as any,
        remove: jest.fn(),
        matches: jest.fn().mockReturnValue(false),
        closest: jest.fn().mockReturnValue(null),
      };

      // Create a mock Restricted Mode dialog
      const mockRestrictedModeDialog = {
        nodeName: 'YTD-POPUP-CONTAINER',
        role: 'dialog',
        className: 'style-scope ytd-app',
        children: [mockToggleButton, mockBackButton],
        parentElement: {} as any,
        remove: jest.fn(),
        matches: jest.fn().mockReturnValue(true),
        closest: jest.fn().mockReturnValue(null),
      };

      // Set parent references
      mockToggleButton.parentElement = mockRestrictedModeDialog as any;
      mockBackButton.parentElement = mockRestrictedModeDialog as any;

      // Mock querySelectorAll to return empty for blocked IDs
      const mockQuerySelectorAll = jest.fn().mockReturnValue([]);
      document.querySelectorAll = mockQuerySelectorAll;

      // Call the YouTube handler
      (contentScriptService as any)._handleYoutube();
      (contentScriptService as any)._isHandlingYoutube = true;

      // Update the content handler with blocked IDs
      if ((contentScriptService as any)._youtubeContentHandler) {
        (contentScriptService as any)._youtubeContentHandler.updateBlockedIds(['dQw4w9WgXcQ']);
      }

      (contentScriptService as any)._canHandleYoutubeEvents = true;

      // Trigger the removeMaliciousYoutubeIdElements method
      (contentScriptService as any)._removeMaliciousYoutubeIdElements();

      // Run any pending timers
      jest.runAllTimers();

      // The dialog should not be removed
      expect(mockRestrictedModeDialog.remove).not.toHaveBeenCalled();

      // The toggle button's click handler should be preserved
      expect(mockToggleButton.onclick).toBeDefined();

      // The back button's click handler should be preserved
      expect(mockBackButton.onclick).toBeDefined();
    });

    it('should respect protected selectors when removing elements', () => {
      // Set up YouTube element names and protected selectors
      (contentScriptService as any)._youtubeElementNames = [
        'ytd-rich-item-renderer',
        'ytd-compact-video-renderer',
      ];
      (contentScriptService as any)._youtubeProtectedSelectors = [
        '[role="dialog"][aria-label*="Restricted Mode"]',
        'a[href="#"][role="menuitem"][aria-label*="Restricted Mode"]',
      ];

      // Create a mock anchor with a blocked video ID but inside a protected element
      const mockAnchor = {
        nodeName: 'A',
        href: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        matches: jest.fn().mockImplementation((selector) => selector.includes('Restricted Mode')),
        closest: jest
          .fn()
          .mockImplementation((selector) => (selector.includes('Restricted Mode') ? {} : null)),
        parentElement: {} as any,
        remove: jest.fn(),
      };

      // Mock querySelectorAll to return our mock anchor when searching for the blocked ID
      const mockQuerySelectorAll = jest.fn().mockImplementation((selector) => {
        if (selector.includes('dQw4w9WgXcQ')) {
          return [mockAnchor];
        }
        return [];
      });
      document.querySelectorAll = mockQuerySelectorAll;

      // Mock console.debug to track calls
      const originalConsoleDebug = console.debug;
      console.debug = jest.fn();

      // Call the YouTube handler
      (contentScriptService as any)._handleYoutube();
      (contentScriptService as any)._isHandlingYoutube = true;

      // Update the content handler with blocked IDs
      if ((contentScriptService as any)._youtubeContentHandler) {
        (contentScriptService as any)._youtubeContentHandler.updateBlockedIds(['dQw4w9WgXcQ']);
      }

      (contentScriptService as any)._canHandleYoutubeEvents = true;

      // Trigger the removeMaliciousYoutubeIdElements method
      (contentScriptService as any)._removeMaliciousYoutubeIdElements();

      // Run any pending timers
      jest.runAllTimers();

      // The anchor should not be removed because it's protected
      expect(mockAnchor.remove).not.toHaveBeenCalled();

      // Should log that it's skipping the protected element
      expect(console.debug).toHaveBeenCalledWith(
        'Skipping protected YouTube UI element:',
        expect.anything(),
      );

      // Restore console.debug
      console.debug = originalConsoleDebug;
    });

    it('should skip protected elements in mutation observer', () => {
      // Set up YouTube protected selectors
      (contentScriptService as any)._youtubeProtectedSelectors = [
        '[role="dialog"][aria-label*="Restricted Mode"]',
        'a[href="#"][role="menuitem"][aria-label*="Restricted Mode"]',
      ];

      // Initialize the URL cache and batch queue
      (contentScriptService as any)._processedUrlsCache = new Map();
      (contentScriptService as any)._urlBatchQueue = [];
      (contentScriptService as any)._batchProcessingTimer = null;
      (contentScriptService as any).mightContainVideoId = jest.fn().mockReturnValue(true);

      // Create a mock protected element
      const mockProtectedElement = {
        nodeType: Node.ELEMENT_NODE,
        nodeName: 'DIV',
        matches: jest.fn().mockImplementation((selector) => selector.includes('Restricted Mode')),
        closest: jest.fn().mockReturnValue(null),
        outerHTML: '<div role="dialog" aria-label="Restricted Mode"></div>',
        querySelectorAll: jest.fn().mockReturnValue([]),
      };

      // Create a mock mutation record
      const mockMutation = {
        type: 'childList',
        addedNodes: [mockProtectedElement],
      };

      // Call the YouTube handler to set up the mutation observer
      (contentScriptService as any)._handleYoutube();
      (contentScriptService as any)._isHandlingYoutube = true;

      // Manually call the mutation callback with our mock mutation
      const mutationCallback = (contentScriptService as any).mutationCallback;
      if (mutationCallback) {
        // Mock the isProtected function to return true for our element
        const originalIsProtected = (contentScriptService as any).isProtected;
        (contentScriptService as any).isProtected = jest.fn().mockReturnValue(true);

        mutationCallback([mockMutation]);

        // Verify the element was not added to the processing set
        // This is an indirect way to verify it was skipped
        expect((contentScriptService as any)._urlBatchQueue.length).toBe(0);

        // Restore the original function
        (contentScriptService as any).isProtected = originalIsProtected;
      }
    });
  });

  describe('prerender handling', () => {
    let mockIsDocumentPrerendered: jest.MockedFunction<typeof PrerenderDetection.isDocumentPrerendered>;
    let mockOnPrerenderActivation: jest.MockedFunction<typeof PrerenderDetection.onPrerenderActivation>;

    beforeEach(() => {
      // Set up mock implementations
      mockIsDocumentPrerendered = PrerenderDetection.isDocumentPrerendered as jest.MockedFunction<typeof PrerenderDetection.isDocumentPrerendered>;
      mockOnPrerenderActivation = PrerenderDetection.onPrerenderActivation as jest.MockedFunction<typeof PrerenderDetection.onPrerenderActivation>;
    });

    it('should defer execution when document is prerendered', () => {
      // Arrange
      mockIsDocumentPrerendered.mockReturnValue(true);
      const cleanupFn = jest.fn();
      mockOnPrerenderActivation.mockReturnValue(cleanupFn);

      // Spy on chrome.storage.managed.get to verify it's not called
      const storageSpy = jest.spyOn(chrome.storage.managed, 'get');

      // Act
      contentScriptService.start();

      // Assert
      expect(mockIsDocumentPrerendered).toHaveBeenCalled();
      expect(mockOnPrerenderActivation).toHaveBeenCalled();
      expect(storageSpy).not.toHaveBeenCalled(); // Should not execute normal startup
    });

    it('should execute normally when document is not prerendered', () => {
      // Arrange
      mockIsDocumentPrerendered.mockReturnValue(false);

      // Spy on chrome.storage.managed.get to verify it's called
      const storageSpy = jest.spyOn(chrome.storage.managed, 'get');

      // Act
      contentScriptService.start();

      // Assert
      expect(mockIsDocumentPrerendered).toHaveBeenCalled();
      expect(mockOnPrerenderActivation).not.toHaveBeenCalled();
      expect(storageSpy).toHaveBeenCalled(); // Should execute normal startup
    });

    it('should execute normal startup when prerender activates', () => {
      // Arrange
      mockIsDocumentPrerendered.mockReturnValue(true);
      
      // We need to capture the callback that ContentScriptService passes to onPrerenderActivation
      // and handle the circular reference where cleanup is called inside the callback
      let capturedCallback: (() => void) | null = null;
      const cleanupFn = jest.fn();
      
      mockOnPrerenderActivation.mockImplementation((callback) => {
        // Store the callback so we can call it later
        capturedCallback = callback;
        return cleanupFn;
      });

      // Spy on chrome.storage.managed.get
      const storageSpy = jest.spyOn(chrome.storage.managed, 'get');

      // Act - Start in prerender mode
      contentScriptService.start();
      
      // Verify initial state
      expect(storageSpy).not.toHaveBeenCalled();
      
      // Simulate prerender activation
      // The actual implementation calls cleanup inside the callback, 
      // so we need to ensure cleanupFn is callable
      if (capturedCallback) {
        (capturedCallback as () => void)();
      }

      // Assert - Should now execute normal startup
      expect(cleanupFn).toHaveBeenCalled(); // Cleanup should be called
      expect(storageSpy).toHaveBeenCalled(); // Normal startup should execute
    });

    it('should not create document hider during prerender', () => {
      // Arrange
      mockIsDocumentPrerendered.mockReturnValue(true);
      const cleanupFn = jest.fn();
      mockOnPrerenderActivation.mockReturnValue(cleanupFn);

      // Spy on document.createElement to ensure no div is created
      const createElementSpy = jest.spyOn(document, 'createElement');

      // Act
      contentScriptService.start();

      // Assert
      expect(createElementSpy).not.toHaveBeenCalled(); // No document hider should be created
    });

    it('should not send content to extension during prerender', () => {
      // Arrange
      mockIsDocumentPrerendered.mockReturnValue(true);
      const cleanupFn = jest.fn();
      mockOnPrerenderActivation.mockReturnValue(cleanupFn);

      // Spy on chrome.runtime.sendMessage
      const sendMessageSpy = jest.spyOn(chrome.runtime, 'sendMessage');

      // Act
      contentScriptService.start();

      // Assert
      expect(sendMessageSpy).not.toHaveBeenCalled(); // No content should be sent
    });
  });
});

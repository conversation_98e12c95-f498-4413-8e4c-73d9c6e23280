import BackOffMethod from 'back-off-methods/BackOffMethod';
import ConstantBackOff from 'back-off-methods/ConstantBackOff';
import DeferredPromise from 'utilities/DeferredPromise';
import StandaloneEvent from 'utilities/StandaloneEvent';
import { setTimeoutPromise } from 'utilities/TimeoutPromise';

/**
 * Enumerates the actions which can be taken following an API response.
 */
export enum Action {
  /**
   * Do not try again.
   * This may indicate success or permanent failure.
   */
  stop,

  /**
   * Try the request again, if there are any attempts remaining.
   */
  retry,
}

/**
 * Describes a callback which handles a response from the API.
 * This is expected to handle any errors which occur, except for the maximum number of retries being
 *  reached.
 *
 * @param response The response received from the API.
 * @return The handler must return an enumerate value indicating whether to stop or trying again. It
 *  can alternatively return a promise which resolves to the action. If an exception is thrown, or
 *  the promise rejects, then the action is assumed to be retry.
 */
export type ResponseHandler = (response: Response) => Action | Promise<Action>;

/**
 * Describes a callback which is triggered when the max number of failed attempts has been reached.
 * This is called to indicate that it will not be attempted again.
 */
export type MaxAttemptsHandler = () => void | Promise<void>;

/**
 * A wrapper round the fetch() API which adds retry logic.
 * Calling code must provide a callback which handles the response and decides whether to retry.
 * This wrapper will handle the timing of the retry, and will stop if the maximum number of attempts
 *  has been reached.
 */
export default class FetchService {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance with the specified retry configuration.
   *
   * @param backOffMethod The back off method to use for if the requests fail.
   * @param maxAttempts The maximum number of times to retry the request. 0 means an unlimited
   *  number of attempts.
   */
  public constructor(
    backOffMethod: BackOffMethod | undefined,
    maxAttempts: number = 10,
    quietMode: boolean = false,
  ) {
    if (maxAttempts < 0) {
      throw new Error('The maximum number of attempts must be at least 0.');
    }

    this._backOffMethod = backOffMethod ?? new ConstantBackOff(30000, 5000);

    this.maxAttempts = maxAttempts;
    this._quietMode = quietMode;

    // Ensure the completion notifier starts already resolved. This guards against a potential
    //  deadlock in case some code awaits the notifier before calling start(). The notifier is reset
    //  every time start() is called.
    this._completionNotifier.resolve(false);
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Check if there is currently a request or retry in progress.
   *
   * @returns True if there is a request in progress or we're waiting to do a retry.
   */
  public get isRunning(): boolean {
    return this._url !== undefined;
  }

  /**
   * Get the number of attempts remaining in the current run.
   *
   * @return The number of attempts remaining on the current run. Returns 0 if the we're not
   *  currently running, or there are an unlimited number of attempts remaining.
   */
  public get attemptsRemaining(): number {
    return this.isRunning ? this._numAttemptsRemaining : 0;
  }

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Provide a callback which will be triggered to process a response and decide what to do with it.
   * This must have been called before start().
   *
   * @param onResponse A callback function which will process the API response. The return value
   *  must specify whether to stop or retry.
   *
   * @todo Change this to something like a StandaloneEvent, but which allows for a return value?
   */
  public readonly setResponseHandler = (onResponse: ResponseHandler): void => {
    this._onResponse = onResponse;
  };

  /**
   * Begin a request, and a series of retries if necessary.
   * If there is already an attempt in progress then it will be stopped first.
   *
   * @param url The URL to send the request to.
   * @param requestInit Optionally contains the body, method, and headers etc of the request. If
   *  omitted or empty then a simple GET request will be done.
   * @enableFloodPrevention If true, this will delay the initial request if necessary to ensure the
   *  minimum delay between requests is honoured, even after being stopped and started. If false,
   *  the initial request will be sent immediately.
   * @returns A promise which resolves when the request has been configured. This will normally be
   *  immediate. However, if there was a previous request in progress then this will have to wait
   *  for that to be cancelled.
   *
   * @note setResponseHandler() must have been called before this.
   * @note It's important to wait for the returned promise to resolve before doing anything else
   *  with this object. If not, you risk a data race where you might access information from a
   *  previous run by mistake.
   */
  public readonly start = async (
    url: URL,
    requestInit: RequestInit = {},
    enableFloodPrevention: boolean = false,
  ): Promise<void> => {
    if (this._onResponse === undefined) {
      throw new Error('Cannot start a request without a response handler.');
    }

    // Important: Ensure any existing attempt is fully stopped before starting another.
    await this.stop();

    // Sanity check: Ensure another call to start() didn't happen while we were waiting for the
    //  stop() call to finish.
    if (this._url !== undefined) {
      throw new Error('URL already initialised. Possible race condition?');
    }

    this._abortController = new AbortController();
    this._url = url;
    this._requestInit = Object.assign({}, requestInit);
    this._requestInit.signal = this._abortController.signal;
    this._numAttemptsRemaining = this.maxAttempts;
    this._completionNotifier = new DeferredPromise<boolean>();

    // If a request was sent recently as part of a previous run, then optionally ensure we don't
    //  send another request too soon.
    let initialDelay = 0;
    if (enableFloodPrevention) {
      const earliestAllowed = this._lastRequestSentAt + this._backOffMethod.minDelay;
      const now = Date.now();
      if (earliestAllowed > now) {
        initialDelay = earliestAllowed - now;
        console.debug('FetchService delaying initial request to avoid flooding the server.');
      }
    }

    this._scheduleAttempt(initialDelay);
  };

  /**
   * Cancel any attempt to send the request.
   * If you want to attempt it again later, then you will need to call start() again.
   * It is safe to call this multiple times.
   *
   * @returns A promise which resolves when any in-flight request has finished or been cancelled.
   */
  public readonly stop = async (): Promise<void> => {
    this._abortController?.abort();
    await this._currentAttempt;
    this._attemptNumber = 0;

    // If there was an attempt in progress, then it will cleanup all the working data when it's
    //  finished.
  };

  /**
   * Get a promise which resolves when the current run finishes.
   * If no run is in progress then this will return a promise which resolves immediately, giving the
   *  result of the most recent run. If no run has been started yet, it will resolve to false.
   *
   * @returns A promise which resolves when the current finishes. The resolved value will be true if
   *  the run finished due to the maximum number of attempts being reached. It will be false if the
   *  run finished for any other reason.
   *
   * @note This is provided as a convenience, mainly to help with unit tests. Production code should
   *  probably use the onFinished event instead, where possible.
   *
   * @todo Use an enum instead of a boolean to improve readability. This could include 3 states:
   *  stoppedByHandler, cancelled, and maxAttemptsReached.
   */
  public readonly waitUntilFinished = async (): Promise<boolean> => {
    return await this._completionNotifier.promise;
  };

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Set a timer which will send the next request after the delay from the backoff method.
   *
   * @param delayOffset An offset to be added to the delay of the attempt.
   *
   * @warning It is the caller's responsibility to ensure multiple attempts aren't scheduled at the
   *  same time. Only schedule a new attempt if the previous one has finished.
   *
   */
  private readonly _scheduleAttempt = (delayOffset: number = 0): void => {
    if (this._abortController === undefined) {
      throw new Error('Cannot schedule attempt without an abort controller.');
    }

    const delay = this._backOffMethod.generateDelayMs(this._attemptNumber, delayOffset);
    this._attemptNumber++;

    // Each attempt occurs in a separate promise chain. This is deliberate to ensure we don't
    //  exhaust memory with a huge promise chain if there are lots of retries.
    this._currentAttempt = setTimeoutPromise(delay, this._abortController.signal).then(
      this._onTimeoutOrAbort,
    );
  };

  /**
   * This is triggered when the delay between attempts has elapsed or been aborted.
   * The parameter indicates which case it is.
   *
   * @param timeoutElapsed True if the timeout elapsed, meaning we need to send another request.
   *  False if the timeout/request was aborted.
   * @returns A promise which resolves when the current attempt has finished or it has been aborted.
   *  This is never expected to reject.
   */
  private readonly _onTimeoutOrAbort = async (timeoutElapsed: boolean): Promise<void> => {
    try {
      // Only send a request if the timeout elapsed (i.e. it wasn't aborted).
      if (timeoutElapsed && (await this._sendRequest()) === Action.retry) {
        // We need to try again later. Note that we deliberately start a new promise for each
        //  attempt. This is to ensure we don't exhaust memory if there are lots of retries.
        this._scheduleAttempt();
        return;
      }
    } catch (e: any) {
      // We shouldn't get here unless there's a bug.
      console.error('Unexpected error in FetchService:', e);
    }

    // If we reach here then we're not sending any more requests.
    const maxAttemptsReached = this.maxAttempts > 0 && this._numAttemptsRemaining <= 0;
    this.onFinished.dispatch(maxAttemptsReached, this._url ?? new URL(''));
    this._completionNotifier?.resolve(maxAttemptsReached);
    this._cleanup();
  };

  /**
   * Wraps one attempt to send the request and process the response.
   * If necessary, this will schedule a retry or cancel the whole run if there are no attempts left.
   *
   * @returns A promise which resolves when this attempt has finished, whether successfully or not.
   *  It's not expected to reject. Note: If a retry is needed, then the next attempt is not chained
   *  onto this one. It's created as a separate promise chain.
   */
  private readonly _sendRequest = async (): Promise<Action> => {
    if (
      this._abortController === undefined ||
      this._onResponse === undefined ||
      this._url === undefined
    ) {
      throw new Error('FetchService cannot attempt a request as it is not fully initialised.');
    }

    // Do nothing if the operation has been cancelled.
    const abortSignal = this._abortController.signal;
    if (abortSignal.aborted) {
      return Action.stop;
    }

    try {
      this._lastRequestSentAt = Date.now();
      const response = await fetch(this._url.toString(), this._requestInit);

      // It's possible that the operation was aborted after the fetch() finished but before we
      //  resumed after the await.
      if (abortSignal.aborted) {
        return Action.stop;
      }

      // Process the response to decide whether we need to try again.
      const action = await Promise.resolve(this._onResponse(response));
      if (action === Action.stop) {
        return Action.stop;
      }

      if (action !== Action.retry) {
        console.warn(
          'FetchService - Unrecognised action returned by response handler. Expected stop or retry.',
        );
      }

      // We're deliberately not returning the retry action yet.
      // We need to check if we have any attempts left first (see below).
    } catch (e: any) {
      if (e.name === 'AbortError' || abortSignal.aborted) {
        return Action.stop;
      }

      if (this._quietMode) {
        console.debug('FetchService - An error occurred during fetch() or response handler:', e);
      } else {
        console.warn('FetchService - An error occurred during fetch() or response handler:', e);
      }

      // Deliberately keep going -- retry (if possible) when an error occurs.
    }

    // Stop if we've run out of attempts.
    if (this.maxAttempts > 0 && --this._numAttemptsRemaining <= 0) {
      return Action.stop;
    }

    return Action.retry;
  };

  /**
   * Reset the working data used for a request.
   * This should be done after the last attempt has finished.
   */
  private readonly _cleanup = (): void => {
    this._abortController = undefined;
    this._url = undefined;
    this._requestInit = {};
    this._currentAttempt = undefined;
    this._attemptNumber = 0;

    // Deliberately leave _completionNotifier in place. This ensures it can be retrieved at any time
    //  after start() is called, even if the request has already finished.
  };

  // -----------------------------------------------------------------------------------------------
  // Events.

  /**
   * This event will be triggered when we've finished sending requests.
   * The actual response is not provided in this event. That will already have been received in the
   *  response handler when processing any requests.
   *
   * @param {boolean} maxAttemptsReached True if we stopped because the maximum number of request
   *  attempts was reached. False if we stopped because the response handler said to stop, or stop()
   *  was called.
   * @param {URL} url The URL which was being requested.
   *
   * @todo Use an enum instead of a boolean to improve readability. This could include 3 states:
   *  stoppedByHandler, cancelled, and maxAttemptsReached.
   */
  public readonly onFinished = new StandaloneEvent<[boolean, URL]>();

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The maximum number of times to attempt the request.
   * 0 means an unlimited number of attempts.
   * This is set on construction.
   */
  public readonly maxAttempts: number;

  /**
   * The back off method to use.
   */
  private readonly _backOffMethod: BackOffMethod;

  /**
   * The number of attempts remaining in the current run (i.e. since start() was called).
   */
  private _numAttemptsRemaining: number = 0;

  /**
   * A callback which will process the response from the API request.
   * This is set during start().
   * It will be undefined if no attempts are currently in progress.
   */
  private _onResponse?: ResponseHandler;

  /**
   * The URL to request.
   * This is set during start().
   * It will be undefined if there are no attempts in progress.
   */
  private _url?: URL;

  /**
   * Information about the request, such as method, headers, and body.
   * This is optionally set during start().
   * It may be empty if we are doing a simple GET request with no headers.
   * The abort signal will be added in each time we send a request.
   */
  private _requestInit: RequestInit = {};

  /**
   * This is used to cancel an in-flight request if stop() is called.
   * It will be undefined if no attempt is in progress.
   *
   * @warning This controller should not be accessed directly from the code making request attempts
   *  as that could result in a race condition. Instead, they should use the abort signal passed
   *  through from the original attempt scheduled in start().
   */
  private _abortController?: AbortController;

  /**
   * Tracks the current attempt number to use when calculating the delay.
   */
  private _attemptNumber: number = 0;

  /**
   * This promise will resolve when the current request attempt finishes.
   * Note that each retry is one attempt, so this promise will be replaced regularly.
   * This is really only stored to facilitate unit tests.
   */
  private _currentAttempt?: Promise<void>;

  /**
   * This promise will be resolved when the current run of attempts finishes for any reason.
   * It will resolve to true if we stopped becase the maximum number of attempts was reached. It
   *  will resolve to false if the response handler told us to stop, or stop() was called.
   * This promise is never expected to reject.
   *
   * @note This is a dummy promise which only exists to provide a means of awaiting completion of a
   *  run of attempts. It will only be populated if a run is in progress.
   */
  private _completionNotifier = new DeferredPromise<boolean>();

  /**
   * Timestamp (milliseconds) of when we last sent a request.
   * This is used to prevent us from sending requests too frequently, even if stop() and start() are
   *  called in quick succession.
   * This will be 0 if we haven't sent any requests yet.
   */
  private _lastRequestSentAt: number = 0;

  /**
   * When true, toggles all console logs to console.debug.
   */
  private readonly _quietMode: boolean;
}

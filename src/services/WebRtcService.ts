import ipRegex from 'ip-regex';
import StandaloneEvent from 'utilities/StandaloneEvent';

/**
 * Uses WebRTC to query the public IP addresses assigned to this device.
 */
export default class WebRtcService {
  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Try to query the public IP addresses of this device using the given RTC config.
   * If another attempt started very recently then this will await the existing attempt instead of
   *  starting a new one.
   *
   * @param rtcConfig The rtc config to use.
   * @returns A promise that resolves to an array containing the public IP address(es) assigned to
   *  this device. It will resolve to an empty array if no addresses could be found. WebRTC can be
   *  slow to finalise processing all candidates, so this promise can take a while to resolve. You
   *  can usually get the IP addresses much quicker by adding a listener to onPublicIpAddress as
   *  well.
   *
   * @warning This function will only work where the WebRTC API is available, such as in an
   *  offscreen document or a content script. It will not work if called directly within a service
   *  worker.
   *
   * @see onPublicIpAddress
   */
  public readonly getPublicIpAddresses = async (rtcConfig: RTCConfiguration): Promise<string[]> => {
    // If we're already querying the public IP addresses, and that attempt started fairly recently,
    //  then await the results from that attempt. Starting multiple attempts at the same time is
    //  pointless and wastes resources, assuming the RTC config is the same for all attempts.
    const timeSinceLastAttempt = Date.now() - this._publicIpAddressPromiseStartedAt;
    if (this._publicIpAddressPromise !== undefined && timeSinceLastAttempt < 3000) {
      console.debug(
        'WebRtcService - An attempt to query public IP addresses is already in progress. Awaiting that instead of starting a new one.',
      );
      return await this._publicIpAddressPromise;
    }

    // We'll use WebRTC to negotiate a connection with an external server.
    const pc = new RTCPeerConnection(rtcConfig);

    // Try to extract public addresses from any ICE candidates used during the negotiation.
    const ipAddresses = new Set<string>();
    pc.onicecandidate = (event: RTCPeerConnectionIceEvent) => {
      // We're only interested in server reflex candidates which contain a valid IP address.
      if (
        event.candidate?.type === 'srflx' &&
        event.candidate?.address != null &&
        ipRegex({ exact: true }).test(event.candidate?.address)
      ) {
        ipAddresses.add(event.candidate.address);
        this.onPublicIpAddress.dispatch([...ipAddresses]);
      }
    };

    // We can stop when we've seen all available ICE candidates.
    const promise = new Promise<string[]>((resolve) => {
      pc.onicegatheringstatechange = (event: Event) => {
        const state = (event.target as RTCPeerConnection).iceGatheringState;
        if (state === 'complete') {
          pc.onicecandidate = null;
          pc.close();
          resolve([...ipAddresses]);
        }
      };
    });

    // Store a copy of the promise so that any subsequent attempts can await that instead of
    //  starting a new one.
    // IMPORTANT: To avoid race conditions, this must be done before any awaits.
    this._publicIpAddressPromise = promise;
    this._publicIpAddressPromiseStartedAt = Date.now();

    try {
      console.debug('WebRtcService querying public IP addresses...');

      // Start the connection negotiation. This must be done after adding the handlers above.
      pc.createDataChannel('');
      const sessionDescription = await pc.createOffer();
      await pc.setLocalDescription(sessionDescription);

      // Note: We're deliberately awaiting the local copy of this promise, rather than the one
      //  stored as a member variable. This is to ensure we always await the correct promise, even
      //  if the stored one accidentally gets overwritten at the wrong time.
      return await promise;
    } finally {
      // Clear the stored promise so that subsequent attempts don't try to wait on it.
      this._publicIpAddressPromise = undefined;
      console.debug('WebRtcService finished querying public IP addresses.', ipAddresses);
    }
  };

  /**
   * Try to query the private IP addresses of this device. This does not require an rtcConfig.
   * @returns A promise that resolves to an array containing the private IP address(es) assigned to
   *  this device. It will resolve to an empty array if no addresses could be found.
   *
   * @warning This function will only work where the WebRTC API is available, such as in an
   *  offscreen document or a content script. It will not work if called directly within a service
   *  worker.
   *
   * @see onPrivateIpAddress
   */
  public readonly getPrivateIpAddresses = async (): Promise<string[]> => {
    // We'll use WebRTC to negotiate a connection with an external server.
    const pc = new RTCPeerConnection();

    // Try to extract private addresses from any ICE candidates used during the negotiation.
    const ipAddresses = new Set<string>();
    pc.onicecandidate = (event: RTCPeerConnectionIceEvent) => {
      // We're only interested in host candidates which contain a valid IP address.
      if (
        event.candidate?.type === 'host' &&
        event.candidate?.address != null &&
        ipRegex({ exact: true }).test(event.candidate?.address)
      ) {
        ipAddresses.add(event.candidate.address);
      }
    };

    // We can stop when we've seen all available ICE candidates.
    const promise = new Promise<string[]>((resolve) => {
      pc.onicegatheringstatechange = (event: Event) => {
        const state = (event.target as RTCPeerConnection).iceGatheringState;
        if (state === 'complete') {
          pc.onicecandidate = null;
          pc.close();
          this.onPrivateIpAddress.dispatch([...ipAddresses]);
          resolve([...ipAddresses]);
        }
      };
    });

    try {
      console.debug('WebRtcService querying private IP addresses...');

      // Start the connection negotiation. This must be done after adding the handlers above.
      pc.createDataChannel('');
      const sessionDescription = await pc.createOffer();
      await pc.setLocalDescription(sessionDescription);

      return await promise;
    } finally {
      console.debug('WebRtcService finished querying private IP addresses.', ipAddresses);
    }
  };

  // -----------------------------------------------------------------------------------------------
  // Events.

  /**
   * Event which is triggered when we have detected a public IP address.
   * If there are multiple public IP addresses assigned to this device, then this will be triggered
   *  each time a new address is detected.
   * The argument is an array of all the public IP addresses which have been detected so far
   *  in the current run.
   * This will not be triggered if we couldn't detect any public IP addresses for this device.
   *
   * @see getPublicIpAddresses()
   */
  public readonly onPublicIpAddress = new StandaloneEvent<[string[]]>();

  /**
   * Event which is triggered when we have detected a private IP address.
   * @see onPublicIpAddress
   */
  public readonly onPrivateIpAddress = new StandaloneEvent<[string[]]>();

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Millisecond timestamp of when the latest attempt to gather public IP addresses started.
   * This is used to decide whether it's worth starting a new attempt, or whether we should use an
   *  existing one.
   * This is irrelevant if _publicIpAddressPromise is undefined.
   */
  private _publicIpAddressPromiseStartedAt: number = 0;

  /**
   * A promise which resolves when we've finished gathering public IP addresses.
   * If multiple attempts to gather public IP addresses are started in quick succession then they
   *  will all await the existing promise stored here, instead of each starting a new one.
   * If successful, this resolves to an array of all the public IP addresses assigned to this
   *  device.
   * If undefined, it means no attempt is currently in progress.
   *
   * @see _publicIpAddressPromiseStartedAt
   */
  private _publicIpAddressPromise?: Promise<string[]>;
}

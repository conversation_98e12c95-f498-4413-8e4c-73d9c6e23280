import LogSeverityLevel from 'models/LogSeverityLevel';
import MetricTelemetry from 'models/MetricTelemetry';
import PlatformConfig from 'models/PlatformConfig';
import ProvisioningInfo from 'provisioning/ProvisioningInfo';

export default interface ITelemetryService {
  /**
   * Initialises the standard telemetry parameters for telemetry service.
   * @param provisioningInfo The provisioning info for the device.
   * @param platformConfig The platform configuration for the device.
   */
  initStandardParameters: (
    provisioningInfo: ProvisioningInfo,
    platformConfig: PlatformConfig,
  ) => void;

  /**
   * Logs a event to the telemetry provider with the given parameters.
   * @param name The name of the event to log.
   * @param customParameters Any custom parameters to send with the event.
   */
  logEvent: (name: string, customParameters?: any) => void;

  /**
   * Logs the given metric to the telemetry provider.
   * @param name The name of the metric to log.
   */
  logMetric: (metric: MetricTelemetry) => void;

  /**
   * Logs a trace to the telemetry provider.
   * @param name The name of the trace.
   * @param customParameters Any custom parameters to send with the trace.
   */
  logTrace: (name: string, customParameters?: any) => void;

  /**
   * Logs an error to the telemetry provider with the given parameters and severity.
   * @param name The name of the exception.
   * @param exception The error or error message to log.
   * @param customParameters Any custom parameters to log with the message.
   * @param severity The severity of the message.
   */
  logError: (
    name: string,
    exception: Error | string,
    customParameters?: any,
    severity?: LogSeverityLevel,
  ) => void;

  /**
   * Sets the deviceId standard parameter.
   * @param deviceId The device id from the device management api.
   */
  setDeviceId: (deviceId: string) => void;
}

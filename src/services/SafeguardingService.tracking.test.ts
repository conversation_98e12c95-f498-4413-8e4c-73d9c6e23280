import safeguardingRules from 'mini-blocklist-data/safeguardingRules';
import AccessLogEntry from 'models/AccessLogEntry';
import SafeguardingService from 'services/SafeguardingService';
import { isTrackingUrl } from 'utilities/UrlUtilities';

import AccessLogManager from '../access-logs/AccessLogManager';

// Test file specifically for tracking URL filtering functionality
describe('SafeguardingService - Tracking URL Filtering', () => {
  describe('UrlUtilities Integration', () => {
    test('isTrackingUrl utility function works correctly', () => {
      // Test tracking URLs
      expect(isTrackingUrl('https://www.google.com/ccm/collect?test=1')).toBe(true);
      expect(isTrackingUrl('https://rd.doubleclick.net/dts/rtlactivity/fledge#1')).toBe(true);

      // Test legitimate URLs
      expect(isTrackingUrl('https://webchat.samaritans.org/api/service-status/')).toBe(false);
      expect(isTrackingUrl('https://www.samaritans.org/help')).toBe(false);
    });
  });
  let safeguardingService: SafeguardingService;
  let mockStorageService: any;
  let mockAlarmService: any;
  let mockTelemetryService: any;
  let mockAccessLogManager: AccessLogManager;

  beforeEach(() => {
    // Setup mocks
    mockStorageService = {
      get: jest.fn(),
      set: jest.fn(),
      save: jest.fn().mockResolvedValue(undefined),
    };

    mockAlarmService = {
      addListener: jest.fn(),
      create: jest.fn().mockResolvedValue(undefined),
      clear: jest.fn().mockResolvedValue(true),
    };

    mockTelemetryService = {
      logEvent: jest.fn(),
      logError: jest.fn(),
    };

    // Mock AccessLogManager
    mockAccessLogManager = {
      lastUploadedAt: 0,
      getUploadCount: jest.fn((reset: boolean) => (reset ? 0 : 10)),
      populate: jest.fn(async (_storageData: Record<string, any>) => {
        await Promise.resolve();
      }),
      start: jest.fn(async () => {
        await Promise.resolve();
      }),
      stop: jest.fn(() => {}),
      configure: jest.fn(async (_hardwareId: string, _tenantId: any, _licenseInfo: any) => {
        await Promise.resolve();
      }),
      clearConfiguration: jest.fn(() => {}),
      clearLogs: jest.fn(async () => {
        await Promise.resolve();
      }),
      storeAccessLog: jest.fn((_log: any, _skipDeduplication: boolean) => {}),
      flush: jest.fn(async () => {
        await Promise.resolve();
      }),
      upload: jest.fn(async () => {
        await Promise.resolve();
      }),
    } as unknown as AccessLogManager;

    // Create service instance
    safeguardingService = new SafeguardingService(
      mockStorageService,
      mockAlarmService,
      mockTelemetryService,
      mockAccessLogManager,
    );

    // Load the rules and enable safeguarding
    safeguardingService.loadRules(safeguardingRules);
    safeguardingService.setClientSettings('test-resource', 'test-token', true);

    // Mock the _post method to prevent actual HTTP requests
    jest.spyOn(safeguardingService as any, '_post').mockResolvedValue('test-context-id');
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Google Analytics and Tracking URLs', () => {
    const trackingUrls = [
      'https://www.google.com/ccm/collect?en=page_view&dl=https%3A%2F%2Fwww.samaritans.org%2Fhow-we-can-help%2Fif-youre-having-difficult-time%2Fi-want-kill-myself%2F',
      'https://www.google-analytics.com/collect?v=1&tid=UA-123456-1&cid=555&t=pageview&dp=%2Fsuicide-help',
      'https://ssl.google-analytics.com/collect?v=1&tid=UA-123456-1&t=event&ec=suicide&ea=view',
      'https://analytics.google.com/analytics/web/provision/embed?authuser=0',
      'https://googletagmanager.com/gtm.js?id=GTM-XXXXXX',
      'https://stats.g.doubleclick.net/j/collect?t=dc&aip=1&_r=3',
      'https://rd.doubleclick.net/dts/rtlactivity/fledge#1',
      'https://googlesyndication.com/pagead/js/adsbygoogle.js',
      'https://googleadservices.com/pagead/conversion/123456789/',
    ];

    test.each(trackingUrls)(
      'Should not generate safeguarding alerts for tracking URL: %s',
      async (url) => {
        const log: AccessLogEntry = {
          took: 123,
          time: '**********',
          groups: ['test-group'],
          categories: ['Suicide - Danger'],
          contenttype: 'xmlhttprequest',
          url,
          username: 'test-user',
        };

        const result = await safeguardingService.handleSafeguarding(log);

        // Should not have safeguarding level or theme set
        expect(result.safeguardinglevel).toBeUndefined();
        expect(result.safeguardingtheme).toBeUndefined();

        // Should not have called _post (no alert sent)
        expect(mockTelemetryService.logEvent).not.toHaveBeenCalledWith(
          'safeguard-alert',
          expect.any(Object),
        );
      },
    );
  });

  describe('Facebook and Social Media Tracking URLs', () => {
    const socialTrackingUrls = [
      'https://connect.facebook.net/en_US/fbevents.js',
      'https://www.facebook.com/tr?id=123456789&ev=PageView&noscript=1',
      'https://www.facebook.com/plugins/like.php?href=https%3A%2F%2Fexample.com%2Fsuicide-help',
    ];

    test.each(socialTrackingUrls)(
      'Should not generate alerts for social tracking URL: %s',
      async (url) => {
        const log: AccessLogEntry = {
          took: 123,
          time: '**********',
          groups: ['test-group'],
          categories: ['Suicide - Danger'],
          contenttype: 'script',
          url,
          username: 'test-user',
        };

        const result = await safeguardingService.handleSafeguarding(log);

        expect(result.safeguardinglevel).toBeUndefined();
        expect(result.safeguardingtheme).toBeUndefined();
      },
    );
  });

  describe('Third-party Analytics Services', () => {
    const analyticsUrls = [
      'https://api.mixpanel.com/track/?data=eyJldmVudCI6InBhZ2V2aWV3IiwicHJvcGVydGllcyI6eyJ1cmwiOiJodHRwczovL2V4YW1wbGUuY29tL3N1aWNpZGUtaGVscCJ9fQ%3D%3D',
      'https://api.segment.com/v1/track',
      'https://api.amplitude.com/2/httpapi',
      'https://script.hotjar.com/modules.js',
      'https://fullstory.com/s/fs.js',
      'https://cdn.logrocket.io/LogRocket.min.js',
      'https://js-agent.newrelic.com/nr-spa-1234.min.js',
    ];

    test.each(analyticsUrls)(
      'Should not generate alerts for analytics service URL: %s',
      async (url) => {
        const log: AccessLogEntry = {
          took: 123,
          time: '**********',
          groups: ['test-group'],
          categories: ['Suicide - Danger'],
          contenttype: 'script',
          url,
          username: 'test-user',
        };

        const result = await safeguardingService.handleSafeguarding(log);

        expect(result.safeguardinglevel).toBeUndefined();
        expect(result.safeguardingtheme).toBeUndefined();
      },
    );
  });

  describe('URLs with Tracking Patterns', () => {
    const patternUrls = [
      'https://example.com/analytics/track?event=pageview&url=https%3A%2F%2Fsuicide-help.com',
      'https://example.com/collect?utm_source=google&utm_campaign=suicide-prevention',
      'https://example.com/pixel?ref=https%3A%2F%2Fsuicide-help.org',
      'https://example.com/beacon?ga_id=123&url=https%3A%2F%2Fexample.com%2Fkill-myself',
      'https://example.com/events?gclid=abc123&destination=https%3A%2F%2Fsuicide.org',
    ];

    test.each(patternUrls)(
      'Should not generate alerts for URL with tracking patterns: %s',
      async (url) => {
        const log: AccessLogEntry = {
          took: 123,
          time: '**********',
          groups: ['test-group'],
          categories: ['Suicide - Danger'],
          contenttype: 'xmlhttprequest',
          url,
          username: 'test-user',
        };

        const result = await safeguardingService.handleSafeguarding(log);

        expect(result.safeguardinglevel).toBeUndefined();
        expect(result.safeguardingtheme).toBeUndefined();
      },
    );
  });

  describe('Legitimate URLs should still generate alerts', () => {
    const legitimateUrls = [
      'https://www.samaritans.org/how-we-can-help/if-youre-having-difficult-time/i-want-kill-myself/',
      'https://example.com/suicide-help',
      'https://support.example.com/mental-health/suicide-prevention',
      'https://webchat.samaritans.org/api/service-status/', // This should generate an alert as it's a legitimate API
    ];

    test.each(legitimateUrls)('Should generate alerts for legitimate URL: %s', async (url) => {
      const log: AccessLogEntry = {
        took: 123,
        time: '**********',
        groups: ['test-group'],
        categories: ['Suicide - Danger'],
        contenttype: 'main_frame',
        url,
        username: 'test-user',
      };

      const result = await safeguardingService.handleSafeguarding(log);

      // Should have safeguarding level and theme set
      expect(result.safeguardinglevel).toBeDefined();
      expect(result.safeguardingtheme).toBeDefined();
      expect(result.safeguardingtheme).toBe('Suicide');
    });
  });

  describe('Edge cases and error handling', () => {
    test('Should handle invalid URLs gracefully', async () => {
      const log: AccessLogEntry = {
        took: 123,
        time: '**********',
        groups: ['test-group'],
        categories: ['Suicide - Danger'],
        contenttype: 'main_frame',
        url: 'not-a-valid-url',
        username: 'test-user',
      };

      // Should not throw an error
      await expect(safeguardingService.handleSafeguarding(log)).resolves.not.toThrow();

      const result = await safeguardingService.handleSafeguarding(log);
      // Should still process the log normally since URL parsing failed
      expect(result.safeguardinglevel).toBeDefined();
    });

    test('Should handle undefined URL', async () => {
      const log: AccessLogEntry = {
        took: 123,
        time: '**********',
        groups: ['test-group'],
        categories: ['Suicide - Danger'],
        contenttype: 'main_frame',
        username: 'test-user',
      };

      // Should not throw an error
      await expect(safeguardingService.handleSafeguarding(log)).resolves.not.toThrow();

      const result = await safeguardingService.handleSafeguarding(log);
      // Should still process the log normally
      expect(result.safeguardinglevel).toBeDefined();
    });
  });

  describe('QA Feedback Specific URLs', () => {
    // Test the exact URLs mentioned in the QA feedback

    test('Should handle doubleclick tracking URL from QA feedback', async () => {
      const log: AccessLogEntry = {
        took: 123,
        time: '**********',
        groups: ['test-group'],
        categories: ['Tracking User tracking and Site stats,Suicide - Danger'],
        contenttype: 'xmlhttprequest',
        url: 'https://rd.doubleclick.net/dts/rtlactivity/fledge#1',
        username: 'test-user',
      };

      const result = await safeguardingService.handleSafeguarding(log);

      // Should not generate alert for tracking URL
      expect(result.safeguardinglevel).toBeUndefined();
      expect(result.safeguardingtheme).toBeUndefined();
    });

    test('Should generate alert for legitimate API endpoint from QA feedback', async () => {
      const log: AccessLogEntry = {
        took: 123,
        time: '**********',
        groups: ['test-group'],
        categories: ['Suicide - Advisory'],
        contenttype: 'xmlhttprequest',
        url: 'https://webchat.samaritans.org/api/service-status/',
        username: 'test-user',
      };

      const result = await safeguardingService.handleSafeguarding(log);

      // Should generate alert for legitimate API (not a tracking URL)
      expect(result.safeguardinglevel).toBeDefined();
      expect(result.safeguardingtheme).toBeDefined();
      expect(result.safeguardingtheme).toBe('Suicide');
    });

    test('Should handle the original problematic Google collect URL', async () => {
      const log: AccessLogEntry = {
        took: 123,
        time: '**********',
        groups: ['test-group'],
        categories: ['Suicide - Danger'],
        contenttype: 'xmlhttprequest',
        url: 'https://www.google.com/ccm/collect?en=page_view&dl=https%3A%2F%2Fwww.samaritans.org%2Fhow-we-can-help%2Fif-youre-having-difficult-time%2Fi-want-kill-myself%2F',
        username: 'test-user',
      };

      const result = await safeguardingService.handleSafeguarding(log);

      // Should not generate alert for Google tracking URL
      expect(result.safeguardinglevel).toBeUndefined();
      expect(result.safeguardingtheme).toBeUndefined();
    });
  });

  describe('Integration with existing deduplication', () => {
    test('Tracking URL filtering works alongside existing deduplication logic', async () => {
      // First, send a legitimate main_frame alert
      const mainFrameLog: AccessLogEntry = {
        took: 123,
        time: '**********',
        groups: ['test-group'],
        categories: ['Suicide - Danger'],
        contenttype: 'main_frame',
        url: 'https://example.com/suicide-help',
        username: 'test-user',
      };

      const mainFrameResult = await safeguardingService.handleSafeguarding(mainFrameLog);
      expect(mainFrameResult.safeguardinglevel).toBeDefined();

      // Then try to send a tracking URL with similar content
      const trackingLog: AccessLogEntry = {
        took: 123,
        time: '**********',
        groups: ['test-group'],
        categories: ['Suicide - Danger'],
        contenttype: 'xmlhttprequest',
        url: 'https://www.google.com/ccm/collect?dl=https%3A%2F%2Fexample.com%2Fsuicide-help',
        username: 'test-user',
      };

      const trackingResult = await safeguardingService.handleSafeguarding(trackingLog);
      // Should be filtered out by tracking detection, not deduplication
      expect(trackingResult.safeguardinglevel).toBeUndefined();
    });
  });

  describe('Main frame vs sub-page request handling', () => {
    test('Should generate alerts for main_frame requests even if URL matches tracking patterns', async () => {
      // Test a main_frame request with a URL that would normally be filtered as tracking
      const mainFrameTrackingLog: AccessLogEntry = {
        took: 123,
        time: '**********',
        groups: ['test-group'],
        categories: ['Suicide - Danger'],
        contenttype: 'main_frame',
        url: 'https://www.google.com/ccm/collect?dl=https%3A%2F%2Fsuicide-help.example.com',
        username: 'test-user',
      };

      const result = await safeguardingService.handleSafeguarding(mainFrameTrackingLog);

      // Should generate alert despite tracking URL pattern because it's main_frame
      expect(result.safeguardinglevel).toBeDefined();
      expect(result.safeguardingtheme).toBeDefined();
      expect(result.safeguardingtheme?.toLowerCase()).toBe('suicide');
    });

    test('Should filter sub-page requests for tracking URLs', async () => {
      // Test a sub-page request with tracking URL (should be filtered)
      const subPageTrackingLog: AccessLogEntry = {
        took: 123,
        time: '**********',
        groups: ['test-group'],
        categories: ['Suicide - Danger'],
        contenttype: 'xmlhttprequest',
        url: 'https://www.google.com/ccm/collect?dl=https%3A%2F%2Fsuicide-help.example.com',
        username: 'test-user',
      };

      const result = await safeguardingService.handleSafeguarding(subPageTrackingLog);

      // Should be filtered out because it's a sub-page tracking request
      expect(result.safeguardinglevel).toBeUndefined();
      expect(result.safeguardingtheme).toBeUndefined();
    });

    test('Should handle undefined contenttype as main_frame for tracking filtering', async () => {
      // Test with undefined contenttype (should be treated as main_frame)
      const undefinedContentTypeLog: AccessLogEntry = {
        took: 123,
        time: '**********',
        groups: ['test-group'],
        categories: ['Suicide - Danger'],
        // contenttype is undefined
        url: 'https://www.google.com/ccm/collect?dl=https%3A%2F%2Fsuicide-help.example.com',
        username: 'test-user',
      };

      const result = await safeguardingService.handleSafeguarding(undefinedContentTypeLog);

      // Should generate alert because undefined contenttype is treated as main_frame
      expect(result.safeguardinglevel).toBeDefined();
      expect(result.safeguardingtheme).toBeDefined();
    });

    test('Should generate alerts for legitimate main_frame requests', async () => {
      // Test a legitimate main_frame request (non-tracking URL)
      const legitimateMainFrameLog: AccessLogEntry = {
        took: 123,
        time: '**********',
        groups: ['test-group'],
        categories: ['Suicide - Danger'],
        contenttype: 'main_frame',
        url: 'https://suicide-help.example.com/crisis-support',
        username: 'test-user',
      };

      const result = await safeguardingService.handleSafeguarding(legitimateMainFrameLog);

      // Should generate alert for legitimate main_frame request
      expect(result.safeguardinglevel).toBeDefined();
      expect(result.safeguardingtheme).toBeDefined();
      expect(result.safeguardingtheme?.toLowerCase()).toBe('suicide');
    });
  });
});

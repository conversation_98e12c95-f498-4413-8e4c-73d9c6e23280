import DiagnosticsInfo from 'models/DiagnosticsInfo';

/**
 * A service for managing the local diagnostic page connections and messages.
 */
export default class DiagnosticsService {
  /**
   * Register a port that is connected to the diagnostics page.
   * @param port The port to register.
   */
  public readonly registerPort = (port: chrome.runtime.Port): void => {
    if (this._ports.find((p: chrome.runtime.Port) => p.name === port.name) !== undefined) {
      return;
    }

    port.onDisconnect.addListener(this.unregisterPort);

    this._ports.push(port);
  };

  /**
   * Removes a port from the list of registered ports.
   */
  public readonly unregisterPort = (port: chrome.runtime.Port): void => {
    this._ports = this._ports.filter((p) => p.name !== port.name);
  };

  /**
   * Broadcasts the given message to any open diagonistic page ports.
   * @param message The diagnostics info message to broadcast.
   */
  public readonly broadcastMessage = (message: DiagnosticsInfo): void => {
    this._ports.forEach((port) => {
      port.postMessage(message.toLocal);
    });
  };

  /**
   * A list of ports that are connected to a diagnostics page.
   *
   * There should be one port connection for each instance of an open diagnostics page.
   */
  private _ports: chrome.runtime.Port[] = [];
}

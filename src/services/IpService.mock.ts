import IpService from './IpService';
import OffscreenDocumentController from 'controllers/OffscreenDocumentController';
import StorageService from './StorageService';

/**
 * A mock of the IpService class, used for unit tests.
 * This provides helper functions to set the IP addresses manually. Any member functions which rely
 *  on outside data or resources are replaced with spies.
 */
export default class IpServiceMock extends IpService {
  // -----------------------------------------------------------------------------------------------
  // Mock helpers.

  /**
   * Store the specified private IP addresses in this mock object.
   * The other original functions should operate using the specified addresses.
   */
  public readonly setPrivateIpAddresses = (addresses: string[]): void => {
    this._storePrivateIpAddresses(addresses);
  };

  /**
   * Store the specified public IP addresses in this mock object.
   * The other original functions should operate using the specified addresses.
   */
  public readonly setPublicIpAddresses = (addresses: string[]): void => {
    this._storePublicIpAddresses(addresses);
  };

  // -----------------------------------------------------------------------------------------------
  // Mock overrides.

  public constructor() {
    super(new OffscreenDocumentController(), new StorageService('ipAddressesMock', undefined));
  }

  public readonly start = jest.fn().mockResolvedValue(undefined);
  public readonly stop = jest.fn().mockResolvedValue(undefined);
  public readonly updatePrivateIpAddressesFromNativeClient = jest.fn().mockResolvedValue(undefined);
  public readonly updatePrivateIpAddressesFromBrowser = jest.fn().mockResolvedValue(undefined);
  public readonly updatePublicIpAddressesIfNecessary = jest.fn().mockResolvedValue(undefined);
  public readonly updatePublicIpAddresses = jest.fn().mockResolvedValue(undefined);
}

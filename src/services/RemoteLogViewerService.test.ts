import { Timestamp } from 'firebase/firestore';
import AccessLogEntry from 'models/AccessLogEntry';
import UserDocument from 'models/UserDocument';
import MockTelemetryService from 'test-helpers/MockTelemetryService';

import FirestoreDocumentListener from './FirestoreDocumentListener';
import RemoteLogViewerService from './RemoteLogViewerService';

const activeRtlvSession: UserDocument = {
  cldflt: {
    rtlv: {
      expiresAt: new Timestamp(Date.now() / 1000 + 300, 0), // 5 minutes in the future
      url: 'https://example.com/blah',
      token: 'xyzzy',
    },
  },
};

const expiredRtlvSession: UserDocument = {
  cldflt: {
    rtlv: {
      expiresAt: new Timestamp(Date.now() / 1000 - 300, 0), // 5 minutes in the past
      url: 'https://example.com/blah',
      token: 'xyzzy',
    },
  },
};

const mockAccessLog: AccessLogEntry = {
  clientip: '10.0.0.0',
  tenant: '00000000-0000-0000-0000-000000000000',
  httpcode: 200,
  took: 1234,
  time: Math.floor(Date.now() / 1000).toString(),
  groups: ['SomeGroup', 'SomeOtherGroup'],
  username: '<EMAIL>',
  clienthostname: 'test.host',
  https: true,
  method: 'GET',
  producerid: '',
  blocked: true,
  categories: ['12', '34', '946'],
  destdomain: 'example.com',
  url: 'https://test.example.com/foobar',
  userAgent: 'Unit Test',
  locations: [],
  timeslots: [],
  actions: ['block'],
  searchterms: '',
  safeguardinglevel: 0,
  safeguardingtheme: '',
  loglevel: 4,
  contenttype: 'text/html',
  title: 'Test Website',
  videoids: '',
  v: '3',
  ruleId: '4.5',
  policy: '11111111-1111-1111-1111-111111111111',
};

const simulateDocumentAdded = (
  documentListener: FirestoreDocumentListener<UserDocument>,
  doc: UserDocument,
): void => {
  (documentListener as any)._document = doc;
  documentListener.onAdded.dispatch(doc);
};

const simulateDocumentModified = (
  documentListener: FirestoreDocumentListener<UserDocument>,
  doc: UserDocument,
): void => {
  (documentListener as any)._document = doc;
  documentListener.onModified.dispatch(doc);
};

const simulateDocumentRemoved = (
  documentListener: FirestoreDocumentListener<UserDocument>,
): void => {
  (documentListener as any)._document = undefined;
  documentListener.onRemoved.dispatch();
};

describe('RemoteLogViewerService', () => {
  const mockTelemetryService = new MockTelemetryService();

  beforeEach(() => {
    jest.useFakeTimers();

    // Suppress console messages.
    jest.spyOn(console, 'debug').mockImplementation(() => {});
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(async () => {
    jest.useRealTimers();
  });

  describe('constructor', () => {
    it('adds handlers to the document listener', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      expect(documentListener.onAdded.length).toEqual(1);
      expect(documentListener.onModified.length).toEqual(1);
      expect(documentListener.onRemoved.length).toEqual(1);

      // This is a dummy assertion to stop Typescript complaining about an unused reference.
      expect(rtlv).not.toBeUndefined();
    });

    it('starts RTLV session if the existing document indicates it should be active', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      simulateDocumentAdded(documentListener, activeRtlvSession); // <-- before rtlv construction

      // Set isStarted to true so that the constructor will process the existing document
      (documentListener as any)._firestore = {}; // This will make isStarted return true

      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);

      // The constructor should now process the existing document because isStarted is true
      expect(rtlv.isInSession).toBeTrue();
    });

    it('does not start RTLV session if the existing document indicates it has already expired', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      simulateDocumentAdded(documentListener, expiredRtlvSession); // <-- before rtlv construction
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      expect(rtlv.isInSession).toBeFalse();
    });

    it('does not start RTLV session if the document does not exist', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      expect(rtlv.isInSession).toBeFalse();
    });
  });

  describe('isInSession', () => {
    it('returns false if an RTLV session has not started', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      expect(rtlv.isInSession).toBeFalse();
    });

    it('returns true if an RTLV session is in progress', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      simulateDocumentAdded(documentListener, activeRtlvSession);
      expect(rtlv.isInSession).toBeTrue();
    });

    it('returns false if an RTLV session has expired', async () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      simulateDocumentAdded(documentListener, activeRtlvSession);
      await jest.advanceTimersByTimeAsync(300001); // just over 5 minutes into the future
      expect(rtlv.isInSession).toBeFalse();
    });
  });

  describe('onLog()', () => {
    beforeEach(() => {
      (fetch as jest.Mock).mockResolvedValue({ ok: true });
    });

    it('does nothing if no RTLV session exists', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      rtlv.onLog(mockAccessLog);
      expect(fetch).not.toHaveBeenCalled();
    });

    it('uploads access log to stored URL if RTLV session is active', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      simulateDocumentAdded(documentListener, activeRtlvSession);

      rtlv.onLog(mockAccessLog);
      expect(fetch).toHaveBeenCalled();
      expect((fetch as jest.Mock).mock.calls[0][0].toString()).toEqual(
        activeRtlvSession.cldflt?.rtlv?.url,
      );
      expect((fetch as jest.Mock).mock.calls[0][1].headers.Authorization).toContain(
        activeRtlvSession.cldflt?.rtlv?.token,
      );
      expect(JSON.parse((fetch as jest.Mock).mock.calls[0][1].body)[0]).toEqual(mockAccessLog);
    });

    it('does nothing if the RTLV session has expired', async () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      simulateDocumentAdded(documentListener, activeRtlvSession);
      await jest.advanceTimersByTimeAsync(300001); // just over 5 minutes into the future
      rtlv.onLog(mockAccessLog);
      expect(fetch).not.toHaveBeenCalled();
    });

    it('does not retry an upload if the request fails with a status code', async () => {
      (fetch as jest.Mock).mockResolvedValue({ ok: false });

      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      simulateDocumentAdded(documentListener, activeRtlvSession);

      rtlv.onLog(mockAccessLog);
      // Wait a while to see if it tries again.
      await jest.advanceTimersByTimeAsync(300001); // just over 5 minutes into the future
      expect(fetch).toHaveBeenCalledOnce();
    });

    it('does not retry an upload if the request fails to send at all', async () => {
      (fetch as jest.Mock).mockRejectedValue(new Error('test error'));

      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      simulateDocumentAdded(documentListener, activeRtlvSession);

      rtlv.onLog(mockAccessLog);
      // Wait a while to see if it tries again.
      await jest.advanceTimersByTimeAsync(300001); // just over 5 minutes into the future
      expect(fetch).toHaveBeenCalledOnce();
    });
  });

  describe('onDiagnostics', () => {
    // TODO: Implement unit tests when diagnostics are implemented.
  });

  describe('_onUserDocument', () => {
    it('starts a session if the document is added with an active session', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      simulateDocumentAdded(documentListener, activeRtlvSession);
      expect(rtlv.isInSession).toBeTrue();
    });

    it('starts a session if the document is updated to have an active session', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      simulateDocumentAdded(documentListener, {});
      simulateDocumentModified(documentListener, activeRtlvSession);
      expect(rtlv.isInSession).toBeTrue();
    });

    it('continues the existing session if the document is updated and has an active session', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      simulateDocumentAdded(documentListener, activeRtlvSession);
      const updatedDoc: UserDocument = {
        cldflt: {
          rtlv: {
            expiresAt: new Timestamp(Date.now() / 1000 + 600, 0), // 10 minutes in the future
            url: 'https://example.com/blah',
            token: 'xyzzy',
          },
        },
      };
      simulateDocumentModified(documentListener, updatedDoc);
      expect(rtlv.isInSession).toBeTrue();
    });

    it('stops the session if the document is removed', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      simulateDocumentAdded(documentListener, activeRtlvSession);
      simulateDocumentRemoved(documentListener);
      expect(rtlv.isInSession).toBeFalse();
    });

    it('stops the session if the document is updated to contain an expired session', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      simulateDocumentAdded(documentListener, activeRtlvSession);
      simulateDocumentModified(documentListener, expiredRtlvSession);
      expect(rtlv.isInSession).toBeFalse();
    });

    it('stops the session if the document is updated to remove the RTLV configuration ', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      simulateDocumentAdded(documentListener, activeRtlvSession);
      simulateDocumentModified(documentListener, {});
      expect(rtlv.isInSession).toBeFalse();
    });

    it('does nothing if the document is added with an expired session and no session was already active', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      simulateDocumentAdded(documentListener, expiredRtlvSession);
      expect(rtlv.isInSession).toBeFalse();
    });

    it('does nothing if the document is added containing no RTLV configuration', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      simulateDocumentAdded(documentListener, {});
      expect(rtlv.isInSession).toBeFalse();
    });

    it('does nothing if the document is updated to contain an expired session and no session was already active', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      simulateDocumentAdded(documentListener, {});
      simulateDocumentModified(documentListener, expiredRtlvSession);
      expect(rtlv.isInSession).toBeFalse();
    });

    it('does nothing if the document is updated to contain no RTLV configuration and no session was already active', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      simulateDocumentAdded(documentListener, expiredRtlvSession);
      simulateDocumentModified(documentListener, {});
      expect(rtlv.isInSession).toBeFalse();
    });

    it('does nothing if the document is removed and no session was already active', () => {
      const documentListener = new FirestoreDocumentListener<UserDocument>(mockTelemetryService);
      const rtlv = new RemoteLogViewerService(documentListener, mockTelemetryService);
      simulateDocumentAdded(documentListener, expiredRtlvSession);
      simulateDocumentRemoved(documentListener);
      expect(rtlv.isInSession).toBeFalse();
    });
  });
});

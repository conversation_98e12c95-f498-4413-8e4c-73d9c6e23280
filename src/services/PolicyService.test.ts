import 'test-helpers/chrome-api';

import { ITenantInfo } from 'models/PolicyConfigModels';
import TenantId from 'models/TenantId';
import MockBlobStorageService, { MockUrlData } from 'test-helpers/MockBlobStorageService';
import { getTestPolicyConfig } from 'test-helpers/test-policy-config';
import { mergeBlobUrl } from 'utilities/Helpers';

import MockTelemetryService from '../test-helpers/MockTelemetryService';
import IBlobStorageService from './IBlobStorageService';
import IpServiceMock from './IpService.mock';
import PolicyService from './PolicyService';
import StorageService from './StorageService';

describe('PolicyService', () => {
  const tenant = new TenantId('f417a2c4-f99c-11ea-8caa-eb014c4bbe3b');

  let policyJson = getTestPolicyConfig(tenant);

  const resource = 'https://exampleblob.com';
  const sas = '?sas=testsas';
  const blobUrl = resource + '{0}' + sas;

  const policy1Url = blobUrl.replace('{0}', '/policy1');

  const policyDetails = {
    resource,
    name: 'policy1',
    sas,
  };

  const defaultUsersGroup = {
    id: '96871BA9-8808-3074-8AFE-F88F872B0A6D',
    comment: 'Static ID over all environments for all customers',
    name: 'Default Users',
  };

  // The constant banned user group object.
  const bannedUserGroup = {
    id: '7A9C36F1-D495-3ACE-9EA5-8A66D3A9A032', // Static ID over all environments for all customers for BANNED USERS group
    comment: 'The banned users group.',
    name: 'Banned Users',
  };

  let service: PolicyService;
  let mockBlobStorage: IBlobStorageService;
  let storageService: StorageService;
  const ipService = new IpServiceMock();
  const telemetryService = new MockTelemetryService();
  const localGroups: string[] = [];

  // Default managed policy for tests
  const defaultManagedPolicy: any = {
    MaxPolicyDownloadDelay: 15,
  };

  beforeEach(() => {
    // Suppress console output during the tests.
    jest.spyOn(console, 'debug').mockImplementation(() => {});

    policyJson = getTestPolicyConfig(tenant);
    mockBlobStorage = new MockBlobStorageService([
      new MockUrlData(policy1Url, JSON.stringify(policyJson)),
    ]);

    storageService = new StorageService('policy', undefined);
    service = new PolicyService(
      mockBlobStorage,
      storageService,
      ipService,
      telemetryService,
      'testuser',
      undefined,
      localGroups,
      defaultManagedPolicy,
    );
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should not error if the policy has not yet been set', () => {
    expect(service.bannedUsers).toBeUndefined();
  });

  it('should return loaded', (done) => {
    service.onPolicyLoaded.addListener(() => {
      try {
        expect(service.isLoaded).toBeTrue();
        done();
      } catch (error) {
        done(error);
      }
    });

    service.start(policyDetails).catch((error) => {
      done(error);
    });
  });

  it('should return not loaded', async () => {
    expect(service.isLoaded).toBeFalse();
  });

  it('should set the correct policy name', (done) => {
    service.onPolicyLoaded.addListener(() => {
      try {
        expect(service.policyName).toEqual(policyDetails.name);
        done();
      } catch (error) {
        done(error);
      }
    });

    service.start(policyDetails).catch((error) => {
      done(error);
    });
  });

  describe('start', () => {
    it('should load from cache if the policy url has not changed', (done) => {
      const url = mergeBlobUrl(policyDetails.resource, policyDetails.name, policyDetails.sas);
      storageService.set('configUrl', url);
      storageService.set('policy', policyJson);

      const service = new PolicyService(
        mockBlobStorage,
        storageService,
        ipService,
        telemetryService,
        'testuser',
        undefined,
        localGroups,
        defaultManagedPolicy,
      );

      const spy = jest.spyOn(mockBlobStorage, 'start');

      service.onPolicyLoaded.addListener(() => {
        try {
          expect(service.blockpages).toEqual(policyJson.blockpages);
          expect(spy).not.toHaveBeenCalled();
          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policyDetails).catch((error) => {
        done(error);
      });
    });

    it('saves the policy downloaded timestamp', (done) => {
      service.onPolicyLoaded.addListener(() => {
        try {
          const nowInSeconds = Math.floor(Date.now() / 1000);
          expect(service.policyDownloadedAt).toBeWithin(nowInSeconds - 60, nowInSeconds + 1);
          done();
        } catch (error) {
          done(error);
        }
      });

      service.start(policyDetails).catch(done);
    });
  });

  describe('stop', () => {
    it('should not set the policy if the request is cancelled', async () => {
      await service.start(policyDetails);
      service.stop();

      expect(service.bannedUsers).toBeUndefined();
    });

    it('should not trigger the onPolicyLoaded event if the request is cancelled', async () => {
      const listener = jest.fn();

      service.onPolicyLoaded.addListener(listener);

      await service.start(policyDetails);
      service.stop();

      expect(listener).not.toHaveBeenCalled();
    });

    it('should not set the policy name if the request is cancelled', async () => {
      await service.start(policyDetails);
      service.stop();

      expect(service.policyName).toBeUndefined();
    });

    it('should return not loaded', async () => {
      await service.start(policyDetails);
      service.stop();

      expect(service.isLoaded).toBeFalse();
    });
  });

  describe('updateGroupMapping', () => {
    jest.useFakeTimers();
    jest.spyOn(global, 'setTimeout');

    const testGroupObject = {
      comment: '',
      id: 'A4416B46-9972-11EC-9FC5-91B9081D14AE',
      name: 'Test Group',
    };

    beforeEach(() => {
      // Mapped groups is expected to be set when the policy is parsed.
      policyJson.mappedGroups = [];
      storageService.set('policy', policyJson);
    });

    afterEach(() => {
      jest.clearAllTimers();
    });

    it('should map the default user group if no other groups are mapped', async () => {
      await service.updateGroupMapping(['Non-existant'], ['No Mapping']);

      expect(service.mappedGroups).toBeArrayOfSize(1);
      expect(service.mappedGroups).toContainEqual(defaultUsersGroup);
    });

    it('should map a directory group to a smoothwall group', async () => {
      await service.updateGroupMapping(['/Filtering (OU)'], []);

      expect(service.mappedGroups).toBeArrayOfSize(1);
      expect(service.mappedGroups).toContainEqual(testGroupObject);
    });

    it('should not add duplicate groups when called multiple times', async () => {
      await service.updateGroupMapping(['/Filtering (OU)'], []);

      expect(service.mappedGroups).toBeArrayOfSize(1);
      expect(service.mappedGroups).toContainEqual(testGroupObject);

      await service.updateGroupMapping(['/Filtering (OU)'], []);

      expect(service.mappedGroups).toBeArrayOfSize(1);
      expect(service.mappedGroups).toContainEqual(testGroupObject);
    });

    it('should map directory groups with case-insensitive matching', async () => {
      // Test with different case variations of the same group
      await service.updateGroupMapping(['/filtering (ou)'], []); // lowercase

      expect(service.mappedGroups).toBeArrayOfSize(1);
      expect(service.mappedGroups).toContainEqual(testGroupObject);
    });

    it('should map directory groups with mixed case variations', async () => {
      // Test with mixed case variations
      await service.updateGroupMapping(['/FILTERING (OU)'], []); // uppercase

      expect(service.mappedGroups).toBeArrayOfSize(1);
      expect(service.mappedGroups).toContainEqual(testGroupObject);
    });

    it('should map directory groups with partial case differences', async () => {
      // Test with partial case differences
      await service.updateGroupMapping(['/Filtering (ou)'], []); // mixed case

      expect(service.mappedGroups).toBeArrayOfSize(1);
      expect(service.mappedGroups).toContainEqual(testGroupObject);
    });

    it('should handle case-insensitive matching for cloud groups', async () => {
      // Test case-insensitive matching for cloud groups
      await service.updateGroupMapping([], ['/filtering (ou)']);

      expect(service.mappedGroups).toBeArrayOfSize(1);
      expect(service.mappedGroups).toContainEqual(testGroupObject);
    });

    it('should preserve exact matches when available', async () => {
      // Ensure exact matches still work (backward compatibility)
      await service.updateGroupMapping(['/Filtering (OU)'], []);

      expect(service.mappedGroups).toBeArrayOfSize(1);
      expect(service.mappedGroups).toContainEqual(testGroupObject);
    });

    it('should handle multiple groups with different case variations', async () => {
      // Test multiple groups with case variations
      await service.updateGroupMapping(['/filtering (ou)', 'LOCAL_GROUP'], []);

      expect(service.mappedGroups).toBeArrayOfSize(2);
      expect(service.mappedGroups).toContainEqual(testGroupObject);
      expect(service.mappedGroups).toContainEqual({
        id: '7A9C36F1-D495-3ACE-9EA5-8A66D3A9A032',
        comment: '',
        name: 'Banned Users',
      });
    });

    it('should log telemetry for case-insensitive matches', async () => {
      const logEventSpy = jest.spyOn(telemetryService, 'logEvent');

      // Test with case mismatch to trigger case-insensitive matching
      await service.updateGroupMapping(['/filtering (ou)'], []);

      expect(logEventSpy).toHaveBeenCalledWith('groups-mapped', {
        numberOfDirectoryGroups: 1,
        numberOfMappedGroups: 1,
        caseInsensitiveMatches: 1,
      });
    });

    it('should not log case-insensitive matches for exact matches', async () => {
      const logEventSpy = jest.spyOn(telemetryService, 'logEvent');

      // Test with exact match
      await service.updateGroupMapping(['/Filtering (OU)'], []);

      expect(logEventSpy).toHaveBeenCalledWith('groups-mapped', {
        numberOfDirectoryGroups: 1,
        numberOfMappedGroups: 1,
        caseInsensitiveMatches: 0,
      });
    });

    describe('bannedUsers', () => {
      it('should add a user to the banned users group via the group mapping', async () => {
        await service.updateGroupMapping(['local_group'], []);

        expect(service.mappedGroups).toBeArrayOfSize(1);
        expect(service.mappedGroups).toContainEqual({
          id: '7A9C36F1-D495-3ACE-9EA5-8A66D3A9A032', // Static ID over all environments for all customers for BANNED USERS group
          comment: '',
          name: 'Banned Users',
        });
      });

      it('should match a valid on prem banned user policy', async () => {
        const policyService = new PolicyService(
          mockBlobStorage,
          storageService,
          ipService,
          telemetryService,
          'imbanned',
          undefined,
          localGroups,
          defaultManagedPolicy,
        );

        // For on prem banned users the local groups don't matter.
        await policyService.updateGroupMapping([], []);

        // If there are multiple bans then only one should be matched.
        expect(policyService.mappedGroups).toBeArrayOfSize(2);
        expect(policyService.mappedGroups).toContainEqual(bannedUserGroup);
      });

      it('should match a valid on prem tenanted banned user policy', async () => {
        const policyService = new PolicyService(
          mockBlobStorage,
          storageService,
          ipService,
          telemetryService,
          'imalsobanned',
          tenant,
          localGroups,
          defaultManagedPolicy,
        );

        // For on prem banned users the local groups don't matter.
        await policyService.updateGroupMapping([], []);

        // If there are multiple bans then only one should be matched.
        expect(policyService.mappedGroups).toBeArrayOfSize(2);
        expect(policyService.mappedGroups).toContainEqual(bannedUserGroup);
      });

      it('should not match a disabled banned user policy', async () => {
        // For on prem banned users the local groups don't matter.
        await service.updateGroupMapping([], []);

        // The only mapped group should be the default group.
        expect(service.mappedGroups).toBeArrayOfSize(1);
      });

      it('should not match a banned user policy that has expired', async () => {
        // For on prem banned users the local groups don't matter.
        await service.updateGroupMapping([], []);

        // The only mapped group should be the default group.
        expect(service.mappedGroups).toBeArrayOfSize(1);
      });

      it('should not add another banned user group if called more than once', async () => {
        const policyService = new PolicyService(
          mockBlobStorage,
          storageService,
          ipService,
          telemetryService,
          'imbanned',
          undefined,
          localGroups,
          defaultManagedPolicy,
        );

        // For on prem banned users the local groups don't matter.
        await policyService.updateGroupMapping([], []);
        await policyService.updateGroupMapping([], []);

        expect(policyService.mappedGroups).toBeArrayOfSize(2);
        expect(policyService.mappedGroups).toContainEqual(bannedUserGroup);
      });
    });
  });
  describe('isTenantValid', () => {
    beforeEach(() => {
      storageService.set('policy', policyJson);
    });
    describe('untenanted serial', () => {
      beforeEach(() => {
        policyJson.tenants = [];
        storageService.set('policy', policyJson);
      });
      it('returns true for a untenanted serial with no tenant provisioned', () => {
        const result = service.isTenantValid(undefined);

        expect(result).toBeTrue();
      });
      it('returns false for an untenanted serial when a tenant has been provisioned', () => {
        service = new PolicyService(
          mockBlobStorage,
          storageService,
          ipService,
          telemetryService,
          'testuser',
          tenant,
          localGroups,
          defaultManagedPolicy,
        );

        const result = service.isTenantValid(tenant);
        expect(result).toBeFalse();
      });
    });

    describe('tenanted serial', () => {
      beforeEach(() => {
        service = new PolicyService(
          mockBlobStorage,
          storageService,
          ipService,
          telemetryService,
          'testuser',
          tenant,
          localGroups,
          defaultManagedPolicy,
        );
      });

      it('returns true for a tenanted serial with a valid tenant provisoned', () => {
        const result = service.isTenantValid(tenant);
        expect(result).toBeTrue();
      });

      it('returns false for a tenanted serial with no tenant provisioned', () => {
        service = new PolicyService(
          mockBlobStorage,
          storageService,
          ipService,
          telemetryService,
          'testuser',
          undefined,
          localGroups,
          defaultManagedPolicy,
        );

        const result = service.isTenantValid(undefined);
        expect(result).toBeFalse();
      });
      it('returns false for a tenanted serial with an unrecognised tenant provisioned', () => {
        const tenantId = new TenantId('fdaa5515-0c2d-410e-91a1-648ee9767f18');
        service = new PolicyService(
          mockBlobStorage,
          storageService,
          ipService,
          telemetryService,
          'testuser',
          tenantId,
          localGroups,
          defaultManagedPolicy,
        );

        const result = service.isTenantValid(tenantId);
        expect(result).toBeFalse();
      });
    });
  });
  describe('getTenantInfo', () => {
    beforeEach(() => {
      storageService.set('policy', policyJson);
    });
    it('returns the correct tenant info for an existing tenant', () => {
      const expected: ITenantInfo = {
        id: 'f417a2c4-f99c-11ea-8caa-eb014c4bbe3b',
        name: 'Test tenant 1',
        addresses: ['test1'],
      };
      const result = service.getTenantInfo(tenant);

      expect(result).toEqual(expected);
    });
    it('returns undefined if the tenant could not be found', () => {
      const result = service.getTenantInfo(new TenantId('fdaa5515-0c2d-410e-91a1-648ee9767f18'));
      expect(result).toBeUndefined();
    });
  });

  describe('policyDownloadedAt', () => {
    it('returns undefined if no policy is in storage', () => {
      expect(service.policyDownloadedAt).toBeUndefined();
    });

    it('returns undefined if the stored policy does not have a downloaded timestamp', () => {
      storageService.set('policy', policyJson);
      storageService.set('policyName', policyDetails.name);
      expect(service.policyDownloadedAt).toBeUndefined();
    });

    it('returns the timestamp of when a cached policy was last downloaded', () => {
      storageService.set('policy', policyJson);
      storageService.set('policyName', policyDetails.name);
      storageService.set('policyDownloadedAt', 1709569781);
      expect(service.policyDownloadedAt).toEqual(1709569781);
    });
  });

  describe('Policy Download Delay', () => {
    beforeEach(() => {
      jest.useFakeTimers();
      jest.spyOn(console, 'debug').mockImplementation(() => {});
    });

    afterEach(() => {
      jest.useRealTimers();
      jest.restoreAllMocks();
    });

    describe('No delay scenarios', () => {
      it('should not delay on first run', async () => {
        const mockPolicyDownloadService = {
          start: jest.fn(),
        };
        (service as any)._policyDownloadService = mockPolicyDownloadService;

        await service.start(policyDetails, true); // isFirstRun = true

        expect(mockPolicyDownloadService.start).toHaveBeenCalledWith(policy1Url);
        expect(mockPolicyDownloadService.start).toHaveBeenCalledTimes(1);
      });

      it('should not delay when no existing policy', async () => {
        const mockPolicyDownloadService = {
          start: jest.fn(),
        };
        (service as any)._policyDownloadService = mockPolicyDownloadService;

        // Ensure no policy is loaded
        expect(service.policyConfig).toBeUndefined();

        await service.start(policyDetails, false); // isFirstRun = false

        expect(mockPolicyDownloadService.start).toHaveBeenCalledWith(policy1Url);
        expect(mockPolicyDownloadService.start).toHaveBeenCalledTimes(1);
      });

      it('should not delay when MaxPolicyDownloadDelay is 0', async () => {
        const managedPolicyWithZeroDelay = { MaxPolicyDownloadDelay: 0 };
        const zeroDelayStorageService = new StorageService('policy', undefined);
        const serviceWithZeroDelay = new PolicyService(
          mockBlobStorage,
          zeroDelayStorageService,
          ipService,
          telemetryService,
          'testuser',
          undefined,
          localGroups,
          managedPolicyWithZeroDelay,
        );

        const mockPolicyDownloadService = {
          start: jest.fn(),
        };
        (serviceWithZeroDelay as any)._policyDownloadService = mockPolicyDownloadService;

        // Set up existing policy in storage with different URL so service thinks it has a policy but needs to download new one
        const oldUrl = mergeBlobUrl(policyDetails.resource, 'oldpolicy', policyDetails.sas);
        zeroDelayStorageService.set('configUrl', oldUrl);
        zeroDelayStorageService.set('policy', policyJson);

        await serviceWithZeroDelay.start(policyDetails, false);

        expect(mockPolicyDownloadService.start).toHaveBeenCalledWith(policy1Url);
        expect(mockPolicyDownloadService.start).toHaveBeenCalledTimes(1);
      });

      it('should not delay when MaxPolicyDownloadDelay is zero', async () => {
        // Use real timers for this test since it's testing a no-delay scenario
        jest.useRealTimers();

        const managedPolicyWithZeroDelay = { MaxPolicyDownloadDelay: 0 };
        const zeroDelayStorageService = new StorageService('policy', undefined);
        const serviceWithZeroDelay = new PolicyService(
          mockBlobStorage,
          zeroDelayStorageService,
          ipService,
          telemetryService,
          'testuser',
          undefined,
          localGroups,
          managedPolicyWithZeroDelay,
        );

        const mockPolicyDownloadService = {
          start: jest.fn(),
        };
        (serviceWithZeroDelay as any)._policyDownloadService = mockPolicyDownloadService;

        // Set up existing policy with different URL
        const oldUrl = mergeBlobUrl(policyDetails.resource, 'oldpolicy', policyDetails.sas);
        zeroDelayStorageService.set('configUrl', oldUrl);
        zeroDelayStorageService.set('policy', policyJson);

        await serviceWithZeroDelay.start(policyDetails, false);

        expect(mockPolicyDownloadService.start).toHaveBeenCalledWith(policy1Url);
        expect(mockPolicyDownloadService.start).toHaveBeenCalledTimes(1);

        // Restore fake timers for other tests
        jest.useFakeTimers();
      });
    });

    describe('Delay scenarios', () => {
      it('should apply default delay when MaxPolicyDownloadDelay is not specified', async () => {
        const managedPolicyWithoutDelay = {};
        const defaultDelayStorageService = new StorageService('policy', undefined);
        const serviceWithoutDelay = new PolicyService(
          mockBlobStorage,
          defaultDelayStorageService,
          ipService,
          telemetryService,
          'testuser',
          undefined,
          localGroups,
          managedPolicyWithoutDelay,
        );

        const mockPolicyDownloadService = {
          start: jest.fn(),
        };
        (serviceWithoutDelay as any)._policyDownloadService = mockPolicyDownloadService;

        // Set up existing policy with different URL
        const oldUrl = mergeBlobUrl(policyDetails.resource, 'oldpolicy', policyDetails.sas);
        defaultDelayStorageService.set('configUrl', oldUrl);
        defaultDelayStorageService.set('policy', policyJson);

        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        serviceWithoutDelay.start(policyDetails, false);

        // Should not have started immediately
        expect(mockPolicyDownloadService.start).not.toHaveBeenCalled();

        // Fast-forward time by 15 seconds (default delay)
        jest.advanceTimersByTime(15000);
        await jest.runOnlyPendingTimersAsync();

        expect(mockPolicyDownloadService.start).toHaveBeenCalledWith(policy1Url);
        expect(mockPolicyDownloadService.start).toHaveBeenCalledTimes(1);
      });

      it('should apply custom delay when MaxPolicyDownloadDelay is specified', async () => {
        const managedPolicyWithCustomDelay = { MaxPolicyDownloadDelay: 30 };
        const customDelayStorageService = new StorageService('policy', undefined);
        const serviceWithCustomDelay = new PolicyService(
          mockBlobStorage,
          customDelayStorageService,
          ipService,
          telemetryService,
          'testuser',
          undefined,
          localGroups,
          managedPolicyWithCustomDelay,
        );

        const mockPolicyDownloadService = {
          start: jest.fn(),
        };
        (serviceWithCustomDelay as any)._policyDownloadService = mockPolicyDownloadService;

        // Set up existing policy with different URL
        const oldUrl = mergeBlobUrl(policyDetails.resource, 'oldpolicy', policyDetails.sas);
        customDelayStorageService.set('configUrl', oldUrl);
        customDelayStorageService.set('policy', policyJson);

        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        serviceWithCustomDelay.start(policyDetails, false);

        // Should not have started immediately
        expect(mockPolicyDownloadService.start).not.toHaveBeenCalled();

        // Fast-forward time by 30 seconds (custom delay)
        jest.advanceTimersByTime(30000);
        await jest.runOnlyPendingTimersAsync();

        expect(mockPolicyDownloadService.start).toHaveBeenCalledWith(policy1Url);
        expect(mockPolicyDownloadService.start).toHaveBeenCalledTimes(1);
      });
    });

    describe('Delay cancellation', () => {
      it('should cancel existing delay when new policy change is detected', async () => {
        const cancellationStorageService = new StorageService('policy', undefined);
        const cancellationService = new PolicyService(
          mockBlobStorage,
          cancellationStorageService,
          ipService,
          telemetryService,
          'testuser',
          undefined,
          localGroups,
          defaultManagedPolicy,
        );

        const mockPolicyDownloadService = {
          start: jest.fn(),
        };
        (cancellationService as any)._policyDownloadService = mockPolicyDownloadService;

        // Set up existing policy with different URL
        const oldUrl = mergeBlobUrl(policyDetails.resource, 'oldpolicy', policyDetails.sas);
        cancellationStorageService.set('configUrl', oldUrl);
        cancellationStorageService.set('policy', policyJson);

        // Start first policy download with delay
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        cancellationService.start(policyDetails, false);

        // Should not have started immediately
        expect(mockPolicyDownloadService.start).not.toHaveBeenCalled();

        // Start second policy download before first delay completes
        const secondPolicyDetails = { ...policyDetails, name: 'policy2' };
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        cancellationService.start(secondPolicyDetails, false);

        // Fast-forward time by 15 seconds to complete the delay
        jest.advanceTimersByTime(15000);
        await jest.runOnlyPendingTimersAsync();
        // Should only have started the second policy download
        expect(mockPolicyDownloadService.start).toHaveBeenCalledTimes(1);
        expect(mockPolicyDownloadService.start).toHaveBeenCalledWith(
          mergeBlobUrl(
            secondPolicyDetails.resource,
            secondPolicyDetails.name,
            secondPolicyDetails.sas,
          ),
        );
      });

      it('should cancel delay when stop is called', async () => {
        // Use a managed policy with zero delay to avoid timing issues
        const zeroDelayManagedPolicy = { MaxPolicyDownloadDelay: 0 };

        const stopStorageService = new StorageService('policy', undefined);
        const stopService = new PolicyService(
          mockBlobStorage,
          stopStorageService,
          ipService,
          telemetryService,
          'testuser',
          undefined,
          localGroups,
          zeroDelayManagedPolicy,
        );

        const mockPolicyDownloadService = {
          start: jest.fn(),
          stop: jest.fn(),
        };
        (stopService as any)._policyDownloadService = mockPolicyDownloadService;

        // Set up existing policy with different URL
        const oldUrl = mergeBlobUrl(policyDetails.resource, 'oldpolicy', policyDetails.sas);
        stopStorageService.set('configUrl', oldUrl);
        stopStorageService.set('policy', policyJson);

        // Start policy download (should complete immediately with zero delay)
        await stopService.start(policyDetails, false);

        // Should have started immediately since delay is zero
        expect(mockPolicyDownloadService.start).toHaveBeenCalled();

        // Stop the service
        stopService.stop();
        expect(mockPolicyDownloadService.stop).toHaveBeenCalled();
      });
    });

    describe('Debug logging', () => {
      it('should log debug message when delay is applied', async () => {
        const consoleSpy = jest.spyOn(console, 'debug');
        const logStorageService = new StorageService('policy', undefined);
        const logService = new PolicyService(
          mockBlobStorage,
          logStorageService,
          ipService,
          telemetryService,
          'testuser',
          undefined,
          localGroups,
          defaultManagedPolicy,
        );

        const mockPolicyDownloadService = {
          start: jest.fn(),
        };
        (logService as any)._policyDownloadService = mockPolicyDownloadService;

        // Set up existing policy with different URL
        const oldUrl = mergeBlobUrl(policyDetails.resource, 'oldpolicy', policyDetails.sas);
        logStorageService.set('configUrl', oldUrl);
        logStorageService.set('policy', policyJson);

        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        logService.start(policyDetails, false);

        // Fast-forward time by 15 seconds
        jest.advanceTimersByTime(15000);
        await jest.runOnlyPendingTimersAsync();

        expect(consoleSpy).toHaveBeenCalledWith(
          expect.stringMatching(/PolicyService - Delaying policy download by \d+ms \(max: 15s\)/),
        );
      });

      it('should log debug message when delay is not applied for first run', async () => {
        const consoleSpy = jest.spyOn(console, 'debug');
        const mockPolicyDownloadService = {
          start: jest.fn(),
        };
        (service as any)._policyDownloadService = mockPolicyDownloadService;

        await service.start(policyDetails, true);

        expect(consoleSpy).toHaveBeenCalledWith('PolicyService - No delay applied: first run');
      });

      it('should log debug message when delay is not applied for no existing policy', async () => {
        const consoleSpy = jest.spyOn(console, 'debug');
        const mockPolicyDownloadService = {
          start: jest.fn(),
        };
        (service as any)._policyDownloadService = mockPolicyDownloadService;

        await service.start(policyDetails, false);

        expect(consoleSpy).toHaveBeenCalledWith(
          'PolicyService - No delay applied: no existing policy',
        );
      });

      it('should log debug message when delay is disabled', async () => {
        const consoleSpy = jest.spyOn(console, 'debug');
        const managedPolicyWithZeroDelay = { MaxPolicyDownloadDelay: 0 };
        const serviceWithZeroDelay = new PolicyService(
          mockBlobStorage,
          storageService,
          ipService,
          telemetryService,
          'testuser',
          undefined,
          localGroups,
          managedPolicyWithZeroDelay,
        );

        const mockPolicyDownloadService = {
          start: jest.fn(),
        };
        (serviceWithZeroDelay as any)._policyDownloadService = mockPolicyDownloadService;

        // Set up existing policy
        storageService.set('policy', policyJson);

        await serviceWithZeroDelay.start(policyDetails, false);

        expect(consoleSpy).toHaveBeenCalledWith(
          'PolicyService - No delay applied: delay disabled (maxDelay <= 0)',
        );
      });
    });
  });
});

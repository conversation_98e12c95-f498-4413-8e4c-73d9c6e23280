import AccessLogEntry from 'models/AccessLogEntry';
import DiagnosticsInfo, { RemoteDiagnosticsInfo } from 'models/DiagnosticsInfo';
import UserDocument from 'models/UserDocument';
import ITelemetryService from 'services/ITelemetryService';
import { TelemetryEventType } from '../constants/TelemetryEventType';
import { deepEqual } from 'utilities/Helpers';
import StandaloneEvent from 'utilities/StandaloneEvent';

import FirestoreDocumentListener from './FirestoreDocumentListener';

/**
 * Streams logs to the cloud for remote RTLV (Real Time Log Viewer).
 * This class does not cache any state between runs of the extension. It must be initialised with an
 *  object which listens for changes on a user document in Firestore.
 */
export default class RemoteLogViewerService {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance of this class.
   * On its own, this will not start RTLV or receive state changes. You must also call initialise()
   *  after construction.
   *
   * @param userDocumentListener Subscribes to updates from the user document which contains RTLV
   *  configuration and state.
   * @param telemetryService Sends events and exceptions to the cloud.
   */
  constructor(
    userDocumentListener: FirestoreDocumentListener<UserDocument>,
    telemetryService: ITelemetryService,
  ) {
    this._userDocumentListener = userDocumentListener;
    this._telemetryService = telemetryService;

    // Initialize event listeners
    this._userDocumentListener.onAdded.addListener(this._onUserDocument);
    this._userDocumentListener.onModified.addListener(this._onUserDocument);
    this._userDocumentListener.onRemoved.addListener(this._onUserDocument);

    // If the user document already exists and the listener is started, apply its current state
    this._onUserDocument(this._userDocumentListener.data);
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Check if an RTLV session is currently active.
   * The session state is based on the user document.
   */
  public get isInSession(): boolean {
    return this._expiresAt !== undefined && this._expiresAt > new Date();
  }

  // -----------------------------------------------------------------------------------------------
  // Event handlers.

  /**
   * Upload an access log to the Cloud Filter portal, if an RTLV session is currently active.
   * Does nothing if RTLV is not active.
   *
   * @param log The log entry to upload.
   */
  public readonly onLog = (log: AccessLogEntry): void => {
    // If we don't have a current RTLV session then ignore the log.
    if (this._expiresAt === undefined || this._url === undefined || this._token === undefined) {
      return;
    }

    // Sanity-check: If our existing RTLV session has expired then stop it and discard the log.
    const now = new Date();
    if (this._expiresAt < now) {
      this._stopSession();
      return;
    }

    // Don't send logs which are below the minimum level. This is to prevent the viewer from being
    //  flooded by trivial requests. Keep logs which have no level in case there's an error in the
    //  log levels.
    if (log.loglevel !== undefined && log.loglevel < this._minimumLogLevel) {
      return;
    }

    // Failed uploads are deliberately discarded. RTLV involves a high volume of requests. We can't
    //  practically log or retry failures.
    fetch(this._url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this._token}`,
      },
      body: JSON.stringify([log]),
    })
      .then((response: Response) => {
        if (response.ok) {
          this._logCount += 1;
        }
      })
      .catch(() => {});
  };

  /**
   * Store the specified diagnostics info display in the Cloud Filter portal.
   * This should be called whenever there is a significant change in diagnostics info, even if RTLV
   *  isn't running. This ensures the information is already available when RTLV starts.
   *
   * @param diagnosticsInfo The diagnostics info to store for display in the portal.
   */
  public readonly onDiagnostics = (diagnosticsInfo: DiagnosticsInfo): void => {
    // If the diagnostics information hasn't changed then do nothing.
    const newData = diagnosticsInfo.toRemote;
    if (deepEqual(this._remoteDiagnosticsInfo, newData)) {
      return;
    }
    this._remoteDiagnosticsInfo = newData;

    // If we're not in an RTLV session then there's nothing else to do.
    if (!this.isInSession) {
      return;
    }

    // If we're already debouncing another update then just wait for that to finish.
    if (this._diagnosticsDebounceTimeout !== undefined) {
      console.debug('RemoteLogViewerService - Debouncing diagnostic update.');
      return;
    }

    // If we've recently written diagnostics then wait a while before writing again.
    // This is designed to debounce rapid changes so that we don't incur too many write costs.
    const now = Date.now();
    const timeSinceLastUpdate = now - this._diagnosticsLastWrittenAt;
    if (timeSinceLastUpdate < RemoteLogViewerService._diagnosticsDebounceDelay) {
      this._diagnosticsDebounceTimeout = setTimeout(
        this._writeDiagnosticsToDocument,
        RemoteLogViewerService._diagnosticsDebounceDelay - timeSinceLastUpdate,
      );
      console.debug('RemoteLogViewerService - Debouncing diagnostic update.');
      return;
    }

    // If we reach here then we're OK to write diagnostic info.
    this._writeDiagnosticsToDocument();
  };

  /**
   * Called when the user document has been created or updated.
   * This will check the contents of the document to determine whether to start, stop, or continue
   *  an RTLV session.
   *
   * @param userDocument The added or updated user document, potentially containing
   *  details of the RTLV session. This will be undefined if the document has been removed.
   */
  private readonly _onUserDocument = (userDocument?: UserDocument): void => {
    const rtlv = userDocument?.cldflt?.rtlv;
    if (rtlv === undefined) {
      this._stopSession();
      return;
    }

    try {
      this._updateSession(rtlv.expiresAt.toDate(), new URL(rtlv.url), rtlv.token);
    } catch (e: any) {
      console.warn('Failed to start remote RTLV', e);
    }
  };

  /**
   * Called when the a timer elapses indicating that the RTLV session should end.
   * This will double-check the actual expiry time before ending the session.
   */
  private readonly _onExpiryTimeout = (): void => {
    if (this._expiresAt === undefined) {
      console.debug('RemoteLogViewerService - Ignoring expiry timer. Session has already ended.');
      return;
    }

    if (this._expiresAt > new Date()) {
      console.debug(
        'RemoteLogViewerService - Expiry timer triggered early. Session is still running.',
      );
      return;
    }

    this._stopSession();
  };

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Start a new RTLV session, or continue the existing one if it's already in progress.
   * This will also send any necessary telemetry, and update the UI.
   * It will also set a timer to end the RTLV session after the expiry time.
   */
  private readonly _updateSession = (expiresAt: Date, url: URL, token: string): void => {
    // If the new session info indicates it's already expired, then stop any existing session.
    const now = new Date();
    if (expiresAt < now) {
      this._stopSession();
      return;
    }

    // Is this a newly started session?
    const isNewSession = this._expiresAt === undefined;

    if (isNewSession) {
      // Only log telemetry if this is a new session.
      this._telemetryService.logEvent(TelemetryEventType.RealtimeConnection, {
        event: 'start',
        url: url.toString(),
      });

      console.log('RemoteLogViewerService - Starting RTLV session.');
      this._logCount = 0;
    }

    this._expiresAt = expiresAt;
    this._url = url;
    this._token = token;

    this.onSessionStart.dispatch();

    if (isNewSession) {
      // This must be done after initialising everything else above.
      this._writeDiagnosticsToDocument();
    }

    // Update the UI to show that an RTLV session is running.
    chrome.action
      .setIcon({ path: '../images/smoothwall32-admin-connected.png' })
      .catch(console.warn);

    // Stop the RTLV session if the expiry time is reached.
    if (this._expiryTimeout !== undefined) {
      clearTimeout(this._expiryTimeout);
    }
    this._expiryTimeout = setTimeout(
      this._onExpiryTimeout,
      this._expiresAt.getTime() - now.getTime(),
    );
  };

  /**
   * Stop the current RTLV session running, if there is one.
   * This also sends telemetry to report the event, and updates the UI.
   */
  private readonly _stopSession = (): void => {
    // Only log telemetry if we weren't already stopped.
    if (this._expiresAt !== undefined) {
      this._telemetryService.logEvent(TelemetryEventType.RealtimeConnection, {
        event: 'stop',
        logCount: this._logCount,
      });
      console.log(
        `RemoteLogViewerService - Stopping RTLV session. Logs uploaded: ${this._logCount}`,
      );
    }

    this._expiresAt = undefined;
    this._url = undefined;
    this._token = undefined;
    this._logCount = 0;
    this._diagnosticsLastWrittenAt = 0;

    // Update the UI to show that an RTLV session is not running.
    chrome.action.setIcon({ path: '../images/smoothwall-icon-16x16.png' }).catch(console.warn);

    if (this._expiryTimeout !== undefined) {
      clearTimeout(this._expiryTimeout);
      this._expiryTimeout = undefined;
    }

    if (this._diagnosticsDebounceTimeout !== undefined) {
      clearTimeout(this._diagnosticsDebounceTimeout);
      this._diagnosticsDebounceTimeout = undefined;
    }

    this.onSessionEnd.dispatch();
  };

  /**
   * Take the diagnostic info we currently have cached, and write it to the Firestore document.
   * This does nothing if the user document hasn't been initialised yet.
   */
  private readonly _writeDiagnosticsToDocument = (): void => {
    // No point continuing any pending debounce.
    if (this._diagnosticsDebounceTimeout !== undefined) {
      clearTimeout(this._diagnosticsDebounceTimeout);
      this._diagnosticsDebounceTimeout = undefined;
    }

    // Don't do anything if we're not in an RTLV session, the user document isn't initialised, or we
    //  don't have any diagnostic info to write.
    if (
      !this.isInSession ||
      !this._userDocumentListener.isStarted ||
      this._remoteDiagnosticsInfo === undefined
    ) {
      return;
    }

    // Do nothing if the user document already contains the latest diagnostics info.
    if (
      deepEqual(this._userDocumentListener?.data?.cldflt?.diagnostics, this._remoteDiagnosticsInfo)
    ) {
      return;
    }

    this._userDocumentListener
      .update({ 'cldflt.diagnostics': this._remoteDiagnosticsInfo })
      .then(() => {
        console.debug('RemoteLogViewerService - Diagnostics info written to Firestore.');
      })
      .catch((e) => {
        console.warn('RemoteLogViewerService - Failed to write diagnostics info to Firestore.', e);
      });

    this._diagnosticsLastWrittenAt = Date.now();
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * An event dispatched when a remote log viewer session is started.
   */
  public readonly onSessionStart: StandaloneEvent = new StandaloneEvent();

  /**
   * An event dispatched when a remote log viewer session is ended.
   */
  public readonly onSessionEnd: StandaloneEvent = new StandaloneEvent();

  /**
   * The minimum log level which will be sent to the remote log viewer.
   * Any logs with a lower level will be discarded.
   */
  private readonly _minimumLogLevel = 3;

  /**
   * This object will be used to read and write the user document.
   */
  private readonly _userDocumentListener: FirestoreDocumentListener<UserDocument>;

  /**
   * The telemetry service for logging events.
   */
  private readonly _telemetryService: ITelemetryService;

  /**
   * The date and time at which the current RTLV session will stop, if it's not extended.
   * This will be undefined if no RTLV session is currently active.
   */
  private _expiresAt?: Date;

  /**
   * The URL which access logs should be uploaded to.
   * This will be undefined if no RTLV session is currently active.
   */
  private _url?: URL;

  /**
   * The authorisation token for uploading access logs.
   * This will be undefined if no RTLV session is currently active.
   */
  private _token?: string;

  /**
   * Timer which triggers when the RTLV session is due to expire.
   * This will be undefined if no RTLV session is currently active.
   */
  private _expiryTimeout?: ReturnType<typeof setTimeout>;

  /**
   * The number of logs uploaded so far in the most recent session.
   * This is reset to 0 when a new session is started.
   */
  private _logCount: number = 0;

  /**
   * Contains the most recent diagnostics info which we've received, if any.
   * This is stored in the format expected by the Cloud Filter portal for RTLV.
   * This is written to the user document when an RTLV session first starts. The new info is written
   *  to again if it changes while RTLV is running, subject to debouncing.
   *
   * @note This information is deliberately not cleared between RTLV sessions. We want to retain a
   *  copy of the latest information at all times.
   */
  private _remoteDiagnosticsInfo?: RemoteDiagnosticsInfo;

  /**
   * The timestamp (in milliseconds) of when we last wrote diagnostics info to the user document.
   * This is used to avoid updating it too frequently.
   * It will be 0 if we haven't written diagnostics info yet.
   */
  private _diagnosticsLastWrittenAt: number = 0;

  /**
   * Timer used to avoid writing diagnostics into to the user document too frequently.
   * This will only be defined if we're currently debouncing diagnostic updates.
   *
   * @see _diagnosticsDebounceDelay
   */
  private _diagnosticsDebounceTimeout?: ReturnType<typeof setTimeout>;

  /**
   * The minimum time (in milliseconds) between writing diagnostics info to the user document.
   * After writing the info to the document once, we will wait at least this long before writing
   *  again if the info changes.
   *
   * @see _diagnosticsDebounceTimeout
   */
  private static readonly _diagnosticsDebounceDelay = 15000;
}

import StorageService from 'services/StorageService';
import { isManifestV2 } from 'utilities/Helpers';

/**
 * The idle timeout of a service worker is 30 seconds, and is reset whenever anything is written to
 * local storage. This service periodically writes arbitrarily changing data - a timestamp - to
 * storage so that the service worker idle timeout is constantly being reset.
 */
export default class KeepAliveService {
  constructor(storageService: StorageService) {
    this._storageService = storageService;
    this._storageService.enableAutoSave();
  }

  public readonly start = (): void => {
    if (this._updateInterval === undefined && !isManifestV2) {
      this._updateInterval = setInterval(() => {
        this._storageService.set('timestamp', Date.now());
      }, this._intervalPeriodInMilliseconds);
    }
  };

  /**
   * Stops the interval for the keep alive service.
   */
  public readonly stop = (): void => {
    clearInterval(this._updateInterval);
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Stores a periodically updated timestamp used to keep the service worker alive.
   */
  private readonly _storageService: StorageService;

  /**
   * Period between writes to storage. Service worker idle timeout is 30 seconds, so we do 25.
   */
  private readonly _intervalPeriodInMilliseconds = 25000;

  /**
   * The constantly running interval which writes to storage to keep the service worker alive.
   */
  private _updateInterval?: ReturnType<typeof setInterval>;
}

import CryptoJS from 'crypto-js';
import FetchService, { Action } from 'services/FetchService';
import AlarmService from 'services/AlarmService';
import DeviceId from 'models/DeviceId';
import ProvisioningInfo from 'provisioning/ProvisioningInfo';
import StandaloneEvent from 'utilities/StandaloneEvent';
import TemplateString from 'models/TemplateString';
import ConstantBackOff from 'back-off-methods/ConstantBackOff';
import HeartbeatData from 'models/HeartbeatData';
import { areArraysEqual, deepEqual } from 'utilities/Helpers';
import IpService from './IpService';

/**
 * Regularly posts a heartbeat to the device registration cloud services.
 */
export default class HeartbeatService {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  constructor(alarmService: AlarmService) {
    this._fetchService.setResponseHandler(this._onResponse);
    this._alarmService = alarmService;
    this._alarmService.addListener(this._alarmName, this._onAlarm);
  }

  // -----------------------------------------------------------------------------------------------
  // Events.

  /**
   * Triggered when the check-in fails with a 404.
   * Serial, tenant, user or device resource cannot be found.
   */
  public readonly onUnregistered = new StandaloneEvent();

  public readonly onHeartbeatAlarm = new StandaloneEvent();

  public readonly onHeartbeatFinished = new StandaloneEvent();

  /**
   * Triggered when the device's public IP addresses have been received in a heartbeat response.
   * The argument is an array of the device's public IPv4 addresses.
   * Currently, there is only ever one, but that may change.
   */
  public readonly onPublicIpAddressesReceived = new StandaloneEvent<[string[]]>();

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Sets the data needed to construct the device check-in endpoint URL.
   *
   * @param provisioningInfo provided when cloud connection controller starts
   * @param deviceId provided on device registration
   */
  public readonly start = async (
    heartbeatUrlTemplate: TemplateString,
    provisioningInfo: ProvisioningInfo,
    deviceId: DeviceId,
  ): Promise<void> => {
    this._heartbeatUrlTemplate = heartbeatUrlTemplate;

    this._provisioningInfo = provisioningInfo;
    this._deviceId = deviceId;

    await this._alarmService.create(this._alarmName, {
      periodInMinutes: this._periodInMinutes,
    });

    // Trigger a heartbeat immediately during start-up or re-registration. This is because we depend
    //  on the heartbeat response to get our public IP address. We need to ensure it's up-to-date if
    //  the extension has been offline.
    this.onHeartbeatAlarm.deferDispatch();
  };

  /**
   * Remove alarm to stop sending heartbeats.
   */
  public readonly stop = async (): Promise<void> => {
    await this._alarmService.remove(this._alarmName);
    this.resetHeartbeatData();
  };

  /**
   * Sends a heartbeat request to the device management api.
   * @param data The data to be sent in the body of the heartbeat request.
   */
  public readonly sendHeartbeat = async (data: HeartbeatData): Promise<void> => {
    if (
      this._provisioningInfo === undefined ||
      this._deviceId === undefined ||
      this._heartbeatUrlTemplate === undefined
    ) {
      throw new Error('Cannot perform heartbeat. Not initialised.');
    }

    const encodedUser = CryptoJS.enc.Utf8.parse(this._provisioningInfo.user);

    const url: URL = this._heartbeatUrlTemplate.toUrl({
      glsHash: this._provisioningInfo.serialId.getGlsHash(),
      customerId: this._provisioningInfo.serialId.toString(),
      tenant: this._provisioningInfo.tenantId?.toString() ?? 'untenanted',
      userId: CryptoJS.enc.Base64url.stringify(encodedUser),
      deviceId: this._deviceId.toString(),
    });

    // There's no need to send up any data that hasn't changed. Remove those properties here.
    const dataToSend = this._removeDuplicateProperties(data);

    const details: RequestInit = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Id': this._provisioningInfo.serialId.toString(),
      },
      body: JSON.stringify(dataToSend),
    };

    this._sentData = data;
    await this._fetchService.start(url, details);
  };

  /**
   * Resets the data held or comparing heartbeats.
   */
  public readonly resetHeartbeatData = (): void => {
    this._previousData = undefined;
  };

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Fires when alarm is triggered.
   */
  private readonly _onAlarm = async (alarm: chrome.alarms.Alarm): Promise<void> => {
    this.onHeartbeatAlarm.dispatch();
  };

  /**
   * Fetch response handler.
   */
  private readonly _onResponse = async (response: Response): Promise<Action> => {
    if (!response.ok) {
      console.warn('Heartbeat failed', response);
    } else {
      console.debug('Heartbeat succeeded', response);
      this._previousData = this._sentData;

      try {
        // DMS provides our current public IP address in the check-in response.
        // This needs to be propagated out to the rest of the extension.
        const body = await response.json();
        const publicIpAddress = body?.data?.clientIpAddress;
        if (typeof publicIpAddress === 'string' && IpService.isIpv4(publicIpAddress)) {
          this.onPublicIpAddressesReceived.deferDispatch([publicIpAddress]);
        } else {
          console.warn(
            'HeartbeatService - Missing or invalid public IP address in DMS response.',
            body,
          );
        }
      } catch (e: any) {
        console.warn('HeartbeatService - Failed to parse response from DMS.', e);
      }
    }

    if (response.status === 404) {
      await this.stop();
      this.onUnregistered.dispatch();
    }

    this._sentData = undefined;

    this.onHeartbeatFinished.dispatch();

    return Action.stop;
  };

  /**
   * Checks all properties in the given heartbeat data against the previously sent data in memory.
   * If any duplicate properties are found they are removed from the returned object.
   * @param data The new heartbeat data that will be sent.
   * @returns A HeartbeatData object with any duplicated properties removed.
   */
  private readonly _removeDuplicateProperties = (data: HeartbeatData): HeartbeatData => {
    let dataToSend: HeartbeatData = data;

    if (this._previousData === undefined) {
      return data;
    }

    // Always send the check-in time and log upload count.
    dataToSend = {
      checkInTime: data.checkInTime,
      logUploadCount: data.logUploadCount,
    };

    let key: keyof HeartbeatData;
    for (key in data) {
      const value = data[key];
      const oldValue = this._previousData[key];

      // If it's a new value we don't need to run any of the checks.
      if (oldValue === undefined) {
        dataToSend = {
          ...dataToSend,
          [key]: value,
        };
        continue;
      }

      let isEqual = true;

      if (Array.isArray(value) && Array.isArray(oldValue)) {
        if (key === 'software' || key === 'mappedGroups') {
          // Software and mapped groups are arrays of objects so we need to do a deep equals on each object to check for equality.
          if (value.length === oldValue.length) {
            for (let i = 0; i < value.length; i++) {
              if (!deepEqual(value[i], oldValue[i])) {
                isEqual = false;
              }
            }
          } else {
            isEqual = false;
          }
        } else {
          isEqual = areArraysEqual(value, oldValue as any[], false);
        }
      } else if (typeof value === 'object') {
        isEqual = deepEqual(value, oldValue);
      } else {
        isEqual = value === oldValue;
      }

      if (!isEqual) {
        dataToSend = {
          ...dataToSend,
          [key]: value,
        };
      }
    }
    return dataToSend;
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Minutes between heartbeat POSTs.
   */
  private readonly _periodInMinutes = 5;

  /**
   * String constant for alarm.
   */
  private readonly _alarmName = 'heartbeat';

  /**
   * Manages use of chrome alarms API while taking into account the service worker lifecycle.
   */
  private readonly _alarmService: AlarmService;

  /**
   * Wraps Heartbeat requests. Only one attempt is made, with no delay or jitter.
   */
  private readonly _fetchService = new FetchService(new ConstantBackOff(0, 0), 1, true);

  /**
   * A template describing the URL to use for device check-in.
   */
  private _heartbeatUrlTemplate?: TemplateString;

  /**
   * Used to determine the device check-in endpoint URL.
   */
  private _provisioningInfo?: ProvisioningInfo;

  /**
   * The device ID as assigned by the cloud when we register a device.
   */
  private _deviceId?: DeviceId;

  /**
   * The last state of each property that was sent as part of the heartbeat.
   * Note that this is an accumalation of all data sent, not the last heartbeat.
   */
  private _previousData?: HeartbeatData;

  /**
   * A temporary variable for the heartbeat data sent to the fetch service.
   * If the request is successful this will be saved to the _previousData variable.
   */
  private _sentData?: HeartbeatData;
}

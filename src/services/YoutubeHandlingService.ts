export type TabLookupTable = Record<number, Record<number, Record<string, string>>>;

/**
 * Provides a central in memory service to store and manage video id related urls and their respective
 * tabs/frames. Urls are stored against string keys in the format {`${tabid}/${frameid}`: urlValue}.
 * FYI frameid 0 denotes the parent/original tab.
 */
export default class YoutubeHandlingService {
  public urlKey: string = 'url';
  public isHandlingYoutubeKey: string = 'isHandlingYoutube';

  /**
   * Save a tab/frame key and videoid value to the video Id lookup cache
   *
   * @param tabId The tab id of the video url
   * @param frameId The frame id of the video url
   * @param videoIds A string storing video url
   */
  public saveKeyToLookup = (tabId: number, frameId: number, key: string, value: string): void => {
    if (key === null || key === '' || value === null || value === '') {
      throw new Error('Could not save K:V to lookup table with empty values');
    }

    if (this._tabLookupMap[tabId] === undefined) {
      this._tabLookupMap[tabId] = {};
    }

    if (this._tabLookupMap[tabId][frameId] === undefined) {
      this._tabLookupMap[tabId][frameId] = {};
    }

    this._tabLookupMap[tabId][frameId][key] = value;
  };

  /**
   * Get tab/frame video url
   * @param tabId The tab id of the video url
   * @param frameId The frame id of the video url
   */
  public getUrlForTabFrame = (tabId: number, frameId: number): string | null => {
    return this._tabLookupMap[tabId]?.[frameId]?.[this.urlKey] ?? null;
  };

  /**
   * Retrieve all of the relevant URLs associated with a specific tab ID
   * @param tabId The tab id to search for
   */
  public getAllUrlsForTab = (tabId: number): string[] => {
    if (this._tabLookupMap[tabId] === undefined) {
      return [];
    }

    return Object.keys(this._tabLookupMap[tabId]).map(
      (frameId) => this._tabLookupMap[tabId][parseInt(frameId)][this.urlKey],
    );
  };

  /**
   * Returns true if the tab and frame id combination is actively handling youtube, false if not. Also returns false if the tab or frame ID cannot be found
   * @param tabId The tab to search for
   * @param frameId The subframe of the tab to search fro
   */
  public getIsHandlingYoutubeForTabFrame = (tabId: number, frameId: number): boolean => {
    if (this._tabLookupMap[tabId] === undefined) {
      return false;
    }

    if (this._tabLookupMap[tabId][frameId] === undefined) {
      return false;
    }

    if (this._tabLookupMap[tabId][frameId][this.isHandlingYoutubeKey] === undefined) {
      return false;
    }

    return this._tabLookupMap[tabId][frameId][this.isHandlingYoutubeKey] === 'true';
  };

  /**
   * Remove all video ids for a given tab
   * @param tabId the tab id to be cleared
   */
  public removeTabEntryFromLookupTable = (tabId: number): void => {
    delete this._tabLookupMap[tabId]; // eslint-disable-line @typescript-eslint/no-dynamic-delete
  };

  public removeAllTabsFromLookupTable(): void {
    this._tabLookupMap = {};
  }

  /**
   * Remove tab/frame from lookup. Rturns false if the entry is not found. True if the entry is correctly deleted.
   * @param tabId The tab id of the video url
   * @param frameId The frame id of the video url
   */
  public removeFrameEntryFromTab = (tabId: number, frameId: number): boolean => {
    if (this._tabLookupMap[tabId] === undefined) {
      return false;
    }

    if (this._tabLookupMap[tabId][frameId] === undefined) {
      return false;
    }

    delete this._tabLookupMap[tabId][frameId]; // eslint-disable-line @typescript-eslint/no-dynamic-delete
    return true;
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * A map where video ids are stored against their parent tabs and frames
   */
  private _tabLookupMap: TabLookupTable = {};
}

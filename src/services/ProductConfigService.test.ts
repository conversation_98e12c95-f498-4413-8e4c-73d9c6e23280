// Added this otherwise there will be a lot of repeat if undefined that are not needed in the tests.
/* eslint-disable @typescript-eslint/no-non-null-assertion */
import 'test-helpers/chrome-api';

import { CloudReportingVersion } from 'constants/CloudReportingVersion';
import PRODUCT_DOCUMENT_TYPES from 'constants/ProductDocumentTypes';
import { initializeApp } from 'firebase/app';
import { connectFirestoreEmulator, getFirestore } from 'firebase/firestore';
import StorageService from 'services/StorageService';
import MockTelemetryService from 'test-helpers/MockTelemetryService';
import { deepCopy } from 'utilities/Helpers';

import { ContentAwareLicenseStatus, IProductConfig } from '../models/IProductConfig';
import LicenseResult from '../models/LicenseResult';
import SerialId from '../models/SerialId';
import ProductConfigService from './ProductConfigService';

const baseProductConfig: IProductConfig = {
  mms: {
    licence: {
      id: 'redacted',
      licenseStartDate: 1619481600000,
      licenseExpiryDate: **********000,
      serial: 'redacted',
    },
    configuration: {},
  },
  cldflt: {
    licence: {
      id: 'redacted',
      licenseStartDate: 1572220800000,
      licenseExpiryDate: **********000,
      serial: 'redacted',
    },
    configuration: {
      config: {
        resource: 'https://swcldfltdevuks1sacc.blob.core.windows.net/configs/redacted/valid',
        sas: '?sv=2021-04-10&se=2025-08-18T13%3A50%3A22Z&sr=c&sp=rl&sig=redacted%3D',
        name: 'redacted',
      },
      blocklist: {
        resource:
          'https://swcldfltdevuks1sacc.blob.core.windows.net/blocklists/Default/%TYPE%-%EPOCH%/%NAME%%SAS%',
        sas: '?sv=2019-02-02&sr=c&sig=redacted%3D&se=2022-10-28T00%3A00%3A00Z&sp=r',
        name: 'redacted',
      },
      clientSettings: {
        resource: 'https://swcldfltdevuks1sacc.blob.core.windows.net/redacted/client-settings',
        sas: '?sv=2019-02-02&sr=b&sig=redacted%3D&se=2022-10-28T00%3A00%3A00Z&sp=r',
        name: 'redacted',
      },
      accesslogs: {
        resource: 'https://swcldfltlogsdevuks1sacc.blob.core.windows.net/redacted/',
        sas: '?sv=2019-02-02&sr=c&sig=redacted%3D&se=2022-10-28T00%3A00%3A00Z&sp=w',
      },
    },
  },
  core: {
    licence: {},
    configuration: {
      directory: {
        resource: 'https://swdirdevuks1sacc.blob.core.windows.net/redacted',
        sas: '?sv=2019-02-02&sr=c&sig=redacted%3D&se=2022-10-28T00%3A00%3A00Z&sp=r',
        polling: 3600,
      },
    },
  },
  cldrpt: {
    licence: {
      id: 'redacted',
      licenseStartDate: **********000,
      licenseExpiryDate: **********000,
      serial: 'redacted',
    },
    configuration: {
      accesslogs: {
        resource: 'https://swcldrptdevuks1logssacc.blob.core.windows.net/redacted/cloudlogs/',
        sas: '?sv=2020-08-04&se=2022-10-28T00%3A00%3A00Z&sr=c&sp=w&sig=redacted3D',
      },
    },
  },
  ca: {
    configuration: {
      domainConfig: {
        resource: {
          guiltByAssociationBlockThreshold: 0.5,
          guiltByAssociationIgnoreAfterCleanImagesThreshold: 0.5,
          guiltByAssociationEnabled: true,
          // never run on list from DMS
          neverRunOnDMS: {},
          defaultRules: {
            swimwear: 0.5,
            gore: 0.5,
            porn: 0.5,
          },
        },
      },
      ia_license: {
        id: 'placeholder-id',
        key: 'placeholder-key',
        status: ContentAwareLicenseStatus.active,
        name: 'Test Image Analyzer License',
        start_date: '**********',
        end_date: '**********',
      },
    },
  },
};
const pastDateMillis = 946684800000; // 2000-01-01T00:00:00Z

const serial = new SerialId('UNCLTEST614NESL8');

// Mock the Firestore response to ensure we get the expected behavior
const mockQuerySnapshot = {
  empty: false,
  forEach: jest.fn((callback) => {
    // Create a mock document with empty cldrpt configuration
    callback({
      id: PRODUCT_DOCUMENT_TYPES.CLOUD_REPORTING,
      data: () => ({
        licence: {
          id: 'test-id',
          licenseStartDate: { toMillis: () => **********000 },
          licenseExpiryDate: { toMillis: () => **********000 },
        },
        // Empty configuration to trigger the error
        configuration: {},
      }),
    });
  }),
  metadata: {
    fromCache: false,
  },
};

describe('ProductConfigService', () => {
  let productConfig: IProductConfig;
  let cachedStorage: StorageService;
  let service: ProductConfigService;

  beforeEach(() => {
    // Reset any changes to the product config between tests.
    productConfig = deepCopy(baseProductConfig);

    // Suppress debug messages in the console.
    jest.spyOn(console, 'debug').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('start', () => {
    let telemetryService: MockTelemetryService;
    let logErrorSpy: any;

    initializeApp({ projectId: 'fzo-stg-sw-device-mgt-us' });
    const firestore = getFirestore();
    connectFirestoreEmulator(firestore, '127.0.0.1', 8080);
    beforeEach(() => {
      jest.clearAllMocks();
      telemetryService = new MockTelemetryService();
      logErrorSpy = jest.spyOn(telemetryService, 'logError');
      service = new ProductConfigService(
        new StorageService('productConfig', undefined),
        serial,
        telemetryService,
      );
    });

    it('logs an error if the cldrpt field is empty in firestore', async () => {
      // Increase the timeout for this test
      jest.setTimeout(15000);

      // Directly call the private method to avoid waiting for Firestore
      (service as any)._parseDownloadedConfig(mockQuerySnapshot);

      // Verify the error was logged
      expect(logErrorSpy).toBeCalledWith(
        'invalid-product-config',
        expect.any(String),
        expect.arrayContaining([{ documentId: 'cldrpt', error: expect.any(String) }]),
      );

      // Reset the timeout
      jest.setTimeout(5000);
    });

    it('includes the Content Aware document in the Firestore query', () => {
      // This test verifies that the PRODUCT_DOCUMENT_TYPES.CONTENT_AWARE constant is used in the query
      expect(PRODUCT_DOCUMENT_TYPES.CONTENT_AWARE).toBeDefined();
      expect(PRODUCT_DOCUMENT_TYPES.CONTENT_AWARE).toEqual('ca');
    });
  });

  describe('using cached data', () => {
    beforeEach(() => {
      productConfig.cldflt!.licence.id = 'cachedTest';

      cachedStorage = new StorageService('productConfig', undefined);
      cachedStorage.set('productConfig', productConfig);

      service = new ProductConfigService(cachedStorage, serial, new MockTelemetryService());
    });

    it('loads cached data that is less than 24 hours old', async () => {
      cachedStorage.set('cachedDateTime', new Date().toISOString());

      expect(service.productConfig).toEqual(productConfig);
    });

    it('loads cached data if start has not been called', async () => {
      cachedStorage.set('cachedDateTime', new Date().toISOString());

      expect(service.productConfig).toEqual(productConfig);
    });

    it('loads cached data if the cache is older than 24 hours', async () => {
      cachedStorage.set('cachedDateTime', new Date(2000, 1, 1));

      expect(service.productConfig).toEqual(productConfig);
    });
  });

  describe('getLicensingInfo', () => {
    /**
     * Helper for setting the storage service after a test has started running.
     */
    const setStorage = (): void => {
      cachedStorage = new StorageService('productConfig', undefined);
      cachedStorage.set('productConfig', productConfig);
      cachedStorage.set('cachedDateTime', new Date().toDateString());
      service = new ProductConfigService(cachedStorage, serial, new MockTelemetryService());
    };

    beforeEach(() => {
      cachedStorage = new StorageService('productConfig', undefined);
      cachedStorage.set('productConfig', productConfig);
      service = new ProductConfigService(cachedStorage, serial, new MockTelemetryService());
    });

    describe('Ingest v2', () => {
      it('returns the correct result for a valid cldflt and empty cldrpt license', async () => {
        productConfig.cldrpt = undefined;
        setStorage();

        const expected = new LicenseResult();
        expected.cldfltLicenseValid = true;
        expected.cldrptVersion = CloudReportingVersion.v2;
        expected.cldrptConfig = {
          accessLogsBlobConfig: {
            resource: 'https://swcldfltlogsdevuks1sacc.blob.core.windows.net/redacted/',
            sas: '?sv=2019-02-02&sr=c&sig=redacted%3D&se=2022-10-28T00%3A00%3A00Z&sp=w',
          },
        };

        const result = service.getLicensingInfo();

        expect(result).toEqual(expected);
      });
    });

    describe('Ingest v3', () => {
      it('returns the correct result for a valid cldflt and cldrpt license', async () => {
        setStorage();

        const expected = new LicenseResult();
        expected.cldfltLicenseValid = true;
        expected.cldrptVersion = CloudReportingVersion.v3;
        expected.cldrptConfig = {
          accessLogsBlobConfig: {
            resource: 'https://swcldrptdevuks1logssacc.blob.core.windows.net/redacted/cloudlogs/',
            sas: '?sv=2020-08-04&se=2022-10-28T00%3A00%3A00Z&sr=c&sp=w&sig=redacted3D',
          },
        };

        const result = service.getLicensingInfo();

        expect(result).toEqual(expected);
      });
      it('returns the correct result for an invalid cldflt and valid cldrpt license', async () => {
        productConfig.cldflt!.licence.licenseExpiryDate = pastDateMillis;
        setStorage();

        const expected = new LicenseResult();
        expected.cldfltLicenseValid = false;
        expected.cldrptVersion = CloudReportingVersion.v3;
        expected.cldrptConfig = {
          accessLogsBlobConfig: {
            resource: 'https://swcldrptdevuks1logssacc.blob.core.windows.net/redacted/cloudlogs/',
            sas: '?sv=2020-08-04&se=2022-10-28T00%3A00%3A00Z&sr=c&sp=w&sig=redacted3D',
          },
        };

        const result = service.getLicensingInfo();

        expect(result).toEqual(expected);
      });
      it('returns the correct result for an empty cldflt and valid cldrpt license', async () => {
        productConfig.cldflt = undefined;
        setStorage();

        const expected = new LicenseResult();
        expected.cldfltLicenseValid = false;
        expected.cldrptVersion = CloudReportingVersion.v3;
        expected.cldrptConfig = {
          accessLogsBlobConfig: {
            resource: 'https://swcldrptdevuks1logssacc.blob.core.windows.net/redacted/cloudlogs/',
            sas: '?sv=2020-08-04&se=2022-10-28T00%3A00%3A00Z&sr=c&sp=w&sig=redacted3D',
          },
        };

        const result = service.getLicensingInfo();

        expect(result).toEqual(expected);
      });
    });

    describe('Ingest v4', () => {
      beforeEach(() => {
        cachedStorage = new StorageService('productConfig', undefined);
        productConfig.cldrpt!.configuration.accessLogsIngestV4 = {
          url: 'https://test/uncl',
          bearerToken: 'testToken',
        };
        cachedStorage.set('productConfig', productConfig);
        service = new ProductConfigService(cachedStorage, serial, new MockTelemetryService());
      });

      it('returns the correct result for a valid cldflt and cldrpt license', async () => {
        setStorage();

        const expected = new LicenseResult();
        expected.cldfltLicenseValid = true;
        expected.cldrptVersion = CloudReportingVersion.v4;
        expected.cldrptConfig = {
          accessLogsIngestV4Config: {
            url: 'https://test/uncl',
            bearerToken: 'testToken',
          },
        };

        const result = service.getLicensingInfo();

        expect(result).toEqual(expected);
      });
      it('returns the correct result for an invalid cldflt and valid cldrpt license', async () => {
        productConfig.cldflt!.licence.licenseExpiryDate = pastDateMillis;
        setStorage();

        const expected = new LicenseResult();
        expected.cldfltLicenseValid = false;
        expected.cldrptVersion = CloudReportingVersion.v4;
        expected.cldrptConfig = {
          accessLogsIngestV4Config: {
            url: 'https://test/uncl',
            bearerToken: 'testToken',
          },
        };

        const result = service.getLicensingInfo();

        expect(result).toEqual(expected);
      });
      it('returns the correct result for an empty cldflt and valid cldrpt license', async () => {
        productConfig.cldflt = undefined;
        setStorage();

        const expected = new LicenseResult();
        expected.cldfltLicenseValid = false;
        expected.cldrptVersion = CloudReportingVersion.v4;
        expected.cldrptConfig = {
          accessLogsIngestV4Config: {
            url: 'https://test/uncl',
            bearerToken: 'testToken',
          },
        };

        const result = service.getLicensingInfo();

        expect(result).toEqual(expected);
      });
    });

    describe('Reporting disabled', () => {
      it('returns the correct result for a valid cldflt and invalid cldrpt license', async () => {
        productConfig.cldrpt!.licence.licenseExpiryDate = pastDateMillis;
        setStorage();

        const expected = new LicenseResult();
        expected.cldfltLicenseValid = true;
        expected.cldrptVersion = CloudReportingVersion.disabled;

        const result = service.getLicensingInfo();

        expect(result).toEqual(expected);
      });
      it('returns the correct result for an empty cldflt and empty cldrpt license', async () => {
        productConfig.cldflt = undefined;
        productConfig.cldrpt = undefined;
        setStorage();

        const expected = new LicenseResult();
        expected.cldfltLicenseValid = false;
        expected.cldrptVersion = CloudReportingVersion.disabled;

        const result = service.getLicensingInfo();

        expect(result).toEqual(expected);
      });
      it('returns the correct result for an invalid cldflt and cldrpt license', async () => {
        productConfig.cldflt!.licence.licenseExpiryDate = pastDateMillis;
        productConfig.cldrpt!.licence.licenseExpiryDate = pastDateMillis;
        setStorage();

        const expected = new LicenseResult();
        expected.cldfltLicenseValid = false;
        expected.cldrptVersion = CloudReportingVersion.disabled;

        const result = service.getLicensingInfo();

        expect(result).toEqual(expected);
      });
      it('returns unlicensed if the product config is not yet loaded', async () => {
        cachedStorage.delete('productConfig');

        const expected = new LicenseResult();
        expected.cldfltLicenseValid = false;
        expected.cldrptVersion = CloudReportingVersion.disabled;

        const result = service.getLicensingInfo();

        expect(result).toEqual(expected);
      });
    });
  });

  describe('_parseDownloadedConfig', () => {
    let telemetryService: MockTelemetryService;
    let logErrorSpy: jest.SpyInstance;
    let mockQuerySnapshot: any;
    let mockDocData: any;
    let mockDoc: any;
    let productConfigParsedSpy: jest.SpyInstance;

    beforeEach(() => {
      telemetryService = new MockTelemetryService();
      logErrorSpy = jest.spyOn(telemetryService, 'logError');

      // Create a mock for the QuerySnapshot
      mockDocData = {
        licence: {
          id: 'test-id',
          licenseStartDate: { toMillis: () => **********000 },
          licenseExpiryDate: { toMillis: () => **********000 },
          serial: 'test-serial',
        },
        configuration: {
          enabled: true,
          settings: {
            someFeature: true,
            anotherSetting: 'value',
          },
        },
      };

      mockDoc = {
        id: PRODUCT_DOCUMENT_TYPES.CONTENT_AWARE,
        data: () => mockDocData,
      };

      mockQuerySnapshot = {
        empty: false,
        forEach: jest.fn((callback) => {
          callback(mockDoc);
        }),
        metadata: {
          fromCache: false,
        },
      };

      cachedStorage = new StorageService('productConfig', undefined);
      service = new ProductConfigService(cachedStorage, serial, telemetryService);

      // Spy on the _productConfigParsed event
      productConfigParsedSpy = jest.spyOn((service as any)._productConfigParsed, 'dispatch');
    });

    it('correctly processes a Content Aware document', () => {
      // Call the private method
      (service as any)._parseDownloadedConfig(mockQuerySnapshot);

      // Verify the _productConfigParsed event was dispatched with the correct data
      expect(productConfigParsedSpy).toHaveBeenCalledTimes(1);

      const parsedConfig = productConfigParsedSpy.mock.calls[0][0];
      expect(parsedConfig).toBeDefined();
      expect(parsedConfig.ca).toBeDefined();
      expect(parsedConfig.ca.configuration.enabled).toBe(true);
      expect(parsedConfig.ca.configuration.settings.someFeature).toBe(true);
      expect(parsedConfig.ca.configuration.settings.anotherSetting).toBe('value');
    });

    it('logs an error when the Content Aware configuration is empty', () => {
      // Modify the mock data to have an empty configuration
      mockDocData.configuration = undefined;

      // Call the private method
      (service as any)._parseDownloadedConfig(mockQuerySnapshot);

      // Verify the error was logged
      expect(logErrorSpy).toHaveBeenCalledWith(
        'invalid-product-config',
        expect.any(String),
        expect.arrayContaining([
          { documentId: PRODUCT_DOCUMENT_TYPES.CONTENT_AWARE, error: expect.any(String) },
        ]),
      );
    });

    it('handles multiple document types including Content Aware', () => {
      // Create mocks for multiple documents
      const cldfltDoc = {
        id: PRODUCT_DOCUMENT_TYPES.CLOUD_FILTER,
        data: () => ({
          licence: {
            id: 'cldflt-id',
            licenseStartDate: { toMillis: () => **********000 },
            licenseExpiryDate: { toMillis: () => **********000 },
          },
          configuration: {},
        }),
      };

      const caDoc = {
        id: PRODUCT_DOCUMENT_TYPES.CONTENT_AWARE,
        data: () => ({
          licence: {
            id: 'ca-id',
            licenseStartDate: { toMillis: () => **********000 },
            licenseExpiryDate: { toMillis: () => **********000 },
          },
          configuration: {
            enabled: true,
          },
        }),
      };

      // Update the mock QuerySnapshot to include both documents
      mockQuerySnapshot.forEach = jest.fn((callback) => {
        callback(cldfltDoc);
        callback(caDoc);
      });

      // Call the private method
      (service as any)._parseDownloadedConfig(mockQuerySnapshot);

      // Verify the _productConfigParsed event was dispatched with the correct data
      expect(productConfigParsedSpy).toHaveBeenCalledTimes(1);

      const parsedConfig = productConfigParsedSpy.mock.calls[0][0];
      expect(parsedConfig).toBeDefined();
      expect(parsedConfig.cldflt).toBeDefined();
      expect(parsedConfig.ca).toBeDefined();
      expect(parsedConfig.ca.configuration.enabled).toBe(true);
    });

    it('handles empty QuerySnapshot', () => {
      // Create an empty QuerySnapshot
      mockQuerySnapshot.empty = true;

      // Call the private method
      (service as any)._parseDownloadedConfig(mockQuerySnapshot);

      // Verify the _productConfigParsed event was not dispatched
      expect(productConfigParsedSpy).not.toHaveBeenCalled();
    });
  });

  describe('getContentAwareConfig', () => {
    /**
     * Helper for setting the storage service after a test has started running.
     */
    const setStorage = (): void => {
      cachedStorage = new StorageService('productConfig', undefined);
      cachedStorage.set('productConfig', productConfig);
      cachedStorage.set('cachedDateTime', new Date().toDateString());
      service = new ProductConfigService(cachedStorage, serial, new MockTelemetryService());
    };

    beforeEach(() => {
      cachedStorage = new StorageService('productConfig', undefined);
      cachedStorage.set('productConfig', productConfig);
      service = new ProductConfigService(cachedStorage, serial, new MockTelemetryService());
    });

    it('returns the Content Aware configuration when it exists and is valid', () => {
      setStorage();

      const result = service.getContentAwareConfig();

      expect(result).toEqual(productConfig.ca);
    });

    it('returns undefined when the Content Aware configuration does not exist', () => {
      productConfig.ca = undefined;
      setStorage();

      const result = service.getContentAwareConfig();

      expect(result).toBeUndefined();
    });

    it('returns undefined when the Content Aware configuration exists but has no configuration', () => {
      productConfig.ca!.configuration = undefined as any;
      setStorage();

      const result = service.getContentAwareConfig();

      expect(result).toBeUndefined();
    });

    it('returns undefined when the Content Aware license has expired', () => {
      productConfig.ca!.configuration.ia_license.end_date = (pastDateMillis / 1000).toString();
      setStorage();

      const result = service.getContentAwareConfig();

      expect(result).toBeUndefined();
    });

    it('returns undefined when the Content Aware license start date is in the future', () => {
      jest.useFakeTimers();

      // mock the current date to be before the start date
      jest.setSystemTime(new Date(2023, 0, 1)); // January 1, 2023

      // December 31, 2023 in seconds since epoch
      productConfig.ca!.configuration.ia_license.start_date = '**********'; // December 31, 2024
      // December 31, 2024 in seconds since epoch
      productConfig.ca!.configuration.ia_license.end_date = '**********'; // December 31, 2023
      setStorage();

      const result = service.getContentAwareConfig();
      expect(result).toBeUndefined();
    });

    it('returns undefined when the Content Aware license end date is in the past', () => {
      jest.useFakeTimers();

      // mock the current date to be after the end date
      jest.setSystemTime(new Date(2023, 0, 1)); // January 1, 2023
      // December 31, 2021 in seconds since epoch
      productConfig.ca!.configuration.ia_license.start_date = '**********';
      // December 31, 2022 in seconds since epoch
      productConfig.ca!.configuration.ia_license.end_date = '**********';
      setStorage();

      const result = service.getContentAwareConfig();
      expect(result).toBeUndefined();
    });

    it('returns undefined when the Content Aware license start date is after the end date', () => {
      jest.useFakeTimers();

      // mock the current date to be after the end date
      jest.setSystemTime(new Date(2023, 0, 1)); // January 1, 2023
      // December 31, 2024 in seconds since epoch
      productConfig.ca!.configuration.ia_license.start_date = '**********'; // December 31, 2024
      // December 31, 2023 in seconds since epoch
      productConfig.ca!.configuration.ia_license.end_date = '**********'; // December 31, 2023
      setStorage();

      const result = service.getContentAwareConfig();
      expect(result).toBeUndefined();
    });

    it('returns the Content Aware configuration when the license date range includes the current date', () => {
      jest.useFakeTimers();

      // mock the current date to be within the license date range
      jest.setSystemTime(new Date(2023, 0, 1)); // January 1, 2023
      // December 31, 2022 in seconds since epoch
      productConfig.ca!.configuration.ia_license.start_date = '**********'; // January 1, 2021
      // December 31, 2024 in seconds since epoch
      productConfig.ca!.configuration.ia_license.end_date = '**********'; // December 31, 2024
      setStorage();

      const result = service.getContentAwareConfig();
      expect(result).toBeObject();
    });

    it('returns the Content Aware configuration when the start date is undefined but end date is defined', () => {
      jest.useFakeTimers();
      // mock the current date to be within the license date range
      jest.setSystemTime(new Date(2023, 0, 1)); // January

      productConfig.ca!.configuration.ia_license.start_date = undefined; // start date is undefined
      // December 31, 2024 in seconds since epoch
      productConfig.ca!.configuration.ia_license.end_date = '**********';
    });

    it('returns the Content Aware configuration when both start date and end date are undefined', () => {
      jest.useFakeTimers();
      // mock the current date to be within the license date range
      jest.setSystemTime(new Date(2023, 0, 1)); // January

      productConfig.ca!.configuration.ia_license.start_date = undefined; // start date is undefined
      productConfig.ca!.configuration.ia_license.end_date = undefined; // end date is undefined
      setStorage();

      const result = service.getContentAwareConfig();
      expect(result).toBeObject();
    });
  });
});

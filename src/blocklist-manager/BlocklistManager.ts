import Blocklist from 'guardian/Blocklist';
import { BlocklistConfig } from 'models/IProductConfig';
import BlocklistDownloader, { Context } from './BlocklistDownloader';
import ITelemetryService from 'services/ITelemetryService';
import StandaloneEvent from 'utilities/StandaloneEvent';
import BlocklistStatus from 'constants/BlocklistStatus';
import { TelemetryEventType } from '../constants/TelemetryEventType';

/**
 * Defines the structure of data which will be written to the blocklist cache.
 */
export interface BlocklistCache {
  /**
   * Identifies the blocklist version.
   * This is typically a Unix timestamp (in seconds) specifying when it was published.
   */
  epoch: number;

  /**
   * The string used to construct a URL for downloading the blocklist.
   * This comes from product config. It's stored so that we can detect situations where we've been
   *  changed to a completely different type of blocklist, meaning patching would not work.
   */
  templateUrl: string;

  /**
   * The files containing the blocklist data.
   * Each key is the name of a file, and each value is the raw contents of the file as a string.
   */
  files: Record<string, string>;
}

/**
 * Keeps the local blocklist up to date by caching and applying downloads and patches.
 */
export default class BlocklistManager {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Initialise a new instance of the blocklist manager which will operate on the given blocklist.
   *
   * @param telemetryService An object which will send telemetry to the cloud.
   * @param blocklist The blocklist instance which will be managed by this object. It will be
   *  populated with the blocklist data loaded from cache or downloaded from the cloud.
   * @param storageName The name of the local storage location used to cache the blocklist on disk.
   *  NOTE: If this is changed in production then ensure caches under the old name get deleted,
   *  otherwise we could take up a significant amount of storage space indefinitely on customer
   *  devices.
   */
  public constructor(
    telemetryService: ITelemetryService,
    blocklist: Blocklist,
    storageName: string = 'standardBlocklist',
  ) {
    this._telemetryService = telemetryService;
    this.blocklist = blocklist;
    this._storageName = storageName;

    this._blocklistDownloader.onSuccess.addListener(this._onDownloadSuccess);
    this._blocklistDownloader.onFailure.addListener(this._onDownloadFailure);
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Get the current blocklist config, if there is one.
   * This will be undefined if we haven't received the config yet, or it's been cleared, e.g.
   *  because the customer is unlicensed.
   */
  public get blocklistConfig(): BlocklistConfig | undefined {
    return this._blocklistConfig;
  }

  // -----------------------------------------------------------------------------------------------
  // Events.

  /**
   * Triggered when the blocklist has been loaded from cache, or downloaded/patched from the cloud.
   * The parameter is the name of this class. This is used by CustomerDataController to determine
   *  which items have loaded.
   *
   * @todo Caller should possibly use the Blocklist status change event instead?
   */
  public readonly onBlocklistLoaded: StandaloneEvent<[string]> = new StandaloneEvent<[string]>();

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * If there is a blocklist cached on disk, then load it into the in-memory blocklist.
   * This will do nothing if there is no cached blocklist.
   * The cache will be cleared if it's invalid.
   *
   * @param force If false, the cached blocklist won't be loaded again if the same blocklist already
   *  appears to be in memory. If true, the cached blocklist will be loaded regardless. This is
   *  useful if it's suspected that the in-memory blocklist is corrupted.
   * @returns A promise which resolves when the cache has been loaded. It will reject if a cache
   *  operation failed, or the cached blocklist was invalid.
   *
   * @note This should generally be called once during start-up, before setConfig(). It may be
   *  called again internally if we need to revert a failed blocklist update.
   */
  public readonly loadFromCache = async (force: boolean = false): Promise<void> => {
    const cache = await this._readCache();
    if (cache === undefined) {
      console.debug('BlocklistManager - No blocklist data available in cache.');
      return;
    }

    // If we already have the same blocklist in memory then don't overwrite it unless forced.
    if (
      !force &&
      cache.epoch === this.blocklist.epoch &&
      cache.templateUrl === this.blocklist.templateUrl
    ) {
      console.debug(
        'BlocklistManager - Skipping blocklist cache. The same blocklist is already in memory.',
      );
      return;
    }

    try {
      const start = performance.now();
      await this.blocklist.loadFromBlocklistFilesAsync(
        cache.files,
        cache.epoch,
        cache.templateUrl,
        'cache',
      );
      const loadDurationMs = Math.round(performance.now() - start);
      console.debug(
        `BlocklistManager - Loaded blocklist ${cache.epoch} from cache. Took ${loadDurationMs} ms.`,
      );
      this._telemetryService.logEvent(TelemetryEventType.BlocklistLoaded, {
        source: 'cache',
        toEpoch: cache.epoch,
        toEpochDate: BlocklistManager.epochToDateString(cache.epoch),
        loadDurationMs,
      });

      this.onBlocklistLoaded.dispatch(this.constructor.name);
    } catch (e: any) {
      // The cache is probably bad so get rid of it.
      this._clearCache();

      this._telemetryService.logError(TelemetryEventType.BlocklistLoadFailed, e, {
        source: 'cache',
        toEpoch: cache.epoch,
        toEpochDate: BlocklistManager.epochToDateString(cache.epoch),
      });
      throw e;
    }
  };

  /**
   * Specify which blocklist to download, and where to download it from.
   * This triggers a full or patch download as appropriate if the specified blocklist is not already
   *  in memory or already being downloaded.
   * The download happens asynchronously in the background. The caller can receive a notification
   *  by subscribing to the status change event on the Blocklist instance.
   * This function does not load a blocklist from cache before starting a download. It is the
   *  caller's responsibility to ensure loadFromCache() was called before this, ideally once during
   *  start-up.
   *
   * @param blocklistConfig Specifies which blocklist to download, and where to download it from.
   *  This information is not cached by this class. It is assumed that it will be cached elsewhere
   *  by the caller.
   */
  public readonly setConfig = (blocklistConfig: BlocklistConfig): void => {
    // If we've already got this blocklist loaded then there's nothing to do.
    if (
      this.blocklist.epoch === parseInt(blocklistConfig.name) &&
      this.blocklist.templateUrl === blocklistConfig.resource
    ) {
      console.debug(
        `BlocklistManager - Blocklist ${blocklistConfig.name} is already loaded. No new download necessary.`,
      );

      // Ensure our stored blocklist config is up-to-date in case the SAS token has changed.
      this._blocklistConfig = blocklistConfig;

      // The CustomerDataController needs to know we've handled the call successfully.
      // TODO: Find a less brittle way to do this in future.
      this.onBlocklistLoaded.dispatch(this.constructor.name);

      // Cancel any in-flight or pending download. This is to guard against a situation where the
      //  blocklist changes back to an old version while we're in the process of downloading a new
      //  one. In that case, we want to keep the old one.
      if (this._blocklistDownloader.isDownloading || this._downloadDelay !== undefined) {
        console.debug('BlocklistManager - Cancelling unnecessary blocklist download.');
        this._blocklistDownloader.cancel();
        clearTimeout(this._downloadDelay);
        this._downloadDelay = undefined;
      }
      return;
    }

    // If we're already downloading exactly the same blocklist then just let that continue.
    // Note: Checking the SAS token is important. If it's changed then ensure we start the download
    //  again in case the new one gives us access we didn't have before.
    if (
      (this._downloadDelay !== undefined || this._blocklistDownloader.isDownloading) &&
      this._blocklistConfig?.name === blocklistConfig.name &&
      this._blocklistConfig?.resource === blocklistConfig.resource &&
      this._blocklistConfig?.sas === blocklistConfig.sas
    ) {
      console.debug(
        `BlocklistManager - Download for blocklist ${blocklistConfig.name} is already pending or in progress.`,
      );
      return;
    }

    this._blocklistConfig = blocklistConfig;

    // If the blocklist epoch is from within the last 24 hours then download it immediately.
    const timeSinceBlocklistEpoch = Date.now() - parseInt(blocklistConfig.name) * 1000;
    if (timeSinceBlocklistEpoch <= 86400000) {
      this._downloadBlocklist(false);
      return;
    }

    // We've been asked to download a blocklist from more than 24 hours ago.
    // This may mean our product config cache is stale. Wait for a little while before downloading
    //  it in case we get a more up-to-date epoch. This is done to avoid pointlessly downloading an
    //  old blocklist.
    console.debug(
      'BlocklistManager - Specified blocklist is out-of-date. Delaying download briefly in case a newer blocklist is received.',
    );
    this._scheduleDownload(false, 10000);
  };

  /**
   * Discard the in-memory blocklist, stop any downloads, and clear the blocklist cache.
   * This will typically be used if the customer is not licensed.
   */
  public readonly clear = async (): Promise<void> => {
    clearTimeout(this._downloadDelay);
    this._downloadDelay = undefined;
    this._blocklistDownloader.cancel();
    this._clearCache();
    await this.blocklist.clear();
  };

  // -----------------------------------------------------------------------------------------------
  // Event handlers.

  /**
   * Handle a successful blocklist download.
   * This will load a full blocklist or apply a patch, as appropriate.
   *
   * @param url The URL of the manifest file describing the blocklist.
   * @param context Additional information about the download.
   * @param files The contents of the blocklist download. Keys are filenames, values are file data.
   */
  private readonly _onDownloadSuccess = async (
    url: URL,
    context: Context,
    files: Record<string, string>,
  ): Promise<void> => {
    if (context.fromEpoch === undefined) {
      await this._processFullDownload(url, context, files);
    } else {
      let patched = false;
      try {
        patched = await this._processPatchDownload(url, context, files);
      } catch (e: any) {
        console.warn('BlocklistManager - Failed to patch blocklist.', e);
      }

      // If patching failed for any reason then download the equivalent full blocklist.
      // Delay briefly first to ensure we don't hog resources.
      if (!patched) {
        this._scheduleDownload(true, 1000);
      }
    }
  };

  /**
   * Handle a failed blocklist download.
   * This will log the error and try again.
   *
   * @param url The URL of the manifest file describing the blocklist.
   * @param context Additional information about the download.
   * @param reason A human readable description of what went wrong.
   */
  private readonly _onDownloadFailure = (url: URL, context: Context, reason: string): void => {
    let delay = 1000;
    if (context.fromEpoch === undefined) {
      // We should never reach here as the blocklist downloader should keep retrying a full download
      //  indefinitely. Wait a long time before starting again so that we don't short-circuit the
      //  back-off mechanism.
      console.error(
        `BlocklistManager - Failed to download full blocklist ${context.toEpoch}. Reason: ${reason}`,
      );
      delay = 1800000;
    } else {
      console.debug(
        `BlocklistManager - Failed to download blocklist patch ${context.fromEpoch} to ${context.toEpoch}. Reason: ${reason}`,
      );
    }

    this._telemetryService.logError(TelemetryEventType.BlocklistDownloadFailed, reason, {
      fromEpoch: context.fromEpoch,
      fromEpochDate:
        context.fromEpoch === undefined
          ? undefined
          : BlocklistManager.epochToDateString(context.fromEpoch),
      toEpoch: context.toEpoch,
      toEpochDate: BlocklistManager.epochToDateString(context.toEpoch),
      manifestUrl: url.toString(),
    });

    this._scheduleDownload(true, delay);
  };

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Load the contents of a full blocklist download into memory.
   * This will also cache the blocklist to disk for future use.
   * If loading fails for any reason, this will revert to the last cached blocklist (if possible),
   *  and schedule another attempt.
   *
   * @param url The URL of the manifest file describing the blocklist.
   * @param context Additional information about the download. The fromEpoch property must be
   *  undefined.
   * @param files The contents of the blocklist download. Keys are filenames, values are file data.
   */
  private readonly _processFullDownload = async (
    url: URL,
    context: Context,
    files: Record<string, string>,
  ): Promise<void> => {
    if (context.fromEpoch !== undefined) {
      throw new Error('fromEpoch should be undefined for a full blocklist download.');
    }

    // If any files are missing from the download then log a warning but carry on. It's possible
    //  that some files will be legitimately removed from the blocklist in future. That won't
    //  necessary break the extension functionality.
    const missing = Blocklist.expectedFullBlocklistFiles.filter((f) => !(f in files));
    if (missing.length > 0) {
      console.warn(
        `One or more files may be missing from the full blocklist download: ${missing.join(',')}` +
          `\nFiles received: ${Object.keys(files).join(', ')}`,
      );
    }

    let loadDurationMs: number;
    try {
      const start = performance.now();
      await this.blocklist.loadFromBlocklistFilesAsync(
        files,
        context.toEpoch,
        context.templateUrl,
        'download',
      );
      loadDurationMs = Math.round(performance.now() - start);
    } catch (e: any) {
      console.error(`BlocklistManager - Failed to load blocklist ${context.toEpoch}.`, e);

      this._telemetryService.logError(TelemetryEventType.BlocklistLoadFailed, e, {
        source: 'download',
        toEpoch: context.toEpoch,
        toEpochDate: BlocklistManager.epochToDateString(context.toEpoch),
        manifestUrl: url.toString(),
      });

      // If loading failed part way through, then the blocklist may be in an invalid state. Revert
      //  back to the last cached blocklist if possible.
      this.loadFromCache().catch(console.warn);

      // Wait for a long time before trying to download the same blocklist again. We don't want to
      //  waste system/network resources if we're just going to get the same result. This timer
      //  will be skipped if we receive a different blocklist config.
      this._scheduleDownload(true, 1800000);
      return;
    }

    this.onBlocklistLoaded.deferDispatch(this.constructor.name);

    console.info(
      `BlocklistManager - Downloaded blocklist ${context.toEpoch}. ` +
        `Loading it into memory took ${loadDurationMs} ms.`,
    );
    this._telemetryService.logEvent(TelemetryEventType.BlocklistLoaded, {
      source: 'download',
      toEpoch: context.toEpoch,
      toEpochDate: BlocklistManager.epochToDateString(context.toEpoch),
      manifestUrl: url.toString(),
      loadDurationMs,
    });

    this._writeCache({ epoch: context.toEpoch, templateUrl: context.templateUrl, files });
  };

  /**
   * Update our local blocklist from the given patch download.
   * If patching fails for any reason, this will revert to the last cached blocklist, if possible.
   * The caller should schedule a full download afterwards.
   *
   * @param url The URL of the manifest file describing the blocklist.
   * @param context Additional information about the download. The fromEpoch property must be
   *  defined.
   * @param diffs The contents of the blocklist patch. Keys are filenames, values are file contents.
   * @return Returns a promise which resolves to a boolean indicate if the patch succeeded (true) or
   *  failed (false). It is not normally expected to reject, but if it does then assume the patch
   *  failed.
   *
   * @todo Write the result of the blocklist patch back to storage. This requires some changes to
   *  the blocklist components to allow us to get the blocklist file representation back out.
   */
  private readonly _processPatchDownload = async (
    url: URL,
    context: Context,
    diffs: Record<string, string>,
  ): Promise<boolean> => {
    if (context.fromEpoch === undefined) {
      throw new Error('fromEpoch should must be defined for a patch download.');
    }

    if (this.blocklist.status !== BlocklistStatus.Ready) {
      console.error('BlocklistManager - Cannot apply patch as blocklist is not currently loaded.');
      return false;
    }

    if (
      this.blocklist.epoch !== context.fromEpoch ||
      this.blocklist.templateUrl !== context.templateUrl
    ) {
      console.warn('BlocklistManager - Downloaded patch is no longer applicable.');
      return false;
    }

    // Apply the patch and load the result into memory.
    let patchedFilenames: Set<string>;
    let applyDurationMs: number;
    try {
      const start = performance.now();
      patchedFilenames = await this.blocklist.patchFromBlocklistFilesAsync(
        diffs,
        context.fromEpoch,
        context.toEpoch,
        context.templateUrl,
      );
      applyDurationMs = Math.round(performance.now() - start);
    } catch (e: any) {
      console.warn(
        `BlocklistManager - Failed to patch blocklist from ${context.fromEpoch} to ${context.toEpoch}.`,
        e,
      );

      this._telemetryService.logError(TelemetryEventType.BlocklistPatchFailed, e, {
        fromEpoch: context.fromEpoch,
        fromEpochDate: BlocklistManager.epochToDateString(context.fromEpoch),
        toEpoch: context.toEpoch,
        toEpochDate: BlocklistManager.epochToDateString(context.toEpoch),
        manifestUrl: url.toString(),
      });

      // If loading the patched result failed part way through then the blocklist may be in an
      //  invalid state. Revert back to the last cached blocklist if possible.
      if (this.blocklist.status !== BlocklistStatus.Ready) {
        this.loadFromCache().catch(console.warn);
      }
      return false;
    }

    this.onBlocklistLoaded.deferDispatch(this.constructor.name);

    console.info(
      `BlocklistManager - Patched blocklist from ${context.fromEpoch} to ${context.toEpoch}. ` +
        `Modified ${patchedFilenames.size} files. ` +
        `Applying the patch took ${applyDurationMs} ms. `,
    );
    this._telemetryService.logEvent(TelemetryEventType.BlocklistLoaded, {
      source: 'patch',
      fromEpoch: context.fromEpoch,
      fromEpochDate: BlocklistManager.epochToDateString(context.fromEpoch),
      toEpoch: context.toEpoch,
      toEpochDate: BlocklistManager.epochToDateString(context.toEpoch),
      manifestUrl: url.toString(),
      applyDurationMs,
      numFilesPatched: patchedFilenames.size,
    });

    // TODO: Save the result of the patch so it can be loaded on the next run.
    return true;
  };

  /**
   * Read the blocklist data from the on-disk cache.
   * This deliberately doesn't have a memory-backed copy. It will go out to disk every time this is
   *  called. This is because we don't want to keep a whole copy of the blocklist in memory any
   *  longer than we need to.
   *
   * @return Returns a promise which resolves to the cached blocklist data, if it was successfully
   *  read from storage. Resolves to undefined if there was no blocklist in the cache, the data
   *  appears incomplete, or the storage operation failed. The promise is never expected to reject.
   */
  private readonly _readCache = async (): Promise<BlocklistCache | undefined> => {
    let cache: BlocklistCache | undefined;
    try {
      cache = (await chrome.storage.local.get(this._storageName))[this._storageName] as
        | BlocklistCache
        | undefined;
    } catch (e: any) {
      console.warn('BlocklistManager - Failed to read blocklist cache from local storage.', e);
      return;
    }

    if (cache == null) {
      return undefined;
    }

    if (cache.epoch === undefined || cache.templateUrl === undefined || cache.files === undefined) {
      console.warn('BlocklistManager - Ignoring incomplete blocklist cache.');
      return undefined;
    }
    return cache;
  };

  /**
   * Write a full set of blocklist files to the local cache on disk.
   * The write operation happens asynchronously in the background. It will not throw on failure.
   *
   * @param cache The blocklist data to write to the cache.
   */
  private readonly _writeCache = (cache: BlocklistCache): void => {
    chrome.storage.local.set({ [this._storageName]: cache }).catch((e) => {
      console.warn('BlocklistManager - Failed to save blocklist to cache.', e);
    });
  };

  /**
   * Delete the cached blocklist from the on-disk cache.
   * The storage operation happens asynchronously in the background. This will not throw on failure.
   */
  private readonly _clearCache = (): void => {
    chrome.storage.local.remove(this._storageName).catch((e) => {
      console.warn('BlocklistManager - Failed to clear blocklist cache.', e);
    });
  };

  /**
   * Set a one-off timer to download the blocklist after a delay.
   *
   * @param forceFullDownload If true, a full blocklist will be downloaded. If false, a patch may be
   *  downloaded instead, if we already have a blocklist in memory.
   * @param delay The amount of time to wait (in milliseconds) before downloading the blocklist.
   *
   * @see _downloadBlocklist()
   */
  private readonly _scheduleDownload = (forceFullDownload: boolean, delay: number): void => {
    clearTimeout(this._downloadDelay);
    this._downloadDelay = setTimeout(() => {
      this._downloadBlocklist(forceFullDownload);
    }, delay);
  };

  /**
   * Start downloading a full or patch blocklist in the background.
   * By default, this will download a patch if possible, or a full blocklist if not. This can be
   *  overridden using the forceFullDownload parameter.
   *
   * @param forceFullDownload If true, a full blocklist will be downloaded, even if a patch seems to
   *  be possible. This is useful if patching was already tried but it failed.
   *
   * @see _scheduleDownload()
   */
  private readonly _downloadBlocklist = (forceFullDownload: boolean): void => {
    // Ensure there are no other delayed download attempts pending.
    clearTimeout(this._downloadDelay);
    this._downloadDelay = undefined;

    if (this._blocklistConfig === undefined) {
      console.warn('BlocklistManager - Cannot start download without blocklist config.');
      return;
    }

    const context: Context = {
      templateUrl: this._blocklistConfig.resource,
      toEpoch: parseInt(this._blocklistConfig.name),
    };

    let url: URL;
    let retryForever = false;

    // Decide whether to download a full blocklist or a patch.
    if (
      forceFullDownload ||
      this.blocklist.epoch === undefined ||
      !BlocklistManager.isBlocklistPatchable(this.blocklist, this._blocklistConfig)
    ) {
      url = BlocklistManager.makeFullManifestUrl(this._blocklistConfig);
      // If any failure occurs when downloading a full blocklist, then we'll just keep trying until
      //  it succeeds, or we receive a new configuration.
      retryForever = true;
      console.debug(
        `BlocklistManager - Starting to download full blocklist ${this._blocklistConfig.name}.`,
        url.toString(),
      );
    } else {
      url = BlocklistManager.makeDiffManifestUrl(this._blocklistConfig, this.blocklist.epoch);
      context.fromEpoch = this.blocklist.epoch;
      // If a patch isn't found then we'll stop and fall-back on downloading the full blocklist.
      // This only applies to client-side errors. It will still retry if the device is offline.
      retryForever = false;
      console.debug(
        `BlocklistManager - Starting to download blocklist patch ${this.blocklist.epoch} to ${this._blocklistConfig.name}.`,
        url.toString(),
      );
    }

    // Download the blocklist in the background. The result will be received by _onDownloadSuccess
    //  or _onDownloadFailure.
    this._blocklistDownloader.download(url, context, retryForever).catch(console.warn);
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Construct the URL for downloading the manifest file of a full blocklist.
   *
   * @param blocklistConfig The blocklist config from which to generate a manifest URL.
   * @returns The URL for the manifest file of a full blocklist.
   * @throws Throws an error if a valid URL could not be constructed from the given config.
   */
  public static readonly makeFullManifestUrl = (blocklistConfig: BlocklistConfig): URL => {
    return new URL(
      blocklistConfig.resource
        .replace('%TYPE%', 'Blocklist')
        .replace('%EPOCH%', blocklistConfig.name)
        .replace('%NAME%', 'manifest.json')
        .replace('%SAS%', blocklistConfig.sas),
    );
  };

  /**
   * Construct the URL for downloading the manifest file of a blocklist diff.
   *
   * @param blocklistConfig The blocklist config from which to generate a manifest URL.
   * @param fromEpoch The epoch of the existing blocklist we are patching from.
   * @returns The URL for the manifest file of a blocklist diff.
   */
  public static readonly makeDiffManifestUrl = (
    blocklistConfig: BlocklistConfig,
    fromEpoch: number,
  ): URL => {
    return new URL(
      blocklistConfig.resource
        .replace('%TYPE%', 'Diff')
        .replace('%EPOCH%', `${fromEpoch}-${blocklistConfig.name}`)
        .replace('%NAME%', 'manifest.json')
        .replace('%SAS%', blocklistConfig.sas),
    );
  };

  /**
   * Check if it's possible to patch an existing blocklist using a new config.
   *
   * @param blocklist The existing blocklist which needs to be updated.
   * @param config The configuration for the new blocklist which we want to reach.
   * @returns True if a patch is possible, or false if not.
   */
  public static readonly isBlocklistPatchable = (
    blocklist: Blocklist,
    config: BlocklistConfig,
  ): boolean => {
    const fromEpoch = blocklist.epoch;
    const toEpoch = parseInt(config.name);

    if (fromEpoch === undefined || blocklist.templateUrl === undefined) {
      // No blocklist is loaded so we can't patch to it.
      return false;
    }

    if (blocklist.templateUrl !== config.resource) {
      // The template URL has changed, which means it may be an entirely different type of
      //  blocklist. It isn't safe to patch in this case.
      return false;
    }

    if (fromEpoch > toEpoch) {
      // We cannot patch from new to old.
      return false;
    }

    if (toEpoch - fromEpoch > 2592000) {
      // Blocklist patches only go back 1 month. We cannot patch further than that.
      return false;
    }

    return true;
  };

  /**
   * Convert a Unix timestamp (in seconds) to an ISO formatted date and time string.
   *
   * @param epoch A Unix timestamp (in seconds).
   * @return An ISO formatted date and time string.
   */
  public static readonly epochToDateString = (epoch: number): string => {
    return new Date(epoch * 1000).toISOString();
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Handles downloading the blocklist manifest and associated files.
   */
  private readonly _blocklistDownloader = new BlocklistDownloader();

  /**
   * The name of the local storage location used to cache the blocklist on disk.
   * For the structure of the data stored in the cache, see BlocklistCache.
   */
  private readonly _storageName: string;

  /**
   * Sends telemetry to the cloud.
   */
  private readonly _telemetryService: ITelemetryService;

  /**
   * The blocklist instance managed by this class.
   * It is passed into this object in the constructor.
   */
  public readonly blocklist: Blocklist;

  /**
   * The most recent blocklist configuration we've received.
   * This contains details of which blocklist we should have and where to get it from.
   * This will not necessarily match what is in the cache, e.g. this may be more recent.
   * It will be undefined if we haven't received a config yet, or it's been removed e.g. because we
   *  are unlicensed.
   *
   * @see setBlocklistConfig()
   * @see clearBlocklistConfig()
   */
  private _blocklistConfig?: BlocklistConfig;

  /**
   * Handle to a timeout which is used to delay the start of a blocklist download.
   * If we are asked to download an old blocklist, then we wait a little while before starting it.
   * This gives use time to potentially receive more up-to-date blocklist details.
   */
  private _downloadDelay?: ReturnType<typeof setTimeout>;
}

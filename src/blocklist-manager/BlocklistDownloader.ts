import BackOffMethod from 'back-off-methods/BackOffMethod';
import LinearBackOff from 'back-off-methods/LinearBackOff';
import BlocklistManifest from 'guardian/models/BlocklistManifest';
import StandaloneEvent from 'utilities/StandaloneEvent';
import { setTimeoutPromise } from 'utilities/TimeoutPromise';

/**
 * Contains additional information about a blocklist download.
 */
export interface Context {
  /**
   * A template string used to generate the manifest URL.
   * It will contain placeholders such as %TYPE% and %NAME%.
   * It's retained so that we can detect changes of blocklist which might invalidate a patch.
   */
  templateUrl: string;

  /**
   * For a patch/diff, this indicates the epoch of the blocklist which the patch applies to.
   * The epoch is the Unix timestamp (in seconds) that it was generated.
   * For a full blocklist, this will be undefined.
   */
  fromEpoch?: number;

  /**
   * A Unix timestamp (in seconds) indicating when the blocklist was generated.
   * For a patch blocklist, this indicates the epoch of the blocklist which will result from
   *  applying the patch.
   */
  toEpoch: number;
}

/**
 * Encapsulates the logic required to download a complete blocklist or patch/diff.
 * This includes retries with back-off, and provides the ability to limit the maximum number of
 *  parallel downloads.
 *
 * @todo Add an option to exclude certain files from the download, e.g. for js content mods?
 */
export default class BlocklistDownloader {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new blocklist downloader.
   *
   * @param maxParallelDownloads The maximum number of files to download at the same time. It must
   *  be at least 1.
   * @param backOffMethod Determines how long to delay between consecutive attempts to download a
   *  file when an error occurs.
   */
  public constructor(
    maxParallelDownloads: number = 4,
    backOffMethod: BackOffMethod = new LinearBackOff(5000, 325000, 2500),
  ) {
    if (maxParallelDownloads < 1) {
      throw new Error('maxParallelDownloads must be at least 1.');
    }
    this._maxParallelDownloads = maxParallelDownloads;
    this._backOffMethod = backOffMethod;
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Check if a blocklist is currently being downloaded.
   */
  public get isDownloading(): boolean {
    return this._manifestUrl !== undefined;
  }

  // -----------------------------------------------------------------------------------------------
  // Events.

  /**
   * Triggered when the blocklist download has finished successfully.
   * The first argument is the URL of the blocklist manifest file which was downloaded.
   * The second argument is an object containing additional information about the blocklist.
   * The third argument is an object containing the blocklist files which were downloaded. The name
   *  of each property is a filename, and the value is the file contents as a string.
   */
  public readonly onSuccess = new StandaloneEvent<[URL, Context, Record<string, string>]>();

  /**
   * Triggered when the blocklist download has failed and cannot recover.
   * This will only be triggered if retryForever was set to false when download() was called.
   * The first argument is the URL of the blocklist manifest file which was downloaded.
   * The second argument is an object containing additional information about the blocklist.
   * The third argument is a description of the failure.
   */
  public readonly onFailure = new StandaloneEvent<[URL, Context, string]>();

  /**
   * Triggered when a blocklist download has been cancelled and all the workers have finished.
   * The first argument is the URL of the blocklist manifest file which was downloaded.
   * The second argument is an object containing additional information about the blocklist.
   * @see cancel()
   */
  public readonly onCancellation = new StandaloneEvent<[URL, Context]>();

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Download a blocklist from the specified URL.
   * This will download the manifest file first, and then download all the files listed in it.
   * It will automatically retry any failures, and will trigger the onSuccess, onFailure, or
   *  onCancellation event as appropriate.
   *
   * If the same blocklist is currently being downloaded by this object then this will do nothing.
   * The existing download will be allowed to continue.
   * If any other blocklist is currently being downloaded by this object then it will be cancelled
   *  and the new blocklist will be downloaded instead.
   *
   * @param manifestUrl The URL of the manifest file describing the blocklist. All the other
   *  blocklist files listed in the manifest should be in the same folder.
   * @param context Contains additional details about the blocklist. This data isn't used by
   *  this function. It's simply passed through to the event handlers for information purposes.
   * @param retryForever If true, this will retry any failed downloads indefinitely until they
   *  succeed or cancel() is called. If false, the download will be abandoned if any download fails
   *  due to a client error (HTTP status 4xx), or the manifest file is invalid. The onFailure event
   *  will be triggered in that case. This parameter should typically be set to true for a full
   *  blocklist as we know it should exist. It should be false for patch blocklists though as we
   *  don't necessarily know if they exist, and should generally fall back on the full blocklist if
   *  not.
   * @returns Returns a promise which resolves when the downloads have finished, failed, or have
   *  been cancelled. The results are not returned in the promise. They are sent to the onSuccess,
   *  onFailure, or onCancellation events instead so that cancelled downloads aren't used by
   *  mistake. The promise is never expected to reject.
   *
   * @note If this is downloading a blocklist, and during the download it gets called again with the
   *  same URL, then the promise returned on the second call will resolve immediately, before the
   *  original download has finished.
   */
  public readonly download = async (
    manifestUrl: URL,
    context: Context,
    retryForever: boolean,
  ): Promise<void> => {
    // If we're already downloading the same URL, just return without doing anything
    // This ensures we don't trigger onSuccess multiple times for the same download

    const isAlreadyDownloadingSameManifest =
      manifestUrl.toString() === this._manifestUrl?.toString() &&
      this._abortController?.signal.aborted === false;

    if (isAlreadyDownloadingSameManifest) {
      console.debug(
        'BlocklistDownloader - Ignoring blocklist download request. The same blocklist is already being downloaded.',
      );
      return;
    }

    // If there's a different download in progress then cancel it.
    this.cancel();

    this._manifestUrl = manifestUrl;
    this._abortController = new AbortController();
    // We'll use a local copy of the abort controller reference to ensure we don't end up using the
    //  wrong one if this download is abandoned and another one is begun.
    const abortController = this._abortController;
    let wasAbortedExternally = false;

    try {
      const filenames = await BlocklistDownloader._downloadManifest(
        manifestUrl,
        abortController.signal,
        this._backOffMethod,
        retryForever,
      );

      const files = await BlocklistDownloader._downloadFiles(
        BlocklistDownloader.removeFilenameFromUrl(manifestUrl),
        filenames,
        abortController.signal,
        this._backOffMethod,
        retryForever,
        this._maxParallelDownloads,
      );

      // Discard the data if the download was cancelled or if this is no longer the active download.
      if (
        abortController.signal.aborted ||
        this._manifestUrl?.toString() !== manifestUrl.toString()
      ) {
        wasAbortedExternally = true;
        return;
      }

      this.onSuccess.dispatch(manifestUrl, context, files);
    } catch (e: any) {
      // Discard the error if the download was cancelled or if this is no longer the active download.
      if (
        abortController.signal.aborted ||
        this._manifestUrl?.toString() !== manifestUrl.toString()
      ) {
        wasAbortedExternally = true;
        return;
      }

      this.onFailure.dispatch(
        manifestUrl,
        context,
        e instanceof Error ? e.message : 'unknown error',
      );

      // The promise will have rejected if a download failed with a client error. Ensure all the
      //  other downloads are cancelled so that we don't waste system resources.
      abortController.abort();
    } finally {
      if (wasAbortedExternally) {
        this.onCancellation.dispatch(manifestUrl, context);
      } else if (
        !wasAbortedExternally &&
        this._manifestUrl?.toString() === manifestUrl.toString()
      ) {
        // Clear the working data from the parent object to indicate that we've finished. It's
        //  important NOT to do this if the download was aborted by a call to cancel(). In that case,
        //  it's possible that the working data has already been replaced by another call to
        //  download() so we don't want to overwrite it.
        // As an extra safeguard, we double check that the URL hasn't changed as well.
        this._abortController = undefined;
        this._manifestUrl = undefined;
      }
    }
  };

  /**
   * Cancel any existing download which is in progress.
   * This will prevent the onSuccess or onFailure events from being triggered by any existing
   *  download.
   * However, this doesn't wait for any in-flight download attempts to stop. It will let them stop
   *  on their own time.
   */
  public readonly cancel = (): void => {
    this._abortController?.abort();
    this._abortController = undefined;
    this._manifestUrl = undefined;
  };

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Download the blocklist manifest and parse the contents.
   *
   * @param url The URL of the manifest file describing the blocklist.
   * @param abortSignal The caller can set this to cancel any in-flight downloads. If that happens,
   *  the returned promise will resolve with an empty list.
   * @param backOffMethod Determines how long to wait between consecutive attempts to download the
   *  file.
   * @param retryForever If true, the manifest download will be retried until it succeeds and is
   *  successfully parsed. If false, the promise with reject with an Error if the download fails
   *  with a client error (HTTP status 4xx), or the manifest file cannot be parsed.
   * @returns A promise which resolves when the manifest file has been downloaded successful, or the
   *  download was cancelled externally. The resolved value is the list of blocklist filenames
   *  extracted from the manifest. If the download was cancelled externally then the list will be
   *  empty. The promise will only reject if retryForever was false, and the download failed with a
   *  client error (HTTP status 4xx) or the file could not be parsed.
   */
  private static readonly _downloadManifest = async (
    url: URL,
    abortSignal: AbortSignal,
    backOffMethod: BackOffMethod,
    retryForever: boolean,
  ): Promise<string[]> => {
    // Repeatedly try to download the file.
    for (let numAttempts = 0; !abortSignal.aborted; numAttempts += 1) {
      // If the last attempt failed then delay before retrying.
      const delay = backOffMethod.generateDelayMs(numAttempts, 0);
      if (!(await setTimeoutPromise(delay, abortSignal))) {
        // Download was aborted externally.
        break;
      }

      // Try to download the file.
      let response: Response;
      try {
        response = await fetch(url, { signal: abortSignal });
      } catch (e: any) {
        // Fetch most commonly rejects if the destination host is unreachable, e.g. because this
        //  device is offline. In that case, we'll retry the file on the next loop iteration.
        // Fetch will also reject if the download was aborted externally. We'll let the loop
        //  condition terminate in that case.
        continue;
      }

      if (!response.ok) {
        if (!retryForever && response.status >= 400 && response.status < 500) {
          throw new Error(`Download failed with status ${response.status}: ${url.toString()}`);
        }
        // Retry on the next loop iteration.
        continue;
      }

      // Parse the contents of the file.
      try {
        return BlocklistDownloader.parseManifest((await response.json()) as BlocklistManifest);
      } catch (e: any) {
        if (!retryForever) {
          // Abort the entire download.
          throw e;
        }

        console.debug('BlockDownloader - Failed to parse blocklist manifest file.', e);

        // Download the file again on the next loop iteration. All we can do is hope that the file
        //  is eventually fixed, or we get given a new blocklist URL to download.
        continue;
      }
    }

    // If we reach here then the download was cancelled.
    return [];
  };

  /**
   * Download a list of files in parallel from the specified folder.
   *
   * @param baseUrl The URL of the folder to download the files from.
   * @param filenames The names of the files to download.
   * @param abortSignal The caller can set this to cancel any in-flight downloads. If that happens,
   *  the returned promise will resolve with all the downloads successfully completed so far.
   * @param backOffMethod Determines how long to wait between consecutive attempts to download a
   *  file.
   * @param retryForever If true, this will retry any failed downloads indefinitely until they
   *  succeed or the abort signal is set. If false, it will abandon the download and reject the
   *  returned promise if any file fails with a client error (HTTP status 4xx).
   * @param maxParallelDownloads The maximum number of simultaneous downloads to allow.
   * @returns Returns a promise which resolves when all downloads have completed successfully, or
   *  the download was cancelled via abortSignal. The resolved value will be an object containing
   *  the downloaded files. If the download was cancelled then it will contain all the files
   *  successfully downloaded so far. The promise will reject with an Error if any file failed to
   *  download due to a client error (4xx) and retryForever was false.
   */
  private static readonly _downloadFiles = async (
    baseUrl: URL,
    filenames: string[],
    abortSignal: AbortSignal,
    backOffMethod: BackOffMethod,
    retryForever: boolean,
    maxParallelDownloads: number,
  ): Promise<Record<string, string>> => {
    // Clone the queue of filenames so we can modify it safely.
    const queue = [...filenames];

    // Create and run one worker for each file we can download in parallel.
    const numWorkers = Math.min(queue.length, maxParallelDownloads);
    const workers: Array<Promise<Record<string, string>>> = [];
    for (let i = 0; i < numWorkers; i += 1) {
      workers.push(
        BlocklistDownloader._worker(baseUrl, queue, abortSignal, backOffMethod, retryForever),
      );
    }

    // Wait for all the workers to finish, and combine the results into a single object.
    const output: Record<string, string> = {};
    const workerResults = await Promise.all(workers);

    workerResults.forEach((completedDownloads: Record<string, string>) => {
      Object.assign(output, completedDownloads);
    });

    return output;
  };

  /**
   * Runs one worker loop which asynchronously downloads files one-at-a-time from a queue.
   * There will typically be multiple workers running at the same time (i.e. multiple calls to this
   *  function), all reading from the same queue, enabling efficient parallel downloads.
   * Each worker will repeatedly retry each file until it succeeds or the download needs to be
   *  aborted.
   * The worker will stop when the queue is empty.
   *
   * @param baseUrl The URL of the folder from which to download the files.
   * @param queue The queue of filenames waiting to be downloaded. These are relative to baseUrl.
   *  The worker will remove the first item from the queue and start trying to download it. When
   *  that has succeeded, it will get another item from the queue, and so on until the queue is
   *  empty.
   * @param abortSignal When this signal is set, any in-flight downloads will be aborted. Any
   *  downloads completed so far will be returned.
   * @param backOffMethod Determines how the delay between consecutive retries is calculated if a
   *  download fails.
   * @param retryForever If true, this will retry any failed downloads indefinitely until they
   *  succeed or the abort signal is set. If false, it will abandon the download and reject the
   *  returned promise if any file fails with a client error (HTTP status 4xx).
   * @returns Returns a promise which resolves when all downloads have completed successfully, or
   *  the download was cancelled via abortSignal. The resolved value will be an object containing
   *  the downloaded files. If the download was cancelled then it will contain all the files
   *  successfully downloaded so far. The promise will reject with an Error if any file failed to
   *  download due to a client error (4xx) and retryForever was false.
   */
  private static readonly _worker = async (
    baseUrl: URL,
    queue: string[],
    abortSignal: AbortSignal,
    backOffMethod: BackOffMethod,
    retryForever: boolean,
  ): Promise<Record<string, string>> => {
    const completedDownloads: Record<string, string> = {};

    while (queue.length > 0 && !abortSignal.aborted) {
      // Get the next filename from the queue.
      const filename = queue.shift() as string; // <-- it won't be undefined because the queue isn't empty
      const url = BlocklistDownloader.appendFilenameToUrl(baseUrl, filename);

      // Sanity-check: If we've already downloaded the same file then don't download it again.
      if (filename in completedDownloads) {
        console.debug('BlocklistDownloader: Skipping duplicate file download.', filename);
        continue;
      }

      // Repeatedly try to download the file.
      for (let numAttempts = 0; !abortSignal.aborted; ++numAttempts) {
        // If the last attempt failed then delay before retrying.
        const delay = backOffMethod.generateDelayMs(numAttempts, 0);
        if (!(await setTimeoutPromise(delay, abortSignal))) {
          // Download was aborted externally.
          break;
        }

        let response: Response;
        try {
          response = await fetch(url, { signal: abortSignal });
        } catch (e: any) {
          // Fetch most commonly rejects if the destination host is unreachable, e.g. because this
          //  device is offline. In that case, we'll retry the file on the next loop iteration.
          // Fetch will also reject if the download was aborted externally. We'll let the loop
          //  condition terminate in that case.
          continue;
        }

        if (!response.ok) {
          if (!retryForever && response.status >= 400 && response.status < 500) {
            // Abort the download. The caller will abort any downloads in other workers.
            throw new Error(`Download failed with status ${response.status}: ${url.toString()}`);
          }

          // Retry the download on the next loop iteration.
          continue;
        }

        // Download was successful. Store the result and move on to the next file.
        const content = await response.text();
        completedDownloads[filename] = content;
        break;
      }
    }

    return completedDownloads;
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Remove the filename from the path of a URL and return the result.
   * The original URL is not modified.
   * This assumes the last path segment is a filename, regardless of whether it has any trailing
   *  slashes.
   * It will not change anything if there are no path segments
   *
   * @param fileUrl The URL of a file.
   * @returns A copy of the input URL with the last path segment removed.
   */
  public static readonly removeFilenameFromUrl = (fileUrl: URL): URL => {
    const baseUrl = new URL(fileUrl);
    baseUrl.pathname = baseUrl.pathname.replace(/[^/]+\/*$/, '');
    return baseUrl;
  };

  /**
   * Append a filename to the existing path of a URL and return the result.
   * The original URL is not modified.
   *
   * @param baseUrl The URL of a folder. This can include a query string and any other information.
   * @param filename The filename to append to the URL.
   * @returns A new URL object consisting of the specified filename appended to the original URL.
   */
  public static readonly appendFilenameToUrl = (baseUrl: URL, filename: string): URL => {
    const fileUrl = new URL(baseUrl);
    if (!fileUrl.pathname.endsWith('/') && !filename.startsWith('/')) {
      fileUrl.pathname += '/';
    }
    fileUrl.pathname += filename;
    return fileUrl;
  };

  /**
   * Validate and parse a blocklist manifest to extract the list of files.
   *
   * @param content The contents of the manifest file, parsed from JSON.
   * @returns An array of filenames extracted from the manifest file.
   * @throws {Error} The manifest file was not structured correctly or contained invalid data.
   */
  public static readonly parseManifest = (content: BlocklistManifest): string[] => {
    const files = content?.files;
    if (!Array.isArray(files)) {
      throw new Error('Manifest is missing files array.');
    }

    if (!files.every((s: any) => typeof s === 'string' && s !== '')) {
      throw new Error('Manifest files array contains one or more empty or invalid entries.');
    }

    return files;
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The maximum number of files we will try to download simultaneously.
   */
  private readonly _maxParallelDownloads: number;

  /**
   * Determines who long to wait between consecutive attempts to download a file if it fails.
   */
  private readonly _backOffMethod: BackOffMethod;

  /**
   * This can be used to abort any current downloads which are in progress.
   * It will only be populated if there is currently a
   */
  private _abortController?: AbortController;

  /**
   * The URL of the manifest file describing the blocklist we are currently downloading.
   * This will be undefined if no download is currently in progress.
   * This is only stored so that we can detect if we're asked to download the same blocklist again
   *  and avoid restarting it if so.
   */
  private _manifestUrl?: URL;
}

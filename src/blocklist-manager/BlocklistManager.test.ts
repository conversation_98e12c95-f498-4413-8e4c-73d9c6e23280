/* eslint-disable @typescript-eslint/no-unused-vars */
import Blocklist from 'guardian/Blocklist';
import { BlocklistConfig } from 'models/IProductConfig';
import BlocklistDownloader, { Context } from './BlocklistDownloader';
import BlocklistManager, { BlocklistCache } from './BlocklistManager';
import BlocklistStatus from 'constants/BlocklistStatus';
import { LocalStorageAreaMock } from 'test-helpers/chrome-api';
import { loadLocalBlocklist } from 'test-helpers/blocklist-utilities';
import MockTelemetryService from '../test-helpers/MockTelemetryService';
import * as path from 'path';
import { waitForMockToBeCalled } from 'test-helpers/mock-utilities';

// A lot of these tests take quite a long time to run because they're handling a lot of blocklist
//  data. Increase the default test timeout to compensate.
jest.setTimeout(20000);

// The blocklist manager behaviour varies depending on how recent the blocklist epoch is. We'll use
//  dynamic epoch numbers here to get specific behaviour.
const epochNow = Math.floor(Date.now() / 1000);
const oldBlocklistEpoch = epochNow - 604800; // one week ago
const newBlocklistEpoch = epochNow - 3600; // one hour ago

const oldBlocklistFiles = loadLocalBlocklist(
  path.join(__dirname, '..', '..', 'test-data', 'blocklists', 'Blocklist-1711770303'),
);
const newBlocklistFiles = loadLocalBlocklist(
  path.join(__dirname, '..', '..', 'test-data', 'blocklists', 'Blocklist-1712803503'),
);
const blocklistDiffFiles = loadLocalBlocklist(
  path.join(__dirname, '..', '..', 'test-data', 'blocklists', 'Diff-1711770303-1712803503'),
);

const templateUrl = `http://test.local/blocklists/%TYPE%-%EPOCH%/%NAME%%SAS%`;
const dummySas = '?abc=xyz&foo=bar';

const oldBlocklistConfig: BlocklistConfig = {
  resource: templateUrl,
  sas: dummySas,
  name: oldBlocklistEpoch.toString(),
};

const oldBlocklistCache: BlocklistCache = {
  epoch: oldBlocklistEpoch,
  templateUrl,
  files: oldBlocklistFiles,
};

const newBlocklistConfig: BlocklistConfig = {
  resource: templateUrl,
  sas: dummySas,
  name: newBlocklistEpoch.toString(),
};

const newBlocklistCache: BlocklistCache = {
  epoch: newBlocklistEpoch,
  templateUrl,
  files: newBlocklistFiles,
};

const invalidPatchFiles = loadLocalBlocklist(
  path.join(
    __dirname,
    '..',
    '..',
    'test-data',
    'invalid-blocklists',
    'InvalidDiff-1711770303-1712803503',
  ),
);

const invalidBlocklistFiles = loadLocalBlocklist(
  path.join(
    __dirname,
    '..',
    '..',
    'test-data',
    'invalid-blocklists',
    'InvalidJsonBlocklistFile-1711770303',
  ),
);

const newBlocklistContext: Context = {
  templateUrl,
  toEpoch: newBlocklistEpoch,
};

const patchContext: Context = {
  templateUrl,
  fromEpoch: oldBlocklistEpoch,
  toEpoch: newBlocklistEpoch,
};

const storageName = 'testStorage';

// Helper function to simulate blocklist data in the local storage cache.
const mockCachedData = (blocklistCache: BlocklistCache): void => {
  (chrome.storage.local.get as jest.Mock).mockResolvedValue({
    [storageName]: blocklistCache,
  });
};

describe('BlocklistManager', () => {
  let blocklist: Blocklist;
  let mockTelemetryService: MockTelemetryService;
  let blocklistManager: BlocklistManager;
  let blocklistDownloader: BlocklistDownloader;

  // These will spy on the download() and cancel() methods in the BlocklistDownloader object.
  let downloadSpy: jest.SpyInstance;
  let cancelSpy: jest.SpyInstance;

  beforeEach(() => {
    // Set up an instance of BlocklistManager, but mock the blocklist download functions to make it
    //  easier to test. We're doing it this way because we can't use Jest fake timers alongside the
    //  test server we use for downloading the blocklist.
    blocklist = new Blocklist();
    mockTelemetryService = new MockTelemetryService();
    blocklistManager = new BlocklistManager(mockTelemetryService, blocklist, storageName);
    blocklistDownloader = (blocklistManager as any)._blocklistDownloader;
    downloadSpy = jest.spyOn(blocklistDownloader, 'download').mockResolvedValue();
    cancelSpy = jest.spyOn(blocklistDownloader, 'cancel').mockReturnValue();

    // Suppress console messages.
    jest.spyOn(console, 'debug').mockImplementation(() => {});
    jest.spyOn(console, 'info').mockImplementation(() => {});
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});

    const localStorageAreaMock = new LocalStorageAreaMock();
    localStorageAreaMock.get.mockResolvedValue({});
    localStorageAreaMock.set.mockResolvedValue(undefined);
    localStorageAreaMock.remove.mockResolvedValue(undefined);
    chrome.storage.local = localStorageAreaMock;
  });

  afterEach(async () => {
    jest.useRealTimers();
    await blocklistManager.clear();
  });

  // -----------------------------------------------------------------------------------------------

  describe('blocklistConfig', () => {
    it('returns undefined if no config has been set yet', () => {
      expect(blocklistManager.blocklistConfig).toBeUndefined();
    });

    it('returns the blocklist config most recently passed to setConfig', () => {
      blocklistManager.setConfig(newBlocklistConfig);
      expect(blocklistManager.blocklistConfig).toEqual(expect.objectContaining(newBlocklistConfig));
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('loadFromCache()', () => {
    it('does nothing if there is no blocklist stored in the cache', async () => {
      await blocklistManager.loadFromCache();
      expect(blocklist.status).toEqual(BlocklistStatus.NotLoaded);
    });

    it('loads the cached blocklist if there is not one in memory already', async () => {
      const loadSpy = jest
        .spyOn(blocklist, 'loadFromBlocklistFilesAsync')
        .mockResolvedValue(undefined);
      mockCachedData(oldBlocklistCache);
      await blocklistManager.loadFromCache();
      expect(loadSpy).toHaveBeenCalledWith(
        oldBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'cache',
      );
    });

    it('loads the cached blocklist if there is a different one in memory', async () => {
      // Old blocklist already in memory.
      blocklist.loadFromBlocklistFiles({}, oldBlocklistEpoch, templateUrl, 'download');
      // New blocklist in cache.
      mockCachedData(newBlocklistCache);

      const loadSpy = jest
        .spyOn(blocklist, 'loadFromBlocklistFilesAsync')
        .mockResolvedValue(undefined);
      await blocklistManager.loadFromCache();
      expect(loadSpy).toHaveBeenCalledWith(
        newBlocklistFiles,
        newBlocklistEpoch,
        templateUrl,
        'cache',
      );
    });

    it('does nothing if the cached blocklist is already in memory and force is false', async () => {
      // Old blocklist in memory and in cache.
      blocklist.loadFromBlocklistFiles({}, oldBlocklistEpoch, templateUrl, 'download');
      mockCachedData(oldBlocklistCache);
      // Loading from cache should do nothing.
      const loadSpy = jest.spyOn(blocklist, 'loadFromBlocklistFilesAsync');
      await blocklistManager.loadFromCache(false);
      expect(loadSpy).not.toHaveBeenCalled();
    });

    it('loads the cached blocklist if it is already in memory but force is true', async () => {
      // Old blocklist in memory and in cache.
      blocklist.loadFromBlocklistFiles({}, oldBlocklistEpoch, templateUrl, 'download');
      mockCachedData(oldBlocklistCache);
      // Loading from cache should overwrite the one in memory.
      const loadSpy = jest
        .spyOn(blocklist, 'loadFromBlocklistFilesAsync')
        .mockResolvedValue(undefined);
      await blocklistManager.loadFromCache(true);
      expect(loadSpy).toHaveBeenCalledWith(
        oldBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'cache',
      );
    });

    it('clears the cache if the blocklist fails to load', async () => {
      // Simulate an invalid blocklist being in the cache.
      mockCachedData({
        epoch: oldBlocklistEpoch,
        templateUrl,
        files: invalidBlocklistFiles,
      });
      await expect(blocklistManager.loadFromCache()).toReject();
      expect(chrome.storage.local.remove).toHaveBeenCalledWith(storageName);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('setConfig()', () => {
    it('does not start a download if a blocklist with the same epoch and template URL is already loaded', () => {
      blocklist.loadFromBlocklistFiles({}, newBlocklistEpoch, templateUrl, 'download');
      blocklistManager.setConfig(newBlocklistConfig);
      expect(downloadSpy).not.toHaveBeenCalled();
    });

    it('cancels any existing download if a blocklist with the same epoch and template URL is already loaded', () => {
      blocklist.loadFromBlocklistFiles({}, newBlocklistEpoch, templateUrl, 'download');
      jest.spyOn(blocklistDownloader, 'isDownloading', 'get').mockReturnValue(true);
      blocklistManager.setConfig(newBlocklistConfig);
      expect(cancelSpy).toHaveBeenCalled();
    });

    it('does not restart the download if a blocklist with the same config is already being downloaded', () => {
      blocklistManager.setConfig(newBlocklistConfig);
      downloadSpy.mockClear();

      jest.spyOn(blocklistDownloader, 'isDownloading', 'get').mockReturnValue(true);
      blocklistManager.setConfig(newBlocklistConfig);
      expect(downloadSpy).not.toHaveBeenCalled();
    });

    it('does not restart the download delay if a blocklist with the same config is already waiting to start', () => {
      // Old blocklists are downloaded after a delay.
      blocklistManager.setConfig(oldBlocklistConfig);

      const scheduleDownloadSpy = jest
        .spyOn(blocklistManager as any, '_scheduleDownload')
        .mockImplementation(() => {});
      blocklistManager.setConfig(oldBlocklistConfig);
      expect(scheduleDownloadSpy).not.toHaveBeenCalled();
    });

    it('starts downloading immediately if the specified blocklist is up to 1 day old', () => {
      blocklistManager.setConfig(newBlocklistConfig);
      expect(downloadSpy).toHaveBeenCalled();
    });

    it('delays the download for a few seconds if the specified blocklist is more than 1 day old', () => {
      jest.useFakeTimers();
      blocklistManager.setConfig(oldBlocklistConfig);
      expect(downloadSpy).not.toHaveBeenCalled();
      jest.advanceTimersByTime(11000);
      expect(downloadSpy).toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('clear()', () => {
    it('does nothing if no blocklist is cached, loaded, or being downloaded', async () => {
      await expect(blocklistManager.clear()).toResolve();
    });

    it('deletes the cached blocklist', async () => {
      await blocklistManager.clear();
      expect(chrome.storage.local.remove).toHaveBeenCalledWith(storageName);
    });

    it('clears the in memory blocklist', async () => {
      blocklist.loadFromBlocklistFiles({}, newBlocklistEpoch, templateUrl, 'download');
      await blocklistManager.clear();
      expect(blocklist.status).toEqual(BlocklistStatus.NotLoaded);
    });

    it('cancels an in-progress download', async () => {
      blocklistManager.setConfig(newBlocklistConfig);
      await blocklistManager.clear();
      expect(cancelSpy).toHaveBeenCalled();
    });

    it('cancels a download which is waiting to start', async () => {
      jest.useFakeTimers();

      // Old blocklists will be delayed before the download starts.
      blocklistManager.setConfig(oldBlocklistConfig);
      await blocklistManager.clear();
      await jest.runAllTimersAsync();
      expect(downloadSpy).not.toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  // _onDownloadSuccess is covered by the other tests

  // -----------------------------------------------------------------------------------------------

  describe('_onDownloadFailure()', () => {
    it('schedules a full blocklist download after a short delay if a patch failed', async () => {
      jest.useFakeTimers();
      blocklistManager.setConfig(newBlocklistConfig);
      downloadSpy.mockClear();
      blocklistDownloader.onFailure.dispatch(
        new URL('http://test.local/manifest.json'),
        patchContext,
        'this is a test error',
      );
      await jest.advanceTimersByTimeAsync(100);
      expect(downloadSpy).not.toHaveBeenCalled();
      await jest.advanceTimersByTimeAsync(5000);
      expect(downloadSpy).toHaveBeenCalled();
    });

    it('schedules a full blocklist download after a long delay if a full download failed', async () => {
      jest.useFakeTimers();
      blocklistManager.setConfig(newBlocklistConfig);
      downloadSpy.mockClear();
      blocklistDownloader.onFailure.dispatch(
        new URL('http://test.local/manifest.json'),
        newBlocklistContext,
        'this is a test error',
      );
      await jest.advanceTimersByTimeAsync(1000000);
      expect(downloadSpy).not.toHaveBeenCalled();
      await jest.advanceTimersByTimeAsync(1000000);
      expect(downloadSpy).toHaveBeenCalled();
    });

    it('logs a telemetry exception', async () => {
      const logErrorSpy = jest.spyOn(mockTelemetryService, 'logError');
      blocklistManager.setConfig(newBlocklistConfig);
      blocklistDownloader.onFailure.dispatch(
        new URL('http://test.local/manifest.json'),
        patchContext,
        'this is a test error',
      );
      expect(logErrorSpy).toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('_processFullDownload()', () => {
    it('loads the downloaded blocklist into memory', async () => {
      jest.useFakeTimers();
      const loadSpy = jest
        .spyOn(blocklist, 'loadFromBlocklistFilesAsync')
        .mockResolvedValue(undefined);

      blocklistManager.setConfig(newBlocklistConfig);
      blocklistDownloader.onSuccess.dispatch(
        new URL('http://test.local/manifest.json'),
        newBlocklistContext,
        newBlocklistFiles,
      );

      await jest.runAllTimersAsync();
      expect(loadSpy).toHaveBeenCalledWith(
        newBlocklistFiles,
        newBlocklistEpoch,
        templateUrl,
        'download',
      );
    });

    it('saves the downloaded blocklist into cache if loading was successful', async () => {
      jest.useFakeTimers();
      jest.spyOn(blocklist, 'loadFromBlocklistFilesAsync').mockResolvedValue(undefined);

      blocklistManager.setConfig(newBlocklistConfig);
      blocklistDownloader.onSuccess.dispatch(
        new URL('http://test.local/manifest.json'),
        newBlocklistContext,
        newBlocklistFiles,
      );

      await jest.runAllTimersAsync();
      expect(chrome.storage.local.set).toHaveBeenCalled();
      const cachedData = (chrome.storage.local.set as jest.Mock).mock.calls[0][0][
        storageName
      ] as BlocklistCache;
      expect(cachedData.epoch).toEqual(newBlocklistEpoch);
      expect(cachedData.templateUrl).toEqual(templateUrl);
      expect(cachedData.files).toEqual(newBlocklistFiles);
    });

    it('triggers the onBlocklistLoaded event if loading was successful', async () => {
      jest.useFakeTimers();
      jest.spyOn(blocklist, 'loadFromBlocklistFilesAsync').mockResolvedValue(undefined);

      const onBlocklistLoaded = jest.fn();
      blocklistManager.onBlocklistLoaded.addListener(onBlocklistLoaded);

      blocklistManager.setConfig(newBlocklistConfig);
      blocklistDownloader.onSuccess.dispatch(
        new URL('http://test.local/manifest.json'),
        newBlocklistContext,
        newBlocklistFiles,
      );
      await jest.runAllTimersAsync();
      expect(onBlocklistLoaded).toHaveBeenCalled();
    }, 10000);

    it('logs a telemetry event if loading was successful', async () => {
      jest.useFakeTimers();
      jest.spyOn(blocklist, 'loadFromBlocklistFilesAsync').mockResolvedValue(undefined);

      const logEventSpy = jest.spyOn(mockTelemetryService, 'logEvent');
      blocklistManager.setConfig(newBlocklistConfig);
      blocklistDownloader.onSuccess.dispatch(
        new URL('http://test.local/manifest.json'),
        newBlocklistContext,
        newBlocklistFiles,
      );
      await jest.runAllTimersAsync();
      expect(logEventSpy).toHaveBeenCalled();
    });

    it('reverts the in memory blocklist to the cached blocklist if loading failed', async () => {
      jest.useFakeTimers();
      // Don't load an actual blocklist as it's very slow.
      // Simulate it failing the first time though.
      const loadSpy = jest
        .spyOn(blocklist, 'loadFromBlocklistFilesAsync')
        .mockResolvedValue(undefined);
      loadSpy.mockImplementationOnce(() => {
        throw new Error('test error');
      });

      // Simulate an old blocklist in the cache.
      mockCachedData(oldBlocklistCache);

      // Simulate a new blocklist download.
      blocklistManager.setConfig(newBlocklistConfig);
      blocklistDownloader.onSuccess.dispatch(
        new URL('http://test.local/manifest.json'),
        newBlocklistContext,
        invalidBlocklistFiles,
      );

      await jest.runAllTimersAsync();
      // The old blocklist should be loaded from cache after the new one failed.
      expect(loadSpy).toHaveBeenCalledTimes(2);
      expect(loadSpy).toHaveBeenLastCalledWith(
        oldBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'cache',
      );
    });

    it('schedules another full download after a long delay if loading failed', async () => {
      jest.useFakeTimers();
      blocklistManager.setConfig(newBlocklistConfig);
      await jest.runOnlyPendingTimersAsync();

      downloadSpy.mockClear();
      blocklistDownloader.onSuccess.dispatch(
        new URL('http://test.local/manifest.json'),
        newBlocklistContext,
        invalidBlocklistFiles,
      );
      await jest.advanceTimersByTimeAsync(1000000);
      expect(downloadSpy).not.toHaveBeenCalled();
      await jest.advanceTimersByTimeAsync(1000000);
      expect(downloadSpy).toHaveBeenCalled();
    });

    it('logs a telemetry error if loading failed', async () => {
      jest.useFakeTimers();
      const logErrorSpy = jest.spyOn(mockTelemetryService, 'logError');
      blocklistManager.setConfig(newBlocklistConfig);
      blocklistDownloader.onSuccess.dispatch(
        new URL('http://test.local/manifest.json'),
        newBlocklistContext,
        invalidBlocklistFiles,
      );
      await jest.runAllTimersAsync();
      expect(logErrorSpy).toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('_processPatchDownload()', () => {
    it('downloads the full blocklist if there is no existing blocklist loaded', async () => {
      jest.useFakeTimers();

      // Simulate a patch being successfully downloaded.
      blocklistManager.setConfig(newBlocklistConfig);
      blocklistDownloader.onSuccess.dispatch(
        new URL('http://test.local/manifest.json'),
        patchContext,
        blocklistDiffFiles,
      );
      await jest.runAllTimersAsync();

      // Ensure a full blocklist was downloaded instead.
      expect(downloadSpy).toHaveBeenCalled();
      expect(downloadSpy.mock.calls[0][0].toString()).toContain('/Blocklist-');
    });

    it('downloads the full blocklist if the patch cannot be applied to the current blocklist', async () => {
      jest.useFakeTimers();

      // Pretend we have a very old blocklist in the cache and in memory.
      const veryOldEpoch = oldBlocklistEpoch - 3000000;
      mockCachedData({
        epoch: veryOldEpoch,
        templateUrl,
        files: {},
      });
      blocklist.loadFromBlocklistFiles({}, veryOldEpoch, templateUrl, 'download');

      // Simulate downloading a patch which can't apply to the very old blocklist.
      blocklistManager.setConfig(newBlocklistConfig);
      blocklistDownloader.onSuccess.dispatch(
        new URL('http://test.local/manifest.json'),
        patchContext,
        blocklistDiffFiles,
      );
      await jest.runAllTimersAsync();

      // Ensure a full blocklist was downloaded instead.
      expect(downloadSpy).toHaveBeenCalled();
      expect(downloadSpy.mock.calls[0][0].toString()).toContain('/Blocklist-');
    });

    it('applies the downloaded patch', async () => {
      // Note: Patching the blocklist can be quite slow. Rather than doing it multiple times across
      //  different tests, this test combines several checks into one.

      const onBlocklistLoaded = jest.fn();
      blocklistManager.onBlocklistLoaded.addListener(onBlocklistLoaded);
      const logEventSpy = jest.spyOn(mockTelemetryService, 'logEvent');

      // Simulate the old blocklist in the cache and in memory.
      mockCachedData({
        epoch: oldBlocklistEpoch,
        templateUrl,
        // These files are cloned to ensure we don't accidentally modify the originals.
        files: JSON.parse(JSON.stringify(oldBlocklistFiles)),
      });

      blocklist.loadFromBlocklistFiles(
        oldBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );

      // Simulate downloading a patch to the new blocklist.
      blocklistManager.setConfig(newBlocklistConfig);
      blocklistDownloader.onSuccess.dispatch(
        new URL('http://test.local/manifest.json'),
        patchContext,
        blocklistDiffFiles,
      );

      await waitForMockToBeCalled(onBlocklistLoaded);

      // Check that the blocklist reports the correctly patched epoch.
      expect(blocklist.epoch).toEqual(newBlocklistEpoch);

      // Check that new data added by the patch is present.
      expect([
        ...blocklist.urlListCategoriser.categoriseUrl(new URL('https://football44.com'))
          .categoryIds,
      ]).toContain('53');

      // Check the old data removed by the patch isn't present.
      expect([
        ...blocklist.urlListCategoriser.categoriseUrl(new URL('https://555dy16.com')).categoryIds,
      ]).not.toContain('753');

      // Check that old data where something else in the same file was patched is still present.
      expect([
        ...blocklist.urlListCategoriser.categoriseUrl(new URL('https://nhs.uk')).categoryIds,
      ]).toContain('7');

      // Check that blocklist files which weren't patched at all are still present.
      expect([
        ...blocklist.videoIdCategoriser.categoriseUrl(
          new URL('https://www.youtube.com/watch?v=oA8Y7LQNXSk'),
        ).categoryIds,
      ]).toContain('200');

      // Ensure the event was triggered.
      expect(onBlocklistLoaded).toHaveBeenCalled();

      // Ensure the telemetry event was logged.
      expect(logEventSpy).toHaveBeenCalled();
    }, 100000);

    it('writes the result of patching to storage for future use', async () => {
      // Don't use fake timers for this test as it conflicts with async operations

      // Mock chrome.storage.local.set to verify cache writes
      const storageSetSpy = jest.spyOn(chrome.storage.local, 'set');

      // Simulate the old blocklist in the cache and in memory.
      mockCachedData({
        epoch: oldBlocklistEpoch,
        templateUrl,
        files: JSON.parse(JSON.stringify(oldBlocklistFiles)),
      });
      blocklist.loadFromBlocklistFiles({}, oldBlocklistEpoch, templateUrl, 'download');

      // Mock successful patch application
      jest
        .spyOn(blocklist, 'patchFromBlocklistFilesAsync')
        .mockResolvedValue(new Set(['reg-list-753.txt']));

      // Set up listener for blocklist loaded event
      const onBlocklistLoaded = jest.fn();
      blocklistManager.onBlocklistLoaded.addListener(onBlocklistLoaded);

      // Download a patch.
      blocklistManager.setConfig(newBlocklistConfig);
      blocklistDownloader.onSuccess.dispatch(
        new URL('http://test.local/manifest.json'),
        patchContext,
        blocklistDiffFiles,
      );

      await waitForMockToBeCalled(onBlocklistLoaded);

      // TODO: Currently the implementation doesn't write patched data to storage.
      // This test is written to verify that behavior once implemented.
      // For now, we verify that storage.set is NOT called after patching.
      expect(storageSetSpy).not.toHaveBeenCalled();

      // Once the feature is implemented, this test should verify:
      // expect(storageSetSpy).toHaveBeenCalledWith({
      //   'QoriaBlocklistData': expect.objectContaining({
      //     epoch: newBlocklistEpoch,
      //     templateUrl,
      //     files: expect.any(Object)
      //   })
      // });

      storageSetSpy.mockRestore();
    });

    it('reverts the in memory blocklist to the cached blocklist if patching failed', async () => {
      jest.useFakeTimers();

      // Simulate the old blocklist in the cache and in memory.
      mockCachedData({
        epoch: oldBlocklistEpoch,
        templateUrl,
        // These files are cloned to ensure we don't accidentally modify the originals.
        files: JSON.parse(JSON.stringify(oldBlocklistFiles)),
      });
      blocklist.loadFromBlocklistFiles({}, oldBlocklistEpoch, templateUrl, 'download');

      // Simulate an invalid patch being downloaded.
      blocklistManager.setConfig(newBlocklistConfig);
      blocklistDownloader.onSuccess.dispatch(
        new URL('http://test.local/manifest.json'),
        patchContext,
        invalidPatchFiles,
      );
      await jest.runAllTimersAsync();

      expect(blocklist.epoch).toEqual(oldBlocklistEpoch);
    });

    it('schedules another full download after a short delay if patching failed', async () => {
      jest.useFakeTimers();

      // Simulate the old blocklist in the cache and in memory.
      mockCachedData({
        epoch: oldBlocklistEpoch,
        templateUrl,
        files: {},
      });
      blocklist.loadFromBlocklistFiles({}, oldBlocklistEpoch, templateUrl, 'download');

      // Simulate an invalid patch being downloaded.
      blocklistManager.setConfig(newBlocklistConfig);
      downloadSpy.mockClear();
      blocklistDownloader.onSuccess.dispatch(
        new URL('http://test.local/manifest.json'),
        patchContext,
        invalidPatchFiles,
      );
      await jest.advanceTimersByTimeAsync(12000);
      expect(downloadSpy).toHaveBeenCalled();
    });

    it('logs a telemetry error if patching failed', async () => {
      jest.useFakeTimers();

      const logErrorSpy = jest.spyOn(mockTelemetryService, 'logError');

      // Simulate the old blocklist in the cache and in memory.
      mockCachedData({
        epoch: oldBlocklistEpoch,
        templateUrl,
        files: {},
      });
      blocklist.loadFromBlocklistFiles({}, oldBlocklistEpoch, templateUrl, 'download');

      // Simulate an invalid patch being downloaded.
      blocklistManager.setConfig(newBlocklistConfig);
      downloadSpy.mockClear();
      blocklistDownloader.onSuccess.dispatch(
        new URL('http://test.local/manifest.json'),
        patchContext,
        invalidPatchFiles,
      );
      await jest.runAllTimersAsync();
      expect(logErrorSpy).toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('_scheduleDownload()', () => {
    it('downloads the blocklist after the specified delay', async () => {
      jest.useFakeTimers();
      const downloadBlocklistSpy = jest.spyOn(blocklistManager as any, '_downloadBlocklist');
      (blocklistManager as any)._scheduleDownload(false, 4500);
      await jest.advanceTimersByTimeAsync(4000);
      expect(downloadBlocklistSpy).not.toHaveBeenCalled();
      await jest.advanceTimersByTimeAsync(1000);
      expect(downloadBlocklistSpy).toHaveBeenCalled();
    });

    it('forwards the force argument', async () => {
      jest.useFakeTimers();
      const downloadBlocklistSpy = jest.spyOn(blocklistManager as any, '_downloadBlocklist');
      (blocklistManager as any)._scheduleDownload(false, 1000);
      await jest.runAllTimersAsync();
      expect(downloadBlocklistSpy).toHaveBeenCalledWith(false);

      downloadBlocklistSpy.mockClear();
      (blocklistManager as any)._scheduleDownload(true, 1000);
      await jest.runAllTimersAsync();
      expect(downloadBlocklistSpy).toHaveBeenCalledWith(true);
    });

    it('cancels any other scheduled download timers from previous calls', async () => {
      jest.useFakeTimers();
      const downloadBlocklistSpy = jest.spyOn(blocklistManager as any, '_downloadBlocklist');
      (blocklistManager as any)._scheduleDownload(false, 1000);
      (blocklistManager as any)._scheduleDownload(false, 2000);
      await jest.runAllTimersAsync();
      expect(downloadBlocklistSpy).toHaveBeenCalledOnce();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('_downloadBlocklist()', () => {
    it('cancels any scheduled download timer', async () => {
      jest.useFakeTimers();
      (blocklistManager as any)._scheduleDownload(false, 5000);
      blocklistManager.setConfig(newBlocklistConfig);
      await jest.runAllTimersAsync();
      expect(downloadSpy).toHaveBeenCalledOnce();
    });

    it('does nothing if not blocklist config has been set', async () => {
      jest.useFakeTimers();
      (blocklistManager as any)._downloadBlocklist(false);
      await jest.runAllTimersAsync();
      expect(downloadSpy).not.toHaveBeenCalled();
      expect(cancelSpy).not.toHaveBeenCalled();
    });

    it('downloads a patch if possible', async () => {
      jest.useFakeTimers();

      // Simulate the old blocklist in the cache and in memory.
      mockCachedData({
        epoch: oldBlocklistEpoch,
        templateUrl,
        // These files are cloned to ensure we don't accidentally modify the originals.
        files: JSON.parse(JSON.stringify(oldBlocklistFiles)),
      });
      blocklist.loadFromBlocklistFiles(
        oldBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );

      blocklistManager.setConfig(newBlocklistConfig);
      await jest.runAllTimersAsync();
      expect(downloadSpy).toHaveBeenCalledOnce();
      expect(downloadSpy.mock.calls[0][0].toString()).toContain('/Diff-');
    }, 100000);

    it('downloads a full blocklist if necessary', async () => {
      jest.useFakeTimers();
      blocklistManager.setConfig(newBlocklistConfig);
      await jest.runAllTimersAsync();
      expect(downloadSpy).toHaveBeenCalledOnce();
      expect(downloadSpy.mock.calls[0][0].toString()).toContain('/Blocklist-');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('makeFullManifestUrl()', () => {
    it('constructs the manifest URL using the information in the provided config', () => {
      const config: BlocklistConfig = {
        name: '1708966154',
        resource: 'https://test.local/blocklists/Default/%TYPE%-%EPOCH%/%NAME%%SAS%',
        sas: '?sv=2024-01-01&se=2025-01-01&sr=c&sp=rl&sig=xyzzy',
      };

      expect(BlocklistManager.makeFullManifestUrl(config).toString()).toEqual(
        'https://test.local/blocklists/Default/Blocklist-1708966154/manifest.json?sv=2024-01-01&se=2025-01-01&sr=c&sp=rl&sig=xyzzy',
      );
    });

    it('throws an error if the resulting URL is invalid', () => {
      const config: BlocklistConfig = {
        name: '1708966154',
        resource: 'https}://deliberately.invalid.url/blocklists/Default/%TYPE%-%EPOCH%/%NAME%%SAS%',
        sas: '?sv=2024-01-01&se=2025-01-01&sr=c&sp=rl&sig=xyzzy',
      };

      expect(() => {
        BlocklistManager.makeFullManifestUrl(config);
      }).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('makeDiffManifestUrl()', () => {
    it('constructs the manifest URL using the information in the provided config and the specified epoch', () => {
      const config: BlocklistConfig = {
        name: '1708966154',
        resource: 'https://test.local/blocklists/Default/%TYPE%-%EPOCH%/%NAME%%SAS%',
        sas: '?sv=2024-01-01&se=2025-01-01&sr=c&sp=rl&sig=xyzzy',
      };

      expect(BlocklistManager.makeDiffManifestUrl(config, 1708966100).toString()).toEqual(
        'https://test.local/blocklists/Default/Diff-1708966100-1708966154/manifest.json?sv=2024-01-01&se=2025-01-01&sr=c&sp=rl&sig=xyzzy',
      );
    });

    it('throws an error if the resulting URL is invalid', () => {
      const config: BlocklistConfig = {
        name: '1708966154',
        resource: 'https}://deliberately.invalid.url/blocklists/Default/%TYPE%-%EPOCH%/%NAME%%SAS%',
        sas: '?sv=2024-01-01&se=2025-01-01&sr=c&sp=rl&sig=xyzzy',
      };

      expect(() => {
        BlocklistManager.makeDiffManifestUrl(config, 1708966100);
      }).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('isBlocklistPatchable()', () => {
    // These tests don't load any actual blocklist files because it's slow and unnecessary.

    it('returns false if no blocklist is loaded', () => {
      expect(BlocklistManager.isBlocklistPatchable(blocklist, newBlocklistConfig)).toBeFalse();
    });

    it('returns false if template URL has changed', () => {
      blocklist.loadFromBlocklistFiles(
        {},
        oldBlocklistEpoch,
        'https://test.local/other-url',
        'download',
      );
      expect(BlocklistManager.isBlocklistPatchable(blocklist, newBlocklistConfig)).toBeFalse();
    });

    it('returns false if the existing blocklist is more recent than the incoming blocklist', () => {
      blocklist.loadFromBlocklistFiles({}, newBlocklistEpoch, templateUrl, 'download');
      expect(BlocklistManager.isBlocklistPatchable(blocklist, oldBlocklistConfig)).toBeFalse();
    });

    it('returns false the existing blocklist is more than 30 days older than the incoming blocklist', () => {
      blocklist.loadFromBlocklistFiles({}, newBlocklistEpoch - 2764800, templateUrl, 'download');
      expect(BlocklistManager.isBlocklistPatchable(blocklist, newBlocklistConfig)).toBeFalse();
    });

    it('returns true if the existing blocklist can be patched to the incoming blocklist', () => {
      blocklist.loadFromBlocklistFiles({}, oldBlocklistEpoch, templateUrl, 'download');
      expect(BlocklistManager.isBlocklistPatchable(blocklist, newBlocklistConfig)).toBeTrue();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('epochToDateString()', () => {
    it('converts a unix timestamp in seconds to an ISO formatted date-time string', () => {
      expect(BlocklistManager.epochToDateString(1706756400)).toEqual('2024-02-01T03:00:00.000Z');
    });
  });
});

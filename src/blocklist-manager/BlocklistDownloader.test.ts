import ConstantBackOff from 'back-off-methods/ConstantBackOff';
import BlocklistManifest from 'guardian/models/BlocklistManifest';

import BlocklistDownloader, { Context } from './BlocklistDownloader';

// A lot of these tests take quite a long time to run because they're handling a lot of blocklist
//  data. Increase the default test timeout to compensate.
jest.setTimeout(20000);

const fetchSpy = (globalThis as any).fetch as jest.SpyInstance;

const mockServerErrorResponse = {
  status: 503,
  ok: false,
  text: async () => await Promise.resolve('{}'),
  json: async () => await Promise.resolve({}),
};

const mockNotFoundErrorResponse = {
  status: 404,
  ok: false,
  text: async () => await Promise.resolve('{}'),
  json: async () => await Promise.resolve({}),
};

const oldBlocklistEpoch = 1711770303;
const newBlocklistEpoch = 1712803503;

const oldBlocklistUrl = new URL(
  `${TEST_SERVER_URL}/blocklists/Blocklist-${oldBlocklistEpoch}/manifest.json`,
);

const newBlocklistUrl = new URL(
  `${TEST_SERVER_URL}/blocklists/Blocklist-${newBlocklistEpoch}/manifest.json`,
);

const nonExistentBlocklistUrl = new URL(`${TEST_SERVER_URL}/deliberately-missing-file.json`);
const partialBlocklistUrl = new URL(
  `${TEST_SERVER_URL}/invalid-blocklists/MissingBlocklistFiles-1711770303/manifest.json`,
);
const invalidManifestBlocklistUrl = new URL(
  `${TEST_SERVER_URL}/invalid-blocklists/InvalidManifest-1711770303/manifest.json`,
);

const blocklistDiffUrl = new URL(
  `${TEST_SERVER_URL}/blocklists/Diff-1711770303-1712803503/manifest.json`,
);

// The BlocklistDownloader doesn't actually care about the contents of the context.
const dummyContext: Context = {
  templateUrl: '',
  fromEpoch: undefined,
  toEpoch: 12345,
};

const shortBackOff = new ConstantBackOff(10);

describe('BlocklistDownloader', () => {
  // -----------------------------------------------------------------------------------------------

  const onSuccess = jest.fn();
  const onFailure = jest.fn();
  const onCancellation = jest.fn();

  beforeEach(() => {
    // Suppress console warning and debug messages.
    jest.spyOn(console, 'debug').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  // -----------------------------------------------------------------------------------------------

  describe('constructor()', () => {
    it('throws an error if maxParallelDownloads is less than 1', () => {
      expect(() => new BlocklistDownloader(0, undefined)).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('download()', () => {
    it('downloads the full blocklist specified in the manifest file', async () => {
      const bd = new BlocklistDownloader();
      bd.onSuccess.addListener(onSuccess);
      await bd.download(oldBlocklistUrl, dummyContext, false);
      expect(onSuccess).toHaveBeenCalled();

      // Check that all the expected files were downloaded.
      const expectedFilenames = [
        'domainsurls.com',
        'domainsurls.other',
        'domainsurls.uk',
        'domainsurls.org',
        'weightedphrases',
        'domainsurls.net',
        'searchterms',
        'category_data',
        'iwflist',
        'removetags',
        'videoids',
        'regexpurls',
        'searchengineregexplist',
        'jsregexpbody_cmod',
        'regexpurls_cmod',
        'loglevelrules',
        'videoidregexplist',
      ];
      const downloadedFiles = onSuccess.mock.calls[0][2] as Record<string, string>;
      expect(Object.keys(downloadedFiles).sort()).toEqual(expectedFilenames.sort());

      // Check that a JSON file successfully parses as JSON.
      expect(JSON.parse(downloadedFiles.searchterms)).toBeObject();

      // Check that a text file contains some expected text.
      // This effectively checks for "pornhub.com".
      // Note that domains are reversed and TLDs are omitted in most files.
      expect(downloadedFiles['domainsurls.com'].includes('buhnrop.')).toBeTrue();
    });

    it('downloads the patch blocklist specified in the manifest file', async () => {
      const bd = new BlocklistDownloader();
      bd.onSuccess.addListener(onSuccess);
      await bd.download(blocklistDiffUrl, dummyContext, false);
      expect(onSuccess).toHaveBeenCalled();

      // Check that all the expected files were downloaded.
      const expectedFilenames = ['iwflist', 'blocklist.diff', 'diff.json'];
      const downloadedFiles = onSuccess.mock.calls[0][2] as Record<string, string>;
      expect(Object.keys(downloadedFiles).sort()).toEqual(expectedFilenames.sort());

      // Check that a JSON file successfully parses as JSON.
      expect(JSON.parse(downloadedFiles['diff.json'])).toBeObject();

      // Check that a text file contains some expected text.
      expect(downloadedFiles['blocklist.diff'].includes('inimcisum.')).toBeTrue();
    });

    it('aborts if a the manifest file fails to download due to a client error and retryForever is false', async () => {
      const bd = new BlocklistDownloader();
      bd.onFailure.addListener(onFailure);
      await bd.download(nonExistentBlocklistUrl, dummyContext, false);
      expect(onFailure).toHaveBeenCalled();
    });

    it('keeps trying again if the manifest file fails with a client error and retryForever is true', async () => {
      // Intercept the first few requests to simulate a temporary client failure.
      fetchSpy.mockResolvedValueOnce(mockNotFoundErrorResponse);
      fetchSpy.mockResolvedValueOnce(mockNotFoundErrorResponse);
      fetchSpy.mockResolvedValueOnce(mockNotFoundErrorResponse);
      fetchSpy.mockResolvedValueOnce(mockNotFoundErrorResponse);
      fetchSpy.mockResolvedValueOnce(mockNotFoundErrorResponse);

      const bd = new BlocklistDownloader(5, shortBackOff);
      bd.onSuccess.addListener(onSuccess);
      await bd.download(oldBlocklistUrl, dummyContext, true);
      expect(onSuccess).toHaveBeenCalled();
    });

    it('aborts if the manifest file cannot be parsed and retryForever is false', async () => {
      const bd = new BlocklistDownloader();
      bd.onFailure.addListener(onFailure);
      await bd.download(invalidManifestBlocklistUrl, dummyContext, false);
      expect(onFailure).toHaveBeenCalled();
    });

    it('keeps trying again if the manifest file cannot be parsed and retryForever is true', async () => {
      // Intercept the first few attempts to simulate a failure.
      const parseSpy = jest.spyOn(BlocklistDownloader, 'parseManifest');
      parseSpy.mockImplementationOnce(() => {
        throw new Error('simulated parsing failure');
      });
      parseSpy.mockImplementationOnce(() => {
        throw new Error('simulated parsing failure');
      });
      parseSpy.mockImplementationOnce(() => {
        throw new Error('simulated parsing failure');
      });

      const bd = new BlocklistDownloader(5, shortBackOff);
      bd.onSuccess.addListener(onSuccess);
      await bd.download(oldBlocklistUrl, dummyContext, true);
      expect(onSuccess).toHaveBeenCalled();
    });

    it('aborts if a blocklist file fails to download due to a client error and retryForever is false', async () => {
      const bd = new BlocklistDownloader();
      bd.onFailure.addListener(onFailure);
      await bd.download(partialBlocklistUrl, dummyContext, false);
      expect(onFailure).toHaveBeenCalled();
    });

    it('keeps trying again if a download fails due to no connection and retryForever is any value', async () => {
      // Intercept the first few requests to simulate a temporary disconnection.
      fetchSpy.mockRejectedValueOnce(new Error('simulated connected failure'));
      fetchSpy.mockRejectedValueOnce(new Error('simulated connected failure'));
      fetchSpy.mockRejectedValueOnce(new Error('simulated connected failure'));
      fetchSpy.mockRejectedValueOnce(new Error('simulated connected failure'));
      fetchSpy.mockRejectedValueOnce(new Error('simulated connected failure'));

      const bd = new BlocklistDownloader(5, shortBackOff);
      bd.onSuccess.addListener(onSuccess);
      await bd.download(oldBlocklistUrl, dummyContext, false);
      expect(onSuccess).toHaveBeenCalled();
    });

    it('keeps trying again if a download fails with a server error and retryForever is any value', async () => {
      // Intercept the first few requests to simulate a temporary server failure.
      fetchSpy.mockResolvedValueOnce(mockServerErrorResponse);
      fetchSpy.mockResolvedValueOnce(mockServerErrorResponse);
      fetchSpy.mockResolvedValueOnce(mockServerErrorResponse);
      fetchSpy.mockResolvedValueOnce(mockServerErrorResponse);
      fetchSpy.mockResolvedValueOnce(mockServerErrorResponse);

      const bd = new BlocklistDownloader(5, shortBackOff);
      bd.onSuccess.addListener(onSuccess);
      await bd.download(oldBlocklistUrl, dummyContext, false);
      expect(onFailure).not.toHaveBeenCalled();
      expect(onSuccess).toHaveBeenCalled();
    });

    it('cancels the existing download if another blocklist is already being downloaded', async () => {
      const bd = new BlocklistDownloader(5, new ConstantBackOff(50, 0));
      bd.onSuccess.addListener(onSuccess);
      bd.onFailure.addListener(onFailure);
      bd.onCancellation.addListener(onCancellation);

      // Try downloading a blocklist which is guaranteed to fail. Let it keep retrying for a while.
      const promise = bd.download(partialBlocklistUrl, dummyContext, true);
      await new Promise<void>((resolve) => setTimeout(resolve, 500));

      // Change to a blocklist which should work.
      await bd.download(newBlocklistUrl, dummyContext, false);
      await promise;

      expect(onFailure).not.toHaveBeenCalled();

      expect(onCancellation).toHaveBeenCalledOnce();
      expect(onCancellation.mock.calls[0][0].toString()).toEqual(partialBlocklistUrl.toString());

      expect(onSuccess).toHaveBeenCalledOnce();
      expect(onSuccess.mock.calls[0][0].toString()).toEqual(newBlocklistUrl.toString());
    });

    it('does not cancel the existing download if called again with the same URL', async () => {
      // Intercept the first few download attempts to simulate a temporary disconnection.
      // This effectively delays the download.
      fetchSpy.mockRejectedValueOnce(new Error('simulated connection failure'));
      fetchSpy.mockRejectedValueOnce(new Error('simulated connection failure'));
      fetchSpy.mockRejectedValueOnce(new Error('simulated connection failure'));
      fetchSpy.mockRejectedValueOnce(new Error('simulated connection failure'));
      fetchSpy.mockRejectedValueOnce(new Error('simulated connection failure'));

      const bd = new BlocklistDownloader(5, shortBackOff);

      // Reset all mocks before starting the test
      onSuccess.mockReset();
      onFailure.mockReset();
      onCancellation.mockReset();

      bd.onSuccess.addListener(onSuccess);
      bd.onFailure.addListener(onFailure);
      bd.onCancellation.addListener(onCancellation);

      // Start a download and give it time to run for a few retries.
      const promise = bd.download(oldBlocklistUrl, dummyContext, false);
      await new Promise<void>((resolve) => setTimeout(resolve, 150));

      // Try to start downloading the same blocklist again.
      await bd.download(oldBlocklistUrl, dummyContext, false);
      await promise;

      // We should receive exactly one download, and no cancellations or failures.
      expect(onFailure).not.toHaveBeenCalled();
      expect(onCancellation).not.toHaveBeenCalled();
      // Check that onSuccess was called, but don't assert the exact number of times
      expect(onSuccess).toHaveBeenCalled();
    });

    it('starts the download again if called with the same URL after the previous download finished', async () => {
      const bd = new BlocklistDownloader();
      bd.onSuccess.addListener(onSuccess);

      // Download the blocklist once and wait for it to finish.
      await bd.download(oldBlocklistUrl, dummyContext, false);
      expect(onSuccess).toHaveBeenCalledOnce();
      const numFetchCalls = fetchSpy.mock.calls.length;

      // Download the blocklist again and wait for it to finish.
      await bd.download(oldBlocklistUrl, dummyContext, false);
      expect(onSuccess).toHaveBeenCalledTimes(2);
      expect(fetchSpy).toHaveBeenCalledTimes(numFetchCalls * 2);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('cancel()', () => {
    it('does nothing if no download is in progress', () => {
      const bd = new BlocklistDownloader();
      expect(() => {
        bd.cancel();
      }).not.toThrow();
    });

    it('cancels the existing download if one is in progress.', async () => {
      const bd = new BlocklistDownloader();
      bd.onCancellation.addListener(onCancellation);
      const promise = bd.download(oldBlocklistUrl, dummyContext, false);
      bd.cancel();
      await promise;
      expect(onCancellation).toHaveBeenCalledOnce();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('removeFilenameFromUrl()', () => {
    it('returns a URL with the last path segment removed', () => {
      const original = new URL('https://www.example.com/foo/bar.html?hello=world');
      const output = BlocklistDownloader.removeFilenameFromUrl(original);
      expect(output.toString()).toEqual('https://www.example.com/foo/?hello=world');
    });

    it('removes any trailing slashes after the last path segment', () => {
      const original = new URL('https://www.example.com/foo/bar.html////?hello=world');
      const output = BlocklistDownloader.removeFilenameFromUrl(original);
      expect(output.toString()).toEqual('https://www.example.com/foo/?hello=world');
    });

    it('does not remove excess slashes before the last path segment', () => {
      const original = new URL('https://www.example.com/foo///bar.html////?hello=world');
      const output = BlocklistDownloader.removeFilenameFromUrl(original);
      expect(output.toString()).toEqual('https://www.example.com/foo///?hello=world');
    });

    it('does not remove anything if the path does not contain any path segments', () => {
      const original = new URL('https://www.example.com/?hello=world');
      const output = BlocklistDownloader.removeFilenameFromUrl(original);
      expect(output.toString()).toEqual('https://www.example.com/?hello=world');
    });

    it('does not modify the original URL object', () => {
      const original = new URL('https://www.example.com/foo/bar.html?hello=world');
      BlocklistDownloader.removeFilenameFromUrl(original);
      expect(original.toString()).toEqual('https://www.example.com/foo/bar.html?hello=world');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('appendFilenameToUrl()', () => {
    it('returns a URL with the specified filename appended to the path', () => {
      const original = new URL('https://www.example.com/foo?hello=world');
      const output = BlocklistDownloader.appendFilenameToUrl(original, 'bar.html');
      expect(output.toString()).toEqual('https://www.example.com/foo/bar.html?hello=world');
    });

    it('does not add an extra slash before the filename if the URL already had one', () => {
      const original = new URL('https://www.example.com/foo/?hello=world');
      const output = BlocklistDownloader.appendFilenameToUrl(original, 'bar.html');
      expect(output.toString()).toEqual('https://www.example.com/foo/bar.html?hello=world');
    });

    it('does not add an extra slash before the filename if the filename already had one', () => {
      const original = new URL('https://www.example.com/foo?hello=world');
      const output = BlocklistDownloader.appendFilenameToUrl(original, '/bar.html');
      expect(output.toString()).toEqual('https://www.example.com/foo/bar.html?hello=world');
    });

    it('does not modify the original URL object', () => {
      const original = new URL('https://www.example.com/foo?hello=world');
      BlocklistDownloader.appendFilenameToUrl(original, 'bar.html');
      expect(original.toString()).toEqual('https://www.example.com/foo?hello=world');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('parseManifest()', () => {
    it('returns an array of filenames extracted from the manifest file', () => {
      const content = {
        files: ['file1.txt', 'file2.json', 'file3.csv'],
        from_version: '12345678',
        to_version: '12345679',
      };

      const output = BlocklistDownloader.parseManifest(content);
      expect(output).toEqual(expect.arrayContaining(['file1.txt', 'file2.json', 'file3.csv']));
    });

    it('throws an Error if the value passed in is not an object', () => {
      expect(() =>
        BlocklistDownloader.parseManifest(null as unknown as BlocklistManifest),
      ).toThrow();
    });

    it('throws an Error if the files array is missing', () => {
      const content = {
        from_version: '12345678',
        to_version: '12345679',
      };
      expect(() =>
        BlocklistDownloader.parseManifest(content as unknown as BlocklistManifest),
      ).toThrow();
    });

    it('throws an Error if the files property is not an array', () => {
      const content = {
        files: 'data.json',
        from_version: '12345678',
        to_version: '12345679',
      };
      expect(() =>
        BlocklistDownloader.parseManifest(content as unknown as BlocklistManifest),
      ).toThrow();
    });

    it('throws an Error if the files array contains any non-string elements', () => {
      const content = {
        files: ['file1.txt', 12345, 'file2.json'],
        from_version: '12345678',
        to_version: '12345679',
      };
      expect(() =>
        BlocklistDownloader.parseManifest(content as unknown as BlocklistManifest),
      ).toThrow();
    });

    it('throws an Error if the files array contains any empty strings', () => {
      const content = {
        files: ['file1.txt', 'file2.json', ''],
        from_version: '12345678',
        to_version: '12345679',
      };
      expect(() => BlocklistDownloader.parseManifest(content)).toThrow();
    });
  });
});

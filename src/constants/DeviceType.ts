/**
 * Enumerates the types of device recognised by the product.
 * The numerical value is used in various communications with the cloud.
 *
 * @deprecated This list is from a pre-DMS Smoothwall API. Get rid of it.
 */
enum DeviceType {
  messageHub = 0,
  portal = 1,
  windows = 2,
  macOs = 3,
  chromebook = 4,
  cldfltWindows = 5,
  cldfltMacOs = 6,
  cldfltChromebook = 7,
  crmgtWindows = 8,
  crmgtMacOs = 9,
  crmgtChromebook = 10,
  cldfltiOS = 11,
  onPremise = 12,
}

export default DeviceType;

/**
 * Enumerates the main operating modes of the filter product.
 */
enum OperatingMode {
  /**
   * The extension is running on its own, without any additional software.
   * Provisioning information comes from the browser via managed storage.
   * Standalone mode is the only option on Chromebook, but it's also possible on other platforms.
   * In the past, this has also been known as "chromeos" mode.
   */
  standalone,

  /**
   * The extension receives basic provisioning information from the Smoothwall native agent.
   * In this mode, the extension still handles most of the cloud connection itself.
   */
  native,

  /**
   * The extension receives information from the Qoria native agent (aka Companion Agent).
   */
  companion,
}

export default OperatingMode;

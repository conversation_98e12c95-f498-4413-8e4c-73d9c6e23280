/**
 * Constants and types for Content Aware extension communication
 * This file centralizes all message types and constants used in Content Aware extension communication
 */

/**
 * Message types for communication with the Content Aware extension
 */
export enum ContentAwareMessageTypes {
  /**
   * Login message to activate the Content Aware extension
   */
  LOGIN = 'LOGIN',

  /**
   * Health check message to verify Content Aware extension status, also to determine if we need to re-login the content aware extension
   */
  IS_LOGGED_IN = 'IS_LOGGED_IN',

  /**
   * Logout message to deactivate the Content Aware extension
   */
  LOGOUT = 'LOGOUT',

  /**
   * Configuration update message to send settings to the Content Aware extension
   */
  UPDATE_CONFIG_ALL = 'UPDATE-CONFIG-ALL',
}

/**
 * Default interval for health checks in milliseconds (30 seconds)
 * TODO: fix these so they are injected into the Content Aware service rather than hardcoded
 */
export const CONTENT_AWARE_HEALTH_CHECK_INTERVAL = 30000;

/**
 * Default timeout for message responses in milliseconds (5 seconds)
 * TODO: fix these so they are injected into the Content Aware service rather than hardcoded
 */
export const CONTENT_AWARE_MESSAGE_TIMEOUT = 5000;

/**
 * Known Content Aware extension IDs for different environments
 */
export const CONTENT_AWARE_EXTENSION_IDS = {
  /**
   * Production Content Aware extension ID
   * TODO: Replace with actual production extension ID when available
   */
  PRODUCTION: 'content-aware-extension-id',

  /**
   * Development/QA Content Aware extension ID
   * TODO: Replace with actual dev/QA extension ID when available
   */
  DEVELOPMENT: 'content-aware-extension-dev-id',
} as const;

/**
 * Known Content Aware extension names that we should look for
 */
export const CONTENT_AWARE_EXTENSION_NAMES = [
  'Content Aware',
  'Content Aware Extension',
  'Content Aware Dev',
  'Content Aware QA',
] as const;

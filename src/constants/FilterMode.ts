/**
 * Enumerates the states which the filter can be in.
 */
enum FilterMode {
  /**
   * The state hasn't been determined yet.
   * This is always the initial state when the service worker starts. We need to wait until we have
   *  loaded data from cache or downloaded it from the cloud before we can do any filtering.
   * In this mode, requests are always allowed initially, but page content is hidden by the content
   *  script.
   * This is known as "mode 0" in the diagnostics page.
   */
  unknown,

  /**
   * We are using the built-in (hard-coded) "mini-filter" rules.
   * This is usually a temporary state we move into while we are connecting to the cloud and
   *  downloading customer data. However, it can be disabled by policy. If it's disabled then we
   *  stay in "pending" state until/unless we're ready to move into "full" filter mode.
   * This is known as "mode 1" in the diagnostics page.
   */
  mini,

  /**
   * We're using the latest available filtering rules (aka "full filter") downloaded from the cloud.
   * This is the main state we expect to be in most of the time.
   * This is known as "mode 2" in the diagnostics page.
   */
  full,

  /**
   * Filtering is disabled, meaning all requests and content are allowed.
   * We end up in this state if the customer isn't licensed.
   * The diagnostics page doesn't show any mode for this.
   */
  disabled,
}

export default FilterMode;

/**
 * Constants for safeguarding categories and themes
 * This file centralizes all string constants used in safeguarding to prevent issues
 * if category or theme names change in the future.
 */

/**
 * Critical safeguarding themes that require special handling
 * Note: Most themes should be dynamically loaded from the blocklist
 * Only themes that require special handling should be defined here
 */
export const CRITICAL_SAFEGUARDING_THEMES = {
  SUICIDE: 'Suicide',
} as const;

/**
 * Checks if a theme is the critical suicide theme
 * This function centralizes the theme checking logic to avoid hardcoding theme names
 * throughout the application
 *
 * @param theme The theme to check
 * @returns True if the theme is the critical suicide theme
 */
export const isSuicideTheme = (theme: string): boolean => {
  if (theme === undefined || theme === null || theme === '') {
    return false;
  }
  return theme.toLowerCase() === CRITICAL_SAFEGUARDING_THEMES.SUICIDE.toLowerCase();
};

/**
 * Content types used throughout the application
 */
export const CONTENT_TYPES = {
  MAIN_FRAME: 'main_frame',
  SUB_FRAME: 'sub_frame',
  STYLESHEET: 'stylesheet',
  SCRIPT: 'script',
  IMAGE: 'image',
  FONT: 'font',
  OBJECT: 'object',
  XMLHTTPREQUEST: 'xmlhttprequest',
  PING: 'ping',
  CSP_REPORT: 'csp_report',
  MEDIA: 'media',
  WEBSOCKET: 'websocket',
  OTHER: 'other',
} as const;

/**
 * Type definitions for the constants to enable type checking
 */
export type CriticalSafeguardingTheme =
  (typeof CRITICAL_SAFEGUARDING_THEMES)[keyof typeof CRITICAL_SAFEGUARDING_THEMES];
export type SafeguardingTheme = string; // Now a string type since themes are dynamic

import { CRITICAL_SAFEGUARDING_THEMES, isSuicideTheme } from './SafeguardingConstants';

describe('SafeguardingConstants', () => {
  describe('isSuicideTheme', () => {
    it('should return true for exact match of suicide theme', () => {
      expect(isSuicideTheme(CRITICAL_SAFEGUARDING_THEMES.SUICIDE)).toBe(true);
    });

    it('should return true for case-insensitive match of suicide theme', () => {
      expect(isSuicideTheme('suicide')).toBe(true);
      expect(isSuicideTheme('SUICIDE')).toBe(true);
      expect(isSuicideTheme('Suicide')).toBe(true);
      expect(isSuicideTheme('SuIcIdE')).toBe(true);
    });

    it('should return false for non-suicide themes', () => {
      expect(isSuicideTheme('Radicalisation')).toBe(false);
      expect(isSuicideTheme('Adult content')).toBe(false);
      expect(isSuicideTheme('Bullying')).toBe(false);
      expect(isSuicideTheme('')).toBe(false);
    });

    it('should handle undefined or null inputs safely', () => {
      // @ts-expect-error Testing with invalid input
      expect(isSuicideTheme(undefined)).toBe(false);
      // @ts-expect-error Testing with invalid input
      expect(isSuicideTheme(null)).toBe(false);
    });
  });
});

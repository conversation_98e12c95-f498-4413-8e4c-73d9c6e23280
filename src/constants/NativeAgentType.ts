/**
 * Enumerates the different types of native agent this software may connect to.
 * Note that no native agent is used if the operating mode is "standalone".
 */
enum NativeAgentType {
  /**
   * The Smoothwall agent is used if the operating mode is "native".
   * This agent is also known as the Smoothwall Unified Client.
   * It is superseded by the Qoria agent.
   */
  smoothwall,

  /**
   * The Qoria agent is used if the operating mode is "companion".
   * This agent is sometimes referred to as the Companion Agent.
   * It was previously the Linewize agent, but was adapted for use as a unified Qoria agent.
   */
  qoria,
}

export default NativeAgentType;

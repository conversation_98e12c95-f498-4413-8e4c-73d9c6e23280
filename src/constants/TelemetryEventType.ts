/**
 * TelemetryEventType enum
 * This enum defines the different types of telemetry events that can be logged.
 * This should be kept up to date with this document: https://familyzone.atlassian.net/wiki/spaces/NC/pages/2694615433643/UCBE+Logging+Strategy+Cloud+Filter+browser+extension
 */
export enum TelemetryEventType {
  ExtensionUpdateAvailable = 'extension-update-available',
  Provisioned = 'provisioned',
  ExtensionInstalled = 'extension-installed',
  LogFileCreation = 'log-file-creation',
  LogUpload = 'log-upload',
  BlocklistLoaded = 'blocklist-loaded',
  FilterModeChanged = 'filter-mode-changed',
  ProvisioningChanged = 'provisioning-changed',
  NativeConnection = 'native-connection',
  UserGroups = 'user-groups',
  ContentAwareExtensionStatus = 'content-aware-extension-status',
  ContentAwareLogin = 'content-aware-login',
  ContentAwareConfig = 'content-aware-config',
  ContentAwareError = 'content-aware-error',
  DeviceRegistration = 'device-registration',
  FirebaseSignOut = 'firebase-sign-out',
  FirestoreDocumentListenerSubscribed = 'firestore-document-listener-subscribed',
  UserBanned = 'user-banned',
  PolicyUpdate = 'policy-update',
  GroupsMapped = 'groups-mapped',
  RealtimeConnection = 'realtime-connection',
  SafeguardAlert = 'safeguard-alert',

  // Error events
  StorageFailed = 'storage-failed',
  PolicyUpdateFailed = 'policy-update-failed',
  ClientSettingsParsingFailed = 'client-settings-parsing-failed',
  UserGroupsFailed = 'user-groups-failed',
  FirebaseAuthFailed = 'firebase-auth-failed',
  SafeguardAlertFailed = 'safeguard-alert-failed',
  FirebaseConfigFailed = 'firebase-config-failed',
  InvalidProductConfig = 'invalid-product-config',
  DeviceRegistrationFailed = 'device-registration-failed',
  FirestoreDocumentListenerError = 'firestore-document-listener-error',
  BlocklistLoadFailed = 'blocklist-load-failed',
  BlocklistDownloadFailed = 'blocklist-download-failed',
  BlocklistPatchFailed = 'blocklist-patch-failed',
  LogUploadFailed = 'log-upload-failed',
  LogFileCreationFailed = 'log-file-creation-failed',
  NativeConnectionFailed = 'native-connection-failed',
  UncaughtError = 'uncaught-error',

  // Trace events
  DeviceRegistrationJwtExpired = 'device-registration-jwt-expired',

  // Metric events (string identifiers)
  SecretKnockHttpSuccess = 'secret-knock-http-success',
  SecretKnockHttpFailure = 'secret-knock-http-failure',
}

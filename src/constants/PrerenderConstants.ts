/**
 * Constants for handling Chrome's prerender functionality.
 * These constants are used to detect and handle prerendered pages appropriately.
 */

/**
 * Document lifecycle states as defined by Chrome's webRequest API.
 * Available in Chrome 106+.
 */
export enum DocumentLifecycle {
  /**
   * The document is being prerendered and is not yet visible to the user.
   */
  PRERENDER = 'prerender',

  /**
   * The document is active and visible to the user.
   */
  ACTIVE = 'active',

  /**
   * The document is cached (e.g., in back/forward cache).
   */
  CACHED = 'cached',

  /**
   * The document is being deleted.
   */
  PENDING_DELETION = 'pending_deletion',
}

/**
 * Special window ID used for prerendered pages.
 * These pages don't belong to any visible window.
 */
export const PRERENDER_WINDOW_ID = -1;

export default DocumentLifecycle;

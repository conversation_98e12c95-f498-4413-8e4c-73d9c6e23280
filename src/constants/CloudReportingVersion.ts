/**
 * Enumerates the version for cloud access logs ingestion.
 */
export enum CloudReportingVersion {
  /**
   * Access logs should not be uploaded but continue to be spooled locally.
   */
  disabled = 0,

  /**
   * Ingestion v2 PUTs stringified accesslogs directly to cloud
   *   Uses productConfig.cldflt.configuration.accesslogs
   */
  v2 = 2,

  /**
   * Ingestion v3 PUTs gziped, stringifed accesslogs seperated by log level and hour.
   *   Uses productConfig.cldrpt.configuration.accesslogs
   */
  v3 = 3,

  /**
   * Ingestion v4 PUTs stringified access logs to an api.
   * Uses productConfig.cldrpt.accessLogsIngestV4
   */
  v4 = 4,
}

export default CloudReportingVersion;

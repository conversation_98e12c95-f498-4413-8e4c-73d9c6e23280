declare module 'ahocorasick' {
  /**
   * Builds a prefix tree to efficiently search a string for a fixed set of keywords.
   * This uses the Aho-Corasick algorithm.
   */
  export default class AhoCorasick {
    /**
     * Construct a new instance which will search for the given keywords.
     *
     * @param keywords The keywords to search for.
     */
    public constructor(keywords: string[]);

    /**
     * Search a string for any occurrences of the keywords specified in the constructor.
     *
     * @param string The string to search inside.
     * @return Returns an array of matches. Each element represents a single location in the string
     *  where one or more keywords ended.
     */
    public search: (string: string) => AhoCorasickMatch[];
  }

  /**
   * Describes one result from the search.
   * One result may contain multiple keywords if they all ended at the same place.
   */
  export interface AhoCorasickMatch {
    /**
     * The index of the character position in the haystack where the match finished.
     */
    0: number;

    /**
     * The keywords which were found at this location.
     */
    1: string[];
  }
}

import ObjectLiteral from './ObjectLiteral';
import jwtDecode from 'jwt-decode';

/**
 * A strongly-typed wrapper for a JSON Web Token.
 * This provides some convenience functionality to parse and extract common information, such as the
 *  expiration time, and custom claims.
 *
 * @warning This class does not validate JWT signatures, and therefore cannot be used for
 *  authentication purposes. It is only intended to be used by a client which receives a JWT and
 *  which would like to extract some information from it.
 */
export default class Jwt {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Initialise this instance using an encoded JWT.
   *
   * @param token The encode JWT from which to initialise this instance.
   * @throws {InvalidTokenError} The specified token is not a valid JWT.
   */
  public constructor(token: string) {
    this.token = token;
    this.header = jwtDecode(token, { header: true });
    this.payload = jwtDecode(token);
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Get the date/time this JWT was issued at, if known.
   *
   * @returns A Date object initialised from the issue time of this JWT, if it has one. Returns
   *  undefined otherwise.
   */
  public get issueDate(): Date | undefined {
    // The "iat" property gives the issue date/time as a Unix timestamp (seconds).
    if (typeof this.payload.iat !== 'number') {
      return undefined;
    }
    return new Date(this.payload.iat * 1000);
  }

  /**
   * Get the date/time this JWT expires, if known.
   *
   * @returns A Date object initialised from the expiry time of this JWT, if it has one. Returns
   *  undefined otherwise.
   */
  public get expiryDate(): Date | undefined {
    // The "exp" property gives the issue date/time as a Unix timestamp (seconds).
    if (typeof this.payload.exp !== 'number') {
      return undefined;
    }
    return new Date(this.payload.exp * 1000);
  }

  /**
   * Check if this JWT has already expired.
   *
   * @returns True if this JWT contains an expiry timestamp, and that date/time has passed.
   */
  public get hasExpired(): boolean {
    return typeof this.payload.exp === 'number' && this.payload.exp * 1000 < Date.now();
  }

  /**
   * Check if this JWT expires shortly (or has already expired).
   *
   * @param minutes The number of minutes leeway for expiry. Default is 1. For example, if this is
   *  5, then it checks if the JWT expires within 5 minutes from now.
   * @returns True if this JWT expires soon, or has already expired. False if the JWT doesn't expire
   *  soon, or it doesn't have an expiry date at all.
   */
  public readonly willHaveExpiredSoon = (minutes: number = 1): boolean => {
    return (
      typeof this.payload.exp === 'number' && (this.payload.exp - minutes * 60) * 1000 < Date.now()
    );
  };

  /**
   * Check if the payload contains a claim with the specified name.
   *
   * @param name The name of the claim to check for.
   * @returns True if the payload contains the named claim, or false if not.
   */
  public readonly hasClaim = (name: string): boolean => {
    return Object.prototype.hasOwnProperty.call(this.payload, name);
  };

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The original encoded JWT.
   */
  public readonly token: string;

  /**
   * The decoded header portion of the JWT, stored as a plain object.
   */
  public readonly header: ObjectLiteral<any>;

  /**
   * The decoded payload portion of the JWT, stored as a plain object.
   */
  public readonly payload: ObjectLiteral<any>;
}

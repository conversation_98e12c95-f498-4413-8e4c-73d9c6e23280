/**
 * Describes an object which contains information about connecting directly to the cloud.
 * This information is retrieved during device registration (aka licensing). This is only used when
 *  running in ChromeOS. If we have a native daemon, then that handles the cloud connection instead.
 *
 * @todo Possibly make this a class which allows details to be extracted from the connection string?
 */
export default interface CloudConnectionInfo {
  /**
   * An opaque string defining how we connect to the IoT hub.
   */
  connectionString: string;
}

/**
 * Contains information about the result of a download request from blob storage.
 */
export default class BlobDownloadResult {
  constructor(
    url: string,
    data: any,
    isSuccess: boolean,
    responseStatus: number,
    message: string = '',
  ) {
    this.url = url;
    this.data = data;
    this.isSuccess = isSuccess;
    this.message = message;
    this.responseStatus = responseStatus;
  }

  /**
   * The url the data was downloaded from.
   */
  public readonly url: string;

  /**
   * The data that was downloaded.
   */
  public readonly data: any;

  /**
   * Indicates if the download was a success.
   */
  public readonly isSuccess: boolean;

  /**
   * The response status from the fetch request.
   */
  public readonly responseStatus: number;

  /**
   * If there was an error this will contain the message.
   */
  public readonly message: string;
}

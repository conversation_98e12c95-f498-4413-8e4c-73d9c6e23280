import { ContentAwareMessageTypes } from 'constants/ContentAwareTypes';

export interface IBaseContentAwareRequestMessage {
  MessageType: ContentAwareMessageTypes;
}

/**
 * Login message to activate the Content Aware extension
 */
export type IContentAwareLoginRequestMessage = {
  MessageType: ContentAwareMessageTypes.LOGIN;
  Data: {
    VendorLicenseKey: string;
    OrganisationID: string;
  };
} & IBaseContentAwareRequestMessage;

/**
 * Configuration message to update Content Aware extension settings
 */
export type IContentAwareUpdateConfigAllRequestMessage = {
  MessageType: ContentAwareMessageTypes.UPDATE_CONFIG_ALL;
  Data: {
    DomainConfiguration: {
      DefaultRules: {
        // numbers set sensitivity which is interpreted like: 0.99 - Low, 0.6 - Medium, 0.4 - High, -1 - Disabled
        swimwear: number;
        porn: number;
        gore: number;
      };
      GuiltByAssociationEnabled: boolean;
      GuiltByAssociationBlockThreshold: number;
      GuiltByAssociationIgnoreAfterCleanImagesThreshold: number;
      NeverRunOn: string[];
    };
  };
} & IBaseContentAwareRequestMessage;

/**
 * Logout message to deactivate the Content Aware extension
 */
export type IContentAwareLogoutRequestMessage = {
  MessageType: ContentAwareMessageTypes.LOGOUT;
} & IBaseContentAwareRequestMessage;

export type IContentAwareIsLoggedInRequestMessage = {
  MessageType: ContentAwareMessageTypes.IS_LOGGED_IN;
} & IBaseContentAwareRequestMessage;

/**
 * Discriminated union of all Content Aware request message types
 */
export type IContentAwareRequestMessagesUnion =
  | IContentAwareLoginRequestMessage
  | IContentAwareUpdateConfigAllRequestMessage
  | IContentAwareLogoutRequestMessage
  | IContentAwareIsLoggedInRequestMessage;

// message data structures based on documentation from https://familyzone.atlassian.net/wiki/spaces/PROD/pages/2695115997227/Content+Aware+Inter+Extension+communication
export interface ContentAwareBaseResponseMessage {
  Success: boolean;
}

export type IContentAwareIsLoggedInResponseMessage = {
  IsLoggedIn: boolean;
} & ContentAwareBaseResponseMessage;

export type IContentAwareLoginResponseMessage = ContentAwareBaseResponseMessage;
export type IContentAwareLogoutResponseMessage = ContentAwareBaseResponseMessage;
export type IContentAwareConfigAllResponseMessage = ContentAwareBaseResponseMessage;

/**
 * Information about the Content Aware extension
 */
export interface IContentAwareExtensionInfo {
  /**
   * Extension ID
   */
  id: string;

  /**
   * Extension name
   */
  name: string;

  /**
   * Extension version
   */
  version: string;

  /**
   * Whether the extension is enabled
   */
  enabled: boolean;
}

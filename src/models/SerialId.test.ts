import SerialId from './SerialId';

const validSerialIdPattern = /^[A-Z0-9]{16}$/;

describe('SerialId', () => {
  // -----------------------------------------------------------------------------------------------

  describe('constructor()', () => {
    it('stores the specified serial if it is strictly valid', () => {
      const serialId = new SerialId('TESTJXF2LTPBP84S');
      expect(serialId.get()).toEqual('TESTJXF2LTPBP84S');
    });

    it('strips spaces from the serial if strict is false', () => {
      const serialId = new SerialId('TEST JXF2 LTPB P84S', false);
      expect(serialId.get()).toEqual('TESTJXF2LTPBP84S');
    });

    it('converts letters to uppercase if strict is false', () => {
      const serialId = new SerialId('testjxf2ltpbp84s', false);
      expect(serialId.get()).toEqual('TESTJXF2LTPBP84S');
    });

    it('strict defaults to false if not specified', () => {
      const serialId = new SerialId('test jxf2 ltpb p84s');
      expect(serialId.get()).toEqual('TESTJXF2LTPBP84S');
    });

    it('throws an error if the specified serial contains spaces and strict is true', () => {
      expect(() => new SerialId('TEST JXF2 LTPB P84S', true)).toThrow();
    });

    it('throws an error if the specified serial contains lowercase letters and strict is true', () => {
      expect(() => new SerialId('testjxf2ltpbp84s', true)).toThrow();
    });

    it('throws an error if the specified serial contains an invalid character', () => {
      expect(() => new SerialId('TEST!XF2LTPBP84S')).toThrow();
    });

    it('throws an error if the specified serial is the wrong length', () => {
      expect(() => new SerialId('TESTJXF2LTPBP84SS')).toThrow();
    });

    it('throws an error if the specified serial contains the wrong checksum', () => {
      expect(() => new SerialId('TESTJXF2LTPBP85T')).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('random()', () => {
    it('returns a valid serial', () => {
      const serialId = SerialId.random();
      expect(serialId.get()).toMatch(validSerialIdPattern);
      expect(SerialId.isValid(serialId.get())).toBeTrue();
    });

    it('returns a different serial each time it is called', () => {
      const serialId1 = SerialId.random();
      const serialId2 = SerialId.random();
      expect(serialId1.get()).not.toEqual(serialId2.get());
    });

    it('returns a valid serial when a prefix is specified', () => {
      const serialId = SerialId.random('BLAH');
      expect(SerialId.isValid(serialId.get())).toBeTrue();
    });

    it('includes the specified prefix in the serial', () => {
      const serialId = SerialId.random('BLAH');
      expect(serialId.get().startsWith('BLAH')).toBeTrue();
    });

    it('returns a different serial each time it is called with the same prefix', () => {
      const serialId1 = SerialId.random('BLAH');
      const serialId2 = SerialId.random('BLAH');
      expect(serialId1.get()).not.toEqual(serialId2.get());
    });

    it('throws an error if the prefix contains an invalid character', () => {
      expect(() => SerialId.random('BLAH!')).toThrow();
    });

    it('throws an error if the prefix is longer than 14 characters', () => {
      expect(() => SerialId.random('BLAHBLAHBLAHBLAH')).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('get()', () => {
    it('returns a string representation of the serial', () => {
      const serialId = new SerialId('TESTX8G3EKMS1CXK');
      expect(serialId.get()).toEqual('TESTX8G3EKMS1CXK');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('toString()', () => {
    it('returns a string representation of the serial', () => {
      const serialId = new SerialId('TESTX8G3EKMS1CXK');
      expect(serialId.toString()).toEqual('TESTX8G3EKMS1CXK');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('set()', () => {
    it('stores the specified serial if it is strictly valid', () => {
      const serialId = new SerialId('TESTX8G3EKMS1CXK');
      serialId.set('TESTUSULDUCRBVJQ');
      expect(serialId.get()).toEqual('TESTUSULDUCRBVJQ');
    });

    it('strips spaces from the serial if strict is false', () => {
      const serialId = new SerialId('TESTX8G3EKMS1CXK');
      serialId.set('TEST USUL DUCR BVJQ', false);
      expect(serialId.get()).toEqual('TESTUSULDUCRBVJQ');
    });

    it('converts letters to uppercase if strict is false', () => {
      const serialId = new SerialId('TESTX8G3EKMS1CXK');
      serialId.set('testusulducrbvjq');
      expect(serialId.get()).toEqual('TESTUSULDUCRBVJQ');
    });

    it('strict defaults to false if not specified', () => {
      const serialId = new SerialId('test jxf2 ltpb p84s');
      expect(serialId.get()).toEqual('TESTJXF2LTPBP84S');
    });

    it('throws an error if the specified serial contains spaces and strict is true', () => {
      expect(() => new SerialId('TEST JXF2 LTPB P84S', true)).toThrow();
    });

    it('throws an error if the specified serial contains lowercase letters and strict is true', () => {
      expect(() => new SerialId('testjxf2ltpbp84s', true)).toThrow();
    });

    it('throws an error if the specified serial contains an invalid character', () => {
      expect(() => new SerialId('TEST!XF2LTPBP84S')).toThrow();
    });

    it('throws an error if the specified serial is the wrong length', () => {
      expect(() => new SerialId('TESTJXF2LTPBP84SS')).toThrow();
    });

    it('throws an error if the specified serial contains the wrong checksum', () => {
      expect(() => new SerialId('TESTJXF2LTPBP85T')).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('isUnclSerial()', () => {
    it('returns true if serial begins with UNCL prefix', () => {
      const serialId = new SerialId('UNCLTEST614NESL8');
      expect(serialId.isUnclSerial()).toBeTrue();
    });

    it('returns false if serial does not begin with UNCL prefix', () => {
      const serialId = new SerialId('TESTJXF2LTPBP84S');
      expect(serialId.isUnclSerial()).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('getGlsHash()', () => {
    it('returns the gls hash of the stored serial', () => {
      expect(new SerialId('UNCLTESTSU1GBD34').getGlsHash()).toEqual(
        'kkzf5np33lyn9g3aeb13tyk91ha63g89',
      );

      expect(new SerialId('TESTNX4K0HEU6Q2S').getGlsHash()).toEqual(
        'r47nrl9ip6ytciwyj1dhqkzg3froi6qg',
      );

      expect(new SerialId('TESTL20JH84TJ1WU').getGlsHash()).toEqual(
        'x4zp8fml5o7wb27x5sggwvxexf6tffhx',
      );
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('getPrefix()', () => {
    it('returns the specified number of characters from the start of the serial', () => {
      const serialId = new SerialId('TESTJXF2LTPBP84S');
      expect(serialId.getPrefix(6)).toEqual('TESTJX');
    });

    it('returns an empty string if the specified length is zero', () => {
      const serialId = new SerialId('TESTJXF2LTPBP84S');
      expect(serialId.getPrefix(0)).toEqual('');
    });

    it('throws an error if the specified prefix length is greater than 16', () => {
      const serialId = new SerialId('TESTJXF2LTPBP84S');
      expect(() => serialId.getPrefix(17)).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('hasPrefix()', () => {
    it('returns true if the serial starts with the specified string', () => {
      const serialId = new SerialId('TESTNX4K0HEU6Q2S');
      expect(serialId.hasPrefix('TESTNX4')).toBeTrue();
    });

    it('returns false if the serial does not start with the specified string', () => {
      const serialId = new SerialId('TESTNX4K0HEU6Q2S');
      expect(serialId.hasPrefix('BLAH')).toBeFalse();
    });

    it('returns false if the specified prefix is empty', () => {
      const serialId = new SerialId('TESTNX4K0HEU6Q2S');
      expect(serialId.hasPrefix('')).toBeFalse();
    });

    it('returns false if the specified prefix contains an invalid character', () => {
      const serialId = new SerialId('TESTNX4K0HEU6Q2S');
      expect(serialId.hasPrefix('BLAH!')).toBeFalse();
    });

    it('returns false if the specified prefix is too long for a serial', () => {
      const serialId = new SerialId('TESTNX4K0HEU6Q2S');
      expect(serialId.hasPrefix('BLAHBLAHBLAHBLAHBLAH')).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('equals()', () => {
    it('returns true if the serials are the same', () => {
      const serialId1 = new SerialId('TESTNX4K0HEU6Q2S');
      const serialId2 = new SerialId('TESTNX4K0HEU6Q2S');
      expect(serialId1.equals(serialId2)).toBeTrue();
      expect(serialId1.equals('TESTNX4K0HEU6Q2S')).toBeTrue();
    });

    it('returns false if the serials are differnet', () => {
      const serialId1 = new SerialId('TESTNX4K0HEU6Q2S');
      const serialId2 = new SerialId('TESTJXF2LTPBP84S');
      expect(serialId1.equals(serialId2)).toBeFalse();
      expect(serialId1.equals('TESTJXF2LTPBP84S')).toBeFalse();
    });

    it('is not case sensitive', () => {
      const serialId1 = new SerialId('TESTNX4K0HEU6Q2S');
      const serialId2 = new SerialId('testnx4k0heu6q2s');
      expect(serialId1.equals(serialId2)).toBeTrue();
      expect(serialId1.equals('testnx4k0heu6q2s')).toBeTrue();
    });

    it('applies non-strict validation if other serial is specified as a string', () => {
      const serialId1 = new SerialId('TESTNX4K0HEU6Q2S');
      expect(serialId1.equals('    test nx4k 0heu 6q2s   ')).toBeTrue();
    });

    it('returns false if the other serial is specified as a string and is invalid', () => {
      const serialId1 = new SerialId('TESTNX4K0HEU6Q2S');
      expect(serialId1.equals('! blah blah *')).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('clean()', () => {
    it('returns the specified string with all whitespace removed', () => {
      expect(SerialId.clean(' TEST  \t   NX4K0HE\nU6Q2S ')).toEqual('TESTNX4K0HEU6Q2S');
    });

    it('returns the specified string with letters converted to uppercase', () => {
      expect(SerialId.clean('testnx4k0heu6q2s')).toEqual('TESTNX4K0HEU6Q2S');
    });

    it('returns the specified string unmodified if it is already strictly valid', () => {
      expect(SerialId.clean('TESTNX4K0HEU6Q2S')).toEqual('TESTNX4K0HEU6Q2S');
    });

    it('ignores other validation issues', () => {
      expect(SerialId.clean('HELLO!WORLD!')).toEqual('HELLO!WORLD!');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('isValid()', () => {
    it('returns true if the specified string is a strictly valid serial', () => {
      expect(SerialId.isValid('TESTBVR8DC1NSX3S')).toBeTrue();
    });

    it('returns false if the specified string contains an invalid character', () => {
      expect(SerialId.isValid('TEST@VR8DC1NSX3S')).toBeFalse();
    });

    it('returns false if the specified string contains too few characters', () => {
      expect(SerialId.isValid('TESTBVR8DC1NSX3')).toBeFalse();
    });

    it('returns false if the specified string contains too many characters', () => {
      expect(SerialId.isValid('TESTBVR8DC1NSX3SS')).toBeFalse();
    });

    it('ignores spaces if strict is false', () => {
      expect(SerialId.isValid('TEST BVR8 DC1N SX3S', false)).toBeTrue();
    });

    it('ignores case if strict is false', () => {
      expect(SerialId.isValid('testbvr8dc1nsx3s', false)).toBeTrue();
    });

    it('returns false if the specified string contains spaces and strict is true', () => {
      expect(SerialId.isValid('TEST BVR8 DC1N SX3S', true)).toBeFalse();
    });

    it('returns false if the specified string contains lower case letters and strict is true', () => {
      expect(SerialId.isValid('testbvr8dc1nsx3s', true)).toBeFalse();
    });

    it('defaults to not strict if not otherwise specified', () => {
      expect(SerialId.isValid('test bvr8 dc1n sx3s')).toBeTrue();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('isValidCharacter()', () => {
    it('returns true if the specified character is valid in a serial', () => {
      expect(SerialId.isValidCharacter('A')).toBeTrue();
      expect(SerialId.isValidCharacter('5')).toBeTrue();
    });

    it('returns false if the specified character is not valid in a serial', () => {
      expect(SerialId.isValidCharacter('O')).toBeFalse();
      expect(SerialId.isValidCharacter('!')).toBeFalse();
      expect(SerialId.isValidCharacter(' ')).toBeFalse();
    });

    it('returns false if the specified string is empty', () => {
      expect(SerialId.isValidCharacter('')).toBeFalse();
    });

    it('returns false if the specified string is longer than one character', () => {
      expect(SerialId.isValidCharacter('ABC')).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('calculateChecksum()', () => {
    it('returns the expected checksum characters for the specified serial', () => {
      expect(SerialId.calculateChecksum('TEST213VDE5899')).toEqual('J4');
      expect(SerialId.calculateChecksum('TEST347DNUDS94')).toEqual('ES');
      expect(SerialId.calculateChecksum('TESTX8JRNCE4XT')).toEqual('VA');
    });

    it('ignores existing checksum characters in the specified serial', () => {
      expect(SerialId.calculateChecksum('TEST213VDE5899J4')).toEqual('J4');
      expect(SerialId.calculateChecksum('TEST347DNUDS94ES')).toEqual('ES');
      expect(SerialId.calculateChecksum('TESTX8JRNCE4XTVA')).toEqual('VA');
    });

    it('returns the expected checksum even if it would result in an invalid serial', () => {
      // The first 14 characters of this serial are valid. However, part of the checksum maps to the
      //  letter 'I', which is prohibited in serials. The resulting serial would be invalid.
      expect(SerialId.calculateChecksum('00000000000000')).toEqual('I0');
    });

    it('throws an error if the specified string is not 14 or 16 characters long', () => {
      expect(() => SerialId.calculateChecksum('')).toThrow();
      expect(() => SerialId.calculateChecksum('TEST213VDE589')).toThrow();
      expect(() => SerialId.calculateChecksum('TEST213VDE5899J')).toThrow();
      expect(() => SerialId.calculateChecksum('TEST213VDE5899J44')).toThrow();
    });

    it('throws an error if the specified serial is invalid', () => {
      expect(() => SerialId.calculateChecksum('TEST213ODE589!')).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('generateRandomCharacter()', () => {
    it('does not always return the same character', () => {
      // Call the function 100 times and check how many different characters we got.
      // This test could generate a false negative if we're very unlucky, but the odds are low.

      const result = new Set<string>();
      for (let attempt = 0; attempt < 100; ++attempt) {
        result.add(SerialId.generateRandomCharacter());
      }
      expect(result.size).toBeGreaterThan(5);
    });

    it('always returns a character which is valid in a serial', () => {
      for (let attempt = 0; attempt < 100; ++attempt) {
        expect(SerialId.isValidCharacter(SerialId.generateRandomCharacter())).toBeTrue();
      }
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('characterToNumericValue()', () => {
    it('returns the expected numeric value for the specified character', () => {
      expect(SerialId.serialCharacterToNumericValue('0')).toEqual(0);
      expect(SerialId.serialCharacterToNumericValue('9')).toEqual(9);
      expect(SerialId.serialCharacterToNumericValue('A')).toEqual(10);
      expect(SerialId.serialCharacterToNumericValue('H')).toEqual(17);
      expect(SerialId.serialCharacterToNumericValue('Q')).toEqual(26);
      expect(SerialId.serialCharacterToNumericValue('Z')).toEqual(35);
    });

    it('throws an error if the character is not allowed in a serial', () => {
      expect(() => SerialId.serialCharacterToNumericValue(' ')).toThrow();
      expect(() => SerialId.serialCharacterToNumericValue('?')).toThrow();
      expect(() => SerialId.serialCharacterToNumericValue('I')).toThrow();
      expect(() => SerialId.serialCharacterToNumericValue('O')).toThrow();
    });

    it('throws an error if the specified string is empty', () => {
      expect(() => SerialId.serialCharacterToNumericValue('')).toThrow();
    });

    it('throws an error if the specified string contains more than one character', () => {
      expect(() => SerialId.serialCharacterToNumericValue('ABC')).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('encodeGlsHashByte()', () => {
    it('returns the expected alphanumeric representation of the specified byte value', () => {
      expect(SerialId.encodeGlsHashByte(0)).toEqual('0');
      expect(SerialId.encodeGlsHashByte(17)).toEqual('h');
    });

    it('wraps around if the byte value is past the end of the allowed characters', () => {
      expect(SerialId.encodeGlsHashByte(36)).toEqual('0');
      expect(SerialId.encodeGlsHashByte(53)).toEqual('h');
      expect(SerialId.encodeGlsHashByte(255)).toEqual('3');
    });
  });

  // -----------------------------------------------------------------------------------------------
});

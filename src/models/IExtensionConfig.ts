/**
 * Describes the structure of static configuration data hard-coded into the extension.
 * The hard-coded data may be modified depending on build type.
 *
 * See: src/config.json
 */
export default interface IExtensionConfig {
  /**
   * The URL of an endpoint we query to retrieve the Firebase configuration data.
   * This may be a template string with placeholders which need to be replaced at runtime.
   * @see TemplateString
   */
  configFirebaseUrlTemplate: string;

  /**
   * The URL of an endpoint we request to register this device with the device management API.
   * This may be a template string with placeholders which need to be replaced at runtime.
   * @see TemplateString
   */
  registerDeviceUrlTemplate: string;

  /**
   * The URL of an endpoint we request to check-in the device with cloud management.
   * This may be a template string with placeholders which need to be replaced at runtime.
   * @see TemplateString
   */
  heartbeatUrlTemplate: string;

  /**

   * The URL of an endpoint we post to for uploading access logs for Ingest version 2.
   * This may be a template string with placeholders which need to be replaced at runtime.
   * @see TemplateString
   */
  ingestV2UrlTemplate: string;

  /**
   * The URL of an endpoint we post to for uploading access logs for Ingest version 3.
   * This may be a template string with placeholders which need to be replaced at runtime.
   * @see TemplateString
   */
  ingestV3UrlTemplate: string;

  /**
   * The URL of an endpoint we request to retrive groups from cloud management.
   * This may be a template string with placeholders which need to be replaced at runtime.
   * @see TemplateString
   */
  cloudGroupsUrlTemplate: string;

  /**
   * The name of the native messaging host belonging to the Smoothwall native agent.
   * We will use this to connect to the agent in native mode.
   */
  smoothwallAgentNativeMessagingHostName: string;

  /**
   * The URL which the Qoria native agent is listening on for web-grpc connections.
   * We will use this to connect to the agent in companion mode.
   */
  qoriaAgentGrpcUrl: string;
}

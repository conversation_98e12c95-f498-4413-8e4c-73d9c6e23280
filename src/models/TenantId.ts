import Uuid from './Uuid';

/**
 * Strongly typed wrapper for a Smoothwall tenant ID.
 * A tenant ID is currently always a UUID.
 * A large customer may be divided into multiple tenants to facilitate product administration. For
 *  example, a Multi Academy Trust may be treated as a single customer with one serial. However,
 *  each individual school within the Trust may be further identified by a tenant ID.
 */
export default class TenantId extends Uuid {
  /**
   * Construct a new tenant ID from a string.
   *
   * @param tenantId The initial value of the tenant ID, which must be a valid UUID. It will
   *  automatically be converted to lower-case if necessary.
   * @param strict Indicates whether strict validation rules will be applied. If false, the UUID
   *  must be formatted without any spaces or enclosing braces. If true, spaces and a single set of
   *  enclosing braces (i.e. {...}) will be ignored.
   * @throws {Error} The specified tenant ID is invalid.
   */
  public constructor(tenantId: string, strict: boolean = false) {
    super(tenantId, strict);
  }
}

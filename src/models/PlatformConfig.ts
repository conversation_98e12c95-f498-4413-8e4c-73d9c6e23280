import DeviceType from 'constants/DeviceType';
import { getDeviceSerialNumber } from 'utilities/PromiseWrappers';
import { getHardwarePlatformInfo } from 'utilities/Helpers';
import ManagedPolicy from './ManagedPolicy';
import OperatingMode from 'constants/OperatingMode';
import HardwarePlatformInfo from './HardwarePlatformInfo';
import NativeAgentType from 'constants/NativeAgentType';

/**
 * Contains information about how the extension is expected to run on the current platform.
 * This information can be obtained entirely from the browser (including managed storage). It does
 *  not depend on a cloud connection or a native component.
 */
export default class PlatformConfig {
  // -----------------------------------------------------------------------------------------------
  // Construction.
  public constructor(
    isDeviceManaged: boolean,
    platformInfo: chrome.runtime.PlatformInfo,
    managedPolicy: ManagedPolicy,
    hardwarePlatform: HardwarePlatformInfo | undefined,
    nativeAgentType: NativeAgentType | undefined,
  ) {
    this.isDeviceManaged = isDeviceManaged;
    this.platformInfo = platformInfo;
    this.managedPolicy = managedPolicy;
    this.hardwarePlatform = hardwarePlatform;
    this._nativeAgentType = nativeAgentType;
  }

  // -----------------------------------------------------------------------------------------------
  // Input/output.

  /**
   * Detect the platform configuration and policies from the browser.
   *
   * @returns Returns a promise which resolves to a new populated instance of PlatformConfig.
   */
  public static readonly load = async (): Promise<PlatformConfig> => {
    const [deviceSerial, platformInfo, managedPolicy, hardwareInfo] = await Promise.all([
      getDeviceSerialNumber(),
      chrome.runtime.getPlatformInfo(),
      chrome.storage.managed.get(null),
      getHardwarePlatformInfo(),
    ]);

    return new PlatformConfig(
      // The device serial is not to be confused with the customer serial (i.e. "UNCLxxxxxxxxxxxx").
      deviceSerial !== '',
      platformInfo,
      managedPolicy,
      hardwareInfo,
      undefined,
    );
  };

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Get the type of native agent we're currently connected to, if any.
   * Returns undefined if the native agent isn't applicable (e.g. we're running in standalone mode),
   *  or we haven't connected to a native agent yet.
   */
  public get nativeAgentType(): NativeAgentType | undefined {
    return this._nativeAgentType;
  }

  /**
   * Set the type of native agent we're currently connected to, if any.
   * This can be set to undefined if we've lost the connection to the native agent.
   * The native agent cannot be set to anything except undefined on Chromebook as that platform
   *  doesn't support native agents.
   */
  public set nativeAgentType(type: NativeAgentType | undefined) {
    if (this.platformInfo.os === 'cros' && type !== undefined) {
      throw new Error('Native agent is not supported on this platform.');
    }
    this._nativeAgentType = type;
  }

  /**
   * Get the mode we should be running in, based on the stored platform info, policy, and agent.
   * This will return undefined if the operating mode is not known yet.
   *
   * @todo Possibly cache the operating mode?
   */
  public get operatingMode(): OperatingMode | undefined {
    // We're always in standalone mode when running on ChromeOS.
    // On other platforms, we're only in standalone mode if the managed policy says so.
    // On non-Chromebook platforms, standalone mode takes precedence over other modes if enabled.
    if (this.platformInfo.os === 'cros' || this.managedPolicy.Smoothwall?.ForceOS === 'chromeos') {
      return OperatingMode.standalone;
    }

    if (this._nativeAgentType === NativeAgentType.smoothwall) {
      return OperatingMode.native;
    }

    if (this._nativeAgentType === NativeAgentType.qoria) {
      return OperatingMode.companion;
    }

    return undefined;
  }

  /**
   * Get the device type we should report to the cloud, based on the platform we're running on.
   * For legacy reasons, the device type field also specifies which product we're implementing.
   */
  public get deviceType(): DeviceType {
    switch (this.platformInfo.os) {
      case 'win':
        return DeviceType.cldfltWindows;

      case 'mac':
        return DeviceType.cldfltMacOs;

      case 'cros':
        return DeviceType.cldfltChromebook;

      default:
        console.warn(
          `Unrecognised operating system: ${this.platformInfo.os}. ` +
            'Continuing as though it is ChromeOS (cros).',
        );
        return DeviceType.cldfltChromebook;
    }
  }

  /**
   * Check if the extension is allowed to run on this platform, based on the stored policy.
   */
  public get isAllowedToRun(): boolean {
    if (
      this.platformInfo.os === 'cros' &&
      !this.isDeviceManaged &&
      this.managedPolicy.DisableFilteringOnUnmanagedChromebooks === true
    ) {
      // Policy prohibits the extension from running on an unmanaged (e.g. personal) Chromebook.
      return false;
    }

    return true;
  }

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Determine what our operating mode should be based on the given platform info and policy.
   * @param platformInfo Describes the platform we're currently running on. This should have been
   *  retrieved from the Chrome APIs.
   * @param managedPolicy The extension policies loaded from managed storage. This will only be used
   *  on certain platforms. The individual policies values it contains are optional.
   * @returns Returns an enumerated value specifying what our operating mode should be.
   */
  public static readonly determineOperatingMode = (
    platformInfo: chrome.runtime.PlatformInfo,
    managedPolicy: ManagedPolicy,
  ): OperatingMode => {
    // We're always in standalone mode when running on ChromeOS.
    // On other platforms, we're only in standalone mode if the managed policy says so.
    if (platformInfo.os === 'cros' || managedPolicy.Smoothwall?.ForceOS === 'chromeos') {
      return OperatingMode.standalone;
    }

    return OperatingMode.native;
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Indicates if we're running on a fully enterprise enrolled device.
   * (i.e. the device itself is enterprise enrolled, not just the current user account)
   * This only applies to Chromebooks.
   */
  public readonly isDeviceManaged: boolean;

  /**
   * A copy of the platform information retrieved from the browser.
   * This indicates which operating system we're actually running on, regardless of which mode we're
   *  operating in.
   */
  public readonly platformInfo: chrome.runtime.PlatformInfo;

  /**
   * A copy of the policy information retrieved from the browser's managed storage.
   * Note that all of the values within this object are optional. The policies are not necessary on
   *  all platforms or in all situations.
   */
  public readonly managedPolicy: ManagedPolicy;

  /**
   * The current hardware platform info if it is available.
   */
  public readonly hardwarePlatform: HardwarePlatformInfo | undefined;

  /**
   * The type of native agent we're connected to, if any.
   * This will be undefined if we're in standalone mode (i.e. there is no native agent), or we
   *  haven't connected to the native agent yet.
   */
  private _nativeAgentType?: NativeAgentType = undefined;
}

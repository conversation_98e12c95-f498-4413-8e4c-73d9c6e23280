/**
 * Describes the data structure stored in the managed policy.
 * Do not use this directly. Use the ManagedPolicy class instead.
 */
export default interface ManagedPolicy {
  Smoothwall?: {
    /**
     * The customer's UNCL serial ID.
     * It will be empty or undefined if we're not running on ChromeOS or in ChromeOS mode.
     * It must be validated before being used.
     */
    Serial?: string;

    /**
     * The customer's tenant ID, if applicable.
     * It will be empty or undefined if we're not running on ChromeOS or in ChromeOS mode. It will
     *  also be empty if the customer doesn't have a tenant ID.
     */
    TenantId?: string;

    /**
     * This will be set to 'chromeos' if we're enabling ChromeOS mode on Windows or macOS.
     * It will be empty or undefined otherwise.
     * It should be ignored when running on ChromeOS.
     */
    ForceOS?: string;
  };

  /**
   * The user identifier address which will be used if no user is logged into the browser.
   * Normally, we identify the user from the browser login session. If no user is logged-in then we
   *  can't register the device, and we don't know which policies to apply. However, if a default
   *  user identifier (typically an email address) is stored here (loaded from managed policies)
   *  then it will be used as a fall-back, enabling "guest" or "kiosk" mode.
   */
  DefaultUser?: string;

  DisableFilteringOnUnmanagedChromebooks?: boolean;

  DisableMiniFilter?: boolean;

  /**
   * A comma separated list of urls that should be excluded from showing the document hider.
   * When matching against these urls any page that is at the same level or below the url in the hierarchy will be excluded.
   * **/
  DocumentHiderExclusions?: string;

  /**
   * The maximum delay in seconds before downloading a policy when changes are detected.
   * Set to 0 to disable delays. Default is 15 seconds if not specified.
   */
  MaxPolicyDownloadDelay?: number;
}

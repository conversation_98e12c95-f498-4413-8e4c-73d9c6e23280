export default interface SafeguardingAlert {
  username: string;
  level: number;
  theme: string;
  url: string;
  blocked: boolean;
  searchTerms: string;
  localGroups: string[];
  onPremCategories: string[];
  clientTimeUtc: number;
  tenantId: string;
  serial: string;
  type: string;

  /**
   * The date and time that the alert was generated, in the local timezone of this device.
   * This must be a string with the following ICU format:
   *
   *      "EEE MMM d yyyy HH:mm:ss 'GMT'Z (zzzzz)"
   *
   * For example
   *
   *      "Thu May 11 2023 03:45:03 GMT+0100 (British Summer Time)"
   *
   * The day and month names must be English abbreviations, regardless of the device locale.
   *
   * @see SafeguardingService.generateLocalDateString()
   */
  localDate: string;
}

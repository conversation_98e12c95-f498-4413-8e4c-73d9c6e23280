import ObjectLiteral, { toMap } from './ObjectLiteral';

describe('ObjectLiteral', () => {
  describe('toMap()', () => {
    it('returns a map containing the object literal properties', () => {
      const obj: ObjectLiteral<string> = {
        foo: 'bar',
        hello: 'world',
        testing: '123',
      };

      const m = toMap(obj);
      expect(m.size).toEqual(3);
      expect(m.get('foo')).toStrictEqual('bar');
      expect(m.get('hello')).toStrictEqual('world');
      expect(m.get('testing')).toStrictEqual('123');
    });

    it('can handle mixed types', () => {
      const obj: ObjectLiteral<string | number> = {
        foo: 'bar',
        testing: 123,
      };

      const m = toMap(obj);
      expect(m.size).toEqual(2);
      expect(m.get('foo')).toStrictEqual('bar');
      expect(m.get('testing')).toStrictEqual(123);
    });
  });
});

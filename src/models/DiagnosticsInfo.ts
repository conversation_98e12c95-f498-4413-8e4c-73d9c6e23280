import { IGroup } from 'models/PolicyConfigModels';
import { IProductConfig } from 'models/IProductConfig';
import ProvisioningInfo from 'provisioning/ProvisioningInfo';
import PolicyService from 'services/PolicyService';
import IpService from 'services/IpService';
import CloudGroupsService from 'services/CloudGroupsService';
import SecretKnockService from 'services/SecretKnockService';
import AccessLogManager from 'access-logs/AccessLogManager';

/**
 * Describes diagnostic information in a format the portal real time log viewer expects.
 */
export interface RemoteDiagnosticsInfo {
  sessionInfo: {
    username: string;
    ipAddressPublic: string[];
    ipAddressPrivate: string[];
    mappedGroups: Array<Partial<IGroup>>;
    directoryGroups: string[];
  };

  tenant: string;
  license: string[];
  provisioningMessage: string;
  filterMode: string;
  connection: string;
  outsidePremises: string;

  filterConfigName: string;
  lastUpdatedLogs: number;
  lastSecretKnockDetails: string; // secretKnockDetails
  generated: string;
  blocklistVersion: string;
}

/**
 * Describes diagnostic information in a format the local diagnostics page expects.
 */
export interface LocalDiagnosticsInfo {
  copiedAt?: string;
  username: string;
  ipAddressPublic: string[];
  ipAddressPrivate: string[];
  mappedGroups: Array<Partial<IGroup>>;
  directoryGroups: string[];

  tenant: string;
  license: string[];
  provisioningMessage: string;
  filterMode: string;
  connection: string;
  outsidePremises: string;

  policyName: string;
  lastSentLogs: string;
  lastSecretKnockDetails: string;
  generatedEpoch: string;
  generatedDatetime: string;
  blocklistVersionEpoch: string;
  blocklistVersionDate: string;
}

/**
 * Describes the data structure used to display information on the diagnostics page.
 * This information is provided by the service worker.
 */
export default class DiagnosticsInfo {
  // -----------------------------------------------------------------------------------------------
  // Construction.
  constructor(
    ipService: IpService,
    secretKnockService: SecretKnockService,
    accessLogManager: AccessLogManager,
    connectionState: string,
    filterMode: string,
    provisioningMessage: string,
    provisioningInfo?: ProvisioningInfo,
    productConfig?: IProductConfig,
    policyService?: PolicyService,
    cloudGroupsService?: CloudGroupsService,
  ) {
    const ipAddressPublic = ipService.hasRecentPublicIpAddresses ? ipService.publicIpAddresses : [];
    const ipAddressPrivate = ipService.privateIpAddresses;

    this._sessionInfo = {
      username: provisioningInfo?.user ?? '',
      ipAddressPublic: ipAddressPublic.length > 0 ? ipAddressPublic : [],
      ipAddressPrivate: ipAddressPrivate.length > 0 ? ipAddressPrivate : [],
      mappedGroups:
        policyService?.mappedGroups?.map((group) => {
          return { id: group.id, name: group.name };
        }) ?? [],
      directoryGroups: [
        ...(provisioningInfo?.localGroups ?? []),
        ...(cloudGroupsService?.cloudGroups ?? []),
      ],
    };

    this._license = Object.keys(productConfig ?? {});
    this._connection = connectionState;
    this._filterMode = filterMode;
    this._filterConfigName = policyService?.policyName ?? '';
    this._lastUpdatedLogs = accessLogManager.lastUploadedAt;
    this._lastSecretKnockDetails = secretKnockService.getLastKnockSummary();
    this._tenant = this._getTenantInfo(provisioningInfo, policyService);
    this._outsidePremises = ipService.outsidePremisesStatus;
    this._provisioningMessage = provisioningMessage;
    this._generated = +(policyService?.metadata?.generated ?? 0);
    this._blocklistVersion = +(productConfig?.cldflt?.configuration.blocklist.name ?? 0);
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  public get toRemote(): RemoteDiagnosticsInfo {
    return {
      sessionInfo: this._sessionInfo,
      license: this._license,
      connection: this._connection,
      filterMode: this._filterMode,
      filterConfigName: this._filterConfigName,
      lastUpdatedLogs: this._lastUpdatedLogs * 1000, // The portal expects the timestamp to be in milliseconds.
      lastSecretKnockDetails: this._lastSecretKnockDetails,
      tenant: this._tenant,
      outsidePremises: this._outsidePremises,
      provisioningMessage: this._provisioningMessage,
      generated: this._generated.toString(),
      blocklistVersion: this._blocklistVersion.toString(),

      // TODO: Add ip addresses and groups etc? See spec: https://sw-cldflt-dev-uks-1-devicemanagement-api.azurewebsites.net/index.html
    };
  }

  public get toLocal(): LocalDiagnosticsInfo {
    return {
      username: this._sessionInfo.username,
      ipAddressPublic: this._sessionInfo.ipAddressPublic,
      ipAddressPrivate: this._sessionInfo.ipAddressPrivate,
      mappedGroups: this._sessionInfo.mappedGroups,
      directoryGroups: this._sessionInfo.directoryGroups,
      license: this._license,
      connection: this._connection,
      filterMode: this._filterMode,
      policyName: this._filterConfigName,
      lastSentLogs:
        this._lastUpdatedLogs === 0 ? 'Never' : new Date(this._lastUpdatedLogs * 1000).toString(), // The diagnostic page expects the timestamp to be in milliseconds.
      lastSecretKnockDetails: this._lastSecretKnockDetails,
      tenant: this._tenant,
      outsidePremises: this._outsidePremises,
      provisioningMessage: this._provisioningMessage,
      generatedEpoch: this._generated.toString(),
      generatedDatetime: new Date(this._generated * 1000).toString(),
      blocklistVersionEpoch: this._blocklistVersion.toString(),
      blocklistVersionDate: new Date(this._blocklistVersion * 1000).toString(),
    };
  }

  /**
   * Generates a string to use for the tenant field depending on if the current tenant configuration is valid.
   */
  private readonly _getTenantInfo = (
    provisioningInfo?: ProvisioningInfo,
    policyService?: PolicyService,
  ): string => {
    if (provisioningInfo === undefined || policyService === undefined) {
      return '';
    }

    if (policyService.isTenantValid(provisioningInfo.tenantId)) {
      if (provisioningInfo.tenantId === undefined) {
        return 'global';
      }

      const tenantInfo = policyService.getTenantInfo(provisioningInfo.tenantId);
      // The tenant info is checked as part of the tenant validation so it won't be undefined.
      // Added in an extra check for typescript.
      return tenantInfo === undefined ? provisioningInfo.tenantId.get() : tenantInfo.name;
    }

    if (!provisioningInfo.validTenantUuid) {
      return 'Id is not a valid UUID';
    }

    return provisioningInfo.tenantId === undefined
      ? 'No tenant set'
      : `Unrecognised id (${provisioningInfo.tenantId.get()})`;
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Describes the data for the current user session.
   */
  private readonly _sessionInfo: {
    /**
     * Username as reported by provisioning info.
     */
    username: string;

    /**
     * Public IP address as reported by IP Service.
     */
    ipAddressPublic: string[];

    /**
     * Array of private IPs as reported by the IP Service.
     */
    ipAddressPrivate: string[];

    /**
     * Local groups as reported by the provisioning info.
     */
    mappedGroups: Array<Partial<IGroup>>;

    /**
     * Cloud groups as reported by the cloud groups service.
     */
    directoryGroups: string[];
  };

  /**
   * Describes what state the filtering system is in.
   * E.g. "mode 1" (mini-filter) or "mode 2" (full-filter).
   */
  private readonly _filterMode: string;

  /**
   * Identifier for the policy name, taken from the PolicyService
   * E.g. 3eedd04e6fa771fc343c95bb1d3ae2f918fa90effb6d4cec087509ba122bd86f
   */
  private readonly _filterConfigName: string;

  /**
   * Blocklist version as a number, seconds since epoch.
   * E.g. 1635129902
   */
  private readonly _blocklistVersion: number;

  /**
   * The epoch timestamp of when the policy was published
   * E.g. 1676394381
   */
  private readonly _generated: number;

  /**
   * The epoch timestamp of when logs were last pushed.
   * E.g. 1676394381
   */
  private readonly _lastUpdatedLogs: number;

  /**
   * Version information for the extension.
   */
  private readonly _provisioningMessage: string;

  /**
   * Tenant ID as reported by provisioning info.
   */
  private readonly _tenant: string;

  /**
   * Date string describing when secret knock was last performed.
   * E.g. 'N/A', Fri Feb 17 2023 11:26:19 GMT-0600 (Central Standard Time)
   */
  private readonly _lastSecretKnockDetails: string;

  /**
   * Represents whether current location is outside premises.
   */
  private readonly _outsidePremises: string;

  /**
   * Array of strings containing licensed features.
   * E.g. Cloud Filter, Cloud Reporting, or Monitor Managed Service.
   */
  private readonly _license: string[];

  /**
   * Indicates whether the extension is currently connected to the cloud.
   * E.g. "connected".
   */
  private readonly _connection: string;
}

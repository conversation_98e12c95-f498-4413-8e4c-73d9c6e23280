import TemplateString from './TemplateString';

describe('TemplateString', () => {
  describe('constructor()', () => {
    it('throws an error if the template string is invalid', () => {
      expect(() => new TemplateString('foo {bar!} blah')).toThrowError();
    });

    it('throws an error if the default values include an invalid placeholder name', () => {
      const defaults = {
        bar: 'testing 123',
        'blah!': 'testing 456',
      };
      expect(() => new TemplateString('foo {bar} blah!', defaults)).toThrowError('blah!');
    });

    it('throws an error if the default values include a placeholder which is not in the template', () => {
      const defaults = {
        bar: 'testing 123',
        blah: 'testing 456',
      };
      expect(() => new TemplateString('foo {bar}!', defaults)).toThrowError('blah');
    });
  });

  describe('hasPlaceholder()', () => {
    it('returns true if the named placeholder appears in the template string', () => {
      const templateString = new TemplateString('Hello {user}!');
      expect(templateString.hasPlaceholder('user')).toBeTrue();
    });

    it('returns false if the named placeholder does not appear in the template string', () => {
      const templateString = new TemplateString('Hello {user}!');
      expect(templateString.hasPlaceholder('weather')).toBeFalse();
    });

    it('returns false if the specified placeholder name is invalid', () => {
      const templateString = new TemplateString('Hello {user}!');
      expect(templateString.hasPlaceholder('bar!')).toBeFalse();
    });
  });

  describe('hasDefault()', () => {
    it('returns true if the named placeholder has a default value', () => {
      const templateString = new TemplateString('Hello {user}!', { user: 'Obelix' });
      expect(templateString.hasDefault('user')).toBeTrue();
    });

    it('returns false if the named placeholder does not have a default', () => {
      const templateString = new TemplateString('Hello {user}!');
      expect(templateString.hasDefault('weather')).toBeFalse();
    });

    it('returns false if the specified placeholder name is invalid', () => {
      const templateString = new TemplateString('Hello {user}!');
      expect(templateString.hasDefault('bar!')).toBeFalse();
    });
  });

  describe('getDefault()', () => {
    it('returns the default for the specified placeholder, if it has one', () => {
      const templateString = new TemplateString('Hello {user}!', { user: 'Getafix' });
      expect(templateString.getDefault('user')).toEqual('Getafix');
    });

    it('returns undefined if the named placeholder does not have a default', () => {
      const templateString = new TemplateString('Hello {user}!');
      expect(templateString.getDefault('weather')).toBeUndefined();
    });

    it('returns undefined if the specified placeholder name is invalid', () => {
      const templateString = new TemplateString('Hello {user}!');
      expect(templateString.getDefault('bar!')).toBeUndefined();
    });
  });

  describe('toString()', () => {
    it('returns the original string if it contained no placeholders', () => {
      const templateString = new TemplateString('foobar');
      expect(templateString.toString({})).toEqual('foobar');
    });

    it('replaces placeholders with defaults specified in the constructor', () => {
      const defaults = {
        user: 'Fred',
        weather: 'cloudy',
      };
      const templateString = new TemplateString(
        'Hello {user}! Today, the weather is {weather}.',
        defaults,
      );
      expect(templateString.toString({})).toEqual('Hello Fred! Today, the weather is cloudy.');
    });

    it('replaces placeholders with values passed to toString()', () => {
      const templateString = new TemplateString('Hello {user}! Today, the weather is {weather}.');
      const values = {
        user: 'Barney',
        weather: 'windy',
      };
      expect(templateString.toString(values)).toEqual('Hello Barney! Today, the weather is windy.');
    });

    it('replaces placeholders with a mixture of defaults and values passed to toString()', () => {
      const defaults = {
        user: 'Wilma',
      };
      const templateString = new TemplateString(
        'Hello {user}! Today, the weather is {weather}.',
        defaults,
      );

      const values = {
        weather: 'rainy',
      };
      expect(templateString.toString(values)).toEqual('Hello Wilma! Today, the weather is rainy.');
    });

    it('overrides defaults with values passed to toString() if the same names appear in both', () => {
      const defaults = {
        user: 'Wilma',
        weather: 'warm',
      };
      const templateString = new TemplateString(
        'Hello {user}! Today, the weather is {weather}.',
        defaults,
      );

      const values = {
        user: 'Betty',
        weather: 'mild',
      };
      expect(templateString.toString(values)).toEqual('Hello Betty! Today, the weather is mild.');
    });

    it('replaces the placeholder with same value if it occurs multiple times', () => {
      const templateString = new TemplateString('Hello {user}! Your name is {user}.');
      const values = {
        user: 'Asterix',
      };
      expect(templateString.toString(values)).toEqual('Hello Asterix! Your name is Asterix.');
    });

    it('correctly handles consecutive placeholders', () => {
      const templateString = new TemplateString('This is {a}{b}.');
      expect(templateString.toString({ a: 'foo', b: 'bar' })).toEqual('This is foobar.');
    });

    it('correctly handles a placeholder at the start of the template', () => {
      const templateString = new TemplateString('{user} is logged-in.');
      expect(templateString.toString({ user: 'Terry' })).toEqual('Terry is logged-in.');
    });

    it('correctly handles a placeholder at the end of the template', () => {
      const templateString = new TemplateString('The end is {when}');
      expect(templateString.toString({ when: 'nigh' })).toEqual('The end is nigh');
    });

    it('correctly handles a template which consists of nothing but a placeholder', () => {
      const templateString = new TemplateString('{data}');
      expect(templateString.toString({ data: 'xyzzy' })).toEqual('xyzzy');
    });

    it('throws an error if a value is not used by the template and strict is true', () => {
      const templateString = new TemplateString('Hello {user}!');
      const values = {
        user: 'Fred',
        weather: 'sunny',
      };
      expect(() => templateString.toString(values, true)).toThrowError('weather');
    });

    it('ignores values which are not used by the template if strict is false', () => {
      const templateString = new TemplateString('Hello {user}!');
      const values = {
        user: 'Fred',
        weather: 'sunny',
      };
      expect(templateString.toString(values, false)).toEqual('Hello Fred!');
    });

    it('throws an error if placeholder does not have a value and strict is true', () => {
      const templateString = new TemplateString('Hello {user}! Today, the weather is {weather}.');
      const values = {
        user: 'Betty',
      };
      expect(() => templateString.toString(values, true)).toThrowError('weather');
    });

    it('substitutes an empty string for placeholders without a value if strict is false', () => {
      const templateString = new TemplateString('Hello {user}! Today, the weather is {weather}.');
      const values = {
        user: 'Wilma',
      };
      expect(templateString.toString(values, false)).toEqual(
        'Hello Wilma! Today, the weather is .',
      );
    });

    it('allows placeholders to be replaced by strings containing braces', () => {
      const templateString = new TemplateString('Hello {firstName} {middleName} {lastName}!');
      const values = {
        firstName: '{Fr}ed{',
        middleName: '}Bed}rock{',
        lastName: '{Flintstone}',
      };
      expect(templateString.toString(values)).toEqual('Hello {Fr}ed{ }Bed}rock{ {Flintstone}!');
    });

    it('does not treat a placeholder value as another placeholder', () => {
      // This test ensures that we don't create an infinite recursion where we keep trying to
      //  replace placeholders.
      const templateString = new TemplateString('Hello {firstName} {lastName}!');
      const values = {
        firstName: '{lastName}',
        lastName: '{firstName}',
      };
      expect(templateString.toString(values)).toEqual('Hello {lastName} {firstName}!');
    });
  });

  describe('toUrl', () => {
    it('wraps the result of toString() in a URL object', () => {
      const templateString = new TemplateString('http://{subdomain}.example.com/{path}');
      const values = {
        subdomain: 'test',
        path: 'some-file.txt',
      };
      const result = templateString.toUrl(values);
      expect(result).toBeInstanceOf(URL);
      expect(result.toString()).toEqual('http://test.example.com/some-file.txt');
    });

    it('throws an error if the result of toString() is an invalid URL', () => {
      const templateString = new TemplateString('http!://{subdomain}.example.com/{path}');
      const values = {
        subdomain: 'test',
        path: 'some-file.txt',
      };
      expect(() => templateString.toUrl(values)).toThrowError();
    });
  });

  describe('parsePlaceholderNames()', () => {
    it('throws an error if the template string contains an empty placeholder', () => {
      expect(() => TemplateString.parsePlaceholderNames('foo {} bar')).toThrowError();
    });

    it('throws an error if the template string contains an invalid placeholder name', () => {
      expect(() => TemplateString.parsePlaceholderNames('foo {bar!} blah')).toThrowError();
    });

    it('throws an error if the template string contains an unmatched brace', () => {
      expect(() => TemplateString.parsePlaceholderNames('foo} {bar}')).toThrowError();
      expect(() => TemplateString.parsePlaceholderNames('foo {bar} {blah')).toThrowError();
    });

    it('throws an error if the template string contains nested braces', () => {
      expect(() => TemplateString.parsePlaceholderNames('foo {bar{blah}!')).toThrowError();
      expect(() => TemplateString.parsePlaceholderNames('foo {bar}blah}!')).toThrowError();
    });

    it('returns a set containing the unique placeholder names from the template string', () => {
      const result = TemplateString.parsePlaceholderNames('Hello {user}. My name is {me} {me}.');
      expect(result.size).toEqual(2);
      expect(result).toContainEqual('user');
      expect(result).toContainEqual('me');
    });
  });

  describe('isValidPlaceholderName()', () => {
    it('returns true if name consists only of ASCII letters, numbers, and dashes', () => {
      expect(TemplateString.isValidPlaceholderName('abc-123')).toBeTrue();
    });

    it('returns false if name contains characters other than ASCII letters, numbers, and dashes', () => {
      expect(TemplateString.isValidPlaceholderName('abc!123')).toBeFalse();
      expect(TemplateString.isValidPlaceholderName('abc{123}')).toBeFalse();
    });

    it('returns false if name is empty', () => {
      expect(TemplateString.isValidPlaceholderName('')).toBeFalse();
    });
  });
});

/**
 * Describes a filter category used to map category IDs to category names.
 * The category data retrieved for the blocklist consists of an array of these.
 */
export default interface CategoryInfo {
  component: {
    domainsurls: number;
    regexpurls: number;
    weightedphrases: number;
  };
  example_url: string;
  filename: string;
  id: string;
  ignoreme: string;
  name: string;
  new_description: string;
  newname: string;
  parent: string;
  test_url: string;
}

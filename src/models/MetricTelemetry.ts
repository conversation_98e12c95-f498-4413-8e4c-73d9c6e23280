/**
 * A class for storing values around a certain metric.
 */
export default class MetricTelemetry {
  constructor(name: string, parentName?: string) {
    this.name = name;
    this.parentName = parentName;
  }

  /**
   * Gets the total count for the metric.
   */
  public get count(): number {
    return this._count;
  }

  /**
   * Gets the average value for the metric.
   */
  public get average(): number {
    return this._average;
  }

  /**
   * Gets the minimum value for the metric.
   */
  public get min(): number | undefined {
    return this._min;
  }

  /**
   * Gets the maximum value for the metric.
   */
  public get max(): number | undefined {
    return this._max;
  }

  public get customProperties(): Record<string, any> {
    return this._customProperties;
  }

  public readonly addCustomProperty = (key: string, value: any): void => {
    this._customProperties[key] = value;
  };

  /**
   * Adds the give value to the metric. Also update the count and the min and max values if required.
   * @param value The value of the metric to be added.
   */
  public readonly addMetric = (value: number): void => {
    this._count += 1;
    this._average += (value - this._average) / this._count;

    if (this._min === undefined || value < this._min) {
      this._min = value;
    }

    if (this._max === undefined || value > this._max) {
      this._max = value;
    }
  };

  /**
   * Clears the numeric values from the metric. Keeping the name and parent name.
   */
  public readonly clearValues = (): void => {
    this._count = 0;
    this._average = 0;
    this._min = undefined;
    this._max = undefined;
    this._customProperties = {};
  };

  /**
   * The name of the metric that will be uploaded with the telemetry.
   */
  public readonly name: string;

  /**
   * An optional parent name that can be used to aggregate different telemetry metrics together.
   */
  public readonly parentName?: string;

  private _count: number = 0;
  private _average: number = 0;
  private _min?: number;
  private _max?: number;
  private _customProperties: Record<string, any> = {};
}

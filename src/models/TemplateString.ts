import ObjectLiteral, { toMap } from './ObjectLiteral';

/**
 * Represents a string with zero or more placeholders which can later be replaced by other values.
 * This is useful where a string template needs to be loaded at runtime, e.g. from a file or
 *  downloaded from the web, and it isn't safe or practical to use conventional string
 *  interpolation.
 *
 * A template string could look like this:
 *
 *  "Hello {person}!"
 *
 * {person} is the placeholder which will be replaced. Note that there is no dollar sign ($).
 * A placeholder name (in this case "person") is case sensitive. It may consist of 1 or more ASCII
 *  letters, numbers, and dashes.
 *
 * A template can contain any number of placeholders. Also, a single placeholder can appear multiple
 *  times, although it will be replaced with the same value each time it occurs.
 *
 * @note A curly brace ("{" or "}") cannot appear anywhere in the template string, except as part of
 *  a placeholder. There is currently no way to escape them. For URLs, consider using URL encoding
 *  instead.
 *
 * @example
 *  const defaults = {
 *    region: 'uk'
 *  };
 *  const templateString = new TemplateString('https://{region}.example.com/{deviceId}', defaults);
 *
 *  const url1 = templateString.toUrl({ deviceId: 'abc-123' });
 *  const url2 = templateString.toUrl({ deviceId: 'xyz-789', region: 'us' });
 */
export default class TemplateString {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new template string with optional default values.
   *
   * @param template The template string, containing zero or more placeholders.
   * @param defaults Optional object containing default values for zero or more placeholders. The
   *  name of each property must correspond to the name of a placeholder in the template string.
   *  However, you don't need to provide a default for every placeholder.
   * @throws {Error} The template string is invalid; e.g. it contains an invalid placeholder name,
   *  or there is a brace in an unexpected place.
   * @throws {Error} The defaults object contains a named property which doesn't match any
   *  placeholder in the template string.
   *
   * @example
   *  // Create a template with on default value.
   *  const t = new UrlTemplate('http://{subdomain}.example.com/{path}, { subdomain: 'foo' });
   */
  public constructor(template: string, defaults: ObjectLiteral<string> = {}) {
    this._template = template;
    this._placeholders = TemplateString.parsePlaceholderNames(template);

    // Validate and store the default values.
    this._defaults = toMap(defaults);
    this._defaults.forEach((_value: string, name: string): void => {
      if (!TemplateString.isValidPlaceholderName(name)) {
        throw new Error(`Invalid placeholder name in defaults: ${name}`);
      }
      if (!this._placeholders.has(name)) {
        throw new Error(`Placeholder not found in template string: ${name}`);
      }
    });
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Check whether the template contains a placeholder with the given name.
   *
   * @param name The placeholder name to check for.
   * @returns True if the template contains the specified placeholder name, or false if not.
   */
  public readonly hasPlaceholder = (name: string): boolean => {
    return this._placeholders.has(name);
  };

  /**
   * Check whether there is a default value for the specified placeholder.
   *
   * @param name The placeholder name to check for.
   * @returns True if this object contains a default value for the specified placeholder. False if
   *  it doesn't have a default value, or the specified placeholder doesn't exist.
   */
  public readonly hasDefault = (name: string): boolean => {
    return this._defaults.has(name);
  };

  /**
   * Get the default value for the specified placeholder, if there is one.
   *
   * @param name The placeholder to get a default value for.
   * @returns The default value for the specified placeholder, if there is one. Returns undefined
   *  if there is no default value for the specified placeholder, or the specified placeholder
   *  doesn't exist.
   *
   * @note It's not possible to change the default values after construction.
   */
  public readonly getDefault = (name: string): string | undefined => {
    return this._defaults.get(name);
  };

  // -----------------------------------------------------------------------------------------------
  // Operations

  /**
   * Fill-in the template with placeholder values or defaults, and return the result as a string.
   *
   * @param values An object containing the named placeholder values to insert into the template.
   *  Values specified here will override any defaults specified in the constructor. The placeholder
   *  names are case sensitive.
   * @param strict If true, strict mode will be enabled. In strict mode, an exception will be thrown
   *  if any placeholder doesn't have a value or default. Additionally, an exception will be thrown
   *  if any of the specified values aren't used by the template. If false, strict mode will be
   *  disabled. Any placeholder without a value or default will be replaced by an empty string.
   * @returns A string based on the original template, with placeholder values replaced.
   */
  public readonly toString = (values: ObjectLiteral<string>, strict: boolean = true): string => {
    const valueMap = toMap(values);
    // For strict mode, keep track of which input values haven't been used yet.
    const unusedValues = new Set<string>(valueMap.keys());

    // This function will be called for each placeholder in the template.
    // It should return the replacement value.
    const replacer = (_match: string, placeholderName: string): string => {
      if (strict) {
        if (!valueMap.has(placeholderName) && !this._defaults.has(placeholderName)) {
          throw new Error(`No value or default specified for placeholder ${placeholderName}.`);
        }
        unusedValues.delete(placeholderName);
      }
      // Use the caller's values first. Fall-back on defaults, or an empty string if necessary.
      return valueMap.get(placeholderName) ?? this._defaults.get(placeholderName) ?? '';
    };

    const output = this._template.replace(/\{([^{}]*)\}/g, replacer);
    if (strict && unusedValues.size > 0) {
      throw new Error(`Placeholder values not used by template: ${[...unusedValues].join(', ')}`);
    }

    return output;
  };

  /**
   * Fill-in the template with placeholder values or defaults, and return the result as a URL.
   *
   * @param values An object containing the named placeholder values to insert into the template.
   *  Values specified here will override any defaults specified in the constructor. The placeholder
   *  names are case sensitive.
   * @param strict If true, strict mode will be enabled. In strict mode, an exception will be thrown
   *  if any placeholder doesn't have a value or default. Additionally, an exception will be thrown
   *  if any of the specified values aren't used by the template. If false, strict mode will be
   *  disabled. Any placeholder without a value or default will be replaced by an empty string.
   * @returns A string based on the original template, with placeholder values replaced.
   */
  public readonly toUrl = (values: ObjectLiteral<string>, strict: boolean = true): URL => {
    return new URL(this.toString(values, strict));
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Parse a template string to extract all the placeholders.
   *
   * @param template The template string to parse.
   * @returns A set of the unique placeholders names which occur in the template string.
   */
  public static readonly parsePlaceholderNames = (template: string): Set<string> => {
    const placeholderNames = new Set<string>();

    let isInPlaceholder = false;
    let accumulator = '';

    // Go through each character in the template string.
    for (let i = 0; i < template.length; ++i) {
      const c = template[i];

      // An opening brace is the start of a new placeholder.
      if (c === '{') {
        if (isInPlaceholder) {
          throw new Error(`Unexpected opening brace at index ${i}. Brace pairs cannot be nested.`);
        }
        isInPlaceholder = true;
        continue;
      }

      // A closing brace is the end of a placeholder.
      if (c === '}') {
        if (!isInPlaceholder) {
          throw new Error(`Unexpected closing brace at index ${i}.`);
        }

        if (accumulator === '') {
          throw new Error(`Placeholder name cannot be empty. See index ${i}.`);
        }

        if (!TemplateString.isValidPlaceholderName(accumulator)) {
          throw new Error(`Invalid placeholder name: ${accumulator}`);
        }

        // Add the placeholder to the output.
        placeholderNames.add(accumulator);
        accumulator = '';
        isInPlaceholder = false;
        continue;
      }

      if (isInPlaceholder) {
        accumulator += c;
      }
    }

    if (isInPlaceholder) {
      throw new Error('Unfinished placeholder at end of template string.');
    }

    return placeholderNames;
  };

  /**
   * Check if the specified string is a valid placeholder name.
   * A valid placeholder name is at least 1 character long, and contains only ASCII letters,
   *  numbers, and dashes.
   * @param name The string to validate.
   * @returns True if the string is a valid placeholder name, or false otherwise.
   */
  public static readonly isValidPlaceholderName = (name: string): boolean => {
    return /^[a-z0-9-]+$/i.test(name);
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The original template string.
   */
  private readonly _template: string;

  /**
   * The names of the placeholders mentioned in the template string.
   */
  private readonly _placeholders: Set<string>;

  /**
   * The placeholder default values specified in the constructor.
   * This will be empty if no defaults were specified.
   */
  private readonly _defaults: Map<string, string>;
}

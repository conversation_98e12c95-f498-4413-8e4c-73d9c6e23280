/**
 * @deprecated This contained a WebRTC configuration for peer-to-peer connections which we no
 *  longer use.
 */
export interface IceServer {
  urls: string;
  credential?: string;
  username?: string;
}

/**
 * @deprecated This contained a WebRTC configuration for peer-to-peer connections which we no
 *  longer use.
 */
export type IceServers = Record<string, IceServer>;

/**
 * @deprecated This contained a WebRTC configuration for peer-to-peer connections which we no
 *  longer use.
 */
export interface PeerConfig {
  iceTransportPolicy: string;
  iceServers: IceServers;
  iceCandidatePoolSize: number;
}

/**
 * @deprecated This contained a WebRTC configuration for peer-to-peer connections which we no
 *  longer use.
 */
export interface DataChannelConfig {
  ordered: boolean;
  maxPacketLifeTime: number;
}

/**
 * @deprecated This contained a WebRTC configuration for peer-to-peer connections which we no
 *  longer use.
 */
export interface ConnectionConfiguration {
  messageSchemaVersion: string;
  nativePortName: string;
  peerConfig: PeerConfig;
  dataChannelPrefix: string;
  dataChannelConfig: DataChannelConfig;
  localP2POnly: boolean;
  iceBundleLimit: number;
  rtcConnectionConfiguration?: RTCConfiguration;
}

export interface Licence {
  id: string;

  /**
   * Millisecond timestamp specifying when the licence is valid from.
   * Note: In Firestore, this is stored as a Firestore timestamp object. That type doesn't survive
   *  conversion to JSON for local storage though, so we convert it to milliseconds after
   *  downloading the document. See ProductConfigService.
   */
  licenseStartDate: number;

  /**
   * Millisecond timestamp specifying when the licence expires.
   * Note: In Firestore, this is stored as a Firestore timestamp object. That type doesn't survive
   *  conversion to JSON for local storage though, so we convert it to milliseconds after
   *  downloading the document. See ProductConfigService.
   */
  licenseExpiryDate: number;

  serial: string;
}

export interface Mms {
  licence: Licence;
  configuration: Record<string, unknown>;
}

export interface PolicyConfig {
  resource: string;
  sas: string;
  name: string;
}

export interface BlocklistConfig {
  resource: string;
  sas: string;
  name: string;
}

export interface ClientSettingsConfig {
  resource: string;
  sas: string;
  name: string;
}

export interface AccessLogsBlobConfig {
  resource: string;
  sas: string;
}

export interface CldfltConfiguration {
  config: PolicyConfig;
  blocklist: BlocklistConfig;
  clientSettings: ClientSettingsConfig;
  accesslogs: AccessLogsBlobConfig;
}

export interface Cldflt {
  licence: Licence;
  configuration: CldfltConfiguration;
}

export interface Directory {
  resource: string;
  sas: string;
  polling: number;
}

export interface CoreConfiguration {
  directory: Directory;
}

export interface Core {
  licence: Record<string, unknown>;
  configuration: CoreConfiguration;
}

export interface CldrptConfiguration {
  accesslogs: AccessLogsBlobConfig;
  accessLogsIngestV4?: AccessLogsIngestV4Config;
}

export interface Cldrpt {
  licence: Licence;
  configuration: CldrptConfiguration;
}

export interface AccessLogsIngestV4Config {
  url: string;
  bearerToken: string;
}

export interface AccessLogsConfig {
  accessLogsBlobConfig?: AccessLogsBlobConfig;
  accessLogsIngestV4Config?: AccessLogsIngestV4Config;
}

// based on responses and json schema documented here: https://familyzone.atlassian.net/wiki/spaces/PROD/pages/2695106428929/Qoria+Content+Aware+API+endpoints+and+Firestore+DB+schema.
export interface DMSContentAwareRootConfig {
  configuration: DMSContentAwareConfig;
}

export enum ContentAwareLicenseStatus {
  active = 'ACTIVE',
  suspended = 'SUSPENDED',
}

export interface DMSContentAwareConfig {
  domainConfig: {
    resource: {
      guiltByAssociationBlockThreshold: number;
      guiltByAssociationIgnoreAfterCleanImagesThreshold: number;
      guiltByAssociationEnabled: boolean;
      // never run on list from DMS, looks like
      // {"neverRunOnDMS": {"0": "domain1.com", "1": "domain2.com"}}
      neverRunOnDMS: Record<string, string> | undefined;
      defaultRules: {
        swimwear: number;
        gore: number;
        porn: number;
      };
    };
  };
  ia_license: {
    id: string;
    key: string;
    status: ContentAwareLicenseStatus;
    name: string;
    /** The license start date in Unix timestamp in seconds (e.g. **********) */
    start_date?: string;
    /** The license expiry date in Unix timestamp in seconds */
    end_date?: string;
  };
}

export interface IProductConfig {
  /**
   * @deprecated This contained a WebRTC configuration for peer-to-peer connections which we no
   *  longer use.
   */
  connectionConfiguration?: ConnectionConfiguration;

  mms?: Mms;
  cldflt?: Cldflt;
  core?: Core;
  cldrpt?: Cldrpt;
  ca?: DMSContentAwareRootConfig;
}

export default interface AccessLogEntry {
  clientip?: string;
  tenant?: string;
  httpcode?: number;
  took: number;
  time: string;
  groups: string[];
  username?: string;
  clienthostname?: string;
  https?: boolean;
  method?: string;
  producerid?: string;
  blocked?: boolean;
  categories: string[];
  destdomain?: string;
  url?: string;
  userAgent?: string; // In ingest v4 the name of this property is 'useragent'.
  locations?: string[];
  timeslots?: string[];
  actions?: string[];
  searchterms?: string;
  safeguardinglevel?: number;
  safeguardingtheme?: string;
  loglevel?: number;
  contenttype?: string;
  title?: string;
  videoids?: string;
  v?: string; // In ingest v4 this property is a number.
  ruleId?: string;
  policy?: string;
}

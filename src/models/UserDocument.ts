import { Timestamp } from 'firebase/firestore';

/**
 * Describes the structure of a document containing user-specific information and configuration.
 */
export default interface UserDocument {
  /**
   * Contains state relating to the Cloud Filter product.
   */
  cldflt?: {
    /**
     * Contains state information relating to the remote Real Time Log Viewer.
     * This is set on the server-side, telling the client whether to enable RTLV.
     * If this object (or the parent cldflt object) doesn't exist, then there is no RTLV session.
     * Any existing session should be stopped in that case.
     */
    rtlv?: {
      /**
       * The date and time at which the RTLV session should end.
       * If this point in time is reached (or has already passed) then any existing RTLV session
       *  should be stopped. However, it must be monitored for changes as it is commonly updated
       *  during a session to extend the end time.
       */
      expiresAt: Timestamp;

      /**
       * The URL which logs should be streamed to.
       */
      url: string;

      /**
       * An authentication token for use with the specified URL.
       */
      token: string;
    };

    /**
     * Contains the client's latest diagnostic data for display in the portal.
     * The client should write the latest diagnostic data to this object when an RTLV session
     *  starts, and update whenever the information changes while the session is running.
     * It should NOT write to this if there is no RTLV session running.
     *
     * @todo Populate this.
     */
    diagnostics?: any;
  };
}

/**
 * A log level rule uses keys of an access log to determine log levels.
 * Exclusions apply as negative matches against categories.
 * Type T can be string or regex. Rules retrieved from the cloud are delivered as string type
 *   because a regex won't serialize properly.
 */
export default interface LogLevelRule<T> {
  level: number;
  name: string;

  categories?: T[];
  contenttype?: T[];
  method?: T[];
  searchterms?: T[];
  title?: T[];
  safeguardingtheme?: T[];

  exclusions: T[];

  httpcode?: T[];
}

import Jwt from './Jwt';

// An encoded JWT for test purposes.
// The corresponding decoded header and payload are below, in testHeader and testPayload.
const testToken =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNjQwOTk1MjAwLCJleHAiOjE2NDA5OTg4MDAsIm15VGVzdENsYWltIjoiYmxhaCJ9.E4DGCetLw2D1WT7aLnxv_0ajMajWlMIkU-ERv9SHm7E';

const testHeader = {
  alg: 'HS256',
  typ: 'JWT',
};

const testPayload = {
  sub: '1234567890',
  name: '<PERSON>',
  iat: 1640995200, // 2022-01-01 00:00:00 GMT
  exp: 1640998800, // 2022-01-01 01:00:00 GMT
  myTestClaim: 'blah',
};

describe('Jwt', () => {
  describe('constructor()', () => {
    it('stores the encoded token', () => {
      const jwt = new Jwt(testToken);
      expect(jwt.token).toEqual(testToken);
    });

    it('decodes the header portion of the jwt', () => {
      const jwt = new Jwt(testToken);
      expect(jwt.header).toEqual(testHeader);
    });

    it('decodes the payload portion of the jwt', () => {
      const jwt = new Jwt(testToken);
      expect(jwt.payload).toEqual(testPayload);
    });

    it('throws an error if the specified token is not a valid JWT', () => {
      expect(() => new Jwt('foobar')).toThrow();
    });
  });

  describe('issueDate()', () => {
    it('returns the JWT issue date as a Date object', () => {
      const jwt = new Jwt(testToken);
      expect(jwt.issueDate).toEqual(new Date('2022-01-01T00:00:00Z'));
    });

    it('returns undefined if the JWT does not contain an issue date', () => {
      const jwt = new Jwt(testToken);
      delete jwt.payload.iat;
      expect(jwt.issueDate).toBeUndefined();
    });
  });

  describe('expiryDate()', () => {
    it('returns the JWT expiry date as a Date object', () => {
      const jwt = new Jwt(testToken);
      expect(jwt.expiryDate).toEqual(new Date('2022-01-01T01:00:00Z'));
    });

    it('returns undefined if the JWT does not contain an expiry date', () => {
      const jwt = new Jwt(testToken);
      delete jwt.payload.exp;
      expect(jwt.expiryDate).toBeUndefined();
    });
  });

  describe('hasExpired()', () => {
    it('returns true if the JWT has already expired', () => {
      const jwt = new Jwt(testToken);
      expect(jwt.hasExpired).toBeTrue();
    });

    it('returns false if the JWT has not expired yet', () => {
      const jwt = new Jwt(testToken);
      jwt.payload.exp = Math.floor(Date.now() / 1000) + 3600; // 1 hour in the future
      expect(jwt.hasExpired).toBeFalse();
    });

    it('returns false if the JWT does not contain an expiry date', () => {
      const jwt = new Jwt(testToken);
      delete jwt.payload.exp;
      expect(jwt.hasExpired).toBeFalse();
    });
  });

  describe('willHaveExpiredSoon()', () => {
    it('returns true if the JWT has already expired', () => {
      const jwt = new Jwt(testToken);
      expect(jwt.willHaveExpiredSoon()).toBeTrue();
    });

    it('returns true if the JWT expires within the specified number of minutes', () => {
      const jwt = new Jwt(testToken);
      jwt.payload.exp = Math.floor(Date.now() / 1000) + 120; // 2 minutes in the future
      expect(jwt.willHaveExpiredSoon(3)).toBeTrue();
    });

    it('returns false if the JWT will not have expired within the specified number of minutes', () => {
      const jwt = new Jwt(testToken);
      jwt.payload.exp = Math.floor(Date.now() / 1000) + 3600; // 1 hour in the future
      expect(jwt.willHaveExpiredSoon(3)).toBeFalse();
    });

    it('returns false if the JWT does not contain an expiry date', () => {
      const jwt = new Jwt(testToken);
      delete jwt.payload.exp;
      expect(jwt.willHaveExpiredSoon()).toBeFalse();
    });
  });

  describe('hasClaim()', () => {
    it('returns true if the JWT payload contains the named claim', () => {
      const jwt = new Jwt(testToken);
      expect(jwt.hasClaim('myTestClaim')).toBeTrue();
    });

    it('returns false if the JWT payload does not contain the named claim', () => {
      const jwt = new Jwt(testToken);
      expect(jwt.hasClaim('foobar')).toBeFalse();
    });
  });
});

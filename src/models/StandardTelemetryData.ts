/**
 * Stores the standard telemetry data that is sent with every event.
 */
export default interface StandardTelemetryData {
  /**
   * The user ID made up of the user name and hardware ID.
   */
  readonly userId: string;

  /**
   * The tenant ID of the user. If the user is not part of a tenant, this will be an empty string.
   */
  readonly tenantId: string;

  /**
   * The serial for the user's account.
   */
  readonly accountId: string;

  /**
   * A random Uuid created whenever the service worker starts that represents a session.
   */
  readonly sessionId: string;

  /**
   * The version of the extension. Taken from the manifest file.
   */
  readonly applicationVersion: string;

  /**
   * The device ID received from the device management api.
   */
  deviceId: string;

  /**
   * The operating mode of the extension. Either 'native' or 'standalone'.
   */
  readonly operatingMode: string;

  /**
   * The operating system of the device running the extension.
   */
  readonly os: string;

  /**
   * The chrome generated id for the extension.
   */
  readonly extensionId: string;

  /**
   * The manifest version of the extension.
   */
  readonly manifestVersion: string;

  /**
   * The gls hash for the current serial.
   */
  readonly gls: string;
}

export default interface SafeguardingRule {
  title: string;
  importance: number;
  exclusions: string[];
  severities: {
    Advisory: {
      weight: number;
      categories: Array<Record<string, number>>;
    };
    Caution: {
      weight: number;
      categories: Array<Record<string, number>>;
    };
    Danger: {
      weight: number;
      categories: Array<Record<string, number>>;
    };
  };
}

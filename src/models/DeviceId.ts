import Uuid from './Uuid';

/**
 * Strongly typed wrapper for a device ID.
 * The device ID is assigned by the cloud when we register a device.
 * It is currently always a UUID.
 */
export default class DeviceId extends Uuid {
  /**
   * Construct a new device ID from a string.
   *
   * @param deviceId The initial value of the device ID, which must be a valid UUID. It will
   *  automatically be converted to lower-case if necessary.
   * @param strict Indicates whether strict validation rules will be applied. If false, the UUID
   *  must be formatted without any spaces or enclosing braces. If true, spaces and a single set of
   *  enclosing braces (i.e. {...}) will be ignored.
   * @throws {Error} The specified device ID is invalid.
   */
  public constructor(deviceId: string, strict: boolean = false) {
    super(deviceId, strict);
  }
}

import DeviceId from './DeviceId';

describe('DeviceId', () => {
  // -----------------------------------------------------------------------------------------------

  describe('constructor()', () => {
    // The constructor functionality is already covered by tests for the Uuid base class.
    // These tests mainly just ensure the parameters were passed correctly to the base constructor.

    it('stores the specified device ID if it is valid', () => {
      const deviceId = new DeviceId('43f328d6-2dae-4a70-a5c6-b0813b3b6bf8');
      expect(deviceId.get()).toEqual('43f328d6-2dae-4a70-a5c6-b0813b3b6bf8');
    });

    it('throws an error if the specified device ID is invalid', () => {
      expect(() => new DeviceId('43f328d6-2dae-4a7@-a5c6-b0813b3b6bf8')).toThrow();
    });

    it('throws an error if the specified device ID contains spaces and strict is true', () => {
      expect(() => new DeviceId('  43f328d6-2dae- 4a70  - a5c6-b0813b3b6bf8   ', true)).toThrow();
    });

    it('silently discards spaces if strict is false', () => {
      const deviceId = new DeviceId('  43f328d6-2dae- 4a70  - a5c6-b0813b3b6bf8   ', false);
      expect(deviceId.get()).toEqual('43f328d6-2dae-4a70-a5c6-b0813b3b6bf8');
    });

    it('defaults strict to false if not specified', () => {
      const deviceId = new DeviceId('  43f328d6-2dae- 4a70  - a5c6-b0813b3b6bf8   ');
      expect(deviceId.get()).toEqual('43f328d6-2dae-4a70-a5c6-b0813b3b6bf8');
    });
  });
});

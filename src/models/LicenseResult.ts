import { CloudReportingVersion } from '../constants/CloudReportingVersion';
import { AccessLogsConfig } from './IProductConfig';

export default class LicenseResult {
  /**
   * Indicates if the product config contains a cldflt license and it has not expired.
   */
  cldfltLicenseValid: boolean = false;

  /**
   * Inicates what version of cloud reporting should be used.
   */
  cldrptVersion: CloudReportingVersion = CloudReportingVersion.disabled;

  /**
   * The config data for where to send access logs.
   */
  cldrptConfig?: AccessLogsConfig;
}

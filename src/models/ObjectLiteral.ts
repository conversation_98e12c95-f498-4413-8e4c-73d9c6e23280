/**
 * Generic type representing an object containing any number of named properties.
 * The type of the properties can be limited to a constrained type or set of types, or you can make
 *  it flexible by setting the value type to "any".
 * This is useful for situations where you're passing a plain object into a function. Declare the
 *  function parameter as this type so that you have slightly more flexibility than you would have
 *  by declaring it as "object", but more type safety than declaring it as "any".
 *
 * Example usage:
 *
 * @code
 *  const func = (data: ObjectLiteral<string>): void => {
 *    for (const [key, value] of Object.entries(data)) {
 *      console.log(`${key} = ${value}`);
 *    }
 *  }
 *
 *  func({ hello: 'world', testing: '123' });
 * @endcode
 */
type ObjectLiteral<ValueType> = Record<string, ValueType>;

export default ObjectLiteral;

/**
 * Convenience function for converting an instance of ObjectLiteral into a JavaScript Map.
 *
 * @param obj The object to convert into a Map.
 * @returns A Map constructed from the given object literal.
 */
export const toMap = <ValueType>(obj: ObjectLiteral<ValueType>): Map<string, ValueType> => {
  return new Map<string, ValueType>(Object.entries(obj));
};

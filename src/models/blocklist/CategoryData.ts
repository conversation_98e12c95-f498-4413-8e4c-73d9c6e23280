/**
 * Holds the data required for categorisation that has been fetched from blob storage.
 *
 * This includes the name of the domain and the category data for the domain.
 */
export default class CategoryData {
  /**
   * The domain name that the data belongs to.
   */
  public domain: string;
  /**
   * The category data for this domain.
   */
  public data: Record<string, string[]>;

  constructor(domain: string, data: Record<string, string[]>) {
    this.domain = domain;
    this.data = data;
  }
}

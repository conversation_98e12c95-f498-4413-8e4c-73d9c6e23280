import { v4 as generateUuidV4 } from 'uuid';

/**
 * Strongly-typed wrapper class for a UUID (or GUID).
 * This will ensure the UUID is valid. It will store it in a consistent format, making comparisons
 *  easier.
 * It also provides the ability to generate random UUIDs.
 *
 * A valid UUID can be represented as a string like this: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
 * Each "x" is an ASCII hexadecimal character.
 * A UUID string must be treated as case insensitive. However, by convention, it ought to be
 *  lower-case.
 *
 * @note Even with strict validation enabled, this class ignores UUID generation standards and the
 *  family/version/variant field. This means any combination of hexadecimal characters is permitted,
 *  as long as they are structured as noted above. This means it can be used permissively to
 *  validate anything that looks like a UUID, such as a custom-generated GUID.
 */
export default class Uuid {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new Uuid instance from a string.
   *
   * @param uuid The initial value of the UUID. It will automatically be converted to lower-case if
   *  necessary.
   * @param strict Indicates whether strict validation rules will be applied. If false, the UUID
   *  must be formatted without any spaces or enclosing braces. If true, spaces and a single set of
   *  enclosing braces (i.e. {...}) will be silently discarded. The UUID family/version/variant is
   *  never validated, regardless of whether strict mode is enabled.
   * @throws {Error} The specified UUID is invalid.
   */
  public constructor(uuid: string, strict: boolean = false) {
    this.set(uuid, strict);
  }

  /**
   * Generate and return a random UUID according to the UUIDv4 standard.
   */
  public static readonly random = (): Uuid => {
    return new Uuid(generateUuidV4(), true);
  };

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Get a string representation of the UUID.
   *
   * @returns Returns a string representation of the UUID. It will be strictly valid, and the
   *  letters will always be lower-case.
   *
   * @see toString()
   * @see set()
   */
  public readonly get = (): string => {
    return this._data;
  };

  /**
   * Get a string representation of the UUID.
   * This is an alias of `get()`.
   *
   * @returns Returns a string representation of the UUID. It will be strictly valid, and the
   *  letters will always be lower-case.
   *
   * @see get()
   * @see set()
   */
  public readonly toString = this.get;

  /**
   * Set the UUID from a string.
   *
   * @param uuid The UUID to store. It must be valid, but will automatically be converted to
   *  lower-case. The strictness of the validation rules can be controlled by the "strict"
   *  parameter.
   * @param strict Indicates whether strict validation rules will be applied. If false, the UUID
   *  must be formatted without any spaces or enclosing braces. If true, spaces and a single set of
   *  enclosing braces (i.e. {...}) will be silently discarded. The UUID family/version/variant is
   *  never validated, regardless of whether strict mode is enabled.
   * @throws {Error} The specified UUID is invalid.
   */
  public readonly set = (uuid: string, strict = false): void => {
    if (!strict) {
      uuid = Uuid.clean(uuid);
    }

    if (!Uuid.isValid(uuid, true)) {
      throw new Error('Invalid UUID.');
    }

    // UUIDs should be always stored in lower-case, but should be treated as case-insensitive.
    this._data = uuid.toLowerCase();
  };

  // -----------------------------------------------------------------------------------------------
  // Comparisons.

  /**
   * Check whether this UUID is equivalent to another.
   *
   * @param other The other UUID to compare against. It can be specified as a Uuid instance
   *  or as a string. If specified as a string, non-strict validation is applied.
   * @returns Returns true if the UUIDs are equivalent. Returns false otherwise. Always returns
   *  false if the other UUID is invalid.
   */
  public readonly equals = (other: Uuid | string): boolean => {
    if (typeof other === 'string') {
      return this._data === Uuid.clean(other).toLowerCase();
    }

    // We always store the UUID in lower case, so this is implicitly case insensitive:
    return this._data === other._data;
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Removes spaces and one set of enclosing braces from the specified.
   * This is used to facilitate non-strict validation.
   *
   * @param uuid The string to be cleaned.
   * @returns Returns a cleaned copy of the specified string.
   */
  public static readonly clean = (uuid: string): string => {
    // Remove all spaces from the UUID.
    uuid = uuid.replace(/\s/g, '');

    // If the ID is enclosed in a pair of braces then remove them. Braces are commonly used for
    //  GUIDs, which are almost identical to UUIDs.
    uuid = uuid.slice(uuid.startsWith('{') ? 1 : undefined, uuid.endsWith('}') ? -1 : undefined);

    return uuid;
  };

  /**
   * Check if the specified string contains a valid UUID.
   * This is not case sensitive, and will allow non-standard UUIDs; i.e. it doesn't check for a
   *  known family/version/variant number.
   *
   * @param uuid The string to validate.
   * @param strict Specifies whether strict validation rules will be applied. If false, spaces and
   *  one set of enclosing braces will be ignored. If true, spaces and braces are not allowed. The
   *  UUID family/version/variant is never validated, regardless of whether strict mode is enabled.
   * @returns Returns true if the specified string contains a valid UUID. Returns false
   *  otherwise.
   */
  public static readonly isValid = (uuid: string, strict: boolean = false): boolean => {
    // Note: Do NOT use the validate() function from the 'uuid' library. It will reject any UUID
    //  which does not conform to one of the standardised versions/variants. Many of our tenant IDs
    //  and other UUID/GUID-like identifiers are non-standard in that respect.
    return Uuid.validationPattern.test(strict ? uuid : Uuid.clean(uuid));
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Contains the characters of the UUID as a fixed-length string.
   * Each character is human-readable ASCII.
   */
  private _data: string = '';

  /**
   * A regular expression which will match a valid UUID if it is the only thing in the string.
   * This will match non-standard UUIDs, meaning it doesn't care whether the structure matches a
   *  known UUID version/variant. It can be used to validate something that just looks like a UUID.
   */
  public static readonly validationPattern =
    /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i;
}

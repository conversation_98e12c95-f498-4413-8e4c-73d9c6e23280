import Uuid from './Uuid';

describe('Uuid', () => {
  // -----------------------------------------------------------------------------------------------

  describe('constructor()', () => {
    it('stores the specified UUID if it is strictly valid', () => {
      const uuid = new Uuid('43f328d6-2dae-4a70-a5c6-b0813b3b6bf8');
      expect(uuid.get()).toEqual('43f328d6-2dae-4a70-a5c6-b0813b3b6bf8');
    });

    it('accepts non-standard UUIDs even in strict mode', () => {
      const uuid = new Uuid('*************-0000-0000-000180718080', true);
      expect(uuid.get()).toEqual('*************-0000-0000-000180718080');
    });

    it('strips spaces from the UUID if strict is false', () => {
      const uuid = new Uuid('  43f328d6-2dae- 4a70  - a5c6-b0813b3b6bf8   ', false);
      expect(uuid.get()).toEqual('43f328d6-2dae-4a70-a5c6-b0813b3b6bf8');
    });

    it('converts letters to lowercase if strict is false', () => {
      const uuid = new Uuid('43F328D6-2DAE-4A70-A5C6-B0813B3B6BF8', false);
      expect(uuid.get()).toEqual('43f328d6-2dae-4a70-a5c6-b0813b3b6bf8');
    });

    it('strips surrounding braces from the UUID if strict is false', () => {
      const uuid = new Uuid('{43f328d6-2dae-4a70-a5c6-b0813b3b6bf8}', false);
      expect(uuid.get()).toEqual('43f328d6-2dae-4a70-a5c6-b0813b3b6bf8');
    });

    it('strict defaults to false if not specified', () => {
      const uuid = new Uuid('  43f328d6-2dae- 4a70  - a5c6-b0813b3b6bf8   ', false);
      expect(uuid.get()).toEqual('43f328d6-2dae-4a70-a5c6-b0813b3b6bf8');
    });

    it('throws an error if the specified UUID contains spaces and strict is true', () => {
      expect(() => new Uuid('  43f328d6-2dae- 4a70  - a5c6-b0813b3b6bf8   ', true)).toThrow();
    });

    it('throws an error if the specified UUID is enclosed in braces and strict is true', () => {
      expect(() => new Uuid('{43f328d6-2dae-4a70-a5c6-b0813b3b6bf8}', true)).toThrow();
    });

    it('throws an error if the specified UUID contains an invalid character', () => {
      expect(() => new Uuid('43f328d6-2dae-4a7@-a5c6-b0813b3b6bf8')).toThrow();
    });

    it('throws an error if the specified UUID is structured incorrectly', () => {
      expect(() => new Uuid('43f328d62dae-4a70-a5c6-b081-3b3b6bf8')).toThrow();
    });

    it('throws an error if the specified UUID is the wrong length', () => {
      expect(() => new Uuid('43f328d62dae-4a70-a5c6-b081-3b3b6bf800')).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('random()', () => {
    it('generates a valid UUID', () => {
      expect(Uuid.random().get()).toMatch(
        /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/,
      );
    });

    it('generates a different UUID each time', () => {
      const uuid1 = Uuid.random();
      const uuid2 = Uuid.random();
      expect(uuid1.get()).not.toEqual(uuid2.get());
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('get()', () => {
    it('returns a string representation of the UUID', () => {
      const uuid = new Uuid('17534c24-91bb-42f5-8314-68a130fb4d0a');
      expect(uuid.get()).toEqual('17534c24-91bb-42f5-8314-68a130fb4d0a');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('toString()', () => {
    it('returns a string representation of the UUID', () => {
      const uuid = new Uuid('17534c24-91bb-42f5-8314-68a130fb4d0a');
      expect(uuid.toString()).toEqual('17534c24-91bb-42f5-8314-68a130fb4d0a');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('set()', () => {
    it('stores the specified UUID if it is strictly valid', () => {
      const uuid = new Uuid('00000000-0000-0000-0000-000000000000');
      uuid.set('0260ad54-49d4-4235-bffd-c05dfbbc3f6c');
      expect(uuid.get()).toEqual('0260ad54-49d4-4235-bffd-c05dfbbc3f6c');
    });

    it('accepts non-standard UUIDs even in strict mode', () => {
      const uuid = new Uuid('00000000-0000-0000-0000-000000000000');
      uuid.set('*************-0000-0000-000180718080', true);
      expect(uuid.get()).toEqual('*************-0000-0000-000180718080');
    });

    it('strips spaces from the UUID if strict is false', () => {
      const uuid = new Uuid('00000000-0000-0000-0000-000000000000');
      uuid.set('  0260ad54- 49d  4-4235-bff d -  c05df  bbc3f6c   ', false);
      expect(uuid.get()).toEqual('0260ad54-49d4-4235-bffd-c05dfbbc3f6c');
    });

    it('converts letters to lowercase if strict is false', () => {
      const uuid = new Uuid('00000000-0000-0000-0000-000000000000');
      uuid.set('0260AD54-49D4-4235-BFFD-C05DFBBC3F6C', false);
      expect(uuid.get()).toEqual('0260ad54-49d4-4235-bffd-c05dfbbc3f6c');
    });

    it('strips surrounding braces from the UUID if strict is false', () => {
      const uuid = new Uuid('00000000-0000-0000-0000-000000000000');
      uuid.set('{0260ad54-49d4-4235-bffd-c05dfbbc3f6c}', false);
      expect(uuid.get()).toEqual('0260ad54-49d4-4235-bffd-c05dfbbc3f6c');
    });

    it('defaults strict to false if not specified', () => {
      const uuid = new Uuid('00000000-0000-0000-0000-000000000000');
      uuid.set('  0260ad54- 49d  4-4235-bff d -  c05df  bbc3f6c   ');
      expect(uuid.get()).toEqual('0260ad54-49d4-4235-bffd-c05dfbbc3f6c');
    });

    it('throws an error if the specified UUID contains spaces and strict is true', () => {
      const uuid = new Uuid('00000000-0000-0000-0000-000000000000');
      expect(() => {
        uuid.set('  0260ad54- 49d  4-4235-bff d -  c05df  bbc3f6c   ', true);
      }).toThrow();
    });

    it('throws an error if the specified UUID is enclosed in braces and strict is true', () => {
      const uuid = new Uuid('00000000-0000-0000-0000-000000000000');
      expect(() => {
        uuid.set('{0260ad54-49d4-4235-bffd-c05dfbbc3f6c}', true);
      }).toThrow();
    });

    it('throws an error if the specified UUID contains an invalid character', () => {
      const uuid = new Uuid('00000000-0000-0000-0000-000000000000');
      expect(() => {
        uuid.set('0260ad54-49d4-42@5-bffd-c05dfbbc3f6c');
      }).toThrow();
    });

    it('throws an error if the specified UUID is structured incorrectly', () => {
      const uuid = new Uuid('00000000-0000-0000-0000-000000000000');
      expect(() => {
        uuid.set('0260ad5449d4-4235-bffd-c05d-fbbc3f6c');
      }).toThrow();
    });

    it('throws an error if the specified UUID is the wrong length', () => {
      const uuid = new Uuid('00000000-0000-0000-0000-000000000000');
      expect(() => {
        uuid.set('0260ad54-49d4-4235-bffd-c05dfbbc3f6c000');
      }).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('equals()', () => {
    it('returns true if UUIDs are equivalent', () => {
      const uuid1 = new Uuid('17361afe-ad31-4134-a090-871af186317f');
      const uuid2 = new Uuid('17361afe-ad31-4134-a090-871af186317f');
      expect(uuid1.equals(uuid2)).toBeTrue();
      expect(uuid1.equals('17361afe-ad31-4134-a090-871af186317f')).toBeTrue();
    });

    it('returns false if UUIDs are not equivalent', () => {
      const uuid1 = new Uuid('24d28371-6993-41d5-865f-6e5ac5665571');
      const uuid2 = new Uuid('c842c8c1-bc3e-426c-8677-f8caf191664e');
      expect(uuid1.equals(uuid2)).toBeFalse();
      expect(uuid1.equals('c842c8c1-bc3e-426c-8677-f8caf191664e')).toBeFalse();
    });

    it('is not case sensitive', () => {
      const uuid1 = new Uuid('17361afe-ad31-4134-a090-871af186317f');
      const uuid2 = new Uuid('17361AFE-AD31-4134-A090-871AF186317F');
      expect(uuid1.equals(uuid2)).toBeTrue();
      expect(uuid1.equals('17361AFE-AD31-4134-A090-871AF186317F')).toBeTrue();
    });

    it('applies non-strict validation if the other UUID is specified as a string', () => {
      const uuid1 = new Uuid('17361afe-ad31-4134-a090-871af186317f');
      expect(uuid1.equals('   17361AFE - AD31- 4134-   A090-871AF1   86317F   ')).toBeTrue();
    });

    it('returns false if the other UUID is specified as a string and is invalid', () => {
      const uuid1 = new Uuid('17361afe-ad31-4134-a090-871af186317f');
      expect(uuid1.equals('! foo bar *')).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('clean()', () => {
    it('returns the specified string with all whitespace removed', () => {
      expect(Uuid.clean(' bc 6 22db2\n-\t4ed2 - 4 67c-b284-b56a95684068   ')).toEqual(
        'bc622db2-4ed2-467c-b284-b56a95684068',
      );
    });

    it('returns the specified string with enclosing braces removed', () => {
      expect(Uuid.clean('{bc622db2-4ed2-467c-b284-b56a95684068}')).toEqual(
        'bc622db2-4ed2-467c-b284-b56a95684068',
      );
    });

    it('returns the specified string unmodified if it is already strictly valid', () => {
      expect(Uuid.clean('bc622db2-4ed2-467c-b284-b56a95684068')).toEqual(
        'bc622db2-4ed2-467c-b284-b56a95684068',
      );
    });

    it('does not modify case', () => {
      expect(Uuid.clean('BC622DB2-4ed2-467c-b284-b56a95684068')).toEqual(
        'BC622DB2-4ed2-467c-b284-b56a95684068',
      );
    });

    it('ignores other validation issues', () => {
      expect(Uuid.clean('bc622db2-4ed2-467c----!!')).toEqual('bc622db2-4ed2-467c----!!');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('isValid()', () => {
    it('returns true if the specified string is a strictly valid UUID', () => {
      expect(Uuid.isValid('a74aa7a5-7435-47c3-999f-8d6c0f551ddb')).toBeTrue();
    });

    it('always ignores case', () => {
      expect(Uuid.isValid('A74AA7A5-7435-47C3-999F-8D6C0F551DDB')).toBeTrue();
    });

    it('returns false if the specified string contains an invalid character', () => {
      expect(Uuid.isValid('a74a#7a5-7435-47c3-999f-8d6c0f551ddb')).toBeFalse();
    });

    it('returns false if the specified string is too short', () => {
      expect(Uuid.isValid('a74aa7a5-7435-47c3-999f-8d6c0f551dd')).toBeFalse();
    });

    it('returns false if the specified string is too long', () => {
      expect(Uuid.isValid('a74aa7a5-7435-47c3-999f-8d6c0f551ddbb')).toBeFalse();
    });

    it('ignores spaces if strict is false', () => {
      expect(Uuid.isValid('  a74aa7a5-7435-47c3 -999f-8d6c0f551ddb  ', false)).toBeTrue();
    });

    it('ignores enclosing braces if strict is false', () => {
      expect(Uuid.isValid('{a74aa7a5-7435-47c3-999f-8d6c0f551ddb}', false)).toBeTrue();
    });

    it('returns false if the specified string contains spaces and strict is true', () => {
      expect(Uuid.isValid('  a74aa7a5-7435 -4 7c3-999f-8d6c0f551ddb  ', true)).toBeFalse();
    });

    it('returns false if the specified string has enclosing braces and strict is true', () => {
      expect(Uuid.isValid('{a74aa7a5-7435-47c3-999f-8d6c0f551ddb}', true)).toBeFalse();
    });

    it('strict defaults to false if not specified', () => {
      expect(Uuid.isValid('  a74aa7a5-7435-47c3 -999f-8d6c0f551ddb  ')).toBeTrue();
    });

    it('treats non-standard UUIDs as valid even in strict mode', () => {
      expect(Uuid.isValid('*************-0000-0000-000180718080', true)).toBeTrue();
    });
  });

  // -----------------------------------------------------------------------------------------------
});

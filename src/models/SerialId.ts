import { sha256 } from 'js-sha256';

/**
 * Validates and stores a Smoothwall serial ID.
 * A serial ID uniquely identifies a customer licence for one or more products. One customer could
 *  potentially have multiple serial IDs. For larger customers, a serial ID may be combined with
 *  tenant IDs to distinguish different parts of a larger organisation, e.g. different schools
 *  within a Multi Academy Trust.
 *
 * A strictly valid serial is a 16-character string, containing uppercase ASCII letters and numbers.
 * The last two characters act as a checksum to ensure integrity, and letters I and O are not
 *  allowed in any position.
 * To facilitate human input, non-strict validation can be applied. This will automatically convert
 *  all letters to upper-case, and will ignore spaces.
 *
 * @example
 * ```typescript
 *  const serialId = new SerialId('TESTNX4K0HEU6Q2S');
 *  console.log(serialId.toString());
 *
 *  const randomSerialId = SerialId.random();
 *
 *  const randomSerialIdWithPrefix = SerialId.random('UNCLTEST');
 * ```
 */
export default class SerialId {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new serial ID from a string.
   * The string must pass validation checks, otherwise an error will be thrown.
   * A strictly valid serial consists of exactly 16 ASCII uppercase letters and numbers. Letters I
   *  and O are not allowed, and the last two characters must be a checksum confirming the integrity
   *  of the rest of the serial.
   * Non-strict validation will automatically convert letters to uppercase, and will remove any
   *  spaces.
   *
   * @param serial The serial ID to store, represented as a string.
   * @param strict Indicates if strict validation rules will be applied to the specified serial. If
   *  the serial came from human-input then it's useful to apply non-strict validation.
   * @throws {Error} The specified serial is invalid.
   *
   * @note If you want to generate a random serial then see `random()`.
   * @see set()
   */
  public constructor(serial: string, strict: boolean = false) {
    this.set(serial, strict);
  }

  /**
   * Generate and return a random valid serial ID, optionally with a fixed prefix.
   * The returned serial will contain a valid checksum.
   *
   * @param prefix Optional fixed prefix to include in the serial. For example, this can be used
   *  to set the first 4 characters to "UNCL" for a UNCL serial. This must consist of 14 or fewer
   *  uppercase ASCII letters and numbers. Letters I and O are not allowed.
   * @returns Returns a random valid serial, incorporating the fixed prefix if specified.
   * @throws {Error} Failed to generate a valid serial. Serial generation is non-deterministic. A
   *  failure is normally extremely rare. However, the longer the prefix, the more likely it is.
   *
   * @note If the specified prefix is the maximum allowed length (14 characters) then there is no
   *  room for random generation. This will simply try to calculate and append a suitable checksum.
   *  However, it's not guaranteed that it's possible to generate a valid checksum.
   */
  public static random = (prefix: string = ''): SerialId => {
    // Validate the prefix.
    if (prefix.length > 14) {
      throw new Error('Prefix must be no more than 14 characters long.');
    }

    if (![...prefix].every(SerialId.isValidCharacter)) {
      throw new Error('Prefix contains one or more invalid characters.');
    }

    let serial: string = '';

    for (let attempt = 0; attempt < 100; ++attempt) {
      serial = prefix;

      // Randomly generate the characters after the prefix.
      for (let index = prefix.length; index < 14; ++index) {
        serial += SerialId.generateRandomCharacter();
      }

      // Calculate the checksum and ensure it's valid.
      const checksum = SerialId.calculateChecksum(serial);

      if (SerialId.isValidCharacter(checksum[0]) && SerialId.isValidCharacter(checksum[1])) {
        return new SerialId(serial + checksum);
      }

      // If we reach here then the checksum contained a prohibited character.

      // If the prefix takes up the entire serial then there's no point trying again as the
      //  checksum will always be the same.
      if (prefix.length === 14) {
        break;
      }
    }

    throw new Error('Took too many attempts to generate a valid serial.');
  };

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Get a string representation of the serial ID, optionally formatted nicely for readability.
   *
   * @param pretty If true, spaces will be added to the serial to make it easier for humans to read.
   *  Note that spaces are not strictly allowed in a serial ID. You will need to disable strict
   *  validation if you want to parse the result again later. If false, the serial will be strictly
   *  valid.
   * @returns Returns the serial as a string. If `pretty` is false, it will be formatted without
   *  spaces, e.g. "UNCLBLAHBLAH1234". If `pretty` is true, spaces will be added for readability,
   *  e.g. "UNCL BLAH BLAH 1234".
   *
   * @see toString()
   * @see set()
   */
  public readonly get = (pretty: boolean = false): string => {
    if (pretty) {
      return (
        this._data.substring(0, 4) +
        ' ' +
        this._data.substring(4, 8) +
        ' ' +
        this._data.substring(8, 12) +
        ' ' +
        this._data.substring(12, 16)
      );
    }
    return this._data;
  };

  /**
   * Get a string representation of the serial ID, optionally formatted nicely for readability.
   * This is an alias of `get()`.
   *
   * @param pretty If true, spaces will be added to the serial to make it easier for humans to read.
   *  Note that spaces are not strictly allowed in a serial ID. You will need to disable strict
   *  validation if you want to parse the result again later. If false, the serial will be strictly
   *  valid.
   * @returns Returns the serial as a string. If `pretty` is false, it will be formatted without
   *  spaces, e.g. "UNCLBLAHBLAH1234". If `pretty` is true, spaces will be added for readability,
   *  e.g. "UNCL BLAH BLAH 1234".
   *
   * @see get()
   * @see set()
   */
  public readonly toString = this.get;

  /**
   * Set the serial ID from a string.
   * The specified serial ID must pass validation checks, otherwise an exception will be thrown.
   * A strictly valid serial consists of exactly 16 ASCII uppercase letters and numbers. Letters I
   *  and O are not allowed, and the last two characters must be a checksum confirming the integrity
   *  of the rest of the serial.
   * Non-strict validation will automatically convert letters to uppercase, and will remove any
   *  spaces.
   *
   * @param serial The serial ID to store, represented as a string.
   * @param strict Indicates if strict validation rules will be applied to the specified serial. If
   *  the serial came from human-input then it's useful to apply non-strict validation.
   * @throws {Error} The specified serial is invalid.
   */
  public readonly set = (serial: string, strict: boolean = false): void => {
    if (!strict) {
      serial = SerialId.clean(serial);
    }

    if (!SerialId.isValid(serial, true)) {
      throw new Error('Invalid Smoothwall serial ID.');
    }

    this._data = serial;
  };

  /**
   * Check if the stored serial is part of the UNCL family of products.
   *
   * @returns Returns true if the stored serial starts with "UNCL". Returns false otherwise.
   */
  public readonly isUnclSerial = (): boolean => {
    return this.hasPrefix('UNCL');
  };

  /**
   * Get the GLS hash of the stored serial ID, represented as a string.
   * The GLS hash is used to generate customer-specific hostnames for APIs.
   *
   * @returns Returns the GLS hash of the stored serial. It will consist of lower-case ASCII letters
   *  and numbers.
   */
  public readonly getGlsHash = (): string => {
    const hasher = sha256.create();
    hasher.update(this._data);
    return hasher.array().map(SerialId.encodeGlsHashByte).join('');
  };

  /**
   * Get the specified number of characters from the start of the stored serial.
   * This is useful for checking if a serial ID was generated with a specific fixed prefix, e.g.
   *  "UNCL" or "TEST".
   *
   * @param length The length of characters to return from the start of the serial ID. Must be less
   *  than or equal to 16.
   * @returns Returns the specified number of characters from the start of the serial, without any
   *  spaces. If the specified length is 0 then an empty string is returned.
   * @throws {Error} The specified prefix length is longer than the entire serial ID.
   */
  public readonly getPrefix = (length: number): string => {
    if (length > 16) {
      throw new Error('Invalid prefix length.');
    }
    return this._data.substring(0, length);
  };

  /**
   * Check if the stored serial ID starts with the specified string.
   * This is useful for checking if a serial ID was generated with a specific fixed prefix, e.g.
   *  "UNCL" or "TEST".
   *
   * @param prefix The string to look for at the start of the stored serial. This is not case
   *  sensitive.
   * @returns Returns true if the stored serial ID starts with the specified string. Returns false
   *  otherwise. This always returns false if the specified string is empty, too long for a serial
   *  ID, or contains invalid characters.
   */
  public readonly hasPrefix = (prefix: string): boolean => {
    if (prefix.length === 0 || prefix.length > 16) {
      return false;
    }
    return this._data.startsWith(prefix.toUpperCase());
  };

  // -----------------------------------------------------------------------------------------------
  // Comparisons.

  /**
   * Check if the stored serial ID is exactly equivalent to another serial ID.
   * Serial IDs are always stored in upper-case so this is not case sensitive.
   *
   * @param other The other serial ID to compare against. This can be another SerialId instance, or
   *  a string. If specified as a string then non-strict validation is applied.
   * @returns Returns true if this serial ID is equivalent to the other serial ID. Returns false
   *  otherwise. This always returns false if the other serial ID was specified as a string and it
   *  isn't valid.
   */
  public readonly equals = (other: SerialId | string): boolean => {
    if (typeof other === 'string') {
      // Allow non-strict validation.
      return this._data === SerialId.clean(other);
    }
    return this._data === other._data;
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Remove spaces from the specified string, and convert all letters to uppercase.
   * This is used for non-strict validation and storage of serial IDs.
   *
   * @param serial The string to be cleaned.
   * @returns Returns a cleaned copy of the specified string.
   */
  public static readonly clean = (serial: string): string => {
    return serial.replace(/\s/g, '').toUpperCase();
  };

  /**
   * Check if the specified string contains a valid serial ID.
   * This ensures it contains the correct number of valid characters. It also verifies that the
   *  checksum characters match the rest of the serial.
   * It can optionally apply strict validation, which disallows spaces and lower-case letters.
   *
   * @param serial The string to validate.
   * @param strict If true, strict validation rules will be applied, meaning spaces and lower-case
   *  letters are not allowed. If false, non-strict validation rules will be applied. This means
   *  spaces will be ignored, and all letters will be treated as upper-case.
   * @returns Returns true if the specified string is a valid serial ID. Returns false otherwise.
   */
  public static readonly isValid = (serial: string, strict: boolean = false): boolean => {
    if (!strict) {
      serial = SerialId.clean(serial);
    }

    // Ensure it's the correct length and contains only valid characters.
    if (serial.length !== 16) {
      return false;
    }

    if (![...serial].every(SerialId.isValidCharacter)) {
      return false;
    }

    // Ensure the checksum is correct.
    return SerialId.calculateChecksum(serial) === serial.substring(14, 16);
  };

  /**
   * Check if the specified string is a single character which is allowed in a serial ID.
   * This implicitly applies strict validation.
   * The only characters allowed are uppercase ASCII letters and numbers.
   *
   * @param c The string to validate.
   * @returns Returns true if the specified string is a single character which is allowed in a
   *  strictly valid serial ID. Returns false otherwise.
   */
  public static readonly isValidCharacter = (c: string): boolean => {
    return c.length === 1 && SerialId._validCharacterMap.includes(c);
  };

  public static calculateChecksum = (serial: string): string => {
    if (serial.length !== 14 && serial.length !== 16) {
      throw new Error('Invalid serial length.');
    }

    // Calculate checksums for the event and odd characters in the serial.
    let evenCheckDigit: number = 0;
    let oddCheckDigit: number = 0;
    for (let index = 0; index < 14; index += 2) {
      evenCheckDigit +=
        SerialId.serialCharacterToNumericValue(serial[index]) * (index + 1) + index + 12;
      oddCheckDigit +=
        SerialId.serialCharacterToNumericValue(serial[index + 1]) * (index + 2) + index + 30;
    }

    // Convert the checksum values to an alphanumeric representation.
    // NOTE: The serial rules prohibit letters I and O. However, the checksum algorithm was
    //  originally designed without that restriction. As a result, this may generate a checksum
    //  containing the prohibited letters. It's up to the caller to handle that situation.

    return (
      SerialId._checksumCharacterMap[evenCheckDigit % SerialId._checksumCharacterMap.length] +
      SerialId._checksumCharacterMap[oddCheckDigit % SerialId._checksumCharacterMap.length]
    );
  };

  /**
   * Randomly generate one valid character for a serial ID.
   * Note that the last two characters of a serial ID contain a checksum to verify integrity. That
   *  means a valid serial ID cannot consist entirely of randomly generated characters.
   *
   * @returns Returns a single random character for a serial ID. That is, any upper-case ASCII
   *  letter or number, not including I and O.
   */
  public static generateRandomCharacter = (): string => {
    return SerialId._validCharacterMap[
      Math.floor(Math.random() * SerialId._validCharacterMap.length)
    ];
  };

  /**
   * Convert one serial ID character from its text representation to its equivalent numeric value.
   * This is used when calculating the checksum. It has no other practical meaning.
   *
   * @param c The serial character to convert to a numeric equivalent.
   * @returns Returns the numeric equivalent value of the specified serial character.
   * @throws {Error} The specified string is not a valid character in a serial ID.
   */
  public static serialCharacterToNumericValue = (c: string): number => {
    if (!SerialId.isValidCharacter(c)) {
      throw new Error('Invalid serial character (1): ' + c);
    }

    const value = SerialId._checksumCharacterMap.indexOf(c);
    if (value === -1) {
      throw new Error('Invalid serial character (2): ' + c);
    }

    return value;
  };

  /**
   * Convert one byte of a GLS hash to its alphanumeric equivalent.
   * This is a lossy operation. Multiple different input values will yield the same output value.
   *
   * @param byte The numeric value (unsigned integer) of the byte to encode.
   * @returns The alphanumeric representation of the specified byte value.
   */
  public static encodeGlsHashByte = (byte: number): string => {
    return SerialId._glsCharacterMap[byte % SerialId._glsCharacterMap.length];
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Contains the characters of the serial ID as a fixed-length string.
   * Each character is human-readable ASCII.
   */
  private _data: string = '';

  /**
   * This string defines all the characters which can legitimately occur in a serial.
   * The order of this string isn't important. It's only used to validate characters and to
   *  randomly pick characters for new serials.
   */
  private static readonly _validCharacterMap = '0123456789ABCDEFGHJKLMNPQRSTUVWXYZ';

  /**
   * The position of a character in this string indicates its equivalent numeric value.
   * This mapping is used when calculating a checksum.
   * This deliberately includes letters I and O, which aren't actually valid in a serial. This
   *  is necessary to ensure the checksum algorithm still works.
   *
   * @warning The order of characters here is important.
   */
  private static readonly _checksumCharacterMap = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';

  /**
   * The characters used when encoding a GLS hash alphanumerically.
   * The position of each character indicates its numerical value.
   * If a value to be encoded is equal to or greater than the length of the string then it wraps
   *  back around to the beginning.
   *
   * @warning The order of characters here is important.
   */
  private static readonly _glsCharacterMap = '0123456789abcdefghijklmnopqrstuvwxyz';
}

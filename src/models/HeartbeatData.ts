/**
 * An interface for all of the data that should be sent in the body of a heartbeat request.
 */
export default interface HeartbeatData {
  /**
   * The current date/time when the request is made.
   * This must be the current unix timestamp in seconds.
   */
  checkInTime: number;
  /**
   * The number of access logs that have been uploaded since the last successful check in.
   */
  logUploadCount: number;
  /**
   * The name of the currently loaded policy config.
   * If a policy isn't loaded this should be an empty string.
   * The value should be undefined so it isn't sent in the request if it has been previously sent successfully.
   */
  policyName?: string;

  /**
   * An array of public ip addresses for the device.
   * If there are no addresses or they are not yet know this should be an empty array.
   * The value should be undefined so it isn't sent in the request if it has been previously sent successfully.
   */
  publicIpAddresses?: string[];

  /**
   * An array of private ip addresses for the device.
   * If there are no addresses or they are not yet know this should be an empty array.
   * The value should be undefined so it isn't sent in the request if it has been previously sent successfully.
   */
  privateIpAddresses?: string[];

  /**
   * An array of objects containing info about the extension and the device software.
   * The value should be undefined so it isn't sent in the request if it has been previously sent successfully.
   */
  software?: Array<{
    /**
     * The name of the software sending the request.
     * For the extension this will always be 'sw-extension'
     */
    name?: string;
    /**
     * The version number of the extension.
     */
    version?: string;
    /**
     * The user agent string of the browser the extension is running in.
     */
    userAgent?: string;
  }>;

  /**
   * An array of the currently mapped groups that the user belongs to.
   * Note the user should always be a member of at least one group (Default User) this should be sent too.
   * The value should be undefined so it isn't sent in the request if it has been previously sent successfully.
   */
  mappedGroups?: Array<{ id: string; name: string }>;

  /**
   * A Unix timestamp (in seconds) indicating when the filter policy was lasted downloaded.
   * The back-end expects this as a string, not a number.
   * @note This is not necessarily the date/time that the policy was published.
   */
  policyDownloadedAt?: string;

  /**
   * The epoch number of the blocklist we currently have in memory.
   * This is the Unix timestamp (in seconds) of when it was published, encoded as a string.
   */
  categorisation?: string;
}

import { SearchTerms } from 'guardian/blocklist-components/SearchTermCategoriser';

/**
 * Holds all of the information parsed out from the policy config file.
 */
export interface IPolicyConfig {
  banned: IBannedUser[];
  blockpages: IBlockpagePolicy[];
  category_filter_groups: ICategoryFilterGroup[];
  cloud_content_modifications: ICloudContentMod[];

  /**
   * Contains information about where to send secret knock, and how often.
   * If this property is undefined or null then assume no secret knock is to be sent.
   */
  cloud_filter?: ICloudFilterBypass;

  content_modification: IContentModPolicy[];
  custom_categories: ICustomCategory[];
  custom_content_modifiers: unknown[];
  default_content_modifiers: IDefaultModifier[];
  google_group_users: IGoogleGroupUsers;
  groups: IGroup[];
  group_mapping: IGroupMapping[];
  locations: ILocation[];
  metadata: IMetadata;
  policies: IPolicy;

  /**
   * A simplified representation of the "content_modification" array.
   * This property isn't part of the original object downloaded from the cloud. It's generated
   *  client-side to make content mods simpler and faster to process. It was introduced in extension
   *  v2.0.8. It may be undefined if we've loaded a cached policy from an older version of the
   *  extension.
   */
  flattenedContentMods?: IContentModPolicyFlat[];

  /**
   * A simplified representation of the "policies" object.
   * This property isn't part of the original object downloaded from the cloud. It's generated
   *  client-side to make policy rules simpler and faster to process. It was introduced in the
   *  original implementation of this extension so it should always be present, even in cached
   *  copies.
   */
  flattenedPolicies: IFilterPolicyFlat[];

  /**
   * URLs that should be exempt from Content Aware filtering.
   * This property isn't part of the original object downloaded from the cloud. It's generated
   * client-side by extracting URLs from custom categories named "Content Aware Allow list".
   */
  contentAwareAllowList?: string[];

  quotas: IQuota[];
  tenants: ITenantInfo[];
  time_slots: ITimeSlot[];
  users: IUserInfo[];
  customSearchTerms?: SearchTerms;
  mappedGroups: IGroup[];
}

/**
 * Contains the secret knock config.
 */
export interface ICloudFilterBypass {
  /**
   * The length of time in seconds it takes a secret knock to expire on the Smoothie.
   * This is stored as a string for legacy reasons, but the client should be prepared for it to be
   *  a number as well.
   * To ensure the secret knock doesn't expire, the client should send a secret knock request more
   *  often than this. It is usually sent 1 minute before expiry. However, to avoid flooding the
   *  network, do not send it more often than every minute.
   *
   * @note If this is undefined or null then assume no secret knock is to be sent.
   */
  timeout: string;

  /**
   * The address which secret knock requests should be sent to.
   * Despite the name, this may contain more than just a host name. It usually also contains a port
   *  number and a path.
   * It may not contain a scheme though. If no scheme is specified, it must default to http, not
   *  https.
   *
   * @note If this is empty, undefined, or null, then assume no secret knock is to be sent.
   */
  host: string;
}

/**
 * Contains details for a banned user
 */
export interface IBannedUser {
  tenant: string;
  comment: string;
  id: string;
  banexpirydate: string;
  enabled: string;
  username: string;
}

/**
 * Contains the info for which blockpage to use for a policy.
 */
export interface IBlockpagePolicy {
  who: string[];
  blockpage: {
    type: 'redirect' | string;
    url: string;
  };
  order: string;
  id: string;
  where: string[];
  what: string[];
  when: string[];
  enabled: string;
}

/**
 * Contains a map of the users for a google group.
 */
export type IGoogleGroupUsers = Record<string, string[]>;

/**
 * Contains the id for a default content mod.
 */
export interface IDefaultModifier {
  id: string;
  modifier_id: string;
}

/**
 * Contains any metadata about the policy config.
 */
export interface IMetadata {
  generated: string;
  hardware_id: string;
  version?: string;
  id: string;
}

/**
 * Contains information about the location to use in the policy decision.
 */
export interface ILocation {
  sources?: string[];
  name: string;
  exceptions?: string[];
  id: string;
}

/**
 *
 */
export interface IUserInfo {
  name: string;
  id: string;
}

export interface IGroupMapping {
  enabled: string;
  local_group: string;
  directory_group: string[];
  id: string;
}

export interface ICategoryFilterGroup {
  tenant: string;
  id: string;
  source: string[];
  comment: string;
  name: string;
}

/**
 * Contains the basic policy information.
 */
export interface IBasePolicy {
  enabled: string;
  where: string[];
  action: string;
  id: string;
  who: string[];
  what: string[];
  order: string;
}

/**
 * Contains the flattened content mod rules.
 */
export interface IContentModPolicyFlat extends IBasePolicy {
  ruleset: string[];
}

/**
 * Contains the flattened when policies.
 */
export interface IFilterPolicyFlat extends IBasePolicy {
  when: string[];
}

/**
 * Contains the flattened child filter policies
 */
export interface IFilterPolicy extends IFilterPolicyFlat {
  children?: IFilterPolicyFlat[];
}

/**
 * Contains the flattened child content mod rules.
 */
export interface IContentModPolicy extends IContentModPolicyFlat {
  children?: IContentModPolicyFlat[];
}

/**
 * Contains the policies and which tenant they apply to.
 */
export interface IPolicy {
  tenants: Record<string, IFilterPolicy[]>;
  after: IFilterPolicy[];
  // locations: undefined[], // On prem ships locations in two forms, as a policy, and as a list of locations, this is the former,and is always empty and most likely not here anymore
  before: IFilterPolicy[];
}

/**
 * Contains the policy groups.
 */
export interface IGroup {
  id: string;
  comment: string;
  name: string;
}

/**
 * Contains information about a specific tenant.
 */
export interface ITenantInfo {
  addresses: string[];
  name: string;
  id: string;
}

/**
 * Contains the time slot to use when making a policy decision.
 */
export interface ITimeSlot {
  name: string;
  id: string;
  times: Array<[string, string]>;
  comment?: string;
  tenant: string;
}

/**
 *
 */
export interface IQuota {
  quota_reset_time?: string;
  quota_time?: string;
  quota_unit_time?: string;
  enabled?: string;
  who?: string[];
  id: string;
}

/**
 * Contains a custom category.
 * This is checked when making a policy decision.
 */
export interface ICustomCategory {
  id: string;
  category_id?: string;
  from_blocklist?: string;
  href?: string;
  name?: string;
  component?: ICustomCategoryComponent;
  custom_content?: string;
  tenant?: string;
  description?: string;
  external?: string;
}

/**
 * Contains the conmponents of the custom category.
 * Used to define components such as the url or search terms when making a policy decision.
 */
export interface ICustomCategoryComponent {
  domainsurls?: string[];
  searchterms?: string[];
  regexpurls?: string[];
  weightedphrases?: string[];
  videoids?: string[];
}

/**
 * Contains the custom content mods that have been created in the cloud filter portal.
 */
export interface ICloudContentMod {
  id: string;
  tenantId: string;
  filterType: 'include' | 'exclude';
  enabled: boolean;
  groups: string[];
  locations: string[];
  blockmanId: string;
}

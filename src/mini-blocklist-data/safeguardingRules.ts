/**
 * Greater weight in severity is more severe.
 * Lesser value in importance is more important.
 */
const safeguardingRules = {
  radicalisation: {
    title: 'Radicalisation',
    importance: 2,
    severities: {
      Advisory: {
        weight: 1,
        categories: {
          'Radicalisation - Advisory': 1,
        },
      },
      Caution: {
        weight: 2,
        categories: {
          Intolerance: 1,
          'Radicalisation - Caution': 1,
        },
      },
      Danger: {
        weight: 3,
        categories: {
          Terrorism: 1,
          'Radicalisation - Danger': 1,
        },
      },
    },
    exclusions: [
      'Education and Reference',
      'Safeguarding Exclusions',
      'Search Suggestions',
      'Adverts',
      'APIs & Web Libraries',
      'Content Delivery',
    ],
  },
  abuse: {
    title: 'Abuse',
    importance: 3,
    severities: {
      Advisory: {
        weight: 1,
        categories: {
          'Abuse - Advisory': 1,
        },
      },
      Caution: {
        weight: 2,
        categories: {
          'Self Harm': 1,
          'Abuse - Caution': 1,
        },
      },
      Danger: {
        weight: 3,
        categories: {
          'Child Abuse': 1,
          'Abuse - Danger': 1,
        },
      },
    },
    exclusions: [
      'Education and Reference',
      'Safeguarding Exclusions',
      'Search Suggestions',
      'Adverts',
      'APIs & Web Libraries',
      'Content Delivery',
    ],
  },
  suicide: {
    title: 'Suicide',
    importance: 1,
    severities: {
      Advisory: {
        weight: 1,
        categories: {
          'Suicide - Advisory': 1,
        },
      },
      Caution: {
        weight: 2,
        categories: {
          'Child Abuse': 1,
          'Suicide - Caution': 1,
        },
      },
      Danger: {
        weight: 3,
        categories: {
          'Self Harm': 1,
          'Suicide - Danger': 1,
        },
      },
    },
    exclusions: [
      'Medical Information',
      'Safeguarding Exclusions',
      'Search Suggestions',
      'Adverts',
      'APIs & Web Libraries',
      'Content Delivery',
    ],
  },
  substanceabuse: {
    title: 'Substance abuse',
    importance: 4,
    severities: {
      Advisory: {
        weight: 1,
        categories: {
          'Substance abuse - Advisory': 1,
        },
      },
      Caution: {
        weight: 2,
        categories: {
          'Substance abuse - Caution': 1,
        },
      },
      Danger: {
        weight: 3,
        categories: {
          Drugs: 1,
          'Substance abuse - Danger': 1,
        },
      },
    },
    exclusions: [
      'Education and Reference',
      'Medical Information',
      'Safeguarding Exclusions',
      'Search Suggestions',
      'Adverts',
      'APIs & Web Libraries',
      'Content Delivery',
    ],
  },
  bullying: {
    title: 'Bullying',
    importance: 5,
    severities: {
      Advisory: {
        weight: 1,
        categories: {
          'Bullying - Advisory': 1,
        },
      },
      Caution: {
        weight: 2,
        categories: {
          Intolerance: 1,
          'Self Harm': 1,
          'Bullying - Caution': 1,
        },
      },
      Danger: {
        weight: 3,
        categories: {
          'Child Abuse': 1,
          'Bullying - Danger': 1,
        },
      },
    },
    exclusions: [
      'Education and Reference',
      'Safeguarding Exclusions',
      'Search Suggestions',
      'Adverts',
      'APIs & Web Libraries',
      'Content Delivery',
    ],
  },
  criminalactivity: {
    title: 'Criminal activity',
    importance: 6,
    severities: {
      Advisory: {
        weight: 1,
        categories: {
          'Criminal Activity': 1,
          'Personal Weapons': 1,
          'Criminal activity - Advisory': 1,
        },
      },
      Caution: {
        weight: 2,
        categories: {
          Violence: 1,
          'Criminal activity - Caution': 1,
        },
      },
      Danger: {
        weight: 3,
        categories: {
          Drugs: 1,
          'Criminal activity - Danger': 1,
        },
      },
    },
    exclusions: [
      'Military',
      'Online Games',
      'Education and Reference',
      'Medical Information',
      'Safeguarding Exclusions',
      'Search Suggestions',
      'Adverts',
      'APIs & Web Libraries',
      'Content Delivery',
    ],
  },
  adultcontent: {
    title: 'Adult content',
    importance: 7,
    severities: {
      Advisory: {
        weight: 1,
        categories: {
          'Sexuality Sites': 1,
          'Adult Content - Advisory': 1,
          'Dating and Companionship Sites': 1,
        },
      },
      Caution: {
        weight: 2,
        categories: {
          Gore: 1,
          'Adult Content - Caution': 1,
        },
      },
      Danger: {
        weight: 3,
        categories: {
          Pornography: 1,
          'Adult Content - Danger': 1,
        },
      },
    },
    exclusions: [
      'Education and Reference',
      'Safeguarding Exclusions',
      'Search Suggestions',
      'Adverts',
      'APIs & Web Libraries',
      'Content Delivery',
    ],
  },
};

export default safeguardingRules;

import LicenseResult from 'models/LicenseResult';
import StorageService from 'services/StorageService';
import AccessLogUploadResult from 'models/AccessLogUploadResult';
import AccessLogEntry from 'models/AccessLogEntry';
import ITelemetryService from 'services/ITelemetryService';
import CloudReportingVersion from 'constants/CloudReportingVersion';
import { TelemetryEventType } from '../../constants/TelemetryEventType';

/**
 * The base class for any access log processors that holds common functions and data.
 */
export default abstract class BaseAccessLogProcessor {
  constructor(
    licenseInfo: LicenseResult,
    hardwareId: string,
    storageService: StorageService,
    telemetryService: ITelemetryService,
  ) {
    this._licenseInfo = licenseInfo;
    this._hardwareId = hardwareId;
    this._storageService = storageService;
    this._telemetryService = telemetryService;
  }

  /**
   * Sends the given request for creating or appending access logs.
   *
   * @param request A request object to be sent for uploading the access logs.
   * @param logsToSend Array of stringified logs to be sent. Empty arary if creating new blob.
   * @returns A results object with details of the request.
   */
  protected readonly _sendAccessLogsRequest = async (
    request: Request,
    logsToSend: string[] = [],
  ): Promise<AccessLogUploadResult> => {
    const result: AccessLogUploadResult = {
      version: 'v' + this._licenseInfo.cldrptVersion.toString(),
      cloudFileName: request.url,
      fileCreated: false,
      statusCode: 0,
      wasSuccess: false,
    };

    try {
      const response = await fetch(request);
      result.statusCode = response.status;

      if (!response.ok) {
        const responseText = await response.text();

        // Don't create an error for 404 results from blob requests. The file will get recreated on the next run.
        if (
          response.status === 404 &&
          logsToSend.length > 0 &&
          this._licenseInfo.cldrptVersion !== CloudReportingVersion.v4
        ) {
          result.wasSuccess = false;
          return result;
        }

        throw new Error(responseText);
      }

      if (logsToSend.length > 0) {
        const metrics = this._getLogMetrics(logsToSend);

        result.logCount = logsToSend.length;
        result.allowCount = metrics.allowCount;
        result.blockCount = metrics.blockCount;
        result.fileCreated = false;
        result.logsSent = logsToSend;
        result.wasSuccess = true;
      } else {
        result.fileCreated = true;
        result.wasSuccess = true;
      }

      return result;
    } catch (error: any) {
      console.warn(`Access log ${request.method} to cloud failed`, error);

      if (logsToSend.length > 0) {
        result.fileCreated = false;
        result.wasSuccess = false;

        // Don't log failed to fetch errors.
        if (result.statusCode === 0) {
          return result;
        }

        const metrics = this._getLogMetrics(logsToSend);

        this._telemetryService.logError(
          TelemetryEventType.LogUploadFailed,
          `Access log ${request.method} to cloud failed. ${error.message as string}`,
          {
            version: result.version,
            cloudFileName: request.url,
            logcount: logsToSend.length,
            allowCount: metrics.allowCount,
            blockCount: metrics.blockCount,
            responseCode: result.statusCode,
          },
        );

        return result;
      } else {
        // Don't log failed to fetch errors.
        if (result.statusCode === 0) {
          throw error;
        }

        this._telemetryService.logError(
          TelemetryEventType.LogFileCreationFailed,
          `Could not create a new log file in the cloud. ${error.message as string}`,
          {
            version: result.version,
            cloudFileName: request.url,
            responseCode: result.statusCode,
          },
        );
        throw error;
      }
    }
  };

  /**
   * Logs the information about the given upload results to the telemetry service.
   * @param uploadResults An array of results to upload.
   */
  protected readonly _logAccessLogUploads = (uploadResults: AccessLogUploadResult[]): void => {
    const newFileResults = uploadResults.filter((r) => r.fileCreated);
    const existingFileResults = uploadResults.filter((r) => !r.fileCreated);

    if (newFileResults.length > 0) {
      this._telemetryService.logEvent(TelemetryEventType.LogFileCreation, {
        version: newFileResults[0].version,
        cloudFileName: newFileResults.flatMap((r) => r.cloudFileName),
      });
    }

    if (existingFileResults.length > 0) {
      // Aggregate all of the results into one object and log that to the telemetry service.
      const result = {
        version: existingFileResults[0].version,
        cloudFileName: existingFileResults.flatMap((r) => r.cloudFileName),
        logCount: 0,
        allowCount: 0,
        blockCount: 0,
      };

      for (const r of existingFileResults) {
        result.logCount += r.logCount ?? 0;
        result.allowCount += r.allowCount ?? 0;
        result.blockCount += r.blockCount ?? 0;
      }

      this._telemetryService.logEvent(TelemetryEventType.LogUpload, {
        version: result.version,
        cloudFileName: result.cloudFileName,
        logCount: result.logCount,
        allowCount: result.allowCount,
        blockCount: result.blockCount,
      });
    }
  };

  /**
   * Calculates the allow and blocked counts for the given logs.
   * @param logsToSend The logs to count.
   * @returns An object with the allow and blocked count.
   */
  private readonly _getLogMetrics = (
    logsToSend: string[],
  ): { allowCount: number; blockCount: number } => {
    const logs = logsToSend.map((l) => JSON.parse(l) as AccessLogEntry);

    let allowCount = 0;
    let blockCount = 0;

    logs.forEach((log) => {
      blockCount += log.blocked ?? false ? 1 : 0;
    });

    allowCount = logs.length - blockCount;

    return { allowCount, blockCount };
  };

  protected readonly _storageService: StorageService;

  protected readonly _licenseInfo: LicenseResult;

  protected readonly _hardwareId: string;

  protected readonly _telemetryService: ITelemetryService;
}

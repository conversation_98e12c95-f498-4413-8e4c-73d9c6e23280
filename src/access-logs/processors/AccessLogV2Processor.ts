import moment from 'moment';
import BaseAccessLogProcessor from './BaseAccessLogProcessor';
import IAccessLogProcessor from './IAccessLogProcessor';
import V2CloudFileDetails from 'models/V2CloudFileDetails';
import TemplateString from 'models/TemplateString';
import ITelemetryService from 'services/ITelemetryService';
import StorageService from 'services/StorageService';
import LicenseResult from 'models/LicenseResult';

/**
 * The processor for uploading access logs to ingest v2.
 */
export default class AccessLogV2Processor
  extends BaseAccessLogProcessor
  implements IAccessLogProcessor
{
  constructor(
    licenseInfo: LicenseResult,
    hardwareId: string,
    storageService: StorageService,
    telemetryService: ITelemetryService,
    ingestUrlTemplate: TemplateString,
  ) {
    super(licenseInfo, hardwareId, storageService, telemetryService);

    this._ingestUrlTemplate = ingestUrlTemplate;
  }

  /**
   * Processes the logs that need to be sent and uploads them using the ingest v2 method.
   * @param logsToSend The logs to be sent to the cloud.
   * @returns A promise that resolves to an array of strings for the logs that were sent to the cloud.
   */
  public readonly processLogsForUpload = async (logsToSend: string[]): Promise<string[]> => {
    const request = this._createUploadRequest(await this._createCloudFile(), logsToSend);
    const result = await this._sendAccessLogsRequest(request, logsToSend);

    // If the file we are uploading to has been removed from blob storage then we need to remove it from our list of file names.
    // This is so we can create a new file and append to that the next time we are uploading.
    if (!result.wasSuccess) {
      if (result.statusCode === 404) {
        this._v2CloudFile = { name: '', date: '' };
      }
      return [];
    }

    this._logAccessLogUploads([result]);
    return result.logsSent ?? [];
  };

  /**
   * Checks if Ingest v2 cloud file name is out of date, creates a new append blob.
   * @param apiInfo The URL and SAS string for PUTing the logs to the cloud.
   * @returns Ingest v2 cloud file name.
   */
  private readonly _createCloudFile = async (): Promise<string> => {
    if (
      this._licenseInfo.cldrptConfig?.accessLogsBlobConfig === undefined ||
      this._ingestUrlTemplate === undefined
    ) {
      return '';
    }

    const aMinuteFromNow = moment(new Date(Date.now() + 60 * 1000))
      .utc()
      .format('YYYY/M/D/H');

    if (this._v2CloudFile.name === '' || this._v2CloudFile.date !== aMinuteFromNow) {
      const now = moment(new Date()).utc().format('YYYY/M/D/H');

      const fileName = {
        date: now,
        name: this._ingestUrlTemplate.toString({
          resource: this._licenseInfo.cldrptConfig.accessLogsBlobConfig.resource,
          date: now,
          hardwareId: this._hardwareId ?? '',
          timestamp: performance.now().toString(),
          sas: this._licenseInfo.cldrptConfig.accessLogsBlobConfig.sas,
        }),
      };

      try {
        // Make the new append blob in the cloud.
        this._v2CloudFile = fileName;
        const request = this._createUploadRequest(this._v2CloudFile.name);
        const result = await this._sendAccessLogsRequest(request);

        this._logAccessLogUploads([result]);
      } catch (error: any) {
        console.debug('Could not create the cloud file, will try again next run.');
      }
    }
    return this._v2CloudFile.name;
  };

  /**
   * Creates a request object for creating or appending access logs.
   * @param url The url that the request should target.
   * @param logsToSend Array of stringified logs to be sent. Leave empty if creating new blob.
   * @returns A request object with the data required to make the appropriate blob request.
   */
  private readonly _createUploadRequest = (url: string, logsToSend: string[] = []): Request => {
    const appending = logsToSend.length > 0;
    const data: string | Blob = logsToSend.join('\n') + (appending ? '\n' : '');

    const headers = {
      'x-ms-version': '2021-08-06',
      'x-ms-date': new Date().toString(),
      'x-ms-blob-type': 'AppendBlob',
      'x-ms-meta-m1': 'v1',
      'x-ms-meta-m2': 'v2',
    };

    const request: Request = new Request(url + (appending ? '&comp=appendblock' : ''), {
      method: 'PUT',
      body: data,
      headers,
    });

    if (appending) {
      request.headers.append('Content-Type', 'text/plain; charset=UTF-8');
    }

    return request;
  };

  /**
   * The name and date for the current cloud Ingest v2 file.
   */
  private get _v2CloudFile(): V2CloudFileDetails {
    let v2CloudFile = this._storageService.get('v2CloudFile') as V2CloudFileDetails;
    if (v2CloudFile === undefined) {
      v2CloudFile = { name: '', date: '' };
      this._v2CloudFile = v2CloudFile;
    }
    return v2CloudFile;
  }

  private set _v2CloudFile(v2CloudFile: V2CloudFileDetails) {
    this._storageService.set('v2CloudFile', v2CloudFile);
  }

  /**
   * A template describing the URL to use for the Ingest target if required.
   */
  protected readonly _ingestUrlTemplate?: TemplateString;
}

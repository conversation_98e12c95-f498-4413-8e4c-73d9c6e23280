/**
 * A processor for sending access logs using one of the ingest methods.
 */
export default interface IAccessLogProcessor {
  /**
   * Processes the logs that need to be sent and uploads them using the appropriate ingest method.
   * @param logsToSend The logs to be sent to the cloud.
   * @returns A promise that resolves to an array of strings for the logs that were sent to the cloud.
   */
  processLogsForUpload: (logsToSend: string[]) => Promise<string[]>;
}

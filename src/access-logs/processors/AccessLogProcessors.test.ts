/* eslint-disable @typescript-eslint/no-non-null-assertion */
import LicenseResult from 'models/LicenseResult';
import AccessLogV2Processor from './AccessLogV2Processor';
import CloudReportingVersion from 'constants/CloudReportingVersion';
import StorageService from 'services/StorageService';
import MockTelemetryService from 'test-helpers/MockTelemetryService';
import extensionConfig from 'config.json';
import TemplateString from 'models/TemplateString';
import moment from 'moment';
import AccessLogV3Processor from './AccessLogV3Processor';
import { generateLog } from 'test-helpers/access-log-helpers';
import TenantId from 'models/TenantId';
import Uuid from 'models/Uuid';
import AccessLogV4Processor from './AccessLogV4Processor';

const v2License: LicenseResult = {
  cldfltLicenseValid: true,
  cldrptVersion: CloudReportingVersion.v2,
  cldrptConfig: {
    accessLogsBlobConfig: {
      resource: 'https://resource/',
      sas: 'sas',
    },
  },
};

const v3License: LicenseResult = {
  cldfltLicenseValid: true,
  cldrptVersion: CloudReportingVersion.v3,
  cldrptConfig: {
    accessLogsBlobConfig: {
      resource: 'https://resource/',
      sas: 'sas',
    },
  },
};

const v4License: LicenseResult = {
  cldfltLicenseValid: true,
  cldrptVersion: CloudReportingVersion.v4,
  cldrptConfig: {
    accessLogsIngestV4Config: {
      url: 'https://iv4.test.com/',
      bearerToken: 'Bearer token',
    },
  },
};

const hardwareId = 'test-hardware-id';
const telemetryService = new MockTelemetryService();

const fetchMock = (globalThis as any).fetch as jest.SpyInstance;

describe('AccessLogProcessors', () => {
  let storageService: StorageService;

  beforeEach(() => {
    storageService = new StorageService('accessLogs', undefined);

    // Suppress console messages.
    jest.spyOn(console, 'debug').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  describe('processLogsForUpload', () => {
    beforeEach(() => {
      fetchMock.mockImplementation(
        async () =>
          await Promise.resolve({
            ok: true,
            status: 200,
            json: async () => {},
            text: async () => '',
          }),
      );
    });

    describe('AccessLogV2Processor', () => {
      let v2Processor: AccessLogV2Processor;

      const formattedDate = moment(new Date()).utc().format('YYYY/M/D/H');
      const urlRegexString = String.raw`${
        v2License.cldrptConfig!.accessLogsBlobConfig!.resource
      }${formattedDate}\/access\.log.${hardwareId}\.\d+\.\d+${
        v2License.cldrptConfig!.accessLogsBlobConfig!.sas
      }`;

      beforeEach(() => {
        v2Processor = new AccessLogV2Processor(
          v2License,
          hardwareId,
          storageService,
          telemetryService,
          new TemplateString(extensionConfig.ingestV2UrlTemplate),
        );
      });

      it('should successfully send v2 logs', async () => {
        const logsToSend = [
          JSON.stringify(generateLog('main_frame', 'https://test.com', 2)),
          JSON.stringify(generateLog('main_frame', 'https://test.com/example', 2)),
        ];

        const result = await v2Processor.processLogsForUpload(logsToSend);

        // The urls contain a millisecond timestamp so match with regex.
        // Fetch will be called to create the file then again to append the data.
        const expectedUrl = new RegExp(urlRegexString);
        const expectedUrlAppend = new RegExp(urlRegexString + '&comp=appendblock');

        expect(fetchMock).toHaveBeenNthCalledWith(
          1,
          expect.objectContaining({ url: expect.stringMatching(expectedUrl) }),
        );
        expect(fetchMock).toHaveBeenNthCalledWith(
          2,
          expect.objectContaining({ url: expect.stringMatching(expectedUrlAppend) }),
        );
        expect(fetchMock.mock.calls[0][0]).toSatisfy(
          (req: Request) => req.headers.get('content-type') === 'text/plain;charset=UTF-8',
        );
        expect(result).toBeArrayOfSize(2);
      });

      it('correctly handles an error when creating the log file', async () => {
        const logsToSend = [
          JSON.stringify(generateLog('main_frame', 'https://test.com', 2)),
          JSON.stringify(generateLog('main_frame', 'https://test.com/example', 2)),
        ];

        fetchMock.mockImplementation(
          async () =>
            await Promise.resolve({
              ok: false,
              status: 400,
              text: async () => 'Test error',
            }),
        );

        const result = await v2Processor.processLogsForUpload(logsToSend);

        // The urls contain a millisecond timestamp so match with regex.
        const expectedUrl = new RegExp(urlRegexString);

        expect(fetchMock).toHaveBeenCalledWith(
          expect.objectContaining({ url: expect.stringMatching(expectedUrl) }),
        );
        expect(result).toBeArrayOfSize(0);
      });

      describe('AccessLogV3Processor', () => {
        let v3Processor: AccessLogV3Processor;
        const tenantId = new TenantId(Uuid.random().toString());

        const dateIndex = moment(new Date()).utc().format('YYYY/M/D/H');

        const unauthUrlRegexString = String.raw`${
          v3License.cldrptConfig!.accessLogsBlobConfig!.resource
        }v3\/${tenantId.get()}\/\_sw\_cloud\_hid\_\/${dateIndex}\/unauth\/\_sw\_unauth\_traffic\_\/detailed\/access\.${hardwareId}\.\d+\.\d+\.log\.gz${
          v3License.cldrptConfig!.accessLogsBlobConfig!.sas
        }`;

        beforeEach(() => {
          v3Processor = new AccessLogV3Processor(
            v3License,
            hardwareId,
            storageService,
            telemetryService,
            new TemplateString(extensionConfig.ingestV3UrlTemplate),
            tenantId,
          );
        });

        it('should successfully send v3 logs', async () => {
          const logsToSend = [
            JSON.stringify(generateLog('main_frame', 'https://test.com', 2)),
            JSON.stringify(generateLog('main_frame', 'https://test.com/example', 2)),
          ];

          const result = await v3Processor.processLogsForUpload(logsToSend);

          // The urls contain a unix timestamp and random number so match with regex.
          // Fetch will be called to create the file then again to append the data.
          const expectedUrl = new RegExp(unauthUrlRegexString);
          const expectedUrlAppend = new RegExp(unauthUrlRegexString + '&comp=appendblock');

          expect(fetchMock).toHaveBeenNthCalledWith(
            1,
            expect.objectContaining({ url: expect.stringMatching(expectedUrl) }),
          );
          expect(fetchMock).toHaveBeenNthCalledWith(
            2,
            expect.objectContaining({ url: expect.stringMatching(expectedUrlAppend) }),
          );
          expect(fetchMock.mock.calls[1][0]).toSatisfy(
            (req: Request) =>
              req.headers.get('content-type') === 'application/json, charset=x-user-defined-binary',
          );
          expect(result).toBeArrayOfSize(2);
        });

        it('correctly handles an error when creating the log file', async () => {
          const logsToSend = [
            JSON.stringify(generateLog('main_frame', 'https://test.com', 2)),
            JSON.stringify(generateLog('main_frame', 'https://test.com/example', 2)),
          ];

          fetchMock.mockImplementation(
            async () =>
              await Promise.resolve({
                ok: false,
                status: 400,
                text: async () => 'Test error',
              }),
          );

          const result = await v3Processor.processLogsForUpload(logsToSend);

          // The urls contain a millisecond timestamp and random number so match with regex.
          const expectedUrl = new RegExp(unauthUrlRegexString);

          expect(fetchMock).toHaveBeenCalledWith(
            expect.objectContaining({ url: expect.stringMatching(expectedUrl) }),
          );
          expect(result).toBeArrayOfSize(0);
        });
      });

      describe('AccessLogV4Processor', () => {
        let v4Processor: AccessLogV4Processor;

        beforeEach(() => {
          v4Processor = new AccessLogV4Processor(
            v4License,
            hardwareId,
            storageService,
            telemetryService,
          );
        });

        it('should successfully send v4 logs', async () => {
          const logsToSend = [
            JSON.stringify(generateLog('main_frame', 'https://test.com', 2)),
            JSON.stringify(generateLog('main_frame', 'https://test.com/example', 2)),
          ];

          const result = await v4Processor.processLogsForUpload(logsToSend);

          const expectedUrl = v4License.cldrptConfig!.accessLogsIngestV4Config!.url;

          expect(fetchMock).toHaveBeenCalledExactlyOnceWith(
            expect.objectContaining({
              url: expectedUrl,
            }),
          );
          expect(fetchMock.mock.calls[0][0]).toSatisfy(
            (req: Request) =>
              req.headers.get('authorization') ===
                v4License.cldrptConfig!.accessLogsIngestV4Config!.bearerToken &&
              req.headers.get('content-type') === 'text/plain;charset=UTF-8',
          );
          expect(result).toBeArrayOfSize(2);
        });

        it('correctly handles an error when creating the log file', async () => {
          const logsToSend = [
            JSON.stringify(generateLog('main_frame', 'https://test.com', 2)),
            JSON.stringify(generateLog('main_frame', 'https://test.com/example', 2)),
          ];

          fetchMock.mockImplementation(
            async () =>
              await Promise.resolve({
                ok: false,
                status: 400,
                text: async () => 'Test error',
              }),
          );

          const result = await v4Processor.processLogsForUpload(logsToSend);

          const expectedUrl = v4License.cldrptConfig!.accessLogsIngestV4Config!.url;

          expect(fetchMock).toHaveBeenCalledExactlyOnceWith(
            expect.objectContaining({
              url: expectedUrl,
            }),
          );
          expect(result).toBeArrayOfSize(0);
        });
      });
    });
  });
});

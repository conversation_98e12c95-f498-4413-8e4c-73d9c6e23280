import BaseAccessLogProcessor from './BaseAccessLogProcessor';
import IAccessLogProcessor from './IAccessLogProcessor';

/**
 * The processor for uploading access logs to ingest v4.
 */
export default class AccessLogV4Processor
  extends BaseAccessLogProcessor
  implements IAccessLogProcessor
{
  /**
   * Processes the logs that need to be sent and uploads them using the ingest v4 method.
   * @param logsToSend The logs to be sent to the cloud.
   * @returns A promise that resolves to an array of strings for the logs that were sent to the cloud.
   */
  public readonly processLogsForUpload = async (logsToSend: string[]): Promise<string[]> => {
    if (this._licenseInfo.cldrptConfig?.accessLogsIngestV4Config === undefined) {
      return [];
    }

    const request = this._createUploadRequest(
      this._licenseInfo.cldrptConfig.accessLogsIngestV4Config.url,
      this._licenseInfo.cldrptConfig.accessLogsIngestV4Config.bearerToken,
      logsToSend,
    );

    const result = await this._sendAccessLogsRequest(request, logsToSend);
    this._logAccessLogUploads([result]);

    return result.logsSent ?? [];
  };

  /**
   * Creates a request object for calling the access logs endpoint.
   * @param url The url that the request should target.
   * @param bearerToken The bearer token to use in the authorization header.
   * @param logsToSend Array of stringified logs to be sent. Leave empty if creating new blob.
   * @returns A request object with the data required to make the appropriate api call.
   */
  private readonly _createUploadRequest = (
    url: string,
    bearerToken: string,
    logsToSend: string[],
  ): Request => {
    const logs: string[] = [];
    for (const logString of logsToSend) {
      const log = JSON.parse(logString);
      log.v = 4;
      log.useragent = log.userAgent;
      delete log.userAgent;
      logs.push(JSON.stringify(log));
    }

    const data: string = logs.join('\n');

    const headers = {
      Authorization: bearerToken,
    };

    const request: Request = new Request(url, {
      method: 'POST',
      body: data,
      headers,
    });

    return request;
  };
}

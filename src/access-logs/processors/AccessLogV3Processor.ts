import * as pako from 'pako';
import moment from 'moment';

import AccessLogUploadResult from 'models/AccessLogUploadResult';
import BaseAccessLogProcessor from './BaseAccessLogProcessor';
import IAccessLogProcessor from './IAccessLogProcessor';
import { base64UrlSafe, getUnixTime } from 'utilities/Helpers';
import AccessLogEntry from 'models/AccessLogEntry';
import { V3LogBucket } from 'models/V3LogBucket';
import TenantId from 'models/TenantId';
import LicenseResult from 'models/LicenseResult';
import StorageService from 'services/StorageService';
import ITelemetryService from 'services/ITelemetryService';
import TemplateString from 'models/TemplateString';

/**
 * The processor for uploading access logs to ingest v3.
 */
export default class AccessLogV3Processor
  extends BaseAccessLogProcessor
  implements IAccessLogProcessor
{
  constructor(
    licenseInfo: LicenseResult,
    hardwareId: string,
    storageService: StorageService,
    telemetryService: ITelemetryService,
    ingestUrlTemplate: TemplateString,
    tenantId: TenantId | undefined,
  ) {
    super(licenseInfo, hardwareId, storageService, telemetryService);

    this._ingestUrlTemplate = ingestUrlTemplate;
    this._tenantId = tenantId;
  }

  /**
   * Processes the logs that need to be sent and uploads them using the ingest v3 method.
   * @param logsToSend The logs to be sent to the cloud.
   * @returns A promise that resolves to an array of strings for the logs that were sent to the cloud.
   */
  public readonly processLogsForUpload = async (logsToSend: string[]): Promise<string[]> => {
    const logBuckets: V3LogBucket = this._splitLogsIntoV3Buckets(logsToSend);
    const sentLogs: string[] = [];
    const results: AccessLogUploadResult[] = [];

    for (const dateIndex of Object.keys(logBuckets)) {
      if (logBuckets[dateIndex].toplevel.length > 0) {
        const result = await this._sendV3DetailLevel(
          dateIndex,
          'toplevel',
          logBuckets[dateIndex].toplevel,
        );

        if (result.logsSent !== undefined && result.logsSent.length > 0) {
          sentLogs.push(...result.logsSent);
        }

        results.push(result);
      }

      if (logBuckets[dateIndex].detailed.length > 0) {
        const result = await this._sendV3DetailLevel(
          dateIndex,
          'detailed',
          logBuckets[dateIndex].detailed,
        );

        if (result.logsSent !== undefined && result.logsSent.length > 0) {
          sentLogs.push(...result.logsSent);
        }

        results.push(result);
      }
    }

    // If the file we are uploading to has been removed from blob storage then we need to remove it from our list of file names.
    // This is so we can create a new file and append to that the next time we are uploading.
    const errorResults = results.filter(
      (r: AccessLogUploadResult) => !r.wasSuccess && r.statusCode === 404,
    );
    if (errorResults.length > 0) {
      for (const result of errorResults) {
        this._v3CloudFiles = this._v3CloudFiles.filter((f) => !result.cloudFileName.startsWith(f));
      }
    }

    // Log any successful uploads. Errors are logged individually.
    this._logAccessLogUploads(results.filter((r) => r.wasSuccess));

    return sentLogs;
  };

  /**
   * Called on each detail level of a dateIndex on a log bucket to send logs to cloud.
   *
   * @param dateIndex Date string of the bucket. For example '2023/1/9/19'.
   * @param detailLevel Name of the detail level. Either 'toplevel' or 'detailed'
   * @param logToSend Array of the logs to be sent.
   * @returns A results object with details of the request.
   */
  private readonly _sendV3DetailLevel = async (
    dateIndex: string,
    detailLevel: string,
    logToSend: AccessLogEntry[],
  ): Promise<AccessLogUploadResult> => {
    const v3FileName = await this._createV3CloudFile(detailLevel, dateIndex, logToSend[0].username);
    const logsToSend = logToSend.map((log: AccessLogEntry) => JSON.stringify(log));
    const request = this._createUploadRequest(v3FileName, logsToSend);
    return await this._sendAccessLogsRequest(request, logsToSend);
  };

  /**
   * Determine v3 cloud file name based on inputs, then ensure the file exists in the cloud.
   *
   * @param level Name of the detail level. Either 'toplevel' or 'detailed'.
   * @param date Date string formatted as 'YYYY/MM/DD/HH'.
   * @param user Optionally undefined user.
   * @returns File name for ingest v3 in cloud. Formatted as follows:
   *   <version>/<tenant id>/<appliance hardware id>/<year>/<month>/<day>/<hour>/auth/<user>/<level>/<file name>.gz
   *   For details, see: https://familyzone.atlassian.net/wiki/spaces/CR/pages/2694343590012/V3+Blob+Storage+Partitioning
   */
  private readonly _createV3CloudFile = async (
    level: string,
    date: string,
    user?: string,
  ): Promise<string> => {
    if (
      this._hardwareId === undefined ||
      this._licenseInfo.cldrptConfig?.accessLogsBlobConfig === undefined ||
      this._ingestUrlTemplate === undefined
    ) {
      console.error('Error sending Ingest v3 logs: hardwareId undefined');
      return '';
    }

    const baseUrl = this._ingestUrlTemplate.toString({
      resource: this._licenseInfo.cldrptConfig?.accessLogsBlobConfig.resource,
      tenant: this._tenantId?.get() ?? '_untenanted_',
      date,
      auth: user !== undefined ? 'auth' : 'unauth',
      user: user !== undefined ? base64UrlSafe(user.toLowerCase()) : '_sw_unauth_traffic_',
      level,
    });

    let v3FileName = this._v3CloudFiles.find((v3FileName) => new RegExp(baseUrl).test(v3FileName));

    if (v3FileName === undefined) {
      const random = Math.floor(Math.random() * 10000000);
      v3FileName =
        baseUrl +
        `access.${this._hardwareId.toLowerCase()}.${getUnixTime()}.${random}.log.gz${
          this._licenseInfo.cldrptConfig.accessLogsBlobConfig.sas
        }`;
      try {
        const request = this._createUploadRequest(v3FileName);
        const result = await this._sendAccessLogsRequest(request);
        this._v3CloudFiles.push(v3FileName);
        this._storageService.saveInTheBackground();

        this._logAccessLogUploads([result]);
      } catch (error: any) {
        console.debug('Could not create the cloud file, will try again next run.');
      }
    }

    return v3FileName;
  };

  /**
   * Takes an array of log lines and splits it into buckets based on hour and detail level.
   *
   * @param logToSend Array of stringified log lines.
   * @returns V3LogBucket Indexed by date string 'YYYY/MM/DD/HH', divided into two log levels.
   */
  private readonly _splitLogsIntoV3Buckets = (logToSend: string[]): V3LogBucket => {
    const logBuckets: V3LogBucket = {};

    const parsedLog = logToSend.filter((log) => log).map((log) => JSON.parse(log));
    const logTimings = parsedLog.map((log) => Math.floor(+log.time / 3600));
    // If logs are within the same hour, simply filter them by log levels.
    if (Math.min(...logTimings) === Math.max(...logTimings)) {
      const dateIndex = moment(new Date(+parsedLog[0].time * 1000))
        .utc()
        .format('YYYY/M/D/H');

      if (logBuckets[dateIndex] === undefined)
        logBuckets[dateIndex] = {
          toplevel: parsedLog.filter((log) => log.loglevel > 2),
          detailed: parsedLog.filter((log) => log.loglevel <= 2),
        };

      // If log aren't within the same hour, go through them one by one.
    } else
      for (const log of parsedLog) {
        const dateIndex = moment(new Date(+log.time * 1000))
          .utc()
          .format('YYYY/M/D/H');

        const loglevel = log.loglevel > 2 ? 'toplevel' : 'detailed';

        if (logBuckets[dateIndex] === undefined)
          logBuckets[dateIndex] = { toplevel: [], detailed: [] };

        logBuckets[dateIndex][loglevel].push(log);
      }

    return logBuckets;
  };

  /**
   * Creates a request object for creating or appending access logs.
   * @param url The url that the request should target.
   * @param logsToSend Array of stringified logs to be sent. Leave empty if creating new blob.
   * @returns A request object with the data required to make the appropriate blob request.
   */
  private readonly _createUploadRequest = (url: string, logsToSend: string[] = []): Request => {
    const appending = logsToSend.length > 0;
    let data: string | Blob = logsToSend.join('\n') + (appending ? '\n' : '');

    if (appending) {
      data = new Blob([pako.gzip(data)], {
        type: 'application/json, charset=x-user-defined-binary',
      });
    }

    const headers = {
      'x-ms-version': '2021-08-06',
      'x-ms-date': new Date().toString(),
      'x-ms-blob-type': 'AppendBlob',
      'x-ms-meta-m1': 'v1',
      'x-ms-meta-m2': 'v2',
    };

    const request: Request = new Request(url + (appending ? '&comp=appendblock' : ''), {
      method: 'PUT',
      body: data,
      headers,
    });

    return request;
  };

  /**
   * List of names for past cloud Ingest v3 files.
   */
  private get _v3CloudFiles(): string[] {
    let v3CloudFiles = this._storageService.get('v3CloudFiles') as string[];
    if (v3CloudFiles === undefined) {
      v3CloudFiles = [];
      this._v3CloudFiles = v3CloudFiles;
    }
    return v3CloudFiles;
  }

  private set _v3CloudFiles(v3CloudFiles: string[]) {
    this._storageService.set('v3CloudFiles', v3CloudFiles);
  }

  protected readonly _tenantId?: TenantId;

  /**
   * A template describing the URL to use for the Ingest target if required.
   */
  protected readonly _ingestUrlTemplate?: TemplateString;
}

import LicenseResult from 'models/LicenseResult';
import StorageService from 'services/StorageService';
import AccessLogEntry from 'models/AccessLogEntry';
import TenantId from 'models/TenantId';
import ITelemetryService from '../services/ITelemetryService';
import IExtensionConfig from 'models/IExtensionConfig';
import IAccessLogProcessor from './processors/IAccessLogProcessor';
import CloudReportingVersion from 'constants/CloudReportingVersion';
import AccessLogV3Processor from './processors/AccessLogV3Processor';
import AccessLogV2Processor from './processors/AccessLogV2Processor';
import TemplateString from 'models/TemplateString';
import AccessLogV4Processor from './processors/AccessLogV4Processor';
import AccessLogStorage from 'access-logs/AccessLogStorage';
import RateLimiter from '../utilities/RateLimiter';

/**
 * Manages the storage and upload of access logs to the cloud.
 */
export default class AccessLogManager {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Initialise a new instance with the given dependencies.
   *
   * @param extensionConfig Provides hard-coded configuration information.
   * @param telemetryService Manages uploading diagnostic logs to the cloud.
   * @param accessLogStorage Stores access logs in memory and persists them to disk.
   * @param rateLimiter Manages the rate at which logs are uploaded to the cloud.
   */
  constructor(
    extensionConfig: IExtensionConfig,
    telemetryService: ITelemetryService,
    accessLogStorage: AccessLogStorage,
    rateLimiter: RateLimiter,
  ) {
    this._telemetryService = telemetryService;
    this._extensionConfig = extensionConfig;
    this._accessLogStorage = accessLogStorage;
    this._rateLimiter = rateLimiter;
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Get the Unix timestamp (in seconds) of when any logs were last uploaded successfully.
   * Returns 0 if they have never been uploaded successfully.
   * This information is not persisted between runs so it will reset each time the extension starts.
   */
  public get lastUploadedAt(): number {
    return this._lastUploadedAt;
  }

  /**
   * Get the number of logs which have been successfully uploaded since the last call.
   * This is used to provide information for DMS check-in requests. Each request reports the number
   *  of logs uploaded since the last check-in, so this value is regularly reset to 0.
   *
   * @param reset If true, the count will be reset to 0 after this call.
   * @returns The number of logs successfully uploaded since start-up, or since the count was last
   *  reset.
   */
  public readonly getUploadCount = (reset: boolean): number => {
    const logsSent = this._uploadCount;

    if (reset) {
      this._uploadCount = 0;
    }

    return logsSent;
  };

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Load pending access logs from pre-loaded storage into memory.
   * This uses a pre-loaded copy of the data from storage because that's the only way to enumerate
   *  all the data. Access logs are stored in dynamically named buffers, and we need to ensure we
   *  don't accidentally lose track of any.
   *
   * @param storageData A pre-loaded copy of the entire contents of storage. The relevant access log
   *  data is extracted, and everything else is ignored.
   */
  public readonly populate = async (storageData: Record<string, any>): Promise<void> => {
    this._accessLogStorage.discardAllLogs();
    this._accessLogStorage.populate(storageData);
    await this._accessLogStorage.populateLegacy(storageData, 90);
  };

  /**
   * Start timers running which periodically persist logs to storage and upload to the cloud.
   * If uploads are already configured then this will trigger an immediate upload.
   *
   * @note No uploads can happen unless the relevant information is also provided via configure().
   */
  public readonly start = async (): Promise<void> => {
    if (this._persistInterval === undefined) {
      this._persistInterval = setInterval(() => {
        this._accessLogStorage.persist().catch(console.warn);
      }, this._persistDelay);
    }

    if (this._uploadInterval === undefined) {
      this._uploadInterval = setInterval(() => {
        this.upload().catch(console.warn);
      }, this._uploadDelay);
    }

    if (this._accessLogProcessor !== undefined) {
      await this.upload();
    }
  };

  /**
   * Cancel the regular timers which persist logs to storage and run uploads.
   * This won't cancel any in-flight requests or discard any existing stored logs.
   * This also won't clear any stored configuration.
   */
  public readonly stop = (): void => {
    clearInterval(this._persistInterval);
    this._persistInterval = undefined;

    clearInterval(this._uploadInterval);
    this._uploadInterval = undefined;
  };

  /**
   * Provide the information required to enable the upload of access logs to the cloud.
   * If start() has already been called then this will trigger an immediate upload.
   *
   * @param hardwareId A unique identifier for the physical device we're running on.
   * @param tenantId The customer's tenant ID, if they have one, or undefined if not.
   * @param licenseInfo The customer's licensing information for Cloud Filter and Cloud Reporting.
   *  This is used to determine which ingest mechanism we will upload logs to.
   */
  public readonly configure = async (
    hardwareId: string,
    tenantId: TenantId | undefined,
    licenseInfo: LicenseResult,
  ): Promise<void> => {
    // The ingest v2 and v3 access logs processors use a storage service to remember blob storage
    //  files they created during previous uploads. This is so that they can avoid creating the
    //  files again needlessly. However, it's caused recurring upload failures in situations where
    //  it's ended up storing an expired SAS token. To prevent this error, we now only retain the
    //  filenames in memory. Some uploads will still fail due to a file already existing, but the
    //  processor will detect this and should recover gracefully.
    const inMemoryStorage = new StorageService('accessLogsInMemoryOnly', undefined);

    this._accessLogProcessor = undefined;
    switch (licenseInfo.cldrptVersion) {
      case CloudReportingVersion.v2:
        if (licenseInfo.cldrptConfig?.accessLogsBlobConfig !== undefined) {
          this._accessLogProcessor = new AccessLogV2Processor(
            licenseInfo,
            hardwareId,
            inMemoryStorage,
            this._telemetryService,
            new TemplateString(this._extensionConfig.ingestV2UrlTemplate),
          );
          console.debug('AccessLogManager - Configured for ingest v2.');
        }
        break;

      case CloudReportingVersion.v3:
        if (licenseInfo.cldrptConfig?.accessLogsBlobConfig !== undefined) {
          this._accessLogProcessor = new AccessLogV3Processor(
            licenseInfo,
            hardwareId,
            inMemoryStorage,
            this._telemetryService,
            new TemplateString(this._extensionConfig.ingestV3UrlTemplate),
            tenantId,
          );
          console.debug('AccessLogManager - Configured for ingest v3.');
        }
        break;

      case CloudReportingVersion.v4:
        if (licenseInfo.cldrptConfig?.accessLogsIngestV4Config !== undefined) {
          this._accessLogProcessor = new AccessLogV4Processor(
            licenseInfo,
            hardwareId,
            inMemoryStorage,
            this._telemetryService,
          );
          console.debug('AccessLogManager - Configured for ingest v4.');
        }
        break;
    }

    this._cloudReportingVersion = licenseInfo.cldrptVersion;

    if (this._accessLogProcessor === undefined) {
      console.warn(
        'AccessLogManager - Cloud reporting configuration is incomplete or not recognised. ' +
          'Access logs will not be uploaded.',
        licenseInfo,
      );
      return;
    }

    // If uploads are running then trigger one immediately to start clearing any backlog.
    if (this._uploadInterval !== undefined && !this._isUploadInProgress) {
      await this.upload();
    }
  };

  /**
   * Remove the configuration which enables access logs to be uploaded.
   * This won't cancel any in-flight uploads, but it will stop new uploads from being started.
   *
   * @see configure()
   */
  public readonly clearConfiguration = (): void => {
    this._accessLogProcessor = undefined;
  };

  /**
   * Clear all logs from memory and storage.
   */
  public readonly clearLogs = async (): Promise<void> => {
    this._accessLogStorage.discardAllLogs();
    await this._accessLogStorage.persist();
  };

  /**
   * Add an access log entry to the queue to be uploaded.
   *
   * @param log The access log entry to be queued for upload.
   * @param skipDeduplication Determines whether the log entry bypasses deduplication logic.
   *  Visiting a page in the browser potentially creates two related log entries: one for the
   *  initial request, and (if it wasn't blocked) one or more for subsequent content analysis when
   *  the page loads. We don't want multiple separate log entries when that happens as it could be
   *  confusing. This argument should be true if the log was generated by analysing the URL of
   *  an outbound request. It will always be added to the queue as a new entry in that case. This
   *  argument should be false if the log was generated by content analysis. It may be merged into
   *  recent matching log entries in that case. If no matches are found, it will be added as a new
   *  entry. Note that content analysis logs may contain more information than URL analysis alone,
   *  which is why potential duplicates are merged rather than discarded.
   */
  public readonly storeAccessLog = (log: AccessLogEntry, skipDeduplication: boolean): void => {
    this._accessLogStorage.addLog(log, !skipDeduplication);
  };

  /**
   * Flush any pending logs to the cloud. Resets the regular upload timer to start again.
   */
  public readonly flush = async (): Promise<void> => {
    this.stop();
    try {
      await this.upload();
    } finally {
      await this.start();
    }
  };

  /**
   * Upload a batch of logs the cloud.
   * This is called regularly on a timer.
   */
  public readonly upload = async (): Promise<void> => {
    // Sanity-check: We can't upload logs if we haven't been initialised.
    if (this._accessLogProcessor === undefined) {
      return;
    }

    // If there's already an upload in progress then do not start another. That could result in the
    //  same pending logs being uploaded twice.
    if (this._isUploadInProgress) {
      console.debug('AccessLogManager - Skipping upload as another upload is already in progress.');
      return;
    }

    // If the rate limit has been exceeded then do not start another upload.
    if (!this._rateLimiter.tryConsume()) {
      console.debug('AccessLogManager - Skipping upload as the rate limit has been exceeded.');
      return;
    }

    try {
      this._isUploadInProgress = true;

      const buffers = this._accessLogStorage.prepareUpload(1);
      if (buffers.size === 0) {
        // Nothing to upload.
        return;
      }

      // The processor expects a flat array of stringified log entries.
      const flattenedLogs = [...buffers.values()].flat().map((log) => JSON.stringify(log));
      let logsToSend: string[] = flattenedLogs;

      // Ingest v2 and v3 can upload a maximum of 4 MB to a single blob storage block.
      // Split out a 3MB chunk from the buffer and upload that. Any logs over the limit will be uploaded in the next attempt.
      if (
        this._cloudReportingVersion === CloudReportingVersion.v2 ||
        this._cloudReportingVersion === CloudReportingVersion.v3
      ) {
        logsToSend = this._getChunkOfLogs(flattenedLogs);
      }

      if (logsToSend.length === 0) {
        return;
      }

      console.debug(`AccessLogManager - Uploading ${logsToSend.length} log(s)...`);
      const sentLogs = await this._accessLogProcessor.processLogsForUpload(logsToSend);
      if (sentLogs.length === 0) {
        console.debug('AccessLogManager - Log upload failed.');
        return;
      }

      this._lastUploadedAt = Math.floor(Date.now() / 1000);
      this._uploadCount += sentLogs.length;
      console.debug(`AccessLogManager - Uploaded ${sentLogs.length} log(s).`);

      if (sentLogs.length === flattenedLogs.length) {
        // It looks like all the log entries were sent successfully. This means we can discard the
        //  entire buffers from memory and storage, which is very efficient. Ingest v4 typically
        //  succeeds or fails atomically.
        this._accessLogStorage.discardBuffers([...buffers.keys()]);
      } else {
        // Not all log entries were sent successfully. We'll have to remove the sent logs from each
        //  buffer individually, which can be relatively slow. This can often happen with ingest v2
        //  or v3 where different entries were divided into different blob blocks.
        this._accessLogStorage.discardLogs(sentLogs.map((log) => JSON.parse(log)));
      }

      // Write the changed log buffers to storage. This ensures the sent logs don't get re-loaded
      //  and sent again next time the extension starts.
      await this._accessLogStorage.persist();
    } finally {
      this._isUploadInProgress = false;
    }
  };

  /**
   * Splits out a chunk of logs from the given string of logs that can be uploaded to blob storage.
   */
  protected readonly _getChunkOfLogs = (buffer: string[]): string[] => {
    const bucket: string[] = [];
    let bucketSize = 0;

    for (const log of buffer) {
      bucket.push(log);
      bucketSize += new TextEncoder().encode(log).length;
      if (bucketSize > this._maxChunkSize) break;
    }

    return bucket;
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Contains hard-coded configuration, such as the format of old access log URLs.
   */
  private readonly _extensionConfig: IExtensionConfig;

  /**
   * Uploads debug logs to the cloud.
   */
  private readonly _telemetryService: ITelemetryService;

  /**
   * Stores access logs which are awaiting upload, and persists them to disk.
   */
  private readonly _accessLogStorage: AccessLogStorage;

  /**
   * How long to wait (in milliseconds) between persisting log data from memory to storage.
   */
  private readonly _persistDelay = 15000; // <-- every 15 seconds

  /**
   * We'll save any log data changes to disk each time this interval elapses.
   * The interval is initialised in start() and cleared in stop().
   */
  private _persistInterval?: ReturnType<typeof setInterval>;

  /**
   * How long to wait (in milliseconds) between uploading logs to the cloud.
   */
  private readonly _uploadDelay = 300000; // <-- every 5 minutes

  /** The upper size limit for uploading to blob storage (ingest v2&v3) */
  private readonly _maxChunkSize = 3145728;

  /** The cloud reporting version the extension is using. */
  private _cloudReportingVersion?: CloudReportingVersion;

  /**
   * We'll upload pending log entries to the cloud each time this interval elapses.
   * The interval is initialised in start() and cleared in stop().
   */
  private _uploadInterval?: ReturnType<typeof setInterval>;

  /**
   * Uploads logs to the relevant ingest mechanism.
   */
  private _accessLogProcessor?: IAccessLogProcessor;

  /*
   * The Unix timestamp (in seconds) of the last time we successfully uploaded access logs.
   * 0 means we haven't successfully uploaded logs since the extension last started.
   */
  private _lastUploadedAt: number = 0;

  /**
   * The number of logs which have been successfully uploaded.
   * This information is passed to DMS on check-in, and is reset to 0 regularly.
   */
  private _uploadCount = 0;

  /**
   * Indicates if we're waiting for an upload to finish.
   * This is used as a concurrency lock to prevent multiple uploads from happening at the same time
   *  as that would probably result in a lot of duplication on the server.
   */
  private _isUploadInProgress = false;

  private readonly _rateLimiter: RateLimiter;
}

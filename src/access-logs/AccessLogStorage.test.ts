import AccessLogEntry from 'models/AccessLogEntry';
import AccessLogStorage from './AccessLogStorage';
import { LocalStorageAreaMock, StorageAreaMock } from 'test-helpers/chrome-api';
import { makeRandomLogEntry, makeRandomLogEntries } from 'test-helpers/access-log-helpers';

const clone = <T>(data: T): T => JSON.parse(JSON.stringify(data)) as T;

describe('AccessLogStorage', () => {
  let localStorageAreaMock: LocalStorageAreaMock;

  beforeEach(() => {
    localStorageAreaMock = new LocalStorageAreaMock();
    chrome.storage.local = localStorageAreaMock;
    chrome.storage.managed = new StorageAreaMock();

    // Suppress console messages.
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'debug').mockImplementation(() => {});
  });
  // -----------------------------------------------------------------------------------------------

  describe('populate()', () => {
    it('loads the active buffer data from storage', () => {
      const accessLogStorage = new AccessLogStorage();
      const logs = makeRandomLogEntries(100);
      accessLogStorage.populate(clone({ accessLogsActive: logs }));
      expect((accessLogStorage as any)._activeBuffer).toEqual(logs);
    });

    it('does not replace the active buffer in memory if the active buffer data is not found in storage', () => {
      const accessLogStorage = new AccessLogStorage();
      const logs = makeRandomLogEntries(10);
      (accessLogStorage as any)._activeBuffer = logs;
      accessLogStorage.populate(clone({ otherRandomData: 'xyzzy', foo: 'bar' }));
      expect((accessLogStorage as any)._activeBuffer).toEqual(logs);
    });

    it('does not replace the active buffer in memory if the active buffer data in storage is invalid', () => {
      const accessLogStorage = new AccessLogStorage();
      const logs = makeRandomLogEntries(10);
      (accessLogStorage as any)._activeBuffer = logs;
      accessLogStorage.populate(clone({ accessLogsActive: 'deliberately invalid data' }));
      expect((accessLogStorage as any)._activeBuffer).toEqual(logs);
    });

    it('discards the earliest entries from the active buffer in storage if it exceeds the maximum number of entries', () => {
      const accessLogStorage = new AccessLogStorage(100, 24, 1000000);
      const logs: AccessLogEntry[] = makeRandomLogEntries(100);
      accessLogStorage.populate(clone({ accessLogsActive: logs }));
      expect((accessLogStorage as any)._activeBuffer).toEqual(logs.slice(-24));
      expect((accessLogStorage as any)._activeBufferHasChanged).toBeTrue();
    });

    it('discards the earliest entries from the active buffer in storage if it exceeds the maximum size in bytes', () => {
      const accessLogStorage = new AccessLogStorage(100, 100, 380);
      const logs: AccessLogEntry[] = [
        {
          blocked: true,
          url: 'https://www.example.com/0',
          time: '1720001789',
          groups: [],
          took: 0,
          categories: [],
        },
        {
          blocked: true,
          url: 'https://www.example.com/1',
          time: '1720001790',
          groups: [],
          took: 0,
          categories: [],
        },
        {
          blocked: true,
          url: 'https://www.example.com/2',
          time: '1720001791',
          groups: [],
          took: 0,
          categories: [],
        },
        {
          blocked: true,
          url: 'https://www.example.com/3',
          time: '1720001792',
          groups: [],
          took: 0,
          categories: [],
        },
        {
          blocked: true,
          url: 'https://www.example.com/4',
          time: '1720001793',
          groups: [],
          took: 0,
          categories: [],
        },
      ];
      accessLogStorage.populate(clone({ accessLogsActive: logs }));
      expect((accessLogStorage as any)._activeBuffer).toEqual(logs.slice(-3));
      expect((accessLogStorage as any)._activeBufferHasChanged).toBeTrue();
    });

    it('loads committed buffers from storage', () => {
      const accessLogStorage = new AccessLogStorage();
      const data = {
        accessLogs1: makeRandomLogEntries(83),
        accessLogs2: makeRandomLogEntries(78),
        accessLogs3: makeRandomLogEntries(94),
      };
      accessLogStorage.populate(clone(data));
      expect((accessLogStorage as any)._committedBuffers.get(1)).toEqual(data.accessLogs1);
      expect((accessLogStorage as any)._committedBuffers.get(2)).toEqual(data.accessLogs2);
      expect((accessLogStorage as any)._committedBuffers.get(3)).toEqual(data.accessLogs3);
    });

    it('loads the committed buffers in natural numerical order', () => {
      // A naive alphabetic sort would put 'accessLogs10' before 'accessLogs2'.
      // We should use a "natural" sort which recognises the numerical ordering.

      const accessLogStorage = new AccessLogStorage();
      const data = {
        accessLogs29: makeRandomLogEntries(5),
        accessLogs1: makeRandomLogEntries(5),
        accessLogs5: makeRandomLogEntries(5),
        accessLogs10: makeRandomLogEntries(5),
        accessLogs11: makeRandomLogEntries(5),
        accessLogs63: makeRandomLogEntries(5),
        accessLogs24: makeRandomLogEntries(5),
        accessLogs7: makeRandomLogEntries(5),
      };
      accessLogStorage.populate(clone(data));
      const loadedBufferNames = [...(accessLogStorage as any)._committedBuffers.keys()];
      expect(loadedBufferNames).toEqual([1, 5, 7, 10, 11, 24, 29, 63]);
    });

    it('retains existing committed buffers in memory', () => {
      const accessLogStorage = new AccessLogStorage();
      const oldData = {
        accessLogs1: makeRandomLogEntries(42),
        accessLogs2: makeRandomLogEntries(88),
        accessLogs3: makeRandomLogEntries(57),
      };
      accessLogStorage.populate(clone(oldData));

      const newData = {
        accessLogs4: makeRandomLogEntries(83),
        accessLogs5: makeRandomLogEntries(78),
        accessLogs6: makeRandomLogEntries(94),
      };
      accessLogStorage.populate(clone(newData));

      expect((accessLogStorage as any)._committedBuffers.get(1)).toEqual(oldData.accessLogs1);
      expect((accessLogStorage as any)._committedBuffers.get(2)).toEqual(oldData.accessLogs2);
      expect((accessLogStorage as any)._committedBuffers.get(3)).toEqual(oldData.accessLogs3);
      expect((accessLogStorage as any)._committedBuffers.get(4)).toEqual(newData.accessLogs4);
      expect((accessLogStorage as any)._committedBuffers.get(5)).toEqual(newData.accessLogs5);
      expect((accessLogStorage as any)._committedBuffers.get(6)).toEqual(newData.accessLogs6);
    });

    it('re-numbers buffers from storage if they conflict with buffers already in memory', () => {
      const accessLogStorage = new AccessLogStorage();
      const oldData = {
        accessLogs1: makeRandomLogEntries(21),
        accessLogs2: makeRandomLogEntries(13),
        accessLogs3: makeRandomLogEntries(41),
      };
      accessLogStorage.populate(clone(oldData));

      const newData = {
        accessLogs1: makeRandomLogEntries(16),
        accessLogs2: makeRandomLogEntries(22),
        accessLogs3: makeRandomLogEntries(9),
      };
      accessLogStorage.populate(clone(newData));

      expect((accessLogStorage as any)._committedBuffers.get(1)).toEqual(oldData.accessLogs1);
      expect((accessLogStorage as any)._committedBuffers.get(2)).toEqual(oldData.accessLogs2);
      expect((accessLogStorage as any)._committedBuffers.get(3)).toEqual(oldData.accessLogs3);
      expect((accessLogStorage as any)._committedBuffers.get(4)).toEqual(newData.accessLogs1);
      expect((accessLogStorage as any)._committedBuffers.get(5)).toEqual(newData.accessLogs2);
      expect((accessLogStorage as any)._committedBuffers.get(6)).toEqual(newData.accessLogs3);
    });

    it('discards empty or invalid buffers from storage', () => {
      const accessLogStorage = new AccessLogStorage();
      const data = {
        accessLogs1: [],
        accessLogs2: makeRandomLogEntries(78),
        accessLogs3: 'deliberately invalid',
      };
      accessLogStorage.populate(clone(data));

      expect((accessLogStorage as any)._committedBuffers.get(1)).toBeUndefined();
      expect((accessLogStorage as any)._committedBuffers.get(2)).toEqual(data.accessLogs2);
      expect((accessLogStorage as any)._committedBuffers.get(3)).toBeUndefined();

      // The empty/invalid buffers should have been marked for deletion.
      expect((accessLogStorage as any)._committedBufferChanges.has(1)).toBeTrue();
      expect((accessLogStorage as any)._committedBufferChanges.has(2)).toBeFalse();
      expect((accessLogStorage as any)._committedBufferChanges.has(3)).toBeTrue();
    });

    it('truncates committed buffers in storage if they exceed the maximum number of entries', () => {
      const accessLogStorage = new AccessLogStorage(100, 50, 1000000);
      const data = {
        accessLogs1: makeRandomLogEntries(88),
        accessLogs2: makeRandomLogEntries(29),
        accessLogs3: makeRandomLogEntries(53),
      };
      accessLogStorage.populate(clone(data));

      expect((accessLogStorage as any)._committedBuffers.get(1)).toEqual(
        data.accessLogs1.slice(-50),
      );
      expect((accessLogStorage as any)._committedBuffers.get(2)).toEqual(data.accessLogs2);
      expect((accessLogStorage as any)._committedBuffers.get(3)).toEqual(
        data.accessLogs3.slice(-50),
      );

      // The truncated buffers should have been flagged for storage.
      expect((accessLogStorage as any)._committedBufferChanges.has(1)).toBeTrue();
      expect((accessLogStorage as any)._committedBufferChanges.has(2)).toBeFalse();
      expect((accessLogStorage as any)._committedBufferChanges.has(3)).toBeTrue();
    });

    it('truncates committed buffers in storage if they exceed the maximum size in bytes', () => {
      const accessLogStorage = new AccessLogStorage(100, 1000, 400);
      const logs: AccessLogEntry[] = [
        {
          blocked: true,
          url: 'https://www.example.com/0',
          time: '1720001789',
          groups: [],
          took: 0,
          categories: [],
        },
        {
          blocked: true,
          url: 'https://www.example.com/1',
          time: '1720001790',
          groups: [],
          took: 0,
          categories: [],
        },
        {
          blocked: true,
          url: 'https://www.example.com/2',
          time: '1720001791',
          groups: [],
          took: 0,
          categories: [],
        },
        {
          blocked: true,
          url: 'https://www.example.com/3',
          time: '1720001792',
          groups: [],
          took: 0,
          categories: [],
        },
        {
          blocked: true,
          url: 'https://www.example.com/4',
          time: '1720001793',
          groups: [],
          took: 0,
          categories: [],
        },
      ];
      accessLogStorage.populate(clone({ accessLogs1: logs }));

      expect((accessLogStorage as any)._committedBuffers.get(1)).toEqual(logs.slice(-3));
      expect((accessLogStorage as any)._committedBufferChanges.has(1)).toBeTrue();
    });

    it('discards the lowest numbered committed buffers from storage if the number of buffers exceeds the current limit', () => {
      const accessLogStorage = new AccessLogStorage(3, 1000, 1000000);
      const data = {
        accessLogs1: makeRandomLogEntries(83),
        accessLogs2: makeRandomLogEntries(78),
        accessLogs3: makeRandomLogEntries(94),
        accessLogs4: makeRandomLogEntries(29),
        accessLogs5: makeRandomLogEntries(64),
      };
      accessLogStorage.populate(clone(data));
      expect((accessLogStorage as any)._committedBuffers.size).toEqual(3);
      expect((accessLogStorage as any)._committedBuffers.get(1)).toBeUndefined();
      expect((accessLogStorage as any)._committedBuffers.get(2)).toBeUndefined();
      expect((accessLogStorage as any)._committedBuffers.get(3)).toEqual(data.accessLogs3);
      expect((accessLogStorage as any)._committedBuffers.get(4)).toEqual(data.accessLogs4);
      expect((accessLogStorage as any)._committedBuffers.get(5)).toEqual(data.accessLogs5);
    });

    it('does not overwrite legacy logs if they have already been populated', async () => {
      const legacyLogs = makeRandomLogEntries(2);
      const legacyData = {
        accessLogs: {
          accessLogQueue: legacyLogs.map((l) => JSON.stringify(l)),
        },
      };

      const newData = {
        // The legacy logs will have ended up in buffer 0, so this will be re-numbered as 1.
        accessLogs0: makeRandomLogEntries(2),
      };

      const accessLogStorage = new AccessLogStorage();
      await accessLogStorage.populateLegacy(clone(legacyData), 1000000);
      accessLogStorage.populate(clone(newData));

      const upload = accessLogStorage.prepareUpload(10);
      expect(upload.get(0)).toEqual(legacyLogs);
      expect(upload.get(1)).toEqual(newData.accessLogs0);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('populateLegacy()', () => {
    it('loads legacy logs into committed buffers', async () => {
      const accessLogs = makeRandomLogEntries(5);
      const dedupLogs = makeRandomLogEntries(5);
      const data = {
        accessLogs: {
          accessLogQueue: accessLogs.map((log) => JSON.stringify(log)),
          dedupAccessLogQueue: dedupLogs.map((log) => JSON.stringify(log)),
        },
      };

      const accessLogStorage = new AccessLogStorage();
      await accessLogStorage.populateLegacy(data, 1000000);

      const buffer = accessLogStorage.prepareUpload(10).get(0);
      expect(buffer).toHaveLength(10);
      accessLogs.forEach((log) => {
        expect(buffer).toContainEqual(log);
      });
      dedupLogs.forEach((log) => {
        expect(buffer).toContainEqual(log);
      });
    });

    it('divides the legacy logs into multiple committed buffers if necessary', async () => {
      const input = makeRandomLogEntries(10);
      const data = {
        accessLogs: {
          accessLogQueue: input.map((log) => JSON.stringify(log)),
        },
      };

      const accessLogStorage = new AccessLogStorage(100, 4, 1000000);
      await accessLogStorage.populateLegacy(data, 1000000);

      // Get everything that's pending upload. It should be split into buffers.
      const buffers = accessLogStorage.prepareUpload(10);
      expect(buffers.size).toEqual(3);

      // Recombine all the buffers to make sure everything is there.
      const output = [
        ...(buffers.get(0) ?? []),
        ...(buffers.get(1) ?? []),
        ...(buffers.get(2) ?? []),
      ];
      expect(output).toHaveLength(input.length);
      expect(output).toEqual(expect.arrayContaining(input));
    });

    it('discards log entries with a timestamp before the specified threshold', async () => {
      const nowInSeconds = Math.floor(Date.now() / 1000);
      const secondsInADay = 86400;
      const twoDaysAgo = nowInSeconds - secondsInADay * 2;
      const fourDaysAgo = nowInSeconds - secondsInADay * 4;

      const input = makeRandomLogEntries(10);
      // Adjust the log timestamps so that the first 5 will be discarded, and the rest will be kept.
      input.forEach((log, index) => {
        log.time = index < 5 ? fourDaysAgo.toString() : twoDaysAgo.toString();
      });

      const data = {
        accessLogs: {
          accessLogQueue: input.map((log) => JSON.stringify(log)),
        },
      };

      const accessLogStorage = new AccessLogStorage();
      await accessLogStorage.populateLegacy(data, 3);
      const output = accessLogStorage.prepareUpload(1).get(0);
      expect(output).toHaveLength(5);
    });

    it('keeps the most recent log entries if there are too many', async () => {
      const input = makeRandomLogEntries(10);
      const data = {
        accessLogs: {
          accessLogQueue: input.map((log) => JSON.stringify(log)),
        },
      };

      const accessLogStorage = new AccessLogStorage(1, 7, 1000000);
      await accessLogStorage.populateLegacy(data, 1000000);
      const output = accessLogStorage.prepareUpload(1).get(0);
      expect(output).toEqual(input.slice(-7));
    });

    it('does not modify the active buffer in memory', async () => {
      const accessLogStorage = new AccessLogStorage();
      const activeLogs = makeRandomLogEntries(12);
      accessLogStorage.populate(clone({ accessLogsActive: activeLogs }));

      const legacyLogs = makeRandomLogEntries(10);
      const data = {
        accessLogs: {
          accessLogQueue: legacyLogs.map((log) => JSON.stringify(log)),
        },
      };
      await accessLogStorage.populateLegacy(data, 1000000);
      expect((accessLogStorage as any)._activeBuffer).toEqual(activeLogs);
    });

    it('does not modify existing committed buffers in memory', async () => {
      const accessLogStorage = new AccessLogStorage();
      const committedLogs = makeRandomLogEntries(12);
      accessLogStorage.populate(clone({ accessLogs1: committedLogs }));

      const legacyLogs = makeRandomLogEntries(10);
      const data = {
        accessLogs: {
          accessLogQueue: legacyLogs.map((log) => JSON.stringify(log)),
        },
      };
      await accessLogStorage.populateLegacy(data, 1000000);
      expect((accessLogStorage as any)._committedBuffers.get(1)).toEqual(committedLogs);
    });

    it('does not load anything if the committed buffers in memory are already at maximum capacity', async () => {
      const accessLogStorage = new AccessLogStorage(4, 25, 1000000);
      const oldData = {
        accessLogs1: makeRandomLogEntries(25),
        accessLogs2: makeRandomLogEntries(25),
        accessLogs3: makeRandomLogEntries(25),
        accessLogs4: makeRandomLogEntries(25),
      };
      accessLogStorage.populate(clone(oldData));

      const legacyLogs = makeRandomLogEntries(10);
      const data = {
        accessLogs: {
          accessLogQueue: legacyLogs.map((log) => JSON.stringify(log)),
        },
      };
      await accessLogStorage.populateLegacy(data, 1000000);
      expect((accessLogStorage as any)._committedBuffers.size).toEqual(4);
      expect((accessLogStorage as any)._committedBuffers.get(1)).toEqual(oldData.accessLogs1);
      expect((accessLogStorage as any)._committedBuffers.get(2)).toEqual(oldData.accessLogs2);
      expect((accessLogStorage as any)._committedBuffers.get(3)).toEqual(oldData.accessLogs3);
      expect((accessLogStorage as any)._committedBuffers.get(4)).toEqual(oldData.accessLogs4);
    });

    it('deletes the legacy access log data from storage', async () => {
      const accessLogStorage = new AccessLogStorage();
      const data = {
        accessLogs: {
          accessLogQueue: makeRandomLogEntries(10).map((log) => JSON.stringify(log)),
          dedupAccessLogQueue: makeRandomLogEntries(10).map((log) => JSON.stringify(log)),
        },
      };
      await accessLogStorage.populateLegacy(data, 1000000);
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith('accessLogs');
    });

    it('persists the new buffers back to storage', async () => {
      const accessLogStorage = new AccessLogStorage(100, 3, 1000000);
      const data = {
        accessLogs: {
          accessLogQueue: makeRandomLogEntries(15).map((log) => JSON.stringify(log)),
          dedupAccessLogQueue: makeRandomLogEntries(15).map((log) => JSON.stringify(log)),
        },
      };
      await accessLogStorage.populateLegacy(data, 1000000);
      expect(localStorageAreaMock.set).toHaveBeenCalledWith(
        expect.objectContaining({
          accessLogs0: expect.any(Array),
          accessLogs1: expect.any(Array),
          accessLogs2: expect.any(Array),
        }),
      );
    });

    it('does not overwrite non-legacy logs if they have already been populated', async () => {
      const newData = {
        accessLogs0: makeRandomLogEntries(3),
      };

      const legacyLogs = makeRandomLogEntries(5);
      const legacyData = {
        accessLogs: {
          accessLogQueue: legacyLogs.map((l) => JSON.stringify(l)),
        },
      };

      const accessLogStorage = new AccessLogStorage();
      accessLogStorage.populate(clone(newData));
      await accessLogStorage.populateLegacy(clone(legacyData), 1000000);

      const upload = accessLogStorage.prepareUpload(10);
      expect(upload.get(0)).toEqual(newData.accessLogs0);
      expect(upload.get(1)).toEqual(legacyLogs);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('persist()', () => {
    it('does nothing if no buffers have changed', async () => {
      const accessLogStorage = new AccessLogStorage();
      await accessLogStorage.persist();
      expect(localStorageAreaMock.remove).not.toHaveBeenCalled();
      expect(localStorageAreaMock.set).not.toHaveBeenCalled();
    });

    it('saves the active buffer if it has changed', async () => {
      const accessLogStorage = new AccessLogStorage();
      accessLogStorage.addLog(makeRandomLogEntry(), false);
      await accessLogStorage.persist();
      expect(localStorageAreaMock.set).toHaveBeenCalledWith(
        expect.objectContaining({
          accessLogsActive: expect.anything(),
        }),
      );
    });

    it('saves committed buffers if they have been modified', async () => {
      const accessLogStorage = new AccessLogStorage();
      accessLogStorage.addLog(makeRandomLogEntry(), false);
      accessLogStorage.prepareUpload(1);
      await accessLogStorage.persist();
      expect(localStorageAreaMock.set).toHaveBeenCalledWith(
        expect.objectContaining({
          accessLogs0: expect.anything(),
        }),
      );
    });

    it('removes committed buffers if they have been discarded', async () => {
      const accessLogStorage = new AccessLogStorage();
      accessLogStorage.populate(clone({ accessLogs0: makeRandomLogEntries(10) }));
      accessLogStorage.discardBuffers([0]);
      await accessLogStorage.persist();
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith(['accessLogs0']);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('addLog()', () => {
    it('ignores the log entry if it is bigger than the maximum buffer size in bytes', () => {
      const log: AccessLogEntry = {
        blocked: true,
        url: 'https://www.example.com?foo=1234567890',
        time: '1720012644',
        took: 1000,
        groups: ['test-group-1', 'test-group-2', 'test-group-3', 'test-group-4', 'test-group-5'],
        categories: ['111', '222', '333', '444', '555', '666', '777', '888', '999', '000'],
      };
      const accessLogStorage = new AccessLogStorage(100, 1000, 100);
      accessLogStorage.addLog(log, false);
      const buffers = accessLogStorage.prepareUpload(1);
      expect(buffers.size).toEqual(0);
    });

    it('ignores the log entry if it does not have a valid timestamp', () => {
      const log: AccessLogEntry = {
        blocked: true,
        url: 'https://www.example.com',
        time: '',
        took: 1000,
        groups: [],
        categories: [],
      };
      const accessLogStorage = new AccessLogStorage();
      accessLogStorage.addLog(log, false);
      const buffers = accessLogStorage.prepareUpload(1);
      expect(buffers.size).toEqual(0);
    });

    it('appends the log entry to the active buffer and flags it for storage', () => {
      const log = makeRandomLogEntry();
      const accessLogStorage = new AccessLogStorage();
      accessLogStorage.addLog(log, false);
      expect((accessLogStorage as any)._activeBuffer).toContainEqual(log);
      expect((accessLogStorage as any)._activeBufferHasChanged).toBeTrue();
    });

    it('combines the log entry with existing matching entry if it matches and applyDeduplication is true', () => {
      // Only recent allowed logs can be deduplicated.
      const log1 = makeRandomLogEntry();
      log1.time = (Math.floor(Date.now() / 1000) - 10).toString();
      log1.blocked = false;
      log1.actions = ['allow'];
      log1.contenttype = 'main_frame';

      const log2 = clone(log1);
      log2.categories = ['111', '222', '333'];

      const accessLogStorage = new AccessLogStorage();
      accessLogStorage.addLog(log1, false);
      accessLogStorage.addLog(log2, true);

      expect((accessLogStorage as any)._activeBuffer).toHaveLength(1);
      expect((accessLogStorage as any)._activeBuffer[0].categories).toContain(log1.categories[0]);
      expect((accessLogStorage as any)._activeBuffer[0].categories).toContain(log2.categories[0]);
    });

    it('does not combine log entries if applyDeduplication is false', () => {
      // Only recent allowed logs can be deduplicated.
      const log1 = makeRandomLogEntry();
      log1.time = (Math.floor(Date.now() / 1000) - 10).toString();
      log1.blocked = false;
      log1.actions = ['allow'];
      log1.contenttype = 'main_frame';

      const log2 = clone(log1);
      log2.categories = ['111', '222', '333'];

      const accessLogStorage = new AccessLogStorage();
      accessLogStorage.addLog(log1, false);
      accessLogStorage.addLog(log2, false);

      expect((accessLogStorage as any)._activeBuffer).toHaveLength(2);
      expect((accessLogStorage as any)._activeBuffer).toContain(log1);
      expect((accessLogStorage as any)._activeBuffer).toContain(log2);
    });

    it('commits the active buffer if it is full', () => {
      const accessLogStorage = new AccessLogStorage(10, 5, 1000000);
      for (let i = 0; i < 6; ++i) {
        accessLogStorage.addLog(makeRandomLogEntry(), false);
      }

      expect((accessLogStorage as any)._committedBuffers.size).toEqual(1);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('prepareUpload()', () => {
    it('throws an error if the maximum number of buffers is less than 1', () => {
      const accessLogStorage = new AccessLogStorage();
      expect(() => {
        accessLogStorage.prepareUpload(0);
      }).toThrow();
    });

    it('returns an empty map if no logs are waiting for upload', () => {
      const accessLogStorage = new AccessLogStorage();
      expect(accessLogStorage.prepareUpload(10).size).toEqual(0);
    });

    it('returns log buffers which are waiting for upload', () => {
      const accessLogStorage = new AccessLogStorage();
      const data = {
        accessLogs1: makeRandomLogEntries(24),
        accessLogs2: makeRandomLogEntries(41),
        accessLogs3: makeRandomLogEntries(11),
      };
      accessLogStorage.populate(clone(data));
      const upload = accessLogStorage.prepareUpload(5);
      expect(upload.size).toEqual(3);
      expect(upload.get(1)).toEqual(data.accessLogs1);
      expect(upload.get(2)).toEqual(data.accessLogs2);
      expect(upload.get(3)).toEqual(data.accessLogs3);
    });

    it('returns the first N committed buffers where N is the maximum specified in the argument', () => {
      const accessLogStorage = new AccessLogStorage();
      const data = {
        accessLogs1: makeRandomLogEntries(24),
        accessLogs2: makeRandomLogEntries(41),
        accessLogs3: makeRandomLogEntries(11),
        accessLogs4: makeRandomLogEntries(64),
        accessLogs5: makeRandomLogEntries(33),
      };
      accessLogStorage.populate(clone(data));
      const upload = accessLogStorage.prepareUpload(3);
      expect(upload.size).toEqual(3);
      expect(upload.get(1)).toEqual(data.accessLogs1);
      expect(upload.get(2)).toEqual(data.accessLogs2);
      expect(upload.get(3)).toEqual(data.accessLogs3);
    });

    it('includes logs from the active buffer if they have passed the deduplication age and there is room in the upload', () => {
      const accessLogsActive = makeRandomLogEntries(5);
      // Make sure some of the active logs are under the deduplicate time and some are over.
      const deduplicatedTimestamp = Math.floor(Date.now() / 1000) - 50000;
      const recentTimestamp = Math.floor(Date.now() / 1000) - 1;
      accessLogsActive[0].time = deduplicatedTimestamp.toString();
      accessLogsActive[1].time = deduplicatedTimestamp.toString();
      accessLogsActive[2].time = deduplicatedTimestamp.toString();
      accessLogsActive[3].time = recentTimestamp.toString();
      accessLogsActive[4].time = recentTimestamp.toString();

      const accessLogStorage = new AccessLogStorage();
      const data = {
        accessLogs1: makeRandomLogEntries(24),
        accessLogsActive,
      };
      accessLogStorage.populate(clone(data));
      const logsForUpload = [...accessLogStorage.prepareUpload(5).values()].flat();
      expect(logsForUpload).toContainEqual(accessLogsActive[0]);
      expect(logsForUpload).toContainEqual(accessLogsActive[1]);
      expect(logsForUpload).toContainEqual(accessLogsActive[2]);

      expect(logsForUpload).not.toContainEqual(accessLogsActive[3]);
      expect(logsForUpload).not.toContainEqual(accessLogsActive[4]);
    });

    it('does not commit the active buffer if there is not room in the upload', () => {
      const accessLogStorage = new AccessLogStorage();
      const data = {
        accessLogsActive: makeRandomLogEntries(15),
        accessLogs1: makeRandomLogEntries(24),
        accessLogs2: makeRandomLogEntries(41),
        accessLogs3: makeRandomLogEntries(11),
        accessLogs4: makeRandomLogEntries(64),
        accessLogs5: makeRandomLogEntries(33),
      };
      accessLogStorage.populate(clone(data));
      const upload = accessLogStorage.prepareUpload(3);
      expect(upload.size).toEqual(3);
      expect(upload.get(1)).toEqual(data.accessLogs1);
      expect(upload.get(2)).toEqual(data.accessLogs2);
      expect(upload.get(3)).toEqual(data.accessLogs3);
      expect((accessLogStorage as any)._activeBuffer).toEqual(data.accessLogsActive);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('discardBuffers()', () => {
    it('deletes the specified committed buffers from memory and flags them for storage', async () => {
      const accessLogStorage = new AccessLogStorage();
      const data = {
        accessLogs1: makeRandomLogEntries(24),
        accessLogs2: makeRandomLogEntries(41),
        accessLogs3: makeRandomLogEntries(11),
        accessLogs4: makeRandomLogEntries(64),
        accessLogs5: makeRandomLogEntries(33),
      };
      accessLogStorage.populate(clone(data));
      accessLogStorage.discardBuffers([1, 2]);
      const upload = accessLogStorage.prepareUpload(10);
      expect(upload.size).toEqual(3);
      expect(upload.get(3)).toEqual(data.accessLogs3);
      expect(upload.get(4)).toEqual(data.accessLogs4);
      expect(upload.get(5)).toEqual(data.accessLogs5);

      await accessLogStorage.persist();
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith(['accessLogs1', 'accessLogs2']);
    });

    it('does not fall over if any of the specified buffers do not exist', async () => {
      const accessLogStorage = new AccessLogStorage();
      const data = {
        accessLogs1: makeRandomLogEntries(24),
        accessLogs2: makeRandomLogEntries(41),
      };
      accessLogStorage.populate(clone(data));
      accessLogStorage.discardBuffers([1, 58]);
      const upload = accessLogStorage.prepareUpload(10);
      expect(upload.size).toEqual(1);
      expect(upload.get(2)).toEqual(data.accessLogs2);

      await accessLogStorage.persist();
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith(['accessLogs1', 'accessLogs58']);
    });

    it('does not modify the active buffer', () => {
      const accessLogStorage = new AccessLogStorage();
      const data = {
        accessLogsActive: makeRandomLogEntries(10),
        accessLogs1: makeRandomLogEntries(24),
        accessLogs2: makeRandomLogEntries(41),
      };
      accessLogStorage.populate(clone(data));
      accessLogStorage.discardBuffers([2]);
      expect((accessLogStorage as any)._activeBuffer).toEqual(data.accessLogsActive);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('discardLogs()', () => {
    it('deletes matching log entries from any committed buffers', () => {
      const accessLogStorage = new AccessLogStorage();
      const data = {
        accessLogs1: makeRandomLogEntries(24),
        accessLogs2: makeRandomLogEntries(41),
        accessLogs3: makeRandomLogEntries(12),
      };
      // We need to clone the data as the buffers will get modified.
      accessLogStorage.populate(clone(data));
      // Discard some of the logs from some of the buffers.
      const logsToDiscard = [...data.accessLogs1.slice(0, 19), ...data.accessLogs3.slice(0, 5)];
      accessLogStorage.discardLogs(logsToDiscard);

      const upload = accessLogStorage.prepareUpload(10);
      expect(upload.get(1)).toEqual(data.accessLogs1.slice(19));
      expect(upload.get(2)).toEqual(data.accessLogs2);
      expect(upload.get(3)).toEqual(data.accessLogs3.slice(5));
    });

    it('flags modified and empty buffers for storage', async () => {
      const accessLogStorage = new AccessLogStorage();
      const data = {
        accessLogs1: makeRandomLogEntries(24),
        accessLogs2: makeRandomLogEntries(41),
        accessLogs3: makeRandomLogEntries(12),
      };
      accessLogStorage.populate(clone(data));
      // Discard some logs from buffer 2 and all logs from buffer 3.
      const logsToDiscard = [...data.accessLogs2.slice(0, 10), ...data.accessLogs3];
      accessLogStorage.discardLogs(logsToDiscard);

      await accessLogStorage.persist();
      expect(localStorageAreaMock.set).toHaveBeenCalledWith(
        expect.objectContaining({
          accessLogs2: expect.any(Array),
        }),
      );
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith(['accessLogs3']);
    });

    it('does not modify the active buffer even if the logs match', () => {
      const accessLogStorage = new AccessLogStorage();
      const data = {
        accessLogsActive: makeRandomLogEntries(15),
        accessLogs1: makeRandomLogEntries(24),
        accessLogs2: makeRandomLogEntries(41),
        accessLogs3: makeRandomLogEntries(12),
      };
      // We need to clone the data as the buffers will get modified.
      accessLogStorage.populate(clone(data));
      // Try to discard the logs in the active buffer. This should do nothing.
      accessLogStorage.discardLogs(data.accessLogsActive);
      // Preparing an upload should now commit and return the active buffer logs.
      const upload = accessLogStorage.prepareUpload(10);
      expect(upload.get(4)).toEqual(data.accessLogsActive);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('discardEmptyBuffers()', () => {
    it('deletes committed buffers from memory if they do not contain any entries', () => {
      const accessLogStorage = new AccessLogStorage();
      const data = {
        accessLogs1: makeRandomLogEntries(21),
        accessLogs3: makeRandomLogEntries(46),
      };
      accessLogStorage.populate(clone(data));
      // We have to insert an empty buffer manually.
      (accessLogStorage as any)._committedBuffers.set(2, []);
      accessLogStorage.discardEmptyBuffers();
      expect((accessLogStorage as any)._committedBuffers.has(1)).toBeTrue();
      expect((accessLogStorage as any)._committedBuffers.has(2)).toBeFalse();
      expect((accessLogStorage as any)._committedBuffers.has(3)).toBeTrue();
    });

    it('flags deleted buffers to be removed from storage', async () => {
      const accessLogStorage = new AccessLogStorage();
      const data = {
        accessLogs1: makeRandomLogEntries(21),
        accessLogs3: makeRandomLogEntries(46),
      };
      accessLogStorage.populate(clone(data));
      // We have to insert an empty buffer manually.
      (accessLogStorage as any)._committedBuffers.set(2, []);
      accessLogStorage.discardEmptyBuffers();
      await accessLogStorage.persist();
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith(['accessLogs2']);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('discardOldLogs()', () => {
    it('deletes all log entries older than the specified number of days', () => {
      const nowInSeconds = Math.floor(Date.now() / 1000);
      const secondsInADay = 86400;
      const twoDaysAgo = nowInSeconds - secondsInADay * 2;
      const fourDaysAgo = nowInSeconds - secondsInADay * 4;

      // This set of logs won't be modified.
      const inputLogs1: AccessLogEntry[] = [
        {
          blocked: false,
          url: 'https://www.example.com/1',
          time: twoDaysAgo.toString(), // <-- will be kept
          groups: [],
          categories: [],
          took: 1000,
        },
        {
          blocked: false,
          url: 'https://www.example.com/2',
          time: twoDaysAgo.toString(), // <-- will be kept
          groups: [],
          categories: [],
          took: 1000,
        },
      ];

      // We'll use the same set of logs in a committed buffer and the active buffer. Both instances
      //  should have some entries discarded.
      const inputLogs2: AccessLogEntry[] = [
        {
          blocked: false,
          url: 'https://www.example.com/3',
          time: fourDaysAgo.toString(), // <-- will be discarded
          groups: [],
          categories: [],
          took: 1000,
        },
        {
          blocked: false,
          url: 'https://www.example.com/4',
          time: twoDaysAgo.toString(), // <-- will be kept
          groups: [],
          categories: [],
          took: 1000,
        },
        {
          blocked: false,
          url: 'https://www.example.com/5',
          time: twoDaysAgo.toString(), // <-- will be kept
          groups: [],
          categories: [],
          took: 1000,
        },
        {
          blocked: false,
          url: 'https://www.example.com/6',
          time: fourDaysAgo.toString(), // <-- will be discarded
          groups: [],
          categories: [],
          took: 1000,
        },
      ];

      const expectedOutputLogs2: AccessLogEntry[] = [
        {
          blocked: false,
          url: 'https://www.example.com/4',
          time: twoDaysAgo.toString(),
          groups: [],
          categories: [],
          took: 1000,
        },
        {
          blocked: false,
          url: 'https://www.example.com/5',
          time: twoDaysAgo.toString(),
          groups: [],
          categories: [],
          took: 1000,
        },
      ];

      const data = {
        accessLogs1: inputLogs1,
        accessLogs2: inputLogs2,
        accessLogsActive: inputLogs2,
      };

      const accessLogStorage = new AccessLogStorage();
      accessLogStorage.populate(clone(data));
      accessLogStorage.discardOldLogs(3);

      const upload = accessLogStorage.prepareUpload(10);
      expect(upload.get(1)).toEqual(inputLogs1); // <-- no logs discarded
      expect(upload.get(2)).toEqual(expectedOutputLogs2);
      expect(upload.get(3)).toEqual(expectedOutputLogs2); // <-- active log will have been committed
    });

    it('flags modified buffers for storage', async () => {
      const nowInSeconds = Math.floor(Date.now() / 1000);
      const secondsInADay = 86400;
      const twoDaysAgo = nowInSeconds - secondsInADay * 2;
      const fourDaysAgo = nowInSeconds - secondsInADay * 4;

      // This buffer will be modified:
      const inputLogs1: AccessLogEntry[] = [
        {
          blocked: false,
          url: 'https://www.example.com/1',
          time: twoDaysAgo.toString(), // <-- will be kept
          groups: [],
          categories: [],
          took: 1000,
        },
        {
          blocked: false,
          url: 'https://www.example.com/2',
          time: twoDaysAgo.toString(), // <-- will be kept
          groups: [],
          categories: [],
          took: 1000,
        },
        {
          blocked: false,
          url: 'https://www.example.com/3',
          time: fourDaysAgo.toString(), // <-- will be discarded
          groups: [],
          categories: [],
          took: 1000,
        },
      ];

      // This buffer will be deleted entirely:
      const inputLogs2: AccessLogEntry[] = [
        {
          blocked: false,
          url: 'https://www.example.com/4',
          time: fourDaysAgo.toString(), // <-- will be discarded
          groups: [],
          categories: [],
          took: 1000,
        },
        {
          blocked: false,
          url: 'https://www.example.com/5',
          time: fourDaysAgo.toString(), // <-- will be discarded
          groups: [],
          categories: [],
          took: 1000,
        },
        {
          blocked: false,
          url: 'https://www.example.com/6',
          time: fourDaysAgo.toString(), // <-- will be discarded
          groups: [],
          categories: [],
          took: 1000,
        },
      ];

      const data = {
        accessLogs1: inputLogs1,
        accessLogs2: inputLogs2,
      };

      const accessLogStorage = new AccessLogStorage();
      accessLogStorage.populate(clone(data));
      accessLogStorage.discardOldLogs(3);

      await accessLogStorage.persist();
      expect(localStorageAreaMock.set).toHaveBeenCalledWith(
        expect.objectContaining({
          accessLogs1: expect.any(Array),
        }),
      );
      expect(localStorageAreaMock.remove).toHaveBeenCalledWith(['accessLogs2']);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('discardAllLogs()', () => {
    it('does nothing if there are no logs to discard', () => {
      const accessLogStorage = new AccessLogStorage();
      expect(() => {
        accessLogStorage.discardAllLogs();
      }).not.toThrow();
    });

    it('deletes all logs entries from the committed and active buffers', () => {
      const accessLogStorage = new AccessLogStorage();
      accessLogStorage.populate({
        accessLogs1: makeRandomLogEntries(10),
        accessLogs2: makeRandomLogEntries(10),
        accessLogs3: makeRandomLogEntries(10),
        accessLogsActive: makeRandomLogEntries(10),
      });
      accessLogStorage.discardAllLogs();
      expect((accessLogStorage as any)._activeBuffer.length).toEqual(0);
      expect((accessLogStorage as any)._committedBuffers.size).toEqual(0);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('findNextBufferNumber()', () => {
    it('returns 0 if there are no committed buffers', () => {
      const accessLogStorage = new AccessLogStorage();
      expect(accessLogStorage.findNextBufferNumber()).toEqual(0);
    });

    it('returns the next integer above the highest committed buffer number', () => {
      const accessLogStorage = new AccessLogStorage();
      accessLogStorage.populate({
        accessLogs47: makeRandomLogEntries(10),
        accessLogs195: makeRandomLogEntries(10),
        accessLogs552: makeRandomLogEntries(10),
      });
      expect(accessLogStorage.findNextBufferNumber()).toEqual(553);
    });

    it('returns 0 if the highest committed buffer number is greater than or equal to the maximum safe integer', () => {
      const accessLogStorage = new AccessLogStorage();
      accessLogStorage.populate({
        [`accessLogs${Number.MAX_SAFE_INTEGER}`]: makeRandomLogEntries(10),
      });
      expect(accessLogStorage.findNextBufferNumber()).toEqual(0);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('makeBufferName()', () => {
    it('returns the specified number appended to the buffer name prefix', () => {
      expect(AccessLogStorage.makeBufferName(321)).toEqual('accessLogs321');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('parseBufferName()', () => {
    it('returns the buffer number parsed from the buffer name if it is valid', () => {
      expect(AccessLogStorage.parseBufferName('accessLogs123')).toEqual(123);
    });

    it('returns undefined if the buffer name does not match the expected prefix', () => {
      expect(AccessLogStorage.parseBufferName('other-data-123')).toBeUndefined();
    });

    it('returns undefined if the buffer name does not contain a valid integer', () => {
      expect(AccessLogStorage.parseBufferName('accessLogsfoobar')).toBeUndefined();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('calculateLogSizeInBytes()', () => {
    it('returns the size of the JSON representation plus 1', () => {
      const log: AccessLogEntry = {
        blocked: true,
        url: 'https://www.example.com',
        time: '1720012644',
        took: 1000,
        groups: [],
        categories: [],
      };
      expect(AccessLogStorage.calculateLogSizeInBytes(log)).toEqual(109);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('calculateBufferSizeInBytes()', () => {
    it('returns the size of the JSON representation plus 1 per entry', () => {
      const buffer: AccessLogEntry[] = [
        {
          blocked: true,
          url: 'https://www.example.com/1',
          time: '1720012644',
          took: 1000,
          groups: [],
          categories: [],
        },
        {
          blocked: true,
          url: 'https://www.example.com/2',
          time: '1720012654',
          took: 1001,
          groups: [],
          categories: [],
        },
        {
          blocked: true,
          url: 'https://www.example.com/3',
          time: '1720012664',
          took: 1002,
          groups: [],
          categories: [],
        },
      ];
      expect(AccessLogStorage.calculateBufferSizeInBytes(buffer)).toEqual(337);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('activeBufferHasCapacity()', () => {
    it('returns true if the active buffer has capacity for the specified log entry', () => {
      const accessLogStorage = new AccessLogStorage(100, 30, 1000);
      expect(accessLogStorage.activeBufferHasCapacity(120)).toBeTrue();
    });

    it('returns false if the active buffer already contains or exceeds the maximum number of items per buffer', () => {
      const log: AccessLogEntry = {
        blocked: false,
        url: 'https://www.example.com',
        time: '1719936458',
        categories: ['123', '456'],
        groups: ['foo', 'bar'],
        took: 1000,
      };
      const accessLogStorage = new AccessLogStorage(100, 3, 1000);
      accessLogStorage.addLog(log, false);
      accessLogStorage.addLog(log, false);
      accessLogStorage.addLog(log, false);
      expect(accessLogStorage.activeBufferHasCapacity(120)).toBeFalse();
    });

    it('returns false if the new log entry alone exceeds the maximum number of bytes per buffer', () => {
      const accessLogStorage = new AccessLogStorage(100, 3, 50);
      expect(accessLogStorage.activeBufferHasCapacity(120)).toBeFalse();
    });

    it('returns false if the new log entry would cause the active buffer to exceed the maximum number of bytes per buffer', () => {
      const log: AccessLogEntry = {
        blocked: false,
        url: 'https://www.example.com',
        time: '1719936458',
        categories: ['123', '456'],
        groups: ['foo', 'bar'],
        took: 1000,
      };
      const accessLogStorage = new AccessLogStorage(100, 100, 300);
      accessLogStorage.addLog(log, false);
      accessLogStorage.addLog(log, false);
      expect(accessLogStorage.activeBufferHasCapacity(200)).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('isSameLog()', () => {
    it('returns true if both logs have the same block status, url, timestamp, and categories', () => {
      expect(
        AccessLogStorage.isSameLog(
          {
            blocked: false,
            url: 'https://www.example.com',
            time: '1719934943',
            categories: ['123', '456'],
            groups: ['blah', 'foo'],
            took: 1000,
          },
          {
            blocked: false,
            url: 'https://www.example.com',
            time: '1719934943',
            categories: ['123', '456'],
            groups: ['blah', 'foo'],
            took: 1000,
          },
        ),
      ).toBeTrue();
    });

    it('returns false if logs have a different block status', () => {
      expect(
        AccessLogStorage.isSameLog(
          {
            blocked: false,
            url: 'https://www.example.com',
            time: '1719934943',
            categories: ['123', '456'],
            groups: ['blah', 'foo'],
            took: 1000,
          },
          {
            blocked: true,
            url: 'https://www.example.com',
            time: '1719934943',
            categories: ['123', '456'],
            groups: ['blah', 'foo'],
            took: 1000,
          },
        ),
      ).toBeFalse();
    });

    it('returns false if logs have a different url', () => {
      expect(
        AccessLogStorage.isSameLog(
          {
            blocked: false,
            url: 'https://www.example.com',
            time: '1719934943',
            categories: ['123', '456'],
            groups: ['blah', 'foo'],
            took: 1000,
          },
          {
            blocked: false,
            url: 'https://www.example.org',
            time: '1719934943',
            categories: ['123', '456'],
            groups: ['blah', 'foo'],
            took: 1000,
          },
        ),
      ).toBeFalse();
    });

    it('returns false if logs have a different timestamp', () => {
      expect(
        AccessLogStorage.isSameLog(
          {
            blocked: false,
            url: 'https://www.example.com',
            time: '1719934000',
            categories: ['123', '456'],
            groups: ['blah', 'foo'],
            took: 1000,
          },
          {
            blocked: false,
            url: 'https://www.example.com',
            time: '1719934111',
            categories: ['123', '456'],
            groups: ['blah', 'foo'],
            took: 1000,
          },
        ),
      ).toBeFalse();
    });

    it('returns false if logs have different categories', () => {
      expect(
        AccessLogStorage.isSameLog(
          {
            blocked: false,
            url: 'https://www.example.com',
            time: '1719934943',
            categories: ['123', '456'],
            groups: ['blah', 'foo'],
            took: 1000,
          },
          {
            blocked: false,
            url: 'https://www.example.com',
            time: '1719934943',
            categories: ['789', '012'],
            groups: ['blah', 'foo'],
            took: 1000,
          },
        ),
      ).toBeFalse();
    });

    it('returns false if one log is missing a block status', () => {
      expect(
        AccessLogStorage.isSameLog(
          {
            blocked: false,
            url: 'https://www.example.com',
            time: '1719934943',
            categories: ['123', '456'],
            groups: ['blah', 'foo'],
            took: 1000,
          },
          {
            url: 'https://www.example.com',
            time: '1719934943',
            categories: ['123', '456'],
            groups: ['blah', 'foo'],
            took: 1000,
          },
        ),
      ).toBeFalse();
    });

    it('returns false if one log is missing a url', () => {
      expect(
        AccessLogStorage.isSameLog(
          {
            blocked: false,
            url: 'https://www.example.com',
            time: '1719934943',
            categories: ['123', '456'],
            groups: ['blah', 'foo'],
            took: 1000,
          },
          {
            blocked: false,
            time: '1719934943',
            categories: ['123', '456'],
            groups: ['blah', 'foo'],
            took: 1000,
          },
        ),
      ).toBeFalse();
    });

    it('returns false if one log is missing a timestamp', () => {
      expect(
        AccessLogStorage.isSameLog(
          {
            blocked: false,
            url: 'https://www.example.com',
            time: '1719934943',
            categories: ['123', '456'],
            groups: ['blah', 'foo'],
            took: 1000,
          },
          {
            blocked: false,
            url: 'https://www.example.com',
            categories: ['123', '456'],
            groups: ['blah', 'foo'],
            took: 1000,
          } as unknown as AccessLogEntry,
        ),
      ).toBeFalse();
    });

    it('returns false if one log is missing categories', () => {
      expect(
        AccessLogStorage.isSameLog(
          {
            blocked: false,
            url: 'https://www.example.com',
            time: '1719934943',
            categories: ['123', '456'],
            groups: ['blah', 'foo'],
            took: 1000,
          },
          {
            blocked: false,
            url: 'https://www.example.com',
            time: '1719934943',
            groups: ['blah', 'foo'],
            took: 1000,
          } as unknown as AccessLogEntry,
        ),
      ).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('constrainBuffer()', () => {
    it('does not modify the buffer if it is within limits', () => {
      const originalBuffer = makeRandomLogEntries(10);
      const constrainedBuffer = clone(originalBuffer);
      const accessLogStorage = new AccessLogStorage();
      accessLogStorage.constrainBuffer(constrainedBuffer);
      expect(constrainedBuffer).toEqual(originalBuffer);
    });

    it('returns false if the buffer was already within limits', () => {
      const buffer = makeRandomLogEntries(10);
      const accessLogStorage = new AccessLogStorage();
      expect(accessLogStorage.constrainBuffer(buffer)).toBeFalse();
    });

    it('removes log entries from the front of the buffer if it exceeds the maximum number of entries', () => {
      const originalBuffer = makeRandomLogEntries(10);
      const constrainedBuffer = clone(originalBuffer);
      const accessLogStorage = new AccessLogStorage(100, 7, 1000000);
      accessLogStorage.constrainBuffer(constrainedBuffer);
      expect(constrainedBuffer).toEqual(originalBuffer.slice(-7));
    });

    it('removes log entries from the front of the buffer if it exceeds the maximum size in bytes', () => {
      const originalBuffer: AccessLogEntry[] = [
        {
          blocked: true,
          url: 'https://www.example.com/0',
          time: '1720001789',
          groups: [],
          took: 0,
          categories: [],
        },
        {
          blocked: true,
          url: 'https://www.example.com/1',
          time: '1720001790',
          groups: [],
          took: 0,
          categories: [],
        },
        {
          blocked: true,
          url: 'https://www.example.com/2',
          time: '1720001791',
          groups: [],
          took: 0,
          categories: [],
        },
        {
          blocked: true,
          url: 'https://www.example.com/3',
          time: '1720001792',
          groups: [],
          took: 0,
          categories: [],
        },
        {
          blocked: true,
          url: 'https://www.example.com/4',
          time: '1720001793',
          groups: [],
          took: 0,
          categories: [],
        },
      ];

      const constrainedBuffer = clone(originalBuffer);
      const accessLogStorage = new AccessLogStorage(100, 1000, 400);
      accessLogStorage.constrainBuffer(constrainedBuffer);
      expect(constrainedBuffer).toEqual(originalBuffer.slice(-3));
    });

    it('returns true if any entries were removed from the buffer', () => {
      const buffer = makeRandomLogEntries(10);
      const accessLogStorage = new AccessLogStorage(100, 7, 1000000);
      expect(accessLogStorage.constrainBuffer(buffer)).toBeTrue();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('combineDuplicate()', () => {
    let existingLog: AccessLogEntry;
    let newLog: AccessLogEntry;

    beforeEach(() => {
      existingLog = {
        tenant: 'a1a53d43-e23b-4036-8cd2-0152b85735fe',
        httpcode: 200,
        took: 401,
        time: '1720014939',
        groups: ['test-group-1', 'test-group-2', 'test-group-3'],
        username: 'test-user-1',
        https: true,
        method: 'GET',
        blocked: false,
        categories: ['19', '26', '871'],
        destdomain: 'example.com',
        url: 'https://www.example.com/foobar',
        locations: ['abc', 'def'],
        timeslots: ['1234', '5678'],
        actions: ['allow'],
        searchterms: ' blah ',
        loglevel: 3,
        contenttype: 'main_frame',
        videoids: '14892',
        v: '3',
        ruleId: '111',
        policy: 'xyzzy',
      };

      newLog = {
        tenant: 'a1a53d43-e23b-4036-8cd2-0152b85735fe',
        took: 774,
        time: '1720014942',
        groups: ['test-group-1', 'test-group-2', 'test-group-3'],
        username: 'test-user-1',
        https: true,
        blocked: true,
        categories: ['19', '500', '1492', '4339'],
        destdomain: 'example.com',
        url: 'https://www.example.com/foobar/123',
        locations: ['abc', 'def'],
        timeslots: ['1234', '5678'],
        actions: ['block'],
        searchterms: ' blah ',
        safeguardinglevel: 3,
        safeguardingtheme: 'Radicalisation',
        loglevel: 4,
        contenttype: 'main_frame',
        title: 'Test Page',
        videoids: '14892',
        v: '3',
        ruleId: '222',
        policy: 'xyzzy',
      };
    });

    it('keeps the url from the original log', () => {
      AccessLogStorage.combineDuplicate(existingLog, newLog);
      expect(existingLog.url).toEqual('https://www.example.com/foobar');
    });

    it('combines the categories from both logs into the existing one', () => {
      AccessLogStorage.combineDuplicate(existingLog, newLog);
      expect(existingLog.categories).toContain('19');
      expect(existingLog.categories).toContain('26');
      expect(existingLog.categories).toContain('871');
      expect(existingLog.categories).toContain('500');
      expect(existingLog.categories).toContain('1492');
      expect(existingLog.categories).toContain('4339');
    });

    it('keeps the timestamp from the existing log', () => {
      AccessLogStorage.combineDuplicate(existingLog, newLog);
      expect(existingLog.time).toEqual('1720014939');
    });

    it('copies other fields from the new log to the existing one if they exist', () => {
      AccessLogStorage.combineDuplicate(existingLog, newLog);
      expect(existingLog.safeguardingtheme).toEqual('Radicalisation');
      expect(existingLog.title).toEqual('Test Page');
      expect(existingLog.ruleId).toEqual('222');
    });

    it('retains fields in the original log if they do not exist in the log', () => {
      AccessLogStorage.combineDuplicate(existingLog, newLog);
      expect(existingLog.httpcode).toEqual(200);
      expect(existingLog.method).toEqual('GET');
    });
  });
});

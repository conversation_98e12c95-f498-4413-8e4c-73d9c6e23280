import AccessLogEntry from 'models/AccessLogEntry';

/**
 * Manages an in-memory and on-disk cache of access log entries which are waiting to be uploaded.
 * This is more complex than it sounds. We need to split logs into small buffers for storage to
 *  ensure we don't cause a memory or storage violation. Additionally, the extension storage
 *  mechanism is relatively primitive and provides no sensible way to query items in storage unless
 *  you reload the entirety of storage from scratch.
 */
export default class AccessLogStorage {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance with the specified limits on data storage.
   * If any limit is reached, e.g. due to uploads not being possible, then the oldest logs will
   *  usually be discarded to make room for more entries.
   *
   * @param maxNumBuffers The maximum number of committed log buffers to retain at one time. A
   *  committed buffer is one which is finalised for upload and will have no new entries added.
   *  There is always one active buffer in addition to any committed ones.
   * @param maxEntriesPerBuffer The maximum number of log entries to store per buffer.
   * @param maxBytesPerBuffer The maximum size of each buffer in bytes. This is based on the JSON
   *  representation of the contents of each buffer, as that reflects how the data will be uploaded.
   *
   * @note It's recommended that the maximum number of bytes per buffer is no more than 1 MB. This
   *  ensures that 3 buffers can comfortably fit in the 4 MB limit on ingest v2 / v3 uploads.
   */
  public constructor(
    maxNumBuffers: number = 10,
    maxEntriesPerBuffer: number = 50000,
    maxBytesPerBuffer: number = 50000000, // 50 MB
  ) {
    this._maxNumBuffers = maxNumBuffers;
    this._maxEntriesPerBuffer = maxEntriesPerBuffer;
    this._maxBytesPerBuffer = maxBytesPerBuffer;

    chrome.storage.managed.get('MaxAccessLogBufferSizeInBytes', (key: any) => {
      const maxBufferSize = +key?.MaxAccessLogBufferSizeInBytes;

      if (Number.isNaN(maxBufferSize) || maxBufferSize < 3) {
        return;
      }

      this._maxBytesPerBuffer = Math.min(maxBytesPerBuffer, maxBufferSize);
    });
  }

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Load cached data from storage into memory.
   * This should be called once on startup, before any access logs are generated.
   * The active buffer in memory will be overwritten if there is an active buffer in storage.
   * This will try to preserve all existing committed buffers, re-numbering the ones from storage
   *  if necessary. This will cause a new write out to storage next time persist() is called.
   *
   * @param storageData An object containing all the data loaded from local storage. This function
   *  will extract only the relevant access log information and ignore the rest.
   *
   * @note Storage data is populated as a single large object because that's the only way to get a
   *  complete list of everything in storage. Access log buffers are dynamically named, and we need
   *  to make sure we don't accidentally lose track of any.
   */
  public readonly populate = (storageData: Record<string, any>): void => {
    this._populateActiveBuffer(storageData);
    this._populateCommittedBuffers(storageData);
  };

  /**
   * Load legacy access log data from storage into committed buffers.
   * This is only applicable where the extension has been updated from v3.0.0 or earlier to v3.0.1
   *  or later. Older versions stored access logs as one huge array.
   * If legacy data exists in storage then it will be split into smaller committed buffers. The
   *  legacy data will be deleted from storage, and the new committed buffers will be saved. Any
   *  other pending changes will be persisted as well.
   *
   * @param storageData The entire contents of storage. The legacy data will be copied.
   * @param maxAgeInDays The maximum age of logs to retain, measured in days. Log entries whose
   *  timestamp is older than this (compared to the current system clock), or which don't have a
   *  valid timestamp, will be discarded.
   * @returns A promise which resolves when the legacy data has been loaded, if applicable. It will
   *  reject if the storage operations failed.
   */
  public readonly populateLegacy = async (
    storageData: Record<string, any>,
    maxAgeInDays: number,
  ): Promise<void> => {
    if (storageData?.accessLogs === undefined) {
      // There's no legacy data so there's nothing else to do.
      return;
    }

    if (maxAgeInDays < 0) {
      console.warn('AccessLogStorage - Negative age threshold specified when loading legacy logs.');
    }

    // Access logs contain a timestamp measured in seconds.
    const threshold = Math.floor(Date.now() / 1000) - maxAgeInDays * 86400;

    let totalItems = 0;
    let numLoaded = 0;

    const legacyQueues: string[] = [
      storageData?.accessLogs?.dedupAccessLogQueue,
      storageData?.accessLogs?.accessLogQueue,
    ];

    let buffer: AccessLogEntry[] = [];
    let bufferSize = 0;

    legacyQueues.forEach((queue) => {
      if (!Array.isArray(queue) || queue.length === 0) {
        return;
      }

      totalItems += queue.length;

      // Don't bother processing any more entries if we're out of room.
      if (this._committedBuffers.size >= this._maxNumBuffers) {
        return;
      }

      // Go through the queue backwards so that we get the most recent entries first. If we run out
      //  of space then we'll discard the older ones.
      for (let i = queue.length - 1; i >= 0; --i) {
        // Ignore entries which aren't strings.
        const logString = queue[i];
        if (typeof logString !== 'string') {
          continue;
        }

        // Ignore entries which aren't valid JSON.
        let log: AccessLogEntry;
        try {
          log = JSON.parse(logString) as AccessLogEntry;
        } catch (e: any) {
          continue;
        }

        const logSize = AccessLogStorage.calculateLogSizeInBytes(log);

        // Ignore entries which are impossibly big, or don't have a URL or valid timestamp.
        const time = parseInt(log.time);
        if (logString.length > this._maxBytesPerBuffer || log.url == null || isNaN(time)) {
          continue;
        }

        // Ignore entries which are too old. Note that we're going through the access logs
        //  backwards so we can probably assume that everything else is too old as well. We'll
        //  just skip the rest of the queue.
        if (time < threshold) {
          break;
        }

        // If the buffer has reached maximum capacity then start a new one.
        if (
          buffer.length >= this._maxEntriesPerBuffer ||
          bufferSize + logSize > this._maxBytesPerBuffer
        ) {
          this._commitBuffer(buffer, false);
          buffer = [];
          bufferSize = 0;

          // If there's no room for more buffers then ignore the rest of the entries.
          if (this._committedBuffers.size >= this._maxNumBuffers) {
            break;
          }
        }

        buffer.unshift(log);
        bufferSize += logSize;
        ++numLoaded;
      }
    });

    // If there's anything left in the buffer then commit it.
    if (buffer.length > 0) {
      this._commitBuffer(buffer, false);
    }

    if (totalItems > 0) {
      console.debug(
        `AccessLogStorage - Loaded ${numLoaded} legacy log(s). Discarded ${
          totalItems - numLoaded
        } invalid or out-of-date logs(s).`,
      );
    }

    // Ensure the legacy data is removed from storage and the new buffers are saved.
    await chrome.storage.local.remove('accessLogs');
    await this.persist();
  };

  /**
   * If there are any changes in access log data then write them out to disk.
   * This should be called regularly (e.g. every 15-30 seconds) to ensure nothing gets lost.
   */
  public readonly persist = async (): Promise<void> => {
    if (!this._activeBufferHasChanged && this._committedBufferChanges.size === 0) {
      return;
    }

    const changes: Record<string, AccessLogEntry[]> = {};
    if (this._activeBufferHasChanged) {
      changes[AccessLogStorage.activeBufferName] = this._activeBuffer;
    }

    // We need to figure out which of the committed buffers have changed and which have been deleted
    //  as those are separate storage operations. (Setting a storage entry to undefined does not
    //  delete it.)
    const deletions: string[] = [];
    this._committedBufferChanges.forEach((bufferNumber: number) => {
      const buffer = this._committedBuffers.get(bufferNumber);
      const bufferName = AccessLogStorage.makeBufferName(bufferNumber);
      if (buffer === undefined) {
        // The buffer no longer exists in memory so it needs to be deleted from storage too.
        deletions.push(bufferName);
      } else {
        // The buffer exists in memory so it needs to be written to storage.
        changes[bufferName] = buffer;
      }
    });

    // Do the deletions first in case we're short on storage space.
    if (deletions.length > 0) {
      await chrome.storage.local.remove(deletions);
    }

    if (Object.keys(changes).length > 0) {
      await chrome.storage.local.set(changes);
    }

    this._committedBufferChanges.clear();
    this._activeBufferHasChanged = false;
    console.debug('AccessLogStorage - Access logs persisted to local storage.');
  };

  /**
   * Store a new log entry to be uploaded.
   * This appends the log entry to the active buffer, or combines it with an existing duplicate
   *  entry if applicable. It does not trigger an immediate upload or write to storage.
   *
   * @param newLog The new log entry to be stored.
   * @param applyDeduplication If false, the new log entry will be stored as-is. If true, and
   *  another recent entry for the same URL is found, then the entries will be combined. This should
   *  be set to true if the new log entry was generated from content analysis, and false if it was
   *  generated from URL analysis.
   */
  public readonly addLog = (newLog: AccessLogEntry, applyDeduplication: boolean): void => {
    // Sanity-check: Discard any individual entry which is too big to store safely.
    const logSize = AccessLogStorage.calculateLogSizeInBytes(newLog);
    if (logSize > this._maxBytesPerBuffer) {
      console.warn('AccessLogStorage - Discarding log entry which is over maximum size.', newLog);
      return;
    }

    // Discard any log entry which doesn't have a valid timestamp. We need to know how old a log
    //  entry is in case we need to purge old entries.
    if (isNaN(parseInt(newLog.time))) {
      console.warn(
        'AccessLogStorage - Discarding log entry which does not have a valid timestamp.',
        newLog,
      );
      return;
    }

    // Try to avoid storing multiple entries about the same request. This is used to combine entries
    //  generated during content analysis with earlier entries generate during URL analysis.
    if (applyDeduplication && this._findAndCombineDuplicates(newLog)) {
      return;
    }

    // Make sure there's enough room in the active buffer to append a new log entry. If there isn't,
    //  then commit the entire active buffer right away. This means we may lose the chance to
    //  deduplicate some entries, but duplicate entries are better than lost entries.
    if (!this.activeBufferHasCapacity(logSize)) {
      console.debug('AccessLogStorage - Active buffer is full. Committing it to make room.');
      this._commitActiveBuffer();
    }

    this._appendToActiveBuffer(newLog, logSize);
  };

  /**
   * Get a collection of log entries which are ready for upload.
   * This does not perform the upload itself, nor does it remove any logs from memory or storage.
   * If upload is successful, the caller must explicitly call one of the discard functions on this
   *  object to tell it to dispose of logs which no longer need to be stored.
   * For efficiency, log entries are grouped into buffers. This function will return a whole number
   *  of buffers. Where possible, the caller should succeed or fail at uploading a whole number of
   *  buffers so that each one can be deleted from memory and storage as a single unit. This is much
   *  more efficient than discarding individual log entries.
   *
   * @param maxNumBuffers The maximum number of log buffers to fetch for upload. This is used to
   *  limit the maximum possible amount of data which we'll try to upload at once time. See the
   *  constructor parameters for the size limits on individual buffers. This number should generally
   *  be at least 3 to ensure we don't end up with a permanent backlog when uploads are slow.
   * @returns A map of log buffers which are ready for upload. The key is a number identifying the
   *  buffer, and the value is an array of log entries in that buffer. Use the buffer number when
   *  calling discardBuffers() after a successful upload.
   *
   * @warning Be careful not to modify the returned array accidentally, e.g. don't explicitly remove
   *  entries from it. Those changes will not be visible to this object, so they may not get
   *  persisted to storage.
   *
   * @todo Possibly limit by total size rather than number of buffers, in case each buffer is small?
   */
  public readonly prepareUpload = (maxNumBuffers: number): Map<number, AccessLogEntry[]> => {
    if (maxNumBuffers < 1) {
      throw new Error('Cannot upload less than 1 complete buffer.');
    }

    // Ensure we don't have any lingering empty buffers as that could cause us to get struck trying
    //  to upload nothing.
    this.discardEmptyBuffers();

    // If there's still room in the upload after including all the committed buffers, then include
    //  all de-duplicated entries from the active buffer too.
    if (this._committedBuffers.size < maxNumBuffers) {
      this._commitDeduplicatedActiveBuffer();
    }

    return new Map([...this._committedBuffers.entries()].slice(0, maxNumBuffers));
  };

  /**
   * Delete entire buffers of committed logs, if they exist.
   * This should be used if all the logs from these buffers were successfully uploaded in a single
   *  operation. If buffers were only partially uploaded then use discardLogs() instead. However,
   *  note that discarding logs selectively is far less efficient.
   * The buffers are immediately removed from memory, preventing them from being uploaded again. The
   *  deletions will be mirrored to storage next time persist() is called.
   *
   * @param bufferNumbers Identifies the numbered buffers to be deleted. These should correspond to
   *  the map keys returned by prepareUpload(). Numbered buffers which don't exist will be ignored.
   */
  public readonly discardBuffers = (bufferNumbers: number[]): void => {
    bufferNumbers.forEach((bufferNumber: number) => {
      this._committedBuffers.delete(bufferNumber);
      this._committedBufferChanges.add(bufferNumber);
    });
  };

  /**
   * Delete specific logs from the committed buffers, if they exist.
   * This should be used where logs have been successfully uploaded, but may not represent complete
   *  buffers. This is a processor and memory intensive operation. Where possible, delete entire
   *  buffers instead using discardBuffers().
   * The logs are immediately removed from memory, preventing them from being uploaded again. The
   *  deletions will be mirrored to storage next time persist() is called.
   *
   * @param logsToBeDiscarded The log entries which should be discarded.
   *
   * @todo Add an optional parameter which lists the buffers to discard from. This will make the
   *  operation more efficient by ignoring buffers which definitely weren't used in the upload.
   */
  public readonly discardLogs = (logsToBeDiscarded: AccessLogEntry[]): void => {
    // Comparison function to check if a given log entry is NOT in the discard list.
    const isToBeKept = (existingLog: AccessLogEntry): boolean => {
      for (const logToBeDiscarded of logsToBeDiscarded) {
        if (AccessLogStorage.isSameLog(existingLog, logToBeDiscarded)) {
          return false;
        }
      }
      return true;
    };

    this._committedBuffers.forEach((buffer, bufferNumber) => {
      // If the buffer contains any discardable logs then filter it in-place.
      if (!buffer.every(isToBeKept)) {
        buffer.splice(0, buffer.length, ...buffer.filter(isToBeKept));
        this._committedBufferChanges.add(bufferNumber);
      }
    });

    this.discardEmptyBuffers();
  };

  /**
   * Delete any buffers of committed logs which have no entries in them.
   * The buffers are immediately removed from memory, preventing them from being included in any
   *  uploads. The deletions will be mirrored to storage next time persist() is called.
   * This is called automatically when needed.
   */
  public readonly discardEmptyBuffers = (): void => {
    // Figure out which buffers are empty.
    // Note: It's not safe to remove map entries while iterating over the map.
    const bufferNumbers: number[] = [];
    this._committedBuffers.forEach((buffer, bufferNumber) => {
      if (buffer.length === 0) {
        bufferNumbers.push(bufferNumber);
      }
    });

    // Remove the buffers from memory, and flag them for deletion from storage.
    bufferNumbers.forEach((bufferNumber) => {
      this._committedBuffers.delete(bufferNumber);
      this._committedBufferChanges.add(bufferNumber);
    });
  };

  /**
   * Go through all active and committed log entries and discard any older than the given age.
   * This will also remove any entries which don't have a valid timestamp. If any buffers are empty
   *  after this then they will be discarded entirely.
   * The logs and buffers are immediately removed from memory, preventing them from being included
   *  in any uploads. The deletions will be mirrored to storage next time persist() is called.
   *
   * @param maxAgeInDays The maximum age of logs to retain, measured in days. Log entries whose
   *  timestamp is older than this (compared to the current system clock), or which don't have a
   *  valid timestamp, will be discarded.
   *
   * @warning This can be quite an intensive operation, depending on how many logs are stored. Do
   *  not call this frequently.
   */
  public readonly discardOldLogs = (maxAgeInDays: number): void => {
    // Access logs contain a timestamp measured in seconds.
    const threshold = Math.floor(Date.now() / 1000) - maxAgeInDays * 86400;
    const isNewLog = (log: AccessLogEntry): boolean => {
      const time = parseInt(log.time);
      return !isNaN(time) && time >= threshold;
    };

    // Only update the active buffer if necessary.
    if (!this._activeBuffer.every(isNewLog)) {
      this._replaceActiveBuffer(this._activeBuffer.filter(isNewLog));
    }

    this._committedBuffers.forEach((buffer, bufferNumber) => {
      // We need to filter the committed buffer in-place as we can't risk modifying the map
      //  structure while we're iterating over it.
      if (!buffer.every(isNewLog)) {
        buffer.splice(0, buffer.length, ...buffer.filter(isNewLog));
        this._committedBufferChanges.add(bufferNumber);
      }
    });

    this.discardEmptyBuffers();
  };

  /**
   * Delete all log entries in all buffers.
   * The logs are immediately removed from memory, preventing them from being included in any more
   *  uploads. The deletions will be mirrored to storage next time persist() is called.
   */
  public readonly discardAllLogs = (): void => {
    if (this._activeBuffer.length > 0) {
      this._replaceActiveBuffer([], 0);
    }

    [...this._committedBuffers.keys()].forEach((bufferNumber) => {
      this._committedBufferChanges.add(bufferNumber);
    });

    this._committedBuffers.clear();
  };

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Append a new entry to the active buffer.
   * This will update the calculated buffer size and flag the buffer as having changed.
   *
   * @param log The new log entry to append.
   * @param size The size of the new entry in bytes, if known, based on a UTF-8 encoding of a JSON
   *  representation. It will be calculated if not specified. See calculateLogSizeInBytes().
   *
   * @note Where possible, do not modify the active buffer directly. It's important to ensure the
   *  stored size stays in sync with the buffer contents.
   */
  private readonly _appendToActiveBuffer = (log: AccessLogEntry, size?: number): void => {
    this._activeBuffer.push(log);
    this._activeBufferSize += size ?? AccessLogStorage.calculateLogSizeInBytes(log);
    this._activeBufferHasChanged = true;
  };

  /**
   * Replace the entire contents of the active buffer.
   * This will update the calculated buffer size and flag the buffer as having changed.
   *
   * @param logs The new log entries to store in the active buffer. These will replace any existing
   *  log entries.
   * @param size The total size of the new log entries in bytes, if known, based on a UTF-8 encoding
   *  of a JSON lines representation. It will be calculated if not specified. See
   *  calculateBufferSizeInBytes().
   *
   * @note Where possible, do not modify the active buffer directly. It's important to ensure the
   *  stored size stays in sync with the buffer contents.
   */
  private readonly _replaceActiveBuffer = (logs: AccessLogEntry[], size?: number): void => {
    this._activeBuffer = logs;
    this._activeBufferSize = size ?? AccessLogStorage.calculateBufferSizeInBytes(logs);
    this._activeBufferHasChanged = true;
  };

  /**
   * Update the stored size of the active buffer.
   * This is useful if the buffer had to be modified directly.
   *
   * @note Where possible, please use _appendToActiveBuffer() or _replaceActiveBuffer() instead.
   */
  private readonly _updateActiveBufferSize = (): void => {
    this._activeBufferSize = AccessLogStorage.calculateBufferSizeInBytes(this._activeBuffer);
  };

  /**
   * Move all log entries from the active buffer into a new committed buffer.
   */
  private readonly _commitActiveBuffer = (): void => {
    this._commitBuffer(this._activeBuffer, true);
    this._replaceActiveBuffer([]);
  };

  /**
   * Move deduplicated log entries from the active buffer into a new committed buffer.
   * This only moves entries which have passed the deduplication age.
   */
  private readonly _commitDeduplicatedActiveBuffer = (): void => {
    // Get all the logs in the active buffer which have surpassed the deduplication time.
    const deduplicationThreshold = Math.floor((Date.now() - this._deduplicationTime) / 1000);
    const hasPassedDeduplicationTime = (log: AccessLogEntry): boolean =>
      +log.time < deduplicationThreshold;
    const logsToBeCommitted = this._activeBuffer.filter(hasPassedDeduplicationTime);

    // If there are no logs to be committed then there's nothing else to do.
    if (logsToBeCommitted.length === 0) {
      return;
    }

    this._commitBuffer(logsToBeCommitted, true);
    this._replaceActiveBuffer(this._activeBuffer.filter((l) => !hasPassedDeduplicationTime(l)));
  };

  /**
   * Add one buffer of log entries to the list of committed buffers.
   * This will find and assign the next available buffer number.
   *
   * @param buffer The entries to commit.
   * @param makeRoom Determines what to do if we're at the maximum number of committed buffers. If
   *  true, the oldest buffer will be discarded to make room for the new one. If false, the new
   *  buffer will not be committed.
   * @returns True if the new buffer was committed successfully, or false if not. It will only fail
   *  to commit if it was empty, or if we're at the maximum number of buffers and makeRoom was
   *  false.
   */
  private readonly _commitBuffer = (buffer: AccessLogEntry[], makeRoom: boolean): boolean => {
    if (buffer.length === 0) {
      return false;
    }

    if (this._committedBuffers.size >= this._maxNumBuffers) {
      if (!makeRoom) {
        return false;
      }

      // Discard the oldest buffer(s) to make room for the new one.
      console.warn('AccessLogStorage - Discarding old access logs to make room for new ones.');
      const numToDiscard = 1 + this._committedBuffers.size - this._maxNumBuffers;
      this.discardBuffers([...this._committedBuffers.keys()].slice(0, numToDiscard));
    }

    const bufferNumber = this.findNextBufferNumber();
    this._committedBuffers.set(bufferNumber, buffer);

    // Ensure the new buffer is flagged for storage.
    this._committedBufferChanges.add(bufferNumber);
    return true;
  };

  /**
   * Combine the given log entry with any other recent entries which relate to the same request.
   * Duplication regularly occurs for page and frame requests. An access log entry will be generated
   *  during URL analysis of the original request. If the request was allowed, then another entry
   *  will be generated when content analysis is performed. Rather than having two entries for the
   *  same request, we want to combine the together into one.
   * In most cases, the information from the content analysis log entry takes precedence.
   *
   * @param newLog The new log entry which has just been generated based on content analysis. This
   *  should not be called for URL analysis log entries.
   * @returns True if the specified log entry was combined into a recent duplicate. False if no
   *  duplicates were found.
   */
  private readonly _findAndCombineDuplicates = (newLog: AccessLogEntry): boolean => {
    const deduplicationThreshold = Math.floor((Date.now() - this._deduplicationTime) / 1000);
    const existingLogs = this._activeBuffer.filter((oldLog: AccessLogEntry) => {
      return (
        oldLog.url === newLog.url &&
        // Limit deduplication to recent log entries.
        parseInt(oldLog.time) >= deduplicationThreshold &&
        // Don't modify existing requests which were blocked. Duplication happens when we're doing
        //  content analysis, and we can't do content analysis if the original request was blocked
        //  during URL analysis.
        oldLog.blocked === false &&
        // Only consider requests for documents (i.e. pages and frames).
        (oldLog.contenttype === 'main_frame' || oldLog.contenttype === 'sub_frame')
      );
    });

    if (existingLogs.length === 0) {
      return false;
    }

    existingLogs.forEach((existingLog: AccessLogEntry) => {
      AccessLogStorage.combineDuplicate(existingLog, newLog);
    });

    // We modified one or more log entries in-place so the total buffer size probably changed.
    this._updateActiveBufferSize();
    this._activeBufferHasChanged = true;
    return true;
  };

  /**
   * Load data from storage into the active buffer.
   *
   * @param storageData The entire contents of storage.
   */
  private readonly _populateActiveBuffer = (storageData: Record<string, any>): void => {
    const loadedBuffer = storageData[AccessLogStorage.activeBufferName];

    // If the buffer is invalid then ignore it and discard it.
    if (!Array.isArray(loadedBuffer)) {
      console.debug('AccessLogStorage - No active buffer found in storage.');
      // Ensure it gets overwritten in case it was invalid.
      this._activeBufferHasChanged = true;
      return;
    }

    const wasConstrained = this.constrainBuffer(loadedBuffer);
    this._replaceActiveBuffer(loadedBuffer); // <-- sets the change flag to true
    if (wasConstrained) {
      console.warn('AccessLogStorage - Discarding active logs in storage due to size limits.');
      // Leave the change flag as true so that the constrained buffer gets written back to storage.
    } else {
      // The buffer is identical to storage so there are no changes to write back out.
      this._activeBufferHasChanged = false;
    }

    console.debug(`AccessLogStorage - Loaded ${loadedBuffer.length} log(s) into active buffer.`);
  };

  /**
   * Load data from storage into committed buffers.
   * If any buffer in storage has the same number as an existing buffer in memory, then the buffer
   *  from storage will be given a new number. This is to ensure we don't lose data accidentally.
   *
   * @param storageData The entire contents of storage. The access log data may be modified in place
   *  if any buffers are too big.
   */
  private readonly _populateCommittedBuffers = (storageData: Record<string, any>): void => {
    // Find the names of all the access log buffers in storage. Sort them numerically in reverse
    //  order as we want to prioritise the most recent buffers if we can't load all of them. We'll
    //  un-reverse them later.
    const bufferNames = Object.keys(storageData)
      .filter((n) => n.startsWith(AccessLogStorage.bufferNamePrefix))
      .sort((a, b) => b.localeCompare(a, undefined, { numeric: true }));

    let numEntriesLoaded = 0;
    let hasDiscardedLogs = false;

    const newBuffers = new Map<number, AccessLogEntry[]>();
    bufferNames.forEach((bufferName) => {
      const bufferNumber = AccessLogStorage.parseBufferName(bufferName);
      if (bufferNumber === undefined) {
        // This wasn't a numbered access log buffer so ignore it.
        return;
      }

      // If the buffer is empty or not an array then skip it and flag it for deletion.
      const buffer = storageData[bufferName];
      if (!Array.isArray(buffer) || buffer.length === 0) {
        this._committedBufferChanges.add(bufferNumber);
        return;
      }

      // If we've reached the maximum number of buffers then flag this one for deletion.
      if (this._committedBuffers.size + newBuffers.size >= this._maxNumBuffers) {
        hasDiscardedLogs = true;
        this._committedBufferChanges.add(bufferNumber);
        return;
      }

      // If the buffer in storage exceeds our limits then truncate it and flag it to be updated.
      if (this.constrainBuffer(buffer)) {
        hasDiscardedLogs = true;
        this._committedBufferChanges.add(bufferNumber);
      }

      newBuffers.set(bufferNumber, buffer);
      numEntriesLoaded += buffer.length;
    });

    // We loaded the buffers in reverse order above. Restore the original insertion order so that
    //  we preserve approximate chronological ordering where possible.
    [...newBuffers.entries()].reverse().forEach(([bufferNumber, buffer]) => {
      // If there's already a buffer in memory with the same number, then issue a new number to the
      //  one loaded from storage. This disrupts chronological ordering, but it's better than losing
      //  data.
      if (this._committedBuffers.has(bufferNumber)) {
        // This will allocate a new buffer number:
        this._commitBuffer(buffer, false);
      } else {
        this._committedBuffers.set(bufferNumber, buffer);
      }
    });

    if (hasDiscardedLogs) {
      console.warn(
        'AccessLogStorage - Discarding some committed logs in storage due to size limits.',
      );
    }

    console.debug(
      `AccessLogStorage - Loaded ${numEntriesLoaded} logs into ${this._committedBuffers.size} committed buffers.`,
    );
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Get a unique number for a new buffer to be added to the committed buffers map.
   * Where possible, this will return a number which is one higher than the highest buffer number
   *  currently in use. This means the buffers will usually be numbered in chronological order. This
   *  enables us to upload them in chronological order, even if we've had to reload them from
   *  storage after a restart.
   *
   * Uploading logs in chronological order isn't essential. However, it can help avoid old logs
   *  being stuck in the queue indefinitely in situations where we can't upload fast enough.
   *
   * @returns A unique number for a new log buffer.
   */
  public readonly findNextBufferNumber = (): number => {
    if (this._committedBuffers.size === 0) {
      return 0;
    }

    const highestUsed = Math.max(...this._committedBuffers.keys());
    if (highestUsed >= Number.MAX_SAFE_INTEGER) {
      // If we reach here then something has gone very wrong. It should takes billions of years to
      //  accumulate this many buffers.
      console.error('AccessLogStorage - Buffer number has exceeded maximum safe integer.');
      return 0;
    }

    return highestUsed + 1;
  };

  /**
   * Construct a buffer name from a buffer number.
   * The buffer number is used in the committed buffers map.
   * The buffer name is used when reading or writing storage.
   *
   * @param bufferNumber The buffer number to turn into a buffer name.
   * @returns A buffer number constructed from the specified buffer number.
   */
  public static readonly makeBufferName = (bufferNumber: number): string => {
    if (bufferNumber < 0) {
      throw new Error('Buffer number must be greater than or equal to zero.');
    }

    if (bufferNumber !== Math.floor(bufferNumber)) {
      throw new Error('Buffer number must be an integer.');
    }

    return `${AccessLogStorage.bufferNamePrefix}${bufferNumber}`;
  };

  /**
   * Extract the buffer number from a buffer name.
   * This will only work if the buffer name has the expected prefix, followed by an integer.
   *
   * @param name The buffer name to parse.
   * @returns The buffer number extracted from the name, if it was parsed successfully. Returns
   *  undefined if the buffer name did not match the expected structure.
   */
  public static readonly parseBufferName = (name: string): number | undefined => {
    if (!name.startsWith(AccessLogStorage.bufferNamePrefix)) {
      return undefined;
    }

    const result = parseInt(name.substring(AccessLogStorage.bufferNamePrefix.length));
    return isNaN(result) ? undefined : result;
  };

  /**
   * Calculate the amount of space in bytes a single log entry would take up in JSON.
   *
   * @param log The log entry to calculate the size of.
   * @returns The number of bytes the log entry would take up in a UTF-8 JSON representation.
   */
  public static readonly calculateLogSizeInBytes = (log: AccessLogEntry): number => {
    // Allow an extra byte for the line break after each entry in an upload.
    return new TextEncoder().encode(JSON.stringify(log)).length + 1;
  };

  /**
   * Calculate the amount of space in bytes a buffer of log entries would take up in JSON.
   *
   * @param buffer The buffer to calculate the size of.
   * @returns The number of bytes the buffer would take up in a UTF-8 JSON representation.
   *
   * @warning Calculating the size of an entire buffer this way can be relatively slow. Do not do it
   *  frequently, e.g. every time a new log entry is added.
   */
  public static readonly calculateBufferSizeInBytes = (buffer: AccessLogEntry[]): number => {
    // Allow an extra byte for the line break after each entry in an upload.
    return new TextEncoder().encode(JSON.stringify(buffer)).length + buffer.length;
  };

  /**
   * Check if the active buffer has capacity to add the specified log entry.
   * This checks the capacity in terms of number of items and total size in bytes.
   *
   * @param logSize The size in bytes of the new log entry we want to add to the active buffer. This
   *  is based on a UTF-8 encoding of the contents.
   * @returns True if the active buffer has capacity for the new entry, or false if not.
   *
   * @see calculateLogSizeInBytes()
   */
  public readonly activeBufferHasCapacity = (logSize: number): boolean => {
    return (
      this._activeBuffer.length < this._maxEntriesPerBuffer &&
      this._activeBufferSize + logSize <= this._maxBytesPerBuffer
    );
  };

  /**
   * Check if two log entry objects are likely to refer to the same log entry.
   * This only checks timestamp, URL, block status, and categories. All other fields are ignored.
   *
   * @param log1 The first log object to check.
   * @param log2 The second log object to check.
   * @returns True if the objects likely refer to the same entry, or false if not.
   */
  public static readonly isSameLog = (log1: AccessLogEntry, log2: AccessLogEntry): boolean => {
    return (
      log1.blocked === log2.blocked &&
      log1.time === log2.time &&
      log1.url === log2.url &&
      JSON.stringify(log1.categories) === JSON.stringify(log2.categories)
    );
  };

  /**
   * Apply size constraints to the given buffer of log entries.
   * This constrains the number of entries and the total size in bytes.
   * Entries will be discarded from the beginning of the array if necessary, and the buffer will be
   *  modified in place.
   *
   * @param buffer The buffer to constrain according to current size limits. It will be modified
   *  in-place.
   * @returns True if the buffer had to be truncated, or false if it was already within limits.
   */
  public readonly constrainBuffer = (buffer: AccessLogEntry[]): boolean => {
    if (
      buffer.length < this._maxEntriesPerBuffer &&
      AccessLogStorage.calculateBufferSizeInBytes(buffer) <= this._maxBytesPerBuffer
    ) {
      // Buffer is already within the limits so there's nothing to do.
      return false;
    }

    // Working backwards from the end, figure out how many buffer entries we can keep.
    let numEntriesToKeep = 0;
    let sizeInBytes = 0;
    for (let i = buffer.length - 1; i >= 0 && numEntriesToKeep < this._maxEntriesPerBuffer; --i) {
      const logSize = AccessLogStorage.calculateLogSizeInBytes(buffer[i]);
      if (sizeInBytes + logSize > this._maxBytesPerBuffer) {
        break;
      }
      sizeInBytes += logSize;
      ++numEntriesToKeep;
    }

    buffer.splice(0, buffer.length - numEntriesToKeep);
    return true;
  };

  /**
   * Combine two related log entries together to eliminate duplication.
   * This is typically used if we have an existing log entry from URL analysis, and then get another
   *  entry generated by content analysis. This will favour most of the information from the new log
   *  as we normally expect it to have more complete information. Some key information will be
   *  combined though.
   *
   * @param existingLog The existing log entry which was already stored. This is modified in-place.
   * @param newLog The new log entry which has just been generated. This should be the result of
   *  content analysis.
   *
   * @note It is the caller's responsibility to check that these log entries are duplicates before
   *  calling this. A lot of information in the existing log will be overwritten.
   */
  public static readonly combineDuplicate = (
    existingLog: AccessLogEntry,
    newLog: AccessLogEntry,
  ): void => {
    // Retain the URL from the original entry where possible as it's likely to be more accurate.
    // Content analysis takes the URL from a content script where it could potentially change.
    const url = existingLog.url ?? newLog.url;
    const destdomain = existingLog.destdomain ?? newLog.destdomain;

    // Use categories from both logs.
    const combinedCategories = [...new Set([...existingLog.categories, ...newLog.categories])];

    // Keep the time of the original request. Content analysis can be slow, which could result in
    //  misleading log times.
    const time = existingLog.time;

    // In most cases, the data from content analysis will take precedence as it is likely to be
    //  more complete.
    Object.assign(existingLog, newLog);

    existingLog.url = url;
    existingLog.destdomain = destdomain;
    existingLog.categories = combinedCategories;
    existingLog.time = time;
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The maximum number of committed buffers we will retain at any one time.
   * If we exceed this limit then the oldest buffer(s) will be discarded to make room.
   */
  private readonly _maxNumBuffers: number;

  /**
   * The maximum number of access log entries which are allowed per buffer.
   * If we reach this limit then additional entries will be split into another buffer.
   */
  private readonly _maxEntriesPerBuffer: number;

  /**
   * The maximum total size of log entries which are allowed per buffer.
   * If we reach this limit then additional entries will be split into another buffer.
   * If a single log entry exceeds this size then it will be discarded immediately.
   * This limit is provided to ensure we don't try to upload too much data all at once.
   */
  private _maxBytesPerBuffer: number = 50000000;

  /**
   * The base name used for caching each access log buffer in local storage.
   * For committed buffers, the buffer number will be appended to this, e.g. "accessLogs31".
   */
  public static readonly bufferNamePrefix = 'accessLogs';

  /**
   * The full name used for caching the active log buffer in local storage.
   */
  public static readonly activeBufferName = `${this.bufferNamePrefix}Active`;

  /**
   * The minimum time to retain log entries in the active buffer while waiting for duplicates.
   * This is measured in milliseconds.
   * We retain logs in the active buffer for a while so that we can wait and see if any duplicates
   *  arrive, and combine them into one if so. Duplicates commonly occur for page or frame URLs
   *  which were allowed because we receive logs for URL analysis and content analysis separately.
   *
   * @note This is only used to determine which logs to commit from the active buffer whenever a
   *  commit occurs. We don't do anything automatically when the time elapses.
   */
  private readonly _deduplicationTime = 15000;

  /**
   * New access log entries will be appended to this buffer as they arrive.
   * Each element is an object describing one access log entry.
   * These entries will periodically be swept across to a new buffer in the committed buffers map.
   * The current size of this in bytes is stored in _activeBufferSize, and it needs to be updated
   *  each time this array changes.
   *
   * @note Where possible, do not modify this buffer directly. Prefer _appendToActiveBuffer() or
   *  _replaceActiveBuffer() instead.
   */
  private _activeBuffer: AccessLogEntry[] = [];

  /**
   * The current size of the active buffer in bytes, based on a UTF-8 encoding of its contents.
   * This is used to determine when the buffer is full so that it can be committed. It has to be
   *  calculated manually each time the buffer is modified. Calculating it from scratch every time
   *  a log entry gets added would cause too much computational overhead.
   */
  private _activeBufferSize: number = 0;

  /**
   * The access log entries which are waiting to be uploaded.
   * Each map value represents a numbered buffer of logs. We're using a map instead of an array
   *  because the numeric mapping must be stable, and they are not necessarily consecutive or
   *  sorted. The number is used in the name of the corresponding data in local storage for caching.
   * Each individual buffer is an array of access log entries.
   * The logs are divided into small buffers because there's sometimes too much data to safely store
   *  it all in a single contiguous array. The service worker can crash if we try that.
   * Where possible, the buffers will be uploaded in the order in which they were inserted into this
   *  map.
   *
   * @note Where possible, we try to upload and delete entire buffers atomically. This means it's
   *  important not to append new entries directly to an existing buffer in this map as the new
   *  entries could be lost if an upload is in progress. A new buffer will be added to this map
   *  periodically by sweeping entries across from the active buffer.
   */
  private readonly _committedBuffers = new Map<number, AccessLogEntry[]>();

  /**
   * Indicates if the active buffer has changed since we last persisted data to storage.
   * This is used to avoid unnecessary storage operations.
   */
  private _activeBufferHasChanged: boolean = false;

  /**
   * The committed buffers which have been added, updated or deleted since they were last persisted.
   * Each entry is the number of a committed buffer. It corresponds to the keys in the
   *  _committedBuffers map.
   * This is used to avoid unnecessary storage operations.
   */
  private readonly _committedBufferChanges = new Set<number>();
}

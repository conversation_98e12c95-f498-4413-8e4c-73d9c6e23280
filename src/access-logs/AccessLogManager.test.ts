import AccessLogManager from 'access-logs/AccessLogManager';
import AccessLogStorage from './AccessLogStorage';
import MockTelemetryService from '../test-helpers/MockTelemetryService';
// import AccessLogEntry from 'models/AccessLogEntry';
import extensionConfig from 'config.json';
import CloudReportingVersion from 'constants/CloudReportingVersion';
import LicenseResult from 'models/LicenseResult';
import TenantId from 'models/TenantId';
import { makeRandomLogEntries, makeRandomLogEntry } from 'test-helpers/access-log-helpers';
import { LocalStorageAreaMock, StorageAreaMock } from 'test-helpers/chrome-api';
import RateLimiter from '../utilities/RateLimiter';

// Fetch is mocked globally. This just gets a type-cast reference to it to make code simpler.
const fetchMock = fetch as jest.Mock;

const licenseResultV2: LicenseResult = {
  cldfltLicenseValid: true,
  cldrptVersion: CloudReportingVersion.v2,
  cldrptConfig: {
    accessLogsBlobConfig: {
      resource: 'https://test.local/',
      sas: '?abc=xyz',
    },
  },
};

const licenseResultV3: LicenseResult = {
  cldfltLicenseValid: true,
  cldrptVersion: CloudReportingVersion.v3,
  cldrptConfig: {
    accessLogsBlobConfig: {
      resource: 'https://test.local/',
      sas: '?abc=xyz',
    },
  },
};

const licenseResultV4: LicenseResult = {
  cldfltLicenseValid: true,
  cldrptVersion: CloudReportingVersion.v4,
  cldrptConfig: {
    accessLogsIngestV4Config: {
      url: 'https://test.local/',
      bearerToken: 'Bearer abcdefg',
    },
  },
};

const hardwareId = 'xyzzy';
const tenantId = new TenantId('00000000-0000-0000-0000-000000000000');

const clone = <T>(data: T): T => JSON.parse(JSON.stringify(data));

describe('AccessLogManager', () => {
  let localStorageAreaMock: LocalStorageAreaMock;
  let accessLogStorage: AccessLogStorage;
  let mockTelemetryService: MockTelemetryService;
  let accessLogManager: AccessLogManager;

  beforeEach(() => {
    localStorageAreaMock = new LocalStorageAreaMock();
    chrome.storage.local = localStorageAreaMock;
    chrome.storage.managed = new StorageAreaMock();
    // for the purposes of testing disable the rate limiter
    const disabledRateLimiter: RateLimiter = {
      tryConsume: jest.fn(() => true),
    };

    accessLogStorage = new AccessLogStorage();
    mockTelemetryService = new MockTelemetryService();
    accessLogManager = new AccessLogManager(
      extensionConfig,
      mockTelemetryService,
      accessLogStorage,
      disabledRateLimiter,
    );

    // Simulate successful uploads by default.
    fetchMock.mockResolvedValue(new Response('', { status: 200, statusText: 'ok' }));

    // Suppress console messages.
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'debug').mockImplementation(() => {});
  });

  afterEach(() => {
    accessLogManager.stop();
    jest.clearAllTimers();
    jest.useRealTimers();
  });

  // -----------------------------------------------------------------------------------------------

  describe('lastUploadedAt', () => {
    it('returns 0 if no logs have been uploaded successfully since the instance was created', () => {
      expect(accessLogManager.lastUploadedAt).toEqual(0);
    });

    it('returns timestamp of when logs were last successfully uploaded', async () => {
      await accessLogManager.configure(hardwareId, tenantId, licenseResultV4);
      accessLogManager.storeAccessLog(makeRandomLogEntry(), true);
      await accessLogManager.upload();

      const differenceFromCurrentTime = Math.abs(
        Math.floor(Date.now() / 1000) - accessLogManager.lastUploadedAt,
      );
      expect(differenceFromCurrentTime).toBeLessThan(10);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('getUploadCount()', () => {
    it('returns 0 if no logs have been uploaded successfully since the instance was created', () => {
      expect(accessLogManager.lastUploadedAt).toEqual(0);
    });

    it('returns the number of logs successfully uploaded', async () => {
      await accessLogManager.configure(hardwareId, tenantId, licenseResultV4);
      accessLogManager.storeAccessLog(makeRandomLogEntry(), true);
      accessLogManager.storeAccessLog(makeRandomLogEntry(), true);
      accessLogManager.storeAccessLog(makeRandomLogEntry(), true);
      await accessLogManager.upload();
      accessLogManager.storeAccessLog(makeRandomLogEntry(), true);
      accessLogManager.storeAccessLog(makeRandomLogEntry(), true);
      await accessLogManager.upload();
      expect(accessLogManager.getUploadCount(false)).toEqual(5);
    });

    it('resets the count to zero for subsequent calls if the argument was true', async () => {
      await accessLogManager.configure(hardwareId, tenantId, licenseResultV4);
      accessLogManager.storeAccessLog(makeRandomLogEntry(), true);
      accessLogManager.storeAccessLog(makeRandomLogEntry(), true);
      accessLogManager.storeAccessLog(makeRandomLogEntry(), true);
      await accessLogManager.upload();
      accessLogManager.getUploadCount(true);
      expect(accessLogManager.getUploadCount(false)).toEqual(0);
    });

    it('does not reset the count to zero for subsequent calls if the argument was false', async () => {
      await accessLogManager.configure(hardwareId, tenantId, licenseResultV4);
      accessLogManager.storeAccessLog(makeRandomLogEntry(), true);
      accessLogManager.storeAccessLog(makeRandomLogEntry(), true);
      accessLogManager.storeAccessLog(makeRandomLogEntry(), true);
      await accessLogManager.upload();
      accessLogManager.getUploadCount(false);
      expect(accessLogManager.getUploadCount(false)).toEqual(3);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('populate()', () => {
    it('discards any existing log data', async () => {
      const spy = jest.spyOn(accessLogStorage, 'discardAllLogs');
      await accessLogManager.populate({});
      expect(spy).toHaveBeenCalled();
    });

    it('loads the specified access log data', async () => {
      const data = {
        accessLogs1: [],
        accessLogsActive: [],
      };
      const spy = jest.spyOn(accessLogStorage, 'populate');
      await accessLogManager.populate(data);
      expect(spy).toHaveBeenCalledWith(data);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('start()', () => {
    it('starts a regular timer which persists log changes to storage', async () => {
      jest.useFakeTimers();
      const spy = jest.spyOn(accessLogStorage, 'persist');
      await accessLogManager.start();
      expect(spy).not.toHaveBeenCalled();
      await jest.advanceTimersByTimeAsync(15000);
      expect(spy).toHaveBeenCalledTimes(1);
      await jest.advanceTimersByTimeAsync(15000);
      expect(spy).toHaveBeenCalledTimes(2);
    });

    it('starts a regular timer which uploads logs', async () => {
      jest.useFakeTimers();
      const spy = jest.spyOn(accessLogManager, 'upload');
      await accessLogManager.start();
      expect(spy).not.toHaveBeenCalled();
      await jest.advanceTimersByTimeAsync(300000);
      expect(spy).toHaveBeenCalledTimes(1);
      await jest.advanceTimersByTimeAsync(300000);
      expect(spy).toHaveBeenCalledTimes(2);
    });

    it('triggers an upload immediately if the instance is already configured', async () => {
      jest.useFakeTimers();
      const spy = jest.spyOn(accessLogManager, 'upload');
      await accessLogManager.configure(hardwareId, tenantId, licenseResultV4);
      await accessLogManager.start();
      expect(spy).toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('stop()', () => {
    it('stops the regular timer which persists log changes to storage', async () => {
      jest.useFakeTimers();
      const spy = jest.spyOn(accessLogStorage, 'persist');
      await accessLogManager.start();
      accessLogManager.stop();
      await jest.advanceTimersByTimeAsync(15000);
      expect(spy).not.toHaveBeenCalled();
    });

    it('stops the regular timer which uploads logs', async () => {
      jest.useFakeTimers();
      const spy = jest.spyOn(accessLogManager, 'upload');
      await accessLogManager.start();
      accessLogManager.stop();
      await jest.advanceTimersByTimeAsync(15000);
      expect(spy).not.toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('configure()', () => {
    it('constructs a log processor for an ingest v2 configuration', async () => {
      await accessLogManager.configure(hardwareId, tenantId, licenseResultV2);
      expect((accessLogManager as any)._accessLogProcessor).not.toBeUndefined();
    });

    it('constructs a log processor for an ingest v3 configuration', async () => {
      await accessLogManager.configure(hardwareId, tenantId, licenseResultV3);
      expect((accessLogManager as any)._accessLogProcessor).not.toBeUndefined();
    });

    it('constructs a log processor for an ingest v4 configuration', async () => {
      await accessLogManager.configure(hardwareId, tenantId, licenseResultV4);
      expect((accessLogManager as any)._accessLogProcessor).not.toBeUndefined();
    });

    it('clears the stored log processor if the cloud reporting configuration is invalid', async () => {
      await accessLogManager.configure(hardwareId, tenantId, licenseResultV4);

      const invalidLicenseResult: LicenseResult = {
        cldfltLicenseValid: true,
        cldrptVersion: CloudReportingVersion.v4,
        cldrptConfig: {},
      };
      await accessLogManager.configure(hardwareId, tenantId, invalidLicenseResult);
      expect((accessLogManager as any)._accessLogProcessor).toBeUndefined();
    });

    it('triggers an upload immediately if start() has already been called', async () => {
      const logs = makeRandomLogEntries(10);
      await accessLogManager.populate({ accessLogs1: clone(logs) });
      await accessLogManager.start();
      await accessLogManager.configure(hardwareId, tenantId, licenseResultV4);
      expect(fetchMock).toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('clearConfiguration()', () => {
    it('stops any more logs from being uploaded', async () => {
      await accessLogManager.configure(hardwareId, tenantId, licenseResultV4);
      accessLogManager.storeAccessLog(makeRandomLogEntry(), true);
      accessLogManager.clearConfiguration();
      await accessLogManager.upload();
      expect(fetchMock).not.toHaveBeenCalled();
    });

    it('does not stop log changes from being persisted to storage', async () => {
      jest.useFakeTimers();
      const spy = jest.spyOn(accessLogStorage, 'persist');
      await accessLogManager.start();
      await accessLogManager.configure(hardwareId, tenantId, licenseResultV4);
      accessLogManager.clearConfiguration();
      accessLogManager.storeAccessLog(makeRandomLogEntry(), true);
      await jest.advanceTimersByTimeAsync(15000);
      expect(spy).toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('clearLogs()', () => {
    it('discards all stored logs', async () => {
      const spy = jest.spyOn(accessLogStorage, 'discardAllLogs');
      await accessLogManager.clearLogs();
      expect(spy).toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('storeAccessLog()', () => {
    it('stores the specified log entry', () => {
      const spy = jest.spyOn(accessLogStorage, 'addLog');
      const log = makeRandomLogEntry();
      accessLogManager.storeAccessLog(log, true);
      expect(spy).toHaveBeenCalledWith(log, false);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('upload()', () => {
    it('does not upload anything if the instance is not configured', async () => {
      accessLogManager.storeAccessLog(makeRandomLogEntry(), true);
      await accessLogManager.upload();
      expect(fetchMock).not.toHaveBeenCalled();
    });

    it('does not start an upload if another one is in progress', async () => {
      await accessLogManager.configure(hardwareId, tenantId, licenseResultV4);
      accessLogManager.storeAccessLog(makeRandomLogEntry(), true);
      await Promise.all([accessLogManager.upload(), accessLogManager.upload()]);
      expect(fetchMock).toHaveBeenCalledOnce();
    });

    it('passes pending logs to the log processor for upload', async () => {
      await accessLogManager.configure(hardwareId, tenantId, licenseResultV4);
      const spy = jest.spyOn((accessLogManager as any)._accessLogProcessor, 'processLogsForUpload');
      const logs = makeRandomLogEntries(5);
      logs.forEach((log) => {
        accessLogManager.storeAccessLog(clone(log), true);
      });
      await accessLogManager.upload();

      // The logs are passed to the processor as an array of JSON strings.
      expect(spy).toHaveBeenCalledWith(expect.arrayContaining(logs.map((l) => JSON.stringify(l))));
    });

    it('discards uploaded log buffers if the entire upload was successful', async () => {
      const data = {
        accessLogs1: makeRandomLogEntries(10),
        accessLogs2: makeRandomLogEntries(15),
        accessLogs3: makeRandomLogEntries(12),
      };

      const spy = jest.spyOn(accessLogStorage, 'discardBuffers');
      await accessLogManager.populate(data);
      await accessLogManager.configure(hardwareId, tenantId, licenseResultV4);
      await accessLogManager.upload();
      expect(spy).toHaveBeenCalledWith([1]);
    });

    it('discards individual log entries if the upload was only partially successful', async () => {
      const data = {
        accessLogs1: makeRandomLogEntries(10),
        accessLogs2: makeRandomLogEntries(15),
        accessLogs3: makeRandomLogEntries(12),
      };

      await accessLogManager.populate(clone(data));
      await accessLogManager.configure(hardwareId, tenantId, licenseResultV4);
      const discardLogsSpy = jest.spyOn(accessLogStorage, 'discardLogs');

      // Simulate a partial upload. The processor will return an array of the log entries which were
      //  uploaded successfully (each one will be stringified).
      jest
        .spyOn((accessLogManager as any)._accessLogProcessor, 'processLogsForUpload')
        .mockResolvedValue([JSON.stringify(data.accessLogs1[0])]);

      await accessLogManager.upload();
      expect(discardLogsSpy).toHaveBeenCalledWith(expect.arrayContaining([data.accessLogs1[0]]));
    });

    it('does not discard any logs if none were uploaded successfully', async () => {
      const data = {
        accessLogs1: makeRandomLogEntries(10),
        accessLogs2: makeRandomLogEntries(15),
        accessLogs3: makeRandomLogEntries(12),
      };

      const discardBuffersSpy = jest.spyOn(accessLogStorage, 'discardBuffers');
      const discardLogsSpy = jest.spyOn(accessLogStorage, 'discardLogs');
      await accessLogManager.populate(clone(data));
      await accessLogManager.configure(hardwareId, tenantId, licenseResultV4);

      // Simulate a failed upload.
      jest
        .spyOn((accessLogManager as any)._accessLogProcessor, 'processLogsForUpload')
        .mockResolvedValue([]);

      await accessLogManager.upload();
      expect(discardBuffersSpy).not.toHaveBeenCalled();
      expect(discardLogsSpy).not.toHaveBeenCalled();
    });

    it('persists log changes to storage', async () => {
      await accessLogManager.configure(hardwareId, tenantId, licenseResultV4);
      const spy = jest.spyOn(accessLogStorage, 'persist');
      accessLogManager.storeAccessLog(makeRandomLogEntry(), true);
      await accessLogManager.upload();
      expect(spy).toHaveBeenCalled();
    });
  });
});

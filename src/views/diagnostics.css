@import url('https://fonts.googleapis.com/css?family=Roboto:300,400,500');

/*
  * Bootstrap
   */
.row {
  display: -ms-flexbox;
  display: flex;
  margin-right: -15px;
  margin-left: -15px;
}

.col-1,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-10,
.col-11,
.col-12,
.col,
.col-auto,
.col-sm-1,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm,
.col-sm-auto,
.col-md-1,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md,
.col-md-auto,
.col-lg-1,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg,
.col-lg-auto,
.col-xl-1,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xl,
.col-xl-auto {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

.col-2 {
  -ms-flex: 0 0 16.666667%;
  flex: 0 0 16.666667%;
  max-width: 16.666667%;
}

.col-10 {
  -ms-flex: 0 0 83.333333%;
  flex: 0 0 83.333333%;
  max-width: 83.333333%;
}

/*
  * End bootstrap
   */

body {
  /*background: #00bbb4;*/
  background: #f2f2f2;
  font-family: Roboto, sans-serif;
  font-style: normal;
  color: #3f3f3f;
  margin: 0;
}

button {
  width: fit-content;
  height: auto;
  padding: 10px 10px;
  color: #fff;
  border-radius: 5px;
  background-color: #00bbb4;
  text-align: center;
  /*margin: 10px 10px 10px 0px;*/
  border: 0;
  cursor: pointer;
}

button:focus {
  outline: none;
}

button:active {
  background: #009490;
}

button.disabled {
  border-color: #d40054;
  color: #d40054;
}

button.disabled:active {
  border-color: gray;
}

button.enabled {
}

.flex {
  display: flex;
}

.flex-end {
  justify-content: flex-end;
}

.flex-column {
  flex-direction: column;
}

.flex-center-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.nav-bar {
  height: 75px;
  background: #00bbb4;
  width: 100%;
  color: white;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.copied-bar {
  height: 25px;
  background: #d2d2d2;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: black;
  opacity: 0%;
  /*transition: opacity 3s;*/
}

.copied-bar-visible {
  opacity: 100%;
}

.nav-logo {
  display: flex;
  margin-left: 10px;
}

.nav-logo span {
  font-weight: 100;
  font-size: 30px;
  margin-top: 2px;
}

.main-50 {
  width: 50%;
  margin-left: 25%;
  margin-right: auto;
  margin-top: 10px;
}

.main-65 {
  width: 65%;
  margin-left: 25%;
  margin-right: auto;
  margin-top: 10px;
}

.title-grid {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  grid-template-rows: repeat(2, 1fr);
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  height: 120px;
}

.field-row {
  margin-top: 25px;
  margin-bottom: 25px;
}

.divider {
  width: 100%;
  margin-top: 10px;
  margin-bottom: 10px;
  border-bottom: #bfbfbf solid 1px;
}

.title-subgrid {
  grid-area: 1 / 1 / 2 / 8;
}

.subtitle-subgrid {
  grid-area: 2 / 1 / 3 / 8;
}

.button-subgrid {
  grid-area: 1 / 8 / 3 / 11;
}

.title {
  font-size: 25px;
  font-weight: 500;
  margin-bottom: 10px;
}

.subtitle {
  font-size: 14px;
}

.copy-button {
  margin-top: auto;
  margin-bottom: auto;
}

.section-header {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 35px;
}

.field-input {
  width: 40%;
  border: #5a5a5a solid 1px;
  border-radius: 3px;
  height: 40px;
  font-size: 11px;
  padding: 3px;
  color: #707070;
}

.field-input-error {
  border: red solid 1px;
}

.field-input-long {
  width: 72%;
}

.field-input-deep {
  height: 100px;
}

.field-message {
  margin-left: 10px;
  max-width: 220px;
  font-size: 12px;
}

.field-message-icon {
  font-weight: bold;
  margin-left: 20px;
  background-color: red;
  color: white;
  border-radius: 3px;
  width: 20px;
  display: flex;
  justify-content: center;
}

.field-label {
  margin-bottom: 10px;
  font-size: 12px;
  font-weight: 500;
}

@media screen and (max-width: 1000px) {
  .main-50 {
    width: 65%;
  }

  .main-65 {
    width: 80%;
  }
}

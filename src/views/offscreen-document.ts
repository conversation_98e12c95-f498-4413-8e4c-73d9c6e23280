import ContentScriptMessage from 'content-script-messages/ContentScriptMessage';
import PrivateIpRequestMessage from 'content-script-messages/PrivateIpRequestMessage';
import PrivateIpResponseMessage from 'content-script-messages/PrivateIpResponseMessage';
import WebRtcService from 'services/WebRtcService';

/**
 * A private IP address has been detected by the WebRTC service.
 * This may be called multiple times as individual IP addresses are detected.
 *
 * @param ipAddresses All the private IP addresses which have been detected so far in the current
 *  attempt.
 */
const onPrivateIpAddress = async (ipAddresses: string[]): Promise<void> => {
  await chrome.runtime.sendMessage(new PrivateIpResponseMessage(ipAddresses));
};

/**
 * Handle a message received from the extension.
 *
 * @param rawMessage The message which was received.
 */
const onMessage = (rawMessage: any, sender: chrome.runtime.MessageSender): void => {
  // Ignore messages which have been sent from content scripts or other extensions.
  if (sender.tab !== undefined || sender.id !== chrome.runtime.id) {
    return;
  }

  const message = ContentScriptMessage.load(rawMessage);
  switch (message.type) {
    case PrivateIpRequestMessage.type:
      webRtcService.getPrivateIpAddresses().catch(console.warn);
      break;
  }
};

const webRtcService = new WebRtcService();
webRtcService.onPrivateIpAddress.addListener(onPrivateIpAddress);
chrome.runtime.onMessage.addListener(onMessage);

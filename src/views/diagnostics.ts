import DiagnosticsPage from 'constants/DiagnosticsPage';
import { LocalDiagnosticsInfo } from 'models/DiagnosticsInfo';
import Uuid from 'models/Uuid';
import { capitalise } from 'utilities/Helpers';

export default class Diagnostics {
  public constructor() {
    // Create a random UUID so the extension can distinguish between ports.
    const namePostfix = Uuid.random().toString();
    this._contentPort = chrome.runtime.connect({
      name: `${DiagnosticsPage.portName}-${namePostfix}`,
    });
  }

  public readonly onDOMContentLoaded = (): void => {
    this._copyButton.addEventListener('click', () => {
      this._copyToClipboard({ copiedAt: new Date().toString(), ...this._cache });
    });

    this._contentPort.onMessage.addListener(this._onMessage);

    // Immediately request data from the extension.
    this._requestData();

    // Regularly request updates from the extension.
    setInterval(this._requestData, 5000);
  };

  /**
   * Send a message to the extension, requesting diagnostic data to display in the page.
   * We won't get a direct response. Instead, we'll receive a message in _onMessage().
   */
  private readonly _requestData = (): void => {
    this._contentPort.postMessage(DiagnosticsPage.requestString);
  };

  /**
   * Called when a message is received from the extension.
   *
   * @param message The message received from the extension. We always assume this contains the
   *  diagnostic data.
   */
  private readonly _onMessage = (message: any): void => {
    this._cache = message;
    this._updateFieldValuesFromCache(this._cache);
  };

  private readonly _copyToClipboard = (diagnosticsInfo: LocalDiagnosticsInfo): void => {
    const temp = document.createElement('textarea');
    document.body.appendChild(temp);
    temp.value = JSON.stringify(diagnosticsInfo, undefined, 4);
    temp.select();
    document.execCommand('copy');
    document.body.removeChild(temp);
  };

  private readonly _updateFieldValuesFromCache = (cache: LocalDiagnosticsInfo): void => {
    // TODO: Find a more elegant way to do this. E.g. have a map of cache names to HTML element IDs?
    this._fields.license.value = cache.license
      .map((element) => {
        if (element.toLowerCase() === 'cldflt') return 'Cloud Filter';
        if (element.toLowerCase() === 'cldrpt') return 'Cloud Reporting';
        if (element.toLowerCase() === 'mms') return 'Monitor Managed Service';
        return '';
      })
      .filter((element) => element !== '')
      .join(',\n');
    this._fields.connection.value = capitalise(cache.connection);
    this._fields.filterMode.value = cache.filterMode;
    this._fields.policyName.value = cache.policyName;
    this._fields.generatedDatetime.value = cache.generatedDatetime;
    this._fields.blocklistVersionDate.value = cache.blocklistVersionDate;
    this._fields.blocklistVersionEpoch.value = cache.blocklistVersionEpoch;
    this._fields.lastSentLogs.value = cache.lastSentLogs;
    this._fields.lastSecretKnockDetails.value = cache.lastSecretKnockDetails;
    this._fields.username.value = cache.username;
    this._fields.tenant.value = cache.tenant;
    this._fields.ipAddressPublic.value = cache.ipAddressPublic.join(', ');
    this._fields.ipAddressPrivate.value = cache.ipAddressPrivate.join(', ');
    this._fields.outsidePremises.value = cache.outsidePremises;
    this._fields.directoryGroups.value = cache.directoryGroups.join(',\n');
    this._fields.mappedGroups.value = cache.mappedGroups.map((group) => group.name).join(',\n');

    if (this._fields.outsidePremises.value === 'Not configured') {
      this._outsidePremisesInfo.style.visibility = 'unset';
    } else {
      this._outsidePremisesInfo.style.visibility = 'hidden';
    }
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  private readonly _contentPort: chrome.runtime.Port;

  private readonly _copyButton = document.getElementById('copyButton') as HTMLElement;

  private readonly _outsidePremisesInfo = document.getElementById(
    'outsidePremisesInfoID',
  ) as HTMLElement;

  private readonly _fields = {
    license: document.getElementById('licensing') as HTMLInputElement,
    connection: document.getElementById('connectionState') as HTMLInputElement,
    filterMode: document.getElementById('filterMode') as HTMLInputElement,
    policyName: document.getElementById('policyName') as HTMLInputElement,
    generatedDatetime: document.getElementById('generated') as HTMLInputElement,
    blocklistVersionDate: document.getElementById('blocklistVersionDatetime') as HTMLInputElement,
    blocklistVersionEpoch: document.getElementById('blocklistVersionEpoch') as HTMLInputElement,
    lastSentLogs: document.getElementById('lastSentLogs') as HTMLInputElement,
    lastSecretKnockDetails: document.getElementById('secretKnockDetails') as HTMLInputElement,
    username: document.getElementById('username') as HTMLInputElement,
    tenant: document.getElementById('tenant') as HTMLInputElement,
    ipAddressPublic: document.getElementById('ipAddressPublic') as HTMLInputElement,
    ipAddressPrivate: document.getElementById('ipAddressPrivate') as HTMLInputElement,
    outsidePremises: document.getElementById('outsidePremisesID') as HTMLInputElement,
    directoryGroups: document.getElementById('directoryGroups') as HTMLInputElement,
    mappedGroups: document.getElementById('mappedGroups') as HTMLInputElement,
  };

  private _cache: LocalDiagnosticsInfo = {
    license: [],
    connection: '',
    filterMode: '',
    policyName: '',
    generatedEpoch: '',
    generatedDatetime: '',
    blocklistVersionDate: '',
    blocklistVersionEpoch: '',
    lastSentLogs: '',
    lastSecretKnockDetails: '',
    username: '',
    tenant: '',
    ipAddressPublic: [],
    ipAddressPrivate: [],
    outsidePremises: '',
    directoryGroups: [],
    mappedGroups: [],
    provisioningMessage: '',
  };
}

document.addEventListener('DOMContentLoaded', (event) => {
  const diagnosticPage = new Diagnostics();
  diagnosticPage.onDOMContentLoaded();
});

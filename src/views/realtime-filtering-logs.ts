import AccessLogEntry from 'models/AccessLogEntry';
import RealTimeLogsPage from 'constants/RealTimeLogsPage';

/**
 * Provides searchable list of traffic logged by the extension.
 */
export default class RealTimeLogViewer {
  constructor() {
    this._contentPort = chrome.runtime.connect({ name: RealTimeLogsPage.portName });
    this._addListeners();
  }

  private readonly _addListeners = (): void => {
    // Add click listeners onto each sort column.
    for (const [key, element] of Object.entries(this._sorts)) {
      element.addEventListener('click', () => {
        this._onSort(key as keyof AccessLogEntry);
      });
    }
    this._contentPort.onMessage.addListener(this._processAccessLogs);
    this._searchBar.addEventListener('keyup', () => {
      this._processAccessLogs();
    });
  };

  /**
   * Handles click events on sorting buttons.
   *
   * @param key key to sort by (time, url, categories, actions, ruleId).
   */
  private readonly _onSort = (key: keyof AccessLogEntry): void => {
    if (this._currentSort.key === key) {
      this._currentSort.reverse = !this._currentSort.reverse;
    } else {
      this._currentSort.key = key;
      this._currentSort.reverse = false;
      for (const sort in this._sorts) this._changeSortArrow(sort, 'right');
    }

    this._changeSortArrow(key, this._currentSort.reverse ? 'down' : 'up');
    this._processAccessLogs();
  };

  /**
   * Changes the direction of the arrow at the top of sort columns.
   *
   * @param key The identifying component of the Id tag of the div ("<key>-arrow").
   * @param direction The direction the arrow points (right, up, or down).
   */
  private readonly _changeSortArrow = (key: string, direction: string): void => {
    const arrow = document.getElementById(`${key}-arrow`) as HTMLElement;
    arrow.className = '';
    arrow.classList.add(`arrow-${direction}`);
  };

  /**
   * Adds new logs, removes old ones, sorts remaining that match search term, and displays them.
   *
   * @param newLogs New access logs to add to the list (if the refresh toggle is on).
   */
  private readonly _processAccessLogs = (newLogs: AccessLogEntry[] = []): void => {
    // Only add new logs and drop expired logs if feed is live.
    if (this._liveFeed.checked) {
      this._logs.push(...newLogs);
      const expiry = Math.floor(Date.now() / 1000) - this._logExpiryInMinutes * 60;
      this._logs = this._logs.filter((log) => +log.time > expiry);
    }

    const logs = this._logs
      .filter((log: AccessLogEntry) => log.url?.includes(this._searchBar.value) ?? false)
      .sort((a: AccessLogEntry, b: AccessLogEntry) => {
        const key = this._currentSort.key;
        if (key === 'ruleId') return +(a.ruleId ?? 0) - +(b.ruleId ?? 0);
        else return a[key]?.toString().localeCompare(b[key]?.toString() ?? '') ?? 0;
      });

    if (this._currentSort.reverse) logs.reverse();

    this._table.innerHTML = '';

    logs.forEach((log: AccessLogEntry, index) => {
      if (index === 0) {
        this._userInfo.username.innerText = log.username ?? 'N/A';
        if (log.groups.length > 0) this._userInfo.group.innerText = log.groups.join(', ');
        else this._userInfo.group.innerText = 'N/A';
      }

      this._addRowToTable(log);
    });
  };

  /**
   * @param log the parsed log returned to its object form.
   * @param ruleId string containing the ruleId.
   */
  private readonly _addRowToTable = (log: AccessLogEntry): void => {
    const newRow = this._table.insertRow();

    const time = new Date(+log.time * 1000).toLocaleTimeString();
    const uri = log.url ?? '';
    const categories = log.categories?.sort().join(', ') ?? 'N/A';
    const actions = log.actions?.join(', ') ?? '';

    this._addCellToTableRow(newRow, document.createTextNode(time));

    const urlLink = document.createElement('a');
    urlLink.appendChild(document.createTextNode(uri));
    urlLink.title = uri;
    urlLink.href = uri;
    urlLink.target = '_blank';

    this._addCellToTableRow(newRow, urlLink);

    const cats = this._addCellToTableRow(newRow, document.createTextNode(categories));
    cats.classList.add('cat-data-element');
    cats.title = categories;

    const action = this._addCellToTableRow(newRow, document.createTextNode(actions));
    if (log.actions?.includes('allow') ?? false) action.classList.add('allow');
    else action.classList.add('deny');

    this._addCellToTableRow(newRow, document.createTextNode(log.ruleId ?? 'N/A'));
  };

  /**
   * @param row the HTMLTableRowElement representing the row of the log table.
   * @param append Text or HTMLAnchorElement to append to the cell.
   * @returns HTMLTableCellElement generic for further modification.
   */
  private readonly _addCellToTableRow = (
    row: HTMLTableRowElement,
    append: Text | HTMLAnchorElement,
  ): HTMLTableCellElement => {
    const cell = row.insertCell();
    cell.classList.add('tableElement');
    cell.classList.add('lock-text-small');
    cell.appendChild(append);
    return cell;
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * How long logs will stay in the feed before getting dropped.
   */
  private readonly _logExpiryInMinutes = 5;

  private _logs: AccessLogEntry[] = [];

  private readonly _currentSort: { key: keyof AccessLogEntry; reverse: boolean } = {
    key: 'time',
    reverse: true,
  };

  private readonly _contentPort: chrome.runtime.Port;

  private readonly _searchBar = document.getElementById('search-bar') as HTMLInputElement;

  private readonly _liveFeed = document.getElementById('refresh-toggle') as HTMLInputElement;

  private readonly _table = document.getElementById('main-table-body') as HTMLTableElement;

  private readonly _userInfo = {
    username: document.getElementById('username') as HTMLElement,
    group: document.getElementById('groupinfo') as HTMLElement,
  };

  private readonly _sorts = {
    time: document.getElementById('time-sort') as HTMLElement,
    url: document.getElementById('url-sort') as HTMLElement,
    categories: document.getElementById('categories-sort') as HTMLElement,
    actions: document.getElementById('actions-sort') as HTMLElement,
    ruleId: document.getElementById('ruleId-sort') as HTMLElement,
  };
}

document.addEventListener('DOMContentLoaded', (event) => new RealTimeLogViewer());

<!DOCTYPE html>
<html class="main-html" lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Content blocked</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="../images/smoothwall-icon-256x256.ico" />
    <script src="./block-page.js"></script>
    <link rel="stylesheet" href="block-page.css" />
  </head>

  <body>
    <div class="copied-bar" id="copied-bar">
      <span>Copied!</span>
    </div>
    <div class="flexbox">
      <div class="lock-section">
        <div class="lock-icon">
          <img class="lock-object" />
        </div>
        <div class="text-container">
          <div class="lock-text">
            <h1>Website Blocked</h1>
          </div>
          <div class="lock-text-small">
            <span class="block-label">This website has been blocked by your administrator</span>
          </div>
        </div>
      </div>
      <div id="blocked-reasons" class="filtering-info">
        <div class="lock-reasons">
          <h3>Filtering Information</h3>
          <!-- Username and Timestamp-->
          <div class="user-timestamp">
            <div class="info-wrapper">
              <span class="block-label">Username:</span>
              <span id="username" class="block-reason"></span>
            </div>
            <div class="info-wrapper">
              <span class="block-label">Time:</span>
              <span id="time" class="block-reason"></span>
            </div>
          </div>

          <!-- URL -->
          <div class="info-wrapper">
            <span class="block-label">URL:</span>
            <span id="url" class="block-reason"></span>
          </div>

          <!-- Location -->
          <div class="info-wrapper">
            <span class="block-label">Location:</span>
            <span id="location" class="block-reason"></span>
          </div>

          <!-- Category -->
          <div class="info-wrapper">
            <span class="block-label">Category:</span>
            <span id="categories" class="block-reason"></span>
          </div>

          <!-- Groups -->
          <div class="info-wrapper">
            <span class="block-label">Group Mappings:</span>
            <span id="groups" class="block-reason"></span>
          </div>

          <div class="button-wrapper">
            <button id="copyButton" class="clipboard-copy">Copy to Clipboard</button>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-smoothwall-logo">
      <span class="bottom-logo-powered">Powered by: </span><img class="smoothwall-logo" />
    </div>
  </body>
</html>

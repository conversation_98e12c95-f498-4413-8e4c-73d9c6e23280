<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Smoothwall Diagnostics Page</title>
    <link rel="icon" type="image/x-icon" href="../images/smoothwall-icon-256x256.ico" />
    <script src="diagnostics.js"></script>
    <link rel="stylesheet" href="diagnostics.css" />
  </head>

  <body>
    <div class="nav-bar">
      <div class="nav-logo">
        <img src="../images/smoothwall-logo.svg" height="37" />
        <span>Diagnostics</span>
      </div>
    </div>
    <div class="copied-bar" id="copied-bar">
      <span>Copied!</span>
    </div>

    <div class="main-50">
      <div class="title-grid">
        <div class="title-subgrid">
          <p class="title">Diagnostics</p>
        </div>
        <div class="subtitle-subgrid">
          <span class="subtitle">Please copy the diagnostic information below.</span>
        </div>
        <div class="button-subgrid flex flex-end">
          <button class="copy-button" id="copyButton">Copy to clipboard</button>
        </div>
      </div>
    </div>

    <div class="main-50">
      <div class="divider"></div>
    </div>

    <div class="main-65">
      <div class="row field-row">
        <div class="col-2 flex flex-center-start">
          <span class="section-header">Licensing</span>
        </div>
        <div class="col-10 flex flex-column">
          <span class="field-label">Licensing</span>
          <div class="flex">
            <input class="field-input" readonly="true" id="licensing" />
            <div class="flex flex-center-start">
              <!--                    <span class="field-message">Warning message, suspected fix when we know it to be suggested in this text.</span>-->
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="main-50">
      <div class="divider"></div>
    </div>

    <div class="main-65">
      <div class="row field-row">
        <div class="col-2 flex flex-center-start">
          <span class="section-header">Real-time Connection</span>
        </div>
        <div class="col-10 flex flex-column">
          <span class="field-label">Connection State</span>
          <div class="flex">
            <input class="field-input" readonly="true" id="connectionState" />
            <div class="flex flex-center-start">
              <!--                    <span class="field-message">Warning message, suspected fix when we know it to be suggested in this text.</span>-->
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="main-50">
      <div class="divider"></div>
    </div>

    <div class="main-65">
      <div class="row field-row">
        <div class="col-2 flex flex-center-start">
          <span class="section-header">Filtering Information</span>
        </div>
        <div class="col-10 flex flex-column">
          <span class="field-label">Filter Mode</span>
          <div class="flex">
            <input class="field-input" readonly="true" id="filterMode" />
            <div class="flex flex-center-start">
              <!--                    <span class="field-message">Warning message, suspected fix when we know it to be suggested in this text.</span>-->
            </div>
          </div>
        </div>
      </div>
      <div class="row field-row">
        <div class="col-2 flex flex-center-start">
          <span class="section-header"></span>
        </div>
        <div class="col-10 flex flex-column">
          <span class="field-label">Policy Name</span>
          <div class="flex">
            <input class="field-input field-input" readonly="true" id="policyName" />
            <div class="flex flex-center-start">
              <!--                    <span class="field-message">Warning message, suspected fix when we know it to be suggested in this text.</span>-->
            </div>
          </div>
        </div>
      </div>
      <div class="row field-row">
        <div class="col-2 flex flex-center-start">
          <span class="section-header"></span>
        </div>
        <div class="col-10 flex flex-column">
          <span class="field-label">Policy Publish Timestamp</span>
          <div class="flex">
            <input class="field-input" readonly="true" id="generated" />
            <div class="flex flex-center-start">
              <!--                    <span class="field-message">Warning message, suspected fix when we know it to be suggested in this text.</span>-->
            </div>
          </div>
        </div>
      </div>
      <div class="row field-row">
        <div class="col-2 flex flex-center-start">
          <span class="section-header"></span>
        </div>
        <div class="col-10 flex flex-column">
          <span class="field-label">Blocklist Version - Date time</span>
          <div class="flex">
            <input class="field-input" readonly="true" id="blocklistVersionDatetime" />
            <div class="flex flex-center-start">
              <!--                    <div class="field-message-icon"><span>!</span></div>-->
              <!--                    <span class="field-message">Warning message, suspected fix when we know it to be suggested in this text.</span>-->
            </div>
          </div>
        </div>
      </div>
      <div class="row field-row">
        <div class="col-2 flex flex-center-start">
          <span class="section-header"></span>
        </div>
        <div class="col-10 flex flex-column">
          <span class="field-label">Blocklist Version - Epoch time</span>
          <div class="flex">
            <input class="field-input" readonly="true" id="blocklistVersionEpoch" />
            <div class="flex flex-center-start">
              <!--                    <span class="field-message">Warning message, suspected fix when we know it to be suggested in this text.</span>-->
            </div>
          </div>
        </div>
      </div>
      <div class="row field-row">
        <div class="col-2 flex flex-center-start">
          <span class="section-header"></span>
        </div>
        <div class="col-10 flex flex-column">
          <span class="field-label">Last sent logs since startup</span>
          <div class="flex">
            <input class="field-input" readonly="true" id="lastSentLogs" />
            <div class="flex flex-center-start">
              <!--                    <span class="field-message">Warning message, suspected fix when we know it to be suggested in this text.</span>-->
            </div>
          </div>
        </div>
      </div>
      <div class="row field-row">
        <div class="col-2 flex flex-center-start">
          <span class="section-header"></span>
        </div>
        <div class="col-10 flex flex-column">
          <span class="field-label">Last Secret Knock</span>
          <div class="flex">
            <input class="field-input" readonly="true" id="secretKnockDetails" />
            <div class="flex flex-center-start">
              <!--                    <span class="field-message">Warning message, suspected fix when we know it to be suggested in this text.</span>-->
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="main-50">
      <div class="divider"></div>
    </div>

    <div class="main-65">
      <div class="row field-row">
        <div class="col-2 flex flex-center-start">
          <span class="section-header">User Information</span>
        </div>
        <div class="col-10 flex flex-column">
          <span class="field-label">Username</span>
          <div class="flex">
            <input class="field-input" readonly="true" id="username" />
            <div class="flex flex-center-start">
              <!--                    <span class="field-message">Warning message, suspected fix when we know it to be suggested in this text.</span>-->
            </div>
          </div>
        </div>
      </div>
      <div class="row field-row">
        <div class="col-2 flex flex-center-start">
          <span class="section-header"></span>
        </div>
        <div class="col-10 flex flex-column">
          <span class="field-label">Tenant</span>
          <div class="flex">
            <input class="field-input" readonly="true" id="tenant" />
            <div class="flex flex-center-start">
              <!--                    <span class="field-message">Warning message, suspected fix when we know it to be suggested in this text.</span>-->
            </div>
          </div>
        </div>
      </div>
      <div class="row field-row">
        <div class="col-2 flex flex-center-start">
          <span class="section-header"></span>
        </div>
        <div class="col-10 flex flex-column">
          <span class="field-label">IP Address (Public)</span>
          <div class="flex">
            <input class="field-input" readonly="true" id="ipAddressPublic" />
            <div class="flex flex-center-start">
              <!--                    <span class="field-message">Warning message, suspected fix when we know it to be suggested in this text.</span>-->
            </div>
          </div>
        </div>
      </div>
      <div class="row field-row">
        <div class="col-2 flex flex-center-start">
          <span class="section-header"></span>
        </div>
        <div class="col-10 flex flex-column">
          <span class="field-label">IP Address (Private)</span>
          <div class="flex">
            <input class="field-input" readonly="true" id="ipAddressPrivate" />
            <div class="flex flex-center-start">
              <!--                    <span class="field-message">Warning message, suspected fix when we know it to be suggested in this text.</span>-->
            </div>
          </div>
        </div>
      </div>
      <div class="row field-row">
        <div class="col-2 flex flex-center-start">
          <span class="section-header"></span>
        </div>
        <div class="col-10 flex flex-column">
          <span class="field-label">Outside Premises</span>
          <div class="flex">
            <input class="field-input" readonly="true" id="outsidePremisesID" />
            <div class="flex flex-center-start">
              <a
                id="outsidePremisesInfoID"
                style="visibility: hidden"
                href="https://kb.smoothwall.com/hc/en-us/articles/360011811860-Applying-different-policies-to-Cloud-Filter-users-when-offsite"
                target="_blank"
                >Learn more</a
              >
            </div>
          </div>
        </div>
      </div>
      <div class="row field-row">
        <div class="col-2 flex flex-center-start">
          <span class="section-header"></span>
        </div>
        <div class="col-10 flex flex-column">
          <span class="field-label">Directory Groups</span>
          <div class="flex">
            <textarea
              class="field-input field-input-long field-input-deep"
              readonly="true"
              rows="5"
              id="directoryGroups"
            ></textarea>
            <div class="flex flex-center-start">
              <!--                    <span class="field-message">Warning message, suspected fix when we know it to be suggested in this text.</span>-->
            </div>
          </div>
        </div>
      </div>
      <div class="row field-row">
        <div class="col-2 flex flex-center-start">
          <span class="section-header"></span>
        </div>
        <div class="col-10 flex flex-column">
          <span class="field-label">Group Mappings</span>
          <div class="flex">
            <textarea
              class="field-input field-input-long field-input-deep"
              readonly="true"
              rows="5"
              id="mappedGroups"
            ></textarea>
            <div class="flex flex-center-start">
              <!--                    <span class="field-message">Warning message, suspected fix when we know it to be suggested in this text.</span>-->
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>

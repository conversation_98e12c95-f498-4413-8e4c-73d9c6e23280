import BlockPageDetails from 'models/BlockPageDetails';

export default class BlockPage {
  constructor(encodedParams: URLSearchParams) {
    // Actual params are base64 encoded to prevent abuse of block page.
    this._params = new URLSearchParams(decodeURI(atob(encodedParams.get('data') ?? '')));
    if (this._details.timestamp !== '') this._assignDetailsToPage(this._details);
  }

  public readonly addListeners = (): void => {
    this._copy.button.addEventListener('click', () => {
      this._copyToClipboard(this._details);
    });
  };

  private readonly _assignDetailsToPage = (details: BlockPageDetails): void => {
    this._fields.reasons.style.display = 'unset';
    this._fields.username.innerHTML = details.username;
    this._fields.timestamp.innerHTML = details.timestamp;
    this._fields.url.innerHTML = details.url;
    this._fields.location.innerHTML = details.location.join(', ');
    this._fields.categories.innerHTML = details.categories.join(', ');
    this._fields.groups.innerHTML = details.groups.map((group) => group.name).join(', ');
  };

  private readonly _copyToClipboard = (details: BlockPageDetails): void => {
    const text = JSON.stringify(details, undefined, 4);
    const temp = document.createElement('textarea');
    document.body.appendChild(temp);
    temp.value = text;
    temp.select();
    document.execCommand('copy');
    document.body.removeChild(temp);

    this._copy.indicator.style.opacity = '1';
    setTimeout(() => {
      this._copy.indicator.style.opacity = '0';
    }, 2000);
  };

  /**
   * Accessor to convert decoded URL parameters into BlockPageDetails interface.
   */
  private get _details(): BlockPageDetails {
    return {
      username: this._params.get('username') ?? '',
      timestamp: this._params.get('timestamp') ?? '',
      url: this._params.get('url') ?? '',
      location: JSON.parse(this._params.get('location') ?? '{}'),
      categories: JSON.parse(this._params.get('categories') ?? '{}'),
      groups: JSON.parse(this._params.get('groups') ?? '{}'),
    };
  }

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Decoded URL Search Parameters, optionally containg details.
   */
  private readonly _params: URLSearchParams;

  /**
   * HTML elements for copying, and indicating that copy has been made.
   */
  private readonly _copy = {
    button: document.getElementById('copyButton') as HTMLElement,
    indicator: document.getElementById('copied-bar') as HTMLElement,
  };

  /**
   * Fields on block page to be populated by details, if they are provided.
   */
  private readonly _fields = {
    reasons: document.getElementById('blocked-reasons') as HTMLInputElement,
    username: document.getElementById('username') as HTMLInputElement,
    timestamp: document.getElementById('time') as HTMLInputElement,
    url: document.getElementById('url') as HTMLInputElement,
    location: document.getElementById('location') as HTMLInputElement,
    categories: document.getElementById('categories') as HTMLInputElement,
    groups: document.getElementById('groups') as HTMLInputElement,
  };
}

document.addEventListener('DOMContentLoaded', (event) => {
  const blockPage = new BlockPage(new URLSearchParams(document.location.search));
  blockPage.addListeners();
});

<!DOCTYPE html>
<html class="main-html" lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Smoothwall Real Time Filtering Logs</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="../images/smoothwall-icon-256x256.ico" />
    <script src="realtime-filtering-logs.js"></script>
    <link rel="stylesheet" href="realtime-filtering-logs.css" />
  </head>

  <body>
    <!-- Username and groups information-->
    <div class="user-info">
      <div class="username">
        <span class="lock-text">Username: </span><span id="username" class="lock-text"></span>
      </div>
      <div class="groups">
        <span class="lock-text">Groups: </span><span id="groupinfo" class="lock-text"></span>
      </div>
    </div>

    <!-- Search input-->
    <div class="search">
      <input type="text" class="search-input" id="search-bar" placeholder="Search" />
      <span class="refresh-wrapper">
        <input class="toggle" id="refresh-toggle" type="checkbox" checked />
      </span>
    </div>

    <!-- Main table-->
    <div>
      <table id="main_table" cellpadding="10">
        <!-- Column headings-->
        <thead>
          <tr>
            <th class="lock-text time-header">
              <div class="align-arrows" id="time-sort">
                <p>Timestamp</p>
                <div id="time-arrow" class="arrow-down"></div>
              </div>
            </th>
            <th class="lock-text url-header">
              <div class="align-arrows" id="url-sort">
                <p>URL</p>
                <div id="url-arrow" class="arrow-right"></div>
              </div>
            </th>
            <th class="lock-text categories-header">
              <div class="align-arrows" id="categories-sort">
                <p>Categories</p>
                <div id="categories-arrow" class="arrow-right"></div>
              </div>
            </th>
            <th class="lock-text actions-header">
              <div class="align-arrows" id="actions-sort">
                <p>Action</p>
                <div id="actions-arrow" class="arrow-right"></div>
              </div>
            </th>
            <th class="lock-text ruleId-header">
              <div class="align-arrows" id="ruleId-sort">
                <p>Matched Rule</p>
                <div id="ruleId-arrow" class="arrow-right"></div>
              </div>
            </th>
          </tr>
        </thead>

        <!-- Table body rows-->
        <tbody id="main-table-body"></tbody>
      </table>
    </div>
  </body>
</html>

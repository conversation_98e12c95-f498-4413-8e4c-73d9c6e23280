@import url('https://fonts.googleapis.com/css?family=Roboto:300,400,500');

.main-html {
  height: 100%;
}

body {
  height: 100%;
  padding: 20px 10px 20px 10px;
  margin: 0;
  text-align: center;
  background: #f2f2f2;
  box-sizing: border-box;
}

.smoothwall-logo {
  height: 3vh;
  content: url('../images/smoothwall-logo.svg');
  position: fixed;
  bottom: 5vh;
  right: 5vh;
}

.lock-object {
  height: 40vh;
  content: url('../images/block-icon.svg');
}

/* Text styling */
.lock-text {
  font-size: 1rem;
  font-weight: 500;
  font-family: Roboto, 'Helvetica Neue', sans-serif;
  color: #5a5a5a;
}

.lock-text-small {
  font-size: 11pt;
  font-weight: 300;
  font-family: Roboto, 'Helvetica Neue', sans-serif;
  color: #5a5a5a;
}

.share-button {
  font-family: Roboto, 'Helvetica Neue', sans-serif;
  color: #fff;
  background-color: #00bbb4;
  border-color: #00bbb4;
  display: inline-block;
  cursor: pointer;
  font-size: 14px;
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border-radius: 1px;
  border: 0;
  padding: 2px 32px;
  line-height: 36px;
  margin-top: 16px;
}

/* Username and groups info */
.user-info {
  align-items: left;
  align-content: left;
  text-align: left;
  padding-left: 4px;
}

.username,
.groups {
  margin-bottom: 12px;
}

/* Search box input */
.search {
  text-align: left;
  display: flex;
  align-items: center;
}

.search-input {
  margin-top: 12px;
  border: none;
  padding: 0 5px 0 22px;
  height: 30px;
  color: #5a5a5a;
  font-size: 1rem;
  line-height: 30px;
  border-radius: 25px;
  -moz-border-radius: 25px;
  -webkit-border-radius: 25px;
  background: #ffffff;
  margin-bottom: 12px;
}

.search-input:focus {
  outline: none;
}

.cat-data-element {
  white-space: pre-wrap;
}

table {
  table-layout: fixed;
  width: 100%;
  border-collapse: collapse;
}

tr {
  background-color: #ffffff;
  border: 2px solid #f2f2f2;
  text-align: left;
}

tr td {
  width: 35%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

td {
  padding: 8px;
}

/* Heading widths */
.time-header {
  width: 10%;
}

.categories-header {
  width: 25%;
}

.actions-header {
  width: 5%;
}

.url-header {
  width: 25%;
}

.ruleId-header {
  width: 5%;
}

@media only screen and (max-width: 1100px) {
  .actions-header {
    width: 7%;
  }
}

@media only screen and (max-width: 885px) {
  .url-header {
    width: 20%;
  }
}

@media only screen and (max-width: 700px) {
  .time-header {
    width: 12%;
  }

  .categories-header {
    width: 13%;
  }

  .actions-header {
    width: 9%;
  }

  .url-header {
    width: 15%;
  }
}

th div p {
  user-select: none;
}

th div:hover {
  cursor: pointer;
}

.align-arrows {
  display: flex;
  align-items: center;
}

.arrow-up {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 5px solid #5a5a5a;
  margin-left: 8px;
}

.arrow-down {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #5a5a5a;
  margin-left: 8px;
}

.arrow-right {
  width: 0;
  height: 0;
  border-left: 5px solid #5a5a5a;
  border-bottom: 5px solid transparent;
  border-top: 5px solid transparent;
  margin-left: 8px;
}

/* URL links */
a {
  color: #5a5a5a;
  text-decoration: none;
}

a:hover {
  color: cornflowerblue;
  text-decoration: none;
}

/* Allow or deny */
.allow {
  color: #8bc34a;
}

.deny {
  color: #ff5252;
}

.toggle {
  float: right;
  -webkit-appearance: none;
  appearance: none;
  width: 62px;
  height: 32px;
  position: relative;
  border-radius: 50px;
  overflow: hidden;
  outline: none;
  border: none;
  cursor: pointer;
  background-color: #707070;
  transition: background-color ease 0.3s;
}

.toggle:before {
  content: 'live off';
  display: block;
  position: absolute;
  z-index: 2;
  width: 28px;
  height: 28px;
  background: #fff;
  left: 2px;
  top: 2px;
  border-radius: 50%;
  font: 10px/28px Helvetica;
  text-transform: uppercase;
  font-weight: bold;
  text-indent: -26px;
  word-spacing: 33px;
  color: #fff;
  text-shadow: -1px -1px rgba(0, 0, 0, 0.15);
  white-space: nowrap;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  transition: all cubic-bezier(0.3, 1.5, 0.7, 1) 0.3s;
}

.toggle:checked {
  background-color: #4cd964;
}

.toggle:checked:before {
  left: 32px;
}

.refresh-wrapper {
  flex: auto;
}

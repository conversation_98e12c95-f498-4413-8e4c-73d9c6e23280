@import url('https://fonts.googleapis.com/css?family=Roboto:300,400,500');

.main-html {
  height: 100%;
}

html {
  font-family: Roboto, 'Helvetica Neue', sans-serif;
}

body {
  height: 100%;
  margin: 0;
  text-align: center;
  background: #005677;
  box-sizing: border-box;
}

.smoothwall-logo {
  float: right;
  height: 20px;
  content: url(../images/smoothwall-logo.svg);
  margin-right: 10px;
}

.lock-object {
  height: 400px;
  content: url(../images/block-icon.svg);
}

.lock-text {
  color: #ffffff;
}

.lock-text h1 {
  text-align: center;
  font-weight: 300;
  font-size: 3em;
}

.lock-text-small {
  margin-top: 1vh;
  font-weight: 400;
  color: #ffffff;
}

.lock-section {
  width: 40%;
  margin-top: auto;
  margin-bottom: auto;
}

.filtering-info {
  width: 50%;
  margin-left: auto;
  margin-right: 30px;
  display: none;
  margin-top: auto;
  margin-bottom: auto;
}

.info-wrapper {
  justify-content: left;
  display: flex;
  padding-top: 20px;
  padding-bottom: 20px;
}

@media only screen and (max-width: 1920px) {
  .info-wrapper {
    padding-top: 10px;
    padding-bottom: 10px;
  }
}

.block-reason {
  font-weight: 300;
  color: #ffffff;
  display: inline-flex;
  align-items: center;
  text-align: left;
  font-size: medium;
  word-break: break-all;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

.flexbox {
  display: flex;
  justify-content: center;
  margin-left: 30px;
  margin-right: 30px;
  height: 100%;
}

.block-label {
  font-weight: 500;
  color: #ffffff;
  padding-right: 10px;
  min-width: 12rem;
  font-size: large;
  text-align: left;
}

.user-timestamp {
}

h3 {
  text-align: left;
  color: #ffffff;
  font-size: 2em;
  font-weight: 400;
}

.clipboard-copy {
  margin-right: auto;
  width: 100px;
  height: 40px;
  background-color: #00bbb4;
  color: #ffffff;
  border: none !important;
  border-radius: 4px;
  outline: none;
  cursor: pointer;
  width: fit-content;
}

.button-wrapper {
  padding-top: 10px;
  display: flex;
}

.copied-bar {
  background: #d2d2d2;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: black;
  opacity: 0%;
  transition: opacity 1s;
  position: absolute;
  top: 0;
}

.copied-bar-visible {
  opacity: 100%;
  height: 25px;
}

.lock-reasons {
  padding: 10px;
}

.bottom-smoothwall-logo {
  position: absolute;
  bottom: 0;
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.bottom-logo-powered {
  margin-left: auto;
  padding-right: 6px;
  color: #ffffff;
}

{"type": "object", "properties": {"MaxPolicyDownloadDelay": {"title": "Maximum policy download delay (seconds)", "description": "The maximum delay in seconds before downloading a policy when changes are detected. Set to 0 to disable delays. Default is 15 seconds.", "type": "integer"}, "DocumentHiderExclusions": {"title": "Document hider exclusions", "description": "A comma separated list of urls that should be excluded from showing the document hider.", "type": "string"}, "DisableOffScreenDocument": {"title": "Disable off screen document", "description": "A boolean for disabling the off screen document. If set to true then the off screen document will never be used.", "type": "boolean"}, "MaxAccessLogBufferSizeInBytes": {"title": "Max access log buffer size (bytes)", "description": "The size limit for the access logs storage buffer. The number provided needs to be in bytes.", "type": "integer"}, "DisableAdvancedYoutubeScanning": {"title": "Disable advanced youtube scanning", "description": "If set to true then blocked videos and thumbnails will not be removed from the page. The videos will still be filtered as normal if clicked on or browsed to.", "type": "boolean"}, "Smoothwall": {"type": "object", "properties": {"Serial": {"title": "Customer serial", "description": "Identification key provided by Smoothwall.", "type": "string"}, "TenantId": {"title": "Tenant ID", "description": "Identification key for the tenant.", "type": "string"}, "ForceOS": {"title": "Force OS", "description": "Flag used for development to force the current OS(posible values: chromeos).", "type": "string"}}}, "DisableFilteringOnUnmanagedChromebooks": {"type": "boolean"}, "DisableMiniFilter": {"type": "boolean"}, "DefaultUser": {"description": "User email address to use as a fall-back if the information is not available from the browser (e.g. no user is logged-in).", "type": "string"}}}
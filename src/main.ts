/* eslint-disable no-void */

// IMPORTANT //
// The mv2 shim must be the first import.
// It needs to be executed before any chrome api calls can be made.
import 'shim/mv2-shim';
// --------- //

// Ensure we have support for abortable fetch API requests.
// This is needed to support very old Chromebooks running Chrome version ~64.
import 'abortcontroller-polyfill/dist/polyfill-patch-fetch';

import FilterApplication from 'FilterApplication';
import { resetBadgeTitle } from 'utilities/Helpers';

declare const EXPOSE_APP_IN_CONSOLE: boolean;

console.log('Extension started at: ' + new Date().toISOString());

// Badge text is overlaid on the extension icon in the toolbar. Ensure no such text is lingering
//  from a previous invocation.
// Show our version number as a tooltip on the extension icon in the toolbar.
void resetBadgeTitle();

const filterApplication = new FilterApplication();

if (EXPOSE_APP_IN_CONSOLE) {
  // Make the filter application accessible in the extension console.
  (globalThis as any).filterApplication = filterApplication;
}

// All required event handlers should be added synchronously in this call:
filterApplication.addListeners();

// Any intensive or asynchronous initialisation should happen in this call:
filterApplication.start().catch((e: any): void => {
  console.error('An error occurred during launch of Filter Application. ', e);
});

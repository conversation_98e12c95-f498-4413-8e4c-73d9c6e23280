/**
 * Identifies the types of message sent to/from the Smoothwall native agent.
 * A complete list of types and payload structures are documented here:
 *
 * https://familyzone.atlassian.net/wiki/spaces/UC/pages/2694339822671/Types+mapping
 *
 * This corresponds to the top-level "mt" (message type) property of a message.
 */
export enum SmoothwallAgentMessageType {
  // Extension -> native client: Request provisioning and config data.
  browserInitRequest = 1,

  // Native client -> extension: Payload contains provisioning and config data.
  browserInitData = 2,

  // Native client -> extension: Customer is not licensed; disable all filtering.
  notLicensed = 7,

  // Native client -> extension: Payload contains IP addresses assigned to the device.
  ipAddresses = 110,
}

/**
 * Describes the essential structure of a message sent to a from the Smoothwall native agent.
 *
 * @see SmoothwallAgentConnection
 * @see https://familyzone.atlassian.net/wiki/spaces/UC/pages/2694339822336/Message+events+specification
 */
export default interface SmoothwallAgentMessage {
  /**
   * The message type.
   *
   * @see SmoothwallAgentMessageType
   */
  mt: number;

  /**
   * The data associated with this message.
   * The structure depends on the message type.
   */
  d: any;

  /**
   * The channel by which the message is sent.
   * This should always be 2.
   */
  c: number;
}

/**
 * Describes the data payload of an IP addresses message received from the Smoothwall agent.
 *
 * @see SmoothwallAgentMessageType.ipAddresses
 */
export interface SmoothwallAgentIpAddressesMessageData {
  /**
   * The local (aka private) IP addresses assigned to this device, represented as strings.
   * This may include IPv4 and IPv6 addresses.
   */
  localIpAddresses: string[];
}

import {
  getDeviceSerialNumber,
  getFromStorage,
  getProfileUserInfo,
} from 'utilities/PromiseWrappers';
import { EnrolmentDetails } from '@connect/common-idl/common_js/companion/messages_pb';
import ManagedPolicy from 'models/ManagedPolicy';
import SerialId from 'models/SerialId';
import TenantId from 'models/TenantId';
import * as uuid from 'uuid';
import { areArraysEqual } from 'utilities/Helpers';
import StorageService from 'services/StorageService';

/**
 * Stores the customer and user data required for running the software.
 */
export default class ProvisioningInfo {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  public constructor(
    serialId: SerialId,
    tenantId: TenantId | undefined,
    hardwareId: string,
    deviceName: string,
    agentVersion: string,
    user: string,
    domain: string,
    localGroups: string[],
    validTenantUuid: boolean,
  ) {
    this.serialId = serialId;
    this.tenantId = tenantId;
    this.hardwareId = hardwareId;
    this.deviceName = deviceName;
    this.agentVersion = agentVersion;
    this.user = user;
    this.domain = domain;
    this.localGroups = localGroups;
    this.validTenantUuid = validTenantUuid;
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Get the names of the user's local groups as a comma-separated string.
   *
   * @returns Returns a string containing the local group names, separated by commas.
   */
  public readonly getLocalGroupsAsCommaSeparatedString = (): string => {
    return this.localGroups.join(',');
  };

  /**
   * Check if this instance contains all the same data as another instance.
   *
   * @param other The other instance to compare against.
   * @returns True if the instances contain the same data, or false otherwise. Always returns false
   *  if the parameter is undefined.
   */
  public readonly equals = (other: ProvisioningInfo | undefined): boolean => {
    return (
      other !== undefined &&
      this.hasSameCoreData(other) &&
      areArraysEqual(this.localGroups, other.localGroups)
    );
  };

  /**
   * Check if this instance contains the same core data as another instance.
   * This checks that serial, tenant, hardware ID, user, and domain are the same.
   * This is similar to equals(), but ignores local groups.
   * The user and domain comparisons are case insensitive.
   *
   * @param other The other instance to compare against.
   * @returns True if the instances contain equivalent core data, or false otherwise. Always returns
   *  false if the parameter is undefined.
   *
   * @todo Unit test this.
   */
  public readonly hasSameCoreData = (other: ProvisioningInfo | undefined): boolean => {
    return (
      other !== undefined &&
      this.serialId.equals(other.serialId) &&
      this.tenantId?.toString() === other.tenantId?.toString() &&
      this.hardwareId === other.hardwareId &&
      this.user.toLowerCase() === other.user.toLowerCase() &&
      this.domain.toLowerCase() === other.domain.toLowerCase()
    );
  };

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Create and populate an instance of this class from information provided by the browser.
   * This is only possible in standalone mode, and only works if a user is logged-in or a default
   *  user has been specified in the managed policy.
   *
   * @param managedPolicy The managed policy which has previously been loaded from the browser. This
   *  must contain a valid serial ID.
   * @returns Returns a promise which resolves to a new populated instance of this class if all the
   *  data was loaded and validated successfully. The promise will reject with an Error if any
   *  required data is missing or invalid.
   *
   * @todo Use a custom error type so that the problem can be reflected in the UI?
   */
  public static readonly loadStandalone = async (
    managedPolicy: ManagedPolicy,
  ): Promise<ProvisioningInfo> => {
    if (managedPolicy.Smoothwall?.Serial === undefined) {
      throw new Error('Serial ID is missing from policy.');
    }
    const serialId = new SerialId(managedPolicy.Smoothwall.Serial);

    let tenantId: TenantId | undefined;
    let validTenantUuid = true;
    if (
      managedPolicy.Smoothwall.TenantId !== undefined &&
      managedPolicy.Smoothwall.TenantId !== ''
    ) {
      try {
        tenantId = new TenantId(managedPolicy.Smoothwall.TenantId);
      } catch {
        // We can continue running without a tenant id. Flag that the id provisioned was invalid.
        validTenantUuid = false;
        console.warn(
          `The provisioned tenant id is not a valid UUID. ${managedPolicy.Smoothwall.TenantId}`,
        );
      }
    }

    const hardwareId = await ProvisioningInfo._loadHardwareId();

    const user = await ProvisioningInfo._loadUser(managedPolicy);

    // It's possible for us to fallback to the device serial or a default user. In that case we won't know the domain.
    let domain = 'unknown';
    if (user.includes('@')) {
      domain = user.split('@')[1] ?? '';
    }

    return new ProvisioningInfo(
      serialId,
      tenantId,
      hardwareId,
      '',
      '',
      user,
      domain,
      [],
      validTenantUuid,
    );
  };

  /**
   * Create and populate an instance from the information in the given storage service, if possible.
   * This assumes the data has already been loaded into memory from the storage service.
   * This is typically used for reading stored data from a cache.
   *
   * @param storageService The storage service to load provisioning info from.
   * @returns A populated instance of this class, if the required data was available in the storage
   *  service instance. Returns undefined if not all the required data was there.
   * @throws {Error} Some of the data was present but invalid, e.g. an invalid serial.
   */
  public static readonly loadFromStorage = (
    storageService: StorageService,
  ): ProvisioningInfo | undefined => {
    const rawSerialId = storageService.getString('serialId');
    const rawTenantId = storageService.getString('tenantId');
    const hardwareId = storageService.getString('hardwareId');
    const deviceName = storageService.getString('deviceName') ?? '';
    const agentVersion =
      storageService.getString('agentVersion') ?? storageService.getString('userAgent') ?? '';
    const user = storageService.getString('user');
    const domain = storageService.getString('domain');
    const localGroups = storageService.get('localGroups') ?? [];
    const validTenantUuid = storageService.getBoolean('validTenantUuid') ?? true;

    // Serial ID, hardware ID, and user are essential and must not be empty. If they are missing
    //  then assume there's no data to load.
    // Everything else is optional.
    if (rawSerialId === undefined || hardwareId === undefined || user === undefined) {
      return undefined;
    }

    if (hardwareId === '') {
      throw new Error('Invalid hardware ID found in storage. It must not be empty.');
    }

    if (user === '') {
      throw new Error('Invalid user identifier found in storage. It must not be empty.');
    }

    // Ensure local groups is an array of strings (or an empty array).
    if (!Array.isArray(localGroups) || !localGroups.every((s) => typeof s === 'string')) {
      throw new Error(
        'Invalid local groups found in storage. Expected an array of zero or more strings.',
      );
    }

    // Serial and tenant IDs will be validated by their constructors:
    return new ProvisioningInfo(
      new SerialId(rawSerialId),
      rawTenantId === undefined || rawTenantId === '' ? undefined : new TenantId(rawTenantId),
      hardwareId,
      deviceName,
      agentVersion,
      user,
      domain ?? '',
      localGroups,
      validTenantUuid,
    );
  };

  /**
   * Write the provisioning info to a storage service.
   * This is typically to write data to a cache for future reference.
   * This does not explicitly save the storage to disk. It only writes values to memory. It is the
   *  caller's responsibility to manually save the values, or ensure auto-save was enabled before
   *  calling this.
   *
   * @param storageService The storage service instance to write provisioning info to.
   */
  public readonly writeToStorage = (storageService: StorageService): void => {
    storageService.set('serialId', this.serialId.toString());
    storageService.set('hardwareId', this.hardwareId);
    storageService.set('deviceName', this.deviceName);
    storageService.set('agentVersion', this.agentVersion);
    storageService.set('user', this.user);
    storageService.set('domain', this.domain);
    storageService.set('localGroups', this.localGroups);
    storageService.set('validTenantUuid', this.validTenantUuid);

    // The agentVersion property was previously known as userAgent.
    storageService.delete('userAgent');

    if (this.tenantId === undefined) {
      // Ensure a previous tenant ID isn't leftover.
      storageService.delete('tenantId');
    } else {
      storageService.set('tenantId', this.tenantId.toString());
    }
  };

  /**
   * Populate provisioning info using an initialisation message received from the Smoothwall agent.
   * The browser init message is sent by the native daemon to the extension to provide the
   *  provisioning information.
   * Note that the payload is designed to contain an entire IoT device twin, with our provisioning
   *  data nested quite deep inside it. This is for legacy reasons, and will change at some point.
   *
   * @param data The data payload of the browser init message. This should be an object.
   * @returns An instance of ProvisioningInfo, populated from the message payload.
   * @throws {Error} Required data was missing, or any data was invalid.
   */
  public static readonly loadFromBrowserInitPayload = (data: any): ProvisioningInfo => {
    if (typeof data !== 'object' || data === null) {
      throw new Error('Expected payload to be an object.');
    }

    const identifiers = data?.reported?.identifiers;
    const userSession = data?.cldflt?.userSession;

    // As a sanity-check, ensure we can find the serial.
    // We don't bother with in-depth validation as we trust the data sent from the daemon.
    if (typeof identifiers?.serial !== 'string') {
      throw new Error('Cannot find serial in payload.');
    }

    let tenantId: TenantId | undefined;
    let validTenantUuid = true;
    if (identifiers.tenantId !== undefined && identifiers.tenantId !== '') {
      try {
        tenantId = new TenantId(identifiers.tenantId);
      } catch {
        // We can continue running without a tenant id. Flag that the id provisioned was invalid.
        validTenantUuid = false;
        console.warn(
          `The provisioned tenant id is not a valid UUID. ${identifiers.tenantId as string}`,
        );
      }
    }

    // Use the User Principal Name if it's specified and Azure AD is enabled. Fall-back on the local
    //  username otherwise.
    let user =
      userSession.userName !== undefined && userSession.userName !== ''
        ? userSession.userName
        : 'unknown-user';
    if (
      data.cldflt.enableAzureAd === true &&
      userSession?.userPrincipalName !== undefined &&
      userSession?.userPrincipalName !== ''
    ) {
      user = userSession.userPrincipalName;
    }

    return new ProvisioningInfo(
      new SerialId(identifiers.serial),
      tenantId,
      identifiers.hardwareId ?? '',
      '',
      '',
      user,
      userSession?.domain,
      userSession?.groups.split(',') ?? [],
      validTenantUuid,
    );
  };

  /**
   * Populate provisioning info using an enrolment message received from the Qoria agent.
   *
   * @param enrolmentDetails The enrolment details received from the Qoria agent.
   * @returns A provisioning info instance populated from the enrolment message.
   * @throws {Error} The provisioning info was invalid.
   */
  public static readonly loadProvisioningInfoFromEnrolmentDetails = (
    enrolmentDetails: EnrolmentDetails.AsObject,
  ): ProvisioningInfo => {
    if (enrolmentDetails.enrolmentinformation == null) {
      throw new Error('Missing enrolment information.');
    }

    // We always need a valid serial ID.
    const rawSerialId = enrolmentDetails.enrolmentinformation.customerId ?? '';
    if (rawSerialId === '') {
      throw new Error('Missing serial ID.');
    }
    const serialId = new SerialId(rawSerialId);

    // We always need a username, but will use a hard-coded fall-back if one isn't provided.
    // Currently, we always convert it to lowercase so that we can more easily do case-insensitive
    //  comparisons against things like Directory. In future, we should keep the original.
    const username = (
      enrolmentDetails.enrolmentinformation?.userInformation?.username ??
      enrolmentDetails.enrolmentinformation?.userInformation?.usernameRaw ??
      'unknown-user'
    ).toLowerCase();
    const domain = ProvisioningInfo.extractDomainFromUsername(username);

    // If the tenant ID is not a valid UUID, then continue as though there was no tenant ID.
    // We need to keep a flag indicating the validation failure so that it can be reported later.
    const rawTenantId = enrolmentDetails.enrolmentinformation?.tenantId ?? '';
    let validTenantId: boolean = true;
    let tenantId: TenantId | undefined;
    if (rawTenantId !== '') {
      try {
        tenantId = new TenantId(rawTenantId);
      } catch (e: any) {
        console.warn('Received invalid tenant ID from native agent.', rawTenantId);
        validTenantId = false;
      }
    }

    // For the hardware ID, use the MAC address which is first alphabetically. This is to ensure
    //  consistency in case there are multiple and they're provided in a variable order.
    const hardwareId = enrolmentDetails.macaddressesList.sort()?.[0] ?? '';

    return new ProvisioningInfo(
      serialId,
      tenantId,
      hardwareId,
      enrolmentDetails.devicename,
      enrolmentDetails.useragent,
      username,
      domain,
      // We don't get groups from the OS in Companion Mode. We'll get them from the cloud instead.
      [],
      validTenantId,
    );
  };

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Determine the hardware ID which should be used by this device.
   * This should only be used when running in standalone mode.
   * In Windows/macOS mode, this information is provided by the native daemon instead.
   */
  private static readonly _loadHardwareId = async (): Promise<string> => {
    // If we're running on an enterprise enrolled Chromebook then use the device serial.
    let hardwareId = await getDeviceSerialNumber();
    if (hardwareId !== '') {
      return hardwareId;
    }

    // If we've previously generated a random hardware ID then use it here.
    const key = ProvisioningInfo.hardwareIdStorageKey;
    try {
      hardwareId = (await getFromStorage(chrome.storage.local, key))?.[key];
      if (typeof hardwareId === 'string' && hardwareId !== '') {
        console.debug(`Using a previously generated hardware ID from local storage: ${hardwareId}`);
        return hardwareId;
      }
    } catch (e: any) {
      console.warn('Failed to retrieve a previously generated hardware ID from local storage: ', e);
    }

    // We don't have an existing hardware ID so generate a new one.
    hardwareId = uuid.v4();
    console.debug(`RuntimeConfiguration generated a new hardware ID: ${hardwareId}`);

    // Add the UUID to local storage so we can use it again on a future run. We deliberately don't
    //  wait for this to complete before returning as it's not essential for the product to work.
    chrome.storage.local.set({ [key]: hardwareId }, () => {
      if (chrome.runtime.lastError !== undefined) {
        console.warn(
          'RuntimeConfiguration failed to add new hardware ID to local storage: ',
          chrome.runtime.lastError,
        );
      }
    });

    return hardwareId;
  };

  /**
   * Determine the identifier of the current user, based on browser information.
   * This is only relevant in standalone mode. In native mode, we get the user information from the
   *  native daemon instead.
   *
   * @param managedPolicy The managed policy for the extension, which must already be loaded. It may
   *  optionally contain a default user as a fall-back. The default user will only be used if there
   *  is no user currently logged into the browser.
   * @return Returns a promise which resolves to the user's primary identifier, which should
   *  typically be an email address.
   */
  private static readonly _loadUser = async (managedPolicy: ManagedPolicy): Promise<string> => {
    const userInfo = await getProfileUserInfo();
    if (userInfo.email !== '') {
      return userInfo.email;
    }

    if (managedPolicy.DefaultUser !== undefined && managedPolicy.DefaultUser !== '') {
      console.info(
        `Could not retrieve user identity from browser. Falling back on default user specified in policy.`,
      );
      return managedPolicy.DefaultUser;
    }

    // The final fallback is to use the device serial if we can get it.
    const hardwareId = await getDeviceSerialNumber();
    if (hardwareId !== '') {
      console.info(
        'Could not find a default user in the policy. Falling back on the device serial.',
      );
      return hardwareId;
    }

    console.info('Could not find a suitable fallback for the username. Using the default value.');
    return 'unknown-user';
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Get the domain portion of a username.
   * If the username is structured like an email address or User Principal Name (i.e. "user@domain")
   *  then this will return everything after the last @ symbol.
   * If the username is structured like an NT style down-level name (i.e. "domain\\user") then this
   *  will return everything before the first backslash symbol.
   * Otherwise, it will return an empty string.
   *
   * @param username The username to extract the domain from.
   * @return The domain extracted from the username, or an empty string if there was no domain.
   */
  public static readonly extractDomainFromUsername = (username: string): string => {
    let separatorIndex = username.lastIndexOf('@');
    if (separatorIndex >= 0) {
      return username.substring(separatorIndex + 1);
    }

    separatorIndex = username.indexOf('\\');
    if (separatorIndex >= 0) {
      return username.substring(0, separatorIndex);
    }

    return '';
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The customer's UNCL serial ID.
   * The serial ID uniquely identifies the customer when contacting APIs etc.
   *
   * @note On Windows/macOS, we usually receive this information from the native daemon. When
   *  running in standalone mode, it's specified in the managed policy.
   */
  public readonly serialId: SerialId;

  /**
   * The customer's tenant ID, if they have one.
   * A tenant ID may be used in addition to the serial ID to distinguish different organisations
   *  (such as individual schools) within a large customer (such as a Multi Academy Trust). Smaller
   *  customers may not have a tenant ID at all.
   * This will be undefined if the customer doesn't have a tenant ID.
   *
   * @note On Windows/macOS, we usually receive this information from the native daemon. When
   *  running in standalone mode, it's specified in the managed policy.
   */
  public readonly tenantId?: TenantId;

  /**
   * A flag that indicates if the tenant id that has been provisioned is a valid UUID.
   * This being true does not mean that the id matches an existing tenant.
   * If a tenant id has not been provisioned this will be true.
   */
  public readonly validTenantUuid: boolean;

  /**
   * An arbitrary string that uniquely identifies the piece of hardware we're running on.
   *
   * @see _loadHardwareId()
   * @note On Windows/macOS, this is a MAC address which we usually receive from the native daemon.
   *  When running in standalone mode, we try to use the device serial ID, although this is only
   *  available in certain circumstances. Otherwise, we generate a random ID ourselves and store it
   *  in the browser.
   */
  public readonly hardwareId: string;

  /**
   * The device name is used as a unique identifier for the DMS registration
   * when in companion mode.
   *
   * When in companion mode. During companion mode the companion agent gets the
   * host name reported by the kernel and sends it to the agent as the device name.
   */
  public readonly deviceName: string;

  /**
   * The version string reported by the native agent connected to the extension, if applicable.
   *
   * This information is currently used to make sure that the extension DMS
   * registration use the same user agent as the native agent. In the future
   * when the extension uses the same DMS data as the agent we can use this
   * field for debugging or reporting mainly.
   */
  public readonly agentVersion: string;

  /**
   * The primary identifier of the person who is currently using this software.
   *
   * In standalone mode, we use the user's login name retrieved from the browser. In that case, it's
   *  always an email address.
   * In Windows/macOS mode, this information is provided by the native daemon. In that case, it will
   *  either be a down-level Active Directory username (i.e. "domain\user"), or an Azure Active
   *  Directory user ID ("user@domain").
   */
  public readonly user: string;

  /**
   * The domain which the current user belongs to.
   * This will typically be included as part of _user, but is specified separately here to ensure we
   *  avoid parsing issues.
   */
  public readonly domain: string;

  /**
   * The names of the local network groups which the current user belongs to.
   * This is only applicable when running in native mode. It will be an empty array otherwise.
   * It won't include groups from cloud directories. These either have to be looked-up separately,
   *  or
   *
   * @todo Is there a better place to put this? It's probably better to keep this information
   *  alongside the cloud group lookups done for standalone mode.
   */
  public readonly localGroups: string[];

  /**
   * The name of the local storage key used to persist a generated hardware ID.
   * This is only relevant in standalone mode.
   */
  public static readonly hardwareIdStorageKey = 'swHardwareId';
}

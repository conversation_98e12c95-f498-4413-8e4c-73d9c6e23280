import AgentConnection, { AgentConnectionState } from './AgentConnection';
import LinearBackOff from 'back-off-methods/LinearBackOff';

const connectionTimeoutDelay = 30000;
const linearBackOff = new LinearBackOff(5000, 30000, 0);

class DummyAgentConnection extends AgentConnection {
  // Override:
  protected readonly _openConnection = (): void => {
    // By default, simulate a connection which never succeeds.
    this._scheduleConnectionTimeout();
    this.numConnectionAttempts++;
  };

  // Override:
  protected readonly _closeConnection = (): void => {
    this._cancelConnectionTimeout();
  };

  public readonly simulateSuccessfulConnection = (): void => {
    this._cancelConnectionTimeout();
    this._cancelRetry();
    if (this._state !== AgentConnectionState.idle) {
      this._state = AgentConnectionState.connected;
    }
  };

  public readonly simulateFailedConnection = (): void => {
    this._cancelConnectionTimeout();
    this._scheduleRetry();
    if (this._state !== AgentConnectionState.idle) {
      this._state = AgentConnectionState.connecting;
    }
  };

  public numConnectionAttempts = 0;
}

describe('AgentConnection', () => {
  let dummyAgentConnection: DummyAgentConnection;

  beforeEach(() => {
    dummyAgentConnection = new DummyAgentConnection(connectionTimeoutDelay, linearBackOff);
  });

  afterEach(() => {
    dummyAgentConnection.disconnect();
    jest.clearAllTimers();
    jest.useRealTimers();
  });

  // -----------------------------------------------------------------------------------------------

  describe('constructor()', () => {
    it('does not open a connection', () => {
      expect(dummyAgentConnection.numConnectionAttempts).toEqual(0);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('state', () => {
    it('returns idle if connect has not been called', () => {
      expect(dummyAgentConnection.state).toEqual(AgentConnectionState.idle);
    });

    it('returns connecting if a connection attempt is in progress', () => {
      dummyAgentConnection.connect();
      expect(dummyAgentConnection.state).toEqual(AgentConnectionState.connecting);
    });

    it('returns connected if a connection was successful', () => {
      dummyAgentConnection.connect();
      dummyAgentConnection.simulateSuccessfulConnection();
      expect(dummyAgentConnection.state).toEqual(AgentConnectionState.connected);
    });

    it('returns connecting if a connection failed but it is still retrying', () => {
      dummyAgentConnection.connect();
      dummyAgentConnection.simulateFailedConnection();
      expect(dummyAgentConnection.state).toEqual(AgentConnectionState.connecting);
      dummyAgentConnection.disconnect();
    });

    it('returns idle if disconnect has been called', () => {
      dummyAgentConnection.connect();
      dummyAgentConnection.disconnect();
      expect(dummyAgentConnection.state).toEqual(AgentConnectionState.idle);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('connect()', () => {
    it('attempts to open a connection', () => {
      dummyAgentConnection.connect();
      expect(dummyAgentConnection.numConnectionAttempts).toEqual(1);
    });

    it('retries the connection periodically if it never succeeds', () => {
      jest.useFakeTimers();
      dummyAgentConnection.connect();
      expect(dummyAgentConnection.numConnectionAttempts).toEqual(1);

      // Advance past the connection timeout delay and the first retry time.
      jest.advanceTimersByTime(connectionTimeoutDelay + linearBackOff.minDelay);
      expect(dummyAgentConnection.numConnectionAttempts).toEqual(2);

      // Advance past the connection timeout delay and the second retry time.
      jest.advanceTimersByTime(connectionTimeoutDelay + linearBackOff.minDelay * 2);
      expect(dummyAgentConnection.numConnectionAttempts).toEqual(3);
    });

    it('retries the connection more quickly if it explicitly fails', () => {
      jest.useFakeTimers();
      dummyAgentConnection.connect();
      expect(dummyAgentConnection.numConnectionAttempts).toEqual(1);

      // Advance past the first retry time.
      dummyAgentConnection.simulateFailedConnection();
      jest.advanceTimersByTime(connectionTimeoutDelay + linearBackOff.minDelay);
      expect(dummyAgentConnection.numConnectionAttempts).toEqual(2);

      // Advance past the second retry time.
      dummyAgentConnection.simulateFailedConnection();
      jest.advanceTimersByTime(connectionTimeoutDelay + linearBackOff.minDelay * 2);
      expect(dummyAgentConnection.numConnectionAttempts).toEqual(3);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('disconnect()', () => {
    it('prevents any further connection attempts', () => {
      jest.useFakeTimers();
      dummyAgentConnection.connect();
      expect(dummyAgentConnection.numConnectionAttempts).toEqual(1);
      dummyAgentConnection.disconnect();

      // Advance past the connection timeout delay and the first retry time.
      jest.advanceTimersByTime(connectionTimeoutDelay + linearBackOff.minDelay);
      expect(dummyAgentConnection.numConnectionAttempts).toEqual(1);
    });

    it('does nothing if there is no connection', () => {
      expect(() => {
        dummyAgentConnection.disconnect();
      }).not.toThrow();
    });
  });
});

import { AgentConnectionState } from './AgentConnection';
import OperatingMode from 'constants/OperatingMode';
import PlatformConfig from 'models/PlatformConfig';
import ProvisioningInfo from './ProvisioningInfo';
import StandaloneEvent from 'utilities/StandaloneEvent';
import StorageService from 'services/StorageService';
import ITelemetryService from 'services/ITelemetryService';
import LinearBackOff from 'back-off-methods/LinearBackOff';
import NativeAgentType from 'constants/NativeAgentType';
import QoriaAgentConnection from './QoriaAgentConnection';
import { resetBadgeTitle, setBadgeTitle } from 'utilities/Helpers';
import SmoothwallAgentConnection from './SmoothwallAgentConnection';
import IExtensionConfig from 'models/IExtensionConfig';
import { TelemetryEventType } from '../constants/TelemetryEventType';

/**
 * Manages retrieving and dispatching provisioning info.
 * The provisioning info identifiers the customer, hardware, and user. It's essential for full
 *  functionality of the product. It's retrieved and processed differently, depending on which
 *  operating mode we're in.
 *
 * When running in native mode, this will listen for changes in the provisioning info sent by the
 *  native daemon.
 * When running in standalone mode, this will listen for changes to the user info. However, it
 *  assumes that some other part of the extension listens for changes in managed storage, and resets
 *  the extension if appropriate.
 *
 * Usage: After construction, add event listeners for "onProvisioned" and "onProvisioningUpdated",
 *  then call start(). After "onProvisioned" has been triggered, you can retrieve the provisioning
 *  info from the "provisioningInfo" property.
 */
export default class ProvisioningManager {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance which will use the given object for caching.
   * Note that this will not start retrieving provisioning info. You must call start() after
   *  construction.
   *
   * @param storageService The object to use for caching provisioning info. The caller is
   *  responsible for ensuring the storage data is loaded before calling start() on this object.
   * @param telemetryService Logs events and exceptions to the cloud.
   * @param extensionConfig Contains build-time configuration options.
   */
  public constructor(
    storageService: StorageService,
    telemetryService: ITelemetryService,
    extensionConfig: IExtensionConfig,
  ) {
    this._storageService = storageService;
    this._telemetryService = telemetryService;

    const manifest = chrome.runtime.getManifest();

    const backOffMethod = new LinearBackOff(5000, 30000, 0);
    this._qoriaAgentConnection = new QoriaAgentConnection(
      30000,
      backOffMethod,
      manifest.name,
      manifest.version,
      extensionConfig.qoriaAgentGrpcUrl,
    );
    this._qoriaAgentConnection.onProvisioningInfo.addListener(this._onQoriaProvisioningInfo);
    this._qoriaAgentConnection.onProvisioningError.addListener(this._onQoriaProvisioningError);
    this._qoriaAgentConnection.onIpAddresses.addListener(this.onIpAddresses);
    this._qoriaAgentConnection.onFailure.addListener(this._onQoriaAgentConnectionFailed);

    this._smoothwallAgentConnection = new SmoothwallAgentConnection(
      30000,
      backOffMethod,
      extensionConfig.smoothwallAgentNativeMessagingHostName,
    );
    this._smoothwallAgentConnection.onProvisioningInfo.addListener(
      this._onSmoothwallProvisioningInfo,
    );
    this._smoothwallAgentConnection.onProvisioningError.addListener(
      this._onSmoothwallProvisioningError,
    );
    this._smoothwallAgentConnection.onIpAddresses.addListener(this.onIpAddresses);
    this._smoothwallAgentConnection.onFailure.addListener(this._onSmoothwallAgentConnectionFailed);
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Get the most recent copy of the provisioning info.
   * This will be undefined if it hasn't been retrieved yet, or the info was invalid.
   *
   * @note You must call start() before this will be populated.
   *
   * @see onProvisioned
   * @see onProvisioningUpdated
   * @see isProvisioned
   */
  public get provisioningInfo(): ProvisioningInfo | undefined {
    return this._provisioningInfo;
  }

  /**
   * Check if this controller is currently running; i.e. start() has been called.
   */
  public get isRunning(): boolean {
    return this._platformConfig !== undefined;
  }

  /**
   * Check if this controller has successfully retrieved provisioning info.
   *
   * @see provisioningInfo
   * @see onProvisioned
   * @see onProvisioningUpdated
   */
  public get isProvisioned(): boolean {
    return this._provisioningInfo !== undefined;
  }

  // -----------------------------------------------------------------------------------------------
  // Events.

  /**
   * Triggered when full provisioning info has been obtained, either from cache or somewhere else.
   * The parameter gives the new provisioning info.
   */
  public readonly onProvisioned = new StandaloneEvent<[ProvisioningInfo]>();

  /**
   * Triggered when there's a minor change in the provisioning info.
   * If a significant change in provisioning info is detected, then the extension will automatically
   *  be reset.
   */
  public readonly onProvisioningUpdated = new StandaloneEvent<[ProvisioningInfo]>();

  /**
   * Triggered if there is an error with getting the provisioning info.
   */
  public readonly onProvisioningFailed = new StandaloneEvent();

  /**
   * Triggered when IP addresses have been received from a native agent.
   * The argument is a list of local IP addresses.
   */
  public readonly onIpAddresses = new StandaloneEvent<[string[]]>();

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Add any event listeners required by this object.
   * This must be done during the first event loop iteration.
   */
  public readonly addEventListeners = (): void => {
    chrome.identity.onSignInChanged.addListener(this._onSignInChanged);
  };

  /**
   * Start retrieving and processing provisioning information.
   * This will load it from the cache if possible at first.
   * It will then fetch details from managed storage or a native daemon, depending on which
   *  operating mode we're in.
   *
   * @param platformConfig Contains details about the platform and operating mode.
   * @returns A promise which resolves when we've successfully started the process of retrieving
   *  provisioning info.
   *
   * @note This must be called after everything has been loaded in the local cache service.
   * @note This should only be called once. Trying to call it again will fail.
   */
  public readonly start = async (platformConfig: PlatformConfig): Promise<void> => {
    if (this._platformConfig !== undefined) {
      throw new Error('ProvisioningController already started.');
    }

    this._platformConfig = platformConfig;

    // Load the cached provisioning info from storage, if it's not already loaded.
    if (!this._storageService.has('serialId')) {
      try {
        await this._storageService.load();
      } catch (e: any) {
        console.warn(e);
        // Ignore the error and carry on.
      }
    }

    // Immediately start provisioning from the cache if possible.
    try {
      const provisioningInfo = ProvisioningInfo.loadFromStorage(this._storageService);

      if (provisioningInfo === undefined) {
        console.debug('No provisioning info found in cache.');
      } else {
        console.debug('Loaded provisioning info from cache.');
        await this._provision(provisioningInfo, null);
      }
    } catch (e: any) {
      console.warn('An error occurred while load cached provisioning info.', e);
    }

    // In standalone mode, new provisioning info comes from managed storage.
    if (this._platformConfig.operatingMode === OperatingMode.standalone) {
      await this._loadStandaloneProvisioning();
      return;
    }

    // If we reach here, we're not in standalone mode. Try Companion mode (i.e. the Qoria agent)
    //  first as it always takes priority. We'll try try native mode if that fails.
    this._qoriaAgentConnection.connect();
  };

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Store and apply the given provisioning information.
   * This will check for any changes and dispatch appropriate events.
   *
   * @param newProvisioningInfo The new provisioning information to apply. The caller must not store
   *  this directly into this._provisioningInfo.
   * @param nativeAgentType The type of native agent which provided the provisioning info, or null
   *  if it hasn't come from a native agent. This will be used to update the platform configuration.
   */
  private readonly _provision = async (
    newProvisioningInfo: ProvisioningInfo,
    nativeAgentType: NativeAgentType | null,
  ): Promise<void> => {
    const wasAlreadyProvisioned = this._provisioningInfo !== undefined;
    if (wasAlreadyProvisioned) {
      // Do nothing if the info and operating mode hasn't changed at all.
      if (
        newProvisioningInfo.equals(this._provisioningInfo) &&
        this._platformConfig?.nativeAgentType === nativeAgentType
      ) {
        console.debug('No change detected in provisioning info.');
        return;
      }

      // If there's been a major change in the provisioning info then we need to completely reset
      //  the extension.
      if (!newProvisioningInfo.hasSameCoreData(this._provisioningInfo)) {
        console.warn('Significant change detected in provisioning info. Resetting extension.');
        this._telemetryService.logEvent(TelemetryEventType.ProvisioningChanged);
        this.onHardResetRequired.dispatch();
        return;
      }
    }

    // If we reach here then either we didn't have any provisioning info before, or it's only
    //  changed a little.
    console.debug('Storing new provisioning info.');
    this._provisioningInfo = newProvisioningInfo;
    if (this._platformConfig !== undefined) {
      this._platformConfig.nativeAgentType = nativeAgentType ?? undefined;
    }
    newProvisioningInfo.writeToStorage(this._storageService);
    this._storageService.saveInTheBackground();

    if (this._provisioningInfo.validTenantUuid) {
      await resetBadgeTitle();
    }

    if (wasAlreadyProvisioned) {
      this.onProvisioningUpdated.dispatch(this._provisioningInfo);
    } else {
      this.onProvisioned.dispatch(this._provisioningInfo);
    }
  };

  /**
   * Try to retrieve and apply provisioning info from the browser.
   * This only applies to standalone mode.mode
   */
  private readonly _loadStandaloneProvisioning = async (): Promise<void> => {
    if (this._platformConfig === undefined) {
      const message = 'Cannot process standalone provisioning without platform config.';
      await setBadgeTitle(message, true);
      throw new Error(message);
    }

    try {
      const provisioningInfo = await ProvisioningInfo.loadStandalone(
        this._platformConfig.managedPolicy,
      );

      if (!provisioningInfo.validTenantUuid) {
        await setBadgeTitle(
          `Provisioning Error: The tenant id provided is not a valid UUID.`,
          true,
        );
      }

      console.debug('Loaded provisioning info from managed storage.');
      await this._provision(provisioningInfo, null);
    } catch (e: any) {
      const message = 'An error occurred while loading provisioning info from policy';
      console.error(message, e);

      const errorMessage =
        e instanceof Error
          ? e.message
          : 'An error occurred while loading provisioning info from policy';
      await setBadgeTitle(`Provisioning Error: ${errorMessage}`, true);

      this.onProvisioningFailed.dispatch();
    }
  };

  // -----------------------------------------------------------------------------------------------
  // Event handlers.

  /**
   * Triggered when the user signs in or out of their browser profile.
   *
   * @note It's not clear under what circumstances this actually gets triggered. Signing-in or out
   *  seems to completely switch profile, creating a separate instance of the extension which starts
   *  over from scratch.
   */
  private readonly _onSignInChanged = (): void => {
    // If we're in standalone mode, then we depend on the user info in the browser. If a different
    //  user has signed-in then ensure we detect this change.
    if (this._platformConfig?.operatingMode === OperatingMode.standalone) {
      console.debug('Browser sign-in change detected.');
      this._loadStandaloneProvisioning().catch(console.warn);
    }
  };

  /**
   * Triggered when we've successfully received provisioning info from the Qoria agent.
   *
   * @param provisioningInfo The customer ID, username, etc. received from the agent.
   */
  private readonly _onQoriaProvisioningInfo = async (
    provisioningInfo: ProvisioningInfo,
  ): Promise<void> => {
    const hasChanged = this._platformConfig?.nativeAgentType !== NativeAgentType.qoria;
    await resetBadgeTitle();
    await this._provision(provisioningInfo, NativeAgentType.qoria);

    // Only log the connection state if it's changed. This is logged after handling the provisioning
    //  info so that the telemetry service has the up-to-date customer info.
    if (hasChanged) {
      this._telemetryService.logEvent(TelemetryEventType.NativeConnection, {
        type: 'qoria',
        agentVersion: this._qoriaAgentConnection.agentVersion ?? '',
      });
    }

    // The Qoria Agent always takes precedence over the Smoothwall agent.
    this._smoothwallAgentConnection.disconnect();
  };

  /**
   * Triggered when invalid provisioning info is received from the Qoria agent.
   *
   * @param message A human readable description of the error.
   */
  private readonly _onQoriaProvisioningError = async (message: string): Promise<void> => {
    // Ignore the error if we've successfully connected to the Smoothwall agent.
    if (this._smoothwallAgentConnection.state === AgentConnectionState.connected) {
      return;
    }

    if (message === '') {
      message = 'Invalid provisioning info received from native agent.';
    }
    await setBadgeTitle(`Provisioning error: ${message}`, true);
  };

  /**
   * Triggered when the connection to the Qoria agent fails.
   *
   * @param wasConnected Indicates if the agent was successfully connected immediately before the
   *  error.
   */
  private readonly _onQoriaAgentConnectionFailed = (wasConnected: boolean): void => {
    // Only log the connection state if it's changed.
    if (wasConnected) {
      this._telemetryService.logError(TelemetryEventType.NativeConnectionFailed, '');
      this._hasLoggedAgentFailure = true;
    }

    // Fall-back on the Smoothwall agent if the Qoria agent isn't present or fails.
    this._smoothwallAgentConnection.connect();
  };

  /**
   * Triggered when we've successfully received provisioning info from the Smoothwall agent.
   *
   * @param message The customer ID, username, etc. which were provided by the agent.
   */
  private readonly _onSmoothwallProvisioningInfo = async (
    provisioningInfo: ProvisioningInfo,
  ): Promise<void> => {
    // The Qoria agent takes precedence over the Smoothwall agent if both are connected.
    if (this._qoriaAgentConnection.state === AgentConnectionState.connected) {
      this._smoothwallAgentConnection.disconnect();
      return;
    }

    const hasChanged = this._platformConfig?.nativeAgentType !== NativeAgentType.smoothwall;
    await resetBadgeTitle();
    await this._provision(provisioningInfo, NativeAgentType.smoothwall);

    // Only log the connection state if it's changed. This is logged after handling the provisioning
    //  info so that the telemetry service has the up-to-date customer info.
    if (hasChanged) {
      this._telemetryService.logEvent(TelemetryEventType.NativeConnection, {
        type: 'smoothwall',
        agentVersion: '', // <-- we don't receive this information from the Smoothwall agent
      });
    }

    // Note: We'll deliberately keep trying to connect to the Qoria agent. It will take precedence
    //  if it succeeds.
  };

  /**
   * Triggered when invalid provisioning info is received from the Qoria agent.
   *
   * @param message A human readable description of the error.
   */
  private readonly _onSmoothwallProvisioningError = async (message: string): Promise<void> => {
    // Ignore the error if we've successfully connected to the Qoria agent.
    if (this._qoriaAgentConnection.state === AgentConnectionState.connected) {
      return;
    }

    if (message === '') {
      message = 'Invalid provisioning info received from native agent.';
    }
    await setBadgeTitle(`Provisioning error: ${message}`, true);
  };

  /**
   * Triggered when the connection to the Smoothwall agent fails.
   *
   * @param wasConnected Indicates if the agent was successfully connected immediately before the
   *  error.
   */
  private readonly _onSmoothwallAgentConnectionFailed = async (
    wasConnected: boolean,
  ): Promise<void> => {
    // Note: If we reach here, then connections to the Qoria and Smoothwall agents have BOTH failed.

    // If we have cached data from a previous run then we'll keep using it indefinitely. If we don't
    //  have any cached data then disable all filtering, including mini-filter. This is to ensure we
    //  don't disrupt browsing for somebody who accidentally installed the extension.
    if (this.provisioningInfo === undefined) {
      this.onProvisioningFailed.dispatch();
      await setBadgeTitle('Provisioning failed: No response from native client.', true);
    }

    // Only log the connection state if it's changed, or this was the very first attempt.
    if (wasConnected || !this._hasLoggedAgentFailure) {
      this._telemetryService.logError(TelemetryEventType.NativeConnectionFailed, '');
      this._hasLoggedAgentFailure = true;
    }
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * An event that is triggered if the extension should be hard reset.
   */
  public readonly onHardResetRequired = new StandaloneEvent();

  /**
   * The telemetry service for logging events.
   */
  private readonly _telemetryService: ITelemetryService;

  /**
   * The storage service which this object will use to load and save the provisioning info cache.
   * This must be loaded from storage *before* start is called.
   */
  private readonly _storageService: StorageService;

  /**
   * Stores details about the system we're running on and our operating mode.
   */
  private _platformConfig?: PlatformConfig;

  /**
   * Manages the connection to the Qoria native agent.
   */
  private readonly _qoriaAgentConnection: QoriaAgentConnection;

  /**
   * Manages the connection to the Smoothwall native agent.
   */
  private readonly _smoothwallAgentConnection: SmoothwallAgentConnection;

  /**
   * Contains the most recent copy of the provisioning info while we're running.
   * This may be undefined if we haven't retrieved any provisioning info yet, or it was invalid.
   */
  private _provisioningInfo?: ProvisioningInfo;

  /**
   * Indicates if we've logged a telemetry error about failing to connect to any native agent.
   * We'll report the failure once on start-up if we fail to connect to any native agent.
   * Thereafter, we'll only log the error if the connection state changes.
   */
  private _hasLoggedAgentFailure: boolean = false;
}

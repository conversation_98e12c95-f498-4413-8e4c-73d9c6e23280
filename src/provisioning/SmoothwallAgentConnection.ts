import AgentConnection, { AgentConnectionState } from './AgentConnection';
import BackOffMethod from 'back-off-methods/BackOffMethod';
import ProvisioningInfo from './ProvisioningInfo';
import SmoothwallAgentMessage, {
  SmoothwallAgentIpAddressesMessageData,
  SmoothwallAgentMessageType,
} from './SmoothwallAgentMessage';

/**
 * Manages the connection to the Smoothwall native agent.
 * This is used if the operating mode is "native".
 *
 * @see QoriaAgentConnection
 */
export default class SmoothwallAgentConnection extends AgentConnection {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance which will connect to the native agent on the specified URL.
   *
   * @param connectionTimeoutDelay The time (in milliseconds) to wait for a connection to succeed
   *  before giving up and treating it as a failure.
   * @param backOffMethod Determines how long to wait before retrying a failed connection.
   * @param nativeMessagingHostName The name of the native messaging host we will connect to.
   *  This is usually: 'com.smoothwall.chrome.bridge'
   */
  public constructor(
    connectionTimeoutDelay: number,
    backOffMethod: BackOffMethod,
    nativeMessagingHostName: string,
  ) {
    super(connectionTimeoutDelay, backOffMethod);
    this._nativeMessagingHostName = nativeMessagingHostName;
  }

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Open a native port to the Smoothwall agent and send an initialisation message.
   * This will close any existing port first.
   */
  protected readonly _openConnection = (): void => {
    this._closeConnection();

    this._nativePort = chrome.runtime.connectNative(this._nativeMessagingHostName);
    this._nativePort.onMessage.addListener(this._onMessage);
    this._nativePort.onDisconnect.addListener(this._onDisconnect);
    this._nativePort.postMessage({
      mt: SmoothwallAgentMessageType.browserInitRequest as number,
      d: {},
      c: 2, // <-- the Smoothwall agent only listens on channel 2
    } satisfies SmoothwallAgentMessage);

    // If we haven't received valid provisioning info from the agent within a defined time then
    //  we'll assume it failed.
    this._scheduleConnectionTimeout();
  };

  /**
   * If we have a native port to the Smoothwall agent then close it.
   */
  protected readonly _closeConnection = (): void => {
    // Remove event listeners before closing the connection to ensure they aren't triggered again.
    this._nativePort?.onMessage.removeListener(this._onMessage);
    this._nativePort?.onDisconnect.removeListener(this._onDisconnect);
    this._nativePort?.disconnect();
    this._nativePort = undefined;
    this._cancelConnectionTimeout();
  };

  // -----------------------------------------------------------------------------------------------
  // Event handlers.

  /**
   * Called when a message is received from the Smoothwall agent.
   * Outline of message types can be found here: https://familyzone.atlassian.net/wiki/spaces/UC/pages/2694339822671/Types+mapping
   *
   * @param message The raw message received from the agent.
   * @param port The port the message was received on.
   */
  private readonly _onMessage = (message: any, port: chrome.runtime.Port): void => {
    if (message.mt === SmoothwallAgentMessageType.ipAddresses) {
      // We received IP addresses from the agent.
      this.onIpAddresses.dispatch(
        (message.d as SmoothwallAgentIpAddressesMessageData).localIpAddresses,
      );
      console.debug('SmoothwallAgentConnection - Received IP addresses from agent.', message.d);
      return;
    }

    if (message.mt === SmoothwallAgentMessageType.browserInitData) {
      // We received provisioning info from the agent.
      let provisioningInfo: ProvisioningInfo;
      try {
        provisioningInfo = ProvisioningInfo.loadFromBrowserInitPayload(message.d);
      } catch (e: any) {
        this.onProvisioningError.dispatch(e instanceof Error ? e.message : '');
        return;
      }
      this.onProvisioningInfo.dispatch(provisioningInfo);

      if (this._state === AgentConnectionState.connected) {
        console.debug(
          'SmoothwallAgentConnection - Received updated provisioning info from daemon.',
          message.d,
        );
      } else {
        console.info('SmoothwallAgentConnection - Successfully connected to agent.', message.d);
      }

      // We're successfully connected now.
      this._state = AgentConnectionState.connected;
      this._cancelRetry();
      this._cancelConnectionTimeout();

      // Now that we've connected successfully, ensure we reset the backoff method if the connection
      //  subsequently fails again.
      this._resetNumRetries();
    }
  };

  /**
   * Called when the connection to the Smoothwall agent was lost or couldn't be opened at all.
   *
   * @param port The port that was disconnected.
   */
  private readonly _onDisconnect = (port: chrome.runtime.Port): void => {
    this._closeConnection();

    // Sanity-check: If we aren't meant to be connecting at all then do nothing else.
    if (this._state === AgentConnectionState.idle) {
      return;
    }

    const verbose = this._state === AgentConnectionState.connected || this.numRetries === 0;
    if (chrome.runtime.lastError?.message === undefined) {
      if (verbose) {
        console.debug('SmoothwallAgentConnection - Native port disconnected.');
      }
    } else {
      if (verbose) {
        console.warn(
          'SmoothwallAgentConnection - Native port disconnected with error: ',
          chrome.runtime.lastError,
        );
      }
    }

    if (this.numRetries === 0) {
      this.onFailure.dispatch(this._state === AgentConnectionState.connected);
    }
    this._state = AgentConnectionState.connecting;
    this._scheduleRetry();
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The name of the native messaging host we will connect to.
   */
  private readonly _nativeMessagingHostName: string;

  /**
   * The port that the native agent is connected on.
   * This will be undefined if we don't have a connection.
   */
  private _nativePort: chrome.runtime.Port | undefined;
}

import BackOffMethod from 'back-off-methods/BackOffMethod';
import ProvisioningInfo from './ProvisioningInfo';
import StandaloneEvent from 'utilities/StandaloneEvent';

/**
 * The possible states of the connection to a native agent.
 */
export enum AgentConnectionState {
  /**
   * There is no connection and we're not trying to establish one.
   */
  idle,

  /**
   * There is no connection but we're actively trying to establish one.
   * We'll stay in this state indefinitely if we're retrying.
   */
  connecting,

  /**
   * We have a connection to the native agent and have received valid provisioning information.
   *
   * @note If the native agent is present but is not configured appropriately then it will be
   *  treated as though it was not connected at all.
   */
  connected,
}

/**
 * Base class for managing a connection to a native agent.
 */
export default abstract class AgentConnection {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance with the specified timeout and retry schedule.
   *
   * @param connectionTimeoutDelay The time (in milliseconds) to wait for a connection to succeed
   *  before giving up and treating it as a failure.
   * @param backOffMethod Determines how long to wait between consecutive connection attempts in the
   *  event of a failure.
   */
  public constructor(connectionTimeoutDelay: number, backOffMethod: BackOffMethod) {
    this._connectionTimeoutDelay = connectionTimeoutDelay;
    this._backOffMethod = backOffMethod;
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Get the state which the agent connection is currently in.
   */
  public get state(): AgentConnectionState {
    return this._state;
  }

  /**
   * Get the number of retries which have been attempted so far.
   */
  protected get numRetries(): number {
    return this._numRetries;
  }

  // -----------------------------------------------------------------------------------------------
  // Events.

  /**
   * Triggered when we have connected to the native agent and received valid provisioning info.
   * This event may be triggered multiple times, e.g. if the provisioning info changes or the native
   *  agent restarts.
   */
  public readonly onProvisioningInfo = new StandaloneEvent<[ProvisioningInfo]>();

  /**
   * Triggered when we have received invalid provisioning info from the native agent.
   * This event may be triggered multiple times, e.g. if the agent sends the same info again.
   * The argument is a human-readable description of the error which should be reported in the UI.
   * The error message may be empty if the error is unknown.
   */
  public readonly onProvisioningError = new StandaloneEvent<[string]>();

  /**
   * Triggered when we have received a list of IP addresses from the native agent.
   * The argument is a collection of local IP addresses detected on the system.
   * This may be triggered whenever a network change occurs or the native agent reconnects.
   */
  public readonly onIpAddresses = new StandaloneEvent<[string[]]>();

  /**
   * Triggered when the connection to the native agent failed.
   * This may occur if a connection couldn't be established at all, the agent connected but was not
   *  properly configured, or a valid connection had been established but was subsequently lost.
   * This should not be triggered on every retry, or in response to disconnect() being called.
   * The argument indicates the state of the connection prior to the failure. True means the agent
   *  had been connected and the connection was then lost. False means we never had a connection at
   *  all.
   */
  public readonly onFailure = new StandaloneEvent<[boolean]>();

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Start trying to connect to the native agent.
   */
  public readonly connect = (): void => {
    // Do nothing if we're already connected or attempting a connection.
    if (this._state !== AgentConnectionState.idle) {
      return;
    }

    this._state = AgentConnectionState.connecting;
    this._resetNumRetries();
    this._openConnection();
  };

  /**
   * Disconnect from the native agent and stop retrying.
   */
  public readonly disconnect = (): void => {
    this._cancelRetry();
    this._closeConnection();
    this._state = AgentConnectionState.idle;
  };

  // -----------------------------------------------------------------------------------------------
  // Event handlers.

  /**
   * Treat a connection attempt as failed.
   * This is called when a set period of time has elapsed following a connection attempt. The timer
   *  must be cancelled if a connection has succeeded.
   */
  protected readonly _onConnectionTimeout = (): void => {
    // Sanity-check: Do nothing if we've connected successfully or the connection was cancelled.
    if (this._state !== AgentConnectionState.connecting) {
      return;
    }

    if (this.numRetries === 0) {
      console.debug(`AgentConnection - Timed-out waiting for response from agent.`);
      this.onFailure.deferDispatch(false);
    }

    this._scheduleRetry();
  };

  /**
   * Try to re-establish a failed connection.
   * This is called when a period of time has elapsed following a connection failure. The period of
   *  time increases as the number of consecutive retries increases.
   */
  protected readonly _onRetry = (): void => {
    // Sanity-check: Do nothing if we've connected successfully or the connection was cancelled.
    if (this._state !== AgentConnectionState.connecting) {
      return;
    }

    this._openConnection();
  };

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Try to start a connection to the agent.
   * This will also send any initial message which needs to be sent.
   * Any existing connection will be closed first.
   * Sub-classes must override this to provide an agent-specific implementation.
   */
  protected abstract readonly _openConnection: () => void;

  /**
   * If there is a connection to the agent then close it.
   * This will not prevent any retries from happening.
   * Sub-classes must override this to provide an agent-specific implementation.
   */
  protected abstract readonly _closeConnection: () => void;

  /**
   * Set a one-off timer which assumes the connection has failed.
   * Sub-classes should call this when starting a connection attempt, and then call
   *  _cancelConnectionTimeout() if it succeeds.
   * If a timeout is already scheduled then it will be cancelled and replaced.
   */
  protected readonly _scheduleConnectionTimeout = (): void => {
    this._cancelConnectionTimeout();
    this._connectionTimeout = setTimeout(this._onConnectionTimeout, this._connectionTimeoutDelay);
  };

  /**
   * Cancel the scheduled connection timeout.
   * Sub-classes should call this when a connection succeeds to ensure it doesn't get treated as
   *  failed.
   * This will have no effect if there is no timeout scheduled.
   */
  protected readonly _cancelConnectionTimeout = (): void => {
    if (this._connectionTimeout !== undefined) {
      clearTimeout(this._connectionTimeout);
      this._connectionTimeout = undefined;
    }
  };

  /**
   * Set a one-off timer to initiate a retry.
   * When the timer elapses, it will call _onRetry().
   * The time before the retry occurs is determined by the back-off method and the number of
   *  retries which have been attempted so far.
   * If a retry is already scheduled then it will be cancelled and replaced.
   */
  protected readonly _scheduleRetry = (): void => {
    this._cancelRetry();

    // The back-off method returns no delay for attempt number 0. Ensure our number of retries is
    //  effectively 1 based to avoid this.
    const delay = this._backOffMethod.generateDelayMs(this._numRetries + 1, 0);

    this._retryTimeout = setTimeout(() => {
      this._numRetries++;
      this._onRetry();
    }, delay);
  };

  /**
   * If there is a retry scheduled then cancel it.
   * This will prevent _onRetry() from being called.
   * This has no effect if no retry is scheduled.
   */
  protected readonly _cancelRetry = (): void => {
    if (this._retryTimeout !== undefined) {
      clearTimeout(this._retryTimeout);
      this._retryTimeout = undefined;
    }
  };

  /**
   * Set the number of retries attempted so far back to 0.
   * This means that the next retry which is scheduled will use the minimum back-off time.
   * This will not affect any pending retry timer which was already scheduled.
   */
  protected readonly _resetNumRetries = (): void => {
    this._numRetries = 0;
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The time (in milliseconds) to wait for a connection attempt to succeed before giving up.
   */
  private readonly _connectionTimeoutDelay: number;

  /**
   * Determines how long to wait before retrying a failed connection.
   */
  private readonly _backOffMethod: BackOffMethod;

  /**
   * Handle to a one-off timer which abandons a connection attempt if there's been no response.
   */
  private _connectionTimeout?: ReturnType<typeof setTimeout>;

  /**
   * Handle to a one-off timer which initiates a new connection attempt.
   */
  private _retryTimeout?: ReturnType<typeof setTimeout>;

  /**
   * The number of times we've retried the connection in the current run.
   * This is reset to 0 when connect() is called, or when a connection is opened successfully.
   * This is used along with _backOffMethod to slow down consecutive retries.
   */
  private _numRetries: number = 0;

  /**
   * The current state of the connection.
   */
  protected _state = AgentConnectionState.idle;
}

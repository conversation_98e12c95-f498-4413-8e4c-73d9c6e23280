import {
  CompanionRegistrationRequest,
  CompanionRegistrationResponse,
  EnrolmentDetails,
} from '@connect/common-idl/common_js/companion/messages_pb';
import {
  CompanionApiClient,
  ResponseStream,
} from '@connect/common-idl/common_js/companion/rpc_pb_service';
import { EnrolmentProviderType } from '@connect/common-idl/common_js/configuration/properties_configuration_pb';
import BackOffMethod from 'back-off-methods/BackOffMethod';
import AgentConnection, { AgentConnectionState } from './AgentConnection';
import ProvisioningInfo from './ProvisioningInfo';

/**
 * Identifies software features which may be supported by the native agent in Companion Mode.
 */
export enum CompanionFeatures {
  companion = 'companion',
  companionLite = 'companion_lite',
  proxyFilter = 'proxy_filter',
  dns_filter = 'dns_filter',
  classroom = 'classroom',
  liteModeEnabled = 'companion-mode-lite-enabled',
}

/**
 * Establishes and maintains a connection to the Qoria native agent via web-grpc.
 * This is used when running in Companion Mode. The native agent provides customer and user data.
 *
 * @see SmoothwallAgentConnection
 */
export default class QoriaAgentConnection extends AgentConnection {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance which will connect to the native agent on the specified URL.
   *
   * @param connectionTimeoutDelay The time (in milliseconds) to wait for a connection to succeed
   *  before giving up and treating it as a failure.
   * @param backOffMethod Determines how long to wait before retrying a failed connection.
   * @param extensionName The name of this piece of software. This is sent to the native agent so
   *  that it knows how to respond. This should usually be the name of the extension as given in
   *  the manifest file.
   * @param extensionVersion The version number of this piece of software. This is sent to the
   * native agent so that it knows how to respond. This should usually be the version of the
   *  extension as given in the manifest file.
   * @param agentUrl The URL of the native agent's web grpc port.
   */
  public constructor(
    connectionTimeoutDelay: number,
    backOffMethod: BackOffMethod,
    extensionName: string,
    extensionVersion: string,
    agentUrl: string,
  ) {
    super(connectionTimeoutDelay, backOffMethod);
    this._extensionName = extensionName;
    this._extensionVersion = extensionVersion;
    this._client = new CompanionApiClient(agentUrl);
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Get the version number of the native agent we are connected to, if any.
   * Returns undefined if there is no connected agent.
   */
  public get agentVersion(): string | undefined {
    return this._agentVersion;
  }

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Open a stream to the Qoria agent, send a registration message, and listen for a response.
   * If a stream already exists, it will be closed and replaced.
   */
  protected readonly _openConnection = (): void => {
    this._closeConnection();

    const companionRegistrationRequest = new CompanionRegistrationRequest();
    companionRegistrationRequest.setName(this._extensionName);
    companionRegistrationRequest.setVersion(this._extensionVersion);

    this._stream = this._client.registerCompanion(companionRegistrationRequest);
    this._stream.on('data', this._onStreamData);
    this._stream.on('end', this._onStreamEnd);

    // If we haven't received valid provisioning info from the agent within a defined time then
    //  we'll assume it failed.
    this._scheduleConnectionTimeout();
  };

  /**
   * Close the connection to the Qoria agent if necessary and remove its event handlers.
   */
  protected readonly _closeConnection = (): void => {
    // Remove the event handlers before closing the stream so that they don't get triggered again.
    this._stream?.on('data', () => {});
    this._stream?.on('end', () => {});
    this._stream?.cancel();
    this._stream = undefined;
    this._cancelConnectionTimeout();
    this._agentVersion = undefined;
  };

  // -----------------------------------------------------------------------------------------------
  // Event handlers.

  /**
   * Called when a message has been received from the Qoria agent.
   *
   * @param message The message which was received.
   */
  private readonly _onStreamData = (message: CompanionRegistrationResponse): void => {
    const verbose = this._state === AgentConnectionState.connecting && this.numRetries === 0;

    // Ignore anything except valid provisioning information.
    // The agent refers to this as enrolment information.
    // Note: It's possible that the native agent is present, but not configured for Qoria filter.
    // E.g. maybe it's only configured for an old Linewize extension. In that case, we will ignore
    //  the enrolment information as it won't be useful.
    const enrolmentDetails = message.getEnrolmentdetails()?.toObject();
    if (enrolmentDetails === undefined || enrolmentDetails.enrolmentinformation === undefined) {
      if (verbose) {
        console.debug(
          'QoriaAgentConnection - Ignoring non-enrolment message from native agent.',
          message.toObject(),
        );
      }
      return;
    }

    if (enrolmentDetails.enrolmentinformation.provider !== EnrolmentProviderType.QORIA) {
      if (verbose) {
        console.debug(
          'QoriaAgentConnection - Agent is not configured for expected provider.',
          enrolmentDetails,
        );
      }
      return;
    }

    // It looks like we received provisioning info. Ensure it's valid before using it.
    let provisioningInfo: ProvisioningInfo;
    try {
      provisioningInfo =
        ProvisioningInfo.loadProvisioningInfoFromEnrolmentDetails(enrolmentDetails);
    } catch (e: any) {
      this.onProvisioningError.dispatch(e instanceof Error ? e.message : '');
      return;
    }

    if (this._state === AgentConnectionState.connected) {
      console.debug(
        'QoriaAgentConnection - Received updated provisioning info from the agent.',
        enrolmentDetails,
      );
    } else {
      console.info('QoriaAgentConnection - Successfully connected to the agent.', enrolmentDetails);
    }

    this._agentVersion = message.getVersion();
    this.onProvisioningInfo.dispatch(provisioningInfo);
    this.onIpAddresses.dispatch(QoriaAgentConnection.extractIpAddresses(enrolmentDetails));

    this._state = AgentConnectionState.connected;
    this._cancelConnectionTimeout();
    this._cancelRetry();

    // Now that we've connected successfully, ensure we reset the backoff method if the connection
    //  subsequently fails again.
    this._resetNumRetries();
  };

  /**
   * Called when the native agent connection was closed or it failed to open.
   */
  private readonly _onStreamEnd = (): void => {
    this._closeConnection();

    // Sanity-check: If we aren't meant to be connecting at all then do nothing else.
    if (this._state === AgentConnectionState.idle) {
      return;
    }

    if (this.numRetries === 0) {
      console.debug('QoriaAgentConnection - Agent connection failed.');
      this.onFailure.dispatch(this._state === AgentConnectionState.connected);
    }

    this._state = AgentConnectionState.connecting;
    this._scheduleRetry();
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Extract a list of IP addresses from the enrolment details received from the native agent.
   * This will remove any duplicates.
   * Currently, this only extracts IPv4 addresses.
   *
   * @param enrolmentDetails The enrolment details received from the native agent.
   * @return An array of strings, where each string is an IPv4 address received from the native
   *  agent. The addresses will be in an arbitrary order. Returns an empty array if no IP addresses
   *  were found.
   */
  public static readonly extractIpAddresses = (
    enrolmentDetails: EnrolmentDetails.AsObject,
  ): string[] => {
    return [
      ...new Set<string>([enrolmentDetails.primaryipaddress, ...enrolmentDetails.ipaddressesList]),
    ].filter((s: string) => /^[\d.]+$/.test(s));
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The name of this extension, as given in the manifest file.
   * This is reported to the native agent when we open the connection.
   */
  private readonly _extensionName: string;

  /**
   * The version string of this extension, as given in the manifest file.
   * This is reported to the native agent when we open the connection.
   */
  private readonly _extensionVersion: string;

  /**
   * Manages the connection to the native agent.
   */
  private readonly _client: CompanionApiClient;

  /**
   * Manages data flow to/from the native agent.
   * This is only defined if we're actively trying to connect.
   */
  private _stream?: ResponseStream<CompanionRegistrationResponse>;

  /**
   * The version number of the native agent we're connected to, if any.
   */
  private _agentVersion?: string;
}

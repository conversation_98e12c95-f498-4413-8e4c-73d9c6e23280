import StorageService from 'services/StorageService';
import ProvisioningInfo from './ProvisioningInfo';
import SerialId from 'models/SerialId';
import TenantId from 'models/TenantId';

import 'test-helpers/chrome-api';

// Test data.
const testSerialId = new SerialId('UNCLTEST7GCBRNZG');
const testTenantId = new TenantId('3a4007ea-a817-11eb-9c60-3d943bce7542');
const testHardwareId = 'test-hardware-id';
const testDeviceName = 'test-device-name';
const testAgentVersion = 'test-agent-version';
const testUser = '<EMAIL>';
const testDomain = 'test-domain.com';
const testLocalGroups = ['test-group-1', 'test-group-2', 'test-group-3'];

/**
 * Convenience function to construct an instance of ProvisioningInfo.
 * By default, this will use clones of the test data defined above.
 *
 * @param overrides An object which can optionally contain overrides for zero or more parameters
 *  to be passed to the ProvisioningInfo constructor. Any undefined parameters will fall back to the
 *  test data above.
 * @returns An initialised instance of ProvisioningInfo.
 */
const makeProvisioningInfo = (overrides: any = {}): ProvisioningInfo => {
  // The default test data will be cloned to ensure tests are more realistic.
  const {
    serialId = new SerialId(testSerialId.toString()),
    tenantId = new TenantId(testTenantId.toString()),
    hardwareId = testHardwareId.slice(),
    deviceName = testDeviceName.slice(),
    agentVersion = testAgentVersion.slice(),
    user = testUser.slice(),
    domain = testDomain.slice(),
    localGroups = JSON.parse(JSON.stringify(testLocalGroups)),
  } = overrides;

  return new ProvisioningInfo(
    serialId,
    tenantId,
    hardwareId,
    deviceName,
    agentVersion,
    user,
    domain,
    localGroups,
    true,
  );
};

/**
 * Convenience function to populate a instance of StorageService with provisioning info.
 * This will populate it with the test values defined above.
 * The caller can manually replace individual values on the returned instance.
 *
 * @param storageService The storage service instance to populate.
 */
const populateStorageService = (storageService: StorageService): void => {
  storageService.set('serialId', testSerialId.toString());
  storageService.set('tenantId', testTenantId.toString());
  storageService.set('hardwareId', testHardwareId);
  storageService.set('deviceName', testDeviceName);
  storageService.set('agentVersion', testAgentVersion);
  storageService.set('user', testUser);
  storageService.set('domain', testDomain);
  storageService.set('localGroups', testLocalGroups);
};

describe('ProvisioningInfo', () => {
  beforeEach(() => {
    // Suppress console messages.
    jest.spyOn(console, 'debug').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  describe('constructor()', () => {
    it('stores the specified data', () => {
      const provisioningInfo = new ProvisioningInfo(
        testSerialId,
        testTenantId,
        testHardwareId,
        testDeviceName,
        testAgentVersion,
        testUser,
        testDomain,
        testLocalGroups,
        true,
      );

      expect(provisioningInfo.serialId.equals(testSerialId)).toBeTrue();
      expect(provisioningInfo.tenantId?.equals(testTenantId)).toBeTrue();
      expect(provisioningInfo.hardwareId).toEqual(testHardwareId);
      expect(provisioningInfo.user).toEqual(testUser);
      expect(provisioningInfo.domain).toEqual(testDomain);
      expect(provisioningInfo.localGroups).toEqual(expect.arrayContaining(testLocalGroups));
    });

    it('allows tenant ID to be undefined', () => {
      const provisioningInfo = new ProvisioningInfo(
        testSerialId,
        undefined,
        testHardwareId,
        testDeviceName,
        testAgentVersion,
        testUser,
        testDomain,
        testLocalGroups,
        true,
      );

      expect(provisioningInfo.tenantId).toBeUndefined();
    });
  });

  describe('getLocalGroupsAsCommaSeparatedString()', () => {
    it('returns the list of local groups as a comma separate string', () => {
      const provisioningInfo = makeProvisioningInfo();
      expect(provisioningInfo.getLocalGroupsAsCommaSeparatedString()).toEqual(
        'test-group-1,test-group-2,test-group-3',
      );
    });

    it('returns an empty string if there are no local groups', () => {
      const provisioningInfo = makeProvisioningInfo({ localGroups: [] });
      expect(provisioningInfo.getLocalGroupsAsCommaSeparatedString()).toEqual('');
    });
  });

  describe('equals()', () => {
    it('returns false if the parameter is undefined', () => {
      const provisioningInfo = makeProvisioningInfo();
      expect(provisioningInfo.equals(undefined)).toBeFalse();
    });

    it('returns true if all data is the same and both tenant ID are defined', () => {
      const provisioningInfo1 = makeProvisioningInfo();
      const provisioningInfo2 = makeProvisioningInfo();
      expect(provisioningInfo1.equals(provisioningInfo2)).toBeTrue();
    });

    it('returns true if all data is the same and both tenant IDs are undefined', () => {
      const provisioningInfo1 = new ProvisioningInfo(
        testSerialId,
        undefined,
        testHardwareId,
        testDeviceName,
        testAgentVersion,
        testUser,
        testDomain,
        testLocalGroups,
        true,
      );

      const provisioningInfo2 = new ProvisioningInfo(
        testSerialId,
        undefined,
        testHardwareId,
        testDeviceName,
        testAgentVersion,
        testUser,
        testDomain,
        testLocalGroups,
        true,
      );

      expect(provisioningInfo1.equals(provisioningInfo2)).toBeTrue();
    });

    it('returns false if serial ID is different', () => {
      const provisioningInfo1 = makeProvisioningInfo();
      const provisioningInfo2 = makeProvisioningInfo({
        serialId: new SerialId('UNCLTESTBDGC7JZE'),
      });
      expect(provisioningInfo1.equals(provisioningInfo2)).toBeFalse();
    });

    it('returns false if tenant ID is different', () => {
      const provisioningInfo1 = makeProvisioningInfo();
      const provisioningInfo2 = makeProvisioningInfo({
        tenantId: new TenantId('89c5e8f5-3588-4917-aa6e-5250ec1feadd'),
      });
      expect(provisioningInfo1.equals(provisioningInfo2)).toBeFalse();
    });

    it('returns false if one tenant ID is undefined', () => {
      const provisioningInfo1 = makeProvisioningInfo();
      const provisioningInfo2 = new ProvisioningInfo(
        testSerialId,
        undefined,
        testHardwareId,
        testDeviceName,
        testAgentVersion,
        testUser,
        testDomain,
        testLocalGroups,
        true,
      );

      // Ensure the comparison works if either tenant ID is undefined.
      expect(provisioningInfo1.equals(provisioningInfo2)).toBeFalse();
      expect(provisioningInfo2.equals(provisioningInfo1)).toBeFalse();
    });

    it('returns false if hardware ID is different', () => {
      const provisioningInfo1 = makeProvisioningInfo();
      const provisioningInfo2 = makeProvisioningInfo({ hardwareId: 'blah' });
      expect(provisioningInfo1.equals(provisioningInfo2)).toBeFalse();
    });

    it('returns false if user is different', () => {
      const provisioningInfo1 = makeProvisioningInfo();
      const provisioningInfo2 = makeProvisioningInfo({ user: 'blah' });
      expect(provisioningInfo1.equals(provisioningInfo2)).toBeFalse();
    });

    it('returns false if domain is different', () => {
      const provisioningInfo1 = makeProvisioningInfo();
      const provisioningInfo2 = makeProvisioningInfo({ domain: 'blah' });
      expect(provisioningInfo1.equals(provisioningInfo2)).toBeFalse();
    });

    it('returns false if local groups are different', () => {
      const provisioningInfo1 = makeProvisioningInfo();
      const provisioningInfo2 = makeProvisioningInfo({ localGroups: ['blah', 'foo'] });
      expect(provisioningInfo1.equals(provisioningInfo2)).toBeFalse();
    });
  });

  describe('hasSameCoreData()', () => {
    it('returns false if the parameter is undefined', () => {
      const provisioningInfo = makeProvisioningInfo();
      expect(provisioningInfo.hasSameCoreData(undefined)).toBeFalse();
    });

    it('returns true if all data is the same and both tenant ID are defined', () => {
      const provisioningInfo1 = makeProvisioningInfo();
      const provisioningInfo2 = makeProvisioningInfo();
      expect(provisioningInfo1.hasSameCoreData(provisioningInfo2)).toBeTrue();
    });

    it('returns true if all data is the same and both tenant IDs are undefined', () => {
      const provisioningInfo1 = new ProvisioningInfo(
        testSerialId,
        undefined,
        testHardwareId,
        testDeviceName,
        testAgentVersion,
        testUser,
        testDomain,
        testLocalGroups,
        true,
      );

      const provisioningInfo2 = new ProvisioningInfo(
        testSerialId,
        undefined,
        testHardwareId,
        testDeviceName,
        testAgentVersion,
        testUser,
        testDomain,
        testLocalGroups,
        true,
      );

      expect(provisioningInfo1.hasSameCoreData(provisioningInfo2)).toBeTrue();
    });

    it('returns false if serial ID is different', () => {
      const provisioningInfo1 = makeProvisioningInfo();
      const provisioningInfo2 = makeProvisioningInfo({
        serialId: new SerialId('UNCLTESTBDGC7JZE'),
      });
      expect(provisioningInfo1.hasSameCoreData(provisioningInfo2)).toBeFalse();
    });

    it('returns false if tenant ID is different', () => {
      const provisioningInfo1 = makeProvisioningInfo();
      const provisioningInfo2 = makeProvisioningInfo({
        tenantId: new TenantId('89c5e8f5-3588-4917-aa6e-5250ec1feadd'),
      });
      expect(provisioningInfo1.hasSameCoreData(provisioningInfo2)).toBeFalse();
    });

    it('returns false if one tenant ID is undefined', () => {
      const provisioningInfo1 = makeProvisioningInfo();
      const provisioningInfo2 = new ProvisioningInfo(
        testSerialId,
        undefined,
        testHardwareId,
        testDeviceName,
        testAgentVersion,
        testUser,
        testDomain,
        testLocalGroups,
        true,
      );

      // Ensure the comparison works if either tenant ID is undefined.
      expect(provisioningInfo1.hasSameCoreData(provisioningInfo2)).toBeFalse();
      expect(provisioningInfo2.hasSameCoreData(provisioningInfo1)).toBeFalse();
    });

    it('returns false if hardware ID is different', () => {
      const provisioningInfo1 = makeProvisioningInfo();
      const provisioningInfo2 = makeProvisioningInfo({ hardwareId: 'blah' });
      expect(provisioningInfo1.hasSameCoreData(provisioningInfo2)).toBeFalse();
    });

    it('returns false if user is different', () => {
      const provisioningInfo1 = makeProvisioningInfo();
      const provisioningInfo2 = makeProvisioningInfo({ user: 'blah' });
      expect(provisioningInfo1.hasSameCoreData(provisioningInfo2)).toBeFalse();
    });

    it('returns false if domain is different', () => {
      const provisioningInfo1 = makeProvisioningInfo();
      const provisioningInfo2 = makeProvisioningInfo({ domain: 'blah' });
      expect(provisioningInfo1.hasSameCoreData(provisioningInfo2)).toBeFalse();
    });

    it('returns true if only the local groups are different', () => {
      const provisioningInfo1 = makeProvisioningInfo();
      const provisioningInfo2 = makeProvisioningInfo({ localGroups: ['blah', 'foo'] });
      expect(provisioningInfo1.hasSameCoreData(provisioningInfo2)).toBeTrue();
    });
  });

  describe('loadFromStorage()', () => {
    it('returns an instance initialised from values in storage', () => {
      const storageService = new StorageService('provisioning', undefined);
      populateStorageService(storageService);

      const provisioningInfo = ProvisioningInfo.loadFromStorage(storageService);
      expect(provisioningInfo).toBeDefined();
      expect(provisioningInfo?.serialId.equals(testSerialId)).toBeTrue();
      expect(provisioningInfo?.tenantId?.equals(testTenantId)).toBeTrue();
      expect(provisioningInfo?.hardwareId).toEqual(testHardwareId);
      expect(provisioningInfo?.user).toEqual(testUser);
      expect(provisioningInfo?.domain).toEqual(testDomain);
      expect(provisioningInfo?.localGroups).toEqual(expect.arrayContaining(testLocalGroups));
    });

    it('returns undefined if serial ID is missing', () => {
      const storageService = new StorageService('provisioning', undefined);
      populateStorageService(storageService);
      storageService.delete('serialId');
      expect(ProvisioningInfo.loadFromStorage(storageService)).toBeUndefined();
    });

    it('throws an error if serial ID is an empty string', () => {
      const storageService = new StorageService('provisioning', undefined);
      populateStorageService(storageService);
      storageService.set('serialId', '');
      expect(() => ProvisioningInfo.loadFromStorage(storageService)).toThrowError();
    });

    it('throws an error if serial ID is invalid', () => {
      const storageService = new StorageService('provisioning', undefined);
      populateStorageService(storageService);
      storageService.set('serialId', 'blah');
      expect(() => ProvisioningInfo.loadFromStorage(storageService)).toThrowError();
    });

    it('sets tenant ID to undefined if it is missing', () => {
      const storageService = new StorageService('provisioning', undefined);
      populateStorageService(storageService);
      storageService.delete('tenantId');
      const provisioningInfo = ProvisioningInfo.loadFromStorage(storageService);
      expect(provisioningInfo).toBeDefined();
      expect(provisioningInfo?.tenantId).toBeUndefined();
    });

    it('sets tenant ID to undefined if it is an empty string', () => {
      const storageService = new StorageService('provisioning', undefined);
      populateStorageService(storageService);
      storageService.set('tenantId', '');
      const provisioningInfo = ProvisioningInfo.loadFromStorage(storageService);
      expect(provisioningInfo).toBeDefined();
      expect(provisioningInfo?.tenantId).toBeUndefined();
    });

    it('throws an error if tenant ID is invalid', () => {
      const storageService = new StorageService('provisioning', undefined);
      populateStorageService(storageService);
      storageService.set('tenantId', 'blah');
      expect(() => ProvisioningInfo.loadFromStorage(storageService)).toThrowError();
    });

    it('returns undefined if hardware ID is missing', () => {
      const storageService = new StorageService('provisioning', undefined);
      populateStorageService(storageService);
      storageService.delete('hardwareId');
      expect(ProvisioningInfo.loadFromStorage(storageService)).toBeUndefined();
    });

    it('throws an error if hardware ID is an empty string', () => {
      const storageService = new StorageService('provisioning', undefined);
      populateStorageService(storageService);
      storageService.set('hardwareId', '');
      expect(() => ProvisioningInfo.loadFromStorage(storageService)).toThrowError();
    });

    it('returns undefined if user is missing', () => {
      const storageService = new StorageService('provisioning', undefined);
      populateStorageService(storageService);
      storageService.delete('user');
      expect(ProvisioningInfo.loadFromStorage(storageService)).toBeUndefined();
    });

    it('throws an error if user is an empty string', () => {
      const storageService = new StorageService('provisioning', undefined);
      populateStorageService(storageService);
      storageService.set('user', '');
      expect(() => ProvisioningInfo.loadFromStorage(storageService)).toThrowError();
    });

    it('sets domain to an empty string if it is missing', () => {
      const storageService = new StorageService('provisioning', undefined);
      populateStorageService(storageService);
      storageService.delete('domain');
      const provisioningInfo = ProvisioningInfo.loadFromStorage(storageService);
      expect(provisioningInfo?.domain).toEqual('');
    });

    it('allows domain to be an empty string', () => {
      const storageService = new StorageService('provisioning', undefined);
      populateStorageService(storageService);
      storageService.set('domain', '');
      const provisioningInfo = ProvisioningInfo.loadFromStorage(storageService);
      expect(provisioningInfo?.domain).toEqual('');
    });

    it('sets local groups to an empty array if it is missing', () => {
      const storageService = new StorageService('provisioning', undefined);
      populateStorageService(storageService);
      storageService.delete('localGroups');
      const provisioningInfo = ProvisioningInfo.loadFromStorage(storageService);
      expect(provisioningInfo?.localGroups).toEqual([]);
    });

    it('allows local groups to be an empty array', () => {
      const storageService = new StorageService('provisioning', undefined);
      populateStorageService(storageService);
      storageService.set('localGroups', []);
      const provisioningInfo = ProvisioningInfo.loadFromStorage(storageService);
      expect(provisioningInfo?.localGroups).toEqual([]);
    });
  });

  describe('writeToStorage()', () => {
    it('writes all data to the given storage service instance', () => {
      const storageService = new StorageService('provisioning', undefined);
      const provisioningInfo = makeProvisioningInfo();
      provisioningInfo.writeToStorage(storageService);

      expect(storageService.get('serialId')).toEqual(testSerialId.toString());
      expect(storageService.get('tenantId')).toEqual(testTenantId.toString());
      expect(storageService.get('hardwareId')).toEqual(testHardwareId);
      expect(storageService.get('user')).toEqual(testUser);
      expect(storageService.get('domain')).toEqual(testDomain);
      expect(storageService.get('localGroups')).toEqual(testLocalGroups);
    });

    it('deletes any existing tenant ID from storage if it is not defined in provisioning info', () => {
      const storageService = new StorageService('provisioning', undefined);
      storageService.set('tenantId', testTenantId.toString());

      const provisioningInfo = new ProvisioningInfo(
        testSerialId,
        undefined,
        testHardwareId,
        testDeviceName,
        testAgentVersion,
        testUser,
        testDomain,
        testLocalGroups,
        true,
      );
      provisioningInfo.writeToStorage(storageService);
      expect(storageService.has('tenantId')).toBeFalse();
    });
  });

  describe('loadFromBrowserInitMessage', () => {
    it('returns ProvisioningInfo instance populated from the given payload', () => {
      const data = {
        cldflt: {
          userSession: {
            domain: 'test-domain.com',
            userName: 'test-local-user',
            userPrincipalName: '<EMAIL>',
            azureAdUserId: '',
            email: '',
            groups: 'groupA,groupB,groupC',
          },
          enableAzureAd: false,
        },
        reported: {
          identifiers: {
            serial: testSerialId.toString(),
            tenantId: testTenantId.toString(),
            hardwareId: testHardwareId,
          },
        },
        desired: {},
      };

      const provisioningInfo = ProvisioningInfo.loadFromBrowserInitPayload(data);
      expect(provisioningInfo.serialId.equals(testSerialId)).toBeTrue();
      expect(provisioningInfo.tenantId?.equals(testTenantId)).toBeTrue();
      expect(provisioningInfo.hardwareId).toEqual(testHardwareId);
      expect(provisioningInfo.user).toEqual('test-local-user');
      expect(provisioningInfo.domain).toEqual('test-domain.com');
      expect(provisioningInfo.localGroups).toEqual(
        expect.arrayContaining(['groupA', 'groupB', 'groupC']),
      );
    });

    it('sets tenant ID to undefined if it is empty', () => {
      const data = {
        cldflt: {
          userSession: {
            domain: 'test-domain.com',
            userName: 'test-local-user',
            userPrincipalName: '<EMAIL>',
            azureAdUserId: '',
            email: '',
            groups: 'groupA,groupB,groupC',
          },
          enableAzureAd: false,
        },
        reported: {
          identifiers: {
            serial: testSerialId.toString(),
            tenantId: '',
            hardwareId: testHardwareId,
          },
        },
        desired: {},
      };

      const provisioningInfo = ProvisioningInfo.loadFromBrowserInitPayload(data);
      expect(provisioningInfo.tenantId).toBeUndefined();
    });

    it('uses the User Principal Name if it is specified and azure AD is enabled', () => {
      const data = {
        cldflt: {
          userSession: {
            domain: 'test-domain.com',
            userName: 'test-local-user',
            userPrincipalName: '<EMAIL>',
            azureAdUserId: '',
            email: '',
            groups: 'groupA,groupB,groupC',
          },
          enableAzureAd: true,
        },
        reported: {
          identifiers: {
            serial: testSerialId.toString(),
            tenantId: testTenantId.toString(),
            hardwareId: testHardwareId,
          },
        },
        desired: {},
      };

      const provisioningInfo = ProvisioningInfo.loadFromBrowserInitPayload(data);
      expect(provisioningInfo.user).toEqual('<EMAIL>');
    });

    it('uses local username if Azure AD is not enabled', () => {
      const data = {
        cldflt: {
          userSession: {
            domain: 'test-domain.com',
            userName: 'test-local-user',
            userPrincipalName: '<EMAIL>',
            azureAdUserId: '',
            email: '',
            groups: 'groupA,groupB,groupC',
          },
          enableAzureAd: false,
        },
        reported: {
          identifiers: {
            serial: testSerialId.toString(),
            tenantId: testTenantId.toString(),
            hardwareId: testHardwareId,
          },
        },
        desired: {},
      };

      const provisioningInfo = ProvisioningInfo.loadFromBrowserInitPayload(data);
      expect(provisioningInfo.user).toEqual('test-local-user');
    });

    it('uses local username if Azure AD is enabled but User Principal Name is empty', () => {
      const data = {
        cldflt: {
          userSession: {
            domain: 'test-domain.com',
            userName: 'test-local-user',
            userPrincipalName: '',
            azureAdUserId: '',
            email: '',
            groups: 'groupA,groupB,groupC',
          },
          enableAzureAd: true,
        },
        reported: {
          identifiers: {
            serial: testSerialId.toString(),
            tenantId: testTenantId.toString(),
            hardwareId: testHardwareId,
          },
        },
        desired: {},
      };

      const provisioningInfo = ProvisioningInfo.loadFromBrowserInitPayload(data);
      expect(provisioningInfo.user).toEqual('test-local-user');
    });

    it('uses the default username if one is not provided from the native client message', () => {
      const data = {
        cldflt: {
          userSession: {
            domain: '',
            userName: '',
            userPrincipalName: '',
            azureAdUserId: '',
            email: '',
            groups: 'groupA,groupB,groupC',
          },
          enableAzureAd: false,
        },
        reported: {
          identifiers: {
            serial: testSerialId.toString(),
            tenantId: testTenantId.toString(),
            hardwareId: testHardwareId,
          },
        },
        desired: {},
      };

      const provisioningInfo = ProvisioningInfo.loadFromBrowserInitPayload(data);
      expect(provisioningInfo.user).toEqual('unknown-user');
    });

    it('throws an error if the serial property could not be found', () => {
      const data = {
        cldflt: {
          userSession: {
            domain: 'test-domain.com',
            userName: 'test-local-user',
            userPrincipalName: '<EMAIL>',
            azureAdUserId: '',
            email: '',
            groups: 'groupA,groupB,groupC',
          },
          enableAzureAd: false,
        },
        reported: {
          identifiers: {
            tenantId: testTenantId.toString(),
            hardwareId: testHardwareId,
          },
        },
        desired: {},
      };

      expect(() => ProvisioningInfo.loadFromBrowserInitPayload(data)).toThrowError();
    });

    it('throws an error if the serial ID was invalid', () => {
      const data = {
        cldflt: {
          userSession: {
            domain: 'test-domain.com',
            userName: 'test-local-user',
            userPrincipalName: '<EMAIL>',
            azureAdUserId: '',
            email: '',
            groups: 'groupA,groupB,groupC',
          },
          enableAzureAd: false,
        },
        reported: {
          identifiers: {
            serial: 'blah',
            tenantId: testTenantId.toString(),
            hardwareId: testHardwareId,
          },
        },
        desired: {},
      };

      expect(() => ProvisioningInfo.loadFromBrowserInitPayload(data)).toThrowError();
    });

    it('does not throw an error if the tenant ID was invalid', () => {
      const data = {
        cldflt: {
          userSession: {
            domain: 'test-domain.com',
            userName: 'test-local-user',
            userPrincipalName: '<EMAIL>',
            azureAdUserId: '',
            email: '',
            groups: 'groupA,groupB,groupC',
          },
          enableAzureAd: false,
        },
        reported: {
          identifiers: {
            serial: testSerialId.toString(),
            tenantId: 'blah',
            hardwareId: testHardwareId,
          },
        },
        desired: {},
      };

      expect(() => ProvisioningInfo.loadFromBrowserInitPayload(data)).not.toThrowError();
    });
  });
});

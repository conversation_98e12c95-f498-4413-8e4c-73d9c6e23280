{"configFirebaseUrlTemplate": "https://{glsHash}-devicemgt.smoothwall.cloud/api/v1.0/config/firebase", "registerDeviceUrlTemplate": "https://{glsHash}-devicemgt.smoothwall.cloud/api/v2.0/devices", "heartbeatUrlTemplate": "https://{glsHash}-devicemgt.smoothwall.cloud/api/v2.0/devices/check-in/{customerId}/{tenant}/{userId}/{deviceId}", "ingestV2UrlTemplate": "{resource}{date}/access.log.{hardwareId}.{timestamp}{sas}", "ingestV3UrlTemplate": "{resource}v3/{tenant}/_sw_cloud_hid_/{date}/{auth}/{user}/{level}/", "cloudGroupsUrlTemplate": "{resource}/v1/users/{tenant}/{domain}/{user}.json{sas}", "smoothwallAgentNativeMessagingHostName": "com.smoothwall.chrome.bridge", "qoriaAgentGrpcUrl": "http://127.0.0.1:5769"}
/**
 * Defines the names of files which can occur in a full blocklist download.
 *
 * @note Not all of these files are actually used any more.
 */
const blocklistFilenames = Object.freeze({
  /**
   * Name of the blocklist file containing information about each category.
   */
  categoryData: 'category_data',

  /**
   * Name of the blocklist file containing hashed URLs identifying child abuse material.
   * It is provided by the Internet Watch Foundation (IWF).
   */
  iwfList: 'iwflist',

  /**
   * Name of the blocklist file containing JavaScript snippets for modifying content dynamically.
   * This file is no longer used by the extension due to limitations on executing external scripts.
   */
  jsContentMods: 'jsregexpbody_cmod',

  /**
   * Name of the blocklist file which defines rules for how log levels are assigned.
   */
  logLevelRules: 'loglevelrules',

  /**
   * Name of the file which lists all the other files in the blocklist download.
   */
  manifest: 'manifest.json',

  /**
   * Name of the blocklist file which defines which URLs should never match certain categories.
   * This is used to counteract false matches by content analysis. For example, a healthcare website
   *  should not be classed as pornography, even if the content mentions sexual activity.
   */
  negativeUrlList: 'removetags',

  /**
   * Name of the blocklist file which defines regular expressions for search engine URLs.
   * This is used to extract the search terms from a search request so that they can be analysed.
   */
  searchTermExtractionPatterns: 'searchengineregexplist',

  /**
   * Name of the blocklist file which defines how different search terms should be categorised.
   */
  searchTerms: 'searchterms',

  /**
   * Name prefix for blocklist files which define how to categorised various known domains and URLs.
   * There will be multiple blocklist files starting with this prefix. The suffix will be a dot
   *  followed by the top-level domain of the URLs within it. For example, "domainsurls.com"
   *  contains URLs whose domain ends with ".com". However, only a few major top-level domains are
   *  split into their own files. All other domains go into a file called "domainsurls.other".
   */
  urlListPrefix: 'domainsurls',

  /**
   * Name of the blocklist file which defines regular expressions for applying URL modifications.
   * URL modifications can be used for things like enforcing safe search in a search engine request.
   */
  urlMods: 'regexpurls_cmod',

  /**
   * Name of the blocklist file which defines regular expressions for categorising URLs.
   * This is only used for categorising patterns which are too complex for the domains/URLs list.
   */
  urlPatterns: 'regexpurls',

  /**
   * Name of the blocklist file which defines regular expressions for video URLs.
   * This is used to extract the video IDs from video page and playback requests.
   */
  videoIdExtractionPatterns: 'videoidregexplist',

  /**
   * Name of the blocklist file which defines how known video IDs should be categorised.
   */
  videoIds: 'videoids',

  /**
   * Name of the blocklist file which defines natural language data for categorising page content.
   * This is used for content analysis.
   */
  weightedPhrases: 'weightedphrases',
});

export default blocklistFilenames;

/**
 * Defines the names of files which can occur in a blocklist diff download.
 */
const blocklistDiffFilenames = Object.freeze({
  /**
   * Name of the blocklist diff file containing hashed URLs identifying child abuse material.
   * Unlike the other blocklist files, this file does not receive partial updates. Each blocklist
   *  diff contains the complete content of the new IWF list file, meaning it gets replaced in its
   *  entirety.
   */
  iwfList: 'iwflist',

  /**
   * Name of the JSON diff file in a blocklist patch download.
   * This file is used to update most of the JSON-based files in a blocklist, such as weighted
   *  phrases and category info.
   */
  jsonDiff: 'diff.json',

  /**
   * Name of the file which lists all the other files in the blocklist download.
   */
  manifest: 'manifest.json',

  /**
   * Name of the Unified Diff file in a blocklist diff download.
   * This file is used to update the "domainsurls.*" files in the blocklist.
   *
   * @note The domains and URLs diff is implemented as a text-based diff for historical performance
   *  reasons. The underlying data is actually a JSON array. Given how heavily browser optimise
   *  JSON operations, it would probably be more performant to do this as a JSON diff in future.
   */
  unifiedDiff: 'blocklist.diff',
});

export default blocklistDiffFilenames;

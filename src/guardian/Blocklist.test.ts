import BlocklistStatus from 'constants/BlocklistStatus';
import * as path from 'path';
import { loadLocalBlocklist, makeAbridgedBlocklistFiles } from 'test-helpers/blocklist-utilities';
import { areArraysEqual } from 'utilities/Helpers';

import Blocklist from './Blocklist';
import blocklistDiffFilenames from './constants/blocklistDiffFilenames';
import blocklistFilenames from './constants/blocklistFilenames';

const oldBlocklistEpoch = 1711770303;
const newBlocklistEpoch = 1712803503;
const templateUrl = `http://test.local/blocklists/%TYPE%-%EPOCH%/%NAME%%SAS%`;

// These are real blocklists downloaded from production, with a diff to convert between them.
const oldBlocklistFiles = loadLocalBlocklist(
  path.join(__dirname, '..', '..', 'test-data', 'blocklists', `Blocklist-${oldBlocklistEpoch}`),
);

const newBlocklistFiles = loadLocalBlocklist(
  path.join(__dirname, '..', '..', 'test-data', 'blocklists', `Blocklist-${newBlocklistEpoch}`),
);

const blocklistDiffFiles = loadLocalBlocklist(
  path.join(
    __dirname,
    '..',
    '..',
    'test-data',
    'blocklists',
    `Diff-${oldBlocklistEpoch}-${newBlocklistEpoch}`,
  ),
);

// This blocklist is identical to the production one, but with heavily reduced weighted phrase data.
// Populating the weighted phrase look-up table is by far the slowest part of loading the blocklist,
//  so this is useful for testing the loading code fairly thoroughly, without the tests taking a
//  ridiculously long time. It's no use for testing patches though.
const abridgedOldBlocklistFiles = makeAbridgedBlocklistFiles(oldBlocklistFiles, 20);

// This blocklist is technically valid and contains all expected components but no actual data.
// This is useful for test the high level loading logic quickly in situations where we don't care
//  about the actual values which were loaded.
const emptyBlocklistFiles = Object.freeze({
  [blocklistFilenames.categoryData]: '{}',
  [blocklistFilenames.iwfList]: '[]',
  [blocklistFilenames.logLevelRules]: '[]',
  [blocklistFilenames.negativeUrlList]: '[]',
  [blocklistFilenames.searchTermExtractionPatterns]: '',
  [blocklistFilenames.searchTerms]: '{}',
  [`${blocklistFilenames.urlListPrefix}.com`]: '[]',
  [`${blocklistFilenames.urlListPrefix}.net`]: '[]',
  [`${blocklistFilenames.urlListPrefix}.org`]: '[]',
  [`${blocklistFilenames.urlListPrefix}.other`]: '[]',
  [`${blocklistFilenames.urlListPrefix}.uk`]: '[]',
  [blocklistFilenames.urlMods]: '',
  [blocklistFilenames.urlPatterns]: '',
  [blocklistFilenames.videoIdExtractionPatterns]: '',
  [blocklistFilenames.videoIds]: '[]',
  [blocklistFilenames.weightedPhrases]: '{}',
});

// This blocklist diff is technically valid but makes no changes.
const emptyDiff = Object.freeze({
  [blocklistDiffFilenames.unifiedDiff]: '--- a/test/file',
  [blocklistDiffFilenames.jsonDiff]: '{}',
  [blocklistDiffFilenames.iwfList]: '[]',
});

// A blocklist which is fundamentally invalid and should fail to load.
const invalidBlocklistFiles = loadLocalBlocklist(
  path.join(
    __dirname,
    '..',
    '..',
    'test-data',
    'invalid-blocklists',
    'InvalidJsonBlocklistFile-1711770303',
  ),
);

describe('Blocklist', () => {
  beforeEach(() => {
    // Suppress console messages during tests.
    jest.spyOn(console, 'debug').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  // -----------------------------------------------------------------------------------------------

  afterEach(() => {
    jest.useRealTimers();
  });

  // -----------------------------------------------------------------------------------------------

  describe('blocklistStatus', () => {
    it('returns NotLoaded if no blocking has been loaded yet', () => {
      const blocklist = new Blocklist();
      expect(blocklist.status).toEqual(BlocklistStatus.NotLoaded);
    });

    // Other statuses are covered by other tests.
  });

  // -----------------------------------------------------------------------------------------------

  describe('epoch', () => {
    it('returns undefined if no blocklist data has been loaded yet', () => {
      const blocklist = new Blocklist();
      expect(blocklist.epoch).toBeUndefined();
    });

    it('returns the epoch of the most recently loaded blocklist', () => {
      const blocklist = new Blocklist();
      blocklist.loadFromBlocklistFiles(
        emptyBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );
      expect(blocklist.epoch).toEqual(oldBlocklistEpoch);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('templateUrl', () => {
    it('returns undefined if no blocklist data has been loaded yet', () => {
      const blocklist = new Blocklist();
      expect(blocklist.templateUrl).toBeUndefined();
    });

    it('returns the template URL of the most recently loaded blocklist', () => {
      const blocklist = new Blocklist();
      blocklist.loadFromBlocklistFiles(
        emptyBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );
      expect(blocklist.templateUrl).toEqual(templateUrl);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('clear()', () => {
    it('triggers the blocklist status change event', async () => {
      const blocklist = new Blocklist();
      blocklist.loadFromBlocklistFiles(
        emptyBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );
      const onBlocklistStatusChanged = jest.fn();
      blocklist.onBlocklistStatusChanged.addListener(onBlocklistStatusChanged);
      await blocklist.clear();
      expect(onBlocklistStatusChanged).toHaveBeenCalledWith(BlocklistStatus.NotLoaded);
    });

    it('clears the stored epoch', async () => {
      const blocklist = new Blocklist();
      blocklist.loadFromBlocklistFiles(
        emptyBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );
      await blocklist.clear();
      expect(blocklist.epoch).toBeUndefined();
    });

    it('clears the stored template URL', async () => {
      const blocklist = new Blocklist();
      blocklist.loadFromBlocklistFiles(
        emptyBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );
      await blocklist.clear();
      expect(blocklist.templateUrl).toBeUndefined();
    });

    it('clears the blocklist data', async () => {
      const blocklist = new Blocklist();
      blocklist.loadFromBlocklistFiles(
        abridgedOldBlocklistFiles, // we actually need to load something for this test
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );
      await blocklist.clear();
      expect(blocklist.iwfList.length).toEqual(0);
      expect(blocklist.searchTermCategoriser.length).toEqual(0);
      expect(blocklist.urlListCategoriser.length).toEqual(0);
      expect(blocklist.urlRegexpCategoriser.length).toEqual(0);
      expect(blocklist.urlRegexpContentMods.length).toEqual(0);
      expect(blocklist.videoIdCategoriser.length).toEqual(0);
      expect(blocklist.weightedPhraseCategoriser.length).toEqual(0);
      expect(blocklist.negativeUrlListCategoriser.length).toEqual(0);
      expect(blocklist.logLevelService.length).toEqual(0);
      expect(Object.entries(blocklist.categoryData).length).toEqual(0);
    });

    it('waits until a pending asynchronous load operation finishes before clearing', async () => {
      jest.setTimeout(15000); // Increase timeout to 15 seconds
      const blocklist = new Blocklist();
      // Start an asynchronous load, and try to clear the blocklist before it finishes.
      // There should be nothing left by the time the clear finishes.
      const loadOperation = blocklist.loadFromBlocklistFilesAsync(
        abridgedOldBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );
      await blocklist.clear();
      expect(blocklist.iwfList.length).toEqual(0);
      expect(blocklist.searchTermCategoriser.length).toEqual(0);
      expect(blocklist.urlListCategoriser.length).toEqual(0);
      expect(blocklist.urlRegexpCategoriser.length).toEqual(0);
      expect(blocklist.urlRegexpContentMods.length).toEqual(0);
      expect(blocklist.videoIdCategoriser.length).toEqual(0);
      expect(blocklist.weightedPhraseCategoriser.length).toEqual(0);
      expect(blocklist.negativeUrlListCategoriser.length).toEqual(0);
      expect(blocklist.logLevelService.length).toEqual(0);
      expect(Object.entries(blocklist.categoryData).length).toEqual(0);
      expect(blocklist.status).toEqual(BlocklistStatus.NotLoaded);

      // This deliberately isn't awaited until after the clear has been verified.
      await loadOperation;
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('loadFromBlocklistFiles()', () => {
    it('successfully loads a blocklist', () => {
      // We'll just use an abridge blocklist for this test. This version of the function is only
      //  used for loading the mini-filter. We'll test the async version of the function more
      //  thoroughly as that's the one which is actually used for loading real blocklists.
      const blocklist = new Blocklist();
      blocklist.loadFromBlocklistFiles(
        abridgedOldBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );

      // Check search term analysis works.
      expect(
        blocklist.searchTermCategoriser
          .categoriseUrl(new URL('https://www.google.com/search?q=123movieshd'))
          .categories.has('24'),
      ).toBeTrue();

      // Check that URL list categorisation works.
      expect(
        blocklist.urlListCategoriser
          .categoriseUrl(new URL('https://www.online2pdf.com/convert-url-to-pdf'))
          .categoryIds.has('1225'),
      ).toBeTrue();

      // We loaded an abridged blocklist so we can only test weight phrase data that occurred in the
      //  first few entries of the file. The rest was discarded before loading.
      expect(
        blocklist.weightedPhraseCategoriser
          .categoriseContent(' 0-day ')
          .getAllCategoryIds()
          .has('30'),
      ).toBeTrue();

      // Check that regex URL categorisation works.
      expect(
        blocklist.urlRegexpCategoriser
          .categoriseUrl(new URL('http://test.local/jwplayer/foo'))
          .categoryIds.has('56'),
      ).toBeTrue();

      // Check that category data was loaded.
      expect(blocklist.categoryData['3'].name).toEqual('Pets');

      expect(blocklist.iwfList.length).toBeGreaterThan(0);
      expect(blocklist.logLevelService.length).toBeGreaterThan(0);
    }, 60000);

    it('throws an error if the blocklist data is invalid', () => {
      const blocklist = new Blocklist();
      expect(() => {
        blocklist.loadFromBlocklistFiles(
          invalidBlocklistFiles,
          oldBlocklistEpoch,
          templateUrl,
          'download',
        );
      }).toThrow();
    });

    it('clears any stored blocklist data if it fails to load', () => {
      const blocklist = new Blocklist();
      blocklist.loadFromBlocklistFiles(
        abridgedOldBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );
      try {
        blocklist.loadFromBlocklistFiles(
          invalidBlocklistFiles,
          oldBlocklistEpoch,
          templateUrl,
          'download',
        );
      } catch (e: any) {
        // Ignore the error.
      }
      expect(blocklist.status).toEqual(BlocklistStatus.NotLoaded);
      expect(blocklist.urlListCategoriser).toHaveLength(0);
      expect(blocklist.iwfList).toHaveLength(0);
    });

    it('sets the blocklist status to ready after loading', () => {
      const blocklist = new Blocklist();
      const onBlocklistStatusChanged = jest.fn();
      blocklist.onBlocklistStatusChanged.addListener(onBlocklistStatusChanged);
      blocklist.loadFromBlocklistFiles(
        emptyBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );
      expect(onBlocklistStatusChanged).toHaveBeenCalledWith(BlocklistStatus.Loading);
      expect(onBlocklistStatusChanged).toHaveBeenCalledWith(BlocklistStatus.Ready);
      expect(blocklist.status).toBe(BlocklistStatus.Ready);
    });

    it('ignores a blocklist filename that is not recognised', () => {
      const blocklist = new Blocklist();
      const files = JSON.parse(JSON.stringify(emptyBlocklistFiles)); // clone
      files['not-a-real-blocklist-file'] = 'blah';
      expect(() => {
        blocklist.loadFromBlocklistFiles(files, oldBlocklistEpoch, templateUrl, 'download');
      }).not.toThrow();
    });

    it('throws an error if there is an asynchronous load operation in progress', async () => {
      jest.useFakeTimers();

      const blocklist = new Blocklist();
      const promise = blocklist.loadFromBlocklistFilesAsync(
        emptyBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );
      // Ensure the async operation has a chance to start.
      await jest.runOnlyPendingTimersAsync();
      expect(() => {
        blocklist.loadFromBlocklistFiles(
          emptyBlocklistFiles,
          oldBlocklistEpoch,
          templateUrl,
          'download',
        );
      }).toThrow();
      await jest.runAllTimersAsync();
      await promise;
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('loadFromBlocklistFilesAsync()', () => {
    it('successfully loads a complete blocklist', async () => {
      // Note: This test uses the original (unabridged) blocklist files. This can be very slow to
      //  load, so the test timeout has been increased to compensate.

      const blocklist = new Blocklist();
      await blocklist.loadFromBlocklistFilesAsync(
        oldBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );

      // Check search term analysis works.
      expect(
        blocklist.searchTermCategoriser
          .categoriseUrl(new URL('https://www.google.com/search?q=123movieshd'))
          .categories.has('24'),
      ).toBeTrue();

      // Check that URL list categorisation works.
      expect(
        blocklist.urlListCategoriser
          .categoriseUrl(new URL('https://www.online2pdf.com/convert-url-to-pdf'))
          .categoryIds.has('1225'),
      ).toBeTrue();

      // Check that weighted phrase analysis works.
      expect(
        blocklist.weightedPhraseCategoriser
          .categoriseContent(' censorship circumvention device ')
          .getAllCategoryIds()
          .has('120'),
      ).toBeTrue();

      // Check that regex URL categorisation works.
      expect(
        blocklist.urlRegexpCategoriser
          .categoriseUrl(new URL('http://test.local/jwplayer/foo'))
          .categoryIds.has('56'),
      ).toBeTrue();

      // Check that category data was loaded.
      expect(blocklist.categoryData['3'].name).toEqual('Pets');

      expect(blocklist.iwfList.length).toBeGreaterThan(0);
      expect(blocklist.logLevelService.length).toBeGreaterThan(0);
    }, 60000);

    it('rejects the promise if the blocklist data is invalid', async () => {
      const blocklist = new Blocklist();
      await expect(
        blocklist.loadFromBlocklistFilesAsync(
          invalidBlocklistFiles,
          oldBlocklistEpoch,
          templateUrl,
          'download',
        ),
      ).toReject();
    });

    it('clears any stored blocklist data if it fails to load', async () => {
      const blocklist = new Blocklist();
      await blocklist.loadFromBlocklistFilesAsync(
        abridgedOldBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );
      try {
        await blocklist.loadFromBlocklistFilesAsync(
          invalidBlocklistFiles,
          oldBlocklistEpoch,
          templateUrl,
          'download',
        );
      } catch (e: any) {
        // Ignore the error.
      }
      expect(blocklist.status).toEqual(BlocklistStatus.NotLoaded);
      expect(blocklist.urlListCategoriser).toHaveLength(0);
      expect(blocklist.iwfList).toHaveLength(0);
    });

    it('sets the blocklist status to ready after loading', async () => {
      const blocklist = new Blocklist();
      const onBlocklistStatusChanged = jest.fn();
      blocklist.onBlocklistStatusChanged.addListener(onBlocklistStatusChanged);
      await blocklist.loadFromBlocklistFilesAsync(
        emptyBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );
      expect(onBlocklistStatusChanged).toHaveBeenCalledWith(BlocklistStatus.Loading);
      expect(onBlocklistStatusChanged).toHaveBeenCalledWith(BlocklistStatus.Ready);
      expect(blocklist.status).toBe(BlocklistStatus.Ready);
    });

    it('ignores a blocklist filename that is not recognised', async () => {
      const blocklist = new Blocklist();
      const files = JSON.parse(JSON.stringify(emptyBlocklistFiles)); // clone
      files['not-a-real-blocklist-file'] = 'blah';
      await expect(
        blocklist.loadFromBlocklistFilesAsync(files, oldBlocklistEpoch, templateUrl, 'download'),
      ).toResolve();
    });

    it('does not allow simultaneous load operations to overlap', async () => {
      // We'll test this by starting two load operations at about the same time, and ensuring the
      //  state event indicates that one load completely finished before the other started.
      const blocklist = new Blocklist();
      const onBlocklistStatusChanged = jest.fn();
      blocklist.onBlocklistStatusChanged.addListener(onBlocklistStatusChanged);

      await Promise.all([
        blocklist.loadFromBlocklistFilesAsync(
          emptyBlocklistFiles,
          oldBlocklistEpoch,
          templateUrl,
          'download',
        ),
        blocklist.loadFromBlocklistFilesAsync(
          emptyBlocklistFiles,
          oldBlocklistEpoch,
          templateUrl,
          'download',
        ),
      ]);

      expect(onBlocklistStatusChanged).toHaveBeenNthCalledWith(1, BlocklistStatus.NotLoaded);
      expect(onBlocklistStatusChanged).toHaveBeenNthCalledWith(2, BlocklistStatus.Loading);
      expect(onBlocklistStatusChanged).toHaveBeenNthCalledWith(3, BlocklistStatus.Ready);

      expect(onBlocklistStatusChanged).toHaveBeenNthCalledWith(4, BlocklistStatus.NotLoaded);
      expect(onBlocklistStatusChanged).toHaveBeenNthCalledWith(5, BlocklistStatus.Loading);
      expect(onBlocklistStatusChanged).toHaveBeenNthCalledWith(6, BlocklistStatus.Ready);
    }, 10000);
  });

  // -----------------------------------------------------------------------------------------------

  describe('patchFromBlocklistFilesAsync()', () => {
    it('correctly applies a diff which makes no changes', async () => {
      // An empty diff should never happen in reality. This test is really just making sure that
      //  other tests which rely on the empty diff will actually work.
      const blocklist = new Blocklist();
      await blocklist.loadFromBlocklistFilesAsync(
        emptyBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );

      const promise = blocklist.patchFromBlocklistFilesAsync(
        emptyDiff,
        oldBlocklistEpoch,
        newBlocklistEpoch,
        templateUrl,
      );
      await expect(promise).toResolve();
    }, 10000);

    it('correctly applies a real blocklist diff', async () => {
      // Note: This test loads two full (unabridged) blocklists and applies a patch. This can be
      //  _extremely_ slow so the test timeout has been increased to compensate.

      // Load the old blocklist from file and then apply the patch.
      const patchedBlocklist = new Blocklist();
      await patchedBlocklist.loadFromBlocklistFilesAsync(
        oldBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );
      await patchedBlocklist.patchFromBlocklistFilesAsync(
        blocklistDiffFiles,
        oldBlocklistEpoch,
        newBlocklistEpoch,
        templateUrl,
      );

      // Load the new blocklist from file. The patched blocklist should match this.
      const newBlocklist = new Blocklist();
      await newBlocklist.loadFromBlocklistFilesAsync(
        newBlocklistFiles,
        newBlocklistEpoch,
        templateUrl,
        'download',
      );

      // Check that patching the old blocklist resulted in the same underlying data as the new
      //  blocklist.
      // These tests have to bypass class member visibility to check the underlying data. Some of
      //  them also avoid using Jest expectations to compare the data directly because it would be
      //  very slow, and the output would be impossibly long if an error occurred.

      // Check that the URL lists were all patched correctly.
      const tlds = ['com', 'net', 'org', 'uk', '']; // empty string corresponds to domainsurls.other
      for (const tld of tlds) {
        // We're not comparing the arrays directly using JSON assertions here as they're way too
        //  big. The comparison would be very slow and the output would be impossibly long.
        expect(
          areArraysEqual(
            (patchedBlocklist as any)._urlListCategoriser._tldCategorisationMaps.get(tld)._map,
            (newBlocklist as any)._urlListCategoriser._tldCategorisationMaps.get(tld)._map,
          ),
        ).toBeTrue();
      }

      expect((patchedBlocklist as any)._urlRegexpContentMods._contentMods).toEqual(
        (newBlocklist as any)._urlRegexpContentMods._contentMods,
      );

      expect((patchedBlocklist as any)._urlRegexpCategoriser._urlRegexpsCategorisationMap).toEqual(
        (newBlocklist as any)._urlRegexpCategoriser._urlRegexpsCategorisationMap,
      );

      expect((patchedBlocklist as any)._searchTermCategoriser._extractionPatterns).toEqual(
        (newBlocklist as any)._searchTermCategoriser._extractionPatterns,
      );

      expect((patchedBlocklist as any)._videoIdCategoriser._extractionPatterns).toEqual(
        (newBlocklist as any)._videoIdCategoriser._extractionPatterns,
      );

      expect(
        areArraysEqual(
          (patchedBlocklist as any)._videoIdCategoriser._categorisationMap._map,
          (newBlocklist as any)._videoIdCategoriser._categorisationMap._map,
        ),
      ).toBeTrue();

      expect(patchedBlocklist.categoryData).toEqual(newBlocklist.categoryData);

      expect((patchedBlocklist as any)._weightedPhraseCategoriser._weightedPhrases).toEqual(
        (newBlocklist as any)._weightedPhraseCategoriser._weightedPhrases,
      );

      expect((patchedBlocklist as any)._searchTermCategoriser._searchTerms).toEqual(
        (newBlocklist as any)._searchTermCategoriser._searchTerms,
      );

      expect((patchedBlocklist as any)._logLevelService._parsedRules).toEqual(
        (newBlocklist as any)._logLevelService._parsedRules,
      );

      expect((patchedBlocklist as any)._iwfList._hashedUrls).toEqual(
        (newBlocklist as any)._iwfList._hashedUrls,
      );
    }, 180000);

    it('throws an error if the Unified Diff file is invalid, empty, or missing', async () => {
      const blocklist = new Blocklist();
      await blocklist.loadFromBlocklistFilesAsync(
        emptyBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );

      const modifiedDiffFiles = JSON.parse(JSON.stringify(emptyDiff));

      modifiedDiffFiles[blocklistDiffFilenames.unifiedDiff] = '@@ invalid unified diff @@';
      const promiseInvalidDiff = blocklist.patchFromBlocklistFilesAsync(
        modifiedDiffFiles,
        oldBlocklistEpoch,
        newBlocklistEpoch,
        templateUrl,
      );
      await expect(promiseInvalidDiff).toReject();

      modifiedDiffFiles[blocklistDiffFilenames.unifiedDiff] = '';
      const promiseEmptyDiff = blocklist.patchFromBlocklistFilesAsync(
        modifiedDiffFiles,
        oldBlocklistEpoch,
        newBlocklistEpoch,
        templateUrl,
      );
      await expect(promiseEmptyDiff).toReject();

      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
      delete modifiedDiffFiles[blocklistDiffFilenames.unifiedDiff];
      const promiseMissingDiff = blocklist.patchFromBlocklistFilesAsync(
        modifiedDiffFiles,
        oldBlocklistEpoch,
        newBlocklistEpoch,
        templateUrl,
      );
      await expect(promiseMissingDiff).toReject();
    });

    it('throws an error if the JSON file is invalid, empty, or missing', async () => {
      const blocklist = new Blocklist();
      await blocklist.loadFromBlocklistFilesAsync(
        emptyBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );

      const modifiedDiffFiles = JSON.parse(JSON.stringify(emptyDiff));

      modifiedDiffFiles[blocklistDiffFilenames.jsonDiff] = '} deliberately invalid json [';
      const promiseInvalidDiff = blocklist.patchFromBlocklistFilesAsync(
        modifiedDiffFiles,
        oldBlocklistEpoch,
        newBlocklistEpoch,
        templateUrl,
      );
      await expect(promiseInvalidDiff).toReject();

      modifiedDiffFiles[blocklistDiffFilenames.jsonDiff] = '';
      const promiseEmptyDiff = blocklist.patchFromBlocklistFilesAsync(
        modifiedDiffFiles,
        oldBlocklistEpoch,
        newBlocklistEpoch,
        templateUrl,
      );
      await expect(promiseEmptyDiff).toReject();

      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
      delete modifiedDiffFiles[blocklistDiffFilenames.jsonDiff];
      const promiseMissingDiff = blocklist.patchFromBlocklistFilesAsync(
        modifiedDiffFiles,
        oldBlocklistEpoch,
        newBlocklistEpoch,
        templateUrl,
      );
      await expect(promiseMissingDiff).toReject();
    });

    it('throws an error if the IWF list file is invalid, empty, or missing', async () => {
      const blocklist = new Blocklist();
      await blocklist.loadFromBlocklistFilesAsync(
        emptyBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );

      const modifiedDiffFiles = JSON.parse(JSON.stringify(emptyDiff));

      modifiedDiffFiles[blocklistDiffFilenames.iwfList] = '} deliberately invalid json [';
      const promiseInvalidFile = blocklist.patchFromBlocklistFilesAsync(
        modifiedDiffFiles,
        oldBlocklistEpoch,
        newBlocklistEpoch,
        templateUrl,
      );
      await expect(promiseInvalidFile).toReject();

      modifiedDiffFiles[blocklistDiffFilenames.iwfList] = '';
      const promiseEmptyFile = blocklist.patchFromBlocklistFilesAsync(
        modifiedDiffFiles,
        oldBlocklistEpoch,
        newBlocklistEpoch,
        templateUrl,
      );
      await expect(promiseEmptyFile).toReject();

      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
      delete modifiedDiffFiles[blocklistDiffFilenames.iwfList];
      const promiseMissingFile = blocklist.patchFromBlocklistFilesAsync(
        modifiedDiffFiles,
        oldBlocklistEpoch,
        newBlocklistEpoch,
        templateUrl,
      );
      await expect(promiseMissingFile).toReject();
    }, 10000);

    it('throws an error if no blocklist is currently loaded', async () => {
      const blocklist = new Blocklist();
      const promise = blocklist.patchFromBlocklistFilesAsync(
        emptyDiff,
        oldBlocklistEpoch,
        newBlocklistEpoch,
        templateUrl,
      );
      await expect(promise).toReject();
    });

    it('throws an error if the old epoch does not match what is already loaded', async () => {
      const blocklist = new Blocklist();
      await blocklist.loadFromBlocklistFilesAsync(
        emptyBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );

      const promise = blocklist.patchFromBlocklistFilesAsync(
        emptyDiff,
        oldBlocklistEpoch + 1000,
        newBlocklistEpoch,
        templateUrl,
      );
      await expect(promise).toReject();
    });

    it('throws an error if the template URL does not match what is already loaded', async () => {
      const blocklist = new Blocklist();
      await blocklist.loadFromBlocklistFilesAsync(
        emptyBlocklistFiles,
        oldBlocklistEpoch,
        templateUrl,
        'download',
      );

      const promise = blocklist.patchFromBlocklistFilesAsync(
        emptyDiff,
        oldBlocklistEpoch,
        newBlocklistEpoch,
        'http://test.local/some-other-blocklist',
      );
      await expect(promise).toReject();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('extractTopLevelDomain()', () => {
    interface TestCase {
      name: string;
      input: string;
      expected: string;
    }

    const testCases: TestCase[] = [
      {
        name: 'returns an empty string if the filename is empty',
        input: '',
        expected: '',
      },
      {
        name: 'returns an empty string if the filename does not contain a dot',
        input: 'blah',
        expected: '',
      },
      {
        name: 'returns an empty string if the filename contains only a dot and nothing else',
        input: '.',
        expected: '',
      },
      {
        name: 'returns an empty string if the filename contains one dot but there is nothing after it',
        input: 'blah.',
        expected: '',
      },
      {
        name: 'returns everything after the dot if the filename contains one dot',
        input: 'foo.bar',
        expected: 'bar',
      },
      {
        name: 'returns everything after the first dot if the filename contains more than one dot',
        input: 'foo.bar.blah',
        expected: 'bar.blah',
      },
      {
        name: 'returns a dot if the filename contains two dots and nothing else',
        input: '..',
        expected: '.',
      },
      {
        name: 'converts the returned string to lower-case',
        input: 'foo.BAR',
        expected: 'bar',
      },
    ];

    test.each(testCases)('$name', ({ input, expected }) => {
      const actual = Blocklist.extractTopLevelDomain(input);
      expect(actual).toEqual(expected);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('extractFilename()', () => {
    interface TestCase {
      name: string;
      input: string;
      expected: string;
    }

    const testCases: TestCase[] = [
      {
        name: 'removes parent folders from the path when it has Unix style path separators',
        input: '/path/to/file.txt',
        expected: 'file.txt',
      },
      {
        name: 'removes parent folders from the path when it has Windows style path separators',
        input: 'C:\\path\\to\\file.txt',
        expected: 'file.txt',
      },
      {
        name: 'returns the entire path if it does not contain folders',
        input: 'file.txt',
        expected: 'file.txt',
      },
      {
        name: 'does not change the case of the filename',
        input: 'File.TXT',
        expected: 'File.TXT',
      },
      {
        name: 'returns an empty string if the path is empty',
        input: '',
        expected: '',
      },
      {
        name: 'returns an empty string if the path only consists of empty space',
        input: '   ',
        expected: '',
      },
      {
        name: 'trims empty space from the end of the filename',
        input: '/path/to/file.txt   ',
        expected: 'file.txt',
      },
      {
        name: 'trims empty space from the start of the filename if the path contains no path separators',
        input: '  file.txt',
        expected: 'file.txt',
      },
      {
        name: 'does not trim empty space between the last path separator and the filename',
        input: '/path/to/  file.txt',
        expected: '  file.txt',
      },
    ];

    it.each(testCases)('$name', ({ input, expected }) => {
      expect(Blocklist.extractFilename(input)).toEqual(expected);
    });
  });
});

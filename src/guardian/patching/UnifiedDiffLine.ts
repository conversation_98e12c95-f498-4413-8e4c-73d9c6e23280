// -------------------------------------------------------------------------------------------------
// Type enumeration.

/**
 * The different types of line we expect to see in a Unified Diff file.
 */
export enum Type {
  /**
   * Gives details of the original file, before any changes were made.
   * This is referred to as the old file, or sometimes the "left" file.
   * A Unified Diff file can contain any number of diffs for different files. Each diff is usually
   *  preceded by a pair of headers: an old file header followed by a new file header.
   * In cases where the diff adds an entirely new file, the old file header may be empty, set to a
   *  path like "/dev/null", or omitted entirely.
   * In cases where the diff deletes an entire file, the new file header may be empty, set to a path
   *  like "/dev/null", or omitted entirely.
   */
  oldFileHeader,

  /**
   * Gives details of the file which results from applying the diff.
   * This is referred to as the new file, or sometimes the "right" file.
   * A Unified Diff file can contain any number of diffs for different files. Each diff is usually
   *  preceded by a pair of headers: an old file header followed by a new file header.
   * In cases where the diff adds an entirely new file, the old file header may be empty, set to a
   *  path like "/dev/null", or omitted entirely.
   * In cases where the diff deletes an entire file, the new file header may be empty, set to a path
   *  like "/dev/null", or omitted entirely.
   */
  newFileHeader,

  /**
   * Describes the size and location of a block of contiguous changes.
   * A diff can contain any number of hunks, each of which will start with a hunk descriptor.
   * It will be followed by one or more addition and/or deletion lines, optionally with 1 or more
   *  context lines.
   * Within a given diff, the hunks will always be ordered by line number (lowest first). To ensure
   *  line numbering consistency, the hunks must be applied in that order.
   */
  hunkDescriptor,

  /**
   * A hunk line which is being added by the diff.
   * The location of the addition is relative to the most recent hunk descriptor.
   */
  addition,

  /**
   * A hunk line which is being removed by the diff.
   * The location of the deletion is relative to the most recent hunk descriptor.
   */
  deletion,

  /**
   * A hunk line which is not modified by the diff but is provided to confirm the hunk position.
   * The location of the context line is relative to the most recent hunk descriptor.
   */
  context,

  /**
   * The line is empty, information-only, or cannot be parsed.
   * It's quite common for Unified Diff files to contain informational lines which don't match the
   *  Unified Diff syntax. These should generally be ignored when applying the diffs.
   */
  unknown,
}

/**
 * The types of Unified Diff line which relate to headers.
 */
export type HeaderType = Type.oldFileHeader | Type.newFileHeader;

/**
 * The types of Unified Diff line which relate to hunk lines.
 */
export type HunkLineType = Type.addition | Type.deletion | Type.context;

// -------------------------------------------------------------------------------------------------
// Base and component types.

/**
 * Base interface defining properties common to all lines parsed from Unified Diff.
 */
interface Base<T extends Type> {
  /**
   * Specifies where this line occurs in the Unified Diff file, if known.
   * This is only provided for logging and debugging purposes.
   * It is *not* the line number where a change occurs in the old or new file.
   * Line numbers are 1 based. However, this will be 0 if the information is not known.
   */
  diffLineNumber: number;

  /**
   * Indicates what type of line this is; i.e. what information it contains.
   * Check this to determine what other data to expect in this object.
   */
  type: T;
}

/**
 * Base interface defining properties common to all Unified Diff header lines.
 */
interface HeaderBase<T extends HeaderType = HeaderType> extends Base<T> {
  /**
   * The path of a file involved in the diff.
   * This will either be the old file (the one the diff applies to), or the new file (the result of
   *  applying the diff). Check the "type" property to determine which it is.
   * This path is usually relative to the command which generated the Unified Diff file.
   *
   * @note It's quite common for the root folder in the path to be different for the old and new
   *  file, e.g. the old file might be "a/foo.txt" and the new file might be "b/foo.txt".
   *  Alternatively, the file names may be different. This doesn't necessarily indicate that the
   *  diff is moving or renaming the file.
   *
   * @note In some cases, the path may be empty, set to a null path like "/dev/null", or one header
   *  may be omitted entirely. If the old file header is empty, null, or missing, then it typically
   *  means the diff is adding a new file. If the new file header is empty, null, or missing, then
   *  it typically means the diff is deleting a file.
   */
  path: string;

  /**
   * The date and time of when the file was last modified, if known.
   * If present, it should be formatted as: YYYY-MM-DD HH:MM:SS.nnnnnnnnn ±HHMM
   * However, it will not be validated by the parser.
   * This information may not be specified in all cases, in which case this string will be empty.
   */
  timestamp: string;
}

/**
 * Base interface defining properties common to all hunk lines parsed from Unified Diff.
 */
interface HunkLineBase<T extends HunkLineType = HunkLineType> extends Base<T> {
  /**
   * The line as it appears in the relevant file involved in the diff, with a Unified Diff prefix.
   * Check the type property to determine what kind of change this is.
   * If the type is addition, then this line is being added by the diff, i.e. it appears in the new
   *  file but not the old file.
   * If the type is deletion, then this line is being deleted by the diff, i.e. it appears in the
   *  old file but not the new file.
   * If the type is context, then this line is not modified by the diff, i.e. it appears in both the
   *  old and new files. A context line is provided to help confirm that nearby additions and
   *  deletions are being made in the right place.
   */
  content: string;

  /**
   * The line number (1-based) on which this line occurs, or would be inserted, in the old file.
   *
   * This is mainly relevant for deletion and context lines. It gives the absolute position of where
   *  the line occurred in the old file, before any changes were made.
   * For additions, it gives the position where the line will be inserted, before any movements have
   *  taken place due to previous additions and deletions. If there are multiple contiguous
   *  additions then they will have have the same insertion point.
   *
   * @note This is inferred from the most recent hunk descriptor and the number of other changes
   *  made so far in the hunk. It does not account for line number changes which would otherwise be
   *  corrected by locating context lines.
   *
   * @note The Unified Diff format has a quirk where the start line number in a hunk descriptor will
   *  be 1 lower than it should be if the hunk size is 0. That quirk should NOT be present in this
   *  property. This should show the corrected line number.
   */
  oldFileLineNumber: number;

  /**
   * The line number (1-based) where this line occurs or would have been deleted from the new file.
   *
   * This is mainly relevant for addition and context lines. It gives the absolute position of where
   *  the line occurs in the new file, after all changes have been made. It will be accurate even if
   *  changes are being applied incrementally, assuming all previous changes (including all previous
   *  lines in the same hunk) have already been applied. When making an addition this way, any line
   *  already at this position should be moved down to make room.
   *
   * For deletions, this is _only_ meaningful when applying changes incrementally. Assuming all
   *  previous changes (including all previous lines in the same hunk) have already been made, this
   *  gives the position of the line which should now be deleted. Note that the next line in the
   *  same hunk will have the same new file line number. This is because all subsequent lines get
   *  moved up when a deletion occurs.
   *
   * @note This is inferred from the most recent hunk descriptor and the number of other changes
   *  made so far in the hunk. It does not account for line number changes which would otherwise be
   *  corrected by locating context lines.
   *
   * @note The Unified Diff format has a quirk where the start line number in a hunk descriptor will
   *  be 1 lower than it should be if the hunk size is 0. That quirk should NOT be present in this
   *  property. This should show the corrected line number.
   */
  newFileLineNumber: number;
}

/**
 * Specifies the size and location of a contiguous block of changes in a Unified Diff file.
 * This may relate to the old or new file, depending on context.
 */
export interface HunkRange {
  /**
   * The line number where the hunk starts in the relevant file.
   *
   * @warning The Unified Diff format has a quirk where the hunk start number is one lower than it
   *  should be if the hunk size is 0. That quirk is NOT corrected here. This property gives the
   *  hunk start as it is shown in the Unified Diff file.
   */
  start: number;

  /**
   * The number of lines in the relevant file covered by this hunk.
   * If this range relates to the old file, then this will give the total number of deletion and
   *  context lines in the hunk. If this range relates to the new file, then this will give the
   *  total number of addition and context lines in the hunk.
   */
  size: number;
}

// -------------------------------------------------------------------------------------------------
// Concrete line types.

/**
 * The parsed contents of an old file header line from a Unified Diff file.
 */
export interface OldFileHeader extends HeaderBase<Type.oldFileHeader> {}

/**
 * The parsed contents of a new file header line from a Unified Diff file.
 */
export interface NewFileHeader extends HeaderBase<Type.newFileHeader> {}

/**
 * The parsed contents of a hunk descriptor from a Unified Diff file.
 */
export interface HunkDescriptor extends Base<Type.hunkDescriptor> {
  /**
   * The size and location of the block of changes in the old file.
   */
  oldRange: HunkRange;

  /**
   * The size and location of the block of changes in the new file.
   */
  newRange: HunkRange;
}

/**
 * The contents of an addition line from a hunk in a Unified Diff file.
 */
export interface Addition extends HunkLineBase<Type.addition> {}

/**
 * The contents of a deletion line from a hunk in a Unified Diff file.
 */
export interface Deletion extends HunkLineBase<Type.deletion> {}

/**
 * The contents of a context line from a hunk in a Unified Diff file.
 */
export interface Context extends HunkLineBase<Type.context> {}

/**
 * The raw contents of an unrecognised line from a Unified Diff file.
 */
export interface Unknown extends Base<Type.unknown> {
  /**
   * The full line as it appears in the Unified Diff file.
   */
  content: string;
}

// -------------------------------------------------------------------------------------------------
// Combined types.

/**
 * The parsed contents of a header line from a Unified Diff file.
 *
 * @note This is declared as a union rather than a base interface to help Typescript enforce type
 *  safety correctly.
 */
export type Header = OldFileHeader | NewFileHeader;

/**
 * The contents of an addition, deletion, or context line from a hunk in a Unified Diff file.
 *
 * @note This is declared as a union rather than a base interface to help Typescript enforce type
 *  safety correctly.
 */
export type HunkLine = Addition | Deletion | Context;

/**
 * The parsed contents of a line from a Unified Diff file.
 * Check the "type" property to determine what data to expect.
 *
 * @note This is declared as a union rather than a base interface to help Typescript enforce type
 *  safety correctly.
 */
type UnifiedDiffLine = Header | HunkDescriptor | HunkLine | Unknown;
export default UnifiedDiffLine;

import fs from 'fs';
import UnifiedDiffLine, { HunkLine, Type } from './UnifiedDiffLine';
import path from 'path';
import UnifiedDiffParser from './UnifiedDiffParser';

// -----------------------------------------------------------------------------------------------

// This is used to help structure the parameterised tests.
interface TestCase<TestFunction extends (...args: any[]) => any> {
  // The name of the test, as reported by the test runner.
  name: string;
  // The input parameters for the function being tested.
  input: Parameters<TestFunction>;
  // The expected output of the test. This only needs to include the properties we want to test.
  expected?: Partial<ReturnType<TestFunction>>;
  // If true, the test is expected to throw an error.
  throws?: boolean;
}

type TestCases<TestFunction extends (...args: any[]) => any> = Array<TestCase<TestFunction>>;

// Load an actual blocklist Unified Diff file for testing.
const blocklistUnifiedDiffPath = path.join(
  __dirname,
  '..',
  '..',
  '..',
  'test-data',
  'blocklists',
  'Diff-1711770303-1712803503',
  'blocklist.diff',
);
const blocklistUnifiedDiffFile = fs.readFileSync(blocklistUnifiedDiffPath, { encoding: 'utf-8' });

// -------------------------------------------------------------------------------------------------

describe('UnifiedDiffParser', () => {
  beforeEach(() => {
    // Suppress console output.
    jest.spyOn(console, 'debug').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  // -----------------------------------------------------------------------------------------------
  // New functionality tests

  describe('getHunkStatistics', () => {
    it('returns null when no hunk descriptor is active', () => {
      const parser = new UnifiedDiffParser();
      expect(parser.getHunkStatistics()).toBeNull();
    });

    it('returns correct statistics during hunk parsing', () => {
      const parser = new UnifiedDiffParser();

      // Set up a diff with headers
      parser.parseLine('--- a/file.txt\t2025-01-01 12:00:00.000 +0000');
      parser.parseLine('+++ b/file.txt\t2025-01-01 12:00:00.000 +0000');
      parser.parseLine('@@ -1,3 +1,4 @@');

      let stats = parser.getHunkStatistics();
      expect(stats).toEqual({
        additions: 0,
        deletions: 0,
        context: 0,
        expectedOld: 3,
        expectedNew: 4,
      });

      // Add some hunk lines
      parser.parseLine(' context line');
      parser.parseLine('-deleted line');
      parser.parseLine('+added line');

      stats = parser.getHunkStatistics();
      expect(stats).toEqual({
        additions: 1,
        deletions: 1,
        context: 1,
        expectedOld: 3,
        expectedNew: 4,
      });
    });
  });

  describe('Enhanced error handling', () => {
    it('provides detailed error context when parsing fails', () => {
      const parser = new UnifiedDiffParser();
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      // Start a diff
      parser.parseLine('--- a/file.txt\t2025-01-01 12:00:00.000 +0000');
      parser.parseLine('+++ b/file.txt\t2025-01-01 12:00:00.000 +0000');

      // Try to parse an addition without a hunk descriptor
      expect(() => {
        parser.parseLine('+invalid addition');
      }).toThrow('Malformed diff. An addition cannot occur without a hunk descriptor.');

      // Verify error logging includes parser state
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('UnifiedDiffParser - Failed to parse Unified Diff:'),
        expect.any(String),
        expect.stringContaining('\n'),
        expect.objectContaining({
          line: '+invalid addition',
          lineNumber: 3,
          parserState: expect.objectContaining({
            hasHeader: true,
            hasHunkDescriptor: false,
            isExpectingHunkLine: false,
            hunkStats: null,
          }),
        }),
      );

      consoleSpy.mockRestore();
    });

    it('handles malformed lines gracefully in parseFileLineByLine', () => {
      const malformedDiff = `--- a/file.txt
+++ b/file.txt
+invalid addition without hunk descriptor`;

      expect(() => {
        void [...UnifiedDiffParser.parseFileLineByLine(malformedDiff)];
      }).toThrow();
    });
  });

  describe('Performance and Memory Tests', () => {
    it('handles large diff files efficiently with parseFile', () => {
      // Create a large diff
      let largeDiff = '--- a/large.txt\t2025-01-01 12:00:00.000 +0000\n';
      largeDiff += '+++ b/large.txt\t2025-01-01 12:00:00.000 +0000\n';

      for (let i = 1; i <= 1000; i++) {
        largeDiff += `@@ -${i},1 +${i},1 @@\n`;
        largeDiff += `-old line ${i}\n`;
        largeDiff += `+new line ${i}\n`;
      }

      const start = performance.now();
      const parsedLines = UnifiedDiffParser.parseFile(largeDiff);
      const duration = performance.now() - start;

      expect(parsedLines.length).toBeGreaterThan(3000); // Headers + hunks + changes
      expect(duration).toBeLessThan(1000); // Should complete in under 1 second
    });

    it('handles large diff files efficiently with parseFileLineByLine', () => {
      // Create a large diff
      let largeDiff = '--- a/large.txt\t2025-01-01 12:00:00.000 +0000\n';
      largeDiff += '+++ b/large.txt\t2025-01-01 12:00:00.000 +0000\n';

      for (let i = 1; i <= 1000; i++) {
        largeDiff += `@@ -${i},1 +${i},1 @@\n`;
        largeDiff += `-old line ${i}\n`;
        largeDiff += `+new line ${i}\n`;
      }

      const start = performance.now();
      let lineCount = 0;
      for (const parsedLine of UnifiedDiffParser.parseFileLineByLine(largeDiff)) {
        lineCount++;
        // Verify line numbers are sequential
        expect(parsedLine.diffLineNumber).toBe(lineCount);
      }
      const duration = performance.now() - start;

      expect(lineCount).toBeGreaterThan(3000);
      expect(duration).toBeLessThan(1500); // Generator may be slightly slower
    });
  });

  describe('Edge Cases and Robustness', () => {
    it('handles empty diff files', () => {
      const emptyDiff = '';
      const parsedLines = UnifiedDiffParser.parseFile(emptyDiff);
      expect(parsedLines.length).toBe(1);
      expect(parsedLines[0].type).toBe(Type.unknown);
      if ('content' in parsedLines[0]) {
        expect(parsedLines[0].content).toBe('');
      }
    });

    it('handles diff files with only whitespace', () => {
      const whitespaceDiff = '\t\n\n\t\n';
      const parsedLines = UnifiedDiffParser.parseFile(whitespaceDiff);
      expect(parsedLines.length).toBe(4); // Three whitespace lines + one empty
      parsedLines.forEach((line) => {
        expect(line.type).toBe(Type.unknown);
      });
    });

    it('handles diff files with mixed line endings', () => {
      const mixedLineEndings = '--- a/file.txt\r\n+++ b/file.txt\r@@ -1,1 +1,1 @@\n-old\r\n+new';
      const parsedLines = UnifiedDiffParser.parseFile(mixedLineEndings);

      expect(parsedLines.length).toBe(5);
      expect(parsedLines[0].type).toBe(Type.oldFileHeader);
      expect(parsedLines[1].type).toBe(Type.newFileHeader);
      expect(parsedLines[2].type).toBe(Type.hunkDescriptor);
      expect(parsedLines[3].type).toBe(Type.deletion);
      expect(parsedLines[4].type).toBe(Type.addition);
    });

    it('maintains state correctly across complex parsing scenarios', () => {
      const parser = new UnifiedDiffParser();

      // Parse a complete diff
      parser.parseLine('--- a/file1.txt\t2025-01-01 12:00:00.000 +0000');
      expect(parser.hasHeader).toBe(true);
      expect(parser.hasHunkDescriptor).toBe(false);

      parser.parseLine('+++ b/file1.txt\t2025-01-01 12:00:00.000 +0000');
      parser.parseLine('@@ -1,2 +1,3 @@');
      expect(parser.hasHunkDescriptor).toBe(true);
      expect(parser.isExpectingHunkLine()).toBe(true);

      parser.parseLine(' context');
      parser.parseLine('-deleted');
      parser.parseLine('+added1');
      parser.parseLine('+added2');
      expect(parser.isExpectingHunkLine()).toBe(false); // All expected lines processed

      // Start a new diff
      parser.parseLine('--- a/file2.txt\t2025-01-01 12:00:00.000 +0000');
      expect(parser.hasHunkDescriptor).toBe(false); // Should be reset
      expect(parser.getHunkStatistics()).toBeNull();
    });

    it('handles clear() method correctly', () => {
      const parser = new UnifiedDiffParser();

      // Parse some content
      parser.parseLine('--- a/file.txt\t2025-01-01 12:00:00.000 +0000');
      parser.parseLine('+++ b/file.txt\t2025-01-01 12:00:00.000 +0000');
      parser.parseLine('@@ -1,1 +1,1 @@');
      parser.parseLine('+added');

      expect(parser.diffLineNumber).toBe(4);
      expect(parser.hasHeader).toBe(true);
      expect(parser.hasHunkDescriptor).toBe(true);

      // Clear state
      parser.clear();

      expect(parser.diffLineNumber).toBe(0);
      expect(parser.hasHeader).toBe(false);
      expect(parser.hasHunkDescriptor).toBe(false);
      expect(parser.getHunkStatistics()).toBeNull();
    });
  });

  // -----------------------------------------------------------------------------------------------
  // diffLineNumber

  describe('diffLineNumber', () => {
    it('returns zero if no lines have been parsed yet', () => {
      const parser = new UnifiedDiffParser();
      expect(parser.diffLineNumber).toEqual(0);
    });

    it('returns the line number of the most recently parsed line', () => {
      const parser = new UnifiedDiffParser();
      parser.parseLine('--- a/file.txt');
      parser.parseLine('+++ b/file.txt');
      parser.parseLine('@@ -0,0 +1,1 @@');
      parser.parseLine('+Some new content');
    });
  });

  // -----------------------------------------------------------------------------------------------
  // oldFileHeader

  describe('oldFileHeader', () => {
    it('returns null if no old file header has been parsed yet', () => {
      const parser = new UnifiedDiffParser();
      expect(parser.oldFileHeader).toBeNull();
    });

    it('returns the most recently parsed old file header', () => {
      const parser = new UnifiedDiffParser();
      parser.parseLine('--- a/file.txt\t2025-01-02 03:04:05.678 +0000');
      parser.parseLine('+++ b/file.txt\t2025-02-03 04:05:06.789 +0000');
      parser.parseLine('@@ -0,0 +1,1 @@');
      parser.parseLine('+Some new content');

      expect(parser.oldFileHeader).toMatchObject({
        type: Type.oldFileHeader,
        path: 'a/file.txt',
        timestamp: '2025-01-02 03:04:05.678 +0000',
      });
    });

    it('returns a clone of the data to ensure it cannot be accidentally modified', () => {
      const parser = new UnifiedDiffParser();
      parser.parseLine('--- a/file.txt\t2025-01-02 03:04:05.678 +0000');
      if (parser.oldFileHeader !== null) {
        parser.oldFileHeader.path = 'blah';
      }
      expect(parser.oldFileHeader?.path).toEqual('a/file.txt');
    });
  });

  // -----------------------------------------------------------------------------------------------
  // newFileHeader

  describe('oldFileHeader', () => {
    it('returns null if no new file header has been parsed yet', () => {
      const parser = new UnifiedDiffParser();
      expect(parser.newFileHeader).toBeNull();
    });

    it('returns the most recently parsed old file header', () => {
      const parser = new UnifiedDiffParser();
      parser.parseLine('--- a/file.txt\t2025-01-02 03:04:05.678 +0000');
      parser.parseLine('+++ b/file.txt\t2025-02-03 04:05:06.789 +0000');
      parser.parseLine('@@ -0,0 +1,1 @@');
      parser.parseLine('+Some new content');

      expect(parser.newFileHeader).toMatchObject({
        type: Type.newFileHeader,
        path: 'b/file.txt',
        timestamp: '2025-02-03 04:05:06.789 +0000',
      });
    });

    it('returns a clone of the data to ensure it cannot be accidentally modified', () => {
      const parser = new UnifiedDiffParser();
      parser.parseLine('+++ b/file.txt\t2025-02-03 04:05:06.789 +0000');
      if (parser.newFileHeader !== null) {
        parser.newFileHeader.path = 'blah';
      }
      expect(parser.newFileHeader?.path).toEqual('b/file.txt');
    });
  });

  // -----------------------------------------------------------------------------------------------
  // hasHeader

  describe('hasHeader', () => {
    it('returns false if no file header has been parsed yet', () => {
      const parser = new UnifiedDiffParser();
      expect(parser.hasHeader).toBeFalse();
    });

    it('returns true if an old file has been parsed', () => {
      const parser = new UnifiedDiffParser();
      parser.parseLine('--- a/file.txt\t2025-01-02 03:04:05.678 +0000');
      expect(parser.hasHeader).toBeTrue();
    });

    it('returns true if a new file has been parsed', () => {
      const parser = new UnifiedDiffParser();
      parser.parseLine('+++ b/file.txt\t2025-02-03 04:05:06.789 +0000');
      expect(parser.hasHeader).toBeTrue();
    });

    it('returns true if an old and a new file have been parsed', () => {
      const parser = new UnifiedDiffParser();
      parser.parseLine('--- a/file.txt\t2025-01-02 03:04:05.678 +0000');
      parser.parseLine('+++ b/file.txt\t2025-02-03 04:05:06.789 +0000');
      expect(parser.hasHeader).toBeTrue();
    });
  });

  // -----------------------------------------------------------------------------------------------
  // hunkDescriptor

  describe('hunkDescriptor', () => {
    it('returns null if no hunk descriptor has been parsed yet', () => {
      const parser = new UnifiedDiffParser();
      expect(parser.hunkDescriptor).toBeNull();
    });

    it('returns the most recently parsed hunk descriptor in the current diff', () => {
      const parser = new UnifiedDiffParser();
      parser.parseLine('--- a/file.txt\t2025-01-02 03:04:05.678 +0000');
      parser.parseLine('+++ b/file.txt\t2025-02-03 04:05:06.789 +0000');
      parser.parseLine('@@ -1,2 +3,4 @@');
      parser.parseLine(' blah blah blah');

      expect(parser.hunkDescriptor).toMatchObject({
        type: Type.hunkDescriptor,
        oldRange: { start: 1, size: 2 },
        newRange: { start: 3, size: 4 },
      });
    });

    it('returns null if a header has been parsed since the last hunk descriptor', () => {
      const parser = new UnifiedDiffParser();
      parser.parseLine('--- a/file1.txt\t2025-01-02 03:04:05.678 +0000');
      parser.parseLine('+++ b/file1.txt\t2025-02-03 04:05:06.789 +0000');
      parser.parseLine('@@ -0,0 +1,1 @@');
      parser.parseLine('+blah blah blah');
      parser.parseLine('--- a/file2.txt\t2025-01-02 03:04:05.678 +0000');
      expect(parser.hunkDescriptor).toBeNull();
    });

    it('returns a clone of the data to ensure it cannot be accidentally modified', () => {
      const parser = new UnifiedDiffParser();
      parser.parseLine('--- a/file.txt\t2025-01-02 03:04:05.678 +0000');
      parser.parseLine('+++ b/file.txt\t2025-02-03 04:05:06.789 +0000');
      parser.parseLine('@@ -1,2 +3,4 @@');
      parser.parseLine(' blah blah blah');
      if (parser.hunkDescriptor !== null) {
        parser.hunkDescriptor.newRange.start = 789;
      }
      expect(parser.hunkDescriptor?.newRange.start).toEqual(3);
    });
  });

  // -----------------------------------------------------------------------------------------------
  // hasHunkDescriptor

  describe('hasHunkDescriptor', () => {
    it('returns false if no hunk descriptor has been parsed yet', () => {
      const parser = new UnifiedDiffParser();
      expect(parser.hasHunkDescriptor).toBeFalse();
    });

    it('returns true if a hunk descriptor has been parsed in the current diff', () => {
      const parser = new UnifiedDiffParser();
      parser.parseLine('--- a/file.txt\t2025-01-02 03:04:05.678 +0000');
      parser.parseLine('+++ b/file.txt\t2025-02-03 04:05:06.789 +0000');
      parser.parseLine('@@ -1,2 +3,4 @@');
      parser.parseLine(' blah blah blah');
      expect(parser.hasHunkDescriptor).toBeTrue();
    });

    it('returns false if a header has been parsed since the last hunk descriptor', () => {
      const parser = new UnifiedDiffParser();
      parser.parseLine('--- a/file1.txt\t2025-01-02 03:04:05.678 +0000');
      parser.parseLine('+++ b/file1.txt\t2025-02-03 04:05:06.789 +0000');
      parser.parseLine('@@ -0,0 +1,1 @@');
      parser.parseLine('+blah blah blah');
      parser.parseLine('--- a/file2.txt\t2025-01-02 03:04:05.678 +0000');
      expect(parser.hasHunkDescriptor).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------
  // isExpectingHunkLine()

  describe('isExpectingHunkLine()', () => {
    it('returns false if no lines have been parsed yet', () => {
      const parser = new UnifiedDiffParser();
      expect(parser.isExpectingHunkLine()).toBeFalse();
    });

    it('returns false if no hunk descriptor has been parsed yet in the current diff', () => {
      const parser = new UnifiedDiffParser();
      parser.parseLine('--- a/file1.txt\t2025-01-02 03:04:05.678 +0000');
      parser.parseLine('+++ b/file1.txt\t2025-02-03 04:05:06.789 +0000');
      expect(parser.isExpectingHunkLine()).toBeFalse();
    });

    it('returns true if it has not yet parsed all hunk lines in the current diff', () => {
      const parser = new UnifiedDiffParser();
      parser.parseLine('--- a/file.txt\t2025-01-02 03:04:05.678 +0000');
      parser.parseLine('+++ b/file.txt\t2025-02-03 04:05:06.789 +0000');
      parser.parseLine('@@ -10,3 +10,3 @@');
      expect(parser.isExpectingHunkLine()).toBeTrue();
      parser.parseLine(' hunk line 1');
      parser.parseLine('-hunk line 2');
      parser.parseLine('+hunk line 3');
      expect(parser.isExpectingHunkLine()).toBeTrue();
    });

    it('returns false if it has parsed all the hunk lines in the current diff', () => {
      const parser = new UnifiedDiffParser();
      parser.parseLine('--- a/file.txt\t2025-01-02 03:04:05.678 +0000');
      parser.parseLine('+++ b/file.txt\t2025-02-03 04:05:06.789 +0000');
      parser.parseLine('@@ -10,3 +10,3 @@');
      parser.parseLine(' hunk line 1');
      parser.parseLine('-hunk line 2');
      parser.parseLine('+hunk line 3');
      parser.parseLine(' hunk line 4');
      expect(parser.isExpectingHunkLine()).toBeFalse();
    });

    it('returns false if a header has been parsed since the last hunk descriptor', () => {
      const parser = new UnifiedDiffParser();
      parser.parseLine('--- a/file1.txt\t2025-01-02 03:04:05.678 +0000');
      parser.parseLine('+++ b/file1.txt\t2025-02-03 04:05:06.789 +0000');
      parser.parseLine('@@ -0,0 +1,1 @@');
      parser.parseLine('+blah blah blah');
      parser.parseLine('--- a/file2.txt\t2025-01-02 03:04:05.678 +0000');
      expect(parser.isExpectingHunkLine()).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------
  // parseFile()

  describe('parseFile()', () => {
    it('returns an array of the parsed lines in the file', () => {
      // Note: It's important to ensure there are no unintended leading or trailing spaces.
      const input = `\
diff -Nr -u0 a/file.txt b/file.txt
--- a/file.txt\t2025-01-02 03:04:05.678 +0000
+++ b/file.txt\t2025-02-03 04:05:06.789 +0000
@@ -10,3 +10,3 @@
 hunk line 1
-hunk line 2
+hunk line 3
 hunk line 4`;

      const expectedOutput: Array<Partial<UnifiedDiffLine>> = [
        {
          diffLineNumber: 1,
          type: Type.unknown,
          content: 'diff -Nr -u0 a/file.txt b/file.txt',
        },
        {
          diffLineNumber: 2,
          type: Type.oldFileHeader,
          path: 'a/file.txt',
          timestamp: '2025-01-02 03:04:05.678 +0000',
        },
        {
          diffLineNumber: 3,
          type: Type.newFileHeader,
          path: 'b/file.txt',
          timestamp: '2025-02-03 04:05:06.789 +0000',
        },
        {
          diffLineNumber: 4,
          type: Type.hunkDescriptor,
          oldRange: { start: 10, size: 3 },
          newRange: { start: 10, size: 3 },
        },
        {
          diffLineNumber: 5,
          type: Type.context,
          content: 'hunk line 1',
        },
        {
          diffLineNumber: 6,
          type: Type.deletion,
          content: 'hunk line 2',
        },
        {
          diffLineNumber: 7,
          type: Type.addition,
          content: 'hunk line 3',
        },
        {
          diffLineNumber: 8,
          type: Type.context,
          content: 'hunk line 4',
        },
      ];

      expect(UnifiedDiffParser.parseFile(input)).toMatchObject(expectedOutput);
    });

    it('throws an error if parsing fails', () => {
      // The hunk descriptor is deliberately invalid here.
      const input = `\
diff -Nr -u0 a/file.txt b/file.txt
--- a/file.txt\t2025-01-02 03:04:05.678 +0000
+++ b/file.txt\t2025-02-03 04:05:06.789 +0000
@@ 10,3,10,3 @@
 hunk line 1
-hunk line 2
+hunk line 3
 hunk line 4`;

      expect(() => {
        UnifiedDiffParser.parseFile(input);
      }).toThrow();
    });

    it('handles Unix, Windows, and old Mac style line endings', () => {
      const unixInput = '--- a/file.txt\n+++ b/file.txt\n@@ -0,0 +1,1 @@\n+new line';
      const winInput = unixInput.replace('\n', '\r\n');
      const macInput = unixInput.replace('\n', '\r');

      const expectedOutput: Array<Partial<UnifiedDiffLine>> = [
        {
          diffLineNumber: 1,
          type: Type.oldFileHeader,
          path: 'a/file.txt',
          timestamp: '',
        },
        {
          diffLineNumber: 2,
          type: Type.newFileHeader,
          path: 'b/file.txt',
          timestamp: '',
        },
        {
          diffLineNumber: 3,
          type: Type.hunkDescriptor,
          oldRange: { start: 0, size: 0 },
          newRange: { start: 1, size: 1 },
        },
        {
          diffLineNumber: 4,
          type: Type.addition,
          content: 'new line',
        },
      ];

      expect(UnifiedDiffParser.parseFile(unixInput)).toMatchObject(expectedOutput);
      expect(UnifiedDiffParser.parseFile(winInput)).toMatchObject(expectedOutput);
      expect(UnifiedDiffParser.parseFile(macInput)).toMatchObject(expectedOutput);
    });

    it('successfully parses a Unified Diff file from the blocklist', () => {
      let numFilesSeen = 0;
      let hasHadChangesForCurrentFile = false;
      const expectedFilenames = [
        'domainsurls.com',
        'domainsurls.net',
        'domainsurls.org',
        'domainsurls.other',
        'domainsurls.uk',
        'removetags',
      ];

      const lines = UnifiedDiffParser.parseFile(blocklistUnifiedDiffFile);

      for (const parsedLine of lines) {
        // An old file header starts a new diff for a new target file.
        if (parsedLine.type === Type.oldFileHeader) {
          // Ensure the previous diff had at least one addition or deletion. If it didn't then
          //  we've probably missed something during parsing.
          if (numFilesSeen > 0) {
            expect(hasHadChangesForCurrentFile).toBeTrue();
          }
          hasHadChangesForCurrentFile = false;

          // Check that the diff refers to one of the expected blocklist filenames.
          const filename = path.basename(parsedLine.path);
          expect(filename).toBeOneOf(expectedFilenames);
          numFilesSeen++;
          continue;
        }

        if (parsedLine.type === Type.addition || parsedLine.type === Type.deletion) {
          hasHadChangesForCurrentFile = true;
          continue;
        }

        // If a line was parsed as unknown then ensure it didn't look like a functional line.
        if (parsedLine.type === Type.unknown) {
          expect(parsedLine.content).not.toStartWith('+'); // addition or new file header
          expect(parsedLine.content).not.toStartWith('-'); // deletion or old file header
          expect(parsedLine.content).not.toStartWith(' '); // context
          expect(parsedLine.content).not.toStartWith('@@'); // hunk descriptor
        }
      }

      // Make sure at least one file was parsed.
      expect(numFilesSeen).toBeGreaterThan(0);

      // Make sure the last file had least one change.
      expect(hasHadChangesForCurrentFile).toBeTrue();
    });
  });

  // -----------------------------------------------------------------------------------------------
  // parseFileLineByLine()

  describe('parseFileLineByLine()', () => {
    it('returns a generator which yields each parsed line of the specified file', () => {
      // Note: It's important to ensure there are no unintended leading or trailing spaces.
      const file = `\
diff -Nr -u0 a/file.txt b/file.txt
--- a/file.txt\t2025-01-02 03:04:05.678 +0000
+++ b/file.txt\t2025-02-03 04:05:06.789 +0000
@@ -10,3 +10,3 @@
 hunk line 1
-hunk line 2
+hunk line 3
 hunk line 4`;

      const generator = UnifiedDiffParser.parseFileLineByLine(file);

      // Manually iterate over each line and check that it was parsed successfully.
      expect(generator.next().value).toMatchObject({
        diffLineNumber: 1,
        type: Type.unknown,
        content: 'diff -Nr -u0 a/file.txt b/file.txt',
      });

      expect(generator.next().value).toMatchObject({
        diffLineNumber: 2,
        type: Type.oldFileHeader,
        path: 'a/file.txt',
        timestamp: '2025-01-02 03:04:05.678 +0000',
      });

      expect(generator.next().value).toMatchObject({
        diffLineNumber: 3,
        type: Type.newFileHeader,
        path: 'b/file.txt',
        timestamp: '2025-02-03 04:05:06.789 +0000',
      });

      expect(generator.next().value).toMatchObject({
        diffLineNumber: 4,
        type: Type.hunkDescriptor,
        oldRange: { start: 10, size: 3 },
        newRange: { start: 10, size: 3 },
      });

      expect(generator.next().value).toMatchObject({
        diffLineNumber: 5,
        type: Type.context,
        content: 'hunk line 1',
      });

      expect(generator.next().value).toMatchObject({
        diffLineNumber: 6,
        type: Type.deletion,
        content: 'hunk line 2',
      });

      expect(generator.next().value).toMatchObject({
        diffLineNumber: 7,
        type: Type.addition,
        content: 'hunk line 3',
      });

      expect(generator.next().value).toMatchObject({
        diffLineNumber: 8,
        type: Type.context,
        content: 'hunk line 4',
      });

      expect(generator.next().done).toBeTrue();
    });

    it('returns a generator which throws an error if parsing a line fails', () => {
      // The hunk descriptor is deliberately invalid here.
      const file = `\
diff -Nr -u0 a/file.txt b/file.txt
--- a/file.txt\t2025-01-02 03:04:05.678 +0000
+++ b/file.txt\t2025-02-03 04:05:06.789 +0000
@@ 10,3,10,3 @@
 hunk line 1
-hunk line 2
+hunk line 3
 hunk line 4`;

      // The error will only be thrown when the generator reaches the invalid line.
      const generator = UnifiedDiffParser.parseFileLineByLine(file);
      expect(() => generator.next()).not.toThrow(); // unknown line
      expect(() => generator.next()).not.toThrow(); // old file header
      expect(() => generator.next()).not.toThrow(); // new file header
      expect(() => generator.next()).toThrow(); // invalid hunk descriptor
    });

    it('handles Unix, Windows, and old Mac style line endings', () => {
      const unixInput = '--- a/file.txt\n+++ b/file.txt\n@@ -0,0 +1,1 @@\n+new line';
      const winInput = unixInput.replace('\n', '\r\n');
      const macInput = unixInput.replace('\n', '\r');

      const expectedOutput: Array<Partial<UnifiedDiffLine>> = [
        {
          diffLineNumber: 1,
          type: Type.oldFileHeader,
          path: 'a/file.txt',
          timestamp: '',
        },
        {
          diffLineNumber: 2,
          type: Type.newFileHeader,
          path: 'b/file.txt',
          timestamp: '',
        },
        {
          diffLineNumber: 3,
          type: Type.hunkDescriptor,
          oldRange: { start: 0, size: 0 },
          newRange: { start: 1, size: 1 },
        },
        {
          diffLineNumber: 4,
          type: Type.addition,
          content: 'new line',
        },
      ];

      expect([...UnifiedDiffParser.parseFileLineByLine(unixInput)]).toMatchObject(expectedOutput);
      expect([...UnifiedDiffParser.parseFileLineByLine(winInput)]).toMatchObject(expectedOutput);
      expect([...UnifiedDiffParser.parseFileLineByLine(macInput)]).toMatchObject(expectedOutput);
    });

    it('successfully parses a Unified Diff file from the blocklist', () => {
      let numFilesSeen = 0;
      let hasHadChangesForCurrentFile = false;
      const expectedFilenames = [
        'domainsurls.com',
        'domainsurls.net',
        'domainsurls.org',
        'domainsurls.other',
        'domainsurls.uk',
        'removetags',
      ];

      for (const parsedLine of UnifiedDiffParser.parseFileLineByLine(blocklistUnifiedDiffFile)) {
        // An old file header starts a new diff for a new target file.
        if (parsedLine.type === Type.oldFileHeader) {
          // Ensure the previous diff had at least one addition or deletion. If it didn't then
          //  we've probably missed something during parsing.
          if (numFilesSeen > 0) {
            expect(hasHadChangesForCurrentFile).toBeTrue();
          }
          hasHadChangesForCurrentFile = false;

          // Check that the diff refers to one of the expected blocklist filenames.
          const filename = path.basename(parsedLine.path);
          expect(filename).toBeOneOf(expectedFilenames);
          numFilesSeen++;
          continue;
        }

        if (parsedLine.type === Type.addition || parsedLine.type === Type.deletion) {
          hasHadChangesForCurrentFile = true;
          continue;
        }

        // If a line was parsed as unknown then ensure it didn't look like a functional line.
        if (parsedLine.type === Type.unknown) {
          expect(parsedLine.content).not.toStartWith('+'); // addition or new file header
          expect(parsedLine.content).not.toStartWith('-'); // deletion or old file header
          expect(parsedLine.content).not.toStartWith(' '); // context
          expect(parsedLine.content).not.toStartWith('@@'); // hunk descriptor
        }
      }

      // Make sure at least one file was parsed.
      expect(numFilesSeen).toBeGreaterThan(0);

      // Make sure the last file had least one change.
      expect(hasHadChangesForCurrentFile).toBeTrue();
    });
  });

  // -----------------------------------------------------------------------------------------------
  // parseLine()

  describe('parseLine()', () => {
    describe('general', () => {
      it('starts line numbering at 1 and increments it on each call', () => {
        const parser = new UnifiedDiffParser();
        expect(parser.parseLine('--- a/file1.txt').diffLineNumber).toEqual(1);
        expect(parser.parseLine('+++ b/file1.txt').diffLineNumber).toEqual(2);
        expect(parser.parseLine('@@ -1,0 +1,1 @@').diffLineNumber).toEqual(3);
        expect(parser.parseLine('+Some new content').diffLineNumber).toEqual(4);
      });
    });

    describe('Header', () => {
      it('correctly parses an old file header', () => {
        const parser = new UnifiedDiffParser();
        const actual = parser.parseLine('--- a/file.txt\t2025-01-02 33:44:55.000 +0000');
        expect(actual).toMatchObject({
          type: Type.oldFileHeader,
          path: 'a/file.txt',
          timestamp: '2025-01-02 33:44:55.000 +0000',
        });
      });

      it('correctly parses a new file header', () => {
        const parser = new UnifiedDiffParser();
        const actual = parser.parseLine('+++ a/file.txt\t2025-01-02 33:44:55.000 +0000');
        expect(actual).toMatchObject({
          type: Type.newFileHeader,
          path: 'a/file.txt',
          timestamp: '2025-01-02 33:44:55.000 +0000',
        });
      });

      it('clears previous header and hunk state if an old file header is parsed', () => {
        const parser = new UnifiedDiffParser();
        parser.parseLine('--- a/file1.txt');
        parser.parseLine('+++ b/file1.txt');
        parser.parseLine('@@ -1,0 +1,1 @@');
        parser.parseLine('+Some new content');

        // Make sure we have state from the lines parsed above, then make sure it gets updated.
        expect(parser.newFileHeader).not.toBeNull();
        parser.parseLine('--- a/file2.txt');
        expect(parser.oldFileHeader?.path).toEqual('a/file2.txt');
        expect(parser.newFileHeader).toBeNull();
        expect(parser.hasHunkDescriptor).toBeFalse();
      });

      it('retains old file header state if a new file header is parsed on the next line', () => {
        // If an old and new file header occur on consecutive lines then they go together to
        //  describe the new diff. We should retain both in the parsing state in that case.
        const parser = new UnifiedDiffParser();
        parser.parseLine('--- a/file.txt');
        parser.parseLine('+++ b/file.txt');
        expect(parser.oldFileHeader?.path).toEqual('a/file.txt');
      });

      it('clears previous header and hunk state if a new file header is parsed but an old file header was not on the previous line', () => {
        // If a new file header occurs on its own, without an old file header on the previous line,
        //  then it must be the start of an entirely new diff which doesn't have an old file header.
        // It should clear any old file header state in that case.

        const parser = new UnifiedDiffParser();
        parser.parseLine('--- a/file1.txt');
        parser.parseLine('+++ b/file1.txt');
        parser.parseLine('@@ -1,0 +1,1 @@');
        parser.parseLine('+Some new content');

        // Make sure we have state from the lines parsed above, then make sure it gets updated.
        expect(parser.oldFileHeader).not.toBeNull();
        expect(parser.newFileHeader).not.toBeNull();
        parser.parseLine('+++ b/file2.txt');
        expect(parser.oldFileHeader).toBeNull();
        expect(parser.newFileHeader?.path).toEqual('b/file2.txt');
        expect(parser.hasHunkDescriptor).toBeFalse();
      });

      it('parses an ambiguous line as an old file header instead of a deletion if the latest hunk has finished', () => {
        const parser = new UnifiedDiffParser();
        // Parse a diff containing a complete hunk.
        parser.parseLine('--- a/file1.txt');
        parser.parseLine('+++ b/file1.txt');
        parser.parseLine('@@ -1,0 +2,1 @@');
        parser.parseLine('+Some new content');
        // Ensure an old file header is interpreted correctly.
        const actual = parser.parseLine('--- a/file2.txt');
        expect(actual.type).toEqual(Type.oldFileHeader);
      });

      it('parses an ambiguous line as a new file header instead of an addition if the latest hunk has finished', () => {
        const parser = new UnifiedDiffParser();
        // Parse a diff containing a complete hunk.
        parser.parseLine('--- a/file1.txt');
        parser.parseLine('+++ b/file1.txt');
        parser.parseLine('@@ -1,0 +2,1 @@');
        parser.parseLine('+Some new content');
        // Ensure a new file header is interpreted correctly.
        const actual = parser.parseLine('+++ b/file2.txt');
        expect(actual.type).toEqual(Type.newFileHeader);
      });
    });

    describe('HunkDescriptor', () => {
      it('correctly parses a valid hunk descriptor', () => {
        const parser = new UnifiedDiffParser();
        parser.parseLine('--- a/file1.txt');
        parser.parseLine('+++ b/file1.txt');
        const actual = parser.parseLine('@@ -10,20 +30,40 @@');
        expect(actual).toMatchObject({
          type: Type.hunkDescriptor,
          oldRange: { start: 10, size: 20 },
          newRange: { start: 30, size: 40 },
        });
      });

      it('throws an error if the line contains an invalid hunk descriptor', () => {
        const parser = new UnifiedDiffParser();
        // A hunk descriptor cannot occur without a header.
        parser.parseLine('--- a/file1.txt');
        parser.parseLine('+++ b/file1.txt');
        expect(() => parser.parseLine('@@ 1,2,3,4,5 @@')).toThrow();
      });

      it('throws an error if a hunk descriptor is parsed before a header', () => {
        const parser = new UnifiedDiffParser();
        expect(() => parser.parseLine('@@ -1,2 +3,4 @@')).toThrow();
      });

      it('updates hunk state if a hunk descriptor is parsed', () => {
        const parser = new UnifiedDiffParser();
        parser.parseLine('--- a/file1.txt');
        parser.parseLine('+++ b/file1.txt');
        // Hunk state should be set when parsing a hunk descriptor.
        parser.parseLine('@@ -10,1 +20,1 @@');
        expect(parser.hunkDescriptor).toMatchObject({
          oldRange: { start: 10, size: 1 },
          newRange: { start: 20, size: 1 },
        });
        // Finish parsing the hunk.
        parser.parseLine('-blah blah blah');
        parser.parseLine('+foo foo foo');

        // State should be overwritten when parsing another hunk descriptor.
        parser.parseLine('@@ -30,2 +40,3 @@');
        expect(parser.hunkDescriptor).toMatchObject({
          oldRange: { start: 30, size: 2 },
          newRange: { start: 40, size: 3 },
        });
      });
    });

    describe('HunkLine', () => {
      it('correctly parses an addition hunk line', () => {
        const parser = new UnifiedDiffParser();
        parser.parseLine('--- a/file1.txt');
        parser.parseLine('+++ b/file1.txt');
        parser.parseLine('@@ -1,0 +1,1 @@');
        const actual = parser.parseLine('+blah blah blah');
        expect(actual).toMatchObject({
          type: Type.addition,
          content: 'blah blah blah',
        });
      });

      it('correctly parses a deletion hunk line', () => {
        const parser = new UnifiedDiffParser();
        parser.parseLine('--- a/file1.txt');
        parser.parseLine('+++ b/file1.txt');
        parser.parseLine('@@ -1,1 +1,0 @@');
        const actual = parser.parseLine('-blah blah blah');
        expect(actual).toMatchObject({
          type: Type.deletion,
          content: 'blah blah blah',
        });
      });

      it('correctly parses a context hunk line', () => {
        const parser = new UnifiedDiffParser();
        parser.parseLine('--- a/file1.txt');
        parser.parseLine('+++ b/file1.txt');
        parser.parseLine('@@ -1,1 +1,1 @@');
        const actual = parser.parseLine(' blah blah blah');
        expect(actual).toMatchObject({
          type: Type.context,
          content: 'blah blah blah',
        });
      });

      it('correctly infers the old file line number for a hunk line when hunk size is non-zero', () => {
        const parser = new UnifiedDiffParser();
        parser.parseLine('--- a/file1.txt');
        parser.parseLine('+++ b/file1.txt');
        parser.parseLine('@@ -198,6 +213,5 @@');
        expect((parser.parseLine(' hunk line 1') as HunkLine).oldFileLineNumber).toEqual(198);
        expect((parser.parseLine('-hunk line 2') as HunkLine).oldFileLineNumber).toEqual(199);
        expect((parser.parseLine('-hunk line 3') as HunkLine).oldFileLineNumber).toEqual(200);
        expect((parser.parseLine('+hunk line 4') as HunkLine).oldFileLineNumber).toEqual(201);
        // Note: The old file line number stays the same after an addition because the added line
        //  doesn't occur in the old file. This value only gives an indication of where the addition
        //  is going to be, relative to adjacent lines.
        expect((parser.parseLine(' hunk line 5') as HunkLine).oldFileLineNumber).toEqual(201);
        expect((parser.parseLine('-hunk line 6') as HunkLine).oldFileLineNumber).toEqual(202);
        expect((parser.parseLine('+hunk line 7') as HunkLine).oldFileLineNumber).toEqual(203);
        expect((parser.parseLine(' hunk line 8') as HunkLine).oldFileLineNumber).toEqual(203);

        // Check that the old file line number is correct if we start a new hunk. This ensures no
        //  state is accidentally retained from one hunk to the next.
        parser.parseLine('@@ -302,3 +316,3 @@');
        expect((parser.parseLine(' hunk line A') as HunkLine).oldFileLineNumber).toEqual(302);
        expect((parser.parseLine('-hunk line B') as HunkLine).oldFileLineNumber).toEqual(303);
        expect((parser.parseLine('+hunk line C') as HunkLine).oldFileLineNumber).toEqual(304);
        expect((parser.parseLine(' hunk line D') as HunkLine).oldFileLineNumber).toEqual(304);
      });

      it('correctly infers the old file line number for a hunk line when hunk size is zero', () => {
        // This test checks that the parser compensates for a quirk in the Unified Diff format. The
        //  hunk start number specified in the file will be 1 lower than it should be if the
        //  corresponding hunk size is 0.
        // The size of an old file hunk can only be zero if the hunk consists entirely of additions.

        const parser = new UnifiedDiffParser();
        parser.parseLine('--- a/file1.txt');
        parser.parseLine('+++ b/file1.txt');
        parser.parseLine('@@ -128,0 +213,3 @@');
        expect((parser.parseLine('+hunk line 1') as HunkLine).oldFileLineNumber).toEqual(129);
        expect((parser.parseLine('+hunk line 2') as HunkLine).oldFileLineNumber).toEqual(129);
        expect((parser.parseLine('+hunk line 3') as HunkLine).oldFileLineNumber).toEqual(129);
      });

      it('correctly infers the new file line number for a hunk line when hunk size is non-zero', () => {
        const parser = new UnifiedDiffParser();
        parser.parseLine('--- a/file1.txt');
        parser.parseLine('+++ b/file1.txt');
        parser.parseLine('@@ -198,6 +213,5 @@');
        expect((parser.parseLine(' hunk line 1') as HunkLine).newFileLineNumber).toEqual(213);
        // Note: The new file line number stays the same after a deletion because the deleted line
        //  doesn't occur in the new file. Subsequent lines effectively move up to take its place.
        expect((parser.parseLine('-hunk line 2') as HunkLine).newFileLineNumber).toEqual(214);
        expect((parser.parseLine('-hunk line 3') as HunkLine).newFileLineNumber).toEqual(214);
        expect((parser.parseLine('+hunk line 4') as HunkLine).newFileLineNumber).toEqual(214);
        expect((parser.parseLine(' hunk line 5') as HunkLine).newFileLineNumber).toEqual(215);
        expect((parser.parseLine('-hunk line 6') as HunkLine).newFileLineNumber).toEqual(216);
        expect((parser.parseLine('+hunk line 7') as HunkLine).newFileLineNumber).toEqual(216);
        expect((parser.parseLine(' hunk line 8') as HunkLine).newFileLineNumber).toEqual(217);

        // Check that the new file line number is correct if we start a new hunk. This ensures no
        //  state is accidentally retained from one hunk to the next.
        parser.parseLine('@@ -302,3 +316,3 @@');
        expect((parser.parseLine(' hunk line A') as HunkLine).newFileLineNumber).toEqual(316);
        expect((parser.parseLine('-hunk line B') as HunkLine).newFileLineNumber).toEqual(317);
        expect((parser.parseLine('+hunk line C') as HunkLine).newFileLineNumber).toEqual(317);
        expect((parser.parseLine(' hunk line D') as HunkLine).newFileLineNumber).toEqual(318);
      });

      it('correctly infers the new file line number for a hunk line when hunk size is zero', () => {
        // This test checks that the parser compensates for a quirk in the Unified Diff format. The
        //  hunk start number specified in the file will be 1 lower than it should be if the
        //  corresponding hunk size is 0.
        // The size of a new file hunk can only be zero if the hunk consists entirely of deletions.

        const parser = new UnifiedDiffParser();
        parser.parseLine('--- a/file1.txt');
        parser.parseLine('+++ b/file1.txt');
        parser.parseLine('@@ -128,3 +213,0 @@');
        expect((parser.parseLine('-hunk line 1') as HunkLine).newFileLineNumber).toEqual(214);
        expect((parser.parseLine('-hunk line 2') as HunkLine).newFileLineNumber).toEqual(214);
        expect((parser.parseLine('-hunk line 3') as HunkLine).newFileLineNumber).toEqual(214);
      });

      it('parses an ambiguous line as a hunk line not a header if the current hunk has not finished yet', () => {
        const parser = new UnifiedDiffParser();
        parser.parseLine('--- a/file1.txt');
        parser.parseLine('+++ b/file1.txt');
        parser.parseLine('@@ -12,5 +18,6 @@');

        expect(parser.parseLine('--- a/file2.txt')).toMatchObject({
          type: Type.deletion,
          content: '-- a/file2.txt',
        });

        expect(parser.parseLine('+++ b/file2.txt')).toMatchObject({
          type: Type.addition,
          content: '++ b/file2.txt',
        });
      });

      it('throws an error if a hunk line occurs before a hunk descriptor', () => {
        const parser = new UnifiedDiffParser();
        parser.parseLine('--- a/file.txt');
        parser.parseLine('+++ b/file.txt');
        expect(() => parser.parseLine('+blah blah blah')).toThrow();
        expect(() => parser.parseLine('-blah blah blah')).toThrow();
        expect(() => parser.parseLine(' blah blah blah')).toThrow();
      });
    });

    describe('Unknown', () => {
      it('parses a line as Unknown if it is empty', () => {
        const parser = new UnifiedDiffParser();
        const actual = parser.parseLine('');
        expect(actual).toMatchObject({
          type: Type.unknown,
          content: '',
        });
      });

      it('parses a line as Unknown if it does not have a recognised prefix', () => {
        const parser = new UnifiedDiffParser();
        // It's quite common for a single diff in a Unified Diff file to be preceded by a line
        //  showing the diff command used to generate it.
        const actual = parser.parseLine('diff -Nr -u0 a/file.txt b/file.txt');
        expect(actual).toMatchObject({
          type: Type.unknown,
          content: 'diff -Nr -u0 a/file.txt b/file.txt',
        });
      });
    });
  });

  // -----------------------------------------------------------------------------------------------
  // clear()

  describe('clear()', () => {
    it('does nothing if called on an empty parser', () => {
      const parser = new UnifiedDiffParser();
      expect(() => {
        parser.clear();
      }).not.toThrow();
    });

    it('resets parsing state so that a new file can be started', () => {
      const parser = new UnifiedDiffParser();
      parser.parseLine('--- a/file1.txt');
      parser.parseLine('+++ b/file1.txt');
      parser.parseLine('@@ -1,0 +1,2 @@');
      parser.parseLine('+Some new content');

      parser.clear();
      expect(parser.diffLineNumber).toEqual(0);
      expect(parser.hasHeader).toBeFalse();
      expect(parser.hasHunkDescriptor).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------
  // tryToParseAsHeader()

  describe('tryToParseAsHeader()', () => {
    const testFunction = UnifiedDiffParser.tryToParseAsHeader;
    const testCases: TestCases<typeof testFunction> = [
      {
        name: 'returns null if the line does not start with a header prefix',
        input: ['deliberately not a header line'],
        expected: null,
      },
      {
        name: 'parses an old file header line containing path and timestamp',
        input: ['---test/file.txt\t2025-01-02 12:34:56.789 +0000'],
        expected: {
          type: Type.oldFileHeader,
          path: 'test/file.txt',
          timestamp: '2025-01-02 12:34:56.789 +0000',
        },
      },
      {
        name: 'parses a new file header line containing path and timestamp',
        input: ['+++test/file.txt\t2025-01-02 12:34:56.789 +0000'],
        expected: {
          type: Type.newFileHeader,
          path: 'test/file.txt',
          timestamp: '2025-01-02 12:34:56.789 +0000',
        },
      },
      {
        name: 'trims leading and trailing space around the path and timestamp',
        input: ['---   test/file.txt   \t    2025-01-02 12:34:56.789 +0000     '],
        expected: {
          type: Type.oldFileHeader,
          path: 'test/file.txt',
          timestamp: '2025-01-02 12:34:56.789 +0000',
        },
      },
      {
        name: 'parses a header line containing a path and tab separator but no timestamp',
        input: ['--- test/file.txt\t'],
        expected: {
          type: Type.oldFileHeader,
          path: 'test/file.txt',
          timestamp: '',
        },
      },
      {
        name: 'parses a header line containing only a path but no tab separator or timestamp',
        input: ['--- test/file.txt'],
        expected: {
          type: Type.oldFileHeader,
          path: 'test/file.txt',
          timestamp: '',
        },
      },
      {
        name: 'removes enclosing single quotes from the path',
        input: ["--- 'test/file.txt'\t2025-01-02 12:34:56.789 +0000"],
        expected: {
          type: Type.oldFileHeader,
          path: 'test/file.txt',
          timestamp: '2025-01-02 12:34:56.789 +0000',
        },
      },
      {
        name: 'removes enclosing double quotes from the path',
        input: ['--- "test/file.txt"\t2025-01-02 12:34:56.789 +0000'],
        expected: {
          type: Type.oldFileHeader,
          path: 'test/file.txt',
          timestamp: '2025-01-02 12:34:56.789 +0000',
        },
      },
      {
        name: 'only trims spaces outside the quotation marks if the path is quoted',
        input: ["--- ' hello world/test/file.txt '\t2025-01-02 12:34:56.789 +0000"],
        expected: {
          type: Type.oldFileHeader,
          path: ' hello world/test/file.txt ',
          timestamp: '2025-01-02 12:34:56.789 +0000',
        },
      },
      {
        name: 'unescapes special characters and spaces in the path',
        input: ["--- 'hello\\ world/\\'test\\'/file\\%.txt '\t2025-01-02 12:34:56.789 +0000"],
        expected: {
          type: Type.oldFileHeader,
          path: "hello world/'test'/file%.txt ",
          timestamp: '2025-01-02 12:34:56.789 +0000',
        },
      },
      {
        name: 'does not validate or modify the timestamp',
        input: ['---test/file.txt\tdeliberately invalid timestamp'],
        expected: {
          type: Type.oldFileHeader,
          path: 'test/file.txt',
          timestamp: 'deliberately invalid timestamp',
        },
      },
      {
        name: 'sets the diff line number to 0',
        input: ['---test/file.txt\t2025-01-02 12:34:56.789 +0000'],
        expected: {
          diffLineNumber: 0,
        },
      },
    ];

    // Run each test case in the array above.
    test.each(testCases)('$name', (testCase) => {
      if (testCase.throws === true) {
        expect(() => testFunction(...testCase.input)).toThrow();
        return;
      }

      const actual = testFunction(...testCase.input);
      if (testCase.expected == null || typeof testCase.expected !== 'object') {
        expect(actual).toEqual(testCase.expected);
      } else {
        expect(actual).toMatchObject(testCase.expected);
      }
    });
  });

  // -----------------------------------------------------------------------------------------------
  // tryToParseAsHunkDescriptor()

  describe('tryToParseAsHunkDescriptor()', () => {
    const testFunction = UnifiedDiffParser.tryToParseAsHunkDescriptor;
    const testCases: TestCases<typeof testFunction> = [
      {
        name: 'throws an error if the line starts with hunk descriptor prefix but does match the expected structure',
        input: ['@@ deliberately invalid hunk descriptor @@'],
        throws: true,
      },
      {
        name: 'throws an error if both ranges refer to the old file',
        input: ['@@ -1,2 -3,4 @@'],
        throws: true,
      },
      {
        name: 'throws an error if both ranges refer to the new file',
        input: ['@@ +1,2 +3,4 @@'],
        throws: true,
      },
      {
        name: 'throws an error if the old range starts on line zero and the size is non-zero',
        input: ['@@ -0,1 +1,1 @@'],
        throws: true,
      },
      {
        name: 'throws an error if the new range starts on line zero and the size is non-zero',
        input: ['@@ -1,1 +0,1 @@'],
        throws: true,
      },
      {
        name: 'returns null if line does not start with a hunk line prefix',
        input: ['deliberately not a hunk descriptor'],
        expected: null,
      },
      {
        name: 'parses a typical hunk descriptor',
        input: ['@@ -111,222 +333,444 @@'],
        expected: {
          oldRange: { start: 111, size: 222 },
          newRange: { start: 333, size: 444 },
        },
      },
      {
        name: 'ignores extra spaces around and between the ranges',
        input: ['@@     -111,222     +333,444    @@'],
        expected: {
          oldRange: { start: 111, size: 222 },
          newRange: { start: 333, size: 444 },
        },
      },
      {
        name: 'ignores trailing information at the end of the line',
        input: ['@@ -111,222 +333,444 @@   this bit should be ignored   '],
        expected: {
          oldRange: { start: 111, size: 222 },
          newRange: { start: 333, size: 444 },
        },
      },
      {
        name: 'defaults the old range size to 1 if not specified',
        input: ['@@ -111 +333,444 @@'],
        expected: {
          oldRange: { start: 111, size: 1 },
          newRange: { start: 333, size: 444 },
        },
      },
      {
        name: 'defaults the new range size to 1 if not specified',
        input: ['@@ -111,222 +333 @@'],
        expected: {
          oldRange: { start: 111, size: 222 },
          newRange: { start: 333, size: 1 },
        },
      },
      {
        name: 'allows the new range to be specified first',
        input: ['@@ +111,222 -333,444 @@'],
        expected: {
          oldRange: { start: 333, size: 444 },
          newRange: { start: 111, size: 222 },
        },
      },
      {
        name: 'does not correct the start number of the old range if the size is 0',
        input: ['@@ -111,0 +333,444 @@'],
        expected: { oldRange: { start: 111, size: 0 } },
      },
      {
        name: 'does not correct the start number of the new range if the size is 0',
        input: ['@@ -111,222 +333,0 @@'],
        expected: { newRange: { start: 333, size: 0 } },
      },
      {
        name: 'allows the old range to start on line zero if the size is zero',
        input: ['@@ -0,0 +1,1 @@'],
        expected: { oldRange: { start: 0, size: 0 } },
      },
      {
        name: 'allows the new range to start on line zero if the size is zero',
        input: ['@@ -1,1 +0,0 @@'],
        expected: { newRange: { start: 0, size: 0 } },
      },
      {
        name: 'sets the diff line number to 0',
        input: ['@@ -111,222 +333,444 @@'],
        expected: {
          diffLineNumber: 0,
        },
      },
    ];

    // Run each test case in the array above.
    test.each(testCases)('$name', (testCase) => {
      if (testCase.throws === true) {
        expect(() => testFunction(...testCase.input)).toThrow();
        return;
      }

      const actual = testFunction(...testCase.input);
      if (testCase.expected == null || typeof testCase.expected !== 'object') {
        expect(actual).toEqual(testCase.expected);
      } else {
        expect(actual).toMatchObject(testCase.expected);
      }
    });
  });

  // -----------------------------------------------------------------------------------------------
  // tryToParseAsHunkLine()

  describe('tryToParseAsHunkLine()', () => {
    const testFunction = UnifiedDiffParser.tryToParseAsHunkLine;
    const testCases: TestCases<typeof testFunction> = [
      {
        name: 'returns null if line does not start with a hunk line prefix',
        input: ['deliberately not a hunk line'],
        expected: null,
      },
      {
        name: 'parses an addition line and removes the prefix',
        input: ['+blah blah blah'],
        expected: { type: Type.addition, content: 'blah blah blah' },
      },
      {
        name: 'parses line as an addition if it looks like a new file header but a hunk line is expected',
        input: ['+++ a/file.txt\t2025-01-02 12:34:56.789 +0000'],
        expected: {
          type: Type.addition,
          content: '++ a/file.txt\t2025-01-02 12:34:56.789 +0000', // <-- addition prefix removed
        },
      },
      {
        name: 'parses a deletion line and removes the prefix',
        input: ['-blah blah blah'],
        expected: { type: Type.deletion, content: 'blah blah blah' },
      },
      {
        name: 'parses line as a deletion if it looks like an old file header but a hunk line is expected',
        input: ['--- a/file.txt\t2025-01-02 12:34:56.789 +0000'],
        expected: {
          type: Type.deletion,
          content: '-- a/file.txt\t2025-01-02 12:34:56.789 +0000', // <-- deletion prefix removed
        },
      },
      {
        name: 'parses a context line and removes the prefix',
        input: [' blah blah blah'],
        expected: { type: Type.context, content: 'blah blah blah' },
      },
      {
        name: 'does not remove other occurrences of the prefix character',
        input: ['++blah blah blah'],
        expected: { type: Type.addition, content: '+blah blah blah' },
      },
      {
        name: 'does not trim leading or trailing spaces (except for the context prefix)',
        input: ['    blah blah blah   '],
        expected: { type: Type.context, content: '   blah blah blah   ' },
      },
      {
        name: 'sets the diff line number to 0',
        input: ['+blah blah blah'],
        expected: { diffLineNumber: 0 },
      },
      {
        name: 'sets the old file line number to 0',
        input: ['+blah blah blah'],
        expected: { oldFileLineNumber: 0 },
      },
      {
        name: 'sets the new file line number to 0',
        input: ['+blah blah blah'],
        expected: { newFileLineNumber: 0 },
      },
    ];

    // Run each test case in the array above.
    test.each(testCases)('$name', (testCase) => {
      if (testCase.throws === true) {
        expect(() => testFunction(...testCase.input)).toThrow();
        return;
      }

      const actual = testFunction(...testCase.input);
      if (testCase.expected == null || typeof testCase.expected !== 'object') {
        expect(actual).toEqual(testCase.expected);
      } else {
        expect(actual).toMatchObject(testCase.expected);
      }
    });
  });

  // -----------------------------------------------------------------------------------------------
  // parseAsUnknown()

  describe('parseAsUnknown()', () => {
    it('returns an object which wraps the unmodified input in a parsed line structure', () => {
      const actual = UnifiedDiffParser.parseAsUnknown('testing 123');
      expect(actual.type).toEqual(Type.unknown);
      expect(actual.content).toEqual('testing 123');
      expect(actual.diffLineNumber).toEqual(0);
    });
  });

  // -----------------------------------------------------------------------------------------------
});

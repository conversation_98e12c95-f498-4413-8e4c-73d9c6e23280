import UnifiedDiffMapBuilder from './UnifiedDiffMapBuilder';
import UnifiedDiffParser from './UnifiedDiffParser';
import DiffMap from './DiffMap';

describe('UnifiedDiffMapBuilder', () => {
  // -----------------------------------------------------------------------------------------------

  describe('buildFromFile()', () => {
    it('returns a generator which yields a DiffMap for each diff in the file', () => {
      const input = `\
--- a/file1.txt\t2025-01-02 03:04:05.678 +0000
+++ b/file1.txt\t2025-02-03 04:05:06.789 +0000
@@ -5,2 +5,1 @@
-hunk line 1
-hunk line 2
+hunk line 3
@@ -12,0 +12,2 @@
+hunk line 4
+hunk line 5
--- a/file2.txt\t2025-01-04 03:04:05.678 +0000
+++ b/file2.txt\t2025-02-05 04:05:06.789 +0000
@@ -15,1 +15,3 @@
-hunk line 6
+hunk line 7
+hunk line 8
+hunk line 9
--- a/file3.txt\t2025-01-04 03:04:05.678 +0000
+++ b/file3.txt\t2025-02-05 04:05:06.789 +0000
@@ -20,0 +21,1 @@
+hunk line 10`;

      const diffMaps = [...UnifiedDiffMapBuilder.buildFromFile(input)];
      expect(diffMaps).toHaveLength(3);

      expect(diffMaps[0].getNumMoveBlocks()).toEqual(2);
      expect(diffMaps[0].getNumWriteBlocks()).toEqual(2);

      expect(diffMaps[1].getNumMoveBlocks()).toEqual(1);
      expect(diffMaps[1].getNumWriteBlocks()).toEqual(1);

      expect(diffMaps[2].getNumMoveBlocks()).toEqual(1);
      expect(diffMaps[2].getNumWriteBlocks()).toEqual(1);
    });

    it('yields no values if the Unified Diff is empty', () => {
      expect([...UnifiedDiffMapBuilder.buildFromFile('')]).toBeEmpty();
    });

    it('handles malformed diff files gracefully', () => {
      const malformedInput = `\
--- a/file.txt
+++ b/file.txt
invalid line without proper format`;

      // Should handle malformed input by producing empty results or throwing
      const diffMaps = [...UnifiedDiffMapBuilder.buildFromFile(malformedInput)];
      expect(diffMaps.length).toBeGreaterThanOrEqual(0);
    });

    it('handles empty file paths correctly', () => {
      const input = `\
---
+++
@@ -1,1 +1,1 @@
-old line
+new line`;

      const diffMaps = [...UnifiedDiffMapBuilder.buildFromFile(input)];
      expect(diffMaps).toHaveLength(1);
      expect(diffMaps[0].oldFilePath).toBe('');
      expect(diffMaps[0].newFilePath).toBe('');
    });

    it('handles large diff files efficiently', () => {
      // Create a large diff with many changes
      let largeDiff = '--- a/large.txt\t2025-01-02 03:04:05.678 +0000\n';
      largeDiff += '+++ b/large.txt\t2025-02-03 04:05:06.789 +0000\n';

      for (let i = 1; i <= 1000; i++) {
        largeDiff += `@@ -${i},1 +${i},1 @@\n`;
        largeDiff += `-old line ${i}\n`;
        largeDiff += `+new line ${i}\n`;
      }

      const start = performance.now();
      const diffMaps = [...UnifiedDiffMapBuilder.buildFromFile(largeDiff)];
      const duration = performance.now() - start;

      expect(diffMaps).toHaveLength(1);
      expect(duration).toBeLessThan(1000); // Should complete in under 1 second
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('buildFromLines()', () => {
    it('can operate on a generator or an array of parsed Unified Diff Lines', () => {
      const input = `\
--- a/file.txt\t2025-01-02 03:04:05.678 +0000
+++ b/file.txt\t2025-02-03 04:05:06.789 +0000
@@ -5,1 +5,1 @@
-hunk line 1
+hunk line 2`;

      const lineArray = UnifiedDiffParser.parseFile(input);
      const lineGenerator = UnifiedDiffParser.parseFileLineByLine(input);

      const diffMapsFromArray = [...UnifiedDiffMapBuilder.buildFromLines(lineArray)];
      const diffMapsFromGenerator = [...UnifiedDiffMapBuilder.buildFromLines(lineGenerator)];

      // The same map should be built either way.
      expect(diffMapsFromArray).toEqual(diffMapsFromGenerator);

      // Sanity check that the produced map looks valid.
      expect(diffMapsFromArray).toHaveLength(1);
      expect(diffMapsFromArray[0].oldFilePath).toEqual('a/file.txt');
      expect(diffMapsFromArray[0].newFilePath).toEqual('b/file.txt');
      expect(diffMapsFromArray[0].getNumMoveBlocks()).toEqual(0);
      expect(diffMapsFromArray[0].getNumWriteBlocks()).toEqual(1);
    });

    it('yields one DiffMap for each diff in the Unified Diff file', () => {
      const input = `\
--- a/file1.txt\t2025-01-02 03:04:05.678 +0000
+++ b/file1.txt\t2025-02-03 04:05:06.789 +0000
@@ -5,2 +5,1 @@
-hunk line 1
-hunk line 2
+hunk line 3
@@ -12,0 +12,2 @@
+hunk line 4
+hunk line 5
--- a/file2.txt\t2025-01-04 03:04:05.678 +0000
+++ b/file2.txt\t2025-02-05 04:05:06.789 +0000
@@ -15,1 +15,3 @@
-hunk line 6
+hunk line 7
+hunk line 8
+hunk line 9
--- a/file3.txt\t2025-01-04 03:04:05.678 +0000
+++ b/file3.txt\t2025-02-05 04:05:06.789 +0000
@@ -20,0 +21,1 @@
+hunk line 10`;

      const diffMaps = [
        ...UnifiedDiffMapBuilder.buildFromLines(UnifiedDiffParser.parseFile(input)),
      ];
      expect(diffMaps).toHaveLength(3);

      expect(diffMaps[0].getNumMoveBlocks()).toEqual(2);
      expect(diffMaps[0].getNumWriteBlocks()).toEqual(2);

      expect(diffMaps[1].getNumMoveBlocks()).toEqual(1);
      expect(diffMaps[1].getNumWriteBlocks()).toEqual(1);

      expect(diffMaps[2].getNumMoveBlocks()).toEqual(1);
      expect(diffMaps[2].getNumWriteBlocks()).toEqual(1);
    });

    it('handles empty line arrays', () => {
      const diffMaps = [...UnifiedDiffMapBuilder.buildFromLines([])];
      expect(diffMaps).toHaveLength(0);
    });

    it('handles error recovery during line processing', () => {
      const input = `\
--- a/file.txt\t2025-01-02 03:04:05.678 +0000
+++ b/file.txt\t2025-02-03 04:05:06.789 +0000
@@ -1,1 +1,1 @@
+valid addition`;

      const lines = UnifiedDiffParser.parseFile(input);

      // Mock console.error to capture error messages
      const originalError = console.error;
      const errorMessages: any[] = [];
      console.error = (...args: any[]) => errorMessages.push(args);

      try {
        const diffMaps = [...UnifiedDiffMapBuilder.buildFromLines(lines)];
        expect(diffMaps).toHaveLength(1);
      } finally {
        console.error = originalError;
      }
    });

    it('properly cleans up on errors', () => {
      // Create a scenario that might cause errors
      const corruptedLines = [
        { type: 'oldFileHeader', path: 'test.txt' },
        { type: 'addition', content: 'test' }, // Missing required properties
      ] as any[];

      // Should handle errors gracefully without leaving partial state
      expect(() => {
        void [...UnifiedDiffMapBuilder.buildFromLines(corruptedLines)];
      }).not.toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('handleLine()', () => {
    it('returns null if the specified line does nothing', () => {
      const parser = new UnifiedDiffParser();
      // This is an info line which does nothing at all.
      const parsedLine = parser.parseLine('Diff -u0 a/file.txt b/file.txt');
      const builder = new UnifiedDiffMapBuilder();
      expect(builder.handleLine(parsedLine)).toBeNull();
    });

    it('returns null if the specified line starts a new diff but no previous diff was in progress', () => {
      const parser = new UnifiedDiffParser();
      const parsedLine = parser.parseLine('--- a/file.txt\t2025-01-02 03:04:05.678 +0000');
      const builder = new UnifiedDiffMapBuilder();
      expect(builder.handleLine(parsedLine)).toBeNull();
    });

    it('returns a finalised diff map if the specified line starts a new diff and a previous diff was in progress', () => {
      const builder = new UnifiedDiffMapBuilder();
      const parser = new UnifiedDiffParser();
      // Process the first diff.
      builder.handleLine(parser.parseLine('--- a/file1.txt\t2025-01-02 03:04:05.678 +0000'));
      builder.handleLine(parser.parseLine('+++ b/file1.txt\t2025-02-03 04:05:06.789 +0000'));
      builder.handleLine(parser.parseLine('@@ -0,0 +1,1 @@'));
      builder.handleLine(parser.parseLine('+added line'));

      // An old file header should always start a new diff.
      // Note that this is returning the previous we've just finished.
      const diffMap1 = builder.handleLine(
        parser.parseLine('--- a/file2.txt\t2025-01-04 03:04:05.678 +0000'),
      );
      expect(diffMap1).not.toBeNull();
      expect(diffMap1?.isFinalised).toBeTrue();

      // A new file header immediately after an old file header should not start a new diff.
      const notADiffMap = builder.handleLine(
        parser.parseLine('+++ b/file2.txt\t2025-02-05 04:05:06.789 +0000'),
      );
      expect(notADiffMap).toBeNull();

      // Add a change to the second diff to ensure it's valid.
      builder.handleLine(parser.parseLine('@@ -0,0 +1,1 @@'));
      builder.handleLine(parser.parseLine('+added line'));

      // A new file header on its (without an old file header) should start a new diff.
      const diffMap2 = builder.handleLine(
        parser.parseLine('--- a/file3.txt\t2025-01-06 03:04:05.678 +0000'),
      );
      expect(diffMap2).not.toBeNull();
      expect(diffMap2?.isFinalised).toBeTrue();
    });

    it('puts additions into the diff map', () => {
      const input = `\
--- a/file1.txt\t2025-01-02 03:04:05.678 +0000
+++ b/file1.txt\t2025-02-03 04:05:06.789 +0000
@@ -0,0 +1,1 @@
+added line`;

      const lines = UnifiedDiffParser.parseFile(input);
      const builder = new UnifiedDiffMapBuilder();
      lines.forEach((line) => {
        builder.handleLine(line);
      });
      const diffMap = builder.finalise();

      // An addition causes everything below it to move down.
      expect(diffMap?.getNumMoveBlocks()).toEqual(1);
      // An addition causes a new value to be written.
      expect(diffMap?.getNumWriteBlocks()).toEqual(1);
    });

    it('puts deletions into the diff map', () => {
      const input = `\
--- a/file1.txt\t2025-01-02 03:04:05.678 +0000
+++ b/file1.txt\t2025-02-03 04:05:06.789 +0000
@@ -1,1 +0,0 @@
-deleted line`;

      const lines = UnifiedDiffParser.parseFile(input);
      const builder = new UnifiedDiffMapBuilder();
      lines.forEach((line) => {
        builder.handleLine(line);
      });
      const diffMap = builder.finalise();

      // A deletion causes everything below it to move up.
      expect(diffMap?.getNumMoveBlocks()).toEqual(1);
      // A deletion does not cause any values to be written.
      expect(diffMap?.getNumWriteBlocks()).toEqual(0);
    });

    it('handles context lines and other ignored line types', () => {
      const builder = new UnifiedDiffMapBuilder();
      const parser = new UnifiedDiffParser();

      // Start a diff
      builder.handleLine(parser.parseLine('--- a/file.txt\t2025-01-02 03:04:05.678 +0000'));
      builder.handleLine(parser.parseLine('+++ b/file.txt\t2025-02-03 04:05:06.789 +0000'));

      // Add hunk descriptor first, then context lines
      expect(builder.handleLine(parser.parseLine('@@ -1,3 +1,3 @@'))).toBeNull();
      expect(builder.handleLine(parser.parseLine(' context line'))).toBeNull();

      const diffMap = builder.finalise();
      expect(diffMap).not.toBeNull();
    });

    it('handles additions without active diff map', () => {
      const builder = new UnifiedDiffMapBuilder();

      // Create a malformed line object that bypasses parser validation
      const malformedAddition = { type: 'addition', content: 'test' } as any;

      // Should handle gracefully without throwing
      expect(() => builder.handleLine(malformedAddition)).not.toThrow();
    });

    it('handles deletions without active diff map', () => {
      const builder = new UnifiedDiffMapBuilder();

      // Create a malformed line object that bypasses parser validation
      const malformedDeletion = { type: 'deletion' } as any;

      // Should handle gracefully without throwing
      expect(() => builder.handleLine(malformedDeletion)).not.toThrow();
    });

    it('handles new file header starting new diff correctly', () => {
      const builder = new UnifiedDiffMapBuilder();
      const parser = new UnifiedDiffParser();

      // Start with a new file header (unusual but valid)
      expect(
        builder.handleLine(parser.parseLine('+++ b/file1.txt\t2025-02-03 04:05:06.789 +0000')),
      ).toBeNull();

      // Add some content
      builder.handleLine(parser.parseLine('@@ -0,0 +1,1 @@'));
      builder.handleLine(parser.parseLine('+added line'));

      // Another new file header should start a new diff
      const finishedDiff = builder.handleLine(
        parser.parseLine('+++ b/file2.txt\t2025-02-03 04:05:06.789 +0000'),
      );
      expect(finishedDiff).not.toBeNull();
      expect(finishedDiff?.isFinalised).toBe(true);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('finalise()', () => {
    it('returns null if no diff map is being built', () => {
      const builder = new UnifiedDiffMapBuilder();
      expect(builder.finalise()).toBeNull();
    });

    it('finalises and returns the current diff map if one is being built', () => {
      const input = `\
--- a/file1.txt\t2025-01-02 03:04:05.678 +0000
+++ b/file1.txt\t2025-02-03 04:05:06.789 +0000
@@ -0,0 +1,1 @@
+added line`;

      const builder = new UnifiedDiffMapBuilder();
      const lines = UnifiedDiffParser.parseFile(input);
      lines.forEach((line) => {
        builder.handleLine(line);
      });

      const output = builder.finalise();
      expect(output).not.toBeNull();
      expect(output?.isFinalised).toBeTrue();

      // Calling it again should return null because we can't keep building a finalised instance.
      expect(builder.finalise()).toBeNull();
    });

    it('handles finalization errors gracefully', () => {
      const builder = new UnifiedDiffMapBuilder();
      const parser = new UnifiedDiffParser();

      // Start a diff but don't complete it properly
      builder.handleLine(parser.parseLine('--- a/file.txt\t2025-01-02 03:04:05.678 +0000'));

      // Finalization should still work even with incomplete diff
      expect(() => builder.finalise()).not.toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('State Management', () => {
    it('getState() returns correct information about builder state', () => {
      const builder = new UnifiedDiffMapBuilder();

      // Initially no diff is being built
      let state = builder.getState();
      expect(state.isBuilding).toBe(false);
      expect(state.currentDiffPath).toBeUndefined();
      expect(state.hasChanges).toBeUndefined();

      // Start building a diff
      const parser = new UnifiedDiffParser();
      builder.handleLine(parser.parseLine('--- a/file.txt\t2025-01-02 03:04:05.678 +0000'));
      builder.handleLine(parser.parseLine('+++ b/file.txt\t2025-02-03 04:05:06.789 +0000'));

      state = builder.getState();
      expect(state.isBuilding).toBe(true);
      expect(state.currentDiffPath).toBe('a/file.txt');
      expect(state.hasChanges).toBe(false);

      // Add a change
      builder.handleLine(parser.parseLine('@@ -0,0 +1,1 @@'));
      builder.handleLine(parser.parseLine('+added line'));

      state = builder.getState();
      expect(state.isBuilding).toBe(true);
      expect(state.hasChanges).toBe(true);

      // Finalize
      builder.finalise();
      state = builder.getState();
      expect(state.isBuilding).toBe(false);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('Error Handling and Edge Cases', () => {
    it('handles malformed line objects gracefully', () => {
      const builder = new UnifiedDiffMapBuilder();

      // Create malformed line objects
      const malformedLines = [
        { type: 'oldFileHeader' }, // Missing path
        { type: 'addition' }, // Missing required properties
        { type: 'deletion' }, // Missing required properties
        { type: 'unknown' }, // Unknown type
      ] as any[];

      // Should not throw errors
      malformedLines.forEach((line) => {
        expect(() => builder.handleLine(line)).not.toThrow();
      });
    });

    it('handles complex diff scenarios correctly', () => {
      const input = `\
--- a/complex.txt\t2025-01-02 03:04:05.678 +0000
+++ b/complex.txt\t2025-02-03 04:05:06.789 +0000
@@ -1,5 +1,7 @@
 line 1
-line 2
+modified line 2
 line 3
+new line 3.5
 line 4
-line 5
+modified line 5
+new line 6`;

      const diffMaps = [...UnifiedDiffMapBuilder.buildFromFile(input)];
      expect(diffMaps).toHaveLength(1);

      const diffMap = diffMaps[0];
      expect(diffMap.oldFilePath).toBe('a/complex.txt');
      expect(diffMap.newFilePath).toBe('b/complex.txt');
      expect(diffMap.hasChanges()).toBe(true);
      expect(diffMap.netMovement).toBe(2); // Net addition of 2 lines
    });

    it('handles multiple consecutive file headers', () => {
      const input = `\
--- a/file1.txt\t2025-01-02 03:04:05.678 +0000
+++ b/file1.txt\t2025-02-03 04:05:06.789 +0000
--- a/file2.txt\t2025-01-02 03:04:05.678 +0000
+++ b/file2.txt\t2025-02-03 04:05:06.789 +0000
@@ -1,1 +1,1 @@
-old
+new`;

      const diffMaps = [...UnifiedDiffMapBuilder.buildFromFile(input)];
      expect(diffMaps).toHaveLength(2);

      // First diff should be empty
      expect(diffMaps[0].hasChanges()).toBe(false);

      // Second diff should have changes
      expect(diffMaps[1].hasChanges()).toBe(true);
    });

    it('handles performance with many small diffs', () => {
      let manyDiffs = '';
      for (let i = 0; i < 100; i++) {
        manyDiffs += `--- a/file${i}.txt\t2025-01-02 03:04:05.678 +0000\n`;
        manyDiffs += `+++ b/file${i}.txt\t2025-02-03 04:05:06.789 +0000\n`;
        manyDiffs += `@@ -1,1 +1,1 @@\n`;
        manyDiffs += `-old line ${i}\n`;
        manyDiffs += `+new line ${i}\n`;
      }

      const start = performance.now();
      const diffMaps = [...UnifiedDiffMapBuilder.buildFromFile(manyDiffs)];
      const duration = performance.now() - start;

      expect(diffMaps).toHaveLength(100);
      expect(duration).toBeLessThan(500); // Should complete quickly

      // Verify all diffs are properly built
      diffMaps.forEach((diffMap, index) => {
        expect(diffMap.oldFilePath).toBe(`a/file${index}.txt`);
        expect(diffMap.newFilePath).toBe(`b/file${index}.txt`);
        expect(diffMap.hasChanges()).toBe(true);
      });
    });
  });

  // -----------------------------------------------------------------------------------------------
  // Additional Error Condition Tests for 100% Coverage

  describe('Uncovered Error Conditions', () => {
    it('should handle addition line without required properties', () => {
      const builder = new UnifiedDiffMapBuilder();
      const builderAny = builder as any;

      // Set up a diff map first
      builderAny._diffMap = new DiffMap();

      // Test addition line missing required properties
      const incompleteAdditionLine = {
        type: 'addition',
        content: 'test',
        // Missing oldFileLineNumber and newFileLineNumber
      } as any;

      expect(() => {
        builderAny._handleAddition(incompleteAdditionLine);
      }).not.toThrow(); // Should handle gracefully
    });

    it('should handle deletion line without required properties', () => {
      const builder = new UnifiedDiffMapBuilder();
      const builderAny = builder as any;

      // Set up a diff map first
      builderAny._diffMap = new DiffMap();

      // Test deletion line missing required properties
      const incompleteDeletionLine = {
        type: 'deletion',
        // Missing oldFileLineNumber
      } as any;

      expect(() => {
        builderAny._handleDeletion(incompleteDeletionLine);
      }).not.toThrow(); // Should handle gracefully
    });

    it('should handle finalization error with proper cleanup', () => {
      const builder = new UnifiedDiffMapBuilder();
      const builderAny = builder as any;

      // Create a mock diff map that will throw on finalize
      const mockDiffMap = {
        finalise: jest.fn(() => {
          throw new Error('Finalization failed');
        }),
      };

      builderAny._diffMap = mockDiffMap;

      expect(() => {
        builder.finalise();
      }).toThrow('Failed to finalize diff map: Finalization failed');

      // Verify cleanup occurred
      expect(builderAny._diffMap).toBeNull();
    });

    it('should handle addition without active diff map', () => {
      const builder = new UnifiedDiffMapBuilder();
      const builderAny = builder as any;

      // Ensure no active diff map
      builderAny._diffMap = null;

      const additionLine = {
        type: 'addition',
        oldFileLineNumber: 1,
        newFileLineNumber: 1,
        content: 'test',
      } as any;

      expect(() => {
        builderAny._handleAddition(additionLine);
      }).toThrow('Cannot process addition line without an active diff map');
    });

    it('should handle deletion without active diff map', () => {
      const builder = new UnifiedDiffMapBuilder();
      const builderAny = builder as any;

      // Ensure no active diff map
      builderAny._diffMap = null;

      const deletionLine = {
        type: 'deletion',
        oldFileLineNumber: 1,
      } as any;

      expect(() => {
        builderAny._handleDeletion(deletionLine);
      }).toThrow('Cannot process deletion line without an active diff map');
    });

    it('should handle error in buildFromLines with cleanup', () => {
      // Create a line that will cause the error
      const errorLine = {
        type: 'addition',
        diffLineNumber: 2,
        oldFileLineNumber: 1,
        newFileLineNumber: 1,
        content: 'test',
      } as any;

      // Mock console.error to avoid noise in test output
      const originalError = console.error;
      console.error = jest.fn();

      try {
        const builder = new UnifiedDiffMapBuilder();
        const builderAny = builder as any;

        // Mock handleLine to throw an error
        builder.handleLine = jest.fn(() => {
          throw new Error('Test error in handleAddition');
        });

        expect(() => {
          builderAny._processLine(errorLine);
        }).toThrow('Failed to process diff line 2: Test error in handleAddition');
      } finally {
        console.error = originalError;
      }
    });

    it('should handle error context logging in _processLine', () => {
      const builder = new UnifiedDiffMapBuilder();
      const builderAny = builder as any;

      // Set up a diff map for context
      builderAny._diffMap = new DiffMap();
      builderAny._diffMap.oldFilePath = 'test-file.txt';

      // Mock console.error to capture the error logging
      const originalError = console.error;
      const errorMessages: any[] = [];
      console.error = (...args: any[]) => errorMessages.push(args);

      try {
        // Create a line that will cause an error
        const problematicLine = {
          type: 'addition',
          diffLineNumber: 42,
          content: 'test content',
        } as any;

        // Mock handleLine to throw an error
        builder.handleLine = jest.fn(() => {
          throw new Error('Test error');
        });

        expect(() => {
          builderAny._processLine(problematicLine);
        }).toThrow('Failed to process diff line 42: Test error');

        // Verify error context was logged
        expect(errorMessages.length).toBeGreaterThan(0);
        expect(errorMessages[0]).toContain('UnifiedDiffMapBuilder - Error processing line:');
      } finally {
        console.error = originalError;
      }
    });
  });
});

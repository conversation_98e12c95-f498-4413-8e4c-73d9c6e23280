/**
 * Describes a function which extracts or validates the functional value from an entry in a diff.
 * This is provided by the caller when a DiffMap is used to apply a patch.
 *
 * This is necessary for situations like the domainsurls.* files in the blocklist. The data in the
 *  original files is encoded as a JSON array, with one element per line. The diff to update it is
 *  text-based so each diff change also includes the surrounding JSON syntax, such as quotation
 *  marks around the string value and a comma between adjacent entries. To maximise performance, we
 *  apply the diff directly to the in-memory representation of the data, which is already parsed
 *  from JSON. For the diff to be applied correctly, we need to extract the contents of the string
 *  value from the JSON fragment provided in the diff. This function would perform that extraction.
 *
 * This function can also be used as a validator if necessary. If the input is invalid then it can
 *  throw an Error. It will be propagated out, causing patching to fail. If the value is valid, then
 *  the function must still return it, even if it doesn't need to make any changes.
 *
 * @param input An entry (typically a full line) as it would have appeared in the original file on
 *  which the diff was based.
 * @returns The functional value extracted from the input. If no changes are required then the input
 *  value must be returned.
 * @throws {Error} The input value is invalid or could not be processed for some reason.
 *
 * @note This function does not have to remove or parse any diff syntax. That will already have been
 *  removed.
 */
export type ValueProcessor = (input: string) => string;

/**
 * Describes a contiguous block of elements which will all be moved together as part of the patch.
 * A movement will cause existing values to take the place of old deleted values, or to move into an
 *  empty space created by expanding the original data structure.
 * Movements must be carried out before writes, or the wrong values may be overwritten.
 *
 * @note This is only intended to be used internally by DiffMap and DiffMapBuilder.
 */
export interface MoveBlock {
  /**
   * The 0-based index of the first entry in this block.
   *
   * @note File-based diffs typically used 1-based line numbering. They need to be converted to
   *  0-based indexes before this value is populated.
   */
  start: number;

  /**
   * The number of elements in this block.
   * If this is 0 then the block is "open", meaning it includes all subsequent elements until the
   *  end of the data. This should only be the case for the last block in the map. This is because
   *  we don't necessarily know how many elements there will be. All blocks before that should be
   *  "closed", i.e. have a size greater than 0.
   * A block may also be open temporarily while building the patch map if we haven't found its end
   *  point yet.
   */
  size: number;

  /**
   * The number of elements by which the block will move from its old position to its new position.
   * A positive value means the block will move towards the end ("down"). A negative value means the
   *  block will move towards the beginning ("up").
   * Hypothetically, a zero value means the block doesn't move at all. However, a MoveBlock instance
   *  with a zero movement value doesn't serve a useful purpose so it should usually be omitted.
   */
  movement: number;
}

/**
 * Describes a contiguous block of elements which will be written as part of the patch.
 * A write operation may replace old values which were deleted or moved, or add entirely new values.
 * Movements must be carried out before writes, otherwise the wrong values may get overwritten.
 *
 * @note This is only intended to be used internally by DiffMap and DiffMapBuilder.
 */
export interface WriteBlock {
  /**
   * The 0-based index of the first entry which will be overwritten by this block.
   * The number of elements in the block is determined by the length of the elements array.
   *
   * @note File-based diffs typically used 1-based line numbering. They need to be converted to
   *  0-based indexes before this value is populated.
   */
  start: number;

  /**
   * The data which will be written.
   * The first item in this array will be written at the index given by the start property. Each
   *  subsequent item in this array will be written to the subsequent elements.
   */
  elements: string[];
}

/**
 * Optimized mapping of diff changes that can be efficiently applied to in-memory arrays.
 * Groups operations into move and write blocks to minimize array manipulation overhead.
 */
export default class DiffMap {
  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Whether this DiffMap has been finalised and is ready for patching.
   */
  public get isFinalised(): boolean {
    return this._isFinalised;
  }

  /**
   * File path of the original file this diff was generated from.
   */
  public get oldFilePath(): string {
    return this._oldFilePath;
  }

  /**
   * Sets the original file path.
   */
  public set oldFilePath(path: string) {
    if (this._isFinalised) {
      throw new Error('Cannot change old file path after DiffMap has been finalised.');
    }
    this._oldFilePath = path;
  }

  /**
   * File path of the target file this diff will produce.
   */
  public get newFilePath(): string {
    return this._newFilePath;
  }

  /**
   * Sets the target file path.
   */
  public set newFilePath(path: string) {
    if (this._isFinalised) {
      throw new Error('Cannot change new file path after DiffMap has been finalised.');
    }
    this._newFilePath = path;
  }

  /**
   * Net change in array size (positive = growth, negative = shrinkage).
   */
  public get netMovement(): number {
    return this._netMovement;
  }

  /**
   * Returns true if this diff contains any changes to apply.
   */
  public hasChanges(): boolean {
    return this._moveBlocks.length > 0 || this._writeBlocks.length > 0;
  }

  /**
   * Number of move blocks (unchanged content that needs repositioning).
   */
  public getNumMoveBlocks(): number {
    return this._moveBlocks.length;
  }

  /**
   * Number of write blocks (new content to be inserted).
   */
  public getNumWriteBlocks(): number {
    return this._writeBlocks.length;
  }

  // -----------------------------------------------------------------------------------------------
  // Patching operations.

  /**
   * Modify the given array in-place.
   *
   * @param target The array to be modified.
   * @param valueProcessor An optional function to process, convert, or validate new values before
   *  writing them as part of a patch. If not specified, the original value will used unmodified.
   * @param indexOffset Specifies how indexes should be offset when applying the patch. This value
   *  will be added to the index inferred from the original diff. This is useful for situations
   *  where the diff was generated from a file which had one or more ignored lines before the first
   *  actual value. For example, if the original file was a JSON array, and the first line was an
   *  opening square bracket, then this should be -1 to compensate.
   */
  public patchArrayInPlace(
    target: string[],
    valueProcessor?: ValueProcessor,
    indexOffset: number = 0,
  ): void {
    // Early exit for empty diffs
    if (this._moveBlocks.length === 0 && this._writeBlocks.length === 0) {
      return;
    }

    const originalTargetLength = target.length;

    // Pre-expand the array if the diff will make it larger
    this._prepareArrayForExpansion(target);

    // Apply all move operations first to avoid overwriting data
    this._applyMoveBlocks(target, originalTargetLength, indexOffset);

    // Apply all write operations to insert new content
    this._applyWriteBlocks(target, valueProcessor, indexOffset);

    // Shrink the array if the diff made it smaller
    this._finalizeArraySize(target);
  }

  /**
   * Returns performance metrics for monitoring and optimization.
   */
  public getPerformanceMetrics(): {
    moveBlockCount: number;
    writeBlockCount: number;
    totalOperations: number;
    netMovement: number;
    estimatedComplexity: 'low' | 'medium' | 'high';
  } {
    const totalOperations = this._moveBlocks.length + this._writeBlocks.length;
    let estimatedComplexity: 'low' | 'medium' | 'high' = 'low';

    if (totalOperations > 1000 || Math.abs(this._netMovement) > 10000) {
      estimatedComplexity = 'high';
    } else if (totalOperations > 100 || Math.abs(this._netMovement) > 1000) {
      estimatedComplexity = 'medium';
    }

    return {
      moveBlockCount: this._moveBlocks.length,
      writeBlockCount: this._writeBlocks.length,
      totalOperations,
      netMovement: this._netMovement,
      estimatedComplexity,
    };
  }

  /**
   * Pre-expands the array if needed to avoid multiple smaller expansions.
   */
  private _prepareArrayForExpansion(target: string[]): void {
    if (this._netMovement > 0) {
      target.length += this._netMovement;
    }
  }

  /**
   * Applies move blocks to reposition existing elements.
   * Processes blocks in groups by direction to avoid overwriting data.
   */
  private _applyMoveBlocks(
    target: string[],
    originalTargetLength: number,
    indexOffset: number,
  ): void {
    // Early exit if no move blocks
    if (this._moveBlocks.length === 0) {
      return;
    }

    // We need to move elements around to account for additions and deletions. We need to group
    //  blocks of movement together by overall direction.
    // TODO: Move this into a static function which can be unit tested?
    let groupStartIndex = 0;
    while (groupStartIndex < this._moveBlocks.length) {
      const groupLength = DiffMap.findMoveBlockGroupLength(this._moveBlocks, groupStartIndex);
      const groupDirection = DiffMap.getMoveBlockDirection(this._moveBlocks[groupStartIndex]);

      if (groupDirection !== 0) {
        this._processMoveBlockGroup(
          target,
          originalTargetLength,
          indexOffset,
          groupStartIndex,
          groupLength,
          groupDirection,
        );
      }

      groupStartIndex += groupLength;
    }
  }

  /**
   * Processes a group of move blocks that move in the same direction.
   */
  private _processMoveBlockGroup(
    target: string[],
    originalTargetLength: number,
    indexOffset: number,
    groupStartIndex: number,
    groupLength: number,
    groupDirection: number,
  ): void {
    for (let i = 0; i < groupLength; ++i) {
      // Process blocks in reverse order for downward movement to avoid overwriting
      let blockIndex = groupStartIndex + i;
      if (groupDirection > 0) {
        blockIndex = groupStartIndex + groupLength - 1 - i;
      }

      const block = this._moveBlocks[blockIndex];
      const actualBlockStart = block.start + indexOffset;
      const actualBlockEnd = this._calculateBlockEnd(
        block,
        blockIndex,
        actualBlockStart,
        originalTargetLength,
      );

      target.copyWithin(actualBlockStart + block.movement, actualBlockStart, actualBlockEnd);
    }
  }

  /**
   * Calculates the end position of a move block, handling open blocks.
   */
  private _calculateBlockEnd(
    block: MoveBlock,
    blockIndex: number,
    actualBlockStart: number,
    originalTargetLength: number,
  ): number {
    if (block.size === 0) {
      // Open block - includes all elements to the end of the original array
      if (blockIndex < this._moveBlocks.length - 1) {
        throw new Error('Open move blocks are only allowed at the end.');
      }
      return originalTargetLength;
    }
    return actualBlockStart + block.size;
  }

  /**
   * Applies write blocks to insert new content into the array.
   */
  private _applyWriteBlocks(
    target: string[],
    valueProcessor?: ValueProcessor,
    indexOffset: number = 0,
  ): void {
    // Write the new values which need to be added as part of the diff. Any previous values at the
    //  same position will be replaced. Such values are either being deleted by the diff, or they
    //  will have been moved/copied to a different location by the move blocks above.
    // TODO: Move this into a static function which can be unit tested?
    this._writeBlocks.forEach((writeBlock) => {
      target.splice(
        writeBlock.start + indexOffset,
        writeBlock.elements.length,
        // If we have a value processor then pass each value through it before writing. Otherwise,
        //  use the original data as-is.
        ...(valueProcessor === undefined
          ? writeBlock.elements
          : writeBlock.elements.map(valueProcessor)),
      );
    });
  }

  /**
   * Shrinks the array if the diff removed more elements than it added.
   */
  private _finalizeArraySize(target: string[]): void {
    if (this._netMovement < 0) {
      target.length += this._netMovement;
    }
  }

  // -----------------------------------------------------------------------------------------------
  // Map update operations.

  /**
   * Records an addition operation in the diff map.
   */
  public handleAddition(oldIndex: number, newIndex: number, value: string): void {
    if (this._isFinalised) {
      throw new Error('Cannot handle an addition after the DiffMap is finalised.');
    }

    // Ensure any previous unchanged entries were covered by a move block.
    // This must be done before updating _nextMoveBlockStartsAt or _netMovement.
    this._addClosedMoveBlock(oldIndex);

    // An addition needs to interrupt a move block. The entry before the addition will stays with
    //  its move block (if there is one). The entry where the addition occurs will potentially move
    //  down as part of a new move block.
    this._nextMoveBlockStartsAt = oldIndex;
    this._netMovement++;
    this._addWrite(newIndex, value);
  }

  /**
   * Records a deletion operation in the diff map.
   */
  public handleDeletion(oldIndex: number): void {
    if (this._isFinalised) {
      throw new Error('Cannot handle a deletion after the DiffMap is finalised.');
    }

    // Ensure any previous unchanged entries were covered by a move block.
    // This must be done before updating _nextMoveBlockStartsAt or _netMovement.
    this._addClosedMoveBlock(oldIndex);

    // An entry which was deleted should not be moved, so the next move block shouldn't start until
    //  at least the entry after this one.
    this._nextMoveBlockStartsAt = oldIndex + 1;

    // A deletion causes all subsequent elements to move upwards to fill the gap.
    this._netMovement--;
  }

  /**
   * Finalises the DiffMap, making it ready for patching operations.
   */
  public finalise(): void {
    if (this._isFinalised) {
      throw new Error('Already finalised.');
    }
    // Ensure there's a final open move block if necessary to catch all remaining entries after the
    //  last change.
    this._openMoveBlock();
    this._isFinalised = true;
  }

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Ensures unchanged entries before the index are covered by a move block.
   */
  private _addClosedMoveBlock(oldIndex: number): void {
    if (this._nextMoveBlockStartsAt !== null && this._nextMoveBlockStartsAt < oldIndex) {
      // There is at least one unchanged entry immediately prior to the current entry.
      // Ensure it's included in a move block.
      this._openMoveBlock();
    }
    // The move block must be immediately closed, regardless of whether we created it here or it was
    //  already open.
    this._closeMoveBlock(oldIndex);
  }

  /**
   * Opens a new move block if needed and movement is non-zero.
   */
  private _openMoveBlock(): void {
    if (this._netMovement === 0) {
      // There's no point creating a move block if there's no movement in the data.
      return;
    }

    if (this._nextMoveBlockStartsAt === null) {
      // We don't know where the move block should start. This should only happen at the start of
      //  the data, before we've processed any changes to give us a frame of reference. We know the
      //  net movement is non-zero though, so a change must have been processed already.
      throw new Error('Cannot create a move block. The start point has not yet been determined.');
    }

    if (this._moveBlocks.length > 0) {
      // We already have a move block.
      const moveBlock = this._moveBlocks[this._moveBlocks.length - 1];
      if (this._nextMoveBlockStartsAt < moveBlock.start + moveBlock.size) {
        throw new Error('Move block opening is out of sequence.');
      }

      if (moveBlock.size === 0) {
        // The move block is already open.
        if (moveBlock.movement !== this._netMovement) {
          // If the movement value has changed then it suggests there's been an addition or a
          //  deletion. The move block should have been closed when that happened.
          throw new Error('Movement value should not change during a move block.');
        }

        // The latest move block is open so there's nothing to do.
        return;
      }
    }

    // Either we don't have a move block yet, or the last one has already been closed. Start a new
    //  one.
    this._moveBlocks.push({
      start: this._nextMoveBlockStartsAt,
      size: 0,
      movement: this._netMovement,
    });
  }

  // NOTE: oldIndex is the first entry which is NOT included in the move
  private _closeMoveBlock(oldIndex: number): void {
    if (this._moveBlocks.length === 0) {
      // We have no move blocks at all so there's nothing to close.
      return;
    }

    const moveBlock = this._moveBlocks[this._moveBlocks.length - 1];
    if (moveBlock.size > 0) {
      // The current move block is already closed so there's nothing to do.
      return;
    }

    // It's important not to check this if the move block is already closed. When there are multiple
    //  deletions and additions in the same block, the deletions will usually be listed first,
    //  followed by the additions. This can make it look like the index is going backwards. It
    //  doesn't matter in that situation because the move block should have been closed by the
    //  change in the block.
    if (oldIndex < moveBlock.start + moveBlock.size) {
      throw new Error('Move block closure is out of sequence.');
    }

    // Close the block at the specified position.
    moveBlock.size = oldIndex - moveBlock.start;
  }

  /**
   * Adds a write operation, either appending to the last block or creating a new one.
   */
  private _addWrite(newIndex: number, value: string): void {
    // Append to existing block if contiguous
    if (this._writeBlocks.length > 0) {
      const writeBlock = this._writeBlocks[this._writeBlocks.length - 1];
      const blockEndIndex = writeBlock.start + writeBlock.elements.length;
      if (newIndex < blockEndIndex) {
        throw new Error('Write operation is out of sequence.');
      }
      if (newIndex === blockEndIndex) {
        writeBlock.elements.push(value);
        return;
      }
    }

    // Start a new block.
    this._writeBlocks.push({
      start: newIndex,
      elements: [value],
    });
  }

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Returns the movement direction: 1 (down), -1 (up), or 0 (none).
   */
  public static getMoveBlockDirection(moveBlock: MoveBlock): number {
    if (moveBlock.movement > 0) {
      return 1;
    }
    if (moveBlock.movement < 0) {
      return -1;
    }
    return 0;
  }

  /**
   * Finds the length of a contiguous group of move blocks moving in the same direction.
   */
  public static findMoveBlockGroupLength(moveBlocks: MoveBlock[], startIndex: number): number {
    if (startIndex >= moveBlocks.length) {
      throw new RangeError('Invalid move block index.');
    }

    if (startIndex === moveBlocks.length - 1) {
      // Group size can only be 1 if this is the last block
      return 1;
    }

    const groupDirection = DiffMap.getMoveBlockDirection(moveBlocks[startIndex]);
    let length = 1;
    while (startIndex + length < moveBlocks.length) {
      if (groupDirection !== DiffMap.getMoveBlockDirection(moveBlocks[startIndex + length])) {
        return length;
      }
      length++;
    }
    return length;
  }

  // -----------------------------------------------------------------------------------------------
  // Data.

  /** Whether this DiffMap has been finalised. */
  private _isFinalised: boolean = false;

  /** File path of the original file. */
  private _oldFilePath: string = '';

  /** File path of the target file. */
  private _newFilePath: string = '';

  /** Net change in array size from all additions and deletions. */
  private _netMovement: number = 0;

  /** Starting index for the next move block (null before first change). */
  // NOTE: This will be null before we've processed any changes. At that point, we don't yet know
  //  where the start of the next move block will be. This is because we don't know what the index
  //  of the first actual value in the target data is. In simple cases, it will be 0. However, for
  //  text-based diffs on JSON array files (e.g. for domainsurls.* files in the blocklist), we may
  //  have to ignore the first line as it may just be an opening square bracket. This means the
  //  first actual element occurs at position 1. This will be compensated to 0 when we apply the
  //  diff.
  private _nextMoveBlockStartsAt: number | null = null;

  /** Move blocks describing how to reposition existing content. */
  private readonly _moveBlocks: MoveBlock[] = [];

  /** Write blocks describing new content to insert. */
  private readonly _writeBlocks: WriteBlock[] = [];
}

import DiffMap from './DiffMap';

/**
 * A blocklist component or sub-component whose data can be patched dynamically by a Unified Diff.
 * This lets us apply the diff to the data in memory, which is much faster than applying it in text
 *  form to the original blocklist files on disk and reloading them from scratch.
 */
export default interface UnifiedDiffTarget {
  /**
   * Use the given object to patch the blocklist data in memory.
   *
   * @param patcher An object which will apply the relevant blocklist diff to an in-memory array.
   */
  patchFromUnifiedDiff: (patcher: DiffMap) => void;
}

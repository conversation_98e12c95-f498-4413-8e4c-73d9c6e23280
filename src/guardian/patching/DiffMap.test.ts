import DiffMap, { MoveBlock } from './DiffMap';
import UnifiedDiffMapBuilder from './UnifiedDiffMapBuilder';
import { areArraysEqual } from 'utilities/Helpers';
import { extractStringValueFromJsonArrayBlocklistLine } from 'guardian/utilities/GuardianUtilities';
import testDataManager from 'test-helpers/testDataManager';

describe('DiffMap', () => {
  // -----------------------------------------------------------------------------------------------
  // Constructor and basic properties tests

  describe('Constructor and Properties', () => {
    let diffMap: DiffMap;

    beforeEach(() => {
      diffMap = new DiffMap();
    });

    it('should initialize with default values', () => {
      expect(diffMap.isFinalised).toBe(false);
      expect(diffMap.oldFilePath).toBe('');
      expect(diffMap.newFilePath).toBe('');
      expect(diffMap.netMovement).toBe(0);
      expect(diffMap.hasChanges()).toBe(false);
      expect(diffMap.getNumMoveBlocks()).toBe(0);
      expect(diffMap.getNumWriteBlocks()).toBe(0);
    });

    it('should allow setting file paths before finalisation', () => {
      diffMap.oldFilePath = 'old/path.txt';
      diffMap.newFilePath = 'new/path.txt';

      expect(diffMap.oldFilePath).toBe('old/path.txt');
      expect(diffMap.newFilePath).toBe('new/path.txt');
    });

    it('should prevent setting file paths after finalisation', () => {
      diffMap.finalise();

      expect(() => {
        diffMap.oldFilePath = 'test.txt';
      }).toThrow('Cannot change old file path after DiffMap has been finalised.');

      expect(() => {
        diffMap.newFilePath = 'test.txt';
      }).toThrow('Cannot change new file path after DiffMap has been finalised.');
    });

    it('should detect changes when move or write blocks exist', () => {
      expect(diffMap.hasChanges()).toBe(false);

      // Add an addition to create a write block
      diffMap.handleAddition(0, 0, 'test');
      expect(diffMap.hasChanges()).toBe(true);
    });
  });

  // -----------------------------------------------------------------------------------------------
  // Addition and deletion handling tests

  describe('Addition and Deletion Handling', () => {
    let diffMap: DiffMap;

    beforeEach(() => {
      diffMap = new DiffMap();
    });

    it('should handle a single addition correctly', () => {
      diffMap.handleAddition(5, 5, 'added line');

      expect(diffMap.netMovement).toBe(1);
      expect(diffMap.getNumWriteBlocks()).toBe(1);
      expect(diffMap.getNumMoveBlocks()).toBe(0); // No move blocks until finalised
    });

    it('should handle a single deletion correctly', () => {
      diffMap.handleDeletion(3);

      expect(diffMap.netMovement).toBe(-1);
      expect(diffMap.getNumWriteBlocks()).toBe(0);
      expect(diffMap.getNumMoveBlocks()).toBe(0); // No move blocks until finalised
    });

    it('should handle multiple additions correctly', () => {
      diffMap.handleAddition(0, 0, 'line 1');
      diffMap.handleAddition(1, 2, 'line 2');
      diffMap.handleAddition(2, 4, 'line 3');

      expect(diffMap.netMovement).toBe(3);
      expect(diffMap.getNumWriteBlocks()).toBe(3); // Non-contiguous additions create separate blocks
    });

    it('should handle contiguous additions in the same write block', () => {
      diffMap.handleAddition(0, 0, 'line 1');
      diffMap.handleAddition(0, 1, 'line 2');
      diffMap.handleAddition(0, 2, 'line 3');

      expect(diffMap.netMovement).toBe(3);
      expect(diffMap.getNumWriteBlocks()).toBe(1); // Contiguous additions in same block
    });

    it('should prevent operations after finalisation', () => {
      diffMap.finalise();

      expect(() => {
        diffMap.handleAddition(0, 0, 'test');
      }).toThrow('Cannot handle an addition after the DiffMap is finalised.');

      expect(() => {
        diffMap.handleDeletion(0);
      }).toThrow('Cannot handle a deletion after the DiffMap is finalised.');
    });

    it('should prevent double finalisation', () => {
      diffMap.finalise();

      expect(() => {
        diffMap.finalise();
      }).toThrow('Already finalised.');
    });
  });

  // -----------------------------------------------------------------------------------------------
  // Patching operation tests

  describe('Patching Operations', () => {
    it('should handle empty diff (no changes)', () => {
      const diffMap = new DiffMap();
      diffMap.finalise();

      const input = ['line 1', 'line 2', 'line 3'];
      const expected = [...input];

      diffMap.patchArrayInPlace(input);
      expect(input).toEqual(expected);
    });

    it('should handle pure additions', () => {
      const diffMap = new DiffMap();
      diffMap.handleAddition(0, 0, 'new line 1');
      diffMap.handleAddition(0, 1, 'new line 2');
      diffMap.finalise();

      const input = ['original line'];
      diffMap.patchArrayInPlace(input);

      expect(input).toEqual(['new line 1', 'new line 2', 'original line']);
    });

    it('should handle pure deletions', () => {
      const diffMap = new DiffMap();
      diffMap.handleDeletion(1); // Delete second line
      diffMap.finalise();

      const input = ['line 1', 'line 2', 'line 3'];
      diffMap.patchArrayInPlace(input);

      expect(input).toEqual(['line 1', 'line 3']);
    });

    it('should handle mixed additions and deletions', () => {
      const diffMap = new DiffMap();
      diffMap.handleDeletion(1); // Delete 'line 2'
      diffMap.handleAddition(2, 1, 'new line'); // Add after 'line 1'
      diffMap.finalise();

      const input = ['line 1', 'line 2', 'line 3'];
      diffMap.patchArrayInPlace(input);

      expect(input).toEqual(['line 1', 'new line', 'line 3']);
    });

    it('should handle value processor correctly', () => {
      const diffMap = new DiffMap();
      diffMap.handleAddition(0, 0, '"quoted value"');
      diffMap.finalise();

      const input = ['original'];
      const valueProcessor = (value: string): string => value.replace(/"/g, '');

      diffMap.patchArrayInPlace(input, valueProcessor);
      expect(input).toEqual(['quoted value', 'original']);
    });

    it('should handle index offset correctly', () => {
      const diffMap = new DiffMap();
      diffMap.handleAddition(2, 2, 'new line'); // Add at index 2 in the diff
      diffMap.finalise();

      const input = ['[', 'line 1', 'line 2', ']']; // JSON-like structure
      diffMap.patchArrayInPlace(input, undefined, -1); // Offset by -1 to skip opening bracket

      // With offset -1, the addition at index 2 becomes index 1, so it goes after '['
      expect(input).toEqual(['[', 'new line', 'line 1', 'line 2', ']']);
    });

    it('should correctly patches an array using a simple Unified Diff', () => {
      // This is a simple test using manually constructed data.
      const input = [
        'original line 1',
        'original line 2',
        'original line 3',
        'original line 4',
        'original line 5',
        'original line 6',
        'original line 7',
        'original line 8',
        'original line 9',
        'original line 10',
        'original line 11', // <-- this line will be deleted
        'original line 12', // <-- this line will be deleted
        'original line 13',
        'original line 14',
        'original line 15',
        'original line 16',
        'original line 17',
        // 3 lines will be added here
        'original line 18',
        'original line 19',
        'original line 20',
        'original line 21',
        'original line 22',
        'original line 23',
        'original line 24',
        'original line 25', // <-- this line will be replaced
        'original line 26', // <-- this line will be replaced
        'original line 27',
        'original line 28',
        'original line 29',
        'original line 30',
      ];

      const expectedOutput = [
        'original line 1',
        'original line 2',
        'original line 3',
        'original line 4',
        'original line 5',
        'original line 6',
        'original line 7',
        'original line 8',
        'original line 9',
        'original line 10',
        // 'original line 11' <-- deleted
        // 'original line 12' <-- deleted
        'original line 13',
        'original line 14',
        'original line 15',
        'original line 16',
        'original line 17',
        'ADDED line 17.1', // <-- added
        'ADDED line 17.2', // <-- added
        'ADDED line 17.3', // <-- added
        'original line 18',
        'original line 19',
        'original line 20',
        'original line 21',
        'original line 22',
        'original line 23',
        'original line 24',
        'REPLACED line 25', // <-- replaced
        'REPLACED line 26', // <-- replaced
        'original line 27',
        'original line 28',
        'original line 29',
        'original line 30',
      ];

      const unifiedDiff = `\
--- a/file.txt\t2025-01-02 03:04:05.678 +0000
+++ b/file.txt\t2025-02-03 04:05:06.789 +0000
@@ -10,4 +10,2 @@
 original line 10
-original line 11
-original line 12
 original line 13
@@ -17,2 +15,5 @@
 original line 17
+ADDED line 17.1
+ADDED line 17.2
+ADDED line 17.3
 original line 18
@@ -24,4 +25,4 @@
 original line 24
-original line 25
-original line 26
+REPLACED line 25
+REPLACED line 26
 original line 27`;

      const diffMap = UnifiedDiffMapBuilder.buildFromFile(unifiedDiff).next().value;
      // The input array is modified in-place.
      diffMap.patchArrayInPlace(input);
      expect(input).toEqual(expectedOutput);
    });
  });

  // -----------------------------------------------------------------------------------------------
  // Helper method tests

  describe('Helper Methods', () => {
    describe('getMoveBlockDirection', () => {
      it('should return 1 for positive movement', () => {
        const moveBlock: MoveBlock = { start: 0, size: 5, movement: 3 };
        expect(DiffMap.getMoveBlockDirection(moveBlock)).toBe(1);
      });

      it('should return -1 for negative movement', () => {
        const moveBlock: MoveBlock = { start: 0, size: 5, movement: -2 };
        expect(DiffMap.getMoveBlockDirection(moveBlock)).toBe(-1);
      });

      it('should return 0 for no movement', () => {
        const moveBlock: MoveBlock = { start: 0, size: 5, movement: 0 };
        expect(DiffMap.getMoveBlockDirection(moveBlock)).toBe(0);
      });
    });

    describe('findMoveBlockGroupLength', () => {
      it('should return 1 for single block', () => {
        const moveBlocks: MoveBlock[] = [{ start: 0, size: 5, movement: 1 }];
        expect(DiffMap.findMoveBlockGroupLength(moveBlocks, 0)).toBe(1);
      });

      it('should find group of blocks with same direction', () => {
        const moveBlocks: MoveBlock[] = [
          { start: 0, size: 5, movement: 1 },
          { start: 5, size: 3, movement: 2 },
          { start: 8, size: 2, movement: 1 },
          { start: 10, size: 4, movement: -1 }, // Different direction
        ];
        expect(DiffMap.findMoveBlockGroupLength(moveBlocks, 0)).toBe(3);
        expect(DiffMap.findMoveBlockGroupLength(moveBlocks, 3)).toBe(1);
      });

      it('should throw error for invalid start index', () => {
        const moveBlocks: MoveBlock[] = [{ start: 0, size: 5, movement: 1 }];
        expect(() => {
          DiffMap.findMoveBlockGroupLength(moveBlocks, 1);
        }).toThrow('Invalid move block index.');
      });
    });
  });

  // -----------------------------------------------------------------------------------------------
  // Error handling and edge cases

  describe('Error Handling and Edge Cases', () => {
    it('should handle empty arrays', () => {
      const diffMap = new DiffMap();
      diffMap.handleAddition(0, 0, 'first line');
      diffMap.finalise();

      const input: string[] = [];
      diffMap.patchArrayInPlace(input);

      expect(input).toEqual(['first line']);
    });

    it('should handle large arrays efficiently', () => {
      const diffMap = new DiffMap();
      const largeArray = Array.from({ length: 10000 }, (_, i) => `line ${i}`);

      // Add some changes
      diffMap.handleAddition(5000, 5000, 'inserted line');
      diffMap.handleDeletion(7500);
      diffMap.finalise();

      const start = performance.now();
      diffMap.patchArrayInPlace(largeArray);
      const duration = performance.now() - start;

      // Should complete in reasonable time (less than 100ms for 10k elements)
      expect(duration).toBeLessThan(100);
      expect(largeArray.length).toBe(10000); // Net change: +1 -1 = 0
    });

    it('should handle value processor errors gracefully', () => {
      const diffMap = new DiffMap();
      diffMap.handleAddition(0, 0, 'test');
      diffMap.finalise();

      const input = ['original'];
      const faultyProcessor = (): never => {
        throw new Error('Processing failed');
      };

      expect(() => {
        diffMap.patchArrayInPlace(input, faultyProcessor);
      }).toThrow('Processing failed');
    });

    it('should handle non-contiguous write operations', () => {
      const diffMap = new DiffMap();

      // Create non-contiguous writes that should result in separate write blocks
      diffMap.handleAddition(0, 0, 'line 0');
      diffMap.handleAddition(0, 2, 'line 2'); // Gap at index 1, should create new block

      expect(diffMap.getNumWriteBlocks()).toBe(2);
    });

    it('should handle complex move block scenarios', () => {
      const diffMap = new DiffMap();

      // Create a scenario with multiple deletions and additions
      diffMap.handleDeletion(1);
      diffMap.handleDeletion(3);
      diffMap.handleAddition(5, 1, 'new line 1');
      diffMap.handleAddition(6, 3, 'new line 2');
      diffMap.finalise();

      const input = ['line 0', 'line 1', 'line 2', 'line 3', 'line 4', 'line 5'];
      diffMap.patchArrayInPlace(input);

      // Should handle the complex transformation correctly
      expect(input.length).toBeGreaterThan(0);
    });

    it('should validate move block constraints', () => {
      // This tests internal validation through the public interface
      const diffMap = new DiffMap();

      // Create a scenario that would result in an open move block not at the end
      diffMap.handleAddition(0, 0, 'test');
      diffMap.finalise();

      // The finalisation should work without throwing
      expect(diffMap.isFinalised).toBe(true);
    });

    it('should handle open move blocks correctly', () => {
      const diffMap = new DiffMap();

      // Create a scenario that will result in an open move block
      diffMap.handleDeletion(0);
      diffMap.finalise();

      const input = ['line 0', 'line 1', 'line 2'];
      diffMap.patchArrayInPlace(input);

      // Should handle open move blocks (size = 0) correctly
      expect(input.length).toBe(2); // One deletion
    });

    it('should throw error for invalid open move block placement', () => {
      // This is a more complex test to trigger the open move block validation
      const diffMap = new DiffMap();

      // Create a scenario with multiple changes that could trigger validation
      diffMap.handleDeletion(1);
      diffMap.handleAddition(3, 2, 'new line');
      diffMap.finalise();

      const input = ['line 0', 'line 1', 'line 2', 'line 3'];

      // Should not throw - the validation is internal
      expect(() => {
        diffMap.patchArrayInPlace(input);
      }).not.toThrow();
    });

    it('should handle write operation sequence validation', () => {
      const diffMap = new DiffMap();

      // Test the internal write sequence validation by creating non-overlapping writes
      diffMap.handleAddition(0, 0, 'first');
      diffMap.handleAddition(0, 2, 'third'); // Gap at index 1, creates new write block

      expect(diffMap.getNumWriteBlocks()).toBe(2);
    });

    it('should handle move block state validation', () => {
      const diffMap = new DiffMap();

      // Create a scenario that exercises move block state management
      diffMap.handleDeletion(0);
      diffMap.handleDeletion(2);
      diffMap.handleAddition(4, 2, 'new');
      diffMap.finalise();

      const input = ['a', 'b', 'c', 'd', 'e'];
      diffMap.patchArrayInPlace(input);

      // Should handle the complex state correctly
      expect(input).toContain('new');
    });
  });

  // -----------------------------------------------------------------------------------------------
  // Integration tests with real data

  describe('Integration Tests', () => {
    it('correctly applies a real blocklist diff to a JSON array blocklist file', () => {
      // A JSON array file like "domainsurls.*" requires changes to be parsed from their JSON
      //  representation, and requires the line numbering to be offset by -1.

      const urlListFilename = 'domainsurls.com';
      const testData = testDataManager.blocklistWithLargeDiff;
      // We need to parse these files as JSON to get an array.
      const oldUrlList = JSON.parse(testData.from.files[urlListFilename]);
      const newUrlList = JSON.parse(testData.to.files[urlListFilename]);

      // Build the diff maps for the Unified Diff file, and find the one we want to test.
      const unifiedDiff = testData.diff.files['blocklist.diff'];
      const diffMaps = [...UnifiedDiffMapBuilder.buildFromFile(unifiedDiff)];
      const diffMap = diffMaps.find((diffMap) => diffMap.oldFilePath.includes(urlListFilename));
      if (diffMap === undefined) {
        throw new Error('Expected diff not found in built diff maps.');
      }

      // Apply the patch and verify that it worked as expected.
      diffMap?.patchArrayInPlace(oldUrlList, extractStringValueFromJsonArrayBlocklistLine, -1);
      expect(oldUrlList).toHaveLength(newUrlList.length);
      // TODO: Use a custom big array matcher. Using Jest's basic equality check is very slow.
      expect(areArraysEqual(oldUrlList, newUrlList)).toBeTrue();
    });

    it('correctly applies a real blocklist diff to a plain text blocklist file', () => {
      // A plain text file like "videoids" doesn't require special parsing or line offsetting.

      const urlListFilename = 'videoids';
      const testData = testDataManager.blocklistWithLargeDiff;
      // We just need to split these files at line breaks to get an array.
      const oldVideoIdList = testData.from.files[urlListFilename].split(/\r\n|\r|\n/);
      const newVideoIdList = testData.to.files[urlListFilename].split(/\r\n|\r|\n/);

      // Build the diff maps for the Unified Diff file, and find the one we want to test.
      const unifiedDiff = testData.diff.files['blocklist.diff'];
      const diffMaps = [...UnifiedDiffMapBuilder.buildFromFile(unifiedDiff)];
      const diffMap = diffMaps.find((diffMap) => diffMap.oldFilePath.includes(urlListFilename));
      if (diffMap === undefined) {
        throw new Error('Expected diff not found in built diff maps.');
      }

      // Apply the patch and verify that it worked as expected.
      diffMap?.patchArrayInPlace(oldVideoIdList);
      expect(oldVideoIdList).toHaveLength(newVideoIdList.length);
      // Use custom array comparison for better performance with large arrays
      expect(areArraysEqual(oldVideoIdList, newVideoIdList)).toBeTrue();
    });
  });

  // -----------------------------------------------------------------------------------------------
  // Performance and stress tests

  describe('Performance Tests', () => {
    it('should handle many small changes efficiently', () => {
      const diffMap = new DiffMap();
      const numChanges = 1000;

      // Add many small changes
      for (let i = 0; i < numChanges; i++) {
        if (i % 2 === 0) {
          diffMap.handleAddition(i, i, `added line ${i}`);
        } else {
          diffMap.handleDeletion(i);
        }
      }

      diffMap.finalise();

      const largeArray = Array.from({ length: 2000 }, (_, i) => `original line ${i}`);
      const start = performance.now();
      diffMap.patchArrayInPlace(largeArray);
      const duration = performance.now() - start;

      // Should handle many changes efficiently
      expect(duration).toBeLessThan(200);
      expect(diffMap.getNumMoveBlocks()).toBeGreaterThan(0);
      expect(diffMap.getNumWriteBlocks()).toBeGreaterThan(0);
    });

    it('should optimize contiguous write operations', () => {
      const diffMap = new DiffMap();

      // Add contiguous writes
      diffMap.handleAddition(0, 0, 'line 1');
      diffMap.handleAddition(0, 1, 'line 2');
      diffMap.handleAddition(0, 2, 'line 3');
      diffMap.handleAddition(0, 3, 'line 4');

      // Should optimize into a single write block
      expect(diffMap.getNumWriteBlocks()).toBe(1);
    });

    it('should handle alternating additions and deletions', () => {
      const diffMap = new DiffMap();

      // Alternate between additions and deletions
      for (let i = 0; i < 100; i++) {
        if (i % 2 === 0) {
          diffMap.handleAddition(i, i, `new ${i}`);
        } else {
          diffMap.handleDeletion(i);
        }
      }

      diffMap.finalise();

      const input = Array.from({ length: 200 }, (_, i) => `line ${i}`);
      diffMap.patchArrayInPlace(input);

      // Should complete without errors
      expect(input.length).toBeGreaterThan(0);
    });
  });

  // -----------------------------------------------------------------------------------------------
  // Regression tests for specific scenarios

  describe('Regression Tests', () => {
    it('should handle JSON array line processing correctly', () => {
      const diffMap = new DiffMap();
      diffMap.handleAddition(1, 1, '"new entry",');
      diffMap.finalise();

      const jsonArray = ['[', '"existing entry",', ']'];
      const processor = extractStringValueFromJsonArrayBlocklistLine;

      diffMap.patchArrayInPlace(jsonArray, processor, -1);

      expect(jsonArray).toContain('new entry');
    });

    it('should maintain array integrity with complex operations', () => {
      const diffMap = new DiffMap();

      // Simulate a complex real-world scenario
      diffMap.handleDeletion(5);
      diffMap.handleDeletion(10);
      diffMap.handleAddition(15, 13, 'replacement 1');
      diffMap.handleAddition(16, 14, 'replacement 2');
      diffMap.handleAddition(20, 18, 'new addition');
      diffMap.finalise();

      const input = Array.from({ length: 25 }, (_, i) => `original ${i}`);
      const originalLength = input.length;

      diffMap.patchArrayInPlace(input);

      // Verify the net change is correct: -2 deletions + 3 additions = +1
      expect(input.length).toBe(originalLength + 1);
      expect(input).toContain('replacement 1');
      expect(input).toContain('replacement 2');
      expect(input).toContain('new addition');
    });
  });

  // -----------------------------------------------------------------------------------------------
  // Performance optimizations and metrics

  describe('getPerformanceMetrics', () => {
    it('returns correct metrics for empty diff map', () => {
      const diffMap = new DiffMap();
      diffMap.oldFilePath = 'old.txt';
      diffMap.newFilePath = 'new.txt';
      diffMap.finalise();

      const metrics = diffMap.getPerformanceMetrics();

      expect(metrics).toEqual({
        moveBlockCount: 0,
        writeBlockCount: 0,
        totalOperations: 0,
        netMovement: 0,
        estimatedComplexity: 'low',
      });
    });

    it('returns correct metrics for simple diff map', () => {
      const diffMap = new DiffMap();
      diffMap.oldFilePath = 'old.txt';
      diffMap.newFilePath = 'new.txt';
      diffMap.handleAddition(1, 0, 'added line');
      diffMap.handleDeletion(2);
      diffMap.finalise();

      const metrics = diffMap.getPerformanceMetrics();

      expect(metrics.moveBlockCount).toBeGreaterThanOrEqual(0);
      expect(metrics.writeBlockCount).toBeGreaterThanOrEqual(0);
      expect(metrics.totalOperations).toBeGreaterThan(0);
      expect(metrics.estimatedComplexity).toBe('low');
    });

    it('estimates high complexity for large diffs', () => {
      const diffMap = new DiffMap();
      diffMap.oldFilePath = 'old.txt';
      diffMap.newFilePath = 'new.txt';

      // Add many operations to trigger high complexity
      for (let i = 0; i < 1500; i++) {
        diffMap.handleAddition(i, i, `line ${i}`);
      }
      diffMap.finalise();

      const metrics = diffMap.getPerformanceMetrics();
      expect(metrics.estimatedComplexity).toBe('high');
      expect(metrics.totalOperations).toBeGreaterThan(1000);
    });
  });

  describe('Performance optimizations', () => {
    it('handles empty diffs efficiently', () => {
      const diffMap = new DiffMap();
      diffMap.oldFilePath = 'old.txt';
      diffMap.newFilePath = 'new.txt';
      diffMap.finalise();

      const target = ['line1', 'line2', 'line3'];
      const originalTarget = [...target];

      const start = performance.now();
      diffMap.patchArrayInPlace(target);
      const duration = performance.now() - start;

      expect(target).toEqual(originalTarget);
      expect(duration).toBeLessThan(1); // Should be very fast for empty diffs
    });

    it('processes large arrays efficiently', () => {
      const diffMap = new DiffMap();
      diffMap.oldFilePath = 'old.txt';
      diffMap.newFilePath = 'new.txt';

      // Create a large array
      const target = new Array(10000).fill(0).map((_, i) => `line ${i}`);

      // Add some changes
      diffMap.handleAddition(5000, 0, 'inserted line');
      diffMap.handleDeletion(7500);
      diffMap.finalise();

      const start = performance.now();
      diffMap.patchArrayInPlace(target);
      const duration = performance.now() - start;

      expect(duration).toBeLessThan(100); // Should complete in reasonable time
      expect(target.length).toBe(10000); // Net change should be 0 (1 add, 1 delete)
    });
  });

  // -----------------------------------------------------------------------------------------------
  // Additional Error Condition Tests for 100% Coverage

  describe('Uncovered Error Conditions', () => {
    it('should throw error when move block start point is not determined', () => {
      const diffMap = new DiffMap();

      // Create a scenario where _nextMoveBlockStartsAt is null but _netMovement is non-zero
      // This happens when we try to create a move block before any reference point is established
      diffMap.handleAddition(0, 0, 'test'); // This sets _netMovement to 1

      // Force the internal state to simulate the error condition
      // We need to access private methods to trigger this specific error
      const diffMapAny = diffMap as any;
      diffMapAny._nextMoveBlockStartsAt = null; // Reset the start point

      expect(() => {
        // Try to trigger move block creation with null start point
        diffMapAny._openMoveBlock();
      }).toThrow('Cannot create a move block. The start point has not yet been determined.');
    });

    it('should throw error for move block opening out of sequence', () => {
      const diffMap = new DiffMap();

      const diffMapAny = diffMap as any;

      // Manually create a move block to set up the error condition
      // The error occurs when _nextMoveBlockStartsAt < moveBlock.start + moveBlock.size
      diffMapAny._moveBlocks = [{ start: 10, size: 5, movement: 1 }];
      diffMapAny._nextMoveBlockStartsAt = 12; // 12 < 10 + 5 = 15, so this should trigger error
      diffMapAny._netMovement = 1;

      expect(() => {
        diffMapAny._openMoveBlock();
      }).toThrow('Move block opening is out of sequence.');
    });

    it('should throw error when movement value changes during open move block', () => {
      const diffMap = new DiffMap();

      const diffMapAny = diffMap as any;

      // Set up an open move block (size = 0)
      diffMapAny._moveBlocks = [{ start: 0, size: 0, movement: 1 }];
      diffMapAny._netMovement = 2; // Different from move block movement
      diffMapAny._nextMoveBlockStartsAt = 0;

      expect(() => {
        diffMapAny._openMoveBlock();
      }).toThrow('Movement value should not change during a move block.');
    });

    it('should throw error for move block closure out of sequence', () => {
      const diffMap = new DiffMap();

      const diffMapAny = diffMap as any;

      // Set up an open move block
      diffMapAny._moveBlocks = [{ start: 10, size: 0, movement: 1 }];

      expect(() => {
        // Try to close at an index before the start
        diffMapAny._closeMoveBlock(5);
      }).toThrow('Move block closure is out of sequence.');
    });

    it('should throw error for write operation out of sequence', () => {
      const diffMap = new DiffMap();

      const diffMapAny = diffMap as any;

      // Set up existing write block
      diffMapAny._writeBlocks = [{ start: 10, elements: ['test1', 'test2'] }];

      expect(() => {
        // Try to write at an index before the end of existing block
        diffMapAny._addWrite(11, 'test3'); // 11 < 10 + 2 = 12
      }).toThrow('Write operation is out of sequence.');
    });

    it('should handle medium complexity performance metrics', () => {
      const diffMap = new DiffMap();

      // Create a scenario with medium complexity (100-1000 operations)
      for (let i = 0; i < 150; i++) {
        diffMap.handleAddition(i, i, `line ${i}`);
      }
      diffMap.finalise();

      const metrics = diffMap.getPerformanceMetrics();
      expect(metrics.estimatedComplexity).toBe('medium');
    });

    it('should handle open move block at end validation', () => {
      const diffMap = new DiffMap();
      const diffMapAny = diffMap as any;

      // Set up move blocks with an open block not at the end
      diffMapAny._moveBlocks = [
        { start: 0, size: 0, movement: 1 }, // Open block not at end
        { start: 5, size: 3, movement: 1 }, // Another block after it
      ];

      expect(() => {
        diffMapAny._calculateBlockEnd(diffMapAny._moveBlocks[0], 0, 0, 10);
      }).toThrow('Open move blocks are only allowed at the end.');
    });

    it('should handle open move block return path', () => {
      const diffMap = new DiffMap();
      const diffMapAny = diffMap as any;

      // Set up an open move block with matching movement
      diffMapAny._moveBlocks = [{ start: 0, size: 0, movement: 1 }];
      diffMapAny._netMovement = 1;
      diffMapAny._nextMoveBlockStartsAt = 0;

      // This should return early without throwing
      expect(() => {
        diffMapAny._openMoveBlock();
      }).not.toThrow();

      // Verify it returned early (no new move block added)
      expect(diffMapAny._moveBlocks.length).toBe(1);
    });

    it('should handle default indexOffset parameter in _applyWriteBlocks', () => {
      const diffMap = new DiffMap();
      diffMap.handleAddition(0, 0, 'test');
      diffMap.finalise();

      const diffMapAny = diffMap as any;
      const target = ['original'];

      // Call _applyWriteBlocks without indexOffset to test default parameter
      expect(() => {
        diffMapAny._applyWriteBlocks(target);
      }).not.toThrow();

      // The addition at index 0 should insert 'test' at the beginning
      expect(target).toEqual(['test']);
    });
  });
});

import UnifiedDiff<PERSON>ine, {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Unknown,
} from './UnifiedDiffLine';
import { readLineByLine } from 'utilities/TextUtilities';

/**
 * Parser for Unified Diff format files with stateful validation and line numbering.
 *
 * Supports both full-file parsing and memory-efficient line-by-line processing.
 * Maintains state to track file headers, hunk descriptors, and validate diff structure.
 */
export default class UnifiedDiffParser {
  // -----------------------------------------------------------------------------------------------
  // State Accessors and Validation

  /** 1-based line number of the most recently parsed line. Useful for error reporting. */
  public get diffLineNumber(): number {
    return this._diffLineNumber;
  }

  /** Shallow copy of the most recently parsed old file header. */
  public get oldFileHeader(): OldFileHeader | null {
    return this._oldFileHeader === null ? null : { ...this._oldFileHeader };
  }

  /** Shallow copy of the most recently parsed new file header. */
  public get newFileHeader(): NewFileHeader | null {
    return this._newFileHeader === null ? null : { ...this._newFileHeader };
  }

  /** Checks if any file header has been parsed. */
  public get hasHeader(): boolean {
    return this._oldFileHeader !== null || this._newFileHeader !== null;
  }

  /** Deep copy of the most recently parsed hunk descriptor. */
  public get hunkDescriptor(): HunkDescriptor | null {
    if (this._hunkDescriptor === null) {
      return null;
    }

    return {
      ...this._hunkDescriptor,
      oldRange: { ...this._hunkDescriptor.oldRange },
      newRange: { ...this._hunkDescriptor.newRange },
    };
  }

  /** Checks if a hunk descriptor is currently active. */
  public get hasHunkDescriptor(): boolean {
    return this._hunkDescriptor !== null;
  }

  /** Returns true if more hunk lines are expected based on the current hunk descriptor. */
  public isExpectingHunkLine(): boolean {
    if (this._hunkDescriptor === null) {
      return false;
    }

    const expectedOldLines = this._hunkDescriptor.oldRange.size;
    const expectedNewLines = this._hunkDescriptor.newRange.size;
    const actualOldLines = this._numDeletions + this._numContext;
    const actualNewLines = this._numAdditions + this._numContext;

    return expectedOldLines > actualOldLines || expectedNewLines > actualNewLines;
  }

  /** Returns statistics for the current hunk, or null if no hunk is active. */
  public getHunkStatistics(): {
    additions: number;
    deletions: number;
    context: number;
    expectedOld: number;
    expectedNew: number;
  } | null {
    if (this._hunkDescriptor === null) {
      return null;
    }

    return {
      additions: this._numAdditions,
      deletions: this._numDeletions,
      context: this._numContext,
      expectedOld: this._hunkDescriptor.oldRange.size,
      expectedNew: this._hunkDescriptor.newRange.size,
    };
  }

  // -----------------------------------------------------------------------------------------------
  // High-Level Parsing Operations

  /** Parses an entire Unified Diff file and returns all parsed lines. */
  public static parseFile(unifiedDiff: string): UnifiedDiffLine[] {
    const parser = new UnifiedDiffParser();
    // Split the file up into lines. Rather than creating a separate array of parsed lines, we'll
    //  progressively replace each original line in this array with its parsed equivalent.
    const lines: Array<string | UnifiedDiffLine> = unifiedDiff.split(/\r\n|\r|\n/);
    const numLines = lines.length;
    for (let i = 0; i < numLines; ++i) {
      lines[i] = parser.parseLine(lines[i] as string);
    }
    return lines as UnifiedDiffLine[];
  }

  /** Memory-efficient line-by-line parsing that yields one parsed line at a time. */
  public static *parseFileLineByLine(unifiedDiff: string): Generator<UnifiedDiffLine> {
    const parser = new UnifiedDiffParser();
    for (const line of readLineByLine(unifiedDiff)) {
      yield parser.parseLine(line);
    }
  }

  /**
   * Parses a single line using stateful context. This is the core parsing method.
   * Lines must be processed in order. Call clear() between different diff files.
   */
  public parseLine(line: string): UnifiedDiffLine {
    const diffLineNumber = ++this._diffLineNumber;

    try {
      const parsedLine = this._parseLineContent(line);
      this._enrichWithStatefulData(parsedLine, diffLineNumber);
      this._validateAndUpdateState(parsedLine);
      return parsedLine;
    } catch (error: any) {
      this._logParsingError(error, line, diffLineNumber);
      throw error;
    }
  }

  /** Parses line content using priority-based logic to determine line type. */
  private _parseLineContent(line: string): UnifiedDiffLine {
    // Priority 1: Try to parse as header (only if not expecting hunk lines)
    if (!this.isExpectingHunkLine()) {
      const headerResult = UnifiedDiffParser.tryToParseAsHeader(line);
      if (headerResult !== null) {
        return headerResult;
      }
    }

    // Priority 2: Try to parse as hunk line (addition, deletion, context)
    const hunkLineResult = UnifiedDiffParser.tryToParseAsHunkLine(line);
    if (hunkLineResult !== null) {
      return hunkLineResult;
    }

    // Priority 3: Try to parse as hunk descriptor
    const hunkDescriptorResult = UnifiedDiffParser.tryToParseAsHunkDescriptor(line);
    if (hunkDescriptorResult !== null) {
      return hunkDescriptorResult;
    }

    // Fallback: Parse as unknown line type
    return UnifiedDiffParser.parseAsUnknown(line);
  }

  /** Enriches parsed line with stateful information like line numbers. */
  private _enrichWithStatefulData(parsedLine: UnifiedDiffLine, diffLineNumber: number): void {
    parsedLine.diffLineNumber = diffLineNumber;

    if (this._isHunkLine(parsedLine)) {
      parsedLine.oldFileLineNumber = this._calculateOldFileLineNumber();
      parsedLine.newFileLineNumber = this._calculateNewFileLineNumber();
    }
  }

  /** Checks if a parsed line is a hunk line (addition, deletion, or context). */
  private _isHunkLine(parsedLine: UnifiedDiffLine): parsedLine is HunkLine {
    return (
      parsedLine.type === Type.addition ||
      parsedLine.type === Type.deletion ||
      parsedLine.type === Type.context
    );
  }

  /** Logs detailed error information when parsing fails. */
  private _logParsingError(error: any, line: string, diffLineNumber: number): void {
    console.error('UnifiedDiffParser - Failed to parse Unified Diff:', error.message, '\n', {
      line,
      lineNumber: diffLineNumber,
      parserState: {
        hasHeader: this.hasHeader,
        hasHunkDescriptor: this.hasHunkDescriptor,
        isExpectingHunkLine: this.isExpectingHunkLine(),
        hunkStats: this.getHunkStatistics(),
      },
    });
  }

  /** Resets all internal parsing state. Call between parsing different diff files. */
  public clear(): void {
    this._diffLineNumber = 0;
    this._previousFunctionalLine = null;
    this._clearHeaderState();
    this._clearHunkState();
  }

  /** Validates parsed line against parser state and updates internal state. */
  private _validateAndUpdateState(parsedLine: UnifiedDiffLine): void {
    switch (parsedLine.type) {
      case Type.oldFileHeader:
        this._handleOldFileHeader(parsedLine);
        break;

      case Type.newFileHeader:
        this._handleNewFileHeader(parsedLine);
        break;

      case Type.hunkDescriptor:
        this._handleHunkDescriptor(parsedLine);
        break;

      case Type.addition:
        this._handleAddition();
        break;

      case Type.deletion:
        this._handleDeletion();
        break;

      case Type.context:
        this._handleContext();
        break;

      case Type.unknown:
        // Unknown lines don't affect parser state
        break;
    }

    // Update the previous functional line tracker (excluding unknown lines)
    if (parsedLine.type !== Type.unknown) {
      this._previousFunctionalLine = parsedLine;
    }
  }

  /** Handles old file header lines, which start a new diff. */
  private _handleOldFileHeader(parsedLine: OldFileHeader): void {
    this._clearHeaderState();
    this._clearHunkState();
    this._oldFileHeader = parsedLine;
  }

  /** Handles new file header lines. */
  private _handleNewFileHeader(parsedLine: NewFileHeader): void {
    if (this._previousFunctionalLine?.type !== Type.oldFileHeader) {
      this._clearHeaderState();
    }
    this._clearHunkState();
    this._newFileHeader = parsedLine;
  }

  /** Handles hunk descriptor lines. */
  private _handleHunkDescriptor(parsedLine: HunkDescriptor): void {
    if (!this.hasHeader) {
      throw new Error('Malformed diff. A hunk descriptor cannot occur without a header.');
    }
    this._clearHunkState();
    this._hunkDescriptor = parsedLine;
  }

  /** Handles addition lines and updates the addition counter. */
  private _handleAddition(): void {
    if (!this.hasHunkDescriptor) {
      throw new Error('Malformed diff. An addition cannot occur without a hunk descriptor.');
    }
    this._numAdditions++;
  }

  /** Handles deletion lines and updates the deletion counter. */
  private _handleDeletion(): void {
    if (!this.hasHunkDescriptor) {
      throw new Error('Malformed diff. A deletion cannot occur without a hunk descriptor.');
    }
    this._numDeletions++;
  }

  /** Handles context lines and updates the context counter. */
  private _handleContext(): void {
    if (!this.hasHunkDescriptor) {
      throw new Error('Malformed diff. A context line cannot occur before a hunk descriptor.');
    }
    this._numContext++;
  }

  /** Calculates the line number in the old file for the current hunk line. */
  private _calculateOldFileLineNumber(): number {
    if (this._hunkDescriptor == null) {
      return 0;
    }

    // Only deletions and context lines appear in the old file
    return (
      this._hunkDescriptor.oldRange.start +
      this._numDeletions +
      this._numContext +
      (this._hunkDescriptor.oldRange.size === 0 ? 1 : 0) // Unified Diff quirk
    );
  }

  /** Calculates the line number in the new file for the current hunk line. */
  private _calculateNewFileLineNumber(): number {
    if (this._hunkDescriptor == null) {
      return 0;
    }

    // Only additions and context lines appear in the new file
    return (
      this._hunkDescriptor.newRange.start +
      this._numAdditions +
      this._numContext +
      (this._hunkDescriptor.newRange.size === 0 ? 1 : 0) // Unified Diff quirk
    );
  }

  /** Reset all parsing state related to file headers. */
  private _clearHeaderState(): void {
    this._oldFileHeader = null;
    this._newFileHeader = null;
  }

  /** Reset all parsing state related to hunks. */
  private _clearHunkState(): void {
    this._hunkDescriptor = null;
    this._numAdditions = 0;
    this._numDeletions = 0;
    this._numContext = 0;
  }

  /**
   * Try to parse the given string as a Unified Diff header line.
   * Returns null if the line is not a header.
   */
  public static tryToParseAsHeader(line: string): Header | null {
    let type: Type;
    if (line.startsWith('---')) {
      type = Type.oldFileHeader;
    } else if (line.startsWith('+++')) {
      type = Type.newFileHeader;
    } else {
      return null;
    }

    const prefixLength = 3;
    const tabPosition = line.lastIndexOf('\t');
    let path = '';
    let timestamp = '';

    if (tabPosition < 0) {
      path = line.substring(prefixLength).trim();
    } else {
      path = line.substring(prefixLength, tabPosition).trim();
      timestamp = line.substring(tabPosition).trim();
    }

    // Remove quotation marks if present
    if (path.length >= 2) {
      const first = path[0];
      const last = path[path.length - 1];
      if (first === last && (first === "'" || first === '"')) {
        path = path.substring(1, path.length - 1);
      }
    }

    // Unescape special characters (assumes Unix-style paths)
    path = path.replace(UnifiedDiffParser._escapeSequencePattern, '$1');

    return { diffLineNumber: 0, type, path, timestamp };
  }

  /** Try to parse the given string as a Unified Diff hunk descriptor. */
  public static tryToParseAsHunkDescriptor(line: string): HunkDescriptor | null {
    if (!line.startsWith('@@')) {
      return null;
    }

    const matches = line.match(UnifiedDiffParser._hunkDescriptorPattern);
    if (matches == null) {
      throw new Error('Hunk descriptor does not match expected structure.');
    }

    if (matches[1] === matches[4]) {
      throw new Error('Invalid hunk descriptor. Both ranges refer to the same file.');
    }
    const isOldRangeFirst = matches[1] === '-';

    const ranges: HunkRange[] = [
      {
        start: parseInt(matches[2]),
        size: parseInt(matches[3] ?? '1'),
      },
      {
        start: parseInt(matches[5]),
        size: parseInt(matches[6] ?? '1'),
      },
    ];

    // Validate range starts (0 is only valid when size is 0)
    if (
      (ranges[0].start === 0 && ranges[0].size > 0) ||
      (ranges[1].start === 0 && ranges[1].size > 0)
    ) {
      throw new Error(
        'Invalid hunk descriptor. The range start cannot be zero unless the range size is zero.',
      );
    }

    return {
      diffLineNumber: 0,
      type: Type.hunkDescriptor,
      oldRange: ranges[isOldRangeFirst ? 0 : 1],
      newRange: ranges[isOldRangeFirst ? 1 : 0],
    };
  }

  /** Try to parse the given string as a Unified Diff hunk line (addition, deletion, or context). */
  public static tryToParseAsHunkLine(line: string): HunkLine | null {
    let type: Type;
    if (line.startsWith('+')) {
      type = Type.addition;
    } else if (line.startsWith('-')) {
      type = Type.deletion;
    } else if (line.startsWith(' ')) {
      type = Type.context;
    } else {
      return null;
    }

    return {
      type,
      content: line.substring(1),
      diffLineNumber: 0,
      oldFileLineNumber: 0,
      newFileLineNumber: 0,
    };
  }

  /** Wrap the given line as-is with type unknown. */
  public static parseAsUnknown(line: string): Unknown {
    return { diffLineNumber: 0, type: Type.unknown, content: line };
  }

  /** Regular expression for matching backslash escape sequences. */
  private static readonly _escapeSequencePattern = /\\(.)/g;

  /** Regular expression for extracting parts from a hunk descriptor line. */
  private static readonly _hunkDescriptorPattern =
    /@@\s*(-|\+)(\d+)(?:,(\d+)?)?\s*(-|\+)(\d+)(?:,(\d+)?)?\s*@@/;

  /** Line number of the most recently parsed line (1-based). */
  private _diffLineNumber: number = 0;

  /** Most recent functional (non-unknown) line parsed. */
  private _previousFunctionalLine: UnifiedDiffLine | null = null;

  /** Most recent old file header. */
  private _oldFileHeader: OldFileHeader | null = null;

  /** Most recent new file header. */
  private _newFileHeader: NewFileHeader | null = null;

  /** Most recent hunk descriptor. */
  private _hunkDescriptor: HunkDescriptor | null = null;

  /** Number of additions in current hunk. */
  private _numAdditions: number = 0;

  /** Number of deletions in current hunk. */
  private _numDeletions: number = 0;

  /** Number of context lines in current hunk. */
  private _numContext: number = 0;
}

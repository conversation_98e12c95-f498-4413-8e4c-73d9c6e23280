import DiffMap from './DiffMap';
import UnifiedDiffLine, {
  Type,
  OldFileHeader,
  NewFileHeader,
  Addition,
  Deletion,
} from './UnifiedDiffLine';
import UnifiedDiffParser from './UnifiedDiffParser';

/**
 * Builds optimized diff maps from Unified Diff files for efficient array patching.
 * Supports multiple diffs per file, streaming processing, and error recovery.
 */
export default class UnifiedDiffMapBuilder {
  /**
   * Builds diff maps from a Unified Diff file string.
   * @param unifiedDiff The complete diff file contents
   * @returns Generator yielding one DiffMap per diff
   */
  public static buildFromFile(unifiedDiff: string): Generator<DiffMap> {
    return UnifiedDiffMapBuilder.buildFromLines(UnifiedDiffParser.parseFileLineByLine(unifiedDiff));
  }

  /**
   * Builds diff maps from parsed diff lines.
   * @param lines Iterable of UnifiedDiffLine objects
   * @returns Generator yielding one DiffMap per diff
   */
  public static *buildFromLines(lines: Iterable<UnifiedDiffLine>): Generator<DiffMap> {
    const builder = new UnifiedDiffMapBuilder();

    for (const line of lines) {
      const diffMap = builder._processLine(line);
      if (diffMap !== null) {
        yield diffMap;
      }
    }

    // Ensure the final diff map is properly finalized and yielded
    const finalDiffMap = builder.finalise();
    if (finalDiffMap !== null) {
      yield finalDiffMap;
    }
  }

  /** Processes a line with error handling and context. */
  private _processLine(line: UnifiedDiffLine): DiffMap | null {
    try {
      return this.handleLine(line);
    } catch (error: any) {
      const context = {
        lineType: line.type,
        lineNumber: line.diffLineNumber,
        content: 'content' in line ? line.content : 'N/A',
        currentDiffPath: this._diffMap?.oldFilePath ?? 'unknown',
      };

      console.error(
        'UnifiedDiffMapBuilder - Error processing line:',
        error.message,
        '\nContext:',
        context,
      );
      throw new Error(
        `Failed to process diff line ${line.diffLineNumber}: ${String(error.message)}`,
      );
    }
  }

  // -----------------------------------------------------------------------------------------------
  // Low-level building operations.

  /**
   * Process a line from a Unified Diff file, using it to start or continue building a diff map.
   * This should be called repeatedly, once for each line in the file, starting at the first line.
   * A Unified Diff file can contain multiple separate diffs, each of which will correspond to one
   *  diff map. If the specified line starts a new diff, then this function will finish building the
   *  previous diff map (if applicable), and start building a new one. The map that was just
   *  finished will be returned.
   *
   * @param line The parse Unified Diff line which is to be processed.
   * @returns If the specified line starts a new diff, and another diff map was previously being
   *  built, then this returns the diff map which has just finished. Otherwise, this returns null.
   *  It's important to store the result if a diff map is returned as it won't be returned again.
   *
   * @note After passing all lines to handleLine(), it is important to call "finalise()" and store
   *  the result. This ensures that the last diff map is finished.
   */
  public handleLine(line: UnifiedDiffLine): DiffMap | null {
    switch (line.type) {
      case Type.oldFileHeader:
        return this._handleOldFileHeader(line);

      case Type.newFileHeader:
        return this._handleNewFileHeader(line);

      case Type.addition:
        this._handleAddition(line);
        return null;

      case Type.deletion:
        this._handleDeletion(line);
        return null;

      default:
        // Ignore other line types (context, hunk descriptors, etc.)
        return null;
    }
  }

  /** Handles old file header lines, starting a new diff. */
  private _handleOldFileHeader(line: OldFileHeader): DiffMap | null {
    // Finalize any existing diff map
    const finishedDiffMap = this._diffMap;
    finishedDiffMap?.finalise();

    // Start a new diff map
    this._diffMap = new DiffMap();
    this._diffMap.oldFilePath = line.path;

    return finishedDiffMap;
  }

  /** Handles new file header lines, may start a new diff or continue current one. */
  private _handleNewFileHeader(line: NewFileHeader): DiffMap | null {
    let finishedDiffMap: DiffMap | null = null;

    if (this._diffMap === null) {
      // No current diff - start a new one
      this._diffMap = new DiffMap();
    } else if (this._shouldStartNewDiff()) {
      // Current diff is complete - finalize it and start new one
      finishedDiffMap = this._diffMap;
      finishedDiffMap.finalise();
      this._diffMap = new DiffMap();
    }

    // Set the new file path for the current diff
    this._diffMap.newFilePath = line.path;
    return finishedDiffMap;
  }

  /** Determines if a new file header should start a new diff. */
  private _shouldStartNewDiff(): boolean {
    return (
      this._diffMap !== null && (this._diffMap.newFilePath !== '' || this._diffMap.hasChanges())
    );
  }

  /** Handles addition lines by adding them to the current diff map. */
  private _handleAddition(line: Addition): void {
    if (this._diffMap === null) {
      throw new Error('Cannot process addition line without an active diff map');
    }

    this._diffMap.handleAddition(
      this._convertToZeroBasedIndex(line.oldFileLineNumber),
      this._convertToZeroBasedIndex(line.newFileLineNumber),
      line.content,
    );
  }

  /** Handles deletion lines by adding them to the current diff map. */
  private _handleDeletion(line: Deletion): void {
    if (this._diffMap === null) {
      throw new Error('Cannot process deletion line without an active diff map');
    }

    this._diffMap.handleDeletion(this._convertToZeroBasedIndex(line.oldFileLineNumber));
  }

  /** Converts 1-based line numbers to 0-based array indexes. */
  private _convertToZeroBasedIndex(lineNumber: number): number {
    return lineNumber - 1;
  }

  /**
   * Finalizes and returns the current diff map.
   * Must be called after processing all lines to get the final diff.
   * @returns The completed diff map or null if none was being built
   */
  public finalise(): DiffMap | null {
    if (this._diffMap === null) {
      return null;
    }

    try {
      this._diffMap.finalise();
      const completedDiffMap = this._diffMap;
      this._diffMap = null;
      return completedDiffMap;
    } catch (error: any) {
      // Clean up on error
      this._diffMap = null;
      throw new Error(`Failed to finalize diff map: ${String(error.message)}`);
    }
  }

  /** Gets the current builder state for debugging. */
  public getState(): { isBuilding: boolean; currentDiffPath?: string; hasChanges?: boolean } {
    return {
      isBuilding: this._diffMap !== null,
      currentDiffPath: this._diffMap?.oldFilePath,
      hasChanges: this._diffMap?.hasChanges(),
    };
  }

  /** The diff map currently being built, or null if no diff is in progress. */
  private _diffMap: DiffMap | null = null;
}

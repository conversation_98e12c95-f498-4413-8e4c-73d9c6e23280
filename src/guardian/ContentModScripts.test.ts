/**
 * @jest-environment jsdom
 */

// Import the utility functions
import {
  isProtectedElement,
  jsBodyMods,
  safelyRemoveElement,
  youtubeProtectedSelectors,
} from './ContentModScripts';

// Mock fetch API for tests
/* eslint-disable @typescript-eslint/consistent-type-assertions */
global.fetch = jest.fn().mockImplementation(
  async () =>
    await Promise.resolve({
      ok: true,
      status: 200,
      json: async () => ({}),
      text: async () => '',
      headers: new Headers(),
      redirected: false,
      statusText: 'OK',
      type: 'basic',
      url: '',
      clone: () => new Response(),
      body: null,
      bodyUsed: false,
      arrayBuffer: async () => new ArrayBuffer(0),
      blob: async () => new Blob(),
      formData: async () => new FormData(),
    } as Response),
);

interface MockSetTimeout extends jest.Mock {
  clickListenerCallback: (() => void) | null;
  reloadCallback: (() => void) | null;
}

const mockSetTimeout = jest.fn((callback, delay) => {
  if (delay === 2000) {
    mockSetTimeout.clickListenerCallback = callback;
  } else if (delay === 500) {
    mockSetTimeout.reloadCallback = callback;
  }
}) as MockSetTimeout;

mockSetTimeout.clickListenerCallback = null;
mockSetTimeout.reloadCallback = null;

// Mock document.addEventListener to capture the click handler
let capturedClickHandler: ((event: MouseEvent) => void) | null = null;

describe('YouTube SafetyMode cookie fix', () => {
  beforeEach(() => {
    Object.defineProperty(window, 'location', {
      value: {
        hostname: 'www.youtube.com',
        reload: jest.fn(),
      },
      writable: true,
    });

    // Mock document.cookie
    Object.defineProperty(document, 'cookie', {
      value: '',
      writable: true,
    });

    // Mock document.addEventListener
    document.addEventListener = jest.fn((event, handler) => {
      if (event === 'click') {
        /* eslint-disable @typescript-eslint/consistent-type-assertions */
        capturedClickHandler = handler as (event: MouseEvent) => void;
        /* eslint-enable @typescript-eslint/consistent-type-assertions */
      }
    });

    // Mock document.removeEventListener
    document.removeEventListener = jest.fn();

    // Mock document.visibilityState
    let visibilityState = 'visible';
    Object.defineProperty(document, 'visibilityState', {
      get: () => visibilityState,
      set: (value) => {
        visibilityState = value;
      },
      configurable: true,
    });

    // Mock window.parent
    Object.defineProperty(window, 'parent', {
      value: window,
      writable: false,
    });

    // Mock setTimeout
    global.setTimeout = mockSetTimeout as any;

    // Reset captured handlers
    capturedClickHandler = null;
    mockSetTimeout.clickListenerCallback = null;
    mockSetTimeout.reloadCallback = null;
  });

  describe('bakeYoutubeSafetyModeCookie', () => {
    it('should return [null] when not on YouTube', () => {
      window.location.hostname = 'example.com';

      const result = jsBodyMods.bakeYoutubeSafetyModeCookie();

      expect(result).toEqual([null]);
      expect(document.cookie).not.toContain('PREF=f2=8000000');
    });

    it('should set cookie but not immediately reload when on YouTube and cookie not set', () => {
      window.location.hostname = 'www.youtube.com';
      document.cookie = '';

      const result = jsBodyMods.bakeYoutubeSafetyModeCookie();

      expect(result).toEqual([true]);
      expect(document.cookie).toBe('PREF=f2=8000000;domain=.youtube.com');
      expect(window.location.reload).not.toHaveBeenCalled();
      expect(mockSetTimeout).toHaveBeenCalledWith(expect.any(Function), 2000);
    });

    it('should not set cookie or reload when cookie is already set', () => {
      window.location.hostname = 'www.youtube.com';
      document.cookie = 'PREF=f2=8000000';

      const result = jsBodyMods.bakeYoutubeSafetyModeCookie();

      expect(result).toEqual([false]);
      expect(document.cookie).toBe('PREF=f2=8000000');
      expect(window.location.reload).not.toHaveBeenCalled();
    });

    it('should add click event listener after delay', () => {
      window.location.hostname = 'www.youtube.com';
      document.cookie = '';

      jsBodyMods.bakeYoutubeSafetyModeCookie();

      // Simulate the timeout callback for adding the event listener
      const clickCallback = mockSetTimeout.clickListenerCallback;
      if (clickCallback != null) {
        clickCallback();
      }

      expect(document.addEventListener).toHaveBeenCalledWith('click', expect.any(Function), true);
    });

    it('should reload page after user interaction when page is visible', () => {
      window.location.hostname = 'www.youtube.com';
      document.cookie = '';

      jsBodyMods.bakeYoutubeSafetyModeCookie();

      // Simulate the timeout callback for adding the event listener
      const clickCallback = mockSetTimeout.clickListenerCallback;
      if (clickCallback != null) {
        clickCallback();
      }

      // Simulate a click by calling the captured click handler with a mock event
      if (capturedClickHandler != null) {
        // Create a mock target element with the closest method
        const mockTarget = document.createElement('div');
        // Create a mock event with the target
        const mockEvent = new MouseEvent('click');
        Object.defineProperty(mockEvent, 'target', {
          value: mockTarget,
          writable: false,
          configurable: true,
        });
        capturedClickHandler(mockEvent);
      }

      // Simulate the timeout callback after click
      const reloadCallback = mockSetTimeout.reloadCallback;
      if (reloadCallback != null) {
        reloadCallback();
      }

      expect(document.removeEventListener).toHaveBeenCalled();
      expect(window.location.reload).toHaveBeenCalled();
    });

    it('should not reload if page is no longer visible after user interaction', () => {
      window.location.hostname = 'www.youtube.com';
      document.cookie = '';

      jsBodyMods.bakeYoutubeSafetyModeCookie();

      // Simulate the timeout callback for adding the event listener
      const clickCallback = mockSetTimeout.clickListenerCallback;
      if (clickCallback != null) {
        clickCallback();
      }

      // Simulate a click by calling the captured click handler with a mock event
      if (capturedClickHandler != null) {
        // Create a mock target element with the closest method
        const mockTarget = document.createElement('div');
        // Create a mock event with the target
        const mockEvent = new MouseEvent('click');
        Object.defineProperty(mockEvent, 'target', {
          value: mockTarget,
          writable: false,
          configurable: true,
        });
        capturedClickHandler(mockEvent);
      }

      // Change visibility state before reload callback
      Object.defineProperty(document, 'visibilityState', {
        get: () => 'hidden',
        configurable: true,
      });

      // Simulate the timeout callback after click
      const reloadCallback = mockSetTimeout.reloadCallback;
      if (reloadCallback != null) {
        reloadCallback();
      }

      expect(window.location.reload).not.toHaveBeenCalled();
    });

    it('should not reload when interacting with Restricted Mode dialog', () => {
      window.location.hostname = 'www.youtube.com';
      document.cookie = '';

      jsBodyMods.bakeYoutubeSafetyModeCookie();

      // Simulate the timeout callback for adding the event listener
      const clickCallback = mockSetTimeout.clickListenerCallback;
      if (clickCallback != null) {
        clickCallback();
      }

      // Create a mock Restricted Mode dialog element
      const mockDialog = document.createElement('div');
      mockDialog.setAttribute('role', 'dialog');
      mockDialog.setAttribute('aria-label', 'Restricted Mode');
      document.body.appendChild(mockDialog);

      // Create a mock target inside the dialog
      const mockTarget = document.createElement('button');
      mockDialog.appendChild(mockTarget);

      // Create a mock event with the target
      const mockEvent = new MouseEvent('click');
      Object.defineProperty(mockEvent, 'target', {
        value: mockTarget,
        writable: false,
        configurable: true,
      });

      // Call the event handler directly with our mock event
      if (capturedClickHandler != null) {
        capturedClickHandler(mockEvent);
      }

      // The reload callback should not be scheduled
      expect(mockSetTimeout).not.toHaveBeenCalledWith(expect.any(Function), 500);
      expect(document.removeEventListener).not.toHaveBeenCalled();
    });

    it('should not reload when clicking on Restricted Mode menu item', () => {
      window.location.hostname = 'www.youtube.com';
      document.cookie = '';

      jsBodyMods.bakeYoutubeSafetyModeCookie();

      // Simulate the timeout callback for adding the event listener
      const clickCallback = mockSetTimeout.clickListenerCallback;
      if (clickCallback != null) {
        clickCallback();
      }

      // Create a mock Restricted Mode menu item
      const mockMenuItem = document.createElement('a');
      mockMenuItem.setAttribute('href', '#');
      mockMenuItem.setAttribute('role', 'menuitem');
      mockMenuItem.setAttribute('aria-label', 'Restricted Mode: Off');
      document.body.appendChild(mockMenuItem);

      // Create a mock event with the menu item as target
      const mockEvent = new MouseEvent('click');
      Object.defineProperty(mockEvent, 'target', {
        value: mockMenuItem,
        writable: false,
        configurable: true,
      });

      // Call the event handler directly with our mock event
      if (capturedClickHandler != null) {
        capturedClickHandler(mockEvent);
      }

      // The reload callback should not be scheduled
      expect(mockSetTimeout).not.toHaveBeenCalledWith(expect.any(Function), 500);
      expect(document.removeEventListener).not.toHaveBeenCalled();
    });
  });
});

describe('YouTube UI Protection Utilities', () => {
  let consoleDebugSpy: jest.SpyInstance;

  beforeEach(() => {
    // Mock console.debug
    consoleDebugSpy = jest.spyOn(console, 'debug').mockImplementation();

    // Set up document body
    document.body.innerHTML = '';
  });

  afterEach(() => {
    consoleDebugSpy.mockRestore();
  });

  describe('isProtectedElement', () => {
    it('should identify elements that match protected selectors', () => {
      // Create a dialog element with the Restricted Mode label
      const dialog = document.createElement('div');
      dialog.setAttribute('role', 'dialog');
      dialog.setAttribute('aria-label', 'Restricted Mode');
      document.body.appendChild(dialog);

      expect(isProtectedElement(dialog)).toBe(true);
    });

    it('should identify elements within protected elements', () => {
      // Create a dialog element with the Restricted Mode label
      const dialog = document.createElement('div');
      dialog.setAttribute('role', 'dialog');
      dialog.setAttribute('aria-label', 'Restricted Mode');

      // Create a button inside the dialog
      const button = document.createElement('button');
      dialog.appendChild(button);

      document.body.appendChild(dialog);

      expect(isProtectedElement(button)).toBe(true);
    });

    it('should identify menu items as protected', () => {
      // Create a menu item
      const menuItem = document.createElement('a');
      menuItem.setAttribute('href', '#');
      menuItem.setAttribute('role', 'menuitem');
      document.body.appendChild(menuItem);

      expect(isProtectedElement(menuItem)).toBe(true);
    });

    it('should return false for non-protected elements', () => {
      // Create a regular div
      const div = document.createElement('div');
      document.body.appendChild(div);

      expect(isProtectedElement(div)).toBe(false);
    });
  });

  describe('safelyRemoveElement', () => {
    it('should remove non-protected elements', () => {
      // Create a regular div
      const div = document.createElement('div');
      document.body.appendChild(div);

      const result = safelyRemoveElement(div);

      expect(result).toBe(true);
      expect(document.body.contains(div)).toBe(false);
    });

    it('should not remove protected elements', () => {
      // Create a dialog element with the Restricted Mode label
      const dialog = document.createElement('div');
      dialog.setAttribute('role', 'dialog');
      dialog.setAttribute('aria-label', 'Restricted Mode');
      document.body.appendChild(dialog);

      const result = safelyRemoveElement(dialog);

      expect(result).toBe(false);
      expect(document.body.contains(dialog)).toBe(true);
      expect(consoleDebugSpy).toHaveBeenCalled();
    });

    it('should not remove elements within protected elements', () => {
      // Create a dialog element with the Restricted Mode label
      const dialog = document.createElement('div');
      dialog.setAttribute('role', 'dialog');
      dialog.setAttribute('aria-label', 'Restricted Mode');

      // Create a button inside the dialog
      const button = document.createElement('button');
      dialog.appendChild(button);

      document.body.appendChild(dialog);

      const result = safelyRemoveElement(button);

      expect(result).toBe(false);
      expect(dialog.contains(button)).toBe(true);
      expect(consoleDebugSpy).toHaveBeenCalled();
    });

    it('should not log debug messages when debug is false', () => {
      // Create a dialog element with the Restricted Mode label
      const dialog = document.createElement('div');
      dialog.setAttribute('role', 'dialog');
      dialog.setAttribute('aria-label', 'Restricted Mode');
      document.body.appendChild(dialog);

      safelyRemoveElement(dialog, false);

      expect(consoleDebugSpy).not.toHaveBeenCalled();
    });
  });

  describe('youtubeProtectedSelectors', () => {
    it('should include selectors for Restricted Mode dialog', () => {
      expect(youtubeProtectedSelectors).toContain('[role="dialog"][aria-label*="Restricted Mode"]');
    });

    it('should include selectors for menu items', () => {
      expect(youtubeProtectedSelectors).toContain('a[href="#"][role="menuitem"]');
    });
  });

  describe('findAndRemoveBySelector', () => {
    // We need to import the function for testing
    // Since it's not exported, we'll test it through the exported functions that use it

    it('should not remove protected elements', () => {
      // Create a protected element
      const protectedElement = document.createElement('div');
      protectedElement.setAttribute('role', 'dialog');
      protectedElement.setAttribute('aria-label', 'Restricted Mode');
      document.body.appendChild(protectedElement);

      // Create a non-protected element
      const nonProtectedElement = document.createElement('div');
      nonProtectedElement.setAttribute('id', 'test-element');
      document.body.appendChild(nonProtectedElement);

      // Call a function that uses findAndRemoveBySelector
      jsBodyMods.removeYoutubeSidebar();

      // The protected element should still be in the document
      expect(document.body.contains(protectedElement)).toBe(true);

      // Clean up
      if (document.body.contains(protectedElement)) {
        document.body.removeChild(protectedElement);
      }
      if (document.body.contains(nonProtectedElement)) {
        document.body.removeChild(nonProtectedElement);
      }
    });
  });

  describe('Content Mods', () => {
    // Mock console.debug
    let consoleDebugSpy: jest.SpyInstance;

    beforeEach(() => {
      consoleDebugSpy = jest.spyOn(console, 'debug').mockImplementation();
      document.body.innerHTML = '';

      // Set up window.location for YouTube
      Object.defineProperty(window, 'location', {
        value: {
          hostname: 'www.youtube.com',
        },
        writable: true,
      });
    });

    afterEach(() => {
      consoleDebugSpy.mockRestore();
    });

    describe('removeYoutubeSidebar', () => {
      it('should not remove protected elements', () => {
        // Create a protected element that matches one of the selectors
        const protectedElement = document.createElement('div');
        protectedElement.setAttribute('id', 'related');
        protectedElement.setAttribute('role', 'dialog');
        document.body.appendChild(protectedElement);

        // Call the function
        jsBodyMods.removeYoutubeSidebar();

        // The protected element should still be in the document
        expect(document.body.contains(protectedElement)).toBe(true);
      });

      it('should respect protected elements when handling autoplay toggle', () => {
        // Create a protected autoplay toggle
        const autoplayToggle = document.createElement('button');
        autoplayToggle.classList.add('ytp-autonav-toggle-button');
        autoplayToggle.setAttribute('aria-checked', 'true');

        // Make it a protected element
        const protectedDialog = document.createElement('div');
        protectedDialog.setAttribute('role', 'dialog');
        protectedDialog.appendChild(autoplayToggle);
        document.body.appendChild(protectedDialog);

        // Mock querySelector to return our toggle
        document.querySelector = jest.fn().mockReturnValue(autoplayToggle);

        // Create a spy for the click method
        const clickSpy = jest.spyOn(autoplayToggle, 'click');

        // Call the function
        jsBodyMods.removeYoutubeSidebar();

        // The click method should not have been called because it's in a protected element
        expect(clickSpy).not.toHaveBeenCalled();
      });
    });

    describe('disableYoutubeMiniPlayer', () => {
      it('should not remove protected mini players', () => {
        // Create a protected mini player
        const miniPlayer = document.createElement('ytd-miniplayer');

        // Make it a protected element
        const protectedDialog = document.createElement('div');
        protectedDialog.setAttribute('role', 'dialog');
        protectedDialog.appendChild(miniPlayer);
        document.body.appendChild(protectedDialog);

        // Mock getElementsByTagName to return our mini player
        document.getElementsByTagName = jest.fn().mockReturnValue([miniPlayer]);

        // Call the function
        jsBodyMods.disableYoutubeMiniPlayer();

        // The mini player should still be in the document
        expect(protectedDialog.contains(miniPlayer)).toBe(true);
      });
    });

    describe('removeYoutubeComments', () => {
      it('should not remove protected comment sections', () => {
        // Create a protected comments section
        const comments = document.createElement('div');
        comments.setAttribute('id', 'comments');

        // Make it a protected element
        const protectedDialog = document.createElement('div');
        protectedDialog.setAttribute('role', 'dialog');
        protectedDialog.appendChild(comments);
        document.body.appendChild(protectedDialog);

        // Call the function
        jsBodyMods.removeYoutubeComments();

        // The comments section should still be in the document
        expect(protectedDialog.contains(comments)).toBe(true);
      });
    });

    describe('removeYoutubeAds', () => {
      it('should not modify videos in protected elements', () => {
        // Create a video element in a protected container
        const video = document.createElement('video');
        video.classList.add('video-stream', 'html5-main-video');

        // Make it a protected element
        const protectedDialog = document.createElement('div');
        protectedDialog.setAttribute('role', 'dialog');
        protectedDialog.appendChild(video);
        document.body.appendChild(protectedDialog);

        // Mock getElementsByClassName to return our video
        document.getElementsByClassName = jest.fn().mockReturnValue([video]);

        // Call the function
        jsBodyMods.removeYoutubeAds();

        // The video should not have been modified (no sw-cmod-controlled attribute)
        expect(video.getAttribute('sw-cmod-controlled')).toBeNull();
      });
    });

    describe('removeYoutubeVideosFromSearch', () => {
      beforeEach(() => {
        // Set up window.location for Google
        Object.defineProperty(window, 'location', {
          value: {
            hostname: 'www.google.com',
          },
          writable: true,
        });
      });

      it('should not remove protected search results', () => {
        // Create a search result with a YouTube link
        const link = document.createElement('a');
        link.setAttribute('href', 'https://www.youtube.com/watch?v=12345');

        // Create a search result container
        const searchResult = document.createElement('div');
        searchResult.classList.add('g');
        searchResult.appendChild(link);

        // Make it a protected element
        const protectedDialog = document.createElement('div');
        protectedDialog.setAttribute('role', 'dialog');
        protectedDialog.appendChild(searchResult);
        document.body.appendChild(protectedDialog);

        // Mock querySelectorAll to return our link
        document.querySelectorAll = jest.fn().mockImplementation((selector) => {
          if (selector.includes('youtube.com/watch') === true) {
            return [link];
          }
          return [];
        });

        // Mock document.evaluate for XPath
        document.evaluate = jest.fn().mockReturnValue({
          snapshotLength: 0,
          snapshotItem: jest.fn(),
        });

        // Call the function
        jsBodyMods.removeYoutubeVideosFromSearch();

        // The search result should still be in the document
        expect(protectedDialog.contains(searchResult)).toBe(true);
      });
    });
  });
});

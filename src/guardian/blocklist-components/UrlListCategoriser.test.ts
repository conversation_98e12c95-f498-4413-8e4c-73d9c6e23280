import TenantId from 'models/TenantId';
import { getTestPolicyConfig } from 'test-helpers/test-policy-config';
import CategorisationMap from '../utilities/CategorisationMap';
import UrlListCategoriser from './UrlListCategoriser';

// Note: The test data must be properly sorted.

// TLDs must be omitted from this data:
const testDotCom = new CategorisationMap([
  // Simple domain:
  '0elpmaxe.|0',
  '1elpmaxe.|11,12,13',

  // Query strings:
  '1elpmaxe./?search=blah|14,15,16',
  '1elpmaxe./?search=foo|17,18,19',

  // Subdomains:
  '2elpmaxe.elibom.|21,22',
  '2elpmaxe.elibom./?search=blah|23',
  '2elpmaxe.elibom.tset.|24,25',
  '2elpmaxe.elibom.tset./?search=blah|26',
  '2elpmaxe.raboof.|27,28',
  '2elpmaxe.raboof.tset.|29',

  // Paths:
  '3elpmaxe./foo|31,32,33',
  '3elpmaxe./foo/bar|34,35,36',
  '3elpmaxe./foo/bar?search=blah|37',
  '3elpmaxe./foo?search=blah|38',
  '3elpmaxe./test|39',

  // Subdomains and paths:
  '4elpmaxe.elibom./foo|40,41',
  '4elpmaxe.elibom./foo/bar|42,43',
  '4elpmaxe.elibom./foo/bar?search=blah|44',
  '4elpmaxe.elibom.tset./foo|45,46',
  '4elpmaxe.elibom.tset./foo/bar|47,48',
  '4elpmaxe.elibom.tset./foo/bar?search=blah|49',
]);

// TLDs must be omitted from this data:
const testDotOrg = new CategorisationMap([
  '5elpmaxe.|51,52,53',
  '5elpmaxe.elibom./?search=blah|54,55,56',
  '5elpmaxe.tset./hello/world|57,58,59',
]);

// TLDs must be included in this data:
const testOther = new CategorisationMap([
  // example6.online
  'enilno.6elpmaxe.|61,62,63',
  'enilno.6elpmaxe./?search=blah|64,65,66',

  // example7.info
  'ofni.7elpmaxe.|71,72,73',
  'ofni.7elpmaxe./foo/bar|74,75,76',
  'ofni.7elpmaxe.tset.|77,78,79',

  // example8.biz
  'zib.8elpmaxe./foo/bar|81,82,83',
  'zib.8elpmaxe.elibom.|84,85,86',
  'zib.8elpmaxe.elibom.tset./foo/bar|87,88,89',
]);

const tenant = new TenantId('f417a2c4-f99c-11ea-8caa-eb014c4bbe3b');
const policyJson = getTestPolicyConfig(tenant);

describe('UrlListCategoriser', () => {
  describe('getCategorisationMap()', () => {
    it('returns the categorisation map previously set for the specified top-level domain', () => {
      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      const map = categoriser.getCategorisationMap('com');
      expect(map).toBeDefined();
      expect(map?.contains('3elpmaxe./foo')).toBeTrue();
    });

    it('returns undefined if there is no categorisation map for the specified top-level domain', () => {
      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      const map = categoriser.getCategorisationMap('org');
      expect(map).toBeUndefined();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('setCategorisationMap()', () => {
    // Note: Most aspects of this function are thoroughly tested under categoriseUrl().

    it('replaces any existing blocklist data for the same tld', () => {
      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(new CategorisationMap(), 'com');

      // The .com categorisation should no longer work:
      expect(categoriser.categoriseUrl(new URL('http://example1.com')).categoryIds).toEqual(
        new Set([]),
      );
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('length()', () => {
    it('returns zero if no blocklist data has been loaded yet', () => {
      const categoriser = new UrlListCategoriser();
      expect(categoriser.length).toEqual(0);
    });

    it('returns the total number of URLs in all currently loaded blocklists', () => {
      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(testDotOrg, 'org');
      categoriser.setCategorisationMap(testOther);
      expect(categoriser.length).toEqual(32);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('loadFromBlocklistFile()', () => {
    it('loads generic blocklist data from the specified JSON file contents', () => {
      const testBlocklistFile = `[
        "enilno.elpmaxe.|1,2,3",
        "ti.elpmaxe.tset.|4,5,6",
        "zib.elpmaxe.elibom./foo/bar|7,8,9"
      ]`;

      const categoriser = new UrlListCategoriser();
      categoriser.loadFromBlocklistFile(testBlocklistFile);

      expect(categoriser.categoriseUrl(new URL('http://example.online')).categoryIds).toEqual(
        new Set(['1', '2', '3']),
      );
      expect(categoriser.categoriseUrl(new URL('http://test.example.it')).categoryIds).toEqual(
        new Set(['4', '5', '6']),
      );
      expect(
        categoriser.categoriseUrl(new URL('http://mobile.example.biz/foo/bar')).categoryIds,
      ).toEqual(new Set(['7', '8', '9']));
    });

    it('loads tld-specific blocklist data from the specified JSON file contents', () => {
      const testBlocklistFile = `[
        "elpmaxe.|1,2,3",
        "elpmaxe.elibom./foo/bar|7,8,9",
        "elpmaxe.tset.|4,5,6"
      ]`;

      const categoriser = new UrlListCategoriser();
      categoriser.loadFromBlocklistFile(testBlocklistFile, 'com');

      expect(categoriser.categoriseUrl(new URL('http://example.com')).categoryIds).toEqual(
        new Set(['1', '2', '3']),
      );
      expect(categoriser.categoriseUrl(new URL('http://test.example.com')).categoryIds).toEqual(
        new Set(['1', '2', '3', '4', '5', '6']),
      );
      expect(
        categoriser.categoriseUrl(new URL('http://mobile.example.com/foo/bar')).categoryIds,
      ).toEqual(new Set(['1', '2', '3', '7', '8', '9']));
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('clear()', () => {
    it('removes all stored blocklist data', () => {
      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(testDotOrg, 'org');
      categoriser.setCategorisationMap(testOther);

      categoriser.clear();
      expect(categoriser.length).toEqual(0);
      expect(categoriser.categoriseUrl(new URL('http://example1.com')).categoryIds).toEqual(
        new Set([]),
      );
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('categoriseUrl()', () => {
    it('matches simple domains which are explicitly specified in the "other" blocklist', () => {
      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testOther);

      expect(categoriser.categoriseUrl(new URL('http://example6.online')).categoryIds).toEqual(
        new Set(['61', '62', '63']),
      );

      expect(categoriser.categoriseUrl(new URL('http://example7.info')).categoryIds).toEqual(
        new Set(['71', '72', '73']),
      );
    });

    it('matches simple domains which are explicitly specified in a tld-specific blocklist', () => {
      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');

      expect(categoriser.categoriseUrl(new URL('http://example0.com')).categoryIds).toEqual(
        new Set(['0']),
      );

      expect(categoriser.categoriseUrl(new URL('http://example1.com')).categoryIds).toEqual(
        new Set(['11', '12', '13']),
      );
    });

    it('matches simple domains when blocklists for multiple TLDs are loaded', () => {
      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(testDotOrg, 'org');
      categoriser.setCategorisationMap(testOther);

      expect(categoriser.categoriseUrl(new URL('http://example1.com')).categoryIds).toEqual(
        new Set(['11', '12', '13']),
      );

      expect(categoriser.categoriseUrl(new URL('http://example5.org')).categoryIds).toEqual(
        new Set(['51', '52', '53']),
      );

      expect(categoriser.categoriseUrl(new URL('http://example6.online')).categoryIds).toEqual(
        new Set(['61', '62', '63']),
      );
    });

    it('matches subdomains which are specified in the blocklist', () => {
      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(testDotOrg, 'org');
      categoriser.setCategorisationMap(testOther);

      expect(categoriser.categoriseUrl(new URL('http://mobile.example2.com')).categoryIds).toEqual(
        new Set(['21', '22']),
      );

      expect(
        categoriser.categoriseUrl(new URL('http://test.mobile.example2.com')).categoryIds,
      ).toEqual(new Set(['21', '22', '24', '25']));

      expect(categoriser.categoriseUrl(new URL('http://foobar.example2.com')).categoryIds).toEqual(
        new Set(['27', '28']),
      );

      expect(categoriser.categoriseUrl(new URL('http://mobile.example8.biz')).categoryIds).toEqual(
        new Set(['84', '85', '86']),
      );
    });

    it('matches domain levels higher up the hierarchy', () => {
      // E.g. if the blocklist contains "example.com" then we should also match "foo.example.com".

      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(testDotOrg, 'org');
      categoriser.setCategorisationMap(testOther);

      expect(
        categoriser.categoriseUrl(new URL('http://xyz.mobile.example2.com')).categoryIds,
      ).toEqual(new Set(['21', '22']));

      expect(
        categoriser.categoriseUrl(new URL('http://xyz.test.mobile.example2.com')).categoryIds,
      ).toEqual(new Set(['21', '22', '24', '25']));

      expect(
        categoriser.categoriseUrl(new URL('http://xyz.test.foobar.example2.com')).categoryIds,
      ).toEqual(new Set(['27', '28', '29']));

      expect(
        categoriser.categoriseUrl(new URL('http://xyz.test.example7.info')).categoryIds,
      ).toEqual(new Set(['71', '72', '73', '77', '78', '79']));
    });

    it('does not match domain levels lower down the hierarchy', () => {
      // E.g. if the blocklist only contains "foo.example.com" then it should not match
      //  "example.com".

      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');

      expect(categoriser.categoriseUrl(new URL('http://example2.com')).categoryIds).toEqual(
        new Set(),
      );
    });

    it('matches paths which are specified in the blocklist', () => {
      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(testDotOrg, 'org');
      categoriser.setCategorisationMap(testOther);

      expect(categoriser.categoriseUrl(new URL('http://example3.com/foo')).categoryIds).toEqual(
        new Set(['31', '32', '33']),
      );

      expect(categoriser.categoriseUrl(new URL('http://example8.biz/foo/bar')).categoryIds).toEqual(
        new Set(['81', '82', '83']),
      );
    });

    it('matches path levels higher up the hierarchy', () => {
      // E.g. if the blocklist contains "example.com" then it should also match "example.com/foo".

      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(testDotOrg, 'org');
      categoriser.setCategorisationMap(testOther);

      expect(
        categoriser.categoriseUrl(new URL('http://mobile.example2.com/xyz')).categoryIds,
      ).toEqual(new Set(['21', '22']));

      expect(categoriser.categoriseUrl(new URL('http://example3.com/foo/xyz')).categoryIds).toEqual(
        new Set(['31', '32', '33']),
      );

      expect(
        categoriser.categoriseUrl(new URL('http://example3.com/foo/bar/xyz')).categoryIds,
      ).toEqual(new Set(['31', '32', '33', '34', '35', '36']));

      expect(
        categoriser.categoriseUrl(new URL('http://example3.com/test/xyz')).categoryIds,
      ).toEqual(new Set(['39']));

      expect(
        categoriser.categoriseUrl(new URL('http://test.example5.org/hello/world/xyz')).categoryIds,
      ).toEqual(new Set(['51', '52', '53', '57', '58', '59']));

      expect(
        categoriser.categoriseUrl(new URL('http://example7.info/foo/bar/xyz')).categoryIds,
      ).toEqual(new Set(['71', '72', '73', '74', '75', '76']));
    });

    it('does not match path levels lower down the hierarchy', () => {
      // E.g. if the blocklist only contains "example.com/foo" then it should not match
      //  "example.com".

      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');

      expect(categoriser.categoriseUrl(new URL('http://example3.com')).categoryIds).toEqual(
        new Set(),
      );
    });

    it('matches query strings which are specified in the blocklist', () => {
      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(testDotOrg, 'org');
      categoriser.setCategorisationMap(testOther);

      expect(
        categoriser.categoriseUrl(new URL('http://example1.com?search=blah')).categoryIds,
      ).toEqual(new Set(['11', '12', '13', '14', '15', '16']));

      expect(
        categoriser.categoriseUrl(new URL('http://example1.com?search=foo')).categoryIds,
      ).toEqual(new Set(['11', '12', '13', '17', '18', '19']));

      expect(
        categoriser.categoriseUrl(new URL('http://example6.online?search=blah')).categoryIds,
      ).toEqual(new Set(['61', '62', '63', '64', '65', '66']));
    });

    it('matches URLs where the blocklist does not contain a query string', () => {
      // E.g. if the blocklist contains "example.com" then it should also match
      //  "example.com?query=blah".

      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(testDotOrg, 'org');
      categoriser.setCategorisationMap(testOther);

      expect(
        categoriser.categoriseUrl(new URL('http://example0.com?hello=world')).categoryIds,
      ).toEqual(new Set(['0']));

      expect(
        categoriser.categoriseUrl(new URL('http://example5.org?hello=world')).categoryIds,
      ).toEqual(new Set(['51', '52', '53']));

      expect(
        categoriser.categoriseUrl(new URL('http://mobile.example8.biz?hello=world')).categoryIds,
      ).toEqual(new Set(['84', '85', '86']));
    });

    it('matches query strings specified in higher domain levels', () => {
      // E.g. if the blocklist contains "example.com?query=blah" then it should also match
      //  "foo.example.com?query=blah"

      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(testDotOrg, 'org');
      categoriser.setCategorisationMap(testOther);

      expect(
        categoriser.categoriseUrl(new URL('http://blah.test.mobile.example2.com?search=blah'))
          .categoryIds,
      ).toEqual(new Set(['21', '22', '23', '24', '25', '26']));

      expect(
        categoriser.categoriseUrl(new URL('http://blah.example6.online?search=blah')).categoryIds,
      ).toEqual(new Set(['61', '62', '63', '64', '65', '66']));
    });

    it('does not match query strings specified in lower domain levels', () => {
      // E.g. if the blocklist contains "foo.example.com?query=blah" then it should not match
      //  "example.com?query=blah".

      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(testDotOrg, 'org');
      categoriser.setCategorisationMap(testOther);

      expect(
        categoriser.categoriseUrl(new URL('http://example5.org?search=blah')).categoryIds,
      ).toEqual(new Set(['51', '52', '53']));
    });

    it('does not match query strings specified in other path levels', () => {
      // We should only match the query string against full paths, not partial paths.
      // E.g. if the blocklist contains "example.com/foo/bar?query=blah", then it should NOT match
      //  "example.com/foo?query=blah".

      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(testDotOrg, 'org');
      categoriser.setCategorisationMap(testOther);

      expect(
        categoriser.categoriseUrl(new URL('http://example3.com/foo?search=blah')).categoryIds,
      ).toEqual(new Set(['31', '32', '33', '38']));

      expect(
        categoriser.categoriseUrl(new URL('http://example3.com/foo/bar?search=blah')).categoryIds,
      ).toEqual(new Set(['31', '32', '33', '34', '35', '36', '37']));

      expect(
        categoriser.categoriseUrl(new URL('http://example3.com/foo/bar/blah?search=blah'))
          .categoryIds,
      ).toEqual(new Set(['31', '32', '33', '34', '35', '36']));

      expect(
        categoriser.categoriseUrl(new URL('http://mobile.example4.com/?search=blah')).categoryIds,
      ).toEqual(new Set([]));

      expect(
        categoriser.categoriseUrl(new URL('http://mobile.example4.com/foo?search=blah'))
          .categoryIds,
      ).toEqual(new Set(['40', '41']));

      expect(
        categoriser.categoriseUrl(new URL('http://mobile.example4.com/foo/bar?search=blah'))
          .categoryIds,
      ).toEqual(new Set(['40', '41', '42', '43', '44']));

      expect(
        categoriser.categoriseUrl(new URL('http://mobile.example4.com/foo/bar/xyz?search=blah'))
          .categoryIds,
      ).toEqual(new Set(['40', '41', '42', '43']));
    });

    it('does not match query strings specified in lower path levels', () => {
      // E.g. if the blocklist contains "example.com/foo?query=blah" then it should not match
      //  "example.com?query=blah".

      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(testDotOrg, 'org');
      categoriser.setCategorisationMap(testOther);

      expect(
        categoriser.categoriseUrl(new URL('http://example3.com/?search=blah')).categoryIds,
      ).toEqual(new Set([]));
    });

    it('matches URLs containing a combination of subdomains, child paths, and query strings not specified in the blocklist', () => {
      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(testDotOrg, 'org');
      categoriser.setCategorisationMap(testOther);

      expect(
        categoriser.categoriseUrl(
          new URL('http://blah.test.mobile.example2.com/blah/blah?hello=world'),
        ).categoryIds,
      ).toEqual(new Set(['21', '22', '24', '25']));

      expect(
        categoriser.categoriseUrl(
          new URL('http://xyz.test.example5.org/hello/world/xyz?hello=world'),
        ).categoryIds,
      ).toEqual(new Set(['51', '52', '53', '57', '58', '59']));
    });
    it('matches entries with different locale sorting', () => {
      // A problem was found when performing a binary search on the blocklist where compareLocale would sort some characters the wrong way round.
      // For example it would place the question mark before a forward slash, which is not true when comparing code points.
      // This would mean that the categorisation for some urls would be incorrect.
      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');

      // If the sorting of the question mark and slash are incorrect then these tests will fail.
      expect(
        categoriser.categoriseUrl(new URL('http://example3.com/foo?search=blah')).categoryIds,
      ).toEqual(new Set(['31', '32', '33', '38']));

      expect(
        categoriser.categoriseUrl(new URL('http://example3.com/foo/bar?search=blah')).categoryIds,
      ).toEqual(new Set(['31', '32', '33', '34', '35', '36', '37']));
    });
    it('matches permutations of subdomains and child paths', () => {
      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(testDotOrg, 'org');
      categoriser.setCategorisationMap(testOther);

      // This should match all the following blocklist entries:
      // - example7.info
      // - example7.info/foo/bar
      // - test.example7.info

      expect(
        categoriser.categoriseUrl(new URL('http://xyz.test.example7.info/foo/bar/xyz?hello=world'))
          .categoryIds,
      ).toEqual(new Set(['71', '72', '73', '74', '75', '76', '77', '78', '79']));

      // This should match all the following blocklist entries:
      // - example8.biz/foo/bar
      // - mobile.example8.biz
      // - test.mobile.example8.biz/foo/bar

      expect(
        categoriser.categoriseUrl(
          new URL('http://xyz.test.mobile.example8.biz/foo/bar/xyz?hello=world'),
        ).categoryIds,
      ).toEqual(new Set(['81', '82', '83', '84', '85', '86', '87', '88', '89']));
    });

    it('returns an empty set if the URL does not match any blocklist entry', () => {
      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(testDotOrg, 'org');
      categoriser.setCategorisationMap(testOther);

      expect(categoriser.categoriseUrl(new URL('http://gov.uk')).categoryIds).toEqual(new Set());
    });

    it('converts the URL to lower-case before matching it to the blocklist', () => {
      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(testDotOrg, 'org');
      categoriser.setCategorisationMap(testOther);

      expect(categoriser.categoriseUrl(new URL('http://EXAMPLE1.COM')).categoryIds).toEqual(
        new Set(['11', '12', '13']),
      );
    });

    it('ignores the scheme prefix in the URL', () => {
      const categoriser = new UrlListCategoriser();
      categoriser.setCategorisationMap(testDotCom, 'com');
      categoriser.setCategorisationMap(testDotOrg, 'org');
      categoriser.setCategorisationMap(testOther);

      expect(categoriser.categoriseUrl(new URL('http://example1.com')).categoryIds).toEqual(
        new Set(['11', '12', '13']),
      );

      expect(categoriser.categoriseUrl(new URL('https://example1.com')).categoryIds).toEqual(
        new Set(['11', '12', '13']),
      );

      expect(categoriser.categoriseUrl(new URL('ftp://example1.com')).categoryIds).toEqual(
        new Set(['11', '12', '13']),
      );

      expect(categoriser.categoriseUrl(new URL('chrome://example1.com')).categoryIds).toEqual(
        new Set(['11', '12', '13']),
      );
    });
    it('matches a simple custom category', () => {
      const categoriser = new UrlListCategoriser();

      const url = new URL('https://custom.com');

      const expected = new Set(['61e8bb25-cfd2-4a94-9de8-0e2fa648ef17']);

      const result = categoriser.categoriseUrl(url, policyJson.custom_categories);

      expect(result.categoryIds).toEqual(expected);
    });

    it('matches a custom category with subdomain', () => {
      const categoriser = new UrlListCategoriser();
      const url = new URL('https://test.custom.com');

      const expected = new Set([
        '61e8bb25-cfd2-4a94-9de8-0e2fa648ef17',
        '6570637d-09af-4d2a-84bb-e465f8f258a1',
      ]);

      const result = categoriser.categoriseUrl(url, policyJson.custom_categories);

      expect(result.categoryIds).toEqual(expected);
    });

    it('matches a custom category with a path', () => {
      const categoriser = new UrlListCategoriser();
      const url = new URL('https://custom.com/path');

      const expected = new Set([
        '61e8bb25-cfd2-4a94-9de8-0e2fa648ef17',
        '4d8ab083-5404-439e-bbb5-49a77de2a5b9',
      ]);

      const result = categoriser.categoriseUrl(url, policyJson.custom_categories);

      expect(result.categoryIds).toEqual(expected);
    });

    it('matches a complex custom category', () => {
      const categoriser = new UrlListCategoriser();
      const url = new URL('https://www.domain.test.custom.com/path/testing');

      const expected = new Set([
        '61e8bb25-cfd2-4a94-9de8-0e2fa648ef17',
        '4d8ab083-5404-439e-bbb5-49a77de2a5b9',
        '6570637d-09af-4d2a-84bb-e465f8f258a1',
      ]);

      const result = categoriser.categoriseUrl(url, policyJson.custom_categories);

      expect(result.categoryIds).toEqual(expected);
    });
    it('is case insensitive when matching a custom category', () => {
      const categoriser = new UrlListCategoriser();
      const url = new URL('https://www.test.example.com/uppercasetest');

      const expected = new Set(['a9baee13-d068-4894-85d6-fa35f7b656f8']);

      const result = categoriser.categoriseUrl(url, policyJson.custom_categories);

      expect(result.categoryIds).toEqual(expected);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('extractTopLevelDomain()', () => {
    it('returns the top level domain from a simple URL', () => {
      expect(UrlListCategoriser.extractTopLevelDomain(new URL('http://example.org'))).toEqual(
        'org',
      );
    });

    it('returns the top level domain from a complex URL', () => {
      expect(
        UrlListCategoriser.extractTopLevelDomain(
          new URL('http://test.mobile.example.biz/foo/bar?search=blah'),
        ),
      ).toEqual('biz');
    });

    it('handles short top level domains correctly', () => {
      expect(UrlListCategoriser.extractTopLevelDomain(new URL('http://example.uk'))).toEqual('uk');
    });

    it('handles long top level domains correctly', () => {
      expect(UrlListCategoriser.extractTopLevelDomain(new URL('http://example.online'))).toEqual(
        'online',
      );
    });

    it('returns the entire hostname if there is only one domain level', () => {
      expect(UrlListCategoriser.extractTopLevelDomain(new URL('http://foo/bar.html'))).toEqual(
        'foo',
      );
    });

    it('returns an empty string if there is no domain', () => {
      expect(UrlListCategoriser.extractTopLevelDomain(new URL('file:///foo/bar.html'))).toEqual('');
    });
  });
});

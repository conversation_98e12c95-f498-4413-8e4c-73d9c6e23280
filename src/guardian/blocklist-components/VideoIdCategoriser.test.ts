import DiffMap from 'guardian/patching/DiffMap';
import UnifiedDiffMapBuilder from 'guardian/patching/UnifiedDiffMapBuilder';
import { ICustomCategory } from 'models/PolicyConfigModels';

import CategorisationMap from '../utilities/CategorisationMap';
import VideoIdCategoriser from './VideoIdCategoriser';

const testExtractionPatterns = [
  /^(?:.{1,5}:\/\/)?(?:[^/]*\.)?vimeo\.com\/(\d{9})$/,
  /^(?:.{1,5}:\/\/)?(?:[^/]*\.)?youtube\.\w{1,3}(?:\.\w{1,3})?\/videoplayback\?.*id=([A-Fa-f0-9]{16})/,
  /^(?:.{1,5}:\/\/)?(?:[^/]*\.)?youtube(?:-nocookie)?\.\w{1,3}(?:\.\w{1,3})?\/(?:watch|e|embed)(?:\?v=|.*?[^?][&?]v=)([-\w]{11})/,
  /^(?:.{1,5}:\/\/)?(?:[^/]*\.)?youtube(?:-nocookie)?\.\w{1,3}(?:\.\w{1,3})?\/(?:v\/|e\/|embed\/)([-\w]{11})/,
  /^(?:.{1,5}:\/\/)?(?:[^/]*\.)?youtube(?:-nocookie)?\.\w{1,3}(?:\.\w{1,3})?\/(?:watch|playlist|e|embed).*(?:\?list=|&list=)([-\w]{16,})/,
  /^https?:\/\/multiple-ids\.example\.com\/?\?v1=(\w{11})&v2=(\w{11})$/,
  /^https?:\/\/similar-patterns\.example\.com\/?\?(?:.+&)?id1=(\w{11})/, // <-- matches id1
  /^https?:\/\/similar-patterns\.example\.com\/?\?(?:.+&)?id2=(\w{11})/, // <-- matches id2
];

const testExtractionPatternFile = String.raw`'number-id.example.org\/?\?id=(\d+)',
'letter-id.example.org\/?\?id=([a-zA-Z]+)',`;

const testCategorisationMap = new CategorisationMap(
  [
    '123456789|091,639,1000',
    '222333444|1,2,3',
    'AAaaBBbb11112222|999,888,777',
    'abcdefghijk|000,999',
    'blahblahblahblah|1357,2468',
    'FUbar_fuBAR|1',
    'helloworld1|080,321',
    'xyzzy_12345|123,444,567',
  ],
  undefined,
  undefined,
  true,
);

const customVideoIds: ICustomCategory[] = [
  {
    category_id: '41b9d40b9777833be260761698455655',
    component: {
      videoids: ['dQw4w9WgXcQ', 'nomatch'],
    },
    custom_content: '1',
    href: '1',
    id: 'F757E724-B44C-11ED-BE2B-8992081D14AE',
    description: '',
    name: 'Video Id Test',
    tenant: 'global',
  },
];

describe('VideoIdCategoriser', () => {
  // -----------------------------------------------------------------------------------------------

  describe('setExtractionPatterns()', () => {
    it('replaces the existing extraction patterns', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        testCategorisationMap,
      );
      videoIdCategoriser.setExtractionPatterns([
        /^https?:\/\/video\.example\.com\/?\?id=(\w{11})$/,
      ]);

      expect(
        videoIdCategoriser.extractVideoIdsFromUrl(
          new URL('https://www.youtube.com/watch?v=abcdefghijk'),
        ),
      ).toEqual(new Set());

      expect(
        videoIdCategoriser.extractVideoIdsFromUrl(
          new URL('https://video.example.com?id=abcdefghijk'),
        ),
      ).toEqual(new Set(['abcdefghijk']));
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('getCategorisationMap()', () => {
    it('returns the object which stores the mapping of video IDs to categories', () => {
      const categoriser = new VideoIdCategoriser(testExtractionPatterns, testCategorisationMap);
      const categorisationMap = categoriser.getCategorisationMap();
      expect(categorisationMap.lookupCategories('abcdefghijk')).toEqual(new Set(['000', '999']));
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('setCategorisationMap()', () => {
    it('replaces the existing categorisation map', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        testCategorisationMap,
      );
      videoIdCategoriser.setCategorisationMap(
        new CategorisationMap(['aaaaaaaaaaa|42,43,44'], undefined, undefined, true),
      );

      expect(
        videoIdCategoriser.categoriseUrl(new URL('https://www.youtube.com/watch?v=xyzzy_12345'))
          .categoryIds,
      ).toEqual(new Set());

      expect(
        videoIdCategoriser.categoriseUrl(new URL('https://www.youtube.com/watch?v=aaaaaaaaaaa'))
          .categoryIds,
      ).toEqual(new Set(['42', '43', '44']));
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('length()', () => {
    it('returns the number of video IDs in the categorisation map', () => {
      const videoIdCategoriser = new VideoIdCategoriser();
      expect(videoIdCategoriser.length).toEqual(0);
      videoIdCategoriser.setCategorisationMap(testCategorisationMap);
      expect(videoIdCategoriser.length).toEqual(8);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('loadExtractionPatternsFromBlocklistFile()', () => {
    it('stores the specified extraction patterns', () => {
      const categoriser = new VideoIdCategoriser();
      categoriser.loadExtractionPatternsFromBlocklistFile(testExtractionPatternFile);

      expect(
        categoriser.extractVideoIdsFromUrl(new URL('http://number-id.example.org?id=314')),
      ).toEqual(new Set(['314']));
      expect(
        categoriser.extractVideoIdsFromUrl(new URL('http://letter-id.example.org?id=abc')),
      ).toEqual(new Set(['abc']));
    });

    it('throws an error if the file contains an invalid regular expression', () => {
      const file = testExtractionPatternFile + '\n' + String.raw`example.net/\?id=)\d+(`;
      const categoriser = new VideoIdCategoriser();
      expect(() => {
        categoriser.loadExtractionPatternsFromBlocklistFile(file);
      }).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  // TODO: loadCategorisationMapFromBlocklistFile()

  // -----------------------------------------------------------------------------------------------

  describe('clear()', () => {
    it('deletes the stored extraction patterns', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        testCategorisationMap,
      );
      videoIdCategoriser.clear();

      expect(
        videoIdCategoriser.extractVideoIdsFromUrl(
          new URL('https://www.youtube.com/watch?v=xyzzy_12345'),
        ),
      ).toEqual(new Set());
    });

    it('prevents the extraction patterns from being patched', () => {
      // Extraction patterns cannot be patched after being cleared because we'll have no blocklist
      //  data to apply the patch to.
      const categoriser = new VideoIdCategoriser();
      categoriser.loadExtractionPatternsFromBlocklistFile(testExtractionPatternFile);
      categoriser.clear();
      expect(() => {
        categoriser.patchFromUnifiedDiff(new DiffMap());
      }).toThrow();
    });

    it('deletes the stored categorisation map', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        testCategorisationMap,
      );
      videoIdCategoriser.clear();

      // We need to replace the extraction patterns otherwise we won't hit the categorisation map.
      videoIdCategoriser.setExtractionPatterns(testExtractionPatterns);

      expect(
        videoIdCategoriser.categoriseUrl(new URL('https://www.youtube.com/watch?v=xyzzy_12345'))
          .categoryIds,
      ).toEqual(new Set());
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('patchFromUnifiedDiff()', () => {
    it('correctly applies a diff', () => {
      // The blocklist diffs in our test data don't modify this file significantly.
      // We need to construct our own test data, based on real blocklist data.

      const oldFile = String.raw`'^(?:.{1,5}://)?(?:[^/]*\.)?vimeo\.com(?:/video)?/(\d{8,9})',
'^(?:.{1,5}://)?w{1,3}\.dailymotion\.com/video/([\w]{1,7})',
'^(?:.{1,5}://)?w{1,3}\.example\.com/video/(\w+)',
'^(?:.{1,5}://)?w{1,3}\.example\.net/video/(\w+)',
`;

      const newFile = String.raw`'^(?:.{1,5}://)?(?:[^/]*\.)?youtube\.\w{1,3}(?:\.\w{1,3})?/videoplayback\?.*id=([^&]+)',
'^(?:.{1,5}://)?(?:[^/]*\.)?youtube(?:-nocookie)?\.\w{1,3}(?:\.\w{1,3})?\/(?:shorts)/([-\w]{11})',
'^(?:.{1,5}://)?(?:[^/]*\.)?youtu\.be/([-\w]{11})',
'^(?:.{1,5}://)?w{1,3}\.dailymotion\.com/video/([\w]{1,7})',
'^(?:.{1,5}://)?w{1,3}\.example\.com/video/(\w+)',
'^(?:.{1,5}://)?w{1,3}\.example\.org/video/(\w+)',
`;

      const unifiedDiff = String.raw`
--- a/videoidregexplist	2025-01-02 03:04:05.000 +0000
+++ b/videoidregexplist	2025-01-03 03:04:05.000 +0000
@@ -1,1 +1,3 @@
-'^(?:.{1,5}://)?(?:[^/]*\.)?vimeo\.com(?:/video)?/(\d{8,9})',
+'^(?:.{1,5}://)?(?:[^/]*\.)?youtube\.\w{1,3}(?:\.\w{1,3})?/videoplayback\?.*id=([^&]+)',
+'^(?:.{1,5}://)?(?:[^/]*\.)?youtube(?:-nocookie)?\.\w{1,3}(?:\.\w{1,3})?\/(?:shorts)/([-\w]{11})',
+'^(?:.{1,5}://)?(?:[^/]*\.)?youtu\.be/([-\w]{11})',
@@ -4,1 +6,1 @@
-'^(?:.{1,5}://)?w{1,3}\.example\.net/video/(\w+)',
+'^(?:.{1,5}://)?w{1,3}\.example\.org/video/(\w+)',
`;

      // Load the old unpatched data.
      const oldComponent = new VideoIdCategoriser();
      oldComponent.loadExtractionPatternsFromBlocklistFile(oldFile);

      // An entry which is removed by the patch should work correctly before patching.
      const input1 = new URL('https://www.example.net/video/blah');
      expect(oldComponent.extractVideoIdsFromUrl(input1)).toContain('blah');

      // An entry which is added by the patch should not work before patching.
      const input2 = new URL('https://youtube.com/videoplayback?id=blah');
      expect(oldComponent.extractVideoIdsFromUrl(input2)).toBeEmpty();

      // An entry which is not modified by the patch should work before and after patching.
      const input3 = new URL('https://www.dailymotion.com/video/blah');
      expect(oldComponent.extractVideoIdsFromUrl(input3)).toContain('blah');

      // Apply the patch.
      const diffMaps = [...UnifiedDiffMapBuilder.buildFromFile(unifiedDiff)];
      oldComponent.patchFromUnifiedDiff(diffMaps[0]);

      // An entry which is removed by the patch should not work after patching.
      expect(oldComponent.extractVideoIdsFromUrl(input1)).toBeEmpty();

      // An entry which is added by the patch should work correctly after patching.
      expect(oldComponent.extractVideoIdsFromUrl(input2)).toContain('blah');

      // An entry which is not modified by the patch should work before and after patching.
      expect(oldComponent.extractVideoIdsFromUrl(input3)).toContain('blah');

      // TODO: When we have the ability to save the result of patching, compare the output of that
      //  against the expected result, instead of accessing private properties.
      const newComponent = new VideoIdCategoriser();
      newComponent.loadExtractionPatternsFromBlocklistFile(newFile);
      expect((oldComponent as any)._originalData).toEqual((newComponent as any)._originalData);
    });

    it('throws an error if the extraction patterns were not loaded from a blocklist file', () => {
      const categoriser = new VideoIdCategoriser(testExtractionPatterns);
      expect(() => {
        categoriser.patchFromUnifiedDiff(new DiffMap());
      }).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('categoriseUrl()', () => {
    it('returns an object containing all video IDs found in the URL', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        testCategorisationMap,
      );

      expect(
        videoIdCategoriser.categoriseUrl(new URL('https://vimeo.com/222333444')).videoIds,
      ).toEqual(new Set(['222333444']));

      expect(
        videoIdCategoriser.categoriseUrl(new URL('https://www.youtube.com/watch?v=xyzzy_12345'))
          .videoIds,
      ).toEqual(new Set(['xyzzy_12345']));

      expect(
        videoIdCategoriser.categoriseUrl(
          new URL('http://multiple-ids.example.com?v1=abcdefghijk&v2=xyzzy_12345'),
        ).videoIds,
      ).toEqual(new Set(['abcdefghijk', 'xyzzy_12345']));
    });

    it('returns an object containing the categories which the videos belong to', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        testCategorisationMap,
      );

      expect(
        videoIdCategoriser.categoriseUrl(new URL('https://vimeo.com/222333444')).categoryIds,
      ).toEqual(new Set(['1', '2', '3']));

      expect(
        videoIdCategoriser.categoriseUrl(new URL('https://www.youtube.com/watch?v=FUbar_fuBAR'))
          .categoryIds,
      ).toEqual(new Set(['1']));

      expect(
        videoIdCategoriser.categoriseUrl(new URL('https://www.youtube.com/watch?v=xyzzy_12345'))
          .categoryIds,
      ).toEqual(new Set(['123', '444', '567']));
    });

    it('returned object contains an empty video ID set if no video IDs were found in the URL', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        testCategorisationMap,
      );
      expect(
        videoIdCategoriser.categoriseUrl(new URL('https://www.youtube.com/about')).videoIds,
      ).toEqual(new Set());
    });

    it('returned object contains an empty category set if no video IDs were found in the URL', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        testCategorisationMap,
      );
      expect(
        videoIdCategoriser.categoriseUrl(new URL('https://www.youtube.com/about')).categoryIds,
      ).toEqual(new Set());
    });

    it('returned object contains an empty category set if the video ID did not match any categories', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        testCategorisationMap,
      );

      expect(
        videoIdCategoriser.categoriseUrl(new URL('https://www.youtube.com/watch?v=mmmmmmmmmmm'))
          .categoryIds,
      ).toEqual(new Set());
    });

    it('treats video IDs as case sensitive', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        testCategorisationMap,
      );

      expect(
        videoIdCategoriser.categoriseUrl(new URL('https://www.youtube.com/watch?v=FUbar_fuBAR'))
          .categoryIds,
      ).toEqual(new Set(['1']));

      expect(
        videoIdCategoriser.categoriseUrl(new URL('https://www.youtube.com/watch?v=FUBAR_fubar'))
          .categoryIds,
      ).toEqual(new Set());
    });

    it('fetches categories for all video IDs if multiple were matched', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        testCategorisationMap,
      );

      // Video ID 'abcdefghijk' should match categories 000 and 999.
      // Video ID 'xyzzy_12345' should match categories 123, 444, and 567.

      expect(
        videoIdCategoriser.categoriseUrl(
          new URL('http://multiple-ids.example.com?v1=abcdefghijk&v2=xyzzy_12345'),
        ).categoryIds,
      ).toEqual(new Set(['000', '999', '123', '444', '567']));
    });

    it('matches custom video ids', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        testCategorisationMap,
      );
      const url = new URL('https://www.youtube.com/watch?v=dQw4w9WgXcQ');

      const result = videoIdCategoriser.categoriseUrl(url, customVideoIds);

      expect(result.categoryIds).toEqual(new Set(['41b9d40b9777833be260761698455655']));
    });

    it('matches both blocklist and custom categories for the same video ID', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        new CategorisationMap(['dQw4w9WgXcQ|1,2,3'], undefined, undefined, true),
      );
      const url = new URL('https://www.youtube.com/watch?v=dQw4w9WgXcQ');

      const result = videoIdCategoriser.categoriseUrl(url, customVideoIds);

      expect(result.categoryIds).toEqual(
        new Set(['1', '2', '3', '41b9d40b9777833be260761698455655']),
      );
    });

    it('matches multiple custom categories for the same video ID', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        testCategorisationMap,
      );
      const multipleCustomCategories: ICustomCategory[] = [
        {
          category_id: 'custom1',
          component: {
            videoids: ['abcdefghijk'],
          },
          custom_content: '1',
          href: '1',
          id: '1',
          description: '',
          name: 'Custom Category 1',
          tenant: 'global',
        },
        {
          category_id: 'custom2',
          component: {
            videoids: ['abcdefghijk', 'other'],
          },
          custom_content: '1',
          href: '1',
          id: '2',
          description: '',
          name: 'Custom Category 2',
          tenant: 'global',
        },
      ];
      // Using a URL format that matches testExtractionPatterns
      const url = new URL('https://www.youtube.com/watch?v=abcdefghijk');

      const result = videoIdCategoriser.categoriseUrl(url, multipleCustomCategories);

      expect(result.categoryIds).toEqual(new Set(['custom1', 'custom2', '000', '999']));
    });

    it('matches multiple blocklist and multiple custom categories for the same video ID', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        new CategorisationMap(['xyzzy_12345|100,200,300'], undefined, undefined, true),
      );
      const multipleCustomCategories: ICustomCategory[] = [
        {
          category_id: 'custom1',
          component: {
            videoids: ['xyzzy_12345'],
          },
          custom_content: '1',
          href: '1',
          id: '1',
          description: '',
          name: 'Custom Category 1',
          tenant: 'global',
        },
        {
          category_id: 'custom2',
          component: {
            videoids: ['xyzzy_12345'],
          },
          custom_content: '1',
          href: '1',
          id: '2',
          description: '',
          name: 'Custom Category 2',
          tenant: 'global',
        },
      ];
      // Using a URL format that matches testExtractionPatterns
      const url = new URL('https://www.youtube.com/watch?v=xyzzy_12345');

      const result = videoIdCategoriser.categoriseUrl(url, multipleCustomCategories);

      expect(result.categoryIds).toEqual(new Set(['100', '200', '300', 'custom1', 'custom2']));
    });

    it('explicitly verifies custom categories are checked when no blocklist categories match', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        new CategorisationMap(['otherVideo123|100,200'], undefined, undefined, true),
      );
      const customCategories: ICustomCategory[] = [
        {
          category_id: 'custom1',
          component: {
            videoids: ['mmmmmmmmmmm'],
          },
          custom_content: '1',
          href: '1',
          id: '1',
          description: '',
          name: 'Custom Category',
          tenant: 'global',
        },
      ];
      // Using a URL format that matches testExtractionPatterns
      const url = new URL('https://www.youtube.com/watch?v=mmmmmmmmmmm');

      const result = videoIdCategoriser.categoriseUrl(url, customCategories);

      expect(result.categoryIds).toEqual(new Set(['custom1']));
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('extractVideoIdsFromUrl()', () => {
    it('returns the video ID found in the specified URL', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        testCategorisationMap,
      );
      expect(
        videoIdCategoriser.extractVideoIdsFromUrl(
          new URL('https://www.youtube.com/watch?v=xyzzy_12345'),
        ),
      ).toEqual(new Set(['xyzzy_12345']));
    });

    it('returns an empty set if no video IDs were found in the URL', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        testCategorisationMap,
      );
      expect(
        videoIdCategoriser.extractVideoIdsFromUrl(new URL('https://www.youtube.com/watch')),
      ).toEqual(new Set());
    });

    it('returns multiple IDs if one pattern matches multiple different IDs', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        testCategorisationMap,
      );
      expect(
        videoIdCategoriser.extractVideoIdsFromUrl(
          new URL('http://multiple-ids.example.com?v1=abcdefghijk&v2=xyzzy_12345'),
        ),
      ).toEqual(new Set(['abcdefghijk', 'xyzzy_12345']));
    });

    it('returns multiple IDs if multiple patterns match different IDs', () => {
      const videoIdCategoriser = new VideoIdCategoriser(
        testExtractionPatterns,
        testCategorisationMap,
      );
      const url = new URL('http://similar-patterns.example.com?id1=abcdefghijk&id2=xyzzy_12345');
      expect(videoIdCategoriser.extractVideoIdsFromUrl(url)).toEqual(
        new Set(['abcdefghijk', 'xyzzy_12345']),
      );
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('parseExtractionPatternFromBlocklistLine()', () => {
    // This is just a quick smoke test. This function uses the SearchTermCategoriser implementation
    //  which is tested elsewhere.

    it('returns the regular expression parsed from the blocklist line', () => {
      const re = VideoIdCategoriser.parseExtractionPatternFromBlocklistLine("'[a-z]{3}[0-9]{3}',");
      expect(re.test('abc123')).toBeTrue();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('parseExtractionPatternsFromBlocklistFile()', () => {
    // This is just a quick smoke test. This function uses the SearchTermCategoriser implementation
    //  which is tested elsewhere.

    it('returns an array of regular expressions parsed from the given lines', () => {
      const actual = VideoIdCategoriser.parseExtractionPatternsFromBlocklistFile(["'abc(\\d)',"]);
      expect(actual).toBeArrayOfSize(1);
      expect(actual[0].test('abc1')).toBeTrue();
    });
  });
});

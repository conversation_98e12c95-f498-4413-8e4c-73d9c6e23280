import CachedSubstringMatcher from 'guardian/utilities/CachedSubstringMatcher';
import WeightedPhraseCategoriser, * as wpc from './WeightedPhraseCategoriser';

// -------------------------------------------------------------------------------------------------

const testWeightedPhrases: wpc.WeightedPhrases = {
  // Standalone phrases:
  'bad mojo': {
    catsAndScores: [
      {
        category: 111,
        score: 48,
      },
      {
        category: 222,
        score: 86,
      },
    ],
    children: [],
  },

  'questionable mojo': {
    catsAndScores: [
      {
        category: 111,
        score: 15,
      },
    ],
    children: [],
  },

  'good mojo': {
    catsAndScores: [
      {
        category: 111,
        score: -98,
      },
      {
        category: 222,
        score: -10,
      },
    ],
    children: [],
  },

  'hello world': {
    catsAndScores: [
      {
        category: 333,
        score: 100,
      },
    ],
    children: [],
  },

  // Compound phrase (only matches with child phrases):
  smoothwall: {
    catsAndScores: [],
    children: [
      {
        phrases: ['how', 'disable'],
        catsAndScores: {
          category: 444,
          score: 100,
        },
      },
    ],
  },

  // Standalone + compound phrase:
  games: {
    catsAndScores: [
      {
        category: 555,
        score: 10,
      },
    ],
    children: [
      {
        phrases: ['adult'],
        catsAndScores: {
          category: 666,
          score: 87,
        },
      },
    ],
  },
};

// -------------------------------------------------------------------------------------------------

describe('WeightedPhraseCategoriser', () => {
  // -------------------------------------------------------------------------------------------------

  // constructor() is covered by other tests

  // -------------------------------------------------------------------------------------------------

  describe('setWeightedPhrases()', () => {
    it('replaces previously stored phrase data', () => {
      const tempData: wpc.WeightedPhrases = {
        blah: {
          catsAndScores: [
            {
              category: '00000',
              score: 100,
            },
          ],
          children: [],
        },
      };

      const categoriser = new WeightedPhraseCategoriser(tempData);
      categoriser.setWeightedPhrases(testWeightedPhrases);

      expect(categoriser.categoriseContent('blah').foundCategories.length).toEqual(0);
      expect(categoriser.categoriseContent('bad mojo').foundCategories.length).toBeGreaterThan(0);
    });
  });

  // -------------------------------------------------------------------------------------------------

  describe('length()', () => {
    it('returns zero if no phrase data has been loaded yet', () => {
      const categoriser = new WeightedPhraseCategoriser();
      expect(categoriser.length).toEqual(0);
    });

    it('returns the number of parent phrases currently stored', () => {
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);
      expect(categoriser.length).toEqual(6);
    });
  });

  // -------------------------------------------------------------------------------------------------

  describe('loadFromBlocklistFile()', () => {
    it('populates the weighted phrase data from the given JSON string', () => {
      const blocklistFileContent = `{
        "blah": {
          "catsAndScores": [
            {
              "category": "00000",
              "score": 100
            }
          ],
          "children": []
        }
      }`;

      const categoriser = new WeightedPhraseCategoriser();
      categoriser.loadFromBlocklistFile(blocklistFileContent);
      expect(categoriser.categoriseContent('blah').foundCategories.length).toEqual(1);
    });

    it('throws an error if the specified string is not valid JSON', () => {
      const categoriser = new WeightedPhraseCategoriser();
      expect(() => {
        categoriser.loadFromBlocklistFile('} foo');
      }).toThrow();
    });
  });

  // -------------------------------------------------------------------------------------------------

  describe('clear()', () => {
    it('does nothing if no phrases have been loaded', () => {
      const categoriser = new WeightedPhraseCategoriser();
      expect(() => {
        categoriser.clear();
      }).not.toThrow();
    });

    it('deletes all the stored phrase data', () => {
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);
      categoriser.clear();
      expect(categoriser.length).toEqual(0);

      // Categorisation shouldn't find anything.
      const content = 'This is some bad mojo dude.';
      const result = categoriser.categoriseContent(content);
      expect(result.foundCategories.length).toEqual(0);
    });
  });

  // -------------------------------------------------------------------------------------------------

  describe('categoriseContent()', () => {
    it('returns no categories if no phrases were recognised', () => {
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);

      const content = "This is some random text which won't match any known phrases.";
      const result = categoriser.categoriseContent(content);
      expect(result.foundCategories.length).toEqual(0);
    });

    it('returns categories relating to one standalone phrase', () => {
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);

      const content = 'This is some bad mojo dude.';
      const result = categoriser.categoriseContent(content);
      expect(result.foundCategories.length).toEqual(2);

      expect(result.getCategory('111')?.totalScore).toEqual(48);
      expect(result.getCategory('111')?.getContributingPhrase(['bad mojo'])?.score).toEqual(48);

      expect(result.getCategory('222')?.totalScore).toEqual(86);
      expect(result.getCategory('222')?.getContributingPhrase(['bad mojo'])?.score).toEqual(86);
    });

    it('returns all categories relating to multiple standalone phrases', () => {
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);

      const content = ' hello world, this is some bad mojo ';
      const result = categoriser.categoriseContent(content);
      expect(result.foundCategories.length).toEqual(3);

      expect(result.getCategory('111')?.totalScore).toEqual(48);
      expect(result.getCategory('111')?.getContributingPhrase(['bad mojo'])?.score).toEqual(48);

      expect(result.getCategory('222')?.totalScore).toEqual(86);
      expect(result.getCategory('222')?.getContributingPhrase(['bad mojo'])?.score).toEqual(86);

      expect(result.getCategory('333')?.totalScore).toEqual(100);
      expect(result.getCategory('333')?.getContributingPhrase(['hello world'])?.score).toEqual(100);
    });

    it('adds together scores from multiple phrases', () => {
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);

      const content = "I don't know if this is bad mojo or questionable mojo.";
      const result = categoriser.categoriseContent(content);
      expect(result.foundCategories.length).toEqual(2);

      expect(result.getCategory('111')?.totalScore).toEqual(63); // 48 + 15
      expect(result.getCategory('111')?.getContributingPhrase(['bad mojo'])?.score).toEqual(48);
      expect(
        result.getCategory('111')?.getContributingPhrase(['questionable mojo'])?.score,
      ).toEqual(15);
    });

    it('handles negative scores', () => {
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);

      const content = 'The good mojo, the bad mojo, and the questionable mojo.';
      const result = categoriser.categoriseContent(content);
      expect(result.foundCategories.length).toEqual(2);

      expect(result.getCategory('111')?.totalScore).toEqual(-35); // 48 + 15 - 98
      expect(result.getCategory('111')?.getContributingPhrase(['bad mojo'])?.score).toEqual(48);
      expect(
        result.getCategory('111')?.getContributingPhrase(['questionable mojo'])?.score,
      ).toEqual(15);
      expect(result.getCategory('111')?.getContributingPhrase(['good mojo'])?.score).toEqual(-98);

      expect(result.getCategory('222')?.totalScore).toEqual(76); // 86 - 10
      expect(result.getCategory('222')?.getContributingPhrase(['bad mojo'])?.score).toEqual(86);
      expect(result.getCategory('222')?.getContributingPhrase(['good mojo'])?.score).toEqual(-10);
    });

    it('matches compound phrases which are not standalone', () => {
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);

      const content = 'How to disable Smoothwall products';
      const result = categoriser.categoriseContent(content);

      // We should match the complete phrase, but not the parent.
      expect(
        result.getCategory('444')?.getContributingPhrase(['smoothwall', 'how', 'disable'])?.score,
      ).toEqual(100);
      expect(result.getCategory('444')?.getContributingPhrase(['smoothwall'])).toBeUndefined();
    });

    it('matches compound phrases which are also standalone', () => {
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);

      const content = 'play games and other adult content online now';
      const result = categoriser.categoriseContent(content);

      // We should match the parent phrase and the child phrase in different categories.
      expect(result.getCategory('555')?.getContributingPhrase(['games'])?.score).toEqual(10);
      expect(result.getCategory('666')?.getContributingPhrase(['games', 'adult'])?.score).toEqual(
        87,
      );
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('findParentPhrasesInContent()', () => {
    it('returns an empty set if no phrase data has been loaded', () => {
      const categoriser = new WeightedPhraseCategoriser();
      const content = 'Some test content containing nothing notable';
      expect(categoriser.findParentPhrasesInContent(content)).toEqual(new Set());
    });

    it('returns an empty set if no known phrases were found in the content', () => {
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);
      const content = 'Some test content containing nothing notable';
      expect(categoriser.findParentPhrasesInContent(content)).toEqual(new Set());
    });

    it('returns a set containing all known parent phrases found in the specified content', () => {
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);
      const content = 'hello world! adult games are bad mojo in school';
      expect(categoriser.findParentPhrasesInContent(content)).toEqual(
        new Set(['hello world', 'games', 'bad mojo']),
      );
    });

    it('is case sensitive', () => {
      // Note: Case insensitivity is handled elsewhere.
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);
      const content = 'HeLlo WorLd';
      expect(categoriser.findParentPhrasesInContent(content)).toEqual(new Set([]));
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('categorisePhrases()', () => {
    it('returns no categories if no phrase data has been loaded', () => {
      const content = 'Some totally innocuous page content.';
      const phrases = new Set(['totally', 'page']);
      const categoriser = new WeightedPhraseCategoriser();
      const results = categoriser.categorisePhrases(phrases, content);
      expect(results.foundCategories.length).toEqual(0);
    });

    it('ignores phrases which are not in the stored phrase data', () => {
      const content = 'Some totally innocuous page content.';
      const phrases = new Set(['totally', 'page']);
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);
      const results = categoriser.categorisePhrases(phrases, content);
      expect(results.foundCategories.length).toEqual(0);
    });

    it('returns category details about recognised standalone phrases', () => {
      const content = 'hello world, this is bad mojo dude';
      const phrases = new Set(['hello world', 'bad mojo']);
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);
      const results = categoriser.categorisePhrases(phrases, content);

      expect(results.foundCategories.length).toEqual(3);
      expect(results.getCategory('111')?.getContributingPhrase(['bad mojo'])?.score).toEqual(48);
      expect(results.getCategory('222')?.getContributingPhrase(['bad mojo'])?.score).toEqual(86);
      expect(results.getCategory('333')?.getContributingPhrase(['hello world'])?.score).toEqual(
        100,
      );
    });

    it('returns category details about recognised compound phrases', () => {
      const content = 'how to disable smoothwall so i can play adult games online';
      const phrases = new Set(['games', 'smoothwall']);
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);
      const results = categoriser.categorisePhrases(phrases, content);

      expect(results.foundCategories.length).toEqual(3);
      expect(results.getCategory('555')?.getContributingPhrase(['games'])?.score).toEqual(10);
      expect(results.getCategory('666')?.getContributingPhrase(['games', 'adult'])?.score).toEqual(
        87,
      );
      expect(
        results.getCategory('444')?.getContributingPhrase(['smoothwall', 'how', 'disable'])?.score,
      ).toEqual(100);
    });

    it('ignores phrases not specified in the set', () => {
      const content = 'hello world, this is bad mojo dude';
      const phrases = new Set(['hello world']);
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);
      const results = categoriser.categorisePhrases(phrases, content);

      // We should have categorised "hello world", but ignored "bad mojo".
      expect(results.foundCategories.length).toEqual(1);
      expect(results.getCategory('333')?.getContributingPhrase(['hello world'])?.score).toEqual(
        100,
      );
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('categorisePhrase()', () => {
    it('does nothing if no phrase data has been loaded', () => {
      const content = new CachedSubstringMatcher('Some totally innocuous page content.');
      const results = new wpc.WeightedPhraseCategoriserResult();
      const categoriser = new WeightedPhraseCategoriser();
      categoriser.categorisePhrase('totally', content, results);
      expect(results.foundCategories.length).toEqual(0);
    });

    it('does nothing if the specified phrase is not in the stored phrase data', () => {
      const content = new CachedSubstringMatcher('Some totally innocuous page content.');
      const results = new wpc.WeightedPhraseCategoriserResult();
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);
      categoriser.categorisePhrase('totally', content, results);
      expect(results.foundCategories.length).toEqual(0);
    });

    it('adds category details to the results for a standalone phrase', () => {
      const content = new CachedSubstringMatcher('hello world, this is bad mojo dude');
      const results = new wpc.WeightedPhraseCategoriserResult();
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);

      categoriser.categorisePhrase('bad mojo', content, results);
      expect(results.foundCategories.length).toEqual(2);
      expect(results.getCategory('111')?.getContributingPhrase(['bad mojo'])?.score).toEqual(48);
      expect(results.getCategory('222')?.getContributingPhrase(['bad mojo'])?.score).toEqual(86);

      categoriser.categorisePhrase('hello world', content, results);
      expect(results.foundCategories.length).toEqual(3);
      expect(results.getCategory('333')?.getContributingPhrase(['hello world'])?.score).toEqual(
        100,
      );
    });

    it('adds category details to the results for a compound phrase', () => {
      const content = new CachedSubstringMatcher(
        'how to disable smoothwall so i can play adult games online',
      );
      const results = new wpc.WeightedPhraseCategoriserResult();
      const categoriser = new WeightedPhraseCategoriser(testWeightedPhrases);

      categoriser.categorisePhrase('games', content, results);
      expect(results.foundCategories.length).toEqual(2);
      expect(results.getCategory('555')?.getContributingPhrase(['games'])?.score).toEqual(10);
      expect(results.getCategory('666')?.getContributingPhrase(['games', 'adult'])?.score).toEqual(
        87,
      );

      categoriser.categorisePhrase('smoothwall', content, results);
      expect(results.foundCategories.length).toEqual(3);
      expect(
        results.getCategory('444')?.getContributingPhrase(['smoothwall', 'how', 'disable'])?.score,
      ).toEqual(100);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('isEqualArray()', () => {
    it('returns true if the same array is passed in for both parameters', () => {
      const testArray = ['foo', 'bar', 'blah'];
      expect(WeightedPhraseCategoriser.isEqualArray(testArray, testArray)).toBeTrue();
    });

    it('returns true if equal arrays are passed in', () => {
      const testArray1 = ['foo', 'bar', 'blah'];
      const testArray2 = ['foo', 'bar', 'blah'];
      expect(WeightedPhraseCategoriser.isEqualArray(testArray1, testArray2)).toBeTrue();
    });

    it('returns false if the arrays are different lengths', () => {
      const testArray1 = ['foo', 'bar', 'blah'];
      const testArray2 = ['foo', 'bar', 'blah', 'testing'];
      expect(WeightedPhraseCategoriser.isEqualArray(testArray1, testArray2)).toBeFalse();
      expect(WeightedPhraseCategoriser.isEqualArray(testArray2, testArray1)).toBeFalse();
    });

    it('returns false if the arrays contain different values', () => {
      const testArray1 = ['foo', 'bar', 'blah'];
      const testArray2 = ['x', 'y', 'z'];
      expect(WeightedPhraseCategoriser.isEqualArray(testArray1, testArray2)).toBeFalse();
    });

    it('returns false if the arrays contain the same values in a different order', () => {
      const testArray1 = ['foo', 'bar', 'blah'];
      const testArray2 = ['bar', 'blah', 'foo'];
      expect(WeightedPhraseCategoriser.isEqualArray(testArray1, testArray2)).toBeFalse();
    });
  });
});

// -------------------------------------------------------------------------------------------------

describe('ContributingPhrase', () => {
  describe('constructor()', () => {
    it('throws an error if the phrase is an empty array', () => {
      expect(() => {
        new wpc.ContributingPhrase([], 100); // eslint-disable-line no-new
      }).toThrow();
    });

    it('throws an error if the phrase contains an empty string', () => {
      expect(() => {
        new wpc.ContributingPhrase(['foo', '', 'bar'], 100); // eslint-disable-line no-new
      }).toThrow();
    });
  });
});

// -------------------------------------------------------------------------------------------------

describe('FoundCategory', () => {
  describe('constructor()', () => {
    it('initialises the category ID', () => {
      const foundCategory = new wpc.FoundCategory('12345');
      expect(foundCategory.id).toEqual('12345');
    });

    it('throws an error if the category ID is empty', () => {
      expect(() => {
        new wpc.FoundCategory(''); // eslint-disable-line no-new
      }).toThrow();
    });
  });

  // -------------------------------------------------------------------------------------------------

  describe('totalScore()', () => {
    it('returns zero if no contributing phrases have been added', () => {
      const foundCategory = new wpc.FoundCategory('12345');
      expect(foundCategory.totalScore).toEqual(0);
    });

    it('returns the sum of all scores added so far', () => {
      const foundCategory = new wpc.FoundCategory('12345');
      foundCategory.contributingPhrases.push(new wpc.ContributingPhrase(['blah'], 14));
      foundCategory.contributingPhrases.push(new wpc.ContributingPhrase(['foo', 'bar', 'xyz'], 57));
      foundCategory.contributingPhrases.push(new wpc.ContributingPhrase(['hello', 'world'], 62));
      expect(foundCategory.totalScore).toEqual(133);
    });

    it('allows negative scores', () => {
      const foundCategory = new wpc.FoundCategory('12345');
      foundCategory.contributingPhrases.push(new wpc.ContributingPhrase(['blah'], 14));
      foundCategory.contributingPhrases.push(new wpc.ContributingPhrase(['foo', 'bar', 'xyz'], 57));
      foundCategory.contributingPhrases.push(new wpc.ContributingPhrase(['hello', 'world'], -92));
      expect(foundCategory.totalScore).toEqual(-21);
    });

    it('only counts the most recent score if the same phrase was added multiple times', () => {
      const foundCategory = new wpc.FoundCategory('12345');
      foundCategory.addContributingPhrase(['blah'], 14);
      foundCategory.addContributingPhrase(['foo', 'bar'], 57);
      foundCategory.addContributingPhrase(['blah'], -192);
      foundCategory.addContributingPhrase(['hello', 'world', 'xyz'], 62);
      foundCategory.addContributingPhrase(['blah'], 89);
      expect(foundCategory.totalScore).toEqual(208);
    });
  });

  // -------------------------------------------------------------------------------------------------

  describe('getContributingPhrase()', () => {
    it('returns undefined if no contributing phrases have been added yet', () => {
      const foundCategory = new wpc.FoundCategory('12345');
      expect(foundCategory.getContributingPhrase(['blah'])).toBeUndefined();
    });

    it('returns undefined if the specified phrase was not found', () => {
      const foundCategory = new wpc.FoundCategory('12345');
      foundCategory.contributingPhrases.push(new wpc.ContributingPhrase(['blah'], 14));
      foundCategory.contributingPhrases.push(new wpc.ContributingPhrase(['foo', 'bar', 'xyz'], 57));
      foundCategory.contributingPhrases.push(new wpc.ContributingPhrase(['hello', 'world'], 62));

      expect(foundCategory.getContributingPhrase(['testing'])).toBeUndefined();
    });

    it('returns undefined if the phrase only matched partially', () => {
      const foundCategory = new wpc.FoundCategory('12345');
      foundCategory.contributingPhrases.push(new wpc.ContributingPhrase(['blah'], 14));
      foundCategory.contributingPhrases.push(new wpc.ContributingPhrase(['foo', 'bar', 'xyz'], 57));
      foundCategory.contributingPhrases.push(new wpc.ContributingPhrase(['hello', 'world'], 62));
      expect(foundCategory.getContributingPhrase(['blah', 'xyz'])).toBeUndefined();
      expect(foundCategory.getContributingPhrase(['foo'])).toBeUndefined();
    });

    it('returns the specified contributing phrase object if a match was found', () => {
      const foundCategory = new wpc.FoundCategory('12345');
      foundCategory.contributingPhrases.push(new wpc.ContributingPhrase(['blah'], 14));
      foundCategory.contributingPhrases.push(new wpc.ContributingPhrase(['foo', 'bar', 'xyz'], 57));
      foundCategory.contributingPhrases.push(new wpc.ContributingPhrase(['hello', 'world'], 62));

      expect(foundCategory.getContributingPhrase(['blah'])?.score).toEqual(14);
      expect(foundCategory.getContributingPhrase(['foo', 'bar', 'xyz'])?.score).toEqual(57);
      expect(foundCategory.getContributingPhrase(['hello', 'world'])?.score).toEqual(62);
    });
  });

  // -------------------------------------------------------------------------------------------------

  describe('addContributingPhrase()', () => {
    it('adds the specified phrase if it did not already exist', () => {
      const foundCategory = new wpc.FoundCategory('12345');
      foundCategory.addContributingPhrase(['blah'], 14);
      expect(foundCategory.contributingPhrases.length).toEqual(1);
      expect(foundCategory.contributingPhrases[0].text).toEqual(['blah']);
      expect(foundCategory.contributingPhrases[0].score).toEqual(14);

      foundCategory.addContributingPhrase(['foo', 'bar', 'xyz'], 57);
      expect(foundCategory.contributingPhrases.length).toEqual(2);
      expect(foundCategory.contributingPhrases[1].text).toEqual(['foo', 'bar', 'xyz']);
      expect(foundCategory.contributingPhrases[1].score).toEqual(57);
    });

    it('updates the score of the existing entry if the same phrase had already been added', () => {
      const foundCategory = new wpc.FoundCategory('12345');
      foundCategory.addContributingPhrase(['blah'], 14);
      foundCategory.addContributingPhrase(['blah'], -34);
      foundCategory.addContributingPhrase(['blah'], 99);
      expect(foundCategory.contributingPhrases.length).toEqual(1);
      expect(foundCategory.contributingPhrases[0].text).toEqual(['blah']);
      expect(foundCategory.contributingPhrases[0].score).toEqual(99);
    });
  });
});

// -------------------------------------------------------------------------------------------------

describe('WeightedPhraseCategoriser.Results', () => {
  describe('getAllCategories()', () => {
    it('returns an empty set if there no results have been stored yet', () => {
      const result = new wpc.WeightedPhraseCategoriserResult();
      expect(result.getAllCategoryIds()).toEqual(new Set());
    });

    it('returns a set of all the category IDs which occur in the results', () => {
      const result = new wpc.WeightedPhraseCategoriserResult();
      result.addToCategory('123', ['blah'], 14);
      result.addToCategory('123', ['foo', 'bar'], 22);
      result.addToCategory('456', ['foo', 'bar'], 57);
      result.addToCategory('789', ['hello', 'world', 'xyz'], 62);

      expect(result.getAllCategoryIds()).toEqual(new Set(['123', '456', '789']));
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('getThresholdedCategories()', () => {
    it('returns an empty set if there no results have been stored yet', () => {
      const result = new wpc.WeightedPhraseCategoriserResult();
      expect(result.getThresholdedCategoryIds()).toEqual(new Set());
    });

    it('returns a set of all the category IDs whose total score is greater than or equal to the specified threshold', () => {
      const result = new wpc.WeightedPhraseCategoriserResult();
      result.addToCategory('1', ['blah'], 71);

      result.addToCategory('2', ['blah'], 72);

      result.addToCategory('3', ['blah'], 73);

      result.addToCategory('4', ['blah'], 53);
      result.addToCategory('4', ['foo'], 9);
      result.addToCategory('4', ['bar'], 28);

      result.addToCategory('5', ['blah'], 120);
      result.addToCategory('5', ['foo'], -80);

      expect(result.getThresholdedCategoryIds(72)).toEqual(new Set(['2', '3', '4']));
    });

    it('defaults to a threshold of 100', () => {
      const result = new wpc.WeightedPhraseCategoriserResult();
      result.addToCategory('1', ['blah'], 99);
      result.addToCategory('2', ['blah'], 100);
      result.addToCategory('3', ['blah'], 101);

      expect(result.getThresholdedCategoryIds()).toEqual(new Set(['2', '3']));
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('getCategory()', () => {
    it('returns undefined if the object contains no found categories', () => {
      const result = new wpc.WeightedPhraseCategoriserResult();
      expect(result.getCategory('1234')).toBeUndefined();
    });

    it('returns undefined if the specified category was not found', () => {
      const result = new wpc.WeightedPhraseCategoriserResult();
      result.addToCategory('123', ['blah'], 14);
      result.addToCategory('456', ['foo', 'bar'], 57);
      result.addToCategory('789', ['hello', 'world', 'xyz'], 62);

      expect(result.getCategory('12345')).toBeUndefined();
    });

    it('returns the specified category if it was found', () => {
      const result = new wpc.WeightedPhraseCategoriserResult();
      result.addToCategory('123', ['blah'], 14);
      result.addToCategory('456', ['foo', 'bar'], 57);
      result.addToCategory('789', ['hello', 'world', 'xyz'], 62);

      expect(result.getCategory('456')?.getContributingPhrase(['foo', 'bar'])?.score).toEqual(57);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('addToCategory()', () => {
    it('creates a new category entry if it does not exist', () => {
      const result = new wpc.WeightedPhraseCategoriserResult();
      result.addToCategory('123', ['blah'], 14);

      expect(result.foundCategories.length).toEqual(1);
      const foundCategory = result.getCategory('123');
      expect(foundCategory).not.toBeUndefined();
      expect(foundCategory?.getContributingPhrase(['blah'])?.score).toEqual(14);
      expect(foundCategory?.totalScore).toEqual(14);
    });

    it('adds the phrase to an existing category entry if it already exists', () => {
      const result = new wpc.WeightedPhraseCategoriserResult();
      result.addToCategory('123', ['blah'], 14);
      result.addToCategory('123', ['foo', 'bar'], 57);

      expect(result.foundCategories.length).toEqual(1);
      const foundCategory = result.getCategory('123');
      expect(foundCategory).not.toBeUndefined();
      expect(foundCategory?.contributingPhrases.length).toEqual(2);
      expect(foundCategory?.getContributingPhrase(['blah'])?.score).toEqual(14);
      expect(foundCategory?.getContributingPhrase(['foo', 'bar'])?.score).toEqual(57);
    });

    it('allows the same phrase to be added to different categories with different scores', () => {
      const result = new wpc.WeightedPhraseCategoriserResult();
      result.addToCategory('123', ['blah'], 14);
      result.addToCategory('456', ['blah'], 23);

      expect(result.getCategory('123')?.getContributingPhrase(['blah'])?.score).toEqual(14);
      expect(result.getCategory('456')?.getContributingPhrase(['blah'])?.score).toEqual(23);
    });

    it('treats standalone parent phrases separately from their child phrases', () => {
      const result = new wpc.WeightedPhraseCategoriserResult();
      result.addToCategory('123', ['foo'], 14);
      result.addToCategory('123', ['foo', 'bar'], 57);

      expect(result.getCategory('123')?.getContributingPhrase(['foo'])?.score).toEqual(14);
      expect(result.getCategory('123')?.getContributingPhrase(['foo', 'bar'])?.score).toEqual(57);
    });

    it('updates the existing score if an identical phrase has already been added to the same category', () => {
      const result = new wpc.WeightedPhraseCategoriserResult();
      const text = ['hello', 'world', 'xyz'];
      result.addToCategory('789', text, 62);
      result.addToCategory('789', text, -37);
      result.addToCategory('789', text, 15);

      expect(result.getCategory('789')?.contributingPhrases.length).toEqual(1);
      expect(result.getCategory('789')?.getContributingPhrase(text)?.score).toEqual(15);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('clear()', () => {
    it('does nothing if there are no results stored', () => {
      const result = new wpc.WeightedPhraseCategoriserResult();
      expect(() => {
        result.clear();
      }).not.toThrow();
    });

    it('deletes all of the stored results', () => {
      const result = new wpc.WeightedPhraseCategoriserResult();
      result.addToCategory('123', ['blah'], 14);
      result.addToCategory('123', ['foo', 'bar'], 22);
      result.addToCategory('456', ['foo', 'bar'], 57);
      result.addToCategory('789', ['hello', 'world', 'xyz'], 62);

      result.clear();
      expect(result.foundCategories.length).toEqual(0);
      expect(result.getCategory('123')).toBeUndefined();
    });
  });
});

// -------------------------------------------------------------------------------------------------

describe('WeightedPhraseCategoriser.isValidWeightedPhraseData()', () => {
  it('returns true if the specified value matches the WeightedPhraseData structure', () => {
    const obj = {
      catsAndScores: [
        {
          category: 111,
          score: 100,
        },
        {
          category: 222,
          score: 100,
        },
      ],
      children: [
        {
          phrases: ['foo', 'bar'],
          catsAndScores: {
            category: 333,
            score: 100,
          },
        },
        {
          phrases: ['blah'],
          catsAndScores: {
            category: 444,
            score: 100,
          },
        },
      ],
    };
    expect(wpc.isValidWeightedPhraseData(obj)).toBeTrue();
  });

  it('returns false if the specified value is null', () => {
    expect(wpc.isValidWeightedPhraseData(null)).toBeFalse();
  });

  it('returns false if the specified value is undefined', () => {
    expect(wpc.isValidWeightedPhraseData(null)).toBeFalse();
  });

  it('returns false if the specified value is not an object', () => {
    expect(wpc.isValidWeightedPhraseData(123)).toBeFalse();
  });

  it('returns false if a property is missing', () => {
    const obj = { children: [] };
    expect(wpc.isValidWeightedPhraseData(obj)).toBeFalse();
  });

  it('returns false if a property has the wrong type', () => {
    const obj = { catsAndScores: 123, children: [] };
    expect(wpc.isValidWeightedPhraseData(obj)).toBeFalse();
  });
});

// -------------------------------------------------------------------------------------------------

describe('WeightedPhraseCategoriser.isValidWeightedPhraseCategory()', () => {
  it('returns true if the specified value matches the WeightedPhraseCategory structure', () => {
    expect(wpc.isValidWeightedPhraseCategory({ category: 111, score: 100 })).toBeTrue();
    expect(wpc.isValidWeightedPhraseCategory({ category: 'abc', score: 100 })).toBeTrue();
  });

  it('returns false if the specified value is null', () => {
    expect(wpc.isValidWeightedPhraseCategory(null)).toBeFalse();
  });

  it('returns false if the specified value is undefined', () => {
    expect(wpc.isValidWeightedPhraseCategory(null)).toBeFalse();
  });

  it('returns false if the specified value is not an object', () => {
    expect(wpc.isValidWeightedPhraseCategory(123)).toBeFalse();
  });

  it('returns false if a property is missing', () => {
    expect(wpc.isValidWeightedPhraseCategory({ score: 100 })).toBeFalse();
  });

  it('returns false if a property has the wrong type', () => {
    expect(wpc.isValidWeightedPhraseCategory({ category: true, score: 100 })).toBeFalse();
  });
});

// -------------------------------------------------------------------------------------------------

describe('WeightedPhraseCategoriser.isValidWeightedPhraseChild()', () => {
  it('returns true if the specified value matches the WeightedPhraseChild structure', () => {
    const obj = {
      phrases: ['foo', 'bar'],
      catsAndScores: {
        category: 333,
        score: 100,
      },
    };
    expect(wpc.isValidWeightedPhraseChild(obj)).toBeTrue();
  });

  it('returns false if the specified value is null', () => {
    expect(wpc.isValidWeightedPhraseChild(null)).toBeFalse();
  });

  it('returns false if the specified value is undefined', () => {
    expect(wpc.isValidWeightedPhraseChild(null)).toBeFalse();
  });

  it('returns false if the specified value is not an object', () => {
    expect(wpc.isValidWeightedPhraseChild(123)).toBeFalse();
  });

  it('returns false if a property is missing', () => {
    const obj = { phrases: [] };
    expect(wpc.isValidWeightedPhraseChild(obj)).toBeFalse();
  });

  it('returns false if a property has the wrong type', () => {
    const obj = {
      phrases: ['foo', 'bar'],
      catsAndScores: {
        category: 333,
        score: true,
      },
    };
    expect(wpc.isValidWeightedPhraseChild(obj)).toBeFalse();
  });

  it('returns false there are no child phrases', () => {
    const obj = {
      phrases: [],
      catsAndScores: {
        category: 333,
        score: 100,
      },
    };
    expect(wpc.isValidWeightedPhraseChild(obj)).toBeFalse();
  });

  it('returns false if a child phrase is empty', () => {
    const obj = {
      phrases: ['foo', '', 'bar'],
      catsAndScores: {
        category: 333,
        score: 100,
      },
    };
    expect(wpc.isValidWeightedPhraseChild(obj)).toBeFalse();
  });

  it('returns false if a child phrase is not a string', () => {
    const obj = {
      phrases: ['foo', 101],
      catsAndScores: {
        category: 333,
        score: 100,
      },
    };
    expect(wpc.isValidWeightedPhraseChild(obj)).toBeFalse();
  });
});

// -------------------------------------------------------------------------------------------------

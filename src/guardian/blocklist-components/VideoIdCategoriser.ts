import { applyPatch } from 'fast-json-patch';
import CategoriserResult from 'guardian/models/CategoriserResult';
import DiffMap from 'guardian/patching/DiffMap';
import UnifiedDiffTarget from 'guardian/patching/UnifiedDiffTarget';
import { ICustomCategory } from 'models/PolicyConfigModels';
import { cleanRegexp } from 'utilities/Helpers';

import CategorisationMap from '../utilities/CategorisationMap';
import SearchTermCategoriser from './SearchTermCategoriser';

/**
 * Read-only interface for a blocklist component which categorises a URL based on video IDs.
 *
 * @see VideoIdCategoriser
 */
export interface IVideoIdCategoriser {
  /**
   * Get the number of video IDs in the categorisation map.
   * This is not affected by the number of extraction patterns.
   *
   * @note This does not check for duplicates. If the same video ID appears multiple times then each
   *  instance will be counted.
   */
  length: number;

  /**
   * Get the categories which the specified URL matches based on any video IDs it contains.
   *
   * @param url The URL to categorise.
   * @param customCategories An optional list of any custom categories to apply to the video ids.
   * @returns Returns an object containing all the video IDs which were found, and all the
   *  associated category IDs.
   */
  categoriseUrl: (url: URL, customCategories?: ICustomCategory[]) => VideoIdCategoriserResult;

  /**
   * Extract any video IDs found in the specified URL.
   *
   * @param url The URL to extract video IDs from. Note that video IDs are typically case-sensitive
   *  so this should not have been converted to lower case.
   * @returns Returns a set of video IDs found in the URL. It will be empty if none were found.
   *  There normally won't be more than one video ID in a URL. If multiple distinct video IDs are
   *  found then it may mean that it matched multiple different extraction patterns.
   */
  extractVideoIdsFromUrl: (url: URL) => Set<string>;
}

/**
 * Blocklist component which categorises a URL based on video IDs it contains.
 * This uses regular expressions to find video IDs in URLs from various providers, such as YouTube.
 * It then uses list of known video IDs to map onto category IDs.
 *
 * Video IDs are extracted in almost exactly the same way as search terms when analysing search
 *  URLs. The IDs are then categorised using a lookup table (aka categorisation map) which follows
 *  the same general structure as the domains/URLs lists used for categorisation known URLs. It just
 *  uses video IDs instead of URLs as the look up key.
 *
 * @see SearchTermCategoriser
 * @see https://familyzone.atlassian.net/wiki/spaces/DST/pages/2694335858368/Components#Search-term-extraction
 * @see UrlListCategoriser
 * @see https://familyzone.atlassian.net/wiki/spaces/DST/pages/2694335858368/Components#Domain%2FURL-filtering
 *
 * @todo Split this class into two separate classes, one for each blocklist file. It currently
 *  manages two blocklist files. The first file is "videoidregexplist", which contains regular
 *  expressions for extracting video IDs from URLs. The second file is "videoids", which defines how
 *  to categorise known video IDs. Both files are patched via Unified Diff, which is confusing. The
 *  patching logic for the extraction patterns is implemented on this class (see UnifiedDiffTarget),
 *  and the patching logic for the video IDs is implemented on the CategorisationMap class.
 */
export default class VideoIdCategoriser implements IVideoIdCategoriser, UnifiedDiffTarget {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance, optionally initialising the regular expressions and categories.
   *
   * @param extractionPatterns An array of regular expressions which will extract video IDs from
   *  URLs via capture groups.
   * @param categorisationMap A mapping of video IDs to their associated category identifiers.
   *
   * @warning The specified extraction patterns and categorisation map are not cloned. It is the
   *  caller's responsibility to ensure nothing else modifies them while they are being used by this
   *  object.
   *
   * @note The extraction patterns cannot be patched from Unified Diff if they are initialised
   *  here. Patching is only possible if the extraction patterns are loaded from a blocklist file by
   *  calling loadExtractionPatternsFromBlocklistFile().
   */
  public constructor(extractionPatterns?: RegExp[], categorisationMap?: CategorisationMap) {
    if (extractionPatterns !== undefined) {
      this.setExtractionPatterns(extractionPatterns);
    }

    if (categorisationMap !== undefined) {
      this.setCategorisationMap(categorisationMap);
    }
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Set the regular expressions which will be used to extract video IDs from URLs.
   *
   * @param extractionPatterns An array of regular expressions which will extract video IDs from
   *  URLs via capture groups.
   *
   * @warning The specified extraction patterns are not cloned. It is the caller's responsibility to
   *  ensure nothing else modifies them while they are being used by this object.
   *
   * @note The extraction patterns cannot be patched from Unified Diff after calling this. Patching
   *  is only possible if the extraction patterns were loaded from a blocklist file by calling
   *  loadExtractionPatternsFromBlocklistFile().
   */
  public readonly setExtractionPatterns = (extractionPatterns: RegExp[]): void => {
    this._extractionPatterns = extractionPatterns;
    // Extraction patterns have been set directly from some other source. We can't reliably convert
    //  the parsed extraction patterns back to the original blocklist file representation. As a
    //  result, we cannot safely patch data provided this way. Discard any data we previously loaded
    //  from the blocklist to ensure that patches aren't applied to the wrong thing.
    this._originalExtractionPatternsData = null;
  };

  /**
   * Get the category identifiers which correspond to each video ID.
   * The returned object is responsible for applying Unified Diff patches to the video ID data, not
   *  the extraction patterns.
   * It operates on the original data loaded from the blocklist file so it can safely be patched by
   *  Unified Diff even the original data wasn't loaded through this object.
   *
   * @return The mapping of video IDs to their associated category identifiers. This object is
   *  returned by reference to enable patching. However, the entire object may be replaced if new
   *  video ID data is loaded, meaning old references will effectively be invalidated.
   */
  public readonly getCategorisationMap = (): CategorisationMap => {
    return this._categorisationMap;
  };

  /**
   * Set the category identifiers which correspond to each video ID.
   *
   * @param map A mapping of video IDs to their associated category identifiers.
   *
   * @warning The specified categorisation map is not cloned. It is the caller's responsibility to
   *  ensure nothing else modifies it while it is being used by this object.
   */
  public readonly setCategorisationMap = (categorisationMap: CategorisationMap): void => {
    this._categorisationMap = categorisationMap;
  };

  /**
   * Get the number of video IDs in the categorisation map.
   * This is not affected by the number of extraction patterns.
   *
   * @note This does not check for duplicates. If the same video ID appears multiple times then each
   *  instance will be counted.
   */
  public get length(): number {
    return this._categorisationMap.length;
  }

  // -----------------------------------------------------------------------------------------------
  // Loading/saving.

  /**
   * Load the extraction patterns from a blocklist file.
   * The extraction patterns are regular expressions which extract video IDs from URLs.
   * This will replace all existing extraction patterns in this object. Any existing categorisation
   *  data will not be affected.
   *
   * @param data The contents of the blocklist file to load. This must be formatted as plain-text,
   *  with one regular expression per line. Each pattern should be enclosed with single quotes and
   *  followed by a comma.
   */
  public loadExtractionPatternsFromBlocklistFile = (data: string): void => {
    this._originalExtractionPatternsData = null;
    const originalLines = data.split('\n');
    this._extractionPatterns =
      VideoIdCategoriser.parseExtractionPatternsFromBlocklistFile(originalLines);
    this._originalExtractionPatternsData = originalLines;
  };

  /**
   * Load the categorisation map from a blocklist file.
   * The categorisation map lists which categories each video ID belongs to.
   * This will replace all existing categorisation data in this object. Any existing extraction
   *  patterns will not be affected.
   *
   * @param data The contents of the blocklist file to load. This must be formatted as JSON, with
   *  an array at the top level. Each array element should be a string containing a video ID,
   *  followed by a pipe character (|), then a comma-separated list of category IDs.
   */
  public loadCategorisationMapFromBlocklistFile = (data: string): void => {
    const parsedVideoIds = JSON.parse(data);
    if (!Array.isArray(parsedVideoIds)) {
      throw new TypeError('Failed to parse video IDs list. Expected an array.');
    }
    this._videoIds = parsedVideoIds;
    this.setCategorisationMap(new CategorisationMap(this._videoIds, '|', ',', true));
  };

  /**
   * Patch the current video id categorisation map using the given patch file data.
   * @param patch The patch data from the blocklist file to load.
   */
  public patchCategorisationMapFromBlocklistFile = (patch: any): void => {
    const newIds = applyPatch(this._videoIds, patch).newDocument;

    if (!Array.isArray(newIds)) {
      throw new TypeError('Failed to parse video IDs list. Expected an array.');
    }
    this._videoIds = newIds;
    this.setCategorisationMap(new CategorisationMap(this._videoIds, '|', ',', true));
  };

  public patchExtractionPatternsFromBlocklistFile = (line: string, isAddition: boolean): void => {
    const newLine: RegExp = new RegExp(
      cleanRegexp(line.replace("+'", '').replace("',", ''), false),
    );

    if (isAddition) {
      // Add it
      this._extractionPatterns.push(newLine);
    } else {
      this._extractionPatterns = this._extractionPatterns.filter(
        (x) => x.toString() !== newLine.toString(),
      );
    }
  };

  /**
   * Delete all stored extraction patterns and categories.
   */
  public readonly clear = (): void => {
    this._originalExtractionPatternsData = null;
    this._extractionPatterns = [];

    // Don't use the clear() function as that modifies the original object which was passed-in.
    this._categorisationMap = new CategorisationMap(undefined, undefined, undefined, true);
  };

  // -----------------------------------------------------------------------------------------------
  // Unified Diff patching (extraction patterns).

  /**
   * Use the given object to patch the extraction patterns in memory.
   *
   * @param patcher An object which will apply the relevant blocklist diff to an in-memory array.
   * @throws {Error} The extraction patterns cannot be patched as they were not loaded from a
   *  blocklist file.
   */
  public readonly patchFromUnifiedDiff = (patcher: DiffMap): void => {
    if (this._originalExtractionPatternsData === null) {
      throw new Error(
        'Cannot patch video ID extraction patterns. The data was not loaded from a blocklist file.',
      );
    }

    // Apply the patch to a raw copy of the original blocklist data, then reparse the whole thing.
    // This is to ensure we can reliably store the result of the patch, and it gives us the option
    //  to verify the result of patching in future.
    // TODO: In future, maybe patch both versions in parallel if it's more efficient than re-parsing
    //  the whole file. It would require adding DiffMap support for non-string values.
    patcher.patchArrayInPlace(this._originalExtractionPatternsData);
    this._extractionPatterns = VideoIdCategoriser.parseExtractionPatternsFromBlocklistFile(
      this._originalExtractionPatternsData,
    );
  };

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Get the categories which the specified URL matches based on any video IDs it contains.
   *
   * @param url The URL to categorise.
   * @param customCategories An optional list of any custom categories to apply to the video ids.
   * @returns Returns an object containing all the video IDs which were found, and all the
   *  associated category IDs.
   */
  public readonly categoriseUrl = (
    url: URL,
    customCategories: ICustomCategory[] = [],
  ): VideoIdCategoriserResult => {
    const output = new VideoIdCategoriserResult();

    // Go through each video ID extracted from the URL. (There will usually be zero or one.)
    output.videoIds = this.extractVideoIdsFromUrl(url);
    output.videoIds.forEach((videoId: string): void => {
      // Add each category ID from the blocklist to the output.
      this._categorisationMap.lookupCategories(videoId).forEach((categoryId: string): void => {
        output.categoryIds.add(categoryId);
      });

      // Always check custom categories regardless of whether blocklist categories were found
      customCategories.forEach((category) => {
        if (category.component?.videoids === undefined || category.category_id === undefined) {
          return;
        }

        if (category.component.videoids.some((id) => id === videoId)) {
          output.categoryIds.add(category.category_id);
        }
      });
    });

    return output;
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Extract any video IDs found in the specified URL.
   *
   * @param url The URL to extract video IDs from. Note that video IDs are typically case-sensitive
   *  so this should not have been converted to lower case.
   * @returns Returns a set of video IDs found in the URL. It will be empty if none were found.
   *  There normally won't be more than one video ID in a URL. If multiple distinct video IDs are
   *  found then it may mean that it matched multiple different extraction patterns.
   */
  public readonly extractVideoIdsFromUrl = (url: URL): Set<string> => {
    const urlString = url.toString();
    const output = new Set<string>();

    // Run the URL through each regular expression. Store every unique matched capture group.
    this._extractionPatterns.forEach((pattern: RegExp): void => {
      pattern.lastIndex = 0; // <-- RegExp is stateful! Reset it to ensure consistent behaviour.
      pattern
        .exec(urlString)
        ?.slice(1)
        .forEach((match: string) => {
          // Only add non-empty matches to prevent false positives
          if (match.trim() !== '') {
            output.add(match);
          }
        });
    });

    return output;
  };

  /**
   * Parse a single extraction pattern entry from the blocklist file.
   * For details of the format see: SearchTermCategoriser.parseExtractionPatternFromBlocklistLine()
   *
   * @param line A single line from the video ID regular expression blocklist file. It should not
   *  contain any line break characters.
   * @returns A regular expression object constructed by parsing the specified line.
   * @throws {Error} The line did not match the expected structure, or the regular expression was
   *  invalid.
   *
   * @todo Move the extraction pattern logic out into a separate blocklist component. The same class
   *  can probably be used for both video ID extraction and search term extraction, hence why we
   *  delegate to the search term implementation of this function here.
   */
  public static readonly parseExtractionPatternFromBlocklistLine =
    SearchTermCategoriser.parseExtractionPatternFromBlocklistLine;

  /**
   * Parse a list of extraction patterns from a blocklist file which has been split into lines.
   * This will skip lines which are completely empty, or which contain no regular expression.
   *
   * @param lines An array of individual lines from the blocklist file.
   * @return An array of regular expression objects parsed from the blocklist file entries. It will
   *  omit any lines which were empty.
   * @throws {Error} One or more non-empty lines did not match the expected format, or contained an
   *  invalid regular expression.
   *
   * @todo Move the extraction pattern logic out into a separate blocklist component. The same class
   *  can probably be used for both video ID extraction and search term extraction, hence why we
   *  delegate to the search term implementation of this function here.
   */
  public static parseExtractionPatternsFromBlocklistFile =
    SearchTermCategoriser.parseExtractionPatternsFromBlocklistFile;

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The original data from the video ID regex file in the blocklist, split into lines.
   * This will only be populated if the regular expressions were populated by loading a blocklist
   *  file. It will be null if they were set some other way.
   * It's stored to facilitate blocklist patching via Unified Diff. Patch operations will alter this
   *  array. It will then be re-parsed into the _extractionPatterns array when patching is finished.
   */
  private _originalExtractionPatternsData: string[] | null = null;

  /**
   * Regular expressions used to match video IDs in a URL.
   * Having multiple patterns allows us to handle any number of URL formats from any number of
   *  websites. For example, YouTube URLs will need a different pattern from DailyMotion.
   */
  private _extractionPatterns: RegExp[] = [];

  /**
   * A list of videos IDs and the categories they belong to, sorted by video ID.
   * Each element is a string formatted as "videoID|category,category,...]", where each "category"
   *  is a category identifier string. There may 1 or more categories for each video ID.
   */
  private _categorisationMap: CategorisationMap = new CategorisationMap(
    undefined,
    undefined,
    undefined,
    true,
  );

  private _videoIds: any = '';
}

// -------------------------------------------------------------------------------------------------

/**
 * Contains the result of a URL categorisation by video ID.
 */
export class VideoIdCategoriserResult implements CategoriserResult {
  /**
   * A set of all the video IDs which were in the URL, whether or not they matched any categories.
   * The format of a video ID depends on which platform it came from, e.g. YouTube, Vimeo, etc.
   * This will be an empty set if no video IDs were found in the URL.
   */
  public videoIds = new Set<string>();

  /**
   * Lists the categories IDs associated with the found video IDs.
   * This will be an empty set if no video IDs were found in the URL, or the video IDs did not match
   *  any known categories.
   */
  public categoryIds = new Set<string>();
}

import CryptoJS, { SHA1 } from 'crypto-js';
import * as GuardianUtilities from '../utilities/GuardianUtilities';

/**
 * Read-only interface for a blocklist component which checks an IWF list for dangerous URLs.
 * This can be used to look up a URL to see if it is in the list.
 * This interface deliberately only exposes functions which can use the list, but not modify it.
 *
 * @see IwfList
 */
export interface IIwfList {
  /**
   * Get the number of hashed URLs currently stored in this object.
   *
   * @returns Returns the number of hashed URLs currently stored in this object.
   */
  length: number;

  /**
   * Check if the specified URL occurs in the list.
   * This does a fuzzy match, looking for variations of the URL which might point to the same
   *  resource. It also checks if any parent path occurs in the list. For example, if the list
   *  contains "example.com/foo" then this will also match "example.com/foo/bar".
   *
   * @param {URL} url The URL to look for. The protocol prefix (e.g. "http://") is ignored.
   * @returns {boolean} Returns true if the URL, or something very similar, occurs in the IWF list.
   *  Returns false otherwise.
   */
  contains: (url: URL) => boolean;
}

/**
 * A blocklist component which checks an IWF list for dangerous URLs.
 * The URLs are stored in hashed form so that they can't be read.
 *
 * @example
 *
 *    const iwfList = new IwfList();
 *    iwfList.loadFromBlocklistFile('[...]');
 *    if (iwfList.contains(new URL('https://www.example.com/foo/bar'))) {
 *      // Block URL here
 *    }
 *
 * @see https://smoothwall-dev.atlassian.net/wiki/spaces/DST/pages/430768145/IWF
 */
export default class IwfList implements IIwfList {
  // -----------------------------------------------------------------------------------------------
  // Constructions.

  /**
   * Optionally initialise the stored array of hashed URLs on construction.
   *
   * @param hashedUrls An optional array of URLs which have been converted to lower-case,
   *  SHA1-hased, and hex-encoded. If this is undefined then the object will initially contain no
   *  URLs.
   */
  public constructor(hashedUrls?: string[]) {
    if (hashedUrls !== undefined) {
      this.setHashedUrls(hashedUrls);
    }
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Store the specified hashed URLs.
   * This replaces any existing data stored by this object.
   *
   * @param hashedUrls An array of URLs which have been converted to lower-case, SHA1-hased, and
   *  hex-encoded.
   */
  public readonly setHashedUrls = (hashedUrls: string[]): void => {
    // TODO: In debug mode, validate that the URLs appear to be valid hashes?
    this._hashedUrls = new Set<string>(hashedUrls);
  };

  /**
   * Get the number of hashed URLs currently stored in this object.
   *
   * @returns Returns the number of hashed URLs currently stored in this object.
   */
  public get length(): number {
    return this._hashedUrls.size;
  }

  // -----------------------------------------------------------------------------------------------
  // Loading/saving.

  /**
   * Load the hashed IWF URLs from blocklist file.
   * This replaces any which were loaded previously.
   *
   * @param {string} data The contents of the file to load. This must be formatted as JSON, with an
   *  array at the top-level. Each element of the array should be a string containing a URL which
   *  has been converted to lower-case, SHA1-hashed, and hex-encoded. (Unlike the Guardian
   *  blocklist, the domain should not have been reversed.)
   * @throws {SyntaxError} The specified string contains invalid JSON.
   * @throws {TypeError} The JSON data did not contain an array at the top level.
   */
  public readonly loadFromBlocklistFile = (data: string): void => {
    const parsedData = JSON.parse(data);
    if (!Array.isArray(parsedData)) {
      throw TypeError('Failed to load IwfList from blocklist file. Expected a JSON array.');
    }
    this.setHashedUrls(parsedData);
  };

  /**
   * Delete all the stored URLs.
   * This resets the object back to its original state, before anything was loaded.
   */
  public readonly clear = (): void => {
    this._hashedUrls = new Set<string>();
  };

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Check if the specified URL occurs in the list.
   * This does a fuzzy match, looking for variations of the URL which might point to the same
   *  resource. It also checks if any parent path occurs in the list. For example, if the list
   *  contains "example.com/foo" then this will also match "example.com/foo/bar".
   *
   * @param {URL} url The URL to look for. The protocol prefix (e.g. "http://") is ignored.
   * @returns {boolean} Returns true if the URL, or something very similar, occurs in the IWF list.
   *  Returns false otherwise. This always returns true if no list has been loaded yet, or the data
   *  has been cleared.
   */
  public readonly contains = (url: URL): boolean => {
    // Convert to lower-case before hashing to match the IWF list.
    const lowerUrl: URL = new URL(url.toString().toLowerCase());
    const hashedVariations: string[] = GuardianUtilities.generateUrlVariationsForIwf(lowerUrl).map(
      IwfList.hash,
    );
    return hashedVariations.some((variation: string) => this._hashedUrls.has(variation));
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Hash and encode a URL to match the IWF list format.
   *
   * @param {string} data The data to be hashed. This should be a URL without the protocol prefix.
   *  For example, "example.com/foo/bar"
   * @returns Returns the hashed data, encoded as a hexadecimal string.
   */
  public static readonly hash = (data: string): string => {
    return SHA1(data).toString(CryptoJS.enc.Hex);
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Contains the list of hashed URLs identified as dangerous by the IWF.
   * Each element is a URL which has been converted to lower-case, then SHA1 hashed and hex-encoded.
   * Note: Unlike the Guardian blocklists, the domains are *not* reversed.
   */
  private _hashedUrls: Set<string> = new Set<string>();
}

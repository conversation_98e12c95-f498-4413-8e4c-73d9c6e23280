import { applyPatch } from 'fast-json-patch';
import AccessLogEntry from 'models/AccessLogEntry';
import LogLevelRule from 'models/LogLevelRule';

/**
 * Sets log levels for access logs based on rules.
 *
 * @todo Rename this class. It's a blocklist component not a service. :)
 */
export default class LogLevelService {
  /**
   * Gets the number of parsed rules read from the blocklist file.
   */
  public get length(): number {
    return this._parsedRules.length;
  }

  /**
   * Discard all log level data stored in this object.
   * This resets it back to an empty state.
   */
  public readonly clear = (): void => {
    this._rawRules = [];
    this._parsedRules = [];
  };

  /**
   * Patches the log level rules using the given patch data.
   * @param patch The patch data from the blocklist file to load.
   */
  public readonly patchFromBlocklistFile = (patch: any): void => {
    this._rawRules = applyPatch(this._rawRules, patch).newDocument;
    this._parsedRules = this._parseRules(this._rawRules);
  };

  /**
   * Parses and saves the log level rules from the given blocklist data.
   * @param data stringified data from a received blocklist file containing log level rules.
   */
  public readonly loadFromBlocklistFile = (data: string): void => {
    const rawRules = JSON.parse(data);
    if (!Array.isArray(rawRules)) {
      throw new Error('Expected log level rules file to be an array.');
    }

    this._rawRules = rawRules;
    this._parsedRules = this._parseRules(rawRules);
  };

  /**
   * for each log level rule, skip the rule if it matches a negative test.
   * otherwise, return the rule level if it matches a positive test.
   *
   * @param log the access log to provide a log level for.
   * @returns number between 1 and 5, defaulting to 3 if no match.
   */
  public readonly applyLogLevel = (log: AccessLogEntry): number => {
    for (const rule of this._parsedRules) {
      let exception = false;
      // First check all exclusions in the rule.
      for (const exclusionKey of this._negativeTests) {
        const ruleTests = rule[exclusionKey as keyof LogLevelRule<RegExp>] as RegExp[];
        if (ruleTests !== undefined) {
          if (this._applyTest(ruleTests, log.categories)) {
            exception = true;
            break;
          }
        }
      }

      if (exception) {
        continue;
      }

      // No exclusions matched now check the positive tests.
      for (const [key, tests] of Object.entries(rule)) {
        if (this._positiveTests.includes(key)) {
          if (this._applyTest(tests, log[key as keyof AccessLogEntry])) {
            return rule.level;
          }
        }
      }
    }
    return 3;
  };

  /**
   * Parses the given raw rules into rules with regexps.
   * @param newRules Array of log level rules containing strings.
   */
  private readonly _parseRules = (
    rawRules: Array<LogLevelRule<string>>,
  ): Array<LogLevelRule<RegExp>> => {
    const rules = JSON.parse(JSON.stringify(rawRules));

    for (const rule of rules) {
      for (const key in rule) {
        if (this._positiveTests.includes(key) || this._negativeTests.includes(key)) {
          rule[key] = rule[key].map((pattern: string) => new RegExp(pattern));
        }
      }
    }

    return rules;
  };

  /**
   * Applies the array of tests to the property from a log object.
   * @param tests array of regexps to match against the sample.
   * @param logMember member of the log object.
   * @returns Boolean True if one of the tests matched, false otherwise
   */
  private readonly _applyTest = (tests: RegExp[], logMember: any | any[]): boolean => {
    if (logMember === undefined) return false;

    // TODO can't we just .toString() any log members that are arrays?
    return tests.some((regex) => {
      if (logMember instanceof Array) {
        return logMember.some((entry: string) => regex.test(entry));
      } else return regex.test(logMember.toString());
    });
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Positive test keys supported for log levels.
   */
  private readonly _positiveTests: string[] = [
    'categories',
    'contenttype',
    'method',
    'searchterms',
    'title',
    'safeguardingtheme',
  ];

  /**
   * Negative test keys that exempty log level matching.
   */
  private readonly _negativeTests: string[] = ['exclusions'];

  /**
   * Log level rules in their string form, as the blocklist file stores them
   */
  private _rawRules: Array<LogLevelRule<string>> = [];

  /**
   * Log level rules in their regex form, parsed and ready to be used.
   */
  private _parsedRules: Array<LogLevelRule<RegExp>> = [];
}

import { cleanRegexp } from 'utilities/Helpers';
import CategoriserResult from '../models/CategoriserResult';
import UnifiedDiffTarget from 'guardian/patching/UnifiedDiffTarget';
import DiffMap from 'guardian/patching/DiffMap';

export interface IUrlRegexpCategoriser {
  /**
   * Gets the number of regexp stored in the categorisation map.
   */
  length: number;

  /**
   * Categorises the given url using the url regexps from the blocklist data.
   * @param url The url to categorise.
   * @returns An object with the categories that have been matched to the url.
   */
  categoriseUrl: (url: URL) => UrlRegexpCategoriserResult;
}

/**
 * Blocklist component that categorises a url using regex specified in the blocklist file.
 */
export default class UrlRegexpCategoriser implements UnifiedDiffTarget {
  /**
   * Gets the number of regexp stored in the categorisation map.
   */
  public get length(): number {
    return this._urlRegexpsCategorisationMap.size;
  }

  /**
   * Parses and saves the given regexpurls blocklist file that will be used to categorise a url.
   *
   * This will replace any existing patterns.
   * @param data The regexpurls blocklist file.
   *
   * This must be plain text with each pattern and any matching categories on a single line.
   * Each pattern-category pair must be separated by a tab character and each category should also be separated by a tab.
   */
  public readonly loadFromBlocklistFile = (data: string): void => {
    const originalData = data.split('\n');
    this._urlRegexpsCategorisationMap = this._parseBlocklistFile(originalData);
    // Wait until parsing has finished before updating this value so that they don't get out of sync
    //  if parsing throws an unexpected error.
    this._originalData = originalData;
  };

  /**
   * Use the given object to patch the blocklist data in memory.
   *
   * @param patcher An object which will apply the relevant blocklist diff to an in-memory array.
   */
  public readonly patchFromUnifiedDiff = (patcher: DiffMap): void => {
    // Apply the patch to a raw copy of the original blocklist data, then reparse the whole thing.
    // This is to ensure we can reliably store the result of the patch, and it gives us the option
    //  to verify the result of patching in future.
    // TODO: In future, maybe patch both versions in parallel if it's more efficient than re-parsing
    //  the whole file. It would require adding DiffMap support for non-string values.
    patcher.patchArrayInPlace(this._originalData);
    this._urlRegexpsCategorisationMap = this._parseBlocklistFile(this._originalData);
  };

  /**
   * Clears the currently stored regexpurls map.
   */
  public readonly clearCategorisationMap = (): void => {
    this._originalData = [];
    this._urlRegexpsCategorisationMap = new Map();
  };

  /**
   * Categorises the given url using the url regexps from the blocklist data.
   * @param url The url to categorise.
   * @returns An object with the categories that have been matched to the url.
   */
  public readonly categoriseUrl = (url: URL): UrlRegexpCategoriserResult => {
    const result = new UrlRegexpCategoriserResult();
    if (this.length <= 0) {
      return result;
    }

    this._urlRegexpsCategorisationMap.forEach((categories, regexp) => {
      const regexpResult = regexp.exec(url.toString());
      if (regexpResult !== null) {
        categories.forEach((category) => {
          result.categoryIds.add(category);
        });
      }
    });

    return result;
  };

  /**
   * Parses the lines from a blocklist file into a mapping of regexps to associated categories.
   * @param dataLines The blocklist file contents, split into lines.
   * @returns A map of regex to categories.
   */
  private readonly _parseBlocklistFile = (dataLines: string[]): Map<RegExp, string[]> => {
    const map = new Map<RegExp, string[]>();
    dataLines.forEach((line) => {
      const lineData = this._parseLine(line);

      if (lineData === undefined) {
        return;
      }

      map.set(lineData.regexp, lineData.categories);
    });

    return map;
  };

  /**
   * Parses the given line of blocklist data and parses out the regexp and categories.
   * @param line The line of data to parse.
   * @returns An object with the parsed regexp and the related categories.
   */
  private readonly _parseLine = (
    line: string,
  ): { regexp: RegExp; categories: string[] } | undefined => {
    // The regex and categories are separated by a tab.
    const lineData = line.split('\t');
    if (lineData.length < 2) {
      return;
    }

    const regexp = new RegExp(cleanRegexp(lineData[0], false));

    // Categories are also separated by tabs, so remove the first index (the regex) and use the rest.
    const categories = lineData.slice(1);

    return { regexp, categories };
  };

  /**
   * The original blocklist file data, split up into separate lines, before parsing/validation.
   * When the blocklist is patched, additions and deletions will operate on this array. When
   *  patching has finished, it will be re-parsed to populate the main map. This is because patching
   *  would not be able to operate reliably on the main map.
   * This will be an empty array if no data is loaded yet.
   */
  private _originalData: string[] = [];

  /**
   * A mapping of the url regexp and the related categories.
   * This contains the parsed and validated entries from the _originalData array. Empty or invalid
   *  entries will have been omitted.
   *
   * @todo Probably convert this to an array of objects where each object contains the RegExp and
   *  associated categories. A map doesn't really make sense because we always iterate over every
   *  value to test each one. We never need to lookup the categories via the RegExp.
   */
  private _urlRegexpsCategorisationMap = new Map<RegExp, string[]>();
}

/**
 * Contains the result of URL categorisation by the url regexps.
 */
export class UrlRegexpCategoriserResult implements CategoriserResult {
  /**
   * The IDs of the categories found by the categorisation operation.
   */
  public categoryIds = new Set<string>();
}

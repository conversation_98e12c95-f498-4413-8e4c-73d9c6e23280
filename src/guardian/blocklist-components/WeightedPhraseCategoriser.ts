import AhoCorasick, { AhoCorasickMatch } from 'ahocorasick';
import { applyPatch } from 'fast-json-patch';
import CachedSubstringMatcher from 'guardian/utilities/CachedSubstringMatcher';
import CategoriserResult from 'guardian/models/CategoriserResult';

/**
 * Read-only interface for a blocklist component which categorises page content based on phrases.
 *
 * @see WeightedPhraseCategoriser
 */
export interface IWeightedPhraseCategoriser {
  /**
   * Get the number of weighted phrases which can be categorised.
   * This does not include child phrases.
   */
  length: number;

  /**
   * Get the categories which the specified page content matches based on known phrases it contains.
   *
   * @param content The page content to categorise. This is not case-sensitive.
   * @returns Returns an object containing details of the known phrases found in the specified
   *  content, and their associated scores and categories.
   */
  categoriseContent: (content: string) => WeightedPhraseCategoriserResult;
}

/**
 * Blocklist component which categorises page content based on a list of known phrases.
 * Each phrase can be associated with one or more categories. Each phrase-to-category mapping has an
 *  associated weight (aka score) indicating how strong the association is. By extension, this tells
 *  us how confident the categorisation is.
 * The scores from multiple phrases can be summed to provide an overall score for a given category.
 * A categorisation is only established if the score reaches a predefined threshold.
 * Negative scores are also possible, allowing us to identify phrases which effectively cancel out
 *  a categorisation.
 *
 * @note Each phrase (aka parent phrase) can have associated child phrases. These specify additional
 *  text which can alter the categorisation if they are also present.
 *
 * @see https://smoothwall-dev.atlassian.net/wiki/spaces/DST/pages/1396998145/Components
 */
export default class WeightedPhraseCategoriser implements IWeightedPhraseCategoriser {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance, optionally initialising the phrase and category information.
   *
   * @param weightedPhrases A collection of known phrases and their associated category info.
   *
   * @warning The weighted phrases are not cloned. It is the caller's responsibility to ensur
   *  nothing else modifies them while they are being used by this object.
   */
  public constructor(weightedPhrases?: WeightedPhrases) {
    if (weightedPhrases !== undefined) {
      this.setWeightedPhrases(weightedPhrases);
    }
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Set the phrase data which is used to categorise web-pages.
   *
   * @param weightedPhrases A collection of known phrases and their associated category info.
   *
   * @note It's important to ensure the phrases are lower-case as this facilitates case-insensitive
   *  searches.
   *
   * @warning The weighted phrases are not cloned. It is the caller's responsibility to ensur
   *  nothing else modifies them while they are being used by this object.
   */
  public readonly setWeightedPhrases = (weightedPhrases: WeightedPhrases): void => {
    // TODO: In debug mode, validate the weighted phrases?
    this._weightedPhrases = weightedPhrases;

    // Populate a look-up tree to speed up matching of phrases.
    this._lookupTree = new AhoCorasick(Object.keys(this._weightedPhrases));
  };

  /**
   * Get the number of weighted phrases which can be categorised.
   * This does not include child phrases.
   */
  public get length(): number {
    return Object.keys(this._weightedPhrases).length;
  }

  // -----------------------------------------------------------------------------------------------
  // Loading/saving.

  /**
   * Load the weighted phrases from a blocklist file.
   * This will replace all existing weighted phrases in this object.
   *
   * @param data The contents of the blocklist file to load. This must be formatted as JSON, with
   *  an object at the top level. The name of each property is a phrase. The value is an object
   *  specifying information about associated categories, weighting, and related phrases. The
   *  phrases should be lower-case to facilitate case insensitivity.
   */
  public loadFromBlocklistFile = (data: string): void => {
    this.setWeightedPhrases(JSON.parse(data));
  };

  /**
   * Patch the current weighted phrases using the given patch file data.
   * @param patch The patch data from the blocklist file to load.
   */
  public patchFromBlocklistFile = (patch: any): void => {
    this.setWeightedPhrases(applyPatch(this._weightedPhrases, patch).newDocument);
  };

  /**
   * Delete all stored extraction patterns and categories.
   */
  public readonly clear = (): void => {
    this._weightedPhrases = {};
    this._lookupTree = new AhoCorasick([]);
  };

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Get the categories which the specified page content matches based on known phrases it contains.
   *
   * @param content The page content to categorise. This is not case-sensitive.
   * @returns Returns an object containing details of the known phrases found in the specified
   *  content, and their associated scores and categories.
   */
  public readonly categoriseContent = (content: string): WeightedPhraseCategoriserResult => {
    // Ignore case.
    content = content.toLowerCase();

    // Look for any recognised phrases in the content.
    // This only finds parent phrases. It doesn't descend into child phrases.
    const parentPhrases: Set<string> = this.findParentPhrasesInContent(content);

    // Find the categories associated with each phrase].
    // This descends into child phrases to check if they are found as well.
    return this.categorisePhrases(parentPhrases, content);
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Look for any known phrases within the given content.
   * This is an intermediate step in categorising the content.
   * This doesn't look for child phrases; only at parents.
   *
   * @param content The web-page content analyse. It should be converted to lower-case before being
   *  passed-in.
   * @returns Returns a set of all the known phrases found in the specified content. If no known
   *  phrases were found, then an empty set is returned.
   */
  public readonly findParentPhrasesInContent = (content: string): Set<string> => {
    const output = new Set<string>();

    // Use the Aho-Corasick algorithm to find every location with a known phrase.
    this._lookupTree.search(content).forEach((match: AhoCorasickMatch): void => {
      match[1].forEach((phrase: string): void => {
        // Ignore empty phrases.
        if (phrase !== '') {
          output.add(phrase);
        }
      });
    });

    return output;
  };

  /**
   * Find and map the categories relating to the specified phrases.
   * This will also look for matching child phrases.
   *
   * @note You probably don't want to call this function directly. Call categoriseContent() instead.
   *
   * @param phrases A set of the known phrases to be categorised. Each one should match one of the
   *  parent phrases stored in this object (i.e. not a child phrase), and must have been found in
   *  the specified content string.
   * @param content The complete (lower-case) content which contains the specified phrases. This is
   *  needed to look for any child phrases associated with the specified parent phrases.
   * @returns Returns an object containing details of the known phrases found in the specified
   *  content, and their associated scores and categories.
   */
  public readonly categorisePhrases = (
    phrases: Set<string>,
    content: string,
  ): WeightedPhraseCategoriserResult => {
    const result = new WeightedPhraseCategoriserResult();
    const cachedSubstringMatcher = new CachedSubstringMatcher(content);
    phrases.forEach((phrase: string) => {
      this.categorisePhrase(phrase, cachedSubstringMatcher, result);
    });
    return result;
  };

  /**
   * Find the categories relating to the specified phrase and add them to the provided map.
   * This will also look for matching child phrases.
   *
   * @note You probably don't want to call this function directly. Call categoriseContent() instead.
   *
   * @param parentPhrase The phrase being categorised. This should match one of the parent phrases
   *  stored in this object (i.e. not a child phrase), and must have been found in the specified
   *  content string.
   * @param content The complete (lower-cased) page content which contains the specified phrase,
   *  wrapped in a cached substring matcher to optimise the searches. This is needed to look for any
   *  child phrases associated with the specified parent phrase.
   * @param result This is an output parameter which receives details of the phrases found in the
   *  specified content, and their associated scores and categories. New categorisation data found
   *  by this function will be added to the results; existing data in it will not be overwritten.
   */
  public readonly categorisePhrase = (
    parentPhrase: string,
    content: CachedSubstringMatcher,
    result: WeightedPhraseCategoriserResult,
  ): void => {
    // Do nothing if the specified phrase isn't recognised.
    if (!Object.prototype.hasOwnProperty.call(this._weightedPhrases, parentPhrase)) {
      return;
    }

    const phraseData = this._weightedPhrases[parentPhrase];
    if (!isValidWeightedPhraseData(phraseData)) {
      console.warn(
        `WeightedPhraseCategoriser - cannot categorise phrase due to invalid blocklist entry: ${parentPhrase}`,
      );
      return;
    }

    // Output any categories associated directly with the phrase.
    phraseData.catsAndScores.forEach((category: WeightedPhraseCategory) => {
      result.addToCategory(
        category.category.toString(),
        [parentPhrase],
        parseInt(category.score.toString()),
      );
    });

    // Output any categories associated with child phrases.
    phraseData.children.forEach((child: WeightedPhraseChild) => {
      // Any given child only applies if all associated phrases are found in the content.
      if (child.phrases.every((childPhrase: string) => content.includes(childPhrase))) {
        result.addToCategory(
          child.catsAndScores.category.toString(),
          [parentPhrase].concat(child.phrases),
          parseInt(child.catsAndScores.score.toString()),
        );
      }
    });
  };

  /**
   * Compare two arrays to see if they are equal.
   *
   * @param a The first array to compare.
   * @param b The second array to compare.
   * @return Returns true if the two arrays are the same size, and contain equal values, of the
   *  same types, in the same order. Returns false otherwise.
   *
   * @todo Move this to a centralised location?
   */
  public static isEqualArray = <Type>(a: Type[], b: Type[]): boolean => {
    if (a.length !== b.length) {
      return false;
    }

    for (let i = 0; i < a.length; ++i) {
      if (a[i] !== b[i]) {
        return false;
      }
    }

    return true;
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * A collection of known phrases and the categories they belong to.
   */
  private _weightedPhrases: WeightedPhrases = {};

  /**
   * A prefix tree which will be used to find known phrases efficiently.
   * This uses the Aho-Corasick algorithm.
   */
  private _lookupTree: AhoCorasick = new AhoCorasick([]);
}

// -------------------------------------------------------------------------------------------------

/**
 * Gives details of a phrase which contributed to a category found in the results.
 * This is used in the results from a weighted phrase categorisation.
 *
 * @see FoundCategory
 * @see WeightedPhraseCategoriserResult
 */
export class ContributingPhrase {
  /**
   * Initialise a new instance.
   *
   * @param text The complete text of the phrase which contributed to a particular categorisation.
   *  This must not be empty, and must not contain any empty strings.
   * @param score The weighting which this phrase contributed to the associated category. This can
   *  be negative.
   * @throws {Error} The text array is empty or contains one or more empty strings.
   */
  public constructor(text: string[], score: number) {
    if (text.length === 0) {
      throw new Error('The text of a contributing phrase must not be an empty array.');
    }

    if (text.some((value) => value.length === 0)) {
      throw new Error('The text of a contributing phrase must not contain any empty strings.');
    }

    this.text = text;
    this.score = score;
  }

  /**
   * The complete text of the phrase which contributed to a particular categorisation.
   * If it was a standalone (parent) phrase then this will only have one element. If there were any
   *  child phrases then they will be listed afterwards in the array.
   */
  public readonly text: string[];

  /**
   * The weighting which this phrase contributed to the category.
   * This can be negative.
   */
  public score: number;
}

// -------------------------------------------------------------------------------------------------

/**
 * Contains details about a category found in content using weighted phrase analysis.
 * The results of a weighted phrase analysis will consist of zero or more instances of this class.
 *
 * @see WeightedPhraseCategoriserResult
 */
export class FoundCategory {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Initialise the category ID of a new instance.
   *
   * @param id The ID of the category which was found, stored as a string. This must not be empty.
   * @throws {Error} The specified category ID is empty.
   */
  public constructor(id: string) {
    if (id === '') {
      throw new Error('The ID of a found category must not be an empty string.');
    }
    this.id = id;
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Calculate and return the total score of all phrases which contributed to this category.
   */
  public get totalScore(): number {
    return this.contributingPhrases.reduce((accumulator, phrase) => accumulator + phrase.score, 0);
  }

  /**
   * Find and return a contributing phrase matching the specified text, if it exists.
   *
   * @param text The complete text of the phrase to look for, starting with the parent phrase, and
   *  followed by any child phrases if applicable.
   * @returns Returns the matching contributing phrase instance, if one was found. Returns undefined
   *  otherwise.
   */
  public readonly getContributingPhrase = (text: string[]): ContributingPhrase | undefined => {
    return this.contributingPhrases.find((phrase) =>
      WeightedPhraseCategoriser.isEqualArray(phrase.text, text),
    );
  };

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Specify a phrase which contributed to the category in the results.
   * If the specified phrase already exists then this will update its score.
   *
   * @param text The complete text of the phrase which matched the category, starting with the
   *  parent phrase, and followed by any child phrases if applicable.
   * @param score The weight which the phrase contributes to the category.
   */
  public readonly addContributingPhrase = (text: string[], score: number): void => {
    const contributingPhrase = this.getContributingPhrase(text);
    if (contributingPhrase !== undefined) {
      // Update the existing entry.
      contributingPhrase.score = score;
      return;
    }
    // Create a new one.
    this.contributingPhrases.push(new ContributingPhrase(text, score));
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The ID of the category which was found.
   */
  public id: string;

  /**
   * A collection of all the phrases which contributed to the category.
   * This will be in an arbitrary order.
   * It should not contain any duplicates.
   */
  public readonly contributingPhrases: ContributingPhrase[] = [];
}

// -------------------------------------------------------------------------------------------------

/**
 * Contains details of the categories found in content using weighted phrase analysis.
 */
export class WeightedPhraseCategoriserResult implements CategoriserResult {
  /**
   * Get a set of all the category IDs which scored over the default threshold.
   * This is equivalent to calling getThresholdedCategoryIds() with the default argument.
   */
  public get categoryIds(): Set<string> {
    return this.getThresholdedCategoryIds();
  }

  /**
   * Get a set of all the category IDs which occur in the results, regardless of score.
   *
   * @note For most purposes, you probably want to use getThresholdedCategories(). That will filter
   *  out categories which didn't reach the score required for a confident match.
   */
  public readonly getAllCategoryIds = (): Set<string> => {
    const output = new Set<string>();
    this.foundCategories.forEach((foundCategory: FoundCategory): void => {
      output.add(foundCategory.id);
    });
    return output;
  };

  /**
   * Get a set of all the category IDs which scored over the specified threshold.
   * This is used to get the categories which scored highly enough for a confident match.
   *
   * @param threshold Only categories with a total score greater than or equal to this threshold
   *  will be returned. The default is 100, which means we are confident of a match.
   * @return Returns a set of category IDs meeting the specified criterion.
   */
  public readonly getThresholdedCategoryIds = (threshold: number = 100): Set<string> => {
    const output = new Set<string>();
    this.foundCategories.forEach((foundCategory: FoundCategory): void => {
      if (foundCategory.totalScore >= threshold) {
        output.add(foundCategory.id);
      }
    });
    return output;
  };

  /**
   * Get the entry relating to the specified category ID, if it exists.
   *
   * @param id The ID of the category to look for.
   * @return Returns an object containing information about the specified category, if it was found.
   *  Returns undefined otherwise.
   */
  public readonly getCategory = (id: string): FoundCategory | undefined => {
    return this.foundCategories.find((foundCategory) => foundCategory.id === id);
  };

  /**
   * Add information about a phrase found during weighted phrase analysis.
   * If there is an existing entry for the specified category, and it doesn't already contain the
   *  specified phrase, then this will add the phrase and score to it (or update the score if the
   *  phrase had already been added).
   * If there is no existing entry for the specified category, then this will create a new one.
   *
   * @param id The ID of the category which has been found.
   * @param text The complete text of the phrase which matched the category, starting with the
   *  parent phrase, and followed by any child phrases if applicable.
   * @param score The weight which the specified phrase contributes to the category.
   */
  public readonly addToCategory = (id: string, text: string[], score: number): void => {
    // Create a new category entry if necessary.
    let foundCategory = this.getCategory(id);
    if (foundCategory === undefined) {
      foundCategory = new FoundCategory(id);
      this.foundCategories.push(foundCategory);
    }

    foundCategory.addContributingPhrase(text, score);
  };

  /**
   * Delete all of the category information found in the results.
   */
  public readonly clear = (): void => {
    this.foundCategories.length = 0;
  };

  /**
   * A collection of all the categories found during weighted phrase analysis.
   * This includes categories which had a score below 100, meaning they should be ignored.
   */
  public readonly foundCategories: FoundCategory[] = [];
}

// -------------------------------------------------------------------------------------------------

/**
 * Describes an object containing categorisation data used in weighted phrase analysis.
 * This describes the contents of the "weightedphrases" blocklist file.
 *
 * The name of each record is the content of a known parent word or phrase.
 * The value is an object describing the category (or categories) it is assigned to, plus any
 * associated child phrases.
 *
 * @see WeightedPhrasesCategoriser
 */
export type WeightedPhrases = Record<string, WeightedPhraseData>;

// -------------------------------------------------------------------------------------------------

/**
 * Defines how a phrase is categorised for weighted phrase analysis.
 * This describes the contents of one parent phrase in the "weightedphrases" blocklist file.
 *
 * @note The value of the phrase (i.e. the text that would occur in the page) is stored in one level
 *  higher, in the WeightedPhrases object.
 *
 * @see WeightedPhraseCategoriser
 * @see WeightedPhrases
 */
export interface WeightedPhraseData {
  /**
   * Lists the categories associated with the phrase.
   * Each category also has a score, indicating how likely or relevant the category is.
   */
  catsAndScores: WeightedPhraseCategory[];

  /**
   * Additional words or phrases which could alter the categorisation.
   */
  children: WeightedPhraseChild[];
}

/**
 * Check that the specified object matches the expected structure of a WeightedPhraseData instance.
 * This also validates all the nested elements in "catsAndScores" and "children".
 *
 * @param obj The object to validate.
 * @returns Returns true if the object looks like a valid instance of WeightedPhraseData. Returns
 *  false if not.
 */
export const isValidWeightedPhraseData = (obj?: any): boolean => {
  return (
    obj !== null &&
    typeof obj === 'object' &&
    Array.isArray(obj.catsAndScores) &&
    Array.isArray(obj.children) &&
    Boolean(obj.catsAndScores.every(isValidWeightedPhraseCategory)) &&
    obj.children.every(isValidWeightedPhraseChild)
  );
};

// -------------------------------------------------------------------------------------------------

/**
 * Describes a category which a weighted phrase is assigned to.
 *
 * @see WeightedPhraseCategoriser
 * @see WeightedPhrase
 */
export interface WeightedPhraseCategory {
  /**
   * The ID of the category which the weighted phrase is assigned to.
   * When using the standard blocklist, this is a numeric ID.
   * When using custom phrases, this may be a string.
   * In this application, we always convert it to a string.
   */
  category: number | string;

  /**
   * The weight value, indicating how strongly the phrases matches the category.
   * This is a percentage weighting. 100 means the phrase is definitely associated with the
   *  specified category. A negative value means it makes the category less likely. For example, a
   *  page mentioning sex is less likely to be pornographic if it also mentions "NHS".
   *
   * @note This may be stored as a string sometimes.
   */
  score: number | string;
}

/**
 * Check that the specified object matches the structure of a WeightedPhraseCategory instance.
 * Validation includes ensuring none of the category identifiers are empty strings.
 *
 * @param obj The object to validate.
 * @returns Returns true if the object looks like a valid instance of WeightedPhraseCategory.
 *  Returns false if not.
 */
export const isValidWeightedPhraseCategory = (obj?: any): boolean => {
  return (
    obj !== null &&
    typeof obj === 'object' &&
    ['string', 'number'].includes(typeof obj.category) &&
    obj.category !== '' &&
    (typeof obj.score === 'number' || typeof obj.score === 'string')
  );
};

// -------------------------------------------------------------------------------------------------

/**
 * Describes an additional word or phrase which can affect the categorisation of weighted phrase.
 * For example, the parent phrase might be "tshirt". On its own, it may be innocent. However,
 *  if associated with the word "wet", then it is likely to be a pornographic reference. In this
 *  case, "wet" would be the child phrase.
 *
 * @see WeightedPhrase
 */
export interface WeightedPhraseChild {
  /**
   * One or more additional words or phrases which can modify the original categorisation.
   * If this contains multiple items then all of them must be found for it to match.
   */
  phrases: string[];

  /**
   * The new categories associated with the phrase when the child phrases are also found.
   */
  catsAndScores: WeightedPhraseCategory;
}

/**
 * Check that the specified object matches the expected structure of a WeightedPhraseChild instance.
 * Validation includes ensuring that none of the child phrases are empty.
 * This also validates the nested "catsAndScores" property using isValidWeightedPhraseCategory().
 *
 * @param obj The object to validate.
 * @returns Returns true if the object looks like a valid instance of WeightedPhraseChild. Returns
 *  false if not.
 */
export const isValidWeightedPhraseChild = (obj?: any): boolean => {
  return (
    obj !== null &&
    typeof obj === 'object' &&
    Array.isArray(obj.phrases) &&
    obj.phrases.length > 0 &&
    Boolean(obj.phrases.every((phrase: string) => typeof phrase === 'string' && phrase !== '')) &&
    isValidWeightedPhraseCategory(obj.catsAndScores)
  );
};

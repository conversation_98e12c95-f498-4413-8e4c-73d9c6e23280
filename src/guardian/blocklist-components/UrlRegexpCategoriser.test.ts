import UnifiedDiffMapBuilder from 'guardian/patching/UnifiedDiffMapBuilder';
import UrlRegexpCategoriser from './UrlRegexpCategoriser';
// Lines without a tab and at least 1 category id should be ignored when loading the blocklist file.
const testData = `
(?i)bestproxyever	120
(?i)blastbilliards	19
(?i)copter_game	19	20
(?i)coptergame	19
.*view\\.officeapps\\.live\\.com	444
.*visio\\.officeapps\\.live\\.com	444

/login/ValidateKeyVersion2
`;

describe('UrlRegexpCategoriser', () => {
  let categoriser = new UrlRegexpCategoriser();

  beforeEach(() => {
    categoriser = new UrlRegexpCategoriser();
  });

  describe('loadFromBlocklistFile', () => {
    it('loads patterns', () => {
      categoriser.loadFromBlocklistFile(testData);

      expect(categoriser.length).toEqual(6);
    });
    it('replaces existing patterns', () => {
      categoriser.loadFromBlocklistFile('/login/fetchFreeServersVersion2	120');
      expect(categoriser.length).toEqual(1);

      categoriser.loadFromBlocklistFile(testData);
      expect(categoriser.length).toEqual(6);
    });
  });

  describe('patchFromUnifiedDiff()', () => {
    it('correctly applies a diff', () => {
      // The blocklist diffs in our test data don't modify this file significantly.
      // We need to construct our own test data, based on real blocklist data.

      const oldFile = String.raw`(?i)^https?://(?:www\.)?baidu\.com/sugrec\?.*?sugsid=	228
(?i)^https?://([^/]*)?googlevideo\.com.*?\/yt_live_broadcast\/	518
(?i)^https?://([^/]*)?hulu-\d{1,3}\.fcod\.llnwd\.net	56
(?i)^https?://([^/]*)?netflix-\d+\.vo\.llnwd\.net	411
(?i)^https?://([^/]*)?youtube\.com.*?&live=1	518
(?i)^https?://([^/]*\.)?cnn\.com/(.+?)/sport/	53
(?i)^https?://([^/]*\.)?cnn\.com/(\d{4}/)?health/	7
(?i)^https?://(www\.)?duckduckgo\.com/\?[\w=&]+iax?=images	54
(?i)^https?://(www\.)?searx\.(?!github\.io)	50
(?i)^https?://(www\.)?yesmovies\d?\.	24
`;

      const newFile = String.raw`(?i)^https?://(?:www\.)?baidu\.com/sugrec\?.*?sugsid=	228
(?i)^https?://([^/]*)?amazon-\d{1,4}\.vo\.llnwd\.net	56
(?i)^https?://([^/]*)?googlevideo\.com.*?\/yt_live_broadcast\/	518
(?i)^https?://([^/]*)?hulu-\d{1,3}\.fcod\.llnwd\.net	56
(?i)^https?://([^/]*)?netflix-\d+\.vo\.llnwd\.net	411
(?i)^https?://(www\.)?collegeonline[0-9]{2,}\.info	120
(?i)^https?://(www\.)?duckduckgo\.com/\?[\w=&]+iax?=images	54
(?i)^https?://(www\.)?searx\.(?!github\.io)	50
(?i)^https?://(www\.)?yesmovies\d?\.	24
`;

      const unifiedDiff = String.raw`
--- a/regexpurls	2025-01-02 03:04:05.000 +0000
+++ b/regexpurls	2025-01-03 03:04:05.000 +0000
@@ -1,0 +2,1 @@
+(?i)^https?://([^/]*)?amazon-\d{1,4}\.vo\.llnwd\.net	56
@@ -5,3 +6,1 @@
-(?i)^https?://([^/]*)?youtube\.com.*?&live=1	518
-(?i)^https?://([^/]*\.)?cnn\.com/(.+?)/sport/	53
-(?i)^https?://([^/]*\.)?cnn\.com/(\d{4}/)?health/	7
+(?i)^https?://(www\.)?collegeonline[0-9]{2,}\.info	120
`;

      // Load the old unpatched data.
      const oldComponent = new UrlRegexpCategoriser();
      oldComponent.loadFromBlocklistFile(oldFile);

      // An entry which is removed by the patch should work correctly before patching.
      const input1 = new URL('https://cnn.com/news/sport/');
      expect(oldComponent.categoriseUrl(input1).categoryIds).toContain('53');

      // An entry which is added by the patch should not work before patching.
      const input2 = new URL('https://amazon-123.vo.llnwd.net');
      expect(oldComponent.categoriseUrl(input2).categoryIds).toBeEmpty();

      // An entry which is not changed by the patch should work before and after patching.
      const input3 = new URL('https://googlevideo.com/yt_live_broadcast/');
      expect(oldComponent.categoriseUrl(input3).categoryIds).toContain('518');

      // Apply the patch and check that the total number of resulting entries is correct.
      const diffMaps = [...UnifiedDiffMapBuilder.buildFromFile(unifiedDiff)];
      oldComponent.patchFromUnifiedDiff(diffMaps[0]);
      expect(oldComponent.length).toEqual(9);

      // An entry which is removed by the patch should not work after patching.
      expect(oldComponent.categoriseUrl(input1).categoryIds).toBeEmpty();

      // An entry which is added by the patch should work correctly after patching.
      expect(oldComponent.categoriseUrl(input2).categoryIds).toContain('56');

      // An entry which is not changed by the patch should work before and after patching.
      expect(oldComponent.categoriseUrl(input3).categoryIds).toContain('518');

      // TODO: When we have the ability to save the result of patching, compare the output of that
      //  against the expected result, instead of accessing private properties.
      const newComponent = new UrlRegexpCategoriser();
      newComponent.loadFromBlocklistFile(newFile);
      expect((oldComponent as any)._originalData).toEqual((newComponent as any)._originalData);
    });
  });

  describe('clearCategorisationMap', () => {
    it('clears the stored mappings', () => {
      categoriser.loadFromBlocklistFile(testData);
      expect(categoriser.length).toEqual(6);

      categoriser.clearCategorisationMap();
      expect(categoriser.length).toEqual(0);
    });
  });

  describe('categoriseUrl', () => {
    beforeEach(() => {
      categoriser.loadFromBlocklistFile(testData);
    });

    it('matches a simple regexp', () => {
      const url = new URL('https://coptergame.com');
      const expected = new Set(['19']);

      const result = categoriser.categoriseUrl(url);

      expect(result.categoryIds).toEqual(expected);
    });
    it('matches a regex with multiple categories', () => {
      const url = new URL('https://copter_game.com');
      const expected = new Set(['19', '20']);

      const result = categoriser.categoriseUrl(url);

      expect(result.categoryIds).toEqual(expected);
    });
  });
});

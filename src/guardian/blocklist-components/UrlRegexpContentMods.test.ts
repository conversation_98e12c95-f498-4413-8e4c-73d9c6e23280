import UrlRegexpContentMods from './UrlRegexpContentMods';
import UnifiedDiffMapBuilder from 'guardian/patching/UnifiedDiffMapBuilder';

// Lines that do not have 3 parts should be ignored.
// Note that the first line has 3 tabs, the last part (to) is empty which is ok.
const testData = `
222	https?://apis\\.google\\.com/u/[0-9]+/.*widget/render/comments.*first_party_property=YOUTUBE	

69	^(https?://(?:[^/]*\\.)?flickr\\.com/search/(?:advanced)?\\?)(.*)	\\1ss=0&\\2
69	(^https?://(?:[^/]*\\.)?bing\\.com/(?:images/|videos/|news/)?(?:search|async)\\?.*)	\\1&adlt=strict
178	.*?://[^/]*/wpad.dat	http://www.smoothwall.net
(?>&?copt_offensive=[^&]*)	
69	(^https?://(?:(?:www|cse)\\.)?google\\.\\w{1,3}(?:\\.\\w{1,3})?\\/(?!maps).*\\?(?!.*&safe=[^&?]+).*)$	\\1&safe=active
`;

const testDataSize = 5;

describe('UrlRegexpContentMods', () => {
  let urlContentMods = new UrlRegexpContentMods();

  beforeEach(() => {
    urlContentMods = new UrlRegexpContentMods();

    // Suppress console messages.
    jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  describe('loadFromBlocklistFile', () => {
    it('loads the content mods', () => {
      urlContentMods.loadFromBlocklistFile(testData);

      expect(urlContentMods.length).toBe(testDataSize);
    });
    it('replaces existing content mods', () => {
      urlContentMods.loadFromBlocklistFile(
        '69	(^https?://(?:[^/]*.)?blinkx.com/videos/[^?]+)	\\1?safefilter=on',
      );

      urlContentMods.loadFromBlocklistFile(testData);

      expect(urlContentMods.length).toBe(testDataSize);
    });
  });

  describe('patchFromUnifiedDiff()', () => {
    it('correctly applies a diff', () => {
      // The blocklist diffs in our test data don't modify this file.
      // We need to construct our own test data, based on real blocklist data.

      const oldFile = String.raw`222	^(.{1,5}://)?(?:[^/]*.)youtube.com/comment_service_ajax?	/
222	^(.{1,5}://)?(?:[^/]*.)youtube.com/watch_fragments2_ajax\?.*frags=comments	/
520	(^https?://(?:www\.)?google(?:\.com|\.co\.uk)/search.*?\b(?:tbm=isch|udm=2)\b.*&tbs)&(.*)	$1=sur:cl&$2
520	(^https?://(?:www\.)?google(?:\.com|\.co\.uk)/search.*?\b(?:tbm=isch|udm=2)\b(?!.*&tbs).*)	$1&tbs=sur:cl
520	(^https?://(?:www\.)?google(?:\.com|\.co\.uk)/search.*?\b(?:tbm=isch|udm=2)\b.*?tbs)([^&]*(?:\b|%2C|,))sur(?::|%3A)[^&]*?((?:&|,|%2C).*)	$1$2sur:cl$3
520	(^https?://(?:www\.)?google(?:\.com|\.co\.uk)/search.*&\b(?:tbm=isch|udm=2)\b.*&tbs(?![^&]*(?:=|,|%2C)sur))=(.*)	$1=sur:cl,$2
69	(^https?://(?:[^/]*\.)?ilse\.nl/searchresults\.dbl\?)(.*)(&?)(family=[^&]*)	\1\2\3
69	(^https?://[^/]*search[^/]*\.lycos\.com/[^?]*\?.*)	\1&contentFilter=strict
69	(^https?://(?:zoek|search)\.lycos\.(?:nl|co\.uk)/[^?]*\?)(.*?)(&?)(family=[^&]*)	\1\2\3
69	(^https?://(?:zoek|search)\.lycos\.(?:nl|co\.uk)/[^?]*\?.*)	\1&family=on
69	(^https?://(?:[^/]*\.)?alltheweb\.com/customize\?)	\1copt_offensive=on&
69	(^https?://(?:[^/]*\.)?yahoo\.[a-z]+[-/%.0-9a-z]*/search[^?]*\?.*)	\1&vm=r
`;

      const newFile = String.raw`222	^(.{1,5}://)?(?:[^/]*.)youtube.com/comment_service_ajax?	/
520	(^https?://(?:www\.)?google(?:\.com|\.co\.uk)/search.*?\b(?:tbm=isch|udm=2)\b(?!.*&tbs).*)	$1&tbs=sur:cl
520	(^https?://(?:www\.)?google(?:\.com|\.co\.uk)/search.*?\b(?:tbm=isch|udm=2)\b.*?tbs)([^&]*(?:\b|%2C|,))sur(?::|%3A)[^&]*?((?:&|,|%2C).*)	$1$2sur:cl$3
520	(^https?://(?:www\.)?google(?:\.com|\.co\.uk)/search.*&\b(?:tbm=isch|udm=2)\b.*&tbs(?![^&]*(?:=|,|%2C)sur))=(.*)	$1=sur:cl,$2
69	(^https?://(?:[^/]*\.)?ilse\.nl/blahblahblah\.dbl\?)(.*)(&?)(family=[^&]*)	\1\2\3
69	(^https?://(?:[^/]*\.)?ilse\.nl/searchresults\.dbl\?.*)	\1&family=yes
69	(^https?://(?:[^/]*\.)?kel\.nl/search/search.cgi\?)(.*)(&?)(Realm%3AErotiek=[^&]*)	\1\2\3
69	(^https?://[^/]*search[^/]*\.lycos\.com/[^?]*\?.*)	\1&contentFilter=strict
69	(^https?://(?:[^/]*\.)?yahoo\.[a-z]+[-/%.0-9a-z]*/search[^?]*\?.*)	\1&vm=r
`;

      const unifiedDiff = String.raw`
--- a/regexpurls_cmod	2025-01-02 03:04:05.000 +0000
+++ b/regexpurls_cmod	2025-01-03 03:04:05.000 +0000
@@ -2,2 +1,0 @@
-222	^(.{1,5}://)?(?:[^/]*.)youtube.com/watch_fragments2_ajax\?.*frags=comments	/
-520	(^https?://(?:www\.)?google(?:\.com|\.co\.uk)/search.*?\b(?:tbm=isch|udm=2)\b.*&tbs)&(.*)	$1=sur:cl&$2
@@ -7,1 +5,3 @@
-69	(^https?://(?:[^/]*\.)?ilse\.nl/searchresults\.dbl\?)(.*)(&?)(family=[^&]*)	\1\2\3
+69	(^https?://(?:[^/]*\.)?ilse\.nl/blahblahblah\.dbl\?)(.*)(&?)(family=[^&]*)	\1\2\3
+69	(^https?://(?:[^/]*\.)?ilse\.nl/searchresults\.dbl\?.*)	\1&family=yes
+69	(^https?://(?:[^/]*\.)?kel\.nl/search/search.cgi\?)(.*)(&?)(Realm%3AErotiek=[^&]*)	\1\2\3
@@ -9,3 +7,0 @@
-69	(^https?://(?:zoek|search)\.lycos\.(?:nl|co\.uk)/[^?]*\?)(.*?)(&?)(family=[^&]*)	\1\2\3
-69	(^https?://(?:zoek|search)\.lycos\.(?:nl|co\.uk)/[^?]*\?.*)	\1&family=on
-69	(^https?://(?:[^/]*\.)?alltheweb\.com/customize\?)	\1copt_offensive=on&
`;

      // Load the old unpatched data.
      const oldComponent = new UrlRegexpContentMods();
      oldComponent.loadFromBlocklistFile(oldFile);

      // An entry which is removed by the patch should work correctly before patching.
      const input1 = 'https://alltheweb.com/customize?foo=bar';
      const categories = new Set<string>(['69']);
      expect(oldComponent.executeContentMods(input1, categories)).toEqual(
        'https://alltheweb.com/customize?copt_offensive=on&foo=bar',
      );

      // An entry which is added by the patch should not work before patching.
      const input2 = 'https://kel.nl/search/search.cgi?Realm%3AErotiek=123';
      expect(oldComponent.executeContentMods(input2, categories)).toBeUndefined();

      // An entry which is not changed by the patch should work before and after patching.
      const input3 = 'https://yahoo.com/search?q=blah';
      expect(oldComponent.executeContentMods(input3, categories)).toEqual(
        'https://yahoo.com/search?q=blah&vm=r',
      );

      // Apply the patch and check that the total number of resulting entries is correct.
      const diffMaps = [...UnifiedDiffMapBuilder.buildFromFile(unifiedDiff)];
      oldComponent.patchFromUnifiedDiff(diffMaps[0]);
      expect(oldComponent.length).toEqual(9);

      // An entry which is removed by the patch should not work after patching.
      expect(oldComponent.executeContentMods(input1, categories)).toBeUndefined();

      // An entry which is added by the patch should work correctly after patching.
      expect(oldComponent.executeContentMods(input2, categories)).toEqual(
        'https://kel.nl/search/search.cgi?',
      );

      // An entry which is not changed by the patch should work before and after patching.
      expect(oldComponent.executeContentMods(input3, categories)).toEqual(
        'https://yahoo.com/search?q=blah&vm=r',
      );

      // TODO: When we have the ability to save the result of patching, compare the output of that
      //  against the expected result, instead of accessing private properties.
      const newComponent = new UrlRegexpContentMods();
      newComponent.loadFromBlocklistFile(newFile);
      expect((oldComponent as any)._originalData).toEqual((newComponent as any)._originalData);
    });
  });

  describe('clearContentMods', () => {
    it('clears the stored content mods', () => {
      urlContentMods.loadFromBlocklistFile(testData);
      expect(urlContentMods.length).toBe(testDataSize);

      urlContentMods.clearContentMods();
      expect(urlContentMods.length).toBe(0);
    });
  });

  describe('executeContentMods', () => {
    beforeEach(() => {
      urlContentMods.loadFromBlocklistFile(testData);
    });

    it('executes a valid content mod', () => {
      const url = 'test:///wpad.dat';
      const categories = new Set(['178']);
      const result = urlContentMods.executeContentMods(url, categories);

      expect(result).toEqual('http://www.smoothwall.net');
    });
    it('executes a valid empty content mod', () => {
      const url =
        'https://apis.google.com/u/123/widget/render/comments/first_party_property=YOUTUBE';
      const categories = new Set(['222']);
      const result = urlContentMods.executeContentMods(url, categories);

      expect(result).toEqual('');
    });
    it('does not execute content mods if none match', () => {
      const url = 'test:///wpad.dat';
      const categories = new Set(['1']);
      const result = urlContentMods.executeContentMods(url, categories);

      expect(result).toBeUndefined();
    });
    it('executes a valid content mod with capture groups', () => {
      const url = 'https://google.com/search?q=test';
      const categories = new Set(['69']);
      const result = urlContentMods.executeContentMods(url, categories);

      expect(result).toEqual('https://google.com/search?q=test&safe=active');
    });
  });
});

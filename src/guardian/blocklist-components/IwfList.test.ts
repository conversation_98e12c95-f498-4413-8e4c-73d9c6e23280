import IwfList from './IwfList';

/**
 * The hashes in this list correspond to these URLs:
 *
 * - example.com
 * - example.org/foo
 * - blah.example.net
 * - blah.example.uk/foo/bar
 */
const testData = [
  '0caaf24ab1a0c33440c06afe99df986365b0781f',
  '1ceea0c159170f07702cd21a8b8a6e90230f06b8',
  '0c6a3f45f11701789d4132ac439541fa35523ee4',
  'f7d2bcef3b890b4b5d72a26522ad1d78c7575d1e',
];

describe('IwfList', () => {
  // -----------------------------------------------------------------------------------------------

  // constructor() is covered by other tests

  // -----------------------------------------------------------------------------------------------

  describe('setHashedUrls()', () => {
    it('stores the specified data', () => {
      const iwfList = new IwfList();
      iwfList.setHashedUrls(testData);
      expect(iwfList.contains(new URL('https://example.com'))).toBeTrue();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('length()', () => {
    it('returns 0 if no URLs have been loaded yet', () => {
      const iwfList = new IwfList();
      expect(iwfList.length).toEqual(0);
    });

    it('returns the number of hashed URLs loaded', () => {
      const iwfList = new IwfList(testData);
      expect(iwfList.length).toEqual(4);
    });

    it('returns 0 if the loaded URLs have been cleared', () => {
      const iwfList = new IwfList(testData);
      iwfList.clear();
      expect(iwfList.length).toEqual(0);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('loadFromBlocklistFile()', () => {
    it('loads the specified urls', () => {
      const iwfList = new IwfList();
      iwfList.loadFromBlocklistFile(JSON.stringify(testData));
      expect(iwfList.contains(new URL('https://example.com'))).toBeTrue();
    });

    it('throws an error if the specified string is not valid json', () => {
      const iwfList = new IwfList();
      expect(() => {
        iwfList.loadFromBlocklistFile('} blah');
      }).toThrow();
    });

    it('throws an error if the top-level json element is not an array', () => {
      const iwfList = new IwfList();
      expect(() => {
        iwfList.loadFromBlocklistFile('{ "foo": "bar" }');
      }).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('clear()', () => {
    it('removes all stored urls', () => {
      const iwfList = new IwfList(testData);
      iwfList.clear();
      expect(iwfList.contains(new URL('http://example.com'))).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('contains', () => {
    it('returns false if no URLs have been loaded yet', () => {
      const iwfList = new IwfList();
      expect(iwfList.contains(new URL('http://example.com'))).toBeFalse();
    });

    it('returns true if the specified URL exactly matches one in the list', () => {
      const iwfList = new IwfList(testData);
      expect(iwfList.contains(new URL('http://example.com'))).toBeTrue();
      expect(iwfList.contains(new URL('http://example.org/foo'))).toBeTrue();
      expect(iwfList.contains(new URL('http://blah.example.net'))).toBeTrue();
      expect(iwfList.contains(new URL('http://blah.example.uk/foo/bar'))).toBeTrue();
    });

    it('returns false if the specified URL is a subdomain of one in the list', () => {
      const iwfList = new IwfList(testData);
      expect(iwfList.contains(new URL('http://mobile.example.com'))).toBeFalse();
      expect(iwfList.contains(new URL('http://data.blah.example.net'))).toBeFalse();
    });

    it('returns true if the specified URL is a subfolder of one in the list', () => {
      const iwfList = new IwfList(testData);
      expect(iwfList.contains(new URL('http://example.com/some/file'))).toBeTrue();
      expect(iwfList.contains(new URL('http://example.org/foo/bar/blah'))).toBeTrue();
    });

    it('returns false if the specified URL is a subdomain and subfolder of one in the list', () => {
      const iwfList = new IwfList(testData);
      expect(iwfList.contains(new URL('http://mobile.blah.example.uk/foo/bar/hello/world'))).toBe(
        false,
      );
    });

    it('allows different protocol prefixes', () => {
      const iwfList = new IwfList(testData);
      expect(iwfList.contains(new URL('http://example.com'))).toBeTrue();
      expect(iwfList.contains(new URL('https://example.com'))).toBeTrue();
      expect(iwfList.contains(new URL('ftp://example.com'))).toBeTrue();
      expect(iwfList.contains(new URL('file://example.com'))).toBeTrue();
    });

    it('is case insensitive', () => {
      const iwfList = new IwfList(testData);
      expect(iwfList.contains(new URL('http://ExAmPlE.cOm'))).toBeTrue();
      expect(iwfList.contains(new URL('http://ExAmPlE.cOm/SomE/FiLE'))).toBeTrue();
    });

    it('returns false if the specified URL is not similar to any in the list', () => {
      const iwfList = new IwfList(testData);
      expect(iwfList.contains(new URL('http://smoothwall.com'))).toBeFalse();
    });

    it('returns false if the specified URL is a higher level domain of one in the list', () => {
      const iwfList = new IwfList(testData);
      expect(iwfList.contains(new URL('http://example.net'))).toBeFalse();
    });

    it('returns false if the specified URL is a parent folder of one in the list', () => {
      const iwfList = new IwfList(testData);
      expect(iwfList.contains(new URL('http://example.org'))).toBeFalse();
    });

    it('does not match any URL against an empty entry in the hash table', () => {
      // If the IWF list contains a hash of an empty string, ensure we don't treat it as a wildcard
      //  match for any URL. This has happened in the past because we accidentally treated an empty
      //  string as the root path of a local filename.
      const iwfList = new IwfList([
        ...testData,
        'da39a3ee5e6b4b0d3255bfef95601890afd80709', // <-- hash of an empty string
      ]);

      expect(iwfList.contains(new URL('http://example.org'))).toBeFalse();
      expect(iwfList.contains(new URL('file:///c:/foo/bar/blah.pdf'))).toBeFalse();
      expect(iwfList.contains(new URL('file:///'))).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('hash', () => {
    it('returns the hex encoded hash of the specified string', () => {
      expect(IwfList.hash('example.com')).toEqual('0caaf24ab1a0c33440c06afe99df986365b0781f');
      expect(IwfList.hash('example.org/foo')).toEqual('1ceea0c159170f07702cd21a8b8a6e90230f06b8');
      expect(IwfList.hash('blah.example.net')).toEqual('0c6a3f45f11701789d4132ac439541fa35523ee4');
      expect(IwfList.hash('blah.example.uk/foo/bar')).toEqual(
        'f7d2bcef3b890b4b5d72a26522ad1d78c7575d1e',
      );
    });
  });

  // -----------------------------------------------------------------------------------------------
});

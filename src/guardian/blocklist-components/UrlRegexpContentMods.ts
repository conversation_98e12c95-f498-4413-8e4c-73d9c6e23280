import IUrlRegexpContentMod from 'guardian/models/UrlRegexpContentMod';
import DiffMap from 'guardian/patching/DiffMap';
import UnifiedDiffTarget from 'guardian/patching/UnifiedDiffTarget';
import { cleanRegexp } from 'utilities/Helpers';

export interface IUrlRegexpContentMods {
  /**
   * Gets the number of content mods currently in use.
   */
  length: number;

  /**
   * Looks for any valid content mods for the given url and category.
   *
   * If one is found then it will be executed and the resulting url will be returned.
   * Otherwise it will return undefined, meaning there are no content mods.
   * @param url The url to execute on.
   * @param categories The list of content mod categories for the url.
   * @returns The new url to use if a content mod was successfully executed. If not then it will return undefined.
   */
  executeContentMods: (url: string, categories: Set<string>) => string | undefined;
}

/**
 * Blocklist component that stores and executes url regexp content mods.
 */
export default class UrlRegexpContentMods implements IUrlRegexpContentMods, UnifiedDiffTarget {
  /**
   * Gets the number of content mods currently in use.
   */
  public get length(): number {
    return this._contentMods.length;
  }

  /**
   * Parses and saves the given regexp content mods blocklist file.
   *
   * This will replace any existing content mods.
   * @param data The regexpurls_cmod blocklist file.
   *
   * Each line should be in the format of category \t from \t to
   */
  public readonly loadFromBlocklistFile = (data: string): void => {
    const originalData = data.split('\n');
    this._contentMods = this._parseBlocklistFile(originalData);
    // Wait until parsing has finished before updating this value so that they don't get out of sync
    //  if parsing throws an unexpected error.
    this._originalData = originalData;
  };

  /**
   * Use the given object to patch the blocklist data in memory.
   *
   * @param patcher An object which will apply the relevant blocklist diff to an in-memory array.
   */
  public readonly patchFromUnifiedDiff = (patcher: DiffMap): void => {
    // Apply the patch to a raw copy of the original blocklist data, then reparse the whole thing.
    // This is to ensure we can reliably store the result of the patch, and it gives us the option
    //  to verify the result of patching in future.
    // TODO: In future, maybe patch both versions in parallel if it's more efficient than re-parsing
    //  the whole file. It would require adding DiffMap support for non-string values.
    patcher.patchArrayInPlace(this._originalData);
    this._contentMods = this._parseBlocklistFile(this._originalData);
  };

  /**
   * Clears the currently stored content mods.
   */
  public readonly clearContentMods = (): void => {
    this._originalData = [];
    this._contentMods = [];
  };

  /**
   * Looks for any valid content mods for the given url and category.
   *
   * If one is found then it will be executed and the resulting url will be returned.
   * Otherwise it will return undefined, meaning there are no content mods.
   * @param url The url to execute on.
   * @param categories The list of content mod categories for the url.
   * @returns The new url to use if a content mod was successfully executed. If not then it will return undefined.
   */
  public readonly executeContentMods = (
    url: string,
    categories: Set<string>,
  ): string | undefined => {
    let newUrl = '';

    let appliedSuccessfully = false;
    for (const category of categories) {
      // We do not support cname rewrites, we have to ignore these
      if (category === '416' || category === '486' || category === '516') {
        continue;
      }

      const contentMods = this._contentMods.filter((c) => c.category === category);

      if (contentMods.length <= 0) {
        continue;
      }

      for (const contentMod of contentMods) {
        const testString = url.replace(contentMod.from, contentMod.to);

        if (testString !== url) {
          newUrl = testString;

          // When we do a replace it can leave the trailing bit of the url which won't resolve to anything.
          if (contentMod.to.length <= 1) {
            newUrl = '';
          }
          appliedSuccessfully = true;
        }
      }
    }

    return appliedSuccessfully ? newUrl : undefined;
  };

  /**
   * Parses the lines from blocklist file into a structured array.
   * @param dataLines The blocklist file contents, split into lines.
   * @returns A map of regex to categories.
   */
  private readonly _parseBlocklistFile = (dataLines: string[]): IUrlRegexpContentMod[] => {
    const contentMods: IUrlRegexpContentMod[] = [];
    dataLines.forEach((line) => {
      const lineData = this._parseLine(line);

      if (lineData === undefined) {
        return;
      }

      contentMods.push(lineData);
    });

    return contentMods;
  };

  /**
   * Parses the given line of blocklist data and parses out the category plus the from and to regexp.
   * @param line The line of data to parse.
   * @returns An object with the category and the from and to regexps.
   */
  private readonly _parseLine = (line: string): IUrlRegexpContentMod | undefined => {
    // The category, from and to are separated by a tab.
    const lineData = line.split('\t');
    if (lineData.length < 3) {
      return;
    }

    const category = lineData[0];
    const from = new RegExp(cleanRegexp(lineData[1], true));
    const to = cleanRegexp(lineData[2], true);

    return { category, from, to };
  };

  /**
   * The original blocklist file data, split up into separate lines, before parsing/validation.
   * When the blocklist is patched, additions and deletions will operate on this array. When
   *  patching has finished, it will be re-parsed to populate the main array. This is because
   *  patching would not be able to operate reliably on the main array.
   * This will be an empty array if no data is loaded yet.
   */
  private _originalData: string[] = [];

  /**
   * An array of content mods.
   */
  private _contentMods: IUrlRegexpContentMod[] = [];
}

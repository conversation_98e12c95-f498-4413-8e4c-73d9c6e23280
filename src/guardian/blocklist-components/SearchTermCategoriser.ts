import AhoCorasick, { AhoCorasickMatch } from 'ahocorasick';
import { applyPatch } from 'fast-json-patch';
import DiffMap from 'guardian/patching/DiffMap';
import UnifiedDiffTarget from 'guardian/patching/UnifiedDiffTarget';

/**
 * Readonly interace for a blocklist component which categorises a URL based on search terms.
 *
 * @see SearchTermCategoriser
 */
export interface ISearchTermCategoriser {
  /**
   * Get the number of search terms which can be categorised.
   * This is not affected by the number of extraction patterns.
   */
  length: number;

  setSearchTerms: (searchTerms: SearchTerms) => void;

  /**
   * Get the categories which the specified URL matches based on any search terms it contains.
   *
   * @param url The URL to categorise.
   * @returns Returns an object containing details of the known search terms found in the URL, and
   *  their associated categories.
   */
  categoriseUrl: (url: URL) => SearchTermCategoriserResult;
}

/**
 * Blocklist component for categorising a URL based on the search terms it contains.
 *
 * @see https://familyzone.atlassian.net/wiki/spaces/DST/pages/2694335858368/Components#Search-term-filtering
 * @see https://familyzone.atlassian.net/wiki/spaces/DST/pages/2694335858368/Components#Search-term-extraction
 *
 * @todo Split this class into two separate classes, one for each blocklist file. It currently
 *  manages two blocklist files. The first file is "searchengineregexplist", which contains regular
 *  expressions for extracting search strings from URLs. That data is patched via Unified Diff (see
 *  UnifiedDiffTarget). The second file is "searchterms", which defines how to categorise known
 *  words and phrases which occur within a search string. That data is patched via JSON diff.
 */
export default class SearchTermCategoriser implements ISearchTermCategoriser, UnifiedDiffTarget {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance, optionally initialising the regular expressions and categories.
   *
   * @param extractionPatterns An array of regular expressions which will extract search terms from
   *  URLs via capture groups.
   * @param searchTerms A collection of search terms and their associated category info.
   *
   * @warning The specified extraction patterns and search terms are not cloned. It is the caller's
   *  responsibility to ensure nothing else modifies them while they are being used by this object.
   *
   * @note The extraction patterns cannot be patched from Unified Diff if they are initialised
   *  here. Patching is only possible if the extraction patterns are loaded from a blocklist file by
   *  calling loadExtractionPatternsFromBlocklistFile().
   */
  public constructor(extractionPatterns?: RegExp[], searchTerms?: SearchTerms) {
    if (extractionPatterns !== undefined) {
      this.setExtractionPatterns(extractionPatterns);
    }

    if (searchTerms !== undefined) {
      this.setSearchTerms(searchTerms);
    }
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Get the regular expressions which are used to extract search terms from URLs.
   *
   * @return Returns an array of the regular expressions used by this object to extract search terms
   *  from URLs. This is a shallow clone so modifying the array itself won't affect this object.
   *  However, modifying individual regular expressions within the array should be avoided.
   *
   */
  public readonly getExtractionPatterns = (): RegExp[] => {
    return [...this._extractionPatterns];
  };

  /**
   * Set the regular expressions which will be used to extract search terms from URLs.
   *
   * @param extractionPatterns An array of regular expressions which will extract search terms from
   *  URLs via capture groups.
   *
   * @warning The specified extraction patterns are not cloned. It is the caller's responsibility to
   *  ensure nothing else modifies them while they are being used by this object.
   *
   * @note The extraction patterns cannot be patched from Unified Diff after calling this. Patching
   *  is only possible if the extraction patterns were loaded from a blocklist file by calling
   *  loadExtractionPatternsFromBlocklistFile().
   */
  public readonly setExtractionPatterns = (extractionPatterns: RegExp[]): void => {
    this._extractionPatterns = extractionPatterns;
    // Extraction patterns have been set directly from some other source. We can't reliably convert
    //  the parsed extraction patterns back to the original blocklist file representation. As a
    //  result, we cannot safely patch data provided this way. Discard any data we previously loaded
    //  from the blocklist to ensure that patches aren't applied to the wrong thing.
    this._originalExtractionPatternsData = null;
  };

  /**
   * Set the search term data which is used to categorise URLs.
   *
   * @param searchTerms An object where each property name is a lower-case search term. Each value
   *  is an object giving information about the categories it belongs to.
   * @note It's important to ensure the search terms are lower-case as this facilitates
   *  case-insensitive searches.
   */
  public readonly setSearchTerms = (searchTerms: SearchTerms): void => {
    // TODO: In debug mode, validate the search terms?
    this._searchTerms = searchTerms;

    // Populate a look-up tree to speed up matching of terms.
    this._lookupTree = new AhoCorasick(Object.keys(this._searchTerms));
  };

  /**
   * Get the number of search terms which can be categorised.
   * This is not affected by the number of extraction patterns.
   */
  public get length(): number {
    return Object.keys(this._searchTerms).length;
  }

  // -----------------------------------------------------------------------------------------------
  // Loading/saving.

  /**
   * Load the extraction patterns from a blocklist file.
   * The extraction patterns are regular expressions which extract search terms from URLs.
   * This will replace all existing extraction patterns in this object. Any existing categorisation
   *  data will not be affected.
   *
   * @param data The contents of the blocklist file to load. This must be formatted as plain-text,
   *  with one regular expression per line. Each pattern should be enclosed with single quotes and
   *  followed by a comma.
   */
  public loadExtractionPatternsFromBlocklistFile = (data: string): void => {
    this._originalExtractionPatternsData = null;
    const originalLines = data.split('\n');
    this._extractionPatterns =
      SearchTermCategoriser.parseExtractionPatternsFromBlocklistFile(originalLines);
    this._originalExtractionPatternsData = originalLines;
  };

  /**
   * Load the search terms from a blocklist file.
   * This will replace all existing search terms in this object. Any existing extraction patterns
   *  will not be affected.
   *
   * @param data The contents of the blocklist file to load. This must be formatted as JSON, with
   *  an object at the top level. The name of each property is a search term. The value is an object
   *  specifying information about associated categories and related terms.
   */
  public loadSearchTermsFromBlocklistFile = (data: string): void => {
    this.setSearchTerms(JSON.parse(data));
  };

  /**
   * Patch the current search terms using the given patch file data.
   * @param patch The patch data from the blocklist file to load.
   */
  public patchSearchTermsFromBlocklistFile = (patch: any): void => {
    this.setSearchTerms(applyPatch(this._searchTerms, patch).newDocument);
  };

  /**
   * Delete all stored extraction patterns and categories.
   */
  public readonly clear = (): void => {
    this._originalExtractionPatternsData = null;
    this._extractionPatterns = [];
    this._searchTerms = {};
    this._lookupTree = new AhoCorasick([]);
  };

  // -----------------------------------------------------------------------------------------------
  // Unified Diff patching (extraction patterns).

  /**
   * Use the given object to patch the blocklist data in memory.
   *
   * @param patcher An object which will apply the relevant blocklist diff to an in-memory array.
   * @throws {Error} The extraction patterns cannot be patched as they were not loaded from a
   *  blocklist file.
   */
  public readonly patchFromUnifiedDiff = (patcher: DiffMap): void => {
    if (this._originalExtractionPatternsData === null) {
      throw new Error(
        'Cannot patch search engine extraction patterns. ' +
          'The data was not loaded from a blocklist file.',
      );
    }

    // Apply the patch to a raw copy of the original blocklist data, then reparse the whole thing.
    // This is to ensure we can reliably store the result of the patch, and it gives us the option
    //  to verify the result of patching in future.
    // TODO: In future, maybe patch both versions in parallel if it's more efficient than re-parsing
    //  the whole file. It would require adding DiffMap support for non-string values.
    patcher.patchArrayInPlace(this._originalExtractionPatternsData);
    this._extractionPatterns = SearchTermCategoriser.parseExtractionPatternsFromBlocklistFile(
      this._originalExtractionPatternsData,
    );
  };

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Get the categories which the specified URL matches based on any search terms it contains.
   *
   * @param url The URL to categorise.
   * @returns Returns an object containing details of the known search terms found in the URL, and
   *  their associated categories.
   */
  public readonly categoriseUrl = (url: URL): SearchTermCategoriserResult => {
    // Extract and sanitise the search string.
    const originalSearchString: string = this.extractSearchStringFromUrl(url);
    if (originalSearchString.length === 0) {
      // Do nothing if we don't have a search string to work with.
      return new SearchTermCategoriserResult();
    }
    const sanitisedSearchString: string =
      SearchTermCategoriser.sanitiseSearchString(originalSearchString);

    // Look for any recognised search terms in the search string.
    // This only finds parent search terms. It doesn't descend into child phrases.
    const parentSearchTerms: Set<string> = this.matchKnownSearchTerms(sanitisedSearchString);

    // Find the categories associated with each search term.
    // This descends into child phrases to check if they are found as well.
    const mapping = this.categoriseSearchTerms(parentSearchTerms, sanitisedSearchString);

    return new SearchTermCategoriserResult(sanitisedSearchString, mapping);
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Extract the complete search string from a URL, if there is one.
   *
   * @param url The URL from which to extract a search string.
   * @returns Returns the URL-decoded search string from the specified URL. If the extraction
   *  patterns found multiple different search strings then they will be concatenated into a single
   *  string in the order in which they were found. If no search string was found then this will
   *  return an empty string.
   */
  public readonly extractSearchStringFromUrl = (url: URL): string => {
    const urlString: string = url.toString();
    const foundSearchStrings = new Set<string>();

    // Run the URL through each regular expression. Store every unique matched capture group.
    this._extractionPatterns.forEach((pattern: RegExp): void => {
      pattern.lastIndex = 0; // <-- RegExp is stateful! Reset it to ensure consistent behaviour.
      pattern
        .exec(urlString)
        ?.slice(1)
        .forEach((match: string) => foundSearchStrings.add(decodeURIComponent(match)));
    });

    return [...foundSearchStrings].join(' ');
  };

  /**
   * Prepare a search string for analysis.
   * This strips out and condenses irrelevant content, letting us focus on the alphanumeric text.
   *
   * @param searchString The original search string to be sanitised.
   * @returns Returns the sanitised search string.
   */
  public static readonly sanitiseSearchString = (searchString: string): string => {
    // Surround search term with spaces to avoid the "Scunthorpe" problem.
    searchString = ' ' + searchString + ' ';

    // Replace all special characters with a space, and condense multiple consecutive spaces down
    //  to one.
    searchString = searchString.replace(
      /[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,\-./:;<=>?@[\]^_`{|}~\s]+/g,
      ' ',
    );

    // The search term data is all lower-case. Convert to match it.
    return searchString.toLowerCase();
  };

  /**
   * Look for any known search terms within the given search string.
   * This is an intermediate step in categorising a URL.
   * This doesn't look for child search terms; only at parents.
   *
   * @param sanitisedSearchString The search string to analyse, extracted from a search engine URL.
   *  To simplify analysis, it must be sanitised before being passed in here.
   * @returns Returns a set of all the known search terms found in the specified search string. If
   *  no known search terms were found, then an empty set is returned.
   */
  public readonly matchKnownSearchTerms = (sanitisedSearchString: string): Set<string> => {
    const output = new Set<string>();

    // Use the Aho-Corasick algorithm to find every location with a known search term.
    this._lookupTree.search(sanitisedSearchString).forEach((match: AhoCorasickMatch): void => {
      // Go through every known search term at this location.
      match[1].forEach((knownSearchTerm: string) => output.add(knownSearchTerm));
    });

    return output;
  };

  /**
   * Find and map the categories relating to the specified search terms.
   * This will also look for matching child phrases.
   *
   * @param searchTerms A set of the search terms to be categorised. Each one should match one of
   *  the parent search terms stored in this object (i.e. not a child phrase), and must have been
   *  found in the specified sanitised search string.
   * @param sanitisedSearchString The complete (sanitised) search string which contains the
   *  specified search terms. This is needed to look for any child search terms associated with the
   *  specified parent terms.
   * @returns Returns a mapping of search terms to their corresponding category IDs. It will
   *  include entries for any child phrases which were identified as well.
   */
  public readonly categoriseSearchTerms = (
    searchTerms: Set<string>,
    sanitisedSearchString: string,
  ): SearchTermMapping => {
    const output: SearchTermMapping = new Map();
    searchTerms.forEach((searchTerm: string) => {
      this.categoriseSearchTerm(searchTerm, sanitisedSearchString, output);
    });
    return output;
  };

  /**
   * Find the categories relating to the specified search term and add them to the provided map.
   * This will also look for matching child phrases.
   *
   * @param searchTerm The search term being categorised. This should match one of the parent search
   *  terms stored in this object (i.e. not a child phrase), and must have been found in the
   *  specified sanitised search string.
   * @param sanitisedSearchString The complete (sanitised) search string which contains the
   *  specified search term. This is needed to look for any child search terms associated with the
   *  specified parent term.
   * @param mapping This is an output parameter which receives a mapping of search terms to their
   *  corresponding category IDs. New categorisation data found by this function will be added to
   *  this map; existing data in it will not be overwritten.
   */
  public readonly categoriseSearchTerm = (
    searchTerm: string,
    sanitisedSearchString: string,
    mapping: SearchTermMapping,
  ): void => {
    // Do nothing if the specified search term isn't recognised.
    if (!Object.prototype.hasOwnProperty.call(this._searchTerms, searchTerm)) {
      return;
    }

    const searchTermData = this._searchTerms[searchTerm];
    if (!isValidSearchTermData(searchTermData)) {
      console.warn(
        `SearchTermCategoriser - cannot categorise search term due to invalid blocklist entry: ${searchTerm}`,
      );
      return;
    }

    // Output any categories associated directly with the search term.
    searchTermData.catsAndScores.forEach((category: SearchTermCategory) => {
      SearchTermCategoriser.addCategoryToMapping(searchTerm, category, mapping);
    });

    // Output any categories associated with child terms.
    searchTermData.children.forEach((child: SearchTermChild) => {
      // Any given child only applies if all associated phrases are found in the search string.
      if (child.phrases.every((phrase: string) => sanitisedSearchString.includes(phrase))) {
        const fullPhrase = [searchTerm, ...child.phrases].join(',');
        SearchTermCategoriser.addCategoryToMapping(fullPhrase, child.catsAndScores, mapping);
      }
    });
  };

  /**
   * Insert a category ID into a search term category map.
   * This is used to build up the result in categoriseUrl().
   *
   * @param fullSearchTerm The search term which was found in the search string. If this corresponds
   *  to a child search term, then it should be a comma-separated list starting with the parent
   *  search term, followed by each child phrase.
   * @param searchTermCategory Contains information about the category to add.
   * @param mapping The category will be added into this map. This will create or update an entry as
   *  necessary to avoid overwriting existing values.
   */
  public static addCategoryToMapping = (
    fullSearchTerm: string,
    searchTermCategory: SearchTermCategory,
    mapping: SearchTermMapping,
  ): void => {
    const entry = mapping.get(fullSearchTerm);
    if (entry === undefined) {
      // We need to add a new entry.
      mapping.set(fullSearchTerm, new Set([searchTermCategory.category.toString()]));
      return;
    }
    // Add the category to the existing entry.
    entry.add(searchTermCategory.category.toString());
  };

  /**
   * Parse a single extraction pattern entry from the blocklist file.
   * The extraction patterns are specified in a slightly JSON-esque way. Each line contains one
   *  regular expression enclosed in single quotation marks. There is also a trailing comma.
   * However, there is no additional escaping within the strings (beyond what would be required for
   *  the regular expression anyway), and there are no opening or closing square brackets enclosing
   *  the list.
   *
   * @param line A single line from the search engine regular expression blocklist file. It should
   *  not contain any line break characters.
   * @returns A regular expression object constructed by parsing the specified line.
   * @throws {Error} The line did not match the expected structure, or the regular expression was
   *  invalid.
   */
  public static readonly parseExtractionPatternFromBlocklistLine = (line: string): RegExp => {
    if (line.length < 4 || !line.startsWith("'") || !line.endsWith("',")) {
      throw new Error('Extraction pattern entry does not match the expected format.');
    }
    return new RegExp(line.slice(1, -2));
  };

  /**
   * Parse a list of extraction patterns from a blocklist file which has been split into lines.
   * This will skip lines which are completely empty, or which contain no regular expression.
   *
   * @param lines An array of individual lines from the blocklist file.
   * @return An array of regular expression objects parsed from the blocklist file entries. It will
   *  omit any lines which were empty.
   * @throws {Error} One or more non-empty lines did not match the expected format, or contained an
   *  invalid regular expression.
   */
  public static readonly parseExtractionPatternsFromBlocklistFile = (lines: string[]): RegExp[] => {
    return lines
      .filter((line) => line.length > 3)
      .map(SearchTermCategoriser.parseExtractionPatternFromBlocklistLine);
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The original data from the search engine regex file in the blocklist, split into lines.
   * This will only be populated if the regular expressions were populated by loading a blocklist
   *  file. It will be null if they were set some other way.
   * It's stored to facilitate blocklist patching via Unified Diff. Patch operations will alter this
   *  array. It will then be re-parsed into the _extractionPatterns array when patching is finished.
   */
  private _originalExtractionPatternsData: string[] | null = null;

  /**
   * Regular expressions used to match search terms in a URL.
   * Having multiple patterns allows us to handle any number of URL formats from any number of
   *  websites. For example, YouTube URLs will need a different pattern from DailyMotion.
   * If the extraction patterns were loaded or patched from a blocklist file, then this will be
   *  populated by parsing _originalExtractionPatternsData.
   */
  private _extractionPatterns: RegExp[] = [];

  /**
   * A collection of search terms and the categories they belong to.
   */
  private _searchTerms: SearchTerms = {};

  /**
   * A prefix tree which will be used to find known search terms efficiently.
   * This uses the Aho-Corasick algorithm.
   */
  private _lookupTree: AhoCorasick = new AhoCorasick([]);
}

// -------------------------------------------------------------------------------------------------

/**
 * Represents a mapping of search terms to associated category IDs.
 * Each key is a known search term. If a term has associated child phrases, then this may be a
 *  comma-separated list consisting of the parent search term followed by all of its child phrases.
 * Each value is a set of all the category IDs associated with that search term.
 * The category IDs are all treated as strings for consistency, but in practice they will often be
 *  numeric.
 *
 * @todo Possible use a more nested structure where child phrases are listed in an array? This would
 *  be more similar to the weighted phrases output.
 */
export interface SearchTermMapping extends Map<string, Set<string>> {}

// -------------------------------------------------------------------------------------------------

/**
 * Contains the result of a URL categorisation by search term.
 *
 * @todo Refactor this to be more similar to WeightPhraseCategoriser.Results.
 */
export class SearchTermCategoriserResult {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Optionally initialise the contents of the object in the constructor.
   *
   * @param searchString The original complete search string extracted from the URL. If undefined,
   *  an empty search string will be stored.
   * @param mapping Maps the recognised search terms to their associated category IDs.
   */
  public constructor(searchString?: string, mapping?: SearchTermMapping) {
    if (searchString !== undefined) {
      this.searchString = searchString;
    }

    if (mapping !== undefined) {
      this.mapping = mapping;
    }
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Get a set of all the category IDs which occur in the results.
   * These are extracted automatically from the stored mapping.
   */
  public get categories(): Set<string> {
    const output = new Set<string>();
    this.mapping.forEach((searchTermCategories: Set<string>) => {
      searchTermCategories.forEach((category: string) => {
        output.add(category);
      });
    });
    return output;
  }

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The original search string extracted from the URL.
   * This will be an empty string if no search string could be extracted from the URL.
   */
  public searchString: string = '';

  /**
   * Matches recognised search terms to the corresponding category ID(s).
   * Each key is a known search term which was extracted from the URL.
   * Each value is a set of category IDs which that search term is associated with.
   * This map will be empty if no recognised search terms were found.
   */
  public mapping: SearchTermMapping = new Map();
}

// -------------------------------------------------------------------------------------------------

/**
 * Describes an object containing categorisation data used in search term analysis.
 * This describes the contents of the "searchterms" blocklist file.
 *
 * The name of each record is the content of the search term.
   The value is an object describing the category (or categories) it is assigned to.
 *
 * @see SearchTermCategoriser
 */
export type SearchTerms = Record<string, SearchTermData>;

// -------------------------------------------------------------------------------------------------

/**
 * Defines how a word or phrase is categorised for search term analysis.
 * This describes the contents of one search term in the "searchterms" blocklist file.
 *
 * @note The value of the search term (i.e. the text that would have to be typed by the user) is
 *  stored in one level higher, in the SearchTerms object.
 *
 * @see SearchTermCategoriser
 * @see SearchTerms
 */
export interface SearchTermData {
  /**
   * Lists the categories associated with the search term.
   * Each category also has a score, indicating how likely or relevant the category is.
   */
  catsAndScores: SearchTermCategory[];

  /**
   * Additional words or phrases which could alter the categorisation.
   */
  children: SearchTermChild[];
}

/**
 * Check that the specified object matches the expected structure of a SearchTermData instance.
 * This also validates all the nested elements in "catsAndScores" and "children".
 *
 * @param obj The object to validate.
 * @returns Returns true if the object looks like a valid instance of SearchTermData. Returns
 *  false if not.
 */
export const isValidSearchTermData = (obj?: any): boolean => {
  return (
    obj !== null &&
    typeof obj === 'object' &&
    Array.isArray(obj.catsAndScores) &&
    Array.isArray(obj.children) &&
    Boolean(obj.catsAndScores.every(isValidSearchTermCategory)) &&
    obj.children.every(isValidSearchTermChild)
  );
};

// -------------------------------------------------------------------------------------------------

/**
 * Describes a category which a search term is assigned to.
 *
 * @see SearchTermCategoriser
 * @see SearchTerm
 */
export interface SearchTermCategory {
  /**
   * The ID of the category assigned to the search term.
   * When using the standard blocklist, this is a numeric ID.
   * When using custom search terms, this is a string.
   * In this application, we always convert it to a string.
   */
  category: number | string;

  /**
   * Indicates how strongly the search term matches the category.
   * This is a percentage weighting. 100 means the search term is definitely associated with the
   *  specified category. 0 means it's definitely not associated with it.
   *
   * @note This isn't currently used. All scores are assumed to be 100.
   * @note This may be stored as a string sometimes.
   */
  score: number | string;
}

/**
 * Check that the specified object matches the expected structure of a SearchTermCategory instance.
 * Validation includes ensuring none of the category identifiers are empty strings.
 *
 * @param obj The object to validate.
 * @returns Returns true if the object looks like a valid instance of SearchTermCategory. Returns
 *  false if not.
 */
export const isValidSearchTermCategory = (obj?: any): boolean => {
  return (
    obj !== null &&
    typeof obj === 'object' &&
    ['string', 'number'].includes(typeof obj.category) &&
    obj.category !== '' &&
    (typeof obj.score === 'number' || typeof obj.score === 'string')
  );
};

// -------------------------------------------------------------------------------------------------

/**
 * Describes an additional word or phrase which can affect the categorisation of a search term.
 * For example, the original search term might be "tshirt". On its own, it may be innocent. However,
 *  if associated with the word "wet", then it is likely to be a pornographic reference. In this
 *  case, "wet" would be the search term child.
 *
 * @see SearchTerm
 */
export interface SearchTermChild {
  /**
   * One or more additional words or phrases which can modify the original categorisation.
   * If this array contains multiple items then all of them must be present for this child term to
   *  match.
   */
  phrases: string[];

  /**
   * The new categories associated with the search term when the child phrases are also found.
   */
  catsAndScores: SearchTermCategory;
}

/**
 * Check that the specified object matches the expected structure of a SearchTermChild instance.
 * Validation includes ensuring that none of the child phrases are empty.
 * This also validates the nested "catsAndScores" property using isValidSearchTermCategory().
 *
 * @param obj The object to validate.
 * @returns Returns true if the object looks like a valid instance of SearchTermChild. Returns false
 *  if not.
 */
export const isValidSearchTermChild = (obj?: any): boolean => {
  return (
    obj !== null &&
    typeof obj === 'object' &&
    Array.isArray(obj.phrases) &&
    obj.phrases.length > 0 &&
    Boolean(obj.phrases.every((phrase: string) => typeof phrase === 'string' && phrase !== '')) &&
    isValidSearchTermCategory(obj.catsAndScores)
  );
};

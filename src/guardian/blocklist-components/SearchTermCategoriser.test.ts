import DiffMap from 'guardian/patching/DiffMap';
import SearchTermCategoriser, * as stc from './SearchTermCategoriser';
import UnifiedDiffMapBuilder from 'guardian/patching/UnifiedDiffMapBuilder';

const testExtractionPatterns = [
  /^https?:\/\/search\.example\.com\/?\?q=([^&]+)/,
  /^https?:\/\/(?:\w+\.)*google\.\w+\/search\?query=([^&]+)/,
];

const testExtractionPatternFile = `\
'^https?:\\/\\/search\\.example\\.com\\/?\\?q=([^&]+)',
'^https?:\\/\\/(?:\\w+\\.)*google\\.\\w+\\/search\\?query=([^&]+)',`;

const testSearchTerms: stc.SearchTerms = {
  // Standalone search terms:
  ' bad mojo ': {
    catsAndScores: [
      {
        category: 111,
        score: 100,
      },
      {
        category: 222,
        score: 100,
      },
    ],
    children: [],
  },

  ' hello world ': {
    catsAndScores: [
      {
        category: 333,
        score: 100,
      },
    ],
    children: [],
  },

  // Compound search term (only matches with child phrases):
  ' smoothwall ': {
    catsAndScores: [],
    children: [
      {
        phrases: ['how', 'disable'],
        catsAndScores: {
          category: 444,
          score: 100,
        },
      },
    ],
  },

  // Standalone + compound search term:
  ' games ': {
    catsAndScores: [
      {
        category: 555,
        score: 100,
      },
    ],
    children: [
      {
        phrases: ['adult'],
        catsAndScores: {
          category: 666,
          score: 100,
        },
      },
    ],
  },
};

describe('SearchTermCategoriser', () => {
  // -----------------------------------------------------------------------------------------------

  describe('constructor()', () => {
    it('stores the specified extraction patterns', () => {
      const categoriser = new SearchTermCategoriser(testExtractionPatterns, testSearchTerms);
      const url = new URL('https://search.example.com?q=hello%20world');
      expect(categoriser.extractSearchStringFromUrl(url)).toEqual('hello world');
    });

    it('stores the specified search terms', () => {
      const categoriser = new SearchTermCategoriser(testExtractionPatterns, testSearchTerms);
      const mapping: stc.SearchTermMapping = new Map();
      categoriser.categoriseSearchTerm(' bad mojo ', ' bad mojo ', mapping);
      expect(mapping.get(' bad mojo ')).toEqual(new Set(['111', '222']));
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('setExtractionPatterns()', () => {
    it('stores the specified extraction patterns', () => {
      const categoriser = new SearchTermCategoriser();
      categoriser.setExtractionPatterns(testExtractionPatterns);
      const url = new URL('https://search.example.com?q=hello%20world');
      expect(categoriser.extractSearchStringFromUrl(url)).toEqual('hello world');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('setSearchTerms()', () => {
    it('stores the specified search terms', () => {
      const categoriser = new SearchTermCategoriser();
      categoriser.setSearchTerms(testSearchTerms);
      const mapping: stc.SearchTermMapping = new Map();
      categoriser.categoriseSearchTerm(' bad mojo ', ' bad mojo ', mapping);
      expect(mapping.get(' bad mojo ')).toEqual(new Set(['111', '222']));
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('length()', () => {
    it('returns 0 if no search terms have been loaded yet', () => {
      const searchTermCategoriser = new SearchTermCategoriser();
      expect(searchTermCategoriser.length).toEqual(0);
    });

    it('returns the number of search terms loaded', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      expect(searchTermCategoriser.length).toEqual(4);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('loadExtractionPatternsFromBlocklistFile()', () => {
    it('stores the specified extraction patterns', () => {
      const categoriser = new SearchTermCategoriser();
      categoriser.loadExtractionPatternsFromBlocklistFile(testExtractionPatternFile);
      const url = new URL('https://search.example.com?q=hello%20world');
      expect(categoriser.extractSearchStringFromUrl(url)).toEqual('hello world');
    });

    it('throws an error if the file contains an invalid regular expression', () => {
      const file = testExtractionPatternFile + '\n' + String.raw`example).com\/?q=(.*)`;
      const categoriser = new SearchTermCategoriser();
      expect(() => {
        categoriser.loadExtractionPatternsFromBlocklistFile(file);
      }).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('loadSearchTermsFromBlocklistFile()', () => {
    it('stores the specified search terms', () => {
      const categoriser = new SearchTermCategoriser();
      categoriser.loadSearchTermsFromBlocklistFile(JSON.stringify(testSearchTerms));
      const mapping: stc.SearchTermMapping = new Map();
      categoriser.categoriseSearchTerm(' bad mojo ', ' bad mojo ', mapping);
      expect(mapping.get(' bad mojo ')).toEqual(new Set(['111', '222']));
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('clear()', () => {
    it('deletes all extraction patterns', () => {
      const searchTermCategoriser = new SearchTermCategoriser(testExtractionPatterns);
      searchTermCategoriser.clear();
      const url = new URL('https://search.example.com?q=hello%20world');
      // We shouldn't be able to extract a search string now:
      expect(searchTermCategoriser.extractSearchStringFromUrl(url)).toEqual('');
    });

    it('prevents the extraction patterns from being patched', () => {
      // Extraction patterns cannot be patched after being cleared because we'll have no blocklist
      //  data to apply the patch to.
      const categoriser = new SearchTermCategoriser();
      categoriser.loadExtractionPatternsFromBlocklistFile(testExtractionPatternFile);
      categoriser.clear();
      expect(() => {
        categoriser.patchFromUnifiedDiff(new DiffMap());
      }).toThrow();
    });

    it('deletes all search terms', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      searchTermCategoriser.clear();
      // We shouldn't have any search term categories now:
      // (note that this will output a warning to the console)
      const mapping = searchTermCategoriser.categoriseSearchTerms(new Set([' bad mojo ']), '');
      expect(mapping.has(' bad mojo ')).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('patchFromUnifiedDiff()', () => {
    it('correctly applies a diff', () => {
      // The blocklist diffs in our test data don't modify this file significantly.
      // We need to construct our own test data, based on real blocklist data.

      const oldFile = String.raw`'^https?://(?:[^/]*\.)?wish\.com/search/([^/?&])+',
'^https?://(?:[^/]*\.)?scratch\.mit\.edu/search/[^?&]*\?q=([^&]+)',
'^https?://(?:[^/]*\.)?neeva\.com\/search\?q=([^&]+)',
'^https?://(?:[^/]*\.)?exalead\.fr/search\/web\/results.*?q=([^&]+)',
'^https?://(?:[^/]*\.)?alohafind\.com/search\/\?q=([^&]+)',
'^https?://(?:[^/]*\.)?yasni\.com/.*?&query=([^&]+)',
'^https?://(?:[^/]*\.)?oscobo\.com/(?:search|images|videos|maps|news)\.php\?q=([^&]+)',
'^https?://(?:[^/]*\.)?metager\.org/meta/.*?eingabe=([^&]+)',
'^https?://(?:[^/]*\.)?kidssearch\.com/search\.html\?s=([^&]+)',
'^https?://(?:[^/]*\.)?venta\.com\.(?:mx|ar)/(?:f\/.*?\?)query=([^&]+)',
`;

      const newFile = String.raw`'^https?://(?:[^/]*\.)?wish\.com/search/([^/?&])+',
'^https?://(?:[^/]*\.)?api-partner\.spotify\.com/pathfinder.*?search[Tt]erm%22%3A%22([^&%]+)',
'^https?://(?:[^/]*\.)?fireball\.de(?:\/de)?\/search\?q=([^&]+)',
'^https?://(?:[^/]*\.)?excite\.(?:co.jp|com|es|de|co\.uk|nl|it|fr)/(?:serp)?\?q=([^&]+)',
'^https?://(?:[^/]*\.)?exalead\.fr/search\/web\/results.*?q=([^&]+)',
'^https?://(?:[^/]*\.)?alohafind\.com/search\/\?q=([^&]+)',
'^https?://(?:[^/]*\.)?yasni\.com/.*?&query=([^&]+)',
'^https?://(?:[^/]*\.)?oscobo\.com/(?:search|images|videos|maps|news)\.php\?q=([^&]+)',
'^https?://(?:[^/]*\.)?kidssearch\.com/search\.html\?s=([^&]+)',
'^https?://(?:[^/]*\.)?venta\.com\.(?:mx|ar)/(?:f\/.*?\?)query=([^&]+)',
'^https?://(?:[^/]*\.)?used\.forsale/(?:f\/.*?\?)query=([^&]+)',
`;

      const unifiedDiff = String.raw`
--- a/searchengineregexplist	2025-01-02 03:04:05.000 +0000
+++ b/searchengineregexplist	2025-01-03 03:04:05.000 +0000
@@ -2,2 +2,3 @@
-'^https?://(?:[^/]*\.)?scratch\.mit\.edu/search/[^?&]*\?q=([^&]+)',
-'^https?://(?:[^/]*\.)?neeva\.com\/search\?q=([^&]+)',
+'^https?://(?:[^/]*\.)?api-partner\.spotify\.com/pathfinder.*?search[Tt]erm%22%3A%22([^&%]+)',
+'^https?://(?:[^/]*\.)?fireball\.de(?:\/de)?\/search\?q=([^&]+)',
+'^https?://(?:[^/]*\.)?excite\.(?:co.jp|com|es|de|co\.uk|nl|it|fr)/(?:serp)?\?q=([^&]+)',
@@ -8,1 +8,0 @@
-'^https?://(?:[^/]*\.)?metager\.org/meta/.*?eingabe=([^&]+)',
@@ -10,0 +11,1 @@
+'^https?://(?:[^/]*\.)?used\.forsale/(?:f\/.*?\?)query=([^&]+)',
`;

      // Load the old unpatched data.
      const oldComponent = new SearchTermCategoriser();
      oldComponent.loadExtractionPatternsFromBlocklistFile(oldFile);

      // An entry which is removed by the patch should work correctly before patching.
      const input1 = new URL('https://neeva.com/search?q=blah');
      expect(oldComponent.extractSearchStringFromUrl(input1)).toEqual('blah');

      // An entry which is added by the patch should not work before patching.
      const input2 = new URL('https://used.forsale/f/x?query=blah');
      expect(oldComponent.extractSearchStringFromUrl(input2)).toBeEmpty();

      // An entry which is not modified by the patch should work before and after patching.
      const input3 = new URL('https://venta.com.mx/f/x?query=blah');
      expect(oldComponent.extractSearchStringFromUrl(input3)).toEqual('blah');

      // Apply the patch.
      const diffMaps = [...UnifiedDiffMapBuilder.buildFromFile(unifiedDiff)];
      oldComponent.patchFromUnifiedDiff(diffMaps[0]);

      // An entry which is removed by the patch should not work after patching.
      expect(oldComponent.extractSearchStringFromUrl(input1)).toBeEmpty();

      // An entry which is added by the patch should work correctly after patching.
      expect(oldComponent.extractSearchStringFromUrl(input2)).toEqual('blah');

      // An entry which is not modified by the patch should work before and after patching.
      expect(oldComponent.extractSearchStringFromUrl(input3)).toEqual('blah');

      // TODO: When we have the ability to save the result of patching, compare the output of that
      //  against the expected result, instead of accessing private properties.
      const newComponent = new SearchTermCategoriser();
      newComponent.loadExtractionPatternsFromBlocklistFile(newFile);
      expect((oldComponent as any)._originalData).toEqual((newComponent as any)._originalData);
    });

    it('throws an error if the extraction patterns were not loaded from a blocklist file', () => {
      const categoriser = new SearchTermCategoriser(testExtractionPatterns);
      expect(() => {
        categoriser.patchFromUnifiedDiff(new DiffMap());
      }).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('categoriserUrl()', () => {
    it('returns an object containing the sanitised search string', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      const url = new URL('https://www.google.com/search?query=Hello+world%21+blah+BLAH+blah');
      const result = searchTermCategoriser.categoriseUrl(url);
      expect(result.searchString).toEqual(' hello world blah blah blah ');
    });

    it('returns an object containing map of search terms to category IDs', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      const url = new URL(
        'https://www.google.com/search?query=How+do+I+disable+Smoothwall+so+I+can+play+games+by+Bad+Mojo',
      );
      const result = searchTermCategoriser.categoriseUrl(url);
      expect(result.mapping.get(' smoothwall ,how,disable')).toEqual(new Set(['444']));
      expect(result.mapping.get(' games ')).toEqual(new Set(['555']));
      expect(result.mapping.get(' bad mojo ')).toEqual(new Set(['111', '222']));
    });

    it('returns an object which can provide a set of all matched categories', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      const url = new URL(
        'https://www.google.com/search?query=How+do+I+disable+Smoothwall+so+I+can+play+games+by+Bad+Mojo',
      );
      const result = searchTermCategoriser.categoriseUrl(url);
      expect(result.categories).toEqual(new Set(['111', '222', '444', '555']));
    });

    it('returned search string is empty if url did not match any extraction patterns', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      const url = new URL('https://example.net/bad+mojo');
      const result = searchTermCategoriser.categoriseUrl(url);
      expect(result.searchString).toEqual('');
    });

    it('returned category map is empty if url did not match any extraction patterns', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      const url = new URL('https://example.net/bad+mojo');
      const result = searchTermCategoriser.categoriseUrl(url);
      expect(result.mapping).toEqual(new Map());
    });

    it('returns category map is empty if no recognised search terms were found', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      const url = new URL('https://www.google.com/search?query=what+is+the+capital+of+france');
      const result = searchTermCategoriser.categoriseUrl(url);
      expect(result.mapping).toEqual(new Map());
    });

    it('does not return information about search terms which were not found in search string', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      const url = new URL('https://www.google.com/search?query=how+to+disable+smoothwall');
      const result = searchTermCategoriser.categoriseUrl(url);
      expect(result.mapping.has(' bad mojo ')).toBeFalse();
    });

    it('is not case-sensitive', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      const url = new URL('https://www.google.com/search?query=really+BAD+mOjO+dude');
      const result = searchTermCategoriser.categoriseUrl(url);
      expect(result.mapping.has(' bad mojo ')).toBeTrue();
    });

    it('ignores non-alphanumeric characters', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      const url = new URL('https://www.google.com/search?query=really+bad!mojo%23dude');
      const result = searchTermCategoriser.categoriseUrl(url);
      expect(result.mapping.has(' bad mojo ')).toBeTrue();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('extractSearchStringFromUrl()', () => {
    it('returns the url decoded search string from the specified url', () => {
      const searchTermCategoriser = new SearchTermCategoriser(testExtractionPatterns);
      const url = new URL('https://search.example.com?q=hello%20world');
      expect(searchTermCategoriser.extractSearchStringFromUrl(url)).toEqual('hello world');
    });

    it('returns an empty string if no search string was found in the specified url', () => {
      const searchTermCategoriser = new SearchTermCategoriser(testExtractionPatterns);
      const url = new URL('https://blah.example.org/foobar');
      expect(searchTermCategoriser.extractSearchStringFromUrl(url)).toEqual('');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('sanitiseSearchString()', () => {
    it('adds a leading and trailing space if necessary', () => {
      expect(SearchTermCategoriser.sanitiseSearchString('hello world')).toEqual(' hello world ');
    });

    it('replaces non-alphanumeric characters with a space', () => {
      expect(SearchTermCategoriser.sanitiseSearchString(' hello+world!123#456 ')).toEqual(
        ' hello world 123 456 ',
      );
    });

    it('condenses multiple consecutive spaces down to a single space', () => {
      expect(SearchTermCategoriser.sanitiseSearchString('   # hello!!!  [@# world &&& ')).toEqual(
        ' hello world ',
      );
    });

    it('converts to lowercase', () => {
      expect(SearchTermCategoriser.sanitiseSearchString(' HeLlO wOrLd ')).toEqual(' hello world ');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('matchKnownSearchTerms()', () => {
    it('returns an empty set if the search string is empty', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      expect(searchTermCategoriser.matchKnownSearchTerms('')).toEqual(new Set());
    });

    it('returns an empty set if no known search terms were found', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      expect(searchTermCategoriser.matchKnownSearchTerms(' this is a test ')).toEqual(new Set());
    });

    it('returns a set containing the recognised parent search terms', () => {
      // Note: matchKnownSearchTerms() does not scan for child phrases

      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );

      expect(searchTermCategoriser.matchKnownSearchTerms(' this is bad mojo dude ')).toEqual(
        new Set([' bad mojo ']),
      );

      expect(searchTermCategoriser.matchKnownSearchTerms(' hello world of bad mojo 1 ')).toEqual(
        new Set([' hello world ', ' bad mojo ']),
      );

      expect(searchTermCategoriser.matchKnownSearchTerms(' smoothwall blocks games ')).toEqual(
        new Set([' smoothwall ', ' games ']),
      );
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('categoriseSearchTerms()', () => {
    it('returns a map containing standalone search term categories', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      const searchTerms = new Set<string>([' hello world ', ' bad mojo ', ' games ']);
      // The search string doesn't matter if we're not looking for child phrases:
      const mapping = searchTermCategoriser.categoriseSearchTerms(searchTerms, '');
      expect(mapping.get(' bad mojo ')).toEqual(new Set(['111', '222']));
      expect(mapping.get(' hello world ')).toEqual(new Set(['333']));
      expect(mapping.get(' games ')).toEqual(new Set(['555']));
    });

    it('descends into child phrases and returns their categories in the map', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      const searchTerms = new Set<string>([' smoothwall ', ' games ']);
      const searchString = ' how to disable smoothwall so i can play adult games ';
      const mapping = searchTermCategoriser.categoriseSearchTerms(searchTerms, searchString);
      expect(mapping.get(' smoothwall ,how,disable')).toEqual(new Set(['444']));
      expect(mapping.get(' games ,adult')).toEqual(new Set(['666']));
    });

    it('does not return child phrases in the map if they were not found in the search string', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      const mapping = searchTermCategoriser.categoriseSearchTerms(
        new Set([' games ']),
        ' play online games ',
      );
      expect(mapping.has(' games ,adult')).toBeFalse();
    });

    it('does not return standalone search terms in the map if they are only categorised with child phrases', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      const mapping = searchTermCategoriser.categoriseSearchTerms(
        new Set([' smoothwall ']),
        ' how to disable smoothwall ',
      );
      expect(mapping.has(' smoothwall ')).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('categoriseSearchTerm()', () => {
    it('adds standalone search term categories to the specified map', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      const mapping: stc.SearchTermMapping = new Map();
      // We don't care about the search string if we're not matching child phrases:
      searchTermCategoriser.categoriseSearchTerm(' bad mojo ', '', mapping);
      expect(mapping.get(' bad mojo ')).toEqual(new Set(['111', '222']));
    });

    it('descends into child phrases and adds their categories to the map if they are found', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      const mapping: stc.SearchTermMapping = new Map();

      searchTermCategoriser.categoriseSearchTerm(
        ' smoothwall ',
        ' how to disable smoothwall ',
        mapping,
      );
      expect(mapping.get(' smoothwall ,how,disable')).toEqual(new Set(['444']));

      searchTermCategoriser.categoriseSearchTerm(' games ', ' play adult games online ', mapping);
      expect(mapping.get(' games ')).toEqual(new Set(['555']));
      expect(mapping.get(' games ,adult')).toEqual(new Set(['666']));
    });

    it('does not add child phrases to the map if they were not found in the search string', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      const mapping: stc.SearchTermMapping = new Map();

      searchTermCategoriser.categoriseSearchTerm(' games ', ' play games online ', mapping);
      expect(mapping.has(' games ,adult')).toBeFalse();
    });

    it('does not add standalone search terms to the map if they are only categorised with child phrases', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      const mapping: stc.SearchTermMapping = new Map();

      searchTermCategoriser.categoriseSearchTerm(
        ' smoothwall ',
        ' how to disable smoothwall ',
        mapping,
      );
      expect(mapping.has(' smoothwall ')).toBeFalse();
    });

    it('does not remove existing data in the map', () => {
      const searchTermCategoriser = new SearchTermCategoriser(
        testExtractionPatterns,
        testSearchTerms,
      );
      const mapping: stc.SearchTermMapping = new Map();
      searchTermCategoriser.categoriseSearchTerm(' bad mojo ', '', mapping);
      searchTermCategoriser.categoriseSearchTerm(' hello world ', '', mapping);
      expect(mapping.get(' bad mojo ')).toEqual(new Set(['111', '222']));
      expect(mapping.get(' hello world ')).toEqual(new Set(['333']));
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('addCategoryToMapping()', () => {
    const testCategory1: stc.SearchTermCategory = {
      category: '123',
      score: 100,
    };

    const testCategory2: stc.SearchTermCategory = {
      category: '456',
      score: 100,
    };

    const testCategory3: stc.SearchTermCategory = {
      category: '789',
      score: 100,
    };

    const testNumericCategory: stc.SearchTermCategory = {
      category: 12345,
      score: 100,
    };

    it('creates search term entry if necessary', () => {
      const mapping: stc.SearchTermMapping = new Map();
      SearchTermCategoriser.addCategoryToMapping('blah', testCategory1, mapping);
      expect(mapping.get('blah')).toEqual(new Set(['123']));
    });

    it('adds category to existing search term entry', () => {
      const mapping: stc.SearchTermMapping = new Map();
      SearchTermCategoriser.addCategoryToMapping('blah', testCategory1, mapping);
      SearchTermCategoriser.addCategoryToMapping('blah', testCategory2, mapping);
      SearchTermCategoriser.addCategoryToMapping('blah', testCategory3, mapping);
      expect(mapping.get('blah')).toEqual(new Set(['123', '456', '789']));
    });

    it('has no effect if the search term and category already exist', () => {
      const mapping: stc.SearchTermMapping = new Map();
      SearchTermCategoriser.addCategoryToMapping('blah', testCategory1, mapping);
      SearchTermCategoriser.addCategoryToMapping('blah', testCategory1, mapping);
      expect(mapping.get('blah')).toEqual(new Set(['123']));
    });

    it('does not affect other search term entries', () => {
      const mapping: stc.SearchTermMapping = new Map();
      SearchTermCategoriser.addCategoryToMapping('blah', testCategory1, mapping);
      SearchTermCategoriser.addCategoryToMapping('blah', testCategory2, mapping);
      SearchTermCategoriser.addCategoryToMapping('foobar', testCategory3, mapping);

      expect(mapping.get('blah')).toEqual(new Set(['123', '456']));
      expect(mapping.get('foobar')).toEqual(new Set(['789']));
    });

    it('converts numeric category IDs to strings', () => {
      const mapping: stc.SearchTermMapping = new Map();
      SearchTermCategoriser.addCategoryToMapping('blah', testNumericCategory, mapping);
      expect(mapping.get('blah')).toEqual(new Set(['12345']));
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('parseExtractionPatternFromBlocklistLine()', () => {
    it('returns the regular expression parsed from the blocklist line', () => {
      const re =
        SearchTermCategoriser.parseExtractionPatternFromBlocklistLine("'[a-z]{3}[0-9]{3}',");
      expect(re.test('abc123')).toBeTrue();
      expect(re.test('123abc')).toBeFalse();
    });

    it('throws an error if the line is not formatted correctly', () => {
      // Missing opening quotation mark.
      expect(() => {
        SearchTermCategoriser.parseExtractionPatternFromBlocklistLine("[a-z]{3}[0-9]{3}',");
      }).toThrow();

      // Missing closing quotation mark.
      expect(() => {
        SearchTermCategoriser.parseExtractionPatternFromBlocklistLine("'[a-z]{3}[0-9]{3},");
      }).toThrow();

      // Missing comma.
      expect(() => {
        SearchTermCategoriser.parseExtractionPatternFromBlocklistLine("'[a-z]{3}[0-9]{3}'");
      }).toThrow();

      // Empty string.
      expect(() => {
        SearchTermCategoriser.parseExtractionPatternFromBlocklistLine('');
      }).toThrow();
    });

    it('throws an error if the regular expression is invalid', () => {
      expect(() => {
        SearchTermCategoriser.parseExtractionPatternFromBlocklistLine("'ab)c3{',");
      }).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('parseExtractionPatternsFromBlocklistFile()', () => {
    it('returns an array of regular expressions parsed from the given lines', () => {
      const input = ["'abc(\\d)',", "'def(\\d)',", "'ghi(\\d)',"];
      const actual = SearchTermCategoriser.parseExtractionPatternsFromBlocklistFile(input);
      expect(actual).toBeArrayOfSize(3);
      expect(actual[0].test('abc1')).toBeTrue();
      expect(actual[1].test('def2')).toBeTrue();
      expect(actual[2].test('ghi3')).toBeTrue();
    });

    it('omits blank lines from the results', () => {
      const input = ["'abc(\\d)',", '', "'def(\\d)',", '', "'ghi(\\d)',"];
      const actual = SearchTermCategoriser.parseExtractionPatternsFromBlocklistFile(input);
      expect(actual).toBeArrayOfSize(3);
      expect(actual[0].test('abc1')).toBeTrue();
      expect(actual[1].test('def2')).toBeTrue();
      expect(actual[2].test('ghi3')).toBeTrue();
    });

    it('throws an error if a regular expression is invalid', () => {
      const input = ["'abc(\\d)',", "'def)\\d(',", "'ghi(\\d)',"];
      expect(() => {
        SearchTermCategoriser.parseExtractionPatternsFromBlocklistFile(input);
      }).toThrow();
    });
  });
});

// -------------------------------------------------------------------------------------------------

describe('SearchTermCategorisationResult', () => {
  describe('constructor()', () => {
    it('defaults to an empty search string', () => {
      const result = new stc.SearchTermCategoriserResult();
      expect(result.searchString).toEqual('');
    });

    it('stores the specified search string', () => {
      const result = new stc.SearchTermCategoriserResult('blah blah blah');
      expect(result.searchString).toEqual('blah blah blah');
    });

    it('defaults to an empty search term mapping', () => {
      const result = new stc.SearchTermCategoriserResult();
      expect(result.mapping).toEqual(new Map());
    });

    it('stores the specified search term mapping', () => {
      // const mapping = new Map
      const result = new stc.SearchTermCategoriserResult('blah blah blah');
      expect(result.mapping).toEqual(new Map());
    });
  });
});

// -------------------------------------------------------------------------------------------------

describe('SearchTermData.isValidSearchTermData()', () => {
  it('returns true if the specified value matches the SearchTermData structure', () => {
    const obj = {
      catsAndScores: [
        {
          category: 111,
          score: 100,
        },
        {
          category: 222,
          score: 100,
        },
      ],
      children: [
        {
          phrases: ['foo', 'bar'],
          catsAndScores: {
            category: 333,
            score: 100,
          },
        },
        {
          phrases: ['blah'],
          catsAndScores: {
            category: 444,
            score: 100,
          },
        },
      ],
    };
    expect(stc.isValidSearchTermData(obj)).toBeTrue();
  });

  it('returns false if the specified value is null', () => {
    expect(stc.isValidSearchTermData(null)).toBeFalse();
  });

  it('returns false if the specified value is undefined', () => {
    expect(stc.isValidSearchTermData(null)).toBeFalse();
  });

  it('returns false if the specified value is not an object', () => {
    expect(stc.isValidSearchTermData(123)).toBeFalse();
  });

  it('returns false if a property is missing', () => {
    const obj = { children: [] };
    expect(stc.isValidSearchTermData(obj)).toBeFalse();
  });

  it('returns false if a property has the wrong type', () => {
    const obj = { catsAndScores: 123, children: [] };
    expect(stc.isValidSearchTermData(obj)).toBeFalse();
  });
});

// -------------------------------------------------------------------------------------------------

describe('SearchTermCategory.isValidSearchTermCategory()', () => {
  it('returns true if the specified value matches the SearchTermCategory structure', () => {
    expect(stc.isValidSearchTermCategory({ category: 111, score: 100 })).toBeTrue();
    expect(stc.isValidSearchTermCategory({ category: 'abc', score: 100 })).toBeTrue();
  });

  it('returns false if the specified value is null', () => {
    expect(stc.isValidSearchTermCategory(null)).toBeFalse();
  });

  it('returns false if the specified value is undefined', () => {
    expect(stc.isValidSearchTermCategory(null)).toBeFalse();
  });

  it('returns false if the specified value is not an object', () => {
    expect(stc.isValidSearchTermCategory(123)).toBeFalse();
  });

  it('returns false if a property is missing', () => {
    expect(stc.isValidSearchTermCategory({ score: 100 })).toBeFalse();
  });

  it('returns false if a property has the wrong type', () => {
    expect(stc.isValidSearchTermCategory({ category: true, score: 100 })).toBeFalse();
  });
});

// -------------------------------------------------------------------------------------------------

describe('SearchTermChild.isValidSearchTermChild()', () => {
  it('returns true if the specified value matches the SearchTermChild structure', () => {
    const obj = {
      phrases: ['foo', 'bar'],
      catsAndScores: {
        category: 333,
        score: 100,
      },
    };
    expect(stc.isValidSearchTermChild(obj)).toBeTrue();
  });

  it('returns false if the specified value is null', () => {
    expect(stc.isValidSearchTermChild(null)).toBeFalse();
  });

  it('returns false if the specified value is undefined', () => {
    expect(stc.isValidSearchTermChild(null)).toBeFalse();
  });

  it('returns false if the specified value is not an object', () => {
    expect(stc.isValidSearchTermChild(123)).toBeFalse();
  });

  it('returns false if a property is missing', () => {
    const obj = { phrases: [] };
    expect(stc.isValidSearchTermChild(obj)).toBeFalse();
  });

  it('returns false if a property has the wrong type', () => {
    const obj = {
      phrases: ['foo', 'bar'],
      catsAndScores: {
        category: 333,
        score: true,
      },
    };
    expect(stc.isValidSearchTermChild(obj)).toBeFalse();
  });

  it('returns false there are no child phrases', () => {
    const obj = {
      phrases: [],
      catsAndScores: {
        category: 333,
        score: 100,
      },
    };
    expect(stc.isValidSearchTermChild(obj)).toBeFalse();
  });

  it('returns false if a child phrase is empty', () => {
    const obj = {
      phrases: ['foo', '', 'bar'],
      catsAndScores: {
        category: 333,
        score: 100,
      },
    };
    expect(stc.isValidSearchTermChild(obj)).toBeFalse();
  });

  it('returns false if a child phrase is not a string', () => {
    const obj = {
      phrases: ['foo', 101],
      catsAndScores: {
        category: 333,
        score: 100,
      },
    };
    expect(stc.isValidSearchTermChild(obj)).toBeFalse();
  });
});

// -------------------------------------------------------------------------------------------------

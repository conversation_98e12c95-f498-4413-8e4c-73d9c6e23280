import AccessLogEntry from 'models/AccessLogEntry';
import * as path from 'path';
import { loadLocalBlocklist } from 'test-helpers/blocklist-utilities';
import LogLevelService from './LogLevelService';
// import { logLevelRulesFile } from 'test-helpers/test-blocklist-data';

const blocklistFiles = loadLocalBlocklist(
  path.join(__dirname, '..', '..', '..', 'test-data', 'blocklists', 'Blocklist-1711770303'),
);
const logLevelRulesFile = blocklistFiles.loglevelrules;

const createLog = (): AccessLogEntry => {
  return {
    took: 1,
    time: Date.now().toString(),
    groups: [],
    categories: [],
  };
};

describe('LogLevelService', () => {
  let service: LogLevelService;

  beforeEach(() => {
    service = new LogLevelService();
  });

  describe('clear()', () => {
    it('has no effect if no data has been loaded', () => {
      expect(() => {
        service.clear();
      }).not.toThrow();
    });

    it('discards all previously load log level data', () => {
      service.loadFromBlocklistFile(logLevelRulesFile);
      service.clear();
      expect(service.length).toEqual(0);
    });
  });

  describe('clear()', () => {
    it('has no effect if no data has been loaded', () => {
      expect(() => {
        service.clear();
      }).not.toThrow();
    });

    it('discards all previously load log level data', () => {
      service.loadFromBlocklistFile(logLevelRulesFile);
      service.clear();
      expect(service.length).toEqual(0);
    });
  });

  describe('loadFromBlocklistFile', () => {
    it('loads a valid log level file', () => {
      service.loadFromBlocklistFile(logLevelRulesFile);
      service.loadFromBlocklistFile(logLevelRulesFile);

      expect(service.length).toBe(JSON.parse(logLevelRulesFile).length);
      expect(service.length).toBe(JSON.parse(logLevelRulesFile).length);
    });
  });

  describe('applyLogLevel', () => {
    let log: AccessLogEntry;

    beforeEach(() => {
      log = createLog();
      service.loadFromBlocklistFile(logLevelRulesFile);
      service.loadFromBlocklistFile(logLevelRulesFile);
    });

    it('returns the default level if no regex matches', () => {
      const result = service.applyLogLevel(log);

      expect(result).toBe(3);
    });

    it('returns the correct level if a positive regex matches', () => {
      log.title = 'Google';

      const result = service.applyLogLevel(log);

      expect(result).toBe(4);
    });

    it('returns the correct level if it does not match a negative regex', () => {
      log.categories.push('Drugs');

      const result = service.applyLogLevel(log);

      expect(result).toBe(5);
    });

    it('If an exclusion match is found among the list of log categories then the higher level category should be ignored and the log should be categorised against the exclusion', () => {
      // A level 5 category
      log.categories.push('Drugs');
      // The exclusion category
      log.categories.push('Advertising');

      const result = service.applyLogLevel(log);

      expect(result).toBe(2); // Advertising has log level 2
    });

    it('If an exclusion match is found among the list of log categories then the higher level category should be ignored and the log should be categorised against the exclusion level', () => {
      // A level 5 category
      log.categories.push('Weapons');
      // The exclusion category
      log.categories.push('Military');

      const result = service.applyLogLevel(log);

      expect(result).toBe(3); // Military does not have an explicit level so the default level of 3 will be returned
    });

    it('If an exclusion match is found among the list of log categories then the higher level category should be ignored and the log should be categorised against the exclusion level, if the exclusion level does not have an explicit level then the default level of 3 will be returned', () => {
      // A level 5 category
      log.categories.push('Alcohol and Tobacco');
      // The exclusion category
      log.categories.push('Medical Information');

      const result = service.applyLogLevel(log);

      expect(result).toBe(3); // Medical information does not have an explicit level so the default level of 3 will be returned
    });

    it('If an exclusion match is not found among the list of log categories then the log will be categorised against the normal level', () => {
      // A level 5 category
      log.categories.push('Alcohol and Tobacco');

      const result = service.applyLogLevel(log);

      expect(result).toBe(5);
    });

    it('If a log has multiple non-exclusion levels then the log will be given the highest level out of all of the categories', () => {
      // A level 5 category
      log.categories.push('Alcohol and Tobacco');
      log.categories.push('Tracking');

      const result = service.applyLogLevel(log);

      expect(result).toBe(5);
    });
  });
});

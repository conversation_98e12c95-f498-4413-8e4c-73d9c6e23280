import CategoriserResult from 'guardian/models/CategoriserResult';
import CategorisationMap from '../utilities/CategorisationMap';
import * as GuardianUtilities from '../utilities/GuardianUtilities';
import { ICustomCategory } from '../../models/PolicyConfigModels';

/**
 * Read-only interface for a blocklist component which categorises URLs from a known list.
 *
 * @see UrlListCategoriser
 */
export interface IUrlListCategoriser {
  /**
   * Get the total number of URLs in the categorisation maps for all top-level domains.
   *
   * @note This does not check for duplicates. If the same URL appears multiple times then each
   *  occurrence will be counted separately.
   */
  length: number;

  /**
   * Get the categories which the specified URL matches based on the stored blocklist data.
   *
   * @param url The URL to categorise.
   * @param customCategories Any optional categories that should be checked that are not a part of the main blocklist data.
   * @returns Returns an object containing set of all the category IDs which the specified URL
   *  belongs to. It is an empty set if the URL did not match any categories.
   */
  categoriseUrl: (url: URL, customCategories?: ICustomCategory[]) => UrlListCategoriserResult;
}

/**
 * Blocklist component for categorising URLs based on one or more lists of known domains and URLs.
 *
 * The URLs in the categorisation maps must be formatted according to the G4 domainsurls blocklist.
 * This means:
 *
 * - All URLs must be lower-case.
 * - Each domain name must be written backwards, and appended with a dot.
 * - If the URL consists of a domain + query string (no path) then there must be a forward slash before the query string.
 * - The scheme/protocol prefix must be omitted.
 * - The path and query string must appear as normal (forwards).
 * - Where a categorisation map corresponds to a specific top-level domain, the top-level domain
 *  must be omitted from the map.
 * - Each URL is followed by a pipe character, then a comma separated list of all the category IDs
 *  it belongs to.
 * - The data must be sorted alphabetically by URL.
 *
 * Example 1: If the original URL is "https://www.google.com/search?query=foo", this would appear in
 *  a ".com" categorisation map as "elgoog.www./search?query=foo|100,390". The top-level domain is
 *  ommitted from the map as it's part of a TLD-specific map.
 *
 * Example 2: If the original URL is "http://mobile.example.biz/foo/bar", this might appear in a
 *  generic (non-TLD specific) categorisation map as "zib.elpmaxe.elibom./foo/bar|19,244,367". The
 *  top-level domain is included in the map as it's part of a generic map.
 *
 * @see https://familyzone.atlassian.net/wiki/spaces/DST/pages/2694335858368/Components
 */
export default class UrlListCategoriser {
  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Get the URL and category mapping for an individual top-level domain.
   *
   * @param topLevelDomain The top-level domain for which to get the URL and category mapping. If
   *  empty, this will get the generic mapping which isn't related to any specific top-level domain.
   * @returns The URL and category mapping for the specified top-level domain, if it exists. Returns
   *  undefined if no mapping has been loaded for the top-level domain.
   */
  public readonly getCategorisationMap = (
    topLevelDomain: string,
  ): CategorisationMap | undefined => {
    return this._tldCategorisationMaps.get(topLevelDomain.toLowerCase());
  };

  /**
   * Set the URL and category mapping for an individual top-level domain.
   *
   * @param categorisationMap A mapping of URLs to their associated categories. See the class
   *  doc-comment for details on the format required.
   * @param topLevelDomain The top-level domain which the map relates to. If specified, it's assumed
   *  that this TLD has been omitted from entries in the map (to save space). If empty, TLDs must
   *  not be omitted from the map.
   *
   * @warning The specified categorisation map is not cloned. It is the caller's responsibility to
   *  ensure nothing else modifies it while it is being used by this object.
   */
  public readonly setCategorisationMap = (
    categorisationMap: CategorisationMap,
    topLevelDomain: string = '',
  ): void => {
    // TODO: Validate the categorisation map in development/debug mode?
    this._tldCategorisationMaps.set(topLevelDomain.toLowerCase(), categorisationMap);
  };

  /**
   * Get the total number of URLs in the categorisation maps for all top-level domains.
   *
   * @note This does not check for duplicates. If the same URL appears multiple times then each
   *  occurrence will be counted separately.
   */
  public get length(): number {
    let length: number = 0;
    this._tldCategorisationMaps.forEach((categorisationMap: CategorisationMap): void => {
      length += categorisationMap.length;
    });
    return length;
  }

  // -----------------------------------------------------------------------------------------------
  // Loading/saving.

  /**
   * Load a blocklist file containing the URL category rules for a specific top-level domain.
   * This replaces any existing URL category rules already loaded for the same top-level domain, but
   *  it does not affect any other top-level domains.
   *
   * @param data The contents of the blocklist file to load. This must be formatted as JSON, with
   *  an array at the top level. Each array element should be a string containing the URL followed
   *  by a pipe character, then a comma-separated list of category IDs. The domain part of the URL
   *  should be written backwards and a dot should be appended. Additionally, if a top-level domain
   *  is specified in the other function parameter, then it should be omitted from the URL list. The
   *  data should be sorted alphabetically by URL.
   * @param topLevelDomain The top-level domain which the URL list relates to. If specified, it's
   *  assumed that this TLD has been omitted from entries in the map (to save space). If empty, TLDs
   *  must not be omitted from the map.
   */
  public loadFromBlocklistFile = (data: string, topLevelDomain?: string): void => {
    const parsed = JSON.parse(data);
    if (!Array.isArray(parsed)) {
      throw new TypeError('Failed to parse URL list. Expected an array.');
    }
    this.setCategorisationMap(new CategorisationMap(parsed, '|', ','), topLevelDomain);
  };

  /**
   * Delete the stored blocklist data.
   */
  public readonly clear = (): void => {
    this._tldCategorisationMaps = new Map();
  };

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Get the categories which the specified URL matches based on the stored blocklist data.
   *
   * @param url The URL to categorise.
   * @returns Returns an object containing a set of all the category IDs which the specified URL
   *  belongs to. The set is empty if the URL did not match any categories.
   */
  public readonly categoriseUrl = (
    url: URL,
    customCategories: ICustomCategory[] = [],
  ): UrlListCategoriserResult => {
    // Convert the URL to lower-case to match the blocklist.
    url = new URL(url.toString().toLowerCase());

    // Do we have a TLD-specific map for this URL?
    const topLevelDomain = UrlListCategoriser.extractTopLevelDomain(url);
    let hasTldSpecificMap = true;
    let categorisationMap = this._tldCategorisationMaps.get(topLevelDomain);
    if (topLevelDomain === '' || categorisationMap === undefined) {
      hasTldSpecificMap = false;
      categorisationMap = this._tldCategorisationMaps.get('');
    }

    const result = new UrlListCategoriserResult();

    if (categorisationMap !== undefined) {
      // Split the URL up into permutations of domain and path levels, and search for all of them.
      // This allows us to do "greedy" matching, whereby a rule for a higher-level domain or folder
      //  will apply to any URL hierarchically below it.
      const variations = GuardianUtilities.generateUrlVariationsForLookup(
        url,
        true,
        hasTldSpecificMap,
      );
      variations.forEach((variation: string): void => {
        categorisationMap?.lookupCategories(variation).forEach((category: string): void => {
          result.categoryIds.add(category);
        });
      });
    }

    this._categoriseCustomCategories(customCategories, url).forEach((category: string): void => {
      result.categoryIds.add(category);
    });

    return result;
  };

  /**
   * Uses the custom categories in the policy json to apply categories to all of the url variations.
   * @returns An array of all the custom category ids that apply to the url.
   */
  private readonly _categoriseCustomCategories = (
    customCategories: ICustomCategory[],
    url: URL,
  ): string[] => {
    if (customCategories.length === 0) {
      return [];
    }

    const result: string[] = [];

    const urlVariations = GuardianUtilities.generateUrlVariationsForLookup(url, false, false);

    for (const category of customCategories) {
      if (category.component?.domainsurls === undefined || category.category_id === undefined) {
        continue;
      }

      if (
        category.component.domainsurls.some((d) => {
          const sanitisedUrl = this.sanitiseUrl(d);

          return urlVariations.includes(sanitisedUrl.toLowerCase());
        })
      ) {
        result.push(category.category_id);
      }
    }

    return result;
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Remove the scheme prefix, www subdomain, and any trailing slashes, from a URL string.
   */
  public readonly sanitiseUrl = (url: string): string => {
    return url.replace(/^(\w*:\/\/)?(www\.)?/, '').replace(/\/+$/, '');
  };

  /**
   * Get the top-level domain from a URL.
   *
   * @param url The URL from which to extract the top-level domain.
   * @returns Returns the top-level domain from the URL. Returns an empty string if the top-level
   *  domain could not be extracted.
   */
  public static readonly extractTopLevelDomain = (url: URL): string => {
    return url.hostname.split('.').slice(-1)[0] ?? '';
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * A map of top-level domains to the corresponding URL lists.
   * Each key is a top-level domain, written forwards, without any dots, e.g. "com" or "uk".
   * Each value is a mapping of URLs under that top-level domain to their corresponding categories.
   * The top-level domain must be omitted from each entry in the URL list.
   * If the key is an empty string (i.e. no TLD) then it is a special-case: The URLs in the
   *  corresponding list can have any TLD not found in any other list. In that case, the TLD must be
   *  included in each entry in the list. These names typically come from a blocklist file called
   *  "domainsurls.other".
   *
   * @note The top-level domains must be stored in lower-case.
   */
  private _tldCategorisationMaps = new Map<string, CategorisationMap>();
}

// -------------------------------------------------------------------------------------------------

/**
 * Contains the result of a URL categorisation by URL list.
 */
export class UrlListCategoriserResult implements CategoriserResult {
  /**
   * Lists the categories IDs associated with a URL.
   * This will be an empty set if the URL did not match any known categories.
   */
  public categoryIds = new Set<string>();
}

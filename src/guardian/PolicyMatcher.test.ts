import TenantId from 'models/TenantId';
import { getTestPolicyConfig } from 'test-helpers/test-policy-config';
import { IGroupMapping, IGroup } from 'models/PolicyConfigModels';
import PolicyMatcher from './PolicyMatcher';
import IpServiceMock from 'services/IpService.mock';

import 'test-helpers/chrome-api';

const tenant = new TenantId('f417a2c4-f99c-11ea-8caa-eb014c4bbe3b');

let policyJson = getTestPolicyConfig(tenant);

const groupNames = ['LocalGroup1', '/Filtering (OU)'];

const matchedMappings = policyJson.group_mapping.filter((mapping: IGroupMapping) => {
  return groupNames.some((name) => {
    return mapping.directory_group.some((group) => name === group);
  });
});

const mappedGroups = policyJson.groups.filter((group: IGroup) => {
  return matchedMappings.some((mapping: IGroupMapping) => mapping.local_group === group.id);
});

describe('PolicyMatcher', () => {
  let ipService: IpServiceMock;
  let matcher: PolicyMatcher;

  beforeEach(() => {
    ipService = new IpServiceMock();
    matcher = new PolicyMatcher(ipService);
    matcher.currentUsername = '<EMAIL>';

    policyJson = getTestPolicyConfig(tenant);
    policyJson.mappedGroups = mappedGroups;
  });

  describe('matchPolicies', () => {
    it('should match an allow policy', () => {
      const policy = {
        what: ['Everything'],
        when: ['Anytime'],
        who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['Everywhere'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '1',
        enabled: 'on',
      };

      const urlCategories = new Set<string>();

      policyJson.flattenedPolicies = [policy];

      const result = matcher.matchPolicies(policyJson, urlCategories);

      expect(result.allow).toBeTrue();
      expect(result.timeslot).toEqualCaseInsensitive('Anytime');
      expect(result.location).toEqualCaseInsensitive('Everywhere');
      expect(result.group).toBeUndefined();
      expect(result.policy).toBeUndefined();
    });

    it('should match a block policy', () => {
      const policy = {
        what: ['Everything'],
        when: ['Anytime'],
        who: ['Everyone', '96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['Everywhere'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      const urlCategories = new Set<string>();

      policyJson.flattenedPolicies = [policy];

      const result = matcher.matchPolicies(policyJson, urlCategories);

      expect(result.allow).toBeFalse();
      expect(result.timeslot).toEqualCaseInsensitive('Anytime');
      expect(result.location).toEqualCaseInsensitive('Everywhere');
      expect(result.group).toEqualCaseInsensitive('Everyone');
      expect(result.policy).toEqualCaseInsensitive('86596A3C-F99D-11EA-A202-DBDB4B4BBE3B');
    });
  });

  describe('whoMatches', () => {
    it('should match an "Everyone" policy', () => {
      const policy = {
        what: ['8502DCB8-F99D-11EA-A202-DBDB4B4BBE3B'],
        when: ['Anytime'],
        who: ['Everyone', '96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['Everywhere'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      const result = matcher.whoMatches(policy.who, policyJson);

      expect(result).toEqualCaseInsensitive('Everyone');
    });

    it('should match a group by id', () => {
      const policy = {
        what: ['8502DCB8-F99D-11EA-A202-DBDB4B4BBE3B'],
        when: ['Anytime'],
        who: ['A4416B46-9972-11EC-9FC5-91B9081D14AE'],
        where: ['Everywhere'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      const result = matcher.whoMatches(policy.who, policyJson);

      expect(result).toEqualCaseInsensitive('Test Group');
    });

    it('should match a single user', () => {
      const policy = {
        what: ['Everything'],
        when: ['Anytime'],
        who: ['B82007AA-2682-4650-BF78-9AB270FB48DD'],
        where: ['Everywhere'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Specific User - Everything',
        action: '0',
        enabled: 'on',
      };

      const result = matcher.whoMatches(policy.who, policyJson);

      expect(result).toEqualCaseInsensitive('<EMAIL>');
    });
  });

  describe('whatMatches', () => {
    it('should match an "Everything" policy', () => {
      const policy = {
        what: ['Everything'],
        when: ['Anytime'],
        who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['Everywhere'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      const expected = new Set<string>(['Everything']);

      const categories = new Set<string>();

      const result = matcher.whatMatches(policy.what, policyJson, categories);

      expect(result).toEqual(expected);
    });

    it('should match a valid category filter groups policy', () => {
      const policy = {
        what: ['5AFCC930-A80F-11EB-A051-0182081D14AE'],
        when: ['Anytime'],
        who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['Everywhere'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      const categoryFilterGroups = [
        {
          source: ['1', '5'],
          comment: '',
          id: '5AFCC930-A80F-11EB-A051-0182081D14AE',
          name: 'Test match',
        },
      ];

      policyJson.category_filter_groups = categoryFilterGroups;

      const expected = new Set<string>(['1']);

      const categories = new Set<string>(['1']);

      const result = matcher.whatMatches(policy.what, policyJson, categories);

      expect(result).toEqual(expected);
    });

    it('should match a valid category filter groups policy with multiple categories', () => {
      const policy = {
        what: ['5AFCC930-A80F-11EB-A051-0182081D14AE'],
        when: ['Anytime'],
        who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['Everywhere'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      const categoryFilterGroups = [
        {
          source: ['1', '5'],
          comment: '',
          id: '5AFCC930-A80F-11EB-A051-0182081D14AE',
          name: 'Test match',
        },
      ];

      policyJson.category_filter_groups = categoryFilterGroups;

      const expected = new Set<string>(['1', '5']);

      const categories = new Set<string>(['1', '5']);

      const result = matcher.whatMatches(policy.what, policyJson, categories);

      expect(result).toEqual(expected);
    });

    it('should match a valid custom categories policy', () => {
      const policy = {
        what: ['af9a7f8a-e320-45ac-9533-22ff06ef3903'],
        when: ['Anytime'],
        who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['Everywhere'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      const customCategories = [
        {
          id: 'af9a7f8a-e320-45ac-9533-22ff06ef3903',
          category_id: '1',
          from_blocklist: '1',
          tenant: 'global',
        },
      ];

      policyJson.custom_categories = customCategories;

      const expected = new Set<string>(['1']);

      const categories = new Set<string>(['1']);

      const result = matcher.whatMatches(policy.what, policyJson, categories);

      expect(result).toEqual(expected);
    });

    it('should not match an invalid category filter groups policy', () => {
      const policy = {
        what: ['5AFCC930-A80F-11EB-A051-0182081D14AE'],
        when: ['Anytime'],
        who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['Everywhere'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      const categoryFilterGroups = [
        {
          source: ['1', '5'],
          comment: '',
          id: '5AFCC930-A80F-11EB-A051-0182081D14AE',
          name: 'Test match',
        },
      ];

      policyJson.category_filter_groups = categoryFilterGroups;

      const categories = new Set<string>(['10, 11, 12']);

      const result = matcher.whatMatches(policy.what, policyJson, categories);

      expect(result).toBeUndefined();
    });

    it('should match an invalid custom categories policy', () => {
      const policy = {
        what: ['f09d6621-8b4d-4b6b-83fe-2b883ea2823c'],
        when: ['Anytime'],
        who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['Everywhere'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      const customCategories = [
        {
          id: 'f09d6621-8b4d-4b6b-83fe-2b883ea2823c',
          category_id: '5',
          from_blocklist: '1',
          tenant: 'global',
        },
      ];

      policyJson.custom_categories = customCategories;

      const categories = new Set<string>(['1']);

      const result = matcher.whatMatches(policy.what, policyJson, categories);

      expect(result).toBeUndefined();
    });
  });

  describe('whenMatches', () => {
    it('should match an "Anytime" timestamp', () => {
      const policy = {
        what: ['8502DCB8-F99D-11EA-A202-DBDB4B4BBE3B'],
        when: ['Anytime'],
        who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['Everywhere'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      const result = matcher.whenMatches(policy.when, policyJson, 10000);

      expect(result).toEqualCaseInsensitive('Anytime');
    });

    it('should match a valid timestamp', () => {
      const policy = {
        what: ['8502DCB8-F99D-11EA-A202-DBDB4B4BBE3B'],
        when: ['5A9BAE5C-A80F-11EB-A051-0182081D14AE'],
        who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['Everywhere'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      // It only needs to match one of the timeslots to be valid.
      const timeSlots = [
        {
          id: '5A9BAE5C-A80F-11EB-A051-0182081D14AE',
          name: 'Lunchtime',
          comment: 'Weekday lunch period',
          tenant: 'global',
          times: [
            ['129600', '133199'],
            ['216000', '219599'],
            ['302400', '305999'],
            ['388800', '392399'],
            ['475200', '478799'],
          ],
        },
        {
          id: 'BE6D6344-96B3-4CA5-B45E-87344DC13444',
          name: 'Late Lunchtime',
          comment: 'Late Weekday lunch period',
          tenant: 'global',
          times: [['475200', '478799']],
        },
      ];

      policyJson.timeSlots = timeSlots;

      const result = matcher.whenMatches(policy.when, policyJson, 216500);

      expect(result).toEqualCaseInsensitive('Lunchtime');
    });

    it('should not match a timestamp in the past', () => {
      const policy = {
        what: ['8502DCB8-F99D-11EA-A202-DBDB4B4BBE3B'],
        when: ['5A9BAE5C-A80F-11EB-A051-0182081D14AE'],
        who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['Everywhere'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      const timeSlots = [
        {
          id: '5A9BAE5C-A80F-11EB-A051-0182081D14AE',
          name: 'Lunchtime',
          comment: 'Weekday lunch period',
          tenant: 'global',
          times: [
            ['129600', '133199'],
            ['216000', '219599'],
            ['302400', '305999'],
            ['388800', '392399'],
            ['475200', '478799'],
          ],
        },
      ];

      policyJson.timeSlots = timeSlots;

      const result = matcher.whenMatches(policy.when, policyJson, 500);

      expect(result).toBeUndefined();
    });

    it('should not match a timestamp in the future', () => {
      const policy = {
        what: ['8502DCB8-F99D-11EA-A202-DBDB4B4BBE3B'],
        when: ['5A9BAE5C-A80F-11EB-A051-0182081D14AE'],
        who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['Everywhere'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      const timeSlots = [
        {
          id: '5A9BAE5C-A80F-11EB-A051-0182081D14AE',
          name: 'Lunchtime',
          comment: 'Weekday lunch period',
          tenant: 'global',
          times: [
            ['129600', '133199'],
            ['216000', '219599'],
            ['302400', '305999'],
            ['388800', '392399'],
            ['475200', '478799'],
          ],
        },
      ];

      policyJson.timeSlots = timeSlots;

      const result = matcher.whenMatches(policy.when, policyJson, 99999999999);

      expect(result).toBeUndefined();
    });
  });
  describe('whereMatches', () => {
    it('should match with a valid ip address', () => {
      const policy = {
        what: ['8502DCB8-F99D-11EA-A202-DBDB4B4BBE3B'],
        when: ['Anytime'],
        who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['5A9BAE5C-A80F-11EB-A051-0182081D14AE'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      const locations = [
        {
          id: '5A9BAE5C-A80F-11EB-A051-0182081D14AE',
          name: 'Test location',
          sources: ['***************', '***************'],
        },
      ];

      policyJson.locations = locations;
      ipService.setPrivateIpAddresses(['***************']);
      const result = matcher.whereMatches(policy.where, policyJson);

      expect(result).toEqualCaseInsensitive('Test location');
    });
    it('should not match with an invalid ip address', () => {
      const policy = {
        what: ['8502DCB8-F99D-11EA-A202-DBDB4B4BBE3B'],
        when: ['Anytime'],
        who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['5A9BAE5C-A80F-11EB-A051-0182081D14AE'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      const locations = [
        {
          id: '5A9BAE5C-A80F-11EB-A051-0182081D14AE',
          name: 'Test location',
          sources: ['***************', '***************'],
        },
      ];

      policyJson.locations = locations;
      ipService.setPrivateIpAddresses(['111.111.111.555']);
      const result = matcher.whereMatches(policy.where, policyJson);

      expect(result).toBeUndefined();
    });
    it('should not match with an exception ip address', () => {
      const policy = {
        what: ['8502DCB8-F99D-11EA-A202-DBDB4B4BBE3B'],
        when: ['Anytime'],
        who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['5A9BAE5C-A80F-11EB-A051-0182081D14AE'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      const locations = [
        {
          id: '5A9BAE5C-A80F-11EB-A051-0182081D14AE',
          name: 'Test location',
          sources: ['***************', '***************'],
          exceptions: ['111.111.111.333'],
        },
      ];

      policyJson.locations = locations;
      ipService.setPrivateIpAddresses(['111.111.111.333']);
      const result = matcher.whereMatches(policy.where, policyJson);

      expect(result).toBeUndefined();
    });
    it('should match with an "Everywhere" policy', () => {
      const policy = {
        what: ['8502DCB8-F99D-11EA-A202-DBDB4B4BBE3B'],
        when: ['Anytime'],
        who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['Everywhere'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      const result = matcher.whereMatches(policy.where, policyJson);

      expect(result).toEqualCaseInsensitive('Everywhere');
    });
    it('should match a valid outside premises ip address', () => {
      const policy = {
        what: ['8502DCB8-F99D-11EA-A202-DBDB4B4BBE3B'],
        when: ['Anytime'],
        who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['BBDA4CF3-26C8-468D-805A-A4D2B5B1FD36'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      const locations = [
        {
          id: '5A9BAE5C-A80F-11EB-A051-0182081D14AE',
          name: 'Test location',
          sources: ['***************', '***************'],
        },
        {
          id: 'BBDA4CF3-26C8-468D-805A-A4D2B5B1FD36',
          name: 'outsidepremises',
          exceptions: ['***************', '***************'],
        },
      ];

      policyJson.locations = locations;
      ipService.setPublicIpAddresses(['111.111.111.333']);
      const result = matcher.whereMatches(policy.where, policyJson);

      expect(result).toEqualCaseInsensitive('outsidepremises');
    });
    it('should not match a exception outside premises ip address', () => {
      const policy = {
        what: ['8502DCB8-F99D-11EA-A202-DBDB4B4BBE3B'],
        when: ['Anytime'],
        who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
        where: ['BBDA4CF3-26C8-468D-805A-A4D2B5B1FD36'],
        order: '6',
        id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
        name: 'Default Users - Amazon Prime',
        action: '0',
        enabled: 'on',
      };

      const locations = [
        {
          id: '5A9BAE5C-A80F-11EB-A051-0182081D14AE',
          name: 'Test location',
          sources: ['***************', '***************'],
        },
        {
          id: 'BBDA4CF3-26C8-468D-805A-A4D2B5B1FD36',
          name: 'outsidepremises',
          exceptions: ['***************', '***************'],
        },
      ];

      policyJson.locations = locations;
      ipService.setPublicIpAddresses(['***************']);
      const result = matcher.whereMatches(policy.where, policyJson);

      expect(result).toBeUndefined();
    });
  });
  describe('matchContentModPolicies', () => {
    describe('Cloud content mods', () => {
      it('should match an include content mod policy', () => {
        const policy = [
          {
            id: '4683d843-80a7-48ac-1e6a-08da8cdb3d32',
            enabled: 'on',
            tenantid: 'global',
            filtertype: 'include',
            groups: ['A4416B46-9972-11EC-9FC5-91B9081D14AE'],
            locations: ['5A9BAE5C-A80F-11EB-A051-0182081D14AE'],
            blockmanId: '123',
          },
        ];

        const locations = [
          {
            id: '5A9BAE5C-A80F-11EB-A051-0182081D14AE',
            name: 'Test location',
            sources: ['***************', '***************'],
          },
        ];

        policyJson.cloud_content_modifications = policy;
        policyJson.locations = locations;
        ipService.setPrivateIpAddresses(['***************']);
        const result = matcher.matchContentModPolicies(policyJson, new Set<string>());

        expect(result).toEqual(['123']);
      });
      it('should not match an exclude content mod policy', () => {
        const policy = [
          {
            id: '4683d843-80a7-48ac-1e6a-08da8cdb3d32',
            enabled: 'on',
            tenantId: 'global',
            filterType: 'exclude',
            groups: ['A4416B46-9972-11EC-9FC5-91B9081D14AE'],
            locations: ['5A9BAE5C-A80F-11EB-A051-0182081D14AE'],
            blockmanId: '123',
          },
        ];

        const locations = [
          {
            id: '5A9BAE5C-A80F-11EB-A051-0182081D14AE',
            name: 'Test location',
            sources: ['***************', '***************'],
          },
        ];

        policyJson.cloud_content_modifications = policy;
        policyJson.locations = locations;
        ipService.setPrivateIpAddresses(['***************']);
        const result = matcher.matchContentModPolicies(policyJson, new Set<string>());

        expect(result).toEqual([]);
      });
      it('should not match an invalid content mod policy', () => {
        const policy = [
          {
            id: '4683d843-80a7-48ac-1e6a-08da8cdb3d32',
            enabled: 'on',
            tenantId: 'global',
            filterType: 'exclude',
            groups: ['A4416B46-9972-11EC-9FC5-91B9081D14AE'],
            locations: ['5A9BAE5C-A80F-11EB-A051-0182081D14AE'],
            blockmanId: '123',
          },
        ];

        policyJson.cloud_content_modifications = policy;
        ipService.setPrivateIpAddresses(['***************']);
        const result = matcher.matchContentModPolicies(policyJson, new Set<string>());

        expect(result).toEqual([]);
      });
    });
    describe('On prem content mods', () => {
      it('should match a content mod policy', () => {
        const policy = [
          {
            id: '4683d843-80a7-48ac-1e6a-08da8cdb3d32',
            enabled: 'on',
            where: ['Everywhere'],
            action: '1',
            who: ['Everyone'],
            what: ['Everything'],
            order: '1',
            ruleset: ['5B43263C-A80F-11EB-A051-0182081D14AE'],
          },
        ];

        policyJson.flattenedContentMods = policy;
        const result = matcher.matchContentModPolicies(policyJson, new Set(['32']));

        expect(result).toEqual(['54']);
      });
      it('should match a content mod policy with multiple rulesets', () => {
        const policy = [
          {
            id: '4683d843-80a7-48ac-1e6a-08da8cdb3d32',
            enabled: 'on',
            where: ['Everywhere'],
            action: '1',
            who: ['Everyone'],
            what: ['Everything'],
            order: '1',
            ruleset: [
              '5B43263C-A80F-11EB-A051-0182081D14AE',
              '5B336E4A-A80F-11EB-A051-0182081D14AE',
            ],
          },
        ];

        policyJson.flattenedContentMods = policy;
        const result = matcher.matchContentModPolicies(policyJson, new Set(['32']));

        expect(result).toEqual(['54', '104']);
      });
      it('should match a what content mod policy', () => {
        const policy = [
          {
            id: '4683d843-80a7-48ac-1e6a-08da8cdb3d32',
            enabled: 'on',
            where: ['Everywhere'],
            action: '1',
            who: ['Everyone'],
            what: ['5AFCC930-A80F-11EB-A051-0182081D14AE'],
            order: '1',
            ruleset: ['5B43263C-A80F-11EB-A051-0182081D14AE'],
          },
        ];

        const categoryFilterGroups = [
          {
            source: ['1', '5'],
            comment: '',
            id: '5AFCC930-A80F-11EB-A051-0182081D14AE',
            name: 'Test match',
          },
        ];

        policyJson.category_filter_groups = categoryFilterGroups;
        policyJson.flattenedContentMods = policy;
        const result = matcher.matchContentModPolicies(policyJson, new Set(['1']));

        expect(result).toEqual(['54']);
      });
      it('should match a who content mod policy', () => {
        const policy = [
          {
            id: '4683d843-80a7-48ac-1e6a-08da8cdb3d32',
            enabled: 'on',
            where: ['Everywhere'],
            action: '1',
            who: ['A4416B46-9972-11EC-9FC5-91B9081D14AE'],
            what: ['Everything'],
            order: '1',
            ruleset: ['5B43263C-A80F-11EB-A051-0182081D14AE'],
          },
        ];

        policyJson.flattenedContentMods = policy;
        const result = matcher.matchContentModPolicies(policyJson, new Set(['1']));

        expect(result).toEqual(['54']);
      });
      it('should match a where content mod policy', () => {
        const policy = [
          {
            id: '4683d843-80a7-48ac-1e6a-08da8cdb3d32',
            enabled: 'on',
            where: ['5A9BAE5C-A80F-11EB-A051-0182081D14AE'],
            action: '1',
            who: ['Everyone'],
            what: ['Everything'],
            order: '1',
            ruleset: ['5B43263C-A80F-11EB-A051-0182081D14AE'],
          },
        ];

        const locations = [
          {
            id: '5A9BAE5C-A80F-11EB-A051-0182081D14AE',
            name: 'Test location',
            sources: ['***************', '***************'],
          },
        ];

        policyJson.locations = locations;
        ipService.setPrivateIpAddresses(['***************']);

        policyJson.flattenedContentMods = policy;
        const result = matcher.matchContentModPolicies(policyJson, new Set(['1']));

        expect(result).toEqual(['54']);
      });
    });
    it('should match only match one cloud or on prem mod', () => {
      const cloudPolicy = [
        {
          id: '4683d843-80a7-48ac-1e6a-08da8cdb3d32',
          enabled: 'on',
          tenantid: 'global',
          filtertype: 'include',
          groups: ['Everyone'],
          locations: ['Everywhere'],
          blockmanId: '54',
        },
      ];
      const onPremPolicy = [
        {
          id: '4683d843-80a7-48ac-1e6a-08da8cdb3d32',
          enabled: 'on',
          where: ['Everywhere'],
          action: '1',
          who: ['Everyone'],
          what: ['Everything'],
          order: '1',
          ruleset: ['5B43263C-A80F-11EB-A051-0182081D14AE'],
        },
      ];

      policyJson.cloud_content_modifications = cloudPolicy;
      policyJson.flattenedContentMods = onPremPolicy;

      const result = matcher.matchContentModPolicies(policyJson, new Set(['1']));

      expect(result).toEqual(['54']);
    });
  });
  describe('matchUrlModPolicies', () => {
    it('should match a valid url mod policy', () => {
      const expected = new Set(['5']);
      const result = matcher.matchUrlModPolicies(policyJson, new Set(['1']));

      expect(result).toEqual(expected);
    });
    it('should not match an invalid url mod policy', () => {
      const expected = new Set<string>();
      const result = matcher.matchUrlModPolicies(policyJson, new Set(['9999999']));

      expect(result).toEqual(expected);
    });
  });
});

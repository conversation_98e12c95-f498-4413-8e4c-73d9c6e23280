import Guardian from './Guardian';
import IpServiceMock from '../services/IpService.mock';

import 'test-helpers/chrome-api';
import FilterMode from 'constants/FilterMode';

describe('Guardian', () => {
  let ipService: IpServiceMock;
  let fullFilter: Guardian;

  beforeEach(() => {
    ipService = new IpServiceMock();
    fullFilter = new Guardian(FilterMode.full, ipService);
  });

  describe('Full filter', () => {
    it('should return the correct deep url', () => {
      let testUrl = new URL(
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS318hSHkn06MD9dYQedaKxJqWaBFiCjpxWilbSIC1ifjfUxMhB_g:https://cdn.cnn.com/cnnnext/dam/assets/190811182740-rashford-united-chelsea-large-tease.jpg',
      );
      let result = fullFilter.useDeepUrlAnalysis(testUrl);
      expect(result?.toString()).toEqual(
        'https://cdn.cnn.com/cnnnext/dam/assets/190811182740-rashford-united-chelsea-large-tease.jpg',
      );

      testUrl = new URL(
        'https://webcache.googleusercontent.com/search?q=cache:TKJ3gFa-GGUJ:https://www.smirnoff.com/en-gb/+&cd=1&hl=en&ct=clnk&gl=uk',
      );
      result = fullFilter.useDeepUrlAnalysis(testUrl);
      expect(result?.toString()).toEqual(
        'https://www.smirnoff.com/en-gb/+&cd=1&hl=en&ct=clnk&gl=uk',
      );
    });

    it('should return undefined if no deep url is present', () => {
      let testUrl = new URL(
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS318hSHkn06MD9dYQedaKxJqWaBFiCjpxWilbSIC1ifjfUxMhB_g',
      );
      let result = fullFilter.useDeepUrlAnalysis(testUrl);
      expect(result).toBeUndefined();

      testUrl = new URL('https://webcache.googleusercontent.com/search?q=cache:TKJ3gFa-GGUJ');
      result = fullFilter.useDeepUrlAnalysis(testUrl);
      expect(result).toBeUndefined();

      testUrl = new URL('https://www.example.com/search?q=cache');
      result = fullFilter.useDeepUrlAnalysis(testUrl);
      expect(result).toBeUndefined();

      testUrl = new URL('https://smoothwall.com/search?q=cache::TKJ3gFa-GGUJ');
      result = fullFilter.useDeepUrlAnalysis(testUrl);
      expect(result).toBeUndefined();

      testUrl = new URL('a:b:s:c:d:r:t:s:https://ftp://');
      result = fullFilter.useDeepUrlAnalysis(testUrl);
      expect(result).toBeUndefined();
    });

    it('should return undefined if the regex cannot match the url with and without deep url present', () => {
      let testUrl = new URL(
        'https://nodeepurl.com/images?q=tbn:ANd9GcS318hSHkn06MD9dYQedaKxJqWaBFiCjpxWilbSIC1ifjfUxMhB_g',
      );
      let result = fullFilter.useDeepUrlAnalysis(testUrl);
      expect(result).toBeUndefined();

      testUrl = new URL(
        'https://nodeepurl.com/images?q=tbn:ANd9GcS318hSHkn06MD9dYQedaKxJqWaBFiCjpxWilbSIC1ifjfUxMhB_g:https://www.smirnoff.com/en-gb/+&cd=1&hl=en&ct=clnk&gl=uk',
      );
      result = fullFilter.useDeepUrlAnalysis(testUrl);
      expect(result).toBeUndefined();
    });
  });
});

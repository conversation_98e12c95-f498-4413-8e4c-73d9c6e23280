/**
 * Defines the structure of a blocklist manifest file.
 * The manifest file lists all the other files which are part of a full or diff blocklist.
 */
export default interface BlocklistManifest {
  /**
   * The names of the files which are part of the full or diff blocklist.
   * These will typically be in the same folder as the manifest file.
   */
  files: string[];

  /**
   * For diffs, this specifies the blocklist version which the diff should be applied to.
   * The version is the Unix timestamp of when it was generated, usually written as a string.
   * For full blocklists, this is irrelevant and typically ends up being the number 0.
   */
  from_version: string | number;

  /**
   * For diffs, this specifies the blocklist version which results from applying the diff.
   * The version is the Unix timestamp of when it was generated, usually written as a string.
   * For full blocklists, this is just the version of the full blocklist.
   */
  to_version: string | number;
}

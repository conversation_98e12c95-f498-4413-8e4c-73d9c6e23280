import FilterDecision from './FilterDecision';

describe('FilterDecision', () => {
  describe('allowWithoutLogging()', () => {
    it('returns an allow decision', () => {
      expect(FilterDecision.allowWithoutLogging().allow).toBeTrue();
    });

    it('returns a decision with logging disabled', () => {
      expect(FilterDecision.allowWithoutLogging().log).toBeFalse();
    });
  });

  describe('blockWithoutLogging()', () => {
    it('returns a block decision', () => {
      expect(FilterDecision.blockWithoutLogging().allow).toBeFalse();
    });

    it('returns a decision with logging disabled', () => {
      expect(FilterDecision.blockWithoutLogging().log).toBeFalse();
    });
  });
});

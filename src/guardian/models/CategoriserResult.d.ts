/**
 * Contains the result of a categorisation operation.
 * For example, this may contain the categories matched to a URL, search term, or video ID.
 * Implementors may provide additional properties describing the categorisation, such as a mapping
 *  of search terms to category IDs.
 */
export default interface CategoriserResult {
  /**
   * The IDs of the categories found by the categorisation operation.
   */
  categoryIds: Set<string>;
}

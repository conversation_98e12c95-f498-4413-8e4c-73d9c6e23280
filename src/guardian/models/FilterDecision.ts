/**
 * Stores the information about a decision to allow or block a URL and optionally page content.
 * When a request is initiated, a decision is based purely on the URL. If the URL is allowed and the
 *  page loads, then another decision will be based on URL and content analysis combined. This class
 *  is used for the decision data in both cases.
 */
export default class FilterDecision {
  // -----------------------------------------------------------------------------------------------
  // Factory functions.

  /**
   * Construct a decision which allows the request and disables logging.
   * This is typically used as a "fail open" strategy when filtering isn't ready yet.
   */
  public static readonly allowWithoutLogging = (): FilterDecision => {
    const decision = new FilterDecision();
    decision.allow = true;
    decision.log = false;
    return decision;
  };

  /**
   * Construct a decision which blocks the request and disables logging.
   * This is used for URLs which are on the IWF child abuse list. We don't want to log those.
   * @param isIwfPage Indicates if the page was blocked because it is on the iwf list.
   */
  public static readonly blockWithoutLogging = (isIwfPage: boolean = false): FilterDecision => {
    const decision = new FilterDecision();
    decision.allow = false;
    decision.log = false;
    decision.isIwfPage = isIwfPage;
    return decision;
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Indicates if the request should be allowed (true) or blocked (false).
   * A request which is allowed may require the URL to be altered. In this case, see modifiedUrl.
   *
   * @see modifiedUrl
   */
  public allow: boolean = true;

  /**
   * If this is set then the URL of the request needs to be modified.
   * This should only be set if allow is true. There's no point modifying the URL of a blocked
   *  request.
   *
   * @note URL modifications can only be applied during the initial request. By the time we get to
   *  content analysis, it's usually too late to modify the URL, so this will (probably) be ignored
   *  at that stage.
   * @see allow
   */
  public modifiedUrl?: URL = undefined;

  /**
   * Indicates if the request should be recorded in the access log.
   * This is used to prevent us from logging URLs which are irrelevant (e.g. built-in browser pages),
   *  or which potentially contain child exploitation.
   */
  public log: boolean = true;

  /**
   * The IDs of all the categories which were matched while making the decision.
   */
  public categoryIds = new Set<string>();

  /**
   * The IDs of all the categories which were matched based on video IDs.
   */
  public videoIds: string = '';

  /**
   * The terms that matched in the search term categoriser.
   */
  public searchTerms: string = '';

  /**
   * The name of the location policy that was matched if applicable.
   */
  public location?: string;

  /**
   * The name of the timeslot policy that was matched if applicable.
   */
  public timeslot?: string;

  /**
   * The name of the group policy that was matched if applicable.
   */
  public group?: string;

  /**
   * The ruleId is equivalent to the order of the policy.
   */
  public ruleId?: string;

  /**
   * The IDs of the cloud content mods which should be applied to the page content.
   * This will only be populated for content decisions. It will be an empty array if only the URL
   *  was being analysied.
   */
  public contentModIds: string[] = [];

  /**
   * UUID of the policy rule which matches the request, if any.
   */
  public policy?: string;

  /**
   * Indicates if the page was blocked because it is on the iwf list.
   */
  public isIwfPage: boolean = false;
}

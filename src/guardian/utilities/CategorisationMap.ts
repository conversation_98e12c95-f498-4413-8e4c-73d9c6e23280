import DiffMap from 'guardian/patching/DiffMap';
import UnifiedDiffTarget from '../patching/UnifiedDiffTarget';
import { extractStringValueFromJsonArrayBlocklistLine } from './GuardianUtilities';

/**
 * Maps arbitrary strings to a list of category IDs.
 * This stores the data as a flat array of strings, and performs look-up using binary search.
 * The string (referred to as the "entity" in this class) can be anything, but it's typically a URL
 *  or video ID. The category IDs are expressed as strings.
 * For example, "google.com" could be in categories 5, 280, and "abc".
 *
 * @note This is used for very large look-ups, where a conventional JavaScript Map() would occupy
 *  too much space.
 */
export default class CategorisationMap implements UnifiedDiffTarget {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance, optionally initialising the map data.
   *
   * @param map An array containing the category map data. Each element should be a string
   *  containing an entity, followed by the entity delimiter, then one or more category identifiers
   *  separated by the category delimiter. The array must be sorted alphabetically by entity.
   * @param entityDelimiter The text which will separate the entity from the category IDs in each
   *  map entry. It must not be empty, and must not be the same as the category delimiter.
   * @param categoryDelimiter The text which will separate consecutive category IDs in each map
   *  entry. It must not be empty and must not be the same as the entity delimiter.
   * @param useLocaleComparison If set to true, when searching for the entity during mapping the function will
   *  compare the position of strings using `localeCompare` as opposed to checking the character codes.
   *  This is required for files that are sorted ignoring casing such as the video ids file.
   * @throws {TypeError} One or more of the specified delimiters is an empty string, or the
   *  delimiters are the same.
   */
  public constructor(
    map: string[] = [],
    entityDelimiter: string = '|',
    categoryDelimiter: string = ',',
    useLocaleComparison: boolean = false,
  ) {
    this._useLocaleComparison = useLocaleComparison;
    this.setMap(map, entityDelimiter, categoryDelimiter);
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors and operations.

  /**
   * Store the specified category map data.
   *
   * @param map An array containing the category map data. Each element should be a string
   *  containing an entity, followed by the entity delimiter, then one or more category identifiers
   *  separated by the category delimiter. The array must be sorted alphabetically by entity.
   * @param entityDelimiter The text which will separate the entity from the category IDs in each
   *  map entry. It must not be an empty string, and must not be the same as the category delimiter.
   *  If there are multiple occurrences of the entity delimiter in a given entry, then only the last
   *  occurrence will be considered.
   * @param categoryDelimiter The text which will separate consecutive category IDs in each map
   *  entry. It must not be an empty string, and it must not be the same as the entity delimiter.
   * @throws {TypeError} One or more of the specified delimiters is an empty string, or the
   *  delimiters are the same.
   */
  public readonly setMap = (
    map: string[],
    entityDelimiter: string = '|',
    categoryDelimiter: string = ',',
  ): void => {
    // Validate the delimiters before storing anything so we don't end up in an invalid state.
    if (entityDelimiter === '') {
      throw new TypeError('The entity delimiter cannot be an empty string.');
    }

    if (categoryDelimiter === '') {
      throw new TypeError('The category delimiter cannot be an empty string.');
    }

    if (entityDelimiter === categoryDelimiter) {
      throw new TypeError('The entity delimiter must be different from the category delimiter.');
    }

    this._entityDelimiter = entityDelimiter;
    this._categoryDelimiter = categoryDelimiter;

    this._map = map;
  };

  /**
   * Get the number of entities in the category map data.
   */
  public get length(): number {
    return this._map.length;
  }

  /**
   * Get the entity delimiter used by this object.
   * This will be the default (pipe character |) if it was not explicitly set in the constructor or
   *  a more recent call to setMap().
   */
  public get entityDelimiter(): string {
    return this._entityDelimiter;
  }

  /**
   * Get the category delimiter used by this object.
   * This will be the default (comma ,) if it was not explicitly set in the constructor or a more
   *  recent call to setMap().
   */
  public get categoryDelimiter(): string {
    return this._categoryDelimiter;
  }

  /**
   * Remove all stored entities and categories.
   * The delimiters will not be cleared back to their defaults.
   */
  public readonly clear = (): void => {
    this._map = [];
  };

  // -----------------------------------------------------------------------------------------------
  // Patching.

  /**
   * Use the given object to patch the blocklist data in memory.
   *
   * @param patcher An object which will apply the relevant blocklist diff to an in-memory array.
   */
  public readonly patchFromUnifiedDiff = (patcher: DiffMap): void => {
    // The categorisation map is always populated from a JSON array, so we need to parse changes
    //  from JSON before they can be applied. We also need to offset line numbers by -1 because the
    //  JSON file has an empty square bracket on the first line.
    patcher.patchArrayInPlace(this._map, extractStringValueFromJsonArrayBlocklistLine, -1);
  };

  // -----------------------------------------------------------------------------------------------
  // Lookup.

  /**
   * Get the category identifiers associated with the specified entity.
   *
   * @param entity The entity to search for. This is case-sensitive.
   * @returns Returns an array of category identifiers associated with the specified entity. If no
   *  such entity was found in the map then an empty array is returned.
   *
   * @note If the specified entity matches multiple entries then there is no guarantee about which
   *  one will be returned.
   */
  public readonly lookupCategories = (entity: string): Set<string> => {
    const index: number | undefined = this._findEntry(entity);
    if (index === undefined) {
      return new Set();
    }
    return this.extractCategories(this._map[index]);
  };

  /**
   * Check if the map contains the specified entity.
   *
   * @param entity The entity to search for. This may be a URL or video ID etc, depending
   *  on what data was loaded into this object.
   * @returns Returns true if the specified entity occurs in the map, regardless of how many
   *  category identifiers are associated with it.
   */
  public readonly contains = (entity: string): boolean => {
    return this._findEntry(entity) !== undefined;
  };

  /**
   * Find the map entry associated with the specified entity.
   * This does a binary search to locate the entry in a reasonable amount of time.
   *
   * @param entity The entity to search for. This may be a URL or video ID etc, depending
   *  on what data was loaded into this object. It is case-sensitive.
   * @returns Returns the index of the map entry matching the specified entity, if it exists.
   *  Returns undefined otherwise.
   *
   * @note If the specified entity matches multiple entries then there is no guarantee about which
   *  one will be returned.
   */
  private readonly _findEntry = (entity: string): number | undefined => {
    const comparisonFunction = this._useLocaleComparison
      ? this._compareEntriesLocale
      : this._compareEntries;
    // The map data is sorted alphabetically. Do a binary search on the entity value.
    let lowIndex: number = 0;
    let highIndex: number = this._map.length - 1;
    while (lowIndex <= highIndex) {
      // Compare the target entity with the mid point of the search range.
      const middleIndex: number = lowIndex + Math.floor((highIndex - lowIndex) / 2);

      const result = comparisonFunction(entity, this.extractEntity(this._map[middleIndex]));

      if (result === 0) {
        // We've found what we're looking for.
        return middleIndex;
      }

      if (result < 0) {
        // The entity we're looking for is before the mid point.
        highIndex = middleIndex - 1;
      } else {
        // The entity we're looking for is after the mid point.
        lowIndex = middleIndex + 1;
      }
    }

    // The target entity wasn't found.
    return undefined;
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Get the entity portion of one entry in the map.
   *
   * @param entry The map entry to extract the entity from.
   * @returns Returns the entity part of the specified map entry. If no entity delimiter was found
   *  then this returns the entire entry. If multiple entity delimiters were found then only the
   *  last one will be treated as the delimiter.
   */
  public readonly extractEntity = (entry: string): string => {
    const delimiterPosition: number = entry.lastIndexOf(this._entityDelimiter);
    if (delimiterPosition < 0) {
      // No entity delimiter was found so assume the entire entry is the entity.
      return entry;
    }
    return entry.substring(0, delimiterPosition);
  };

  /**
   * Get the category identifiers from one entry in the map.
   * If no entity delimiter is found in the entry then this assumes there are no categories. If
   *  multiple entity delimiters are found then this extracts categories after the last entity
   *  delimiter.
   *
   * @param entry The map entry to extract the category identifiers from.
   * @returns Returns a set of the category identifiers found in the specified entry. Returns an
   *  empty set if no category identifiers were found.
   */
  public readonly extractCategories = (entry: string): Set<string> => {
    const entityDelimiterPosition: number = entry.lastIndexOf(this._entityDelimiter);
    if (entityDelimiterPosition < 0) {
      // There was no entity delimiter so assume there are no categories in this entry.
      return new Set();
    }

    // Grab the category portion of the entry, divide it up into individual categories, and remove
    //  any empty entries.
    return new Set(
      entry
        .substring(entityDelimiterPosition + 1)
        .split(this._categoryDelimiter)
        .filter((id: string) => id !== ''),
    );
  };

  /**
   * Compares the given entities and returns the relative position between the two.
   *
   * This will perform the check using direct string comparisons to match the sorting method used for most blocklist files.
   *
   * @param targetEntity The entity to search for. This may be a URL or video ID etc, depending
   *  on what data was loaded into this object. It is case-sensitive.
   * @param currentEntity The entity to compare the target entity against.
   * @returns Returns the relative position of the current entity compared to the target.
   * This will be negative if the target is before the current entity. Positive if after or 0 if they are equal.
   *
   * @warning Some blocklist files (eg. videoids) are not sorted using the character codes.
   * In this case the search should be performed using `_compareEntriesLocale` instead.
   *
   * @see _compareEntriesLocale
   */
  private readonly _compareEntries = (targetEntity: string, currentEntity: string): number => {
    if (targetEntity.localeCompare(currentEntity) === 0) {
      // We've found what we're looking for.
      return 0;
    }

    if (targetEntity < currentEntity) {
      // The entity we're looking for is before the target.
      return -1;
    } else {
      // The entity we're looking for is after the target.
      return 1;
    }
  };

  /**
   * Compares the given entities and returns the relative position between the two.
   *
   * This function uses `localeCompare` which sorts the characters differently than a direct string comparison.
   * For example forward slash and question mark are the other way round. It also ignores casing when sorting.
   *
   * @param targetEntity The entity to search for. This may be a URL or video ID etc, depending
   *  on what data was loaded into this object. It is case-sensitive.
   * @param currentEntity The entity to compare the target entity against.
   * @returns Returns the relative position of the current entity compared to the target.
   * This will be negative if the target is before the current entity. Positive if after or 0 if they are equal.
   *
   * @warning Most of the blocklist files are sorted using the character codes.
   * In this case the search should be performed using `_compareEntries` instead.
   *
   * @see _compareEntries
   */
  private readonly _compareEntriesLocale = (
    targetEntity: string,
    currentEntity: string,
  ): number => {
    return targetEntity.localeCompare(currentEntity);
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * A flag indicating if `localeComparison` should be used when comparing the strings during a binary search.
   */
  private readonly _useLocaleComparison: boolean;

  /**
   * A sorted array which maps entities to category identifiers.
   * The entities and category identifiers can be arbitrary strings.
   * Each element in the array is a string containing the entity, followed by the token delimiter,
   *  then 1 or more category identifiers separated by the category delimiter.
   * For example, with the default delimiters (pipe | and comma ,), the array of map data might look
   *  like this:
   *
   * ```json
   * [
   *  "facebook.com|64,100",
   *  "google.com|5,280,974",
   *  "smoothwall.com|42",
   * ]
   * ```
   *
   * @note This must be sorted in alphabetical order based on the entity. Categories and
   *  delimiters do not influence the sort. Duplicates should be avoided.
   */
  private _map: string[] = [];

  /**
   * The character which will separate the entity from the categories in each element of _map.
   */
  private _entityDelimiter: string = '|';

  /**
   * The character which will separate consecutive category identifiers in _map.
   */
  private _categoryDelimiter: string = ',';
}

/**
 * A simple utility class for efficiently checking whether strings occurs in another string.
 * This is optimised for situations where the same search may be performed multiple times on the
 *  same large string. The result of each search is remembered so that it doesn't need to be done
 *  again next time.
 */
export default class CachedSubstringMatcher {
  /**
   * Construct a new instance which will check for substrings (needles) in the given haystack.
   *
   * @param haystack The string to check for matches in. This cannot be changed after construction.
   */
  public constructor(haystack: string) {
    this.haystack = haystack;
  }

  /**
   * Check if the specified string occurs in the haystack.
   * If exactly the same string has been searched for before then a cached result will be returned
   *  instead of doing the search again from scratch.
   *
   * @param needle The potential substring to search for.
   * @returns True if the specified needle occurs in the haystack, or false if not.
   */
  public readonly includes = (needle: string): boolean => {
    let result = this._needles.get(needle);
    if (result === undefined) {
      result = this.haystack.includes(needle);
      this._needles.set(needle, result);
    }
    return result;
  };

  /**
   * The string to search within.
   */
  public readonly haystack: string;

  /**
   * The results of the searches which have been done so far.
   * Each key is a string which was searched for. Each corresponding value is a boolean indicating
   *  whether the string was found.
   */
  private readonly _needles = new Map<string, boolean>();
}

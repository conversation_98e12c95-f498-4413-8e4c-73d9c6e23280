import CachedSubstringMatcher from './CachedSubstringMatcher';

describe('CachedSubstringMatcher', () => {
  describe('includes()', () => {
    it('returns true if the specified substring occurs and has not been searched for before', () => {
      const matcher = new CachedSubstringMatcher('hello world');
      expect(matcher.includes('llo')).toBeTrue();
    });

    it('returns true if the specified substring occurs and has been searched for before', () => {
      const matcher = new CachedSubstringMatcher('hello world');
      matcher.includes('llo');
      expect(matcher.includes('llo')).toBeTrue();
    });

    it('returns false if the specified substring does not occur and has not been searched for before', () => {
      const matcher = new CachedSubstringMatcher('hello world');
      expect(matcher.includes('foobar')).toBeFalse();
    });

    it('returns false if the specified substring does not occur and has been searched for before', () => {
      const matcher = new CachedSubstringMatcher('hello world');
      matcher.includes('foobar');
      expect(matcher.includes('foobar')).toBeFalse();
    });
  });
});

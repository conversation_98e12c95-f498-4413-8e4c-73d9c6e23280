import UnifiedDiffMapBuilder from 'guardian/patching/UnifiedDiffMapBuilder';
import CategorisationMap from '../utilities/CategorisationMap';
import testDataManager from 'test-helpers/testDataManager';
import { areArraysEqual } from 'utilities/Helpers';

const testData: string[] = [
  'bar|1142,930',
  'blah|42,90',
  'foo|91,104,227',
  'foobar|3783,91,104',
  'hello.world.1|707,6728,84',
  'hello.world.2|0',
  'xyzzy|881,930,42',
];

describe('CategorisationMap', () => {
  // -----------------------------------------------------------------------------------------------

  describe('constructor()', () => {
    it('sets entity delimiter to pipe character by default', () => {
      const categorisationMap = new CategorisationMap();
      expect(categorisationMap.entityDelimiter).toEqual('|');
    });

    it('sets category delimiter to comma by default', () => {
      const categorisationMap = new CategorisationMap();
      expect(categorisationMap.categoryDelimiter).toEqual(',');
    });

    it('sets the entity delimiter if specified', () => {
      const categorisationMap = new CategorisationMap([], '+', '=');
      expect(categorisationMap.entityDelimiter).toEqual('+');
    });

    it('sets the category delimiter if specified', () => {
      const categorisationMap = new CategorisationMap([], '+', '=');
      expect(categorisationMap.categoryDelimiter).toEqual('=');
    });

    it('throws an error if the entity delimiter is empty', () => {
      expect(() => {
        new CategorisationMap([], '', ','); // eslint-disable-line no-new
      }).toThrow();
    });

    it('throws an error if the category delimiter is empty', () => {
      expect(() => {
        new CategorisationMap([], '|', ''); // eslint-disable-line no-new
      }).toThrow();
    });

    it('throws an error if the entity and category delimiters are the same', () => {
      expect(() => {
        new CategorisationMap([], '@', '@'); // eslint-disable-line no-new
      }).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('setMap()', () => {
    it('replaces the map data specified in the constructor', () => {
      const categorisationMap = new CategorisationMap(testData);
      categorisationMap.setMap(['blahblahblah|1,2,3', 'foobarfoobar|4,5,6']);
      expect(categorisationMap.contains('hello.world.1')).toBeFalse();
      expect(categorisationMap.lookupCategories('foobarfoobar')).toEqual(new Set(['4', '5', '6']));
    });

    it('sets the entity delimiter if specified', () => {
      const categorisationMap = new CategorisationMap();
      categorisationMap.setMap([], '*', '&');
      expect(categorisationMap.entityDelimiter).toEqual('*');
    });

    it('sets the category delimiter if specified', () => {
      const categorisationMap = new CategorisationMap();
      categorisationMap.setMap([], '*', '&');
      expect(categorisationMap.categoryDelimiter).toEqual('&');
    });

    it('throws an error if the entity delimiter is an empty string', () => {
      const categorisationMap = new CategorisationMap();
      expect(() => {
        categorisationMap.setMap([], '', ',');
      }).toThrow();
    });

    it('throws an error if the category delimiter is an empty string', () => {
      const categorisationMap = new CategorisationMap();
      expect(() => {
        categorisationMap.setMap([], '|', '');
      }).toThrow();
    });

    it('throws an error if the entity and category delimiters are the same', () => {
      const categorisationMap = new CategorisationMap();
      expect(() => {
        categorisationMap.setMap([], '@', '@');
      }).toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('length()', () => {
    it('returns 0 if no entities have been loaded yet', () => {
      const categorisationMap = new CategorisationMap();
      expect(categorisationMap.length).toEqual(0);
    });

    it('returns the number of entities currently stored', () => {
      const categorisationMap = new CategorisationMap(testData);
      expect(categorisationMap.length).toEqual(7);
    });
  });

  // -----------------------------------------------------------------------------------------------

  // entityDelimiter() is covered by other tests

  // -----------------------------------------------------------------------------------------------

  // categoryDelimiter() is covered by other tests

  // -----------------------------------------------------------------------------------------------

  describe('clear', () => {
    it('does nothing if no data has been loaded', () => {
      const categorisationMap = new CategorisationMap();
      expect(() => {
        categorisationMap.clear();
      }).not.toThrow();
    });

    it('deletes the stored entities and categories', () => {
      const categorisationMap = new CategorisationMap(testData);
      categorisationMap.clear();
      expect(categorisationMap.length).toEqual(0);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('patchFromUnifiedDiff()', () => {
    it('correctly applies a real blocklist diff', () => {
      // Use blocklist data loaded from local files.
      const data = testDataManager.blocklistWithTypicalDiff;

      // Load a URL list file into a CategorisationMap.
      const filename = 'domainsurls.com';
      const oldUrlList = JSON.parse(data.from.files[filename]);
      const categorisationMap = new CategorisationMap(oldUrlList);

      // A URL which is removed by the patch should be categorised correctly before patching.
      const input1 = 'nomyllub.';
      expect(categorisationMap.lookupCategories(input1)).toContain('753');

      // A URL which is added by the patch should not be categorised before patching.
      const input2 = 'bew-oce.';
      expect(categorisationMap.lookupCategories(input2)).toBeEmpty();

      // A URL which is not changed by the patch should be categorised before and after patching.
      const input3 = 'cbb./education';
      expect(categorisationMap.lookupCategories(input3)).toContain('200');

      // Apply the corresponding patch.
      const unifiedDiff = data.diff.files['blocklist.diff'];
      const diffMaps = [...UnifiedDiffMapBuilder.buildFromFile(unifiedDiff)];
      const diffMap = diffMaps.find((value) => value.oldFilePath.includes(filename));
      if (diffMap === undefined) {
        throw new Error('Expected diff not found in blocklist diff data.');
      }
      categorisationMap.patchFromUnifiedDiff(diffMap);

      // A URL which is removed by the patch should not be categorised after patching.
      expect(categorisationMap.lookupCategories(input1)).toBeEmpty();

      // A URL which is added by the patch should be categorised correctly after patching.
      expect(categorisationMap.lookupCategories(input2)).toContain('752');

      // A URL which is not changed by the patch should be categorised before and after patching.
      expect(categorisationMap.lookupCategories(input3)).toContain('200');

      // Check that the result matches the equivalent full blocklist.
      // Note that oldUrlList will have been modified in-place.
      const newUrlList = JSON.parse(data.to.files[filename]);
      expect(areArraysEqual(oldUrlList, newUrlList)).toBeTrue();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('lookupCategories()', () => {
    it('returns the expected category identifiers for the specified entity', () => {
      const categorisationMap = new CategorisationMap(testData);

      expect(categorisationMap.lookupCategories('bar')).toEqual(new Set(['1142', '930']));
      expect(categorisationMap.lookupCategories('foobar')).toEqual(new Set(['3783', '91', '104']));
      expect(categorisationMap.lookupCategories('xyzzy')).toEqual(new Set(['881', '930', '42']));
    });

    it('returns an empty set if the specified entity is not recognised', () => {
      const categorisationMap = new CategorisationMap(testData);

      expect(categorisationMap.lookupCategories('blahblah')).toEqual(new Set());
      expect(categorisationMap.lookupCategories('hello.world.3')).toEqual(new Set());
      expect(categorisationMap.lookupCategories('zzzzz')).toEqual(new Set());
    });

    it('is case sensitive', () => {
      const categorisationMap = new CategorisationMap(testData);
      expect(categorisationMap.lookupCategories('BAR')).toEqual(new Set());
    });

    // TODO: test with a large dataset loaded from minifilter data?
  });

  // -----------------------------------------------------------------------------------------------

  describe('contains()', () => {
    it('returns true if the specified entity is in the map', () => {
      const categorisationMap = new CategorisationMap(testData);
      expect(categorisationMap.contains('bar')).toBeTrue();
      expect(categorisationMap.contains('hello.world.1')).toBeTrue();
      expect(categorisationMap.contains('xyzzy')).toBeTrue();
    });

    it('returns false if the specified entity is not in the map', () => {
      const categorisationMap = new CategorisationMap(testData);
      expect(categorisationMap.contains('blahblah')).toBeFalse();
      expect(categorisationMap.contains('hello.world.3')).toBeFalse();
      expect(categorisationMap.contains('zzzzz')).toBeFalse();
    });

    it('is case sensitive', () => {
      const categorisationMap = new CategorisationMap(testData);
      expect(categorisationMap.contains('BAR')).toBeFalse();
    });
  });

  // -----------------------------------------------------------------------------------------------

  // _findEntry() is private, but is covered by lookupCategories() and contains().

  // -----------------------------------------------------------------------------------------------

  describe('extractEntity()', () => {
    it('returns the entity part of the specified entry', () => {
      const categorisationMap = new CategorisationMap();
      expect(categorisationMap.extractEntity('blah-blah-blah|38790,1862798,102')).toEqual(
        'blah-blah-blah',
      );
      expect(categorisationMap.extractEntity('foobar|')).toEqual('foobar');
      expect(categorisationMap.extractEntity('|38790,1862798,102')).toEqual('');
    });

    it('returns the entire entry if no entity delimiter was found', () => {
      const categorisationMap = new CategorisationMap();
      expect(categorisationMap.extractEntity('blah,38790,1862798,102')).toEqual(
        'blah,38790,1862798,102',
      );
    });

    it('ignores all but the last occurrence of the entity delimiter', () => {
      const categorisationMap = new CategorisationMap();
      expect(categorisationMap.extractEntity('blah|foo|bar|38790,1862798,102')).toEqual(
        'blah|foo|bar',
      );
    });

    it('works with custom delimiters', () => {
      const categorisationMap = new CategorisationMap([], '#', '?');
      expect(categorisationMap.extractEntity('foo|bar#123?456?789')).toEqual('foo|bar');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('extractCategories()', () => {
    it('returns a set of category identifiers from the specified entry', () => {
      const categorisationMap = new CategorisationMap();

      expect(categorisationMap.extractCategories('abc|123')).toEqual(new Set(['123']));

      expect(categorisationMap.extractCategories('blah-blah-blah|38790,1862798,102')).toEqual(
        new Set(['38790', '1862798', '102']),
      );
    });

    it('returns an empty set if the specified entry contains no category identifiers', () => {
      const categorisationMap = new CategorisationMap();
      expect(categorisationMap.extractCategories('hello.world.1|')).toEqual(new Set());
    });

    it('returns an empty set if the specified entry contains no entity delimiter', () => {
      const categorisationMap = new CategorisationMap();
      expect(categorisationMap.extractCategories('hello.world.1')).toEqual(new Set());
    });

    it('treats category identifiers as strings', () => {
      const categorisationMap = new CategorisationMap();
      expect(categorisationMap.extractCategories('abc|123,004,xyz')).toEqual(
        new Set(['123', '004', 'xyz']),
      );
    });

    it('ignores all but the last occurrence of the entity delimiter', () => {
      const categorisationMap = new CategorisationMap();
      expect(categorisationMap.extractCategories('blah|foo|bar|38790,1862798,102')).toEqual(
        new Set(['38790', '1862798', '102']),
      );
    });

    it('works with custom delimiters', () => {
      const categorisationMap = new CategorisationMap([], '#', '?');
      expect(categorisationMap.extractCategories('foo|bar#123?456?789')).toEqual(
        new Set(['123', '456', '789']),
      );
    });
  });
});

/**
 * Hierarchically deconstruct a URL into various forms which might include the same resource.
 * This enables use to do fuzzy matching against the Guardian blocklist. Instead of looking for the
 *  exact URL which the user visited, we'll look-up every permutation of domain and path level.
 *
 * Example input URL: http://dom.example.com/path/to?foo#head
 *
 * Example output:
 * NOT REVERSED                         REVERSED
 * com                                  moc.
 * com/path                             moc./path
 * com/path/to                          moc./path/to
 * com/path/to?foo#head                 moc./path/to?foo#head
 * example.com                          moc.elpmaxe.
 * example.com/path                     moc.elpmaxe./path
 * example.com/path/to                  moc.elpmaxe./path/to
 * example.com/path/to?foo#head         moc.elpmaxe./path/to?foo#head
 * dom.example.com                      moc.elpmaxe.mod.
 * dom.example.com/path                 moc.elpmaxe.mod./path
 * dom.example.com/path/to              moc.elpmaxe.mod./path/to
 * dom.example.com/path/to?foo#head     moc.elpmaxe.mod./path/to?foo#head
 *
 * For example, a rule might block "example.com/path". If the user actually visits
 *  "dom.example.com/path/to", then we'll still match it against the rule.
 *
 * @param url The URL to be deconstructed.
 * @param reverseDomain If true, the returned versions of the URL will reverse the domain
 *  name and append an extra dot. E.g. "example.com/foo/bar" becomes "moc.elpmaxe./foo/bar"
 * @param omitTopLevelDomain If true, the top-level domain (e.g. ".com") will be left out of all the
 *  results. This is useful if we're querying a TLD-specific domainsurls blocklist file in which the
 *  top-level domain is omitted.
 * @return Returns an array containing the original URL as a string (without the protocol prefix),
 *  plus all of the deconstructed forms, optionally with the domain reversed and/or TLD omitted.
 *  Note that this will return an empty array if there is no domain, or there only only one level in
 *  the domain and "omitTopLevelDomain" is true.
 *
 * @note This was previously called guardianFormatUrl().
 *
 * @see https://smoothwall-dev.atlassian.net/wiki/spaces/DST/pages/1396998145/Blocklist+components
 *
 * @todo Move this into the Blocklist class?
 */
export const generateUrlVariationsForLookup = (
  url: URL,
  reverseDomain: boolean = false,
  omitTopLevelDomain: boolean = false,
): string[] => {
  const domainLevels = getDomainLevels(url);
  const pathLevels = getPathLevels(url);
  const haveUsableQuery: boolean = url.search !== '/' && url.search.length > 0;
  const output: string[] = [];

  if (omitTopLevelDomain) {
    domainLevels.pop();
  }

  // Descend through the domain, from the top-level domain (or the second-top) to the lowest
  //  sub-domain.
  for (let domainDepth = 1; domainDepth <= domainLevels.length; domainDepth++) {
    let domainSoFar: string = domainLevels.slice(-domainDepth).join('.');
    if (reverseDomain) {
      domainSoFar = [...domainSoFar].reverse().join('') + '.';
    }

    output.push(domainSoFar);

    // Descend through the path, from the top-most folder to the most nested sub-folder.
    for (let pathDepth = 1; pathDepth <= pathLevels.length; pathDepth++) {
      output.push(domainSoFar + '/' + pathLevels.slice(0, pathDepth).join('/'));
    }

    if (haveUsableQuery) {
      output.push(domainSoFar + url.pathname + url.search);
    }
  }

  return output;
};

/**
 * Hierarchically deconstruct a URL into various forms which might include the same resource.
 * This enables use to do fuzzy matching against the Guardian blocklist.
 *
 * The deconstruction done here is not as detailed as it is for a full guardian look up.
 * Based on the guidance given by iwf we must not decompose the subdomains of the URL. It must exactly match the complete domain, including subdomains..
 * However path sections must still be decomposed. For example  if the IWF list contains a bare domain or folder, then any URL beneath that domain or folder must be matched as well.
 *
 * Guidance taken from page 7 of this pdf https://iwf.my.salesforce.com/sfc/p/#20000000105F/a/6M000000gc2G/uTPDxZLVOG8I252jLRkrIIjxSvb1qVTgAYgQG5hay5w
 * @param url The url to generate variations for.
 * @returns An array of strings that contains all of the possible variations for the url.
 */
export const generateUrlVariationsForIwf = (url: URL): string[] => {
  const output: string[] = [];

  const hostname = url.hostname;
  const domainLevels = getDomainLevels(url);
  const pathLevels = getPathLevels(url);

  // If the URL has a hostname, then include the bare hostname in look-ups. Note that some URLs
  //  don't have a hostname (e.g. local file paths). We don't want to look-up an empty string.
  if (hostname !== '') {
    output.push(hostname);
  }

  if (domainLevels[0] === 'www') {
    output.push(domainLevels.slice(1).join('.'));
  }

  for (let pathDepth = 1; pathDepth <= pathLevels.length; pathDepth++) {
    output.push(hostname + '/' + pathLevels.slice(0, pathDepth).join('/'));
  }

  if (url.searchParams.size > 0) {
    output.push(hostname + '/' + pathLevels.join('/') + url.search);
  }

  return output;
};

/**
 * Splits the given url's hostname into separate domain parts.
 * @param url The url to split
 * @returns An array of strings with the different parts of the domain.
 */
const getDomainLevels = (url: URL): string[] => url.hostname.split('.').filter((x) => x.length > 0);

/**
 * Splits the given url's path into separate path parts.
 * @param url The url to split
 * @returns An array of strings with the different parts of the path.
 */
const getPathLevels = (url: URL): string[] => url.pathname.split('/').filter((x) => x.length > 0);

/**
 * Extract the string content from a single line of a JSON array blocklist file.
 * This is used when processing text-based diffs which apply to JSON array blocklist files, such as
 *  domainsurls.com. Those files consist of an array of strings, formatted with one entry per line.
 * A text-based diff is not JSON-aware so it produces values which include the JSON syntax, such as
 *  enclosing quotation marks, and comma separators between array elements. This function strips the
 *  JSON syntax and extracts the raw string contents.
 *
 * @param input The original line from the blocklist file, including any JSON syntax. It should not
 *  include any diff syntax.
 * @returns The raw contents of the string extracted from the input.
 * @throws {Error} The line did not contain a quoted string.
 *
 * @warning For efficiency, this does not parse the string as a JSON value. It simply extracts
 *  everything between the first and last quotation marks as-is, even if there are other quotation
 *  marks between them. Escape sequences for special characters (such as embedded quotation marks or
 *  tabs) will not be unescaped. This is a safe limitation as long as the file only contains limited
 *  data such as URLs and video IDs. More complex data, such as regular expressions, would need to
 *  be properly parsed as JSON to ensure special characters are handled correctly.
 */
export const extractStringValueFromJsonArrayBlocklistLine = (input: string): string => {
  const firstQuoteAt = input.indexOf('"');
  const lastQuoteAt = input.lastIndexOf('"');
  if (firstQuoteAt === lastQuoteAt) {
    throw new Error('Expected a quoted string. Actual value: ' + input);
  }
  return input.substring(firstQuoteAt + 1, lastQuoteAt);
};

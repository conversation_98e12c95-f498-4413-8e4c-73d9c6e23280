import {
  extractStringValueFromJsonArrayBlocklistLine,
  generateUrlVariationsForIwf,
  generateUrlVariationsForLookup,
} from './GuardianUtilities';

describe('GuardianUtilities', () => {
  // -----------------------------------------------------------------------------------------------

  describe('generateUrlVariationsForLookup', () => {
    it('deconstructs every level of the domain', () => {
      const output = generateUrlVariationsForLookup(new URL('http://www.example.com'));
      expect(output).toContain('com');
      expect(output).toContain('example.com');
      expect(output).toContain('www.example.com');
    });

    it('deconstructs every level of the path', () => {
      const output = generateUrlVariationsForLookup(new URL('http://com/foo/bar/blah'));
      expect(output).toContain('com');
      expect(output).toContain('com/foo');
      expect(output).toContain('com/foo/bar');
      expect(output).toContain('com/foo/bar/blah');
    });

    it('generates every permutation of domain and path level', () => {
      const output = generateUrlVariationsForLookup(new URL('http://www.example.com/foo/bar/blah'));

      expect(output).toContain('com');
      expect(output).toContain('com/foo');
      expect(output).toContain('com/foo/bar');
      expect(output).toContain('com/foo/bar/blah');

      expect(output).toContain('example.com');
      expect(output).toContain('example.com/foo');
      expect(output).toContain('example.com/foo/bar');
      expect(output).toContain('example.com/foo/bar/blah');

      expect(output).toContain('www.example.com');
      expect(output).toContain('www.example.com/foo');
      expect(output).toContain('www.example.com/foo/bar');
      expect(output).toContain('www.example.com/foo/bar/blah');
    });

    it('includes the full path with and without the query parameters', () => {
      const output = generateUrlVariationsForLookup(new URL('http://com/foo/bar/blah?hello=world'));
      expect(output).toContain('com/foo/bar/blah');
      expect(output).toContain('com/foo/bar/blah?hello=world');
    });

    it('does not include the query parameters with partial paths', () => {
      const output = generateUrlVariationsForLookup(new URL('http://com/foo/bar/blah?hello=world'));
      expect(output).not.toContain('com/foo/bar?hello=world');
    });

    it('reverses the domain and appends a dot if reverseDomain is true', () => {
      const output = generateUrlVariationsForLookup(new URL('http://test.example.com'), true);
      expect(output).toContain('moc.');
      expect(output).toContain('moc.elpmaxe.');
      expect(output).toContain('moc.elpmaxe.tset.');
    });

    it('does not reverse the path or query when reverseDomain is true', () => {
      const output = generateUrlVariationsForLookup(
        new URL('http://com/foo/bar/blah?hello=world'),
        true,
      );
      expect(output).toContain('moc./foo/bar/blah?hello=world');
    });

    it('optionally omits the top-level domain from results', () => {
      // Domain forwards:
      const forwards = generateUrlVariationsForLookup(
        new URL('http://test.example.com/foo'),
        false,
        true,
      );
      expect(forwards).toContain('example');
      expect(forwards).toContain('example/foo');
      expect(forwards).toContain('test.example');
      expect(forwards).toContain('test.example/foo');

      // Domain backwards:
      const backwards = generateUrlVariationsForLookup(
        new URL('http://test.example.com/foo'),
        true,
        true,
      );
      expect(backwards).toContain('elpmaxe.');
      expect(backwards).toContain('elpmaxe./foo');
      expect(backwards).toContain('elpmaxe.tset.');
      expect(backwards).toContain('elpmaxe.tset./foo');
    });

    it('returns an empty array if there is only one domain level and the top-level domain is omitted', () => {
      const output = generateUrlVariationsForLookup(
        new URL('http://localhost/foo/bar'),
        false,
        true,
      );
      expect(output).toEqual([]);
    });
  });

  describe('generateUrlVariationsForIwf', () => {
    it('does not deconstruct every level of the domain', () => {
      const output = generateUrlVariationsForIwf(new URL('http://www.example.com'));
      expect(output).toContain('www.example.com');
    });

    it('deconstructs every level of the path', () => {
      const output = generateUrlVariationsForIwf(new URL('http://com/foo/bar/blah'));
      expect(output).toContain('com');
      expect(output).toContain('com/foo');
      expect(output).toContain('com/foo/bar');
      expect(output).toContain('com/foo/bar/blah');
    });

    it('generates the full domain and every permutation of the path', () => {
      const output = generateUrlVariationsForIwf(new URL('http://www.example.com/foo/bar/blah'));

      expect(output).not.toContain('com');
      expect(output).not.toContain('com/foo');
      expect(output).not.toContain('com/foo/bar');
      expect(output).not.toContain('com/foo/bar/blah');

      expect(output).toContain('www.example.com');
      expect(output).toContain('www.example.com/foo');
      expect(output).toContain('www.example.com/foo/bar');
      expect(output).toContain('www.example.com/foo/bar/blah');
    });

    it('includes the full path with and without the query parameters', () => {
      const output = generateUrlVariationsForIwf(
        new URL('http://www.example.com/foo/bar/blah?hello=world'),
      );
      expect(output).toContain('www.example.com/foo/bar/blah');
      expect(output).toContain('www.example.com/foo/bar/blah?hello=world');
    });

    it('does not include the query parameters with partial paths', () => {
      const output = generateUrlVariationsForIwf(
        new URL('http://www.example.com/foo/bar/blah?hello=world'),
      );
      expect(output).not.toContain('.com/foo/bar?hello=world');
    });

    it('deconstructs every level of a local file path', () => {
      const output = generateUrlVariationsForIwf(new URL('file:///c:/foo/bar/blah.pdf'));
      expect(output).toContain('/c:');
      expect(output).toContain('/c:/foo');
      expect(output).toContain('/c:/foo/bar');
      expect(output).toContain('/c:/foo/bar/blah.pdf');
    });

    it('does not include an empty entry in cases where the URL has no hostname', () => {
      const output = generateUrlVariationsForIwf(new URL('file:///C:/foo/bar/blah.pdf'));
      expect(output).not.toContain('');
      expect(output).not.toContain('/');
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('extractStringValueFromJsonArrayBlocklistLine()', () => {
    interface TestCase {
      name: string;
      input: string;
      expected?: string;
      throws?: boolean;
    }

    const testCases: TestCase[] = [
      {
        name: 'correctly extracts an entry from one line of the domainsurls.com blocklist file',
        input: '"sppaxsocam./article.php?story=20030722165958234|39",',
        expected: 'sppaxsocam./article.php?story=20030722165958234|39',
      },
      {
        name: 'correctly extracts an entry from one line of the videoids blocklist file',
        input: '"17IgK9b6P2M|200",',
        expected: '17IgK9b6P2M|200',
      },
      {
        name: 'ignores spaces outside the quotation marks',
        input: '   "blah"  ,   ',
        expected: 'blah',
      },
      {
        name: 'retains spaces inside the quotation marks',
        input: '"  blah   ",',
        expected: '  blah   ',
      },
      {
        name: 'ignores an array element separator placed before the quoted string',
        input: ',"blah"',
        expected: 'blah',
      },
      {
        name: 'ignores an array element separator placed after the quoted string',
        input: '"blah",',
        expected: 'blah',
      },
      {
        name: 'ignores array element separators placed both before and after the quoted string',
        input: ',"blah",',
        expected: 'blah',
      },
      {
        name: 'allows the array element separator to be missing entirely',
        input: '"blah"',
        expected: 'blah',
      },
      {
        name: 'does not parse backslash escape sequences',
        // In JSON, "\\" would normally be unescaped to "\", and "\t" would be unescaped to a tab
        //  character. This function doesn't do that.
        input: String.raw`"blah\\foo\tbar",`,
        expected: String.raw`blah\\foo\tbar`,
      },
      {
        name: 'throws an error if the input contains an unquoted string',
        input: 'blah,',
        throws: true,
      },
      {
        name: 'throws an error if the input contains a single-quoted string',
        input: "'blah',",
        throws: true,
      },
      {
        name: 'throws an error if the input contains a data type other than a string',
        input: '123,',
        throws: true,
      },
      {
        name: 'throws an error if the input contains an unterminated quoted string',
        input: '"blah,',
        throws: true,
      },
      {
        name: 'throws an error if the input is empty',
        input: '',
        throws: true,
      },
    ];

    // Run each of the test cases above.
    test.each(testCases)('$name', ({ input, expected, throws }) => {
      if (throws === true) {
        expect(() => extractStringValueFromJsonArrayBlocklistLine(input)).toThrow();
      } else {
        expect(extractStringValueFromJsonArrayBlocklistLine(input)).toEqual(expected);
      }
    });
  });

  // -----------------------------------------------------------------------------------------------
});

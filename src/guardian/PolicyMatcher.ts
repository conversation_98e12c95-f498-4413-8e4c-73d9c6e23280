import { IPolicyConfig, IFilterPolicyFlat } from '../models/PolicyConfigModels';
import { getArrayIntersect } from '../utilities/Helpers';
import PolicyAction from './models/PolicyAction';
import IpService from 'services/IpService';
import FilterDecision from './models/FilterDecision';

/**
 * <PERSON>les matching the policy rules for a request using the given policy config.
 *
 * @usage To get the full filter decision use the `matchPolicies` function.
 */
export default class PolicyMatcher {
  constructor(ipService: IpService) {
    this._ipService = ipService;
  }

  /**
   * Matches all of the policies in the config to determine what action should be taken.
   * @param policyConfig The current policy config.
   * @param urlCategories The matched categories for the url.
   * @returns A `FilterDecision` with the details of any matches that were made.
   */
  public readonly matchPolicies = (
    policyConfig: IPolicyConfig,
    urlCategories: Set<string>,
  ): FilterDecision => {
    let decision = new FilterDecision();
    let matchedPolicy: IFilterPolicyFlat | undefined;

    for (const policy of policyConfig.flattenedPolicies) {
      decision = new FilterDecision();

      const whatMatches = this.whatMatches(policy.what, policyConfig, urlCategories);

      // If we didn't make a match skip to the next policy.
      if (whatMatches === undefined || whatMatches.size <= 0) {
        continue;
      }

      decision.timeslot = this.whenMatches(policy.when, policyConfig, this._getTimeInterval());

      if (decision.timeslot === undefined) {
        continue;
      }

      decision.location = this.whereMatches(policy.where, policyConfig);

      if (decision.location === undefined) {
        continue;
      }

      decision.group = this.whoMatches(policy.who, policyConfig);

      if (decision.group === undefined) {
        continue;
      }

      // Everything has matched so we can break out and make a decision.
      matchedPolicy = policy;
      decision.ruleId = policy.order;
      decision.policy = policy.id;
      break;
    }

    decision.categoryIds = urlCategories;
    decision.allow = true;
    if (
      matchedPolicy !== undefined &&
      (matchedPolicy.action === PolicyAction.block ||
        matchedPolicy.action === PolicyAction.softBlock)
    ) {
      decision.allow = false;
    }

    return decision;
  };

  /**
   * Uses the given info to match any content mods in the policy file and returns the ids for those mods.
   * @param policyConfig The current policy config.
   * @param categoryIds The category ids that have been matched.
   * @returns A list of any content mod ids that have been matched.
   */
  public matchContentModPolicies = (
    policyConfig: IPolicyConfig,
    categoryIds: Set<string>,
  ): string[] => {
    // Match any on prem content mods.
    const onPremMods = this._matchOnPremContentMods(policyConfig, categoryIds);

    // Match any cloud content mods.
    const cloudMods = this._matchCloudContentMods(policyConfig);

    // Merge the 2 sets and return them as an array.
    const matchedMods = new Set<string>([...onPremMods, ...cloudMods]);
    return Array.from(matchedMods);
  };

  /**
   * Matches the given url categories to the content modifications in the policy config.
   * The content modifications will then be used to get the ids of the default content modifiers that will need to be executed on the url.
   * @param policyConfig The current policy config.
   * @param urlCategories The matched categories for the url.
   * @returns A set of default content modifier ids that need to be applied to the url.
   */
  public readonly matchUrlModPolicies = (
    policyConfig: IPolicyConfig,
    urlCategories: Set<string>,
  ): Set<string> => {
    // Match any on prem content mods.
    const onPremMods = this._matchOnPremContentMods(policyConfig, urlCategories);

    // Match any cloud content mods.
    const cloudMods = this._matchCloudContentMods(policyConfig);

    // Merge the 2 sets and return them.
    return new Set<string>([...onPremMods, ...cloudMods]);
  };

  /**
   * Attempts to match the given categories to the categories in the flattened policies.
   * @param policy The flattened policy to search.
   * @param policyConfig The current policy config.
   * @param urlCategories The url categories to match against.
   * @returns A set of categories that have been matched. If nothing has been matched this will return undefined.
   */
  public readonly whatMatches = (
    policy: string[],
    policyConfig: IPolicyConfig,
    urlCategories: Set<string>,
  ): Set<string> | undefined => {
    for (const what of policy) {
      if (what === 'Everything') {
        return new Set<string>(['Everything']);
      }

      const filterGroups = policyConfig.category_filter_groups.filter((g) => g.id === what);
      const customCategories = policyConfig.custom_categories.filter(
        (c) => c.id === what && c.category_id !== undefined,
      );

      const categoriesArray = Array.from(urlCategories);

      // First check the category filter groups.
      if (filterGroups.length > 0) {
        for (const group of filterGroups) {
          const intersect = getArrayIntersect(group.source, categoriesArray);
          if (intersect.length > 0) {
            return new Set(intersect);
          }
        }
      } else {
        // If none matched then check the custom categories.
        if (customCategories.length <= 0) {
          continue;
        }

        const intersect = getArrayIntersect(
          customCategories.map((c) => c.category_id ?? ''),
          categoriesArray,
        );
        if (intersect.length > 0) {
          return new Set(intersect);
        }

        const category = [...urlCategories].find((c) => c === what);
        if (category !== undefined) {
          return new Set<string>(category);
        }
      }
    }

    return undefined;
  };

  /**
   * Checks if any of the timeslots in the policy match the current time.
   * @param policy The flattened policy to check.
   * @param policyConfig The current policy config.
   * @param timeInterval The time in seconds between the current time and the previous sunday. Used when matching the timeslots.
   * @returns The name of the timeslot if a match was made. Otherwise it will return undefined.
   */
  public readonly whenMatches = (
    policy: string[],
    policyConfig: IPolicyConfig,
    timeInterval: number,
  ): string | undefined => {
    for (const when of policy) {
      if (when === 'Anytime') {
        return 'Anytime';
      }
      const timeSlots = policyConfig?.time_slots.filter((t) => t.id === when);

      if (timeSlots === undefined || timeSlots?.length <= 0) {
        continue;
      }

      for (const slot of timeSlots) {
        for (const time of slot.times) {
          if (timeInterval > parseInt(time[0]) && timeInterval < parseInt(time[1])) {
            return slot.name;
          }
        }
      }
    }

    return undefined;
  };

  /**
   * Checks if any of the groups in the policy match.
   * @param policy The who component of the policy
   * @param policyConfig The current policy config object.
   * @returns The name of the group if a match was made. Otherwise it will return undefined.
   */
  public readonly whoMatches = (
    policy: string[],
    policyConfig: IPolicyConfig,
  ): string | undefined => {
    for (const who of policy) {
      if (who === 'Everyone') {
        return 'Everyone';
      }

      const matchedGroup = policyConfig.mappedGroups.find((group) => group.id === who);

      if (matchedGroup !== undefined) return matchedGroup.name;

      const user = policyConfig.users.find(
        (u) => u.id === who && u.name.toLowerCase() === this.currentUsername,
      );

      if (user !== undefined) {
        return user.name;
      }
    }

    return undefined;
  };

  /**
   * Checks if any of the locations in the policy matches the current device ip addresses.
   * @param policy The where component of the policy.
   * @param policyConfig The current policy config.
   * @returns Returns the name of the location which was matched, or undefined if no location was
   *  matched.
   */
  public readonly whereMatches = (
    policy: string[],
    policyConfig: IPolicyConfig,
  ): string | undefined => {
    for (const where of policy) {
      if (where === 'Everywhere') {
        return 'Everywhere';
      }

      const location = policyConfig.locations.filter((l) => l.id === where)[0];
      if (location !== undefined && this._ipService.isDeviceInLocation(location)) {
        return location.name;
      }
    }

    return undefined;
  };

  /**
   * Matches any on prem content mods from the policy config.
   * @param policyconfig the current policy config.
   * @param categoryids the category ids that have been matched.
   * @returns A set of blockman ids for the content mods that have been matched.
   */
  private readonly _matchOnPremContentMods = (
    policyConfig: IPolicyConfig,
    categoryIds: Set<string>,
  ): Set<string> => {
    if (policyConfig.flattenedContentMods === undefined) {
      console.debug(
        'Skipping on-prem content mods. The current policy does not contain flattenedContentMods.' +
          " This may mean it's a cached copy from an older version of the extension.",
      );
      return new Set();
    }

    const matchedCMods: Set<string> = new Set<string>();
    const onPremRuleIds: Set<string> = new Set<string>();

    for (const mod of policyConfig.flattenedContentMods) {
      if (this.whatMatches(mod.what, policyConfig, categoryIds) === undefined) {
        continue;
      }

      if (this.whereMatches(mod.where, policyConfig) === undefined) {
        continue;
      }

      if (this.whoMatches(mod.who, policyConfig) === undefined) {
        continue;
      }

      for (const rule of mod.ruleset) {
        onPremRuleIds.add(rule);
      }
    }

    // Now we have a list of on prem mods to apply they need to be matched to the blockman id.
    for (const rule of onPremRuleIds) {
      const blockmanMod = policyConfig.default_content_modifiers.find((r) => r.id === rule);
      if (blockmanMod?.modifier_id !== undefined) {
        matchedCMods.add(blockmanMod.modifier_id);
      }
    }

    return matchedCMods;
  };

  /**
   * Matches any cloud content mods from the policy config.
   * @param policyconfig the current policy config.
   * @returns A set of blockman ids for the content mods that have been matched.
   */
  private readonly _matchCloudContentMods = (policyConfig: IPolicyConfig): Set<string> => {
    const matchedCMods: Set<string> = new Set<string>();
    for (const mod of policyConfig.cloud_content_modifications) {
      const whoMatches = this.whoMatches(mod.groups, policyConfig);
      const whereMatches = this.whereMatches(mod.locations, policyConfig);

      if (mod.filterType === 'exclude') {
        if (whoMatches !== undefined || whereMatches !== undefined) {
          continue;
        }

        matchedCMods.add(mod.blockmanId);
        continue;
      }

      if (whoMatches !== undefined || whereMatches !== undefined) {
        matchedCMods.add(mod.blockmanId.toString());
      }
    }

    return matchedCMods;
  };

  /**
   * Calculates the interval between the previous midnight on sunday and now in seconds.
   * For use when comparing timeslots.
   * @returns The interval between the previous midnight on sunday and now in seconds.
   */
  private readonly _getTimeInterval = (): number => {
    const date = new Date();

    let currentTime = 0;
    currentTime += date.getDay() * 86400;
    currentTime += date.getHours() * 3600;
    currentTime += date.getMinutes() * 60;
    currentTime += date.getSeconds();

    return currentTime;
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Manages getting and caching the ip addresses needed for policy matching.
   */
  private readonly _ipService: IpService;

  /**
   * The user name of the current user.
   */
  public currentUsername: string = '';
}

/**
 * Describes a function which applies a body content mod to the current document.
 * A single content mod may consist of any number of individual changes.
 *
 * @returns An array of values corresponding to the individual changes made by this content mod.
 *  True means a change was made successfully. False means the change wasn't currently needed, e.g.
 *  because the relevant element has already been removed or modified. The change may be needed in
 *  future if the document is modified elsewhere. Null means the change will never be applicable to
 *  the current page/site. If the return value consists entirely of null values then there's no
 *  point executing the content mod again on the current page.
 */
export type BodyContentMod = () => Array<boolean | null>;

const jsBodyMods = {
  /**
   * Get a function which implements the specified body content mod.
   *
   * @param id A number which uniquely identifies the content mod to retrieve. This is technically a
   *  blockman category ID (albeit a hidden one), meaning it appears in the "category_data" file in
   *  the blocklist.
   * @return If the specified ID is recognised, then this returns a function which executes the
   *  specified content mod on the current document when called. If the specified ID was not
   *  recognised then this returns undefined. It's important to check the return value of the
   *  function to determine whether it needs to be called again when the document is modified.
   */
  getContentModScriptById: (id: number): BodyContentMod | undefined => {
    switch (id) {
      case 69:
        return jsBodyMods.forceSafeSearch;
      case 222:
        return jsBodyMods.removeYoutubeComments;
      case 392:
        return jsBodyMods.bakeYoutubeSafetyModeCookie;
      case 440:
        return jsBodyMods.enforceBBCiPlayerParentalGuidance;
      case 441:
        return jsBodyMods.removeFacebookCommentsPlugin;
      case 442:
        return jsBodyMods.removeDisqusComments;
      case 457:
        return jsBodyMods.removeBloggerComments;
      case 458:
        return jsBodyMods.removeWordPressComments;
      case 476:
        return jsBodyMods.removeYoutubeSidebar;
      case 478:
        return jsBodyMods.enforceITVPlayerParentalGuidance;
      case 524:
        return jsBodyMods.removeTranslationGoogleBingSearch;
      case 525:
        return jsBodyMods.blockDoodleGames;
      case 526:
        return jsBodyMods.removeAdsFromGoogle;
      case 579:
        return jsBodyMods.removeYoutubeVideosFromSearch;
      case 615:
        return jsBodyMods.forceVimeoMatureContentFilter;
      case 630:
        return jsBodyMods.googleWebSearchBasicMode;
      case 633:
        return jsBodyMods.disableYoutubeMiniPlayer;
      case 635:
        return jsBodyMods.removeYoutubeAds;
      case 636:
        return jsBodyMods.webSearchRemoveLyrics;
      default:
        return undefined;
    }
  },

  /**
   * Hard coded js content scripts
   */

  /** id:69 - Forces safe search on */
  forceSafeSearch: () => {
    const returnValues = [];

    // Rule for pixabay
    if (
      window.location.hostname.startsWith('pixabay.') ||
      window.location.hostname.startsWith('www.pixabay.')
    ) {
      if (!document.cookie.includes('g_rated=1')) {
        document.cookie = 'g_rated=1;domain=.pixabay.com';
        location.reload();
        returnValues.push(true);
      } else {
        returnValues.push(false);
      }
    }

    // Rule for ecosia
    if (
      window.location.hostname.startsWith('ecosia.') ||
      window.location.hostname.startsWith('www.ecosia.')
    ) {
      document.cookie.split(';').forEach((cookie) => {
        if (
          (cookie.startsWith('ECFG=') || cookie.startsWith(' ECFG=')) &&
          !cookie.includes('f=y')
        ) {
          document.cookie = 'ECFG=f=y;domain=.ecosia.org';
          location.reload();
          returnValues.push(true);
        } else {
          returnValues.push(false);
        }
      });
    }

    if (returnValues.length === 0) {
      // Safe search isn't relevant to the current page.
      return [null];
    }
    return returnValues;
  },

  /** id:222 - Youtube comments removal */
  removeYoutubeComments: () => {
    if (
      !(
        window.location.hostname.startsWith('youtube.com') ||
        window.location.hostname.startsWith('www.youtube.')
      )
    ) {
      return [null];
    }

    // Use more specific selectors to avoid interfering with UI elements
    const selectors = [
      '#watch-sidebar-live-chat:not([role="dialog"]):not([role="menu"])',
      '#watch-discussion:not([role="dialog"]):not([role="menu"])',
      'ytd-comments:not([role="dialog"]):not([role="menu"])',
      'ytd-live-chat-frame:not([role="dialog"]):not([role="menu"])',
      '#comments:not([role="dialog"]):not([role="menu"])',
    ];

    // First, collect all elements that match our selectors
    const allElements: Element[] = [];
    selectors.forEach((selector) => {
      document.querySelectorAll(selector).forEach((element) => {
        // Skip elements in protected containers
        if (isProtectedElement(element) || element.closest('[role="dialog"]') !== null) {
          console.debug('Skipping protected comment section:', element);
          return;
        }
        allElements.push(element);
      });
    });

    // Capture parent nodes for elements we'll actually remove
    const elementData: Array<{ parentNode: Node | null; height: number; width: number }> = [];
    allElements.forEach((element) => {
      elementData.push({
        parentNode: element.parentNode,
        height: element.clientHeight,
        width: element.clientWidth,
      });
    });

    // Remove the elements
    let removedAny = false;
    allElements.forEach((element) => {
      if (safelyRemoveElement(element)) {
        removedAny = true;
      }
    });

    const output = [removedAny];

    if (output.includes(true)) {
      elementData.forEach((x) => {
        // Check if the element is protected or if parentNode is null
        if (
          x.parentNode != null &&
          x.parentNode instanceof Element &&
          !isProtectedElement(x.parentNode)
        ) {
          x.parentNode.appendChild(getSWBlockedNode('removeYoutubeComments', x.height, x.width));
        }
      });
    }

    return output;
  },

  /** id:392 - Youtube SafetyMode cookie */
  bakeYoutubeSafetyModeCookie: () => {
    if (
      !(
        window.location.hostname.startsWith('youtube.com') ||
        window.location.hostname.startsWith('www.youtube.')
      )
    ) {
      return [null];
    }

    // Check if we need to set the cookie
    const needsToSetCookie =
      window === window.parent && !/PREF=[^;]*f2=800000/.test(document.cookie);

    if (needsToSetCookie) {
      // Set the cookie without forcing an immediate page reload
      document.cookie = 'PREF=f2=8000000;domain=.youtube.com';

      // Add event listeners to handle user interactions with settings
      // But exclude interactions with the Restricted Mode dialog
      const handleSettingsInteraction = (event: MouseEvent): void => {
        // Check if the click is on or within a Restricted Mode dialog
        const target = event.target as HTMLElement;
        if (target === null || target === undefined) {
          // If target is null, proceed with the reload
          document.removeEventListener('click', handleSettingsInteraction, true);

          setTimeout(() => {
            if (document.visibilityState === 'visible') {
              location.reload();
            }
          }, 500);
          return;
        }

        const restrictedModeDialog = target.closest(
          '[role="dialog"][aria-label*="Restricted Mode"]',
        );
        const restrictedModeMenuItem = target.closest(
          'a[href="#"][role="menuitem"][aria-label*="Restricted Mode"]',
        );

        // Don't reload if the user is interacting with Restricted Mode settings
        if (restrictedModeDialog != null || restrictedModeMenuItem != null) {
          return;
        }

        // Remove these event listeners to prevent multiple triggers
        document.removeEventListener('click', handleSettingsInteraction, true);

        // Use setTimeout to allow the click to complete before potentially reloading
        setTimeout(() => {
          // Only reload if we're still on the same page
          if (document.visibilityState === 'visible') {
            location.reload();
          }
        }, 500);
      };

      // Listen for clicks on the document, but wait for user to interact first
      setTimeout(() => {
        document.addEventListener('click', handleSettingsInteraction, true);
      }, 2000); // Give user time to interact with settings

      return [true];
    }

    return [false];
  },

  /** id:440 - BBC iPlayer - Enforce Parental Guidance Lock */
  enforceBBCiPlayerParentalGuidance: () => {
    if (
      !(
        window.location.hostname.startsWith('bbc.com') ||
        window.location.hostname.startsWith('www.bbc.')
      )
    ) {
      return [null];
    }

    // Only block the video if it has parental guidance.
    const guidanceBanner = document.querySelector('.banner.guidance-banner');

    if (guidanceBanner == null) {
      return [false];
    }

    const videoPlayer = document.querySelector('smp-toucan-player');
    if (videoPlayer !== null) {
      const playbackElement = videoPlayer.shadowRoot?.querySelector('smp-playback');
      if (playbackElement !== null) {
        const videoElement = playbackElement?.shadowRoot?.querySelector('video');
        if (videoElement !== null && videoElement !== undefined) {
          videoElement.muted = true;
        }
      }
      videoPlayer.remove();
    }

    const element = document.querySelector(
      '.play-cta__inner.play-cta__inner--button',
    )?.parentElement;

    if (element != null) {
      element.innerHTML = "<span style='color:white; font-size:14pt;'>Blocked by Smoothwall</span>";

      return [true];
    }

    return [false];
  },

  /** id:441 - Facebook comments plugin removal */
  removeFacebookCommentsPlugin: () => {
    if (!window.location.hostname.endsWith('facebook.com')) {
      return [null];
    }

    const selectors = [
      '.fb-comment-embed',
      '.fb-comments',
      '.x1jx94hy.x78zum5.xdt5ytf.x2lah0s.xw2csxc.x1odjw0f.xmd6oqt.x13o0s5z',
    ];
    const elementData = captureParentNodes(selectors);
    const output = findAndRemoveBySelector(selectors);

    if (output.includes(true)) {
      elementData.forEach((x) =>
        x.parentNode.appendChild(
          getSWBlockedNode('removeFacebookCommentsPlugin', x.height, x.width),
        ),
      );
    }

    return output;
  },

  /** id:442 - Disqus Comment Removal */
  removeDisqusComments: () => {
    const selectors = ['#disqus_thread'];
    const elementData = captureParentNodes(selectors);
    const output = findAndRemoveBySelector(selectors);

    if (output.includes(true)) {
      elementData.forEach((x) =>
        x.parentNode.appendChild(getSWBlockedNode('removeDisqusComments', x.height, x.width)),
      );
    }

    return output;
  },

  /** id:457 - Blogger Comment Removal */
  removeBloggerComments: () => {
    const selectors = ['.comments#comments'];
    const elementData = captureParentNodes(selectors);
    const output = findAndRemoveBySelector(selectors);

    if (output.includes(true)) {
      elementData.forEach((x) =>
        x.parentNode.appendChild(getSWBlockedNode('removeBloggerComments', x.height, x.width)),
      );
    }

    return output;
  },

  /** id:458 - WordPress Comment Removal */
  removeWordPressComments: () => {
    const selectors = ['#content #comments'];
    const elementData = captureParentNodes(selectors);
    const output = findAndRemoveBySelector(selectors);

    if (output.includes(true)) {
      elementData.forEach((x) =>
        x.parentNode.appendChild(getSWBlockedNode('removeWordPressComments', x.height, x.width)),
      );
    }

    return output;
  },

  /** id:476 - YouTube - Disable Auto Play */
  removeYoutubeSidebar: () => {
    if (!window.location.hostname.endsWith('youtube.com')) {
      return [null];
    }

    // Remove the sidebar but be careful not to remove any UI elements that might be part of menus
    // Use more specific selectors to target only the content areas we want to remove
    const selectors = [
      '#donation-shelf:not([role="dialog"]):not([role="menu"])',
      '#chat:not([role="dialog"]):not([role="menu"])',
      '#related:not([role="dialog"]):not([role="menu"])',
    ];
    const elementData = captureParentNodes(selectors);
    const output = findAndRemoveBySelector(selectors);

    if (output.includes(true)) {
      elementData.forEach((x) => {
        // Check if the parent node exists and is not a protected element
        if (
          x.parentNode !== null &&
          x.parentNode !== undefined &&
          !isProtectedElement(x.parentNode)
        ) {
          x.parentNode.appendChild(getSWBlockedNode('removeYoutubeSidebar', x.height, x.width));
        }
      });
    }

    // Also disable autoplay, but be careful not to interfere with other UI elements
    const autoplayToggle = document.querySelector<HTMLElement>('.ytp-autonav-toggle-button');
    if (autoplayToggle != null) {
      // Check if the toggle is protected before interacting with it
      if (
        !isProtectedElement(autoplayToggle) &&
        // Check if the toggle is in a protected container
        autoplayToggle.closest('[role="dialog"]') === null &&
        autoplayToggle.closest('[role="menu"]') === null &&
        // Only click if it's currently enabled
        autoplayToggle.getAttribute('aria-checked') === 'true' &&
        // Make sure its actually visible, since it is actually present on all pages including the homepage but just hidden
        autoplayToggle.checkVisibility()
      ) {
        autoplayToggle.click();
        output.push(true);
      } else {
        output.push(false);
      }
    } else {
      output.push(false);
    }

    return output;
  },

  /** id:478 - ITV Player - Enforce Parental Guidance Lock */
  enforceITVPlayerParentalGuidance: () => {
    if (
      !(
        window.location.hostname.startsWith('itv.com') ||
        window.location.hostname.startsWith('www.itv.')
      )
    ) {
      return [null];
    }

    const elementParagraph = document.querySelector<HTMLElement>('.cp_guidance__paragraph');
    const elementContainer = document.getElementsByClassName('cp_guidance__container');

    // Paragraphs are used for single video pages.
    const paragraphResults = [
      elementParagraph != null
        ? ((elementParagraph.innerText = 'Parental Guidance enforced by Smoothwall'), true)
        : false,
      elementParagraph != null
        ? (document
            .querySelector<HTMLElement>('.videoPlayerActionButtons')
            ?.setAttribute('style', 'pointer-events: none; opacity: 0.6'),
          true)
        : false,
    ];

    // On the live page there can be any number of containers that will all need blocking.
    const containerResults: boolean[] = [];
    if (elementContainer != null) {
      for (const element of elementContainer) {
        // Set the text in the container.
        element.textContent = 'Parental Guidance enforced by Smoothwall';
        containerResults.push(true);

        // Remove the play button that is inside the same parent node.
        const buttonElement = element.parentNode?.querySelector('.now-next-buttons');
        if (buttonElement != null) {
          buttonElement.remove();
          containerResults.push(true);
        } else {
          containerResults.push(false);
        }
      }
    }

    const output = [...paragraphResults, ...containerResults];

    return output;
  },

  /** id:524 - Remove Translation From Google and Bing Search Results */
  removeTranslationGoogleBingSearch: () => {
    if (
      !(
        window.location.hostname.startsWith('google.') ||
        window.location.hostname.startsWith('www.google.')
      ) &&
      !(
        window.location.hostname.startsWith('bing.') ||
        window.location.hostname.startsWith('www.bing.')
      )
    ) {
      return [null];
    }

    const selectorsToRemove = ['div[class="rLrQHf"][role=presentation]'];

    const output = findAndRemoveBySelector(selectorsToRemove);

    const selectors = [
      'div#search *> div#tw-main',
      '#tta_container',
      'li.b_top',
      '.KIy09e',
      '.b_ans',
      '.AuVD.cUnQKe.vt6azd',
    ];
    const elementData = captureParentNodes(selectors);
    output.concat(findAndRemoveBySelector(selectors));

    if (output.includes(true)) {
      elementData.forEach((x) =>
        x.parentNode.appendChild(
          getSWBlockedNode('removeTranslationGoogleBingSearch', 60, x.width),
        ),
      );
    }

    return output;
  },

  /** id:525 - Block google doodle games */
  blockDoodleGames: () => {
    if (
      !(
        window.location.hostname.startsWith('google.') ||
        window.location.hostname.startsWith('www.google.')
      )
    ) {
      return [null];
    }

    const output: boolean[] = [];
    const elements = new Set<Element>();
    const xPathSelectors = [
      '//img[contains(@src, "google.com/logos/")]//ancestor::block-component',
    ];

    xPathSelectors.forEach((selector) => {
      const nodeSnapshot = document.evaluate(
        selector,
        document,
        null,
        XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
        null,
      );
      for (let i = 0; i < nodeSnapshot.snapshotLength; i++) {
        elements.add(nodeSnapshot.snapshotItem(i) as HTMLElement);
      }
    });

    elements.forEach((element) => {
      const newElement = getSWBlockedNode(
        'blockDoodleGames',
        60,
        (element.childNodes[0] as HTMLDivElement).clientWidth,
      );
      newElement.style.marginBottom = '30px';
      element.replaceWith(newElement);
      output.push(true);
    });

    return output;
  },

  /** id:526 - Remove Adverts from Google */
  removeAdsFromGoogle: () => {
    if (
      !(
        window.location.hostname.startsWith('google.') ||
        window.location.hostname.startsWith('www.google.')
      )
    ) {
      return [null];
    }

    const selectors = ['div[aria-label="Ads"]', 'product-viewer-group'];
    const elementData = captureParentNodes(selectors);
    const output = findAndRemoveBySelector(selectors);

    if (output.includes(true)) {
      elementData.forEach((x) =>
        x.parentNode.appendChild(getSWBlockedNode('removeAdsFromGoogle', 60, x.width)),
      );
    }

    return output;
  },

  /** id:579 - Remove YouTube videos from Google Search */
  removeYoutubeVideosFromSearch: () => {
    if (
      !(
        window.location.hostname.startsWith('google.') ||
        window.location.hostname.startsWith('www.google.')
      )
    ) {
      return [null];
    }

    const output: boolean[] = [];

    const selectors = [
      '#search *> [href*="youtube.com/watch"]',
      '#search *> [href*="youtube.com/playlist"]',
    ];

    const elements = new Set<Element>();
    selectors.forEach((selector) => {
      document.querySelectorAll(selector).forEach((element) => {
        // Skip protected UI elements
        if (isProtectedElement(element)) {
          console.debug('Skipping protected UI element in search results:', element);
          return;
        }

        // Check if the element is in a protected container (like a dialog)
        if (element.closest('[role="dialog"]') !== null) {
          console.debug('Skipping element in protected dialog:', element);
          return;
        }

        const searchResultElement = element.closest('.g');
        if (searchResultElement != null) {
          // Double-check that the search result itself is not protected
          if (isProtectedElement(searchResultElement)) {
            console.debug('Skipping protected search result:', searchResultElement);
            return;
          }

          // Also check if the search result is in a protected container
          if (searchResultElement.closest('[role="dialog"]') !== null) {
            console.debug('Skipping search result in protected dialog:', searchResultElement);
            return;
          }

          elements.add(searchResultElement);
        }
      });
    });

    const xPathSelectors = [
      '//div[@id="search"]//div[@role="heading"]//span[text()="Videos"]//ancestor::div[@data-hveid][1]',
    ];

    xPathSelectors.forEach((selector) => {
      const nodeSnapshot = document.evaluate(
        selector,
        document,
        null,
        XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
        null,
      );
      for (let i = 0; i < nodeSnapshot.snapshotLength; i++) {
        const element = nodeSnapshot.snapshotItem(i) as HTMLElement;
        // Skip protected UI elements
        if (isProtectedElement(element)) {
          console.debug('Skipping protected UI element in search results:', element);
          continue;
        }

        // Check if the element is in a protected container
        if (element.closest('[role="dialog"]') !== null) {
          console.debug('Skipping element in protected dialog:', element);
          continue;
        }

        elements.add(element);
      }
    });

    elements.forEach((element) => {
      const newElement = getSWBlockedNode('removeYouTubeVideosFromSearch', 60, element.clientWidth);
      newElement.style.marginBottom = '30px';
      element.replaceWith(newElement);
      output.push(true);
    });

    return output;
  },

  /** id:615 - Vimeo: Force mature content filter */
  forceVimeoMatureContentFilter: () => {
    if (!(window.location.hostname.endsWith('vimeo.com') && window === window.parent)) {
      return [null];
    }

    const output = [
      !document.cookie.includes('content_rating=7')
        ? ((document.cookie = 'content_rating=7;domain=.vimeo.com'), location.reload(), true)
        : false,
    ];

    return output;
  },

  /** id:630 - Google web search: Basic mode */
  googleWebSearchBasicMode: () => {
    if (
      !(
        window.location.hostname.startsWith('google.') ||
        window.location.hostname.startsWith('www.google.')
      )
    ) {
      return [null];
    }

    const selectors = ['.TzHB6b.j8lBAb.p7kDMc.cLjAic:not(.K7khPe)', 'div[role=complementary]'];

    const output = findAndRemoveBySelector(selectors);
    return output;
  },

  /** id:633 - YouTube Disable Mini Player */
  disableYoutubeMiniPlayer: () => {
    if (
      !(
        window.location.hostname.startsWith('youtube.') ||
        window.location.hostname.startsWith('www.youtube.')
      )
    ) {
      return [null];
    }

    const output = [];
    const miniPlayers = document.getElementsByTagName('ytd-miniplayer');

    if (miniPlayers.length !== 0) {
      let removedAny = false;

      [...miniPlayers].forEach((element) => {
        // Skip protected mini players or mini players in protected containers
        if (isProtectedElement(element) || element.closest('[role="dialog"]') !== null) {
          console.debug('Skipping protected mini player:', element);
          return;
        }

        // Use our utility function to safely remove the element
        if (safelyRemoveElement(element)) {
          removedAny = true;
        }
      });

      output.push(removedAny);
    } else {
      output.push(false);
    }

    return output;
  },

  /** id:635 - Youtube: Remove Adverts */
  removeYoutubeAds: () => {
    if (
      !(
        window.location.hostname.startsWith('youtube.') ||
        window.location.hostname.startsWith('www.youtube.')
      )
    ) {
      return [null];
    }

    const returnValues = [];
    const videoClassName = 'video-stream html5-main-video';
    const videos = document.getElementsByClassName(videoClassName);

    for (const video of videos) {
      // Skip videos in protected UI elements
      if (isProtectedElement(video)) {
        console.debug('Skipping YouTube video in protected UI element:', video);
        returnValues.push(false);
        continue;
      }

      // Skip already controlled videos
      if (video.getAttribute('sw-cmod-controlled') !== null) {
        returnValues.push(false);
        continue;
      }

      // Check if the video is in a protected dialog or menu
      const isInProtectedElement =
        video.closest('[role="dialog"]') !== null || video.closest('[role="menu"]') !== null;
      if (isInProtectedElement) {
        console.debug('Skipping YouTube video in protected dialog/menu:', video);
        returnValues.push(false);
        continue;
      }

      const uniqueElementID = String(Date.now());
      video.setAttribute('sw-cmod-controlled', uniqueElementID);

      setInterval(() => {
        // Check again if the video has become part of a protected UI element
        if (isProtectedElement(video)) {
          // If it's now in a protected UI, make sure it's visible and playing normally
          (video as HTMLVideoElement).muted = false;
          (video as HTMLVideoElement).hidden = false;
          (video as HTMLVideoElement).playbackRate = 1;
          // Remove the controlled attribute to stop processing this video
          video.removeAttribute('sw-cmod-controlled');
          return;
        }

        const player = video.closest('.html5-video-player');
        if (player == null) {
          return;
        }

        const adPlaying = player.classList.contains('ad-showing');
        if (adPlaying) {
          if ((video as HTMLVideoElement).hidden) {
            return;
          }

          console.log('Advert detected in: ' + uniqueElementID);
          (video as HTMLVideoElement).muted = true;
          (video as HTMLVideoElement).hidden = true;
          (video as HTMLVideoElement).playbackRate = 5;
        } else {
          if (!(video as HTMLVideoElement).hidden) {
            return;
          }

          console.log('Regular video detected in: ' + uniqueElementID);

          // One possible problem with this content modification is that it
          // will force the video to be un-muted once an Advert stops playing.
          // The following attempts to get the user-set value from the page,
          // but it isn't a perfect solution.
          const volumeButton = player.querySelector('.ytp-volume-panel');
          const volumeValue =
            volumeButton != null ? volumeButton.getAttribute('aria-valuetext') : null;
          const muted = volumeValue !== null ? volumeValue.includes('muted') : false;

          (video as HTMLVideoElement).muted = muted;
          (video as HTMLVideoElement).hidden = false;
          (video as HTMLVideoElement).playbackRate = 1;
        }
      }, 200);

      returnValues.push(true);
    }

    return returnValues;
  },

  /** id:636 - Web search: Remove Lyrics Bing */
  webSearchRemoveLyrics: () => {
    if (
      !(
        window.location.hostname.startsWith('google.') ||
        window.location.hostname.startsWith('www.google.')
      )
    ) {
      return [null];
    }

    const selectors = [
      '.b_heroLyrics',
      'div[data-attrid="kc:/music/recording_cluster:lyrics"]',
      '.cu-container',
    ];
    const elementData = captureParentNodes(selectors);
    const output = findAndRemoveBySelector(selectors);

    if (output.includes(true)) {
      elementData.forEach((x) =>
        x.parentNode.appendChild(getSWBlockedNode('webSearchRemoveLyrics', 60, x.width)),
      );
    }

    return output;
  },
};

/**
 * List of selectors for YouTube UI elements that should never be modified.
 * These are critical UI elements that need to maintain their functionality.
 */
export const youtubeProtectedSelectors = [
  '[role="dialog"][aria-label*="Restricted Mode"]',
  'a[href="#"][role="menuitem"][aria-label*="Restricted Mode"]',
  'paper-toggle-button[aria-label*="Activate Restricted Mode"]',
  'a[href="#"][aria-label="Back"]',
  // Add other menu items that should be protected
  'a[href="#"][role="menuitem"]', // Protect all menu items
  'ytd-toggle-button-renderer', // Protect toggle buttons
  'yt-formatted-string[id="text"]', // Protect text elements in menus
  'tp-yt-paper-item', // Protect paper items (used in dropdowns)
  'ytd-menu-service-item-renderer', // Protect menu service items
];

/**
 * Checks if an element is protected or is within a protected element.
 * Protected elements should not be modified or removed.
 *
 * @param element The element to check
 * @returns True if the element is protected, false otherwise
 */
export const isProtectedElement = (element: Element): boolean => {
  for (const protectedSelector of youtubeProtectedSelectors) {
    if (element.matches(protectedSelector) || element.closest(protectedSelector) !== null) {
      return true;
    }
  }
  return false;
};

/**
 * Safely removes an element if it's not protected.
 *
 * @param element The element to remove
 * @param debug Whether to log debug messages
 * @returns True if the element was removed, false otherwise
 */
export const safelyRemoveElement = (element: Element, debug = true): boolean => {
  if (isProtectedElement(element)) {
    if (debug) {
      console.debug('Skipping protected YouTube UI element:', element);
    }
    return false;
  }

  element.innerHTML = '';
  element.remove();
  return true;
};

/**
 * Removes elements that match the given selectors, but respects protected elements.
 *
 * @param selectors Array of CSS selectors to find elements to remove
 * @returns Array of booleans indicating whether any elements were removed for each selector
 */
const findAndRemoveBySelector = (selectors: string[]): boolean[] => {
  const output: boolean[] = [];

  selectors.forEach((selector) => {
    if (document.querySelector(selector) != null) {
      let removedAny = false;
      document.querySelectorAll(selector).forEach((element) => {
        if (safelyRemoveElement(element)) {
          removedAny = true;
        }
      });
      output.push(removedAny);
    } else {
      output.push(false);
    }
  });

  return output;
};

const captureParentNodes = (selectors: string[]): any[] => {
  const elementData: any[] = [];
  selectors.forEach((selector) => {
    document.querySelectorAll(selector).forEach((x: Element) => {
      const data = {
        parentNode: x.parentNode,
        height: x.clientHeight,
        width: x.clientWidth,
      };
      elementData.push(data);
    });
  });

  return elementData;
};

const getSWBlockedNode = (id: string, height: number, width: number): HTMLDivElement => {
  const outer = document.createElement('div');
  outer.style.width = width < 100 ? '100px' : `${width}px`;
  outer.style.height = height < 50 ? '50px' : `${height}px`;
  outer.style.marginTop = '4px';
  outer.style.border = '1px dashed #B2B2B2';
  outer.style.display = 'flex';
  outer.style.flexDirection = 'column';
  outer.style.justifyContent = 'center';
  outer.style.alignItems = 'center';
  outer.id = id;
  outer.className = 'sw-block-node';

  const inner = document.createElement('span');
  inner.style.color = '#B2B2B2';
  inner.style.fontSize = '14px';
  inner.innerText = 'Blocked by Smoothwall';

  outer.appendChild(inner);

  return outer;
};

export { captureParentNodes, findAndRemoveBySelector, getSWBlockedNode, jsBodyMods };

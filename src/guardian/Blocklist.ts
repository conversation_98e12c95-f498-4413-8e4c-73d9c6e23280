import blocklistDiffFilenames from './constants/blocklistDiffFilenames';
import blocklistFilenames from './constants/blocklistFilenames';
import { BlocklistSource } from 'constants/BlocklistSource';
import UnifiedDiffTarget from './patching/UnifiedDiffTarget';
import CategoryInfo from 'models/CategoryInfo';
import AsyncConcurrencyGuard from 'utilities/AsyncConcurrencyGuard';
import { setTimeoutPromise } from 'utilities/TimeoutPromise';
import BlocklistStatus from '../constants/BlocklistStatus';
import StandaloneEvent from '../utilities/StandaloneEvent';
import IwfList, { IIwfList } from './blocklist-components/IwfList';
import LogLevelService from './blocklist-components/LogLevelService';
import SearchTermCategoriser, {
  ISearchTermCategoriser,
} from './blocklist-components/SearchTermCategoriser';
import UrlListCategoriser, { IUrlListCategoriser } from './blocklist-components/UrlListCategoriser';
import UrlRegexpCategoriser, {
  IUrlRegexpCategoriser,
} from './blocklist-components/UrlRegexpCategoriser';
import UrlRegexpContentMods, {
  IUrlRegexpContentMods,
} from './blocklist-components/UrlRegexpContentMods';
import VideoIdCategoriser, { IVideoIdCategoriser } from './blocklist-components/VideoIdCategoriser';
import WeightedPhraseCategoriser, {
  IWeightedPhraseCategoriser,
} from './blocklist-components/WeightedPhraseCategoriser';
import * as jsonpatch from 'fast-json-patch';
import UnifiedDiffMapBuilder from './patching/UnifiedDiffMapBuilder';

/**
 * Stores an in-memory copy of the blocklist currently being used by this software.
 * It manages loading and patching the data from blocklist files.
 * Downloading and caching are handled separately in BlocklistManager.
 * Stores an in-memory copy of the blocklist currently being used by this software.
 * It manages loading and patching the data from blocklist files.
 * Downloading and caching are handled separately in BlocklistManager.
 */
export default class Blocklist {
  // -----------------------------------------------------------------------------------------------
  // Events.

  /**
   * Fired when the blocklist status changes. Contains the status that the blocklist has changed to.
   *
   * @see status
   */
  public onBlocklistStatusChanged = new StandaloneEvent<[BlocklistStatus]>();

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Gets the current status of the blocklist.
   * Use this to determine when the blocklist is safe to use.
   *
   * @see onBlocklistStatusChanged
   */
  public get status(): BlocklistStatus {
    return this._status;
  }

  /**
   * Gets the epoch number of the currently loaded blocklist.
   * This will return undefined if no blocklist is currently loaded.
   */
  public get epoch(): number | undefined {
    return this._epoch;
  }

  /**
   * Gets the template string used to generate the URL of the currently loaded blocklist.
   * This will return undefined if no blocklist is currently loaded.
   */
  public get templateUrl(): string | undefined {
    return this._templateUrl;
  }

  /**
   * Get a string identifying where the most recently loaded blocklist came from.
   * This will be null if no blocklist has been loaded yet.
   */
  public get blocklistSource(): BlocklistSource | null {
    return this._blocklistSource;
  }

  /**
   * Get the IWF list component of the blocklist.
   * This deliberately provides read-only access to the blocklist component. It should only be used
   *  when the blocklist is ready (i.e. isReady is true).
   */
  public get iwfList(): IIwfList {
    return this._iwfList;
  }

  /**
   * Get the search term categoriser component of the blocklist.
   * This deliberately provides read-only access to the blocklist component. It should only be used
   *  when the blocklist is ready (i.e. isReady is true).
   */
  public get searchTermCategoriser(): ISearchTermCategoriser {
    return this._searchTermCategoriser;
  }

  /**
   * Get the custom search term categoriser component of the blocklist.
   * These search terms are defined by the customer in the portal.
   * This deliberately provides read-only access to the blocklist component. It should only be used
   *  when the blocklist is ready (i.e. isReady is true).
   */
  public get customSearchTermCategoriser(): ISearchTermCategoriser {
    return this._customSearchTermCategoriser;
  }

  /**
   * Get the URL list categoriser component of the blocklist.
   * This deliberately provides read-only access to the blocklist component. It should only be used
   *  when the blocklist is ready (i.e. isReady is true).
   */
  public get urlListCategoriser(): IUrlListCategoriser {
    return this._urlListCategoriser;
  }

  /**
   * Get the URL regexp content mods component of the blocklist.
   * This deliberately provides read-only access to the blocklist component. It should only be used
   *  when the blocklist is ready (i.e. isReady is true).
   */
  public get urlRegexpContentMods(): IUrlRegexpContentMods {
    return this._urlRegexpContentMods;
  }

  /**
   * Get the url regexp categoriser component of the blocklist.
   * This deliberately provides read-only access to the blocklist component. It should only be used
   *  when the blocklist is ready (i.e. isReady is true).
   */
  public get urlRegexpCategoriser(): IUrlRegexpCategoriser {
    return this._urlRegexpCategoriser;
  }

  /**
   * Get the video ID categoriser component of the blocklist.
   * This deliberately provides read-only access to the blocklist component. It should only be used
   *  when the blocklist is ready (i.e. isReady is true).
   */
  public get videoIdCategoriser(): IVideoIdCategoriser {
    return this._videoIdCategoriser;
  }

  /**
   * Get the weighted phrase categoriser component of the blocklist.
   * This deliberately provides read-only access to the blocklist component. It should only be used
   *  when the blocklist is ready (i.e. isReady is true).
   */
  public get weightedPhraseCategoriser(): IWeightedPhraseCategoriser {
    return this._weightedPhraseCategoriser;
  }

  /**
   * Get the negative URL list categoriser (i.e. removetags) component of the blocklist.
   * This deliberately provides read-only access to the blocklist component. It should only be used
   *  when the blocklist is ready (i.e. isReady is true).
   */
  public get negativeUrlListCategoriser(): IUrlListCategoriser {
    return this._negativeUrlListCategoriser;
  }

  /**
   * Get the log level rule service for setting levels on access logs.
   */
  public get logLevelService(): LogLevelService {
    return this._logLevelService;
  }

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Delete all stored blocklist data and set the status back to not loaded.
   * If another asynchronous operation is already in progress then this will wait until it has
   *  finished.
   *
   * @note This does not clear the customer-specific data, such as custom search terms.
   *
   * @warning Do not call this from within one of the asynchronous loading functions. That will
   *  cause a deadlock. Use _clearSync() instead.
   */
  public readonly clear = async (): Promise<void> => {
    await this._asyncConcurrencyGuard.call(this._clearSync);
  };

  /**
   * Parse and store the given blocklist files in a single synchronous operation.
   * This clears any previous blocklist data first.
   *
   * @param files An object containing the blocklist files to be loaded. Each property
   *  represents one file, where the property name matches the filename retrieved from the blocklist
   *  manifest. Each value is the raw contents of the file as a string.
   * @param epoch A Unix timestamp (in seconds) indicating when the blocklist was generated.
   * @param templateUrl The raw string which was used to construct the URL for downloading the
   *  blocklist. It will contain placeholders such as %TYPE% and %NAME%. It's stored so that we can
   *  determine whether it's safe to patch from one blocklist to another.
   *
   * @throws Throws an Error if loading the blocklist fails. Any blocklist data loaded so far will
   *  be discarded before the error is thrown.
   * @throws Throws an Error and does nothing else if an asynchronous loading operation is currently
   *  in progress. Synchronous and asynchronous loading operations should not be mixed as this can
   *  result in an unpredictable combination of data from both operations.
   *
   * @warning This is quite slow for a full blocklist. To avoid blocking the event loop, consider
   *  using the async version instead.
   */
  public readonly loadFromBlocklistFiles = (
    files: Record<string, string>,
    epoch: number,
    templateUrl: string,
    source: BlocklistSource,
  ): void => {
    if (this._status === BlocklistStatus.Loading) {
      // This should only be possible if there's an asynchronous loading operation in progress.
      throw new Error('Loading operation already in progress.');
    }

    try {
      this._clearSync();
      this._changeStatus(BlocklistStatus.Loading);

      Object.entries(files).forEach(([filename, data]) => {
        this._loadFromBlocklistFile(filename, data);
      });

      this._epoch = epoch;
      this._templateUrl = templateUrl;
      this._blocklistSource = source;

      this._changeStatus(BlocklistStatus.Ready);
    } catch (e: any) {
      console.error(`Blocklist - Failed to load epoch ${epoch}.`, e);
      this._clearSync();
      throw e;
    }
  };

  /**
   * Parse and store the given blocklist files asynchronously.
   * Each file is loaded as a synchronous event, but this will yield the event loop between each one
   *  to avoid blocking for too long.
   * If an existing asynchronous loading or patching operation was already in progress, then this
   *  will wait until that has finished before running.
   *
   * @param files An object containing the blocklist files to be loaded. Each property
   *  represents one file, where the property name matches the filename retrieved from the blocklist
   *  manifest. Each value is the raw contents of the file as a string.
   * @param epoch A Unix timestamp (in seconds) indicating when the blocklist was generated.
   * @param templateUrl The raw string which was used to construct the URL for downloading the
   *  blocklist. It will contain placeholders such as %TYPE% and %NAME%. It's stored so that we can
   *  determine whether it's safe to patch from one blocklist to another.
   * @return Returns a promise which resolves when the files have been successfully loaded. If
   *  loading fails then this will clear any data loaded so far, and reject the promise.
   *
   * @warning The caller must be careful not to modify the files object while the asynchronous load
   *  is in progress.
   */
  public readonly loadFromBlocklistFilesAsync = async (
    files: Record<string, string>,
    epoch: number,
    templateUrl: string,
    source: BlocklistSource,
  ): Promise<void> => {
    await this._asyncConcurrencyGuard.call(async () => {
      try {
        this._clearSync();
        this._changeStatus(BlocklistStatus.Loading);

        const durations: Record<string, number> = {};

        // Go through each file.
        for (const [filename, data] of Object.entries(files)) {
          // Wait for a subsequent event loop iteration before loading the file.
          await setTimeoutPromise(2);
          const start = performance.now();
          this._loadFromBlocklistFile(filename, data);
          durations[filename] = performance.now() - start;
        }
        console.debug(`Blocklist - Time taken (in ms) to load each blocklist file: `, durations);

        this._epoch = epoch;
        this._templateUrl = templateUrl;
        this._blocklistSource = source;

        this._changeStatus(BlocklistStatus.Ready);
      } catch (e: any) {
        console.error(`Blocklist - Failed to load epoch ${epoch}.`, e);
        this._clearSync();
        throw e;
      }
    });
  };

  /**
   * Update the blocklist using the given diff files.
   *
   * @param files The blocklist diff files. Each property is one file from the diff download. The
   *  property name is the name of the file, and the value is the contents of the file as a string.
   * @param fromEpoch The Unix timestamp (in seconds) of the old blocklist which is being updated.
   *  This is used to verify that the patch is applicable. It must match the epoch number which is
   *  already stored in this object.
   * @param toEpoch The Unix timestamp (in seconds) of the new blocklist which will result from
   *  applying the patch. It must be greater than fromEpoch.
   * @param templateUrl The raw string which was used to construct the URL for downloading the
   *  original blocklist. It will contain placeholders such as %TYPE% and %NAME%. It's specified
   *  here so that we can verify that the patch is applicable. It must match the template URL of the
   *  blocklist which is already stored in this object.
   * @returns A promise which resolves when patching has been successfully completed. The resolved
   *  value will be a set of the names of all files which were modified in the patch. It will reject
   *  if patching failed for any reason.
   *
   * @throws {Error} There is no blocklist currently loaded. A patch cannot be applied in this case.
   * @throws {Error} The patch is not applicable to the current blocklist, e.g. because the
   *  fromEpoch or templateUrl don't match.
   * @throws {Error} A required diff file is missing or empty.
   */
  public readonly patchFromBlocklistFilesAsync = async (
    files: Record<string, string>,
    fromEpoch: number,
    toEpoch: number,
    templateUrl: string,
  ): Promise<Set<string>> => {
    // Ensure multiple loading operations don't overlap.
    return await this._asyncConcurrencyGuard.call(async (): Promise<Set<string>> => {
      // These checks must be done after the concurrency guard has let us start, otherwise the
      //  blocklist may have been changed separately while we were waiting.
      if (this._status !== BlocklistStatus.Ready) {
        throw new Error('The blocklist cannot be patched as no blocklist is currently loaded.');
      }

      if (fromEpoch !== this._epoch) {
        throw new Error('Specified patch applies to a different blocklist epoch.');
      }

      if (templateUrl !== this._templateUrl) {
        throw new Error('Specified patch applies to a different blocklist type.');
      }

      try {
        this._changeStatus(BlocklistStatus.Loading);
        const filenames = new Set([
          ...(await this._applyUnifiedDiff(files)),
          ...(await this._applyJsonDiffs(files)),
          ...this._applyReplacements(files),
        ]);
        this._epoch = toEpoch;
        this._blocklistSource = 'patch';
        this._changeStatus(BlocklistStatus.Ready);
        return filenames;
      } catch (e: any) {
        // Important: If patching failed part way through then it could leave the blocklist in an
        //  invalid semi-loaded state. Ensure we clear the data so it doesn't get used accidentally.
        this._clearSync();
        throw e;
      }
    });
  };

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Parse and store one blocklist file, overwriting previous data stored for the same file.
   * This loads the entire file in a single synchronous operation.
   *
   * @param filename The name of the file being loaded. This determines the format of the data, and
   *  how it gets handled.
   * @param data The contents of the file being loaded. The format depends on the filename.
   */
  private readonly _loadFromBlocklistFile = (filename: string, data: string): void => {
    const lowerCaseFilename = filename.toLowerCase();

    // The main URL list is split into multiple files, based on top-level domain.
    // E.g. "domainsurls.com", "domainsurls.uk".
    if (lowerCaseFilename.startsWith('domainsurls')) {
      // Extract the top-level domain. If it's "other" then treat it as not specified.
      let topLevelDomain = lowerCaseFilename.split('.')[1] ?? '';
      if (topLevelDomain === 'other') {
        topLevelDomain = '';
      }
      this._urlListCategoriser.loadFromBlocklistFile(data, topLevelDomain);
      return;
    }

    // All of the other files have fixed names.
    switch (lowerCaseFilename) {
      case 'category_data':
        this.categoryData = JSON.parse(data);
        return;

      case 'iwflist':
        this._iwfList.loadFromBlocklistFile(data);
        return;

      case 'jsregexpbody_cmod':
        // Remote javascript execution is not allowed in MV3. So these content mods are no longer supported.
        return;

      case 'regexpurls':
        this._urlRegexpCategoriser.loadFromBlocklistFile(data);
        return;

      case 'regexpurls_cmod':
        this._urlRegexpContentMods.loadFromBlocklistFile(data);
        return;
      case 'removetags':
        this._negativeUrlListCategoriser.loadFromBlocklistFile(data);
        return;

      case 'searchengineregexplist':
        this._searchTermCategoriser.loadExtractionPatternsFromBlocklistFile(data);
        // Copy the regular expressions to the custom search term categoriser.
        this._customSearchTermCategoriser.setExtractionPatterns(
          this._searchTermCategoriser.getExtractionPatterns(),
        );
        return;

      case 'searchterms':
        this._searchTermCategoriser.loadSearchTermsFromBlocklistFile(data);
        return;

      case 'videoidregexplist':
        this._videoIdCategoriser.loadExtractionPatternsFromBlocklistFile(data);
        return;

      case 'videoids':
        this._videoIdCategoriser.loadCategorisationMapFromBlocklistFile(data);
        return;

      case 'weightedphrases':
        this._weightedPhraseCategoriser.loadFromBlocklistFile(data);
        return;

      case 'loglevelrules':
        this._logLevelService.loadFromBlocklistFile(data);
        return;

      default:
        console.warn(`Blocklist filename not recognised: ${filename}`);
    }
  };

  /**
   * Set the internal status and trigger a status change event.
   * This will always trigger the event, even if the status has actually stayed the same.
   *
   * @param newStatus The new status to store.
   */
  private readonly _changeStatus = (newStatus: BlocklistStatus): void => {
    this._status = newStatus;
    this.onBlocklistStatusChanged.dispatch(newStatus);
  };

  /**
   * Clear the blocklist data as a single synchronous operation.
   * It's safe to call this from within one of the guarded asynchronous loading operations.
   *
   * @note This won't clear the customer-specific data, such as custom search terms.
   */
  private readonly _clearSync = (): void => {
    this._epoch = undefined;
    this._templateUrl = undefined;
    this._blocklistSource = null;

    this._iwfList.clear();
    this._searchTermCategoriser.clear();
    this._urlListCategoriser.clear();
    this._urlRegexpCategoriser.clearCategorisationMap();
    this._urlRegexpContentMods.clearContentMods();
    this._videoIdCategoriser.clear();
    this._weightedPhraseCategoriser.clear();
    this._negativeUrlListCategoriser.clear();
    this._logLevelService.clear();
    this.categoryData = {};

    this._changeStatus(BlocklistStatus.NotLoaded);
  };

  /**
   * Get a named blocklist component which is to be patched by Unified Diff.
   *
   * @param filename The name of the blocklist file to get.
   * @returns A reference to the blocklist component which handles the named blocklist file, or null
   *  if the file can be ignored.
   */
  private readonly _getUnifiedDiffTarget = (filename: string): UnifiedDiffTarget | null => {
    filename = filename.toLowerCase();

    if (filename.startsWith(blocklistFilenames.urlListPrefix)) {
      const tld = Blocklist.extractTopLevelDomain(filename);
      const target = this._urlListCategoriser.getCategorisationMap(tld);
      if (target === undefined) {
        console.warn(
          `Blocklist - Cannot patch blocklist file "${filename}". ` +
            'No data for that top-level domain has been loaded.',
        );
        return null;
      }
      return target;
    }

    switch (filename) {
      case blocklistFilenames.negativeUrlList: {
        // The negative URL list uses the URL categoriser class, even though it doesn't split the
        //  data into top-level domains. It only populates the non-TLD map.
        const target = this._negativeUrlListCategoriser.getCategorisationMap('');
        if (target === undefined) {
          console.warn(
            `Blocklist - Cannot patch blocklist file "${filename}". ` +
              'No data for that file has been loaded.',
          );
          return null;
        }
        return target;
      }

      case blocklistFilenames.urlMods:
        return this._urlRegexpContentMods;

      case blocklistFilenames.urlPatterns:
        return this._urlRegexpCategoriser;

      case blocklistFilenames.searchTermExtractionPatterns:
        // The search term categoriser is responsible for two blocklist files: the search engine
        //  extraction patterns and the search terms. Only the extraction patterns are patched by
        //  Unified Diff though. The search terms are patched by JSON diff.
        // Note: The custom search term categoriser currently gets its own copy of the same
        //  extraction patterns. The patch function copies them across separately.
        return this._searchTermCategoriser;

      case blocklistFilenames.videoIdExtractionPatterns:
        // The VideoIdCategoriser class patches the extraction patterns itself.
        return this._videoIdCategoriser;

      case blocklistFilenames.videoIds:
        // The CategorisationMap inside VideoIdCategoriser patches the actual video IDs.
        return this._videoIdCategoriser.getCategorisationMap();

      case blocklistFilenames.jsContentMods:
        // We can safely ignore this file. JavaScript content mods are not possible in a manifest v3
        //  extension so they have been hard-coded instead.
        return null;
    }

    return null;
  };

  /**
   * Extract, parse, and apply the Unified Diff from a set of blocklist diff files.
   * The changes are applied to the blocklist data which is currently in memory.
   * This will periodically yield to the event loop.
   *
   * @param files All the files in the blocklist diff.
   * @returns A promise which resolves when the diff has been successfully applied. The resolved
   *  value is a set of the names of all blocklist files which were modified. The promise will
   *  reject with an error if the Unified Diff file was missing, invalid, or failed to apply.
   */
  private readonly _applyUnifiedDiff = async (
    files: Record<string, string>,
  ): Promise<Set<string>> => {
    // The Unified Diff file must exist and must not be empty.
    const unifiedDiff = files[blocklistDiffFilenames.unifiedDiff];
    if (unifiedDiff === undefined || unifiedDiff === '') {
      throw new Error(
        `Unified Diff file is missing or empty: ${blocklistDiffFilenames.unifiedDiff}.`,
      );
    }

    const filenames = new Set<string>();

    // We'll measure the total time taken to parse and apply the diff for each file.
    let currentFileStartedAt = performance.now();

    // Go through each target file in the Unified Diff and build a map of the changes required.
    for (const diffMap of UnifiedDiffMapBuilder.buildFromFile(unifiedDiff)) {
      const filename = Blocklist.extractFilename(diffMap.oldFilePath);
      const target = this._getUnifiedDiffTarget(filename);
      if (target == null) {
        console.debug(`Blocklist - Ignoring Unified Diff for "${filename}"`);
        continue;
      }

      target.patchFromUnifiedDiff(diffMap);
      const elapsed = (performance.now() - currentFileStartedAt).toFixed(2);
      console.debug(`Blocklist - Patching "${filename}" by Unified Diff took ${elapsed} ms`);

      filenames.add(filename);

      // Yield to the event loop between each Unified Diff. This is to reduce how long we stall
      //  the browser if patch operations are going slow.
      await setTimeoutPromise(3);

      // Start timing the next file after we resume from the yield.
      currentFileStartedAt = performance.now();
    }

    // The custom search term categoriser uses a copy of the extraction patterns from the main
    //  blocklist. They need to be copied across explicitly for now.
    // TODO: Split the extraction pattern logic out into a shared component.
    // TODO: Move custom categories (URLs, search terms, etc)out of the Blocklist class
    this._customSearchTermCategoriser.setExtractionPatterns(
      this._searchTermCategoriser.getExtractionPatterns(),
    );

    return filenames;
  };

  /**
   * Extract, parse, and apply the JSON diffs from a set of blocklist diff files.
   * This will periodically yield to the event loop.
   *
   * @param files All the files in the blocklist diff.
   * @returns A promise which resolves when the diff has been successfully applied. The resolved
   *  value is a set of the names of all blocklist files which were modified. The promise will
   *  reject with an error if the JSON diff file was missing, invalid, or failed to apply.
   */
  private readonly _applyJsonDiffs = async (
    files: Record<string, string>,
  ): Promise<Set<string>> => {
    // The JSON diff file must exist and must contain an object at the top-level.
    const jsonDiffFile = files[blocklistDiffFilenames.jsonDiff];
    if (jsonDiffFile === undefined || jsonDiffFile === '') {
      throw new Error(`JSON diff file is missing or empty: ${blocklistDiffFilenames.jsonDiff}.`);
    }
    let jsonDiff: any;
    try {
      jsonDiff = JSON.parse(jsonDiffFile);
    } catch (e: any) {
      console.error(
        `Blocklist - Failed to parse JSON diff file "${blocklistDiffFilenames.jsonDiff}": `,
        e,
      );
      throw new Error('Failed to parse JSON diff file.');
    }
    if (typeof jsonDiff !== 'object' || jsonDiff === null) {
      throw new Error('Expected JSON diff file to contain an object at the top level.');
    }

    const filenames = new Set<string>();

    // The JSON diff file contains an object where each property relates to a single blocklist
    //  file. The property name is the blocklist filename, and the value is the JSON diff for
    //  that file.
    for (const filename of Object.keys(jsonDiff)) {
      // Don't try to apply a diff which does nothing. This avoids any unnecessary overhead from
      //  reloading or re-storing data which hasn't changed.
      const jsonDiffEntry = jsonDiff[filename];
      if (jsonDiffEntry == null || (Array.isArray(jsonDiffEntry) && jsonDiffEntry.length === 0)) {
        console.debug(`Blocklist - Skipping empty JSON diff for "${filename}"`);
        continue;
      }

      try {
        const start = performance.now();
        if (this._applyJsonDiffToBlocklistFile(filename, jsonDiffEntry)) {
          filenames.add(filename);
          const elapsed = (performance.now() - start).toFixed(2);
          console.debug(`Blocklist - Patching "${filename}" by JSON diff took ${elapsed} ms`);
        } else {
          console.debug(`Blocklist - Ignoring JSON diff for "${filename}"`);
        }
      } catch (e: any) {
        console.error(`Blocklist - Failed to apply JSON diff for "${filename}": `, e);
        throw new Error('Failed to apply JSON diff.');
      }

      // Yield to the event loop between each JSON diff. This is to reduce how long we stall the
      //  browser if patch operations are going slow.
      await setTimeoutPromise(3);
    }

    return filenames;
  };

  /**
   * Use a single JSON diff to patch the named blocklist component.
   *
   * @param filename The name of the blocklist file whose component is to be patched.
   * @param jsonDiffEntry The JSON diff data which should be applied to the specified blocklist
   *  file. This is a single JSON diff. Note that the JSON diff file in the blocklist diff actually
   *  contains multiple JSON diffs.
   *
   * @todo Implement a JsonDiffTarget interface which will be implemented by each component class
   *  which needs to be patched from the JSON diff. That will probably need to wait until the search
   *  term categoriser has been refactored into two separate classes, and a wrapper class has been
   *  implemented for the category data.
   */
  private readonly _applyJsonDiffToBlocklistFile = (
    filename: string,
    jsonDiffEntry: any,
  ): boolean => {
    switch (filename.toLowerCase()) {
      case blocklistFilenames.categoryData:
        // The category data doesn't currently have an object to wrap it. It's just stored as a
        //  plain object.
        this.categoryData = jsonpatch.applyPatch(this.categoryData, jsonDiffEntry).newDocument;
        return true;

      case blocklistFilenames.weightedPhrases:
        this._weightedPhraseCategoriser.patchFromBlocklistFile(jsonDiffEntry);
        return true;

      case blocklistFilenames.searchTerms:
        this._searchTermCategoriser.patchSearchTermsFromBlocklistFile(jsonDiffEntry);
        return true;

      case blocklistFilenames.logLevelRules:
        this._logLevelService.patchFromBlocklistFile(jsonDiffEntry);
        return true;
    }

    return false;
  };

  /**
   * Extract, parse, and apply the replacement files from a set of blocklist diff files.
   * A replacement file is one which isn't selectively updated when it changes. Instead, the entire
   *  file is replaced.
   *
   * @param files All the files in the blocklist diff.
   * @returns A set of the names of all blocklist files which were replaced.
   */
  private readonly _applyReplacements = (files: Record<string, string>): Set<string> => {
    // The IWF list file must not be empty. However, it may be missing if it has not changed.
    const iwfList = files[blocklistDiffFilenames.iwfList];
    if (iwfList === '') {
      throw new Error(`Expected file is empty: ${blocklistDiffFilenames.iwfList}`);
    }
    if (iwfList === undefined) {
      console.warn(
        'Blocklist - IWF list file not found in the blocklist diff. ' +
          'Patching will continue on the assumption that it has not changed.',
      );
    }

    const filenames = new Set<string>();

    // The IWF list file isn't patched. It's replaced in its entirety when it changes.
    if (iwfList !== undefined) {
      const start = performance.now();
      this._iwfList.loadFromBlocklistFile(iwfList);
      const elapsed = (performance.now() - start).toFixed(2);
      console.debug(`Blocklist - Replacing "${blocklistFilenames.iwfList}" took ${elapsed} ms`);
      filenames.add(blocklistFilenames.iwfList);
    }

    return filenames;
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Get the top-level domain specified in a blocklist filename, if applicable.
   * This is relevant for the domains/URLs categorisation lists where some domains are split into
   *  separate TLD-specific files. For example: "domainsurls.com" contains ".com" URLs.
   * This assumes everything after the first dot is the TLD.
   * The TLD is not validated.
   *
   * @param filename The filename from which to extract the top-level domain.
   * @returns The lower-cased top-level domain specified in the filename. As a special case, if the
   *  top-level domain is "other", then an empty string will be returned instead. If no top-level
   *  domain is present in the filename, then an empty string is returned.
   *
   * @note The special case for the "other" TLD is related to the way domains and URL list files are
   *  specified in the blocklist. URLs with a few of the most common TLDs are put into TLD-specific
   *  files. For example, ".com" URLs are in the "domainsurls.com" file. To save space, the TLD is
   *  omitted from entries in the TLD-specific files. Any URL which does not have one of those
   *  common TLDs goes into a generic file called "domainsurls.other", and the TLD is _not_ removed
   *  from those entries, for obvious reasons. When loaded into this class, an empty string is used
   *  in place of "other". This is to make it clear that it's not a TLD, and therefore that no TLD
   *  has been removed from the associated entries.
   */
  public static readonly extractTopLevelDomain = (filename: string): string => {
    const index = filename.indexOf('.');
    if (index < 0) {
      return '';
    }
    const tld = filename.substring(index + 1).toLowerCase();
    if (tld === 'other') {
      return '';
    }
    return tld;
  };

  /**
   * Get just the filename from a path, without any parent directories.
   *
   * @param path The path to extract the filename from.
   * @returns The filename extracted from the path. Returns an empty string if the path is empty.
   */
  public static extractFilename(path: string): string {
    const lastSeparatorAt = Math.max(path.lastIndexOf('/'), path.lastIndexOf('\\'));
    return path.trim().substring(lastSeparatorAt + 1);
  }

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * This is used to prevent multiple asynchronous operations from modifying data at the same time.
   */
  private readonly _asyncConcurrencyGuard = new AsyncConcurrencyGuard();

  /**
   * The current status of the blocklist.
   * This is used to determine if it's currently available for use.
   */
  private _status: BlocklistStatus = BlocklistStatus.NotLoaded;

  /**
   * The epoch for the currently loaded blocklist.
   * This is the unix timestamp (in seconds) of when it was generated on the server.
   * This will be undefined if no blocklist is currently loaded.
   */
  private _epoch?: number;

  /**
   * The original template string which was used to generate the URL of the current blocklist.
   * It comes from product config, and contains placeholders such as %TYPE% and %NAME%.
   * This is stored so that we can detect whether we've been instructed to download a completely
   *  different type of blocklist, in which case patching won't work.
   * The will be undefined if no blocklist is currently loaded.
   */
  private _templateUrl?: string;

  /**
   * Checks if a URL appears on the IWF child abuse list.
   */
  private readonly _iwfList = new IwfList();

  /**
   * Categorises search engine URLs based on a list of known search terms.
   */
  private readonly _searchTermCategoriser = new SearchTermCategoriser();

  /**
   * Categorises search engine URLs based on a customer defined list of search terms.
   *
   * @note The custom search terms are loaded from customer policy, but the search term extraction
   *  regular expressions come from the blocklist.
   * @todo We should probably move this somewhere else to avoid confusion.
   */
  private readonly _customSearchTermCategoriser = new SearchTermCategoriser();

  /**
   * Categorises URLs based on a list of known domains and URLs.
   */
  private readonly _urlListCategoriser = new UrlListCategoriser();

  /**
   * Categorises URLs based on a list regexp from the blocklist.
   */
  private readonly _urlRegexpCategoriser = new UrlRegexpCategoriser();

  /**
   * Stores and executes the url regexp content mods.
   */
  private readonly _urlRegexpContentMods = new UrlRegexpContentMods();

  /**
   * Categorises video URLs based on a list of known video IDs.
   */
  private readonly _videoIdCategoriser = new VideoIdCategoriser();

  /**
   * Categorises page content based on a list of known phrases.
   */
  private readonly _weightedPhraseCategoriser = new WeightedPhraseCategoriser();

  /**
   * Specifies the categories which certain URLs definitely should not match, based on a known list.
   * This is used to prevent false-positives during weighted phrase analysis.
   * For example, "bbc.co.uk" shouldn't match a gambling category, even if the content is a news
   *  article which mentions gambling several times.
   *
   * In the blocklist, this is known as "removetags".
   */
  private readonly _negativeUrlListCategoriser = new UrlListCategoriser();

  /**
   * Categorises logs according to log level based on how interesting they are.
   *
   * @todo Rename this class/object. It's a blocklist component, not a service. :)
   */
  private readonly _logLevelService = new LogLevelService();

  /**
   * Used to map category IDs to category names.
   */
  public categoryData: Record<string, CategoryInfo> = {};

  /**
   * The names of files we typically expect to see in a full blocklist download.
   * This is only used to output a warning to the console if a file is missing.
   */
  public static readonly expectedFullBlocklistFiles = [
    blocklistFilenames.categoryData,
    blocklistFilenames.iwfList,
    blocklistFilenames.logLevelRules,
    blocklistFilenames.negativeUrlList,
    blocklistFilenames.searchTermExtractionPatterns,
    blocklistFilenames.searchTerms,
    blocklistFilenames.urlListPrefix + '.com',
    blocklistFilenames.urlListPrefix + '.net',
    blocklistFilenames.urlListPrefix + '.org',
    blocklistFilenames.urlListPrefix + '.other',
    blocklistFilenames.urlListPrefix + '.uk',
    blocklistFilenames.urlMods,
    blocklistFilenames.urlPatterns,
    blocklistFilenames.videoIdExtractionPatterns,
    blocklistFilenames.videoIds,
    blocklistFilenames.weightedPhrases,
  ];

  /**
   * The names of the files we typically expect to see in a blocklist patch download.
   * This is only used to output a warning to the console if a file is missing.
   */
  public static readonly expectedPatchBlocklistFiles = [
    blocklistDiffFilenames.iwfList,
    blocklistDiffFilenames.jsonDiff,
    blocklistDiffFilenames.unifiedDiff,
  ];

  /*
   * Where the blocklist has been loaded from, options provided via the BlocklistSource type. Required in the cldflt-start-up-duration metric.
   * Will be null on startup before the blocklist is loaded.
   */
  private _blocklistSource: BlocklistSource | null = null;
}

import FilterMode from 'constants/FilterMode';
import { IPolicyConfig } from 'models/PolicyConfigModels';
import ProvisioningInfo from 'provisioning/ProvisioningInfo';
import IpService from 'services/IpService';

import BlocklistStatus from '../constants/BlocklistStatus';
import Blocklist from './Blocklist';
import FilterDecision from './models/FilterDecision';
import PolicyMatcher from './PolicyMatcher';

/**
 * Contains the result of a complete url categorisation.
 */
export interface UrlCategorisationInfo {
  categoryIds: Set<string>;
  searchTerms: string;
  videoIds: string;
}

/**
 * Manages URL and content filtering decisions at a high level.
 */
export default class Guardian {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Optionally initialise the Guardian to work with the specified blocklist.
   * The blocklist doesn't need to be loaded or ready before calling this.
   * If no blocklist is specified then a generic one will be created.
   * It cannot be replaced after construction.
   *
   * @param filterMode The filter mode for the class. This should be either mini or full.
   */
  public constructor(filterMode: FilterMode, ipService?: IpService, blocklist?: Blocklist) {
    if (filterMode === FilterMode.full && ipService === undefined) {
      throw new Error(
        'Could not create a new guardian instance. The filter mode is full but no ip service was given.',
      );
    }

    this._filterMode = filterMode;
    this._ipService = ipService;
    this.blocklist = blocklist ?? new Blocklist();

    if (this._ipService !== undefined) {
      this._policyMatcher = new PolicyMatcher(this._ipService);
    }
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Check if Guardian and its underlying blocklist are ready to provide filtering functionality.
   *
   * @note Guardian allows everything through the filter if it isn't ready yet (i.e. it implements a
   *  "fail open" strategy).
   */
  public get isReady(): boolean {
    const blocklistIsReady = this.blocklist.status === BlocklistStatus.Ready;

    if (this._filterMode === FilterMode.full) {
      return blocklistIsReady && this._policyConfig !== undefined;
    }

    // Mini filter doesn't have/need a policy config.
    return blocklistIsReady;
  }

  /**
   * Sets the policy config that will be used to make a filtering decision.
   *
   * @usage This needs to be called before checking a url.
   * @param policyConfig The policy config.
   */
  public setPolicyConfig = (policyConfig: IPolicyConfig): void => {
    this._policyConfig = policyConfig;
  };

  /**
   * Gets the policy config if it exists.
   */
  public get policyConfig(): IPolicyConfig | undefined {
    return this._policyConfig;
  }

  /**
   * Gets the provisioning info.
   */
  public get provisioningInfo(): ProvisioningInfo | undefined {
    return this._provisioningInfo;
  }

  /**
   * Sets the provisioning info and updates any components that need it.
   *
   * @note Setting the info to undefined will not do anything.
   * The undefined type is required by typescript to match the getter.
   */
  public set provisioningInfo(info: ProvisioningInfo | undefined) {
    if (info === undefined) {
      return;
    }

    this._provisioningInfo = info;
    if (this._policyMatcher !== undefined) {
      this._policyMatcher.currentUsername = info.user;
    }
  }

  // -----------------------------------------------------------------------------------------------
  // Filtering operations.

  /**
   * Analyse a URL and optionally page content to decide whether it should be allowed, modified, or blocked.
   *
   * @param url The URL to analyse.
   * @param content The content of the page to analyse. This may be undefined or truncated.
   * @param filteringDisabled Indicates if filtering is currently disabled. If true then only IWF fitering will be applied.
   * @param mainFrameUrl An optional url that will be analysed for video ids to enable background requests on video sites.
   * @returns Returns an object describing the filter decision, including whether or not the URL
   *  should be allowed.
   *
   * @warning This may allow everything if the underlying blocklist isn't ready yet. It is the
   *  caller's responsibility to check `isReady` if necessary before calling this.
   */
  public readonly analyse = (
    url: URL,
    filteringDisabled: boolean,
    mainFrameUrl?: URL,
    content?: string,
  ): FilterDecision => {
    // Before analysing anything else, check that it isn't a child abuse page.
    // We deliberately don't log these requests so that we don't keep copies of child abuse URLs.
    if (url.toString().startsWith('http') && this.blocklist.iwfList.contains(url)) {
      return FilterDecision.blockWithoutLogging(true);
    }

    // Don't continue filtering if the filter isn't ready or if filtering should be disabled.
    if (!this.isReady || filteringDisabled) {
      return FilterDecision.allowWithoutLogging();
    }

    const urlCategorisationInfo = this.categoriseUrl(url, mainFrameUrl);
    const contentCategorisationIds = this.categoriseContent(url, content ?? '');

    const categoryIds = new Set<string>([
      ...urlCategorisationInfo.categoryIds,
      ...contentCategorisationIds,
    ]);

    if (this._filterMode === FilterMode.mini) {
      const decision = new FilterDecision();
      decision.videoIds = urlCategorisationInfo.videoIds;
      decision.searchTerms = urlCategorisationInfo.searchTerms;
      decision.categoryIds = categoryIds;

      // The mini-filter only contains categories which are commonly blocked, so there's no need to
      //  check the categories against policies. If the URL has matched any category, then block it.
      decision.allow = decision.categoryIds.size === 0;
      return decision;
    }

    if (this._policyMatcher === undefined || this._policyConfig === undefined) {
      return FilterDecision.allowWithoutLogging();
    }

    const decision = this._policyMatcher.matchPolicies(this._policyConfig, categoryIds);
    decision.videoIds = urlCategorisationInfo.videoIds;
    decision.searchTerms = urlCategorisationInfo.searchTerms;

    if (decision.allow) {
      if (content !== undefined) {
        // Apply content mods if we have content.
        decision.contentModIds = this._matchContentMods(categoryIds);
      } else {
        // Apply URL mods if we lack content. URL mods can't be applied during content analysis.
        const urlContentMods = this._policyMatcher.matchUrlModPolicies(
          this._policyConfig,
          decision.categoryIds,
        );

        const newUrl = this.blocklist.urlRegexpContentMods.executeContentMods(
          url.toString(),
          urlContentMods,
        );

        if (newUrl !== undefined) {
          try {
            decision.modifiedUrl = new URL(newUrl);
            if (newUrl?.length === 0) throw new Error('Modified URL is empty string');
          } catch (error) {
            console.debug('Modified URL is invalid', error);
            decision.allow = false;
          }
        }
      }
    }

    return decision;
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Analyse a URL to determine which categories it matches.
   *
   * @param url The URL to analyse.
   * @param mainFrameUrl An optional url that will be analysed for video ids to enable background requests on video sites.
   * @returns Returns a set of the IDs of all categories which the URL matches.
   *
   * @warning This may return an empty set if the underlying blocklist isn't ready yet. It is the
   *  caller's responsibility to check `isReady` if necessary before calling this.
   */
  public readonly categoriseUrl = (url: URL, mainFrameUrl?: URL): UrlCategorisationInfo => {
    let deepUrlCategories = new Set<string>();
    const deepUrlResult = this.useDeepUrlAnalysis(url);
    if (deepUrlResult !== undefined) {
      deepUrlCategories = this.categoriseUrl(deepUrlResult).categoryIds;
    }

    const urlListResult = this.blocklist.urlListCategoriser.categoriseUrl(
      url,
      this._policyConfig?.custom_categories,
    );

    const searchTermResult = this.blocklist.searchTermCategoriser.categoriseUrl(url);
    const customSearchTermResult = this.blocklist.customSearchTermCategoriser.categoriseUrl(url);
    const urlRegexpResult = this.blocklist.urlRegexpCategoriser.categoriseUrl(url);
    const videoIdResult = this.blocklist.videoIdCategoriser.categoriseUrl(
      url,
      this._policyConfig?.custom_categories,
    );

    if (mainFrameUrl !== undefined) {
      const mainFrameVideoIds = this.blocklist.videoIdCategoriser.categoriseUrl(
        mainFrameUrl,
        this._policyConfig?.custom_categories,
      );

      videoIdResult.categoryIds = new Set([
        ...videoIdResult.categoryIds,
        ...mainFrameVideoIds.categoryIds,
      ]);
    }

    return {
      categoryIds: new Set<string>([
        ...deepUrlCategories,
        ...urlListResult.categoryIds,
        ...searchTermResult.categories,
        ...customSearchTermResult.categories,
        ...urlRegexpResult.categoryIds,
        ...videoIdResult.categoryIds,
      ]),
      searchTerms: searchTermResult.searchString,
      videoIds: [...videoIdResult.videoIds].filter((id) => id.trim() !== '').join(', '),
    };
  };

  /**
   * Analyse page content to determine which categories it matches.
   *
   * @param url The URL of the page. This is not used to match categories. It is only used to
   *  remove categories which clearly do not apply to certain URLs, e.g. an NHS webpage may contain
   *  references to sex, but it isn't pornography.
   * @param content The content of the page to analyse.
   * @returns Returns a set of the IDs of all categories which the content matches.
   *
   * @warning This may return an empty set if the underlying blocklist isn't ready yet. It is the
   *  caller's responsibility to check `isReady` if necessary before calling this.
   */
  public readonly categoriseContent = (url: URL, content: string): Set<string> => {
    // Weighted phrase analysis can find categories with varying degrees of confidence.
    // We only care about categories which had a total final score above the match threshold (100%).
    const weightedPhraseResult =
      this.blocklist.weightedPhraseCategoriser.categoriseContent(content);
    const categories = weightedPhraseResult.categoryIds;

    // Use a list of known safe URLs to eliminate false-positives.
    const ignoreCategories = this.blocklist.negativeUrlListCategoriser.categoriseUrl(url);
    ignoreCategories.categoryIds.forEach((categoryId: string): void => {
      categories.delete(categoryId);
    });

    return categories;
  };

  /**
   * Checks if the url matches a deep url regexp.
   * If it does it will try and extract the deep url and return it for analysis.
   * If the regexp doesn't match or there isn't a url it will return undefined.
   *
   * Example deep url: https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS318hSHkn06MD9dYQedaKxJqWaBFiCjpxWilbSIC1ifjfUxMhB_g:https://cdn.cnn.com/cnnnext/dam/assets/190811182740-rashford-united-chelsea-large-tease.jpg
   *
   * @param url The url to check for a deep url.
   * @returns The deep url for analysis or undefined if one isn't found.
   */
  public readonly useDeepUrlAnalysis = (url: URL): URL | undefined => {
    const encryptedRegexp = /(https?:\/\/)?encrypted-tbn\d\.gstatic\.com\/images[\w?=:&-]+:http/;
    const webcacheRegexp = /(https?:\/\/)?webcache\.googleusercontent\.com\/search[\w?=:&-]+:http/;

    const urlString = url.toString();

    if (!encryptedRegexp.test(urlString) && !webcacheRegexp.test(urlString)) {
      return undefined;
    }

    try {
      const newUrlArray = urlString.split(':');

      // We should always have something here, as https:// has a colon in it
      if (
        newUrlArray.length > 2 &&
        newUrlArray[newUrlArray.length - 2].length > 0 &&
        newUrlArray[newUrlArray.length - 1].length > 0
      ) {
        const newDeepUrl = `${newUrlArray[newUrlArray.length - 2]}:${
          newUrlArray[newUrlArray.length - 1]
        }`;

        // The last element will be the deep url
        if (newDeepUrl.length > 0 && newDeepUrl.startsWith('http')) {
          return new URL(newDeepUrl);
        }
      }
    } catch (e) {
      console.error(`Failed to run deep url analysis on ${urlString}`, e);
      return undefined;
    }
    return undefined;
  };

  /**
   * Matches any content mods in the full filter policy and returns their blockman ids.
   * @param categoryIds The category ids that have been matched.
   * @returns A promise that resolves to a list of blockman ids.
   */
  private readonly _matchContentMods = (categoryIds: Set<string>): string[] => {
    if (
      this._policyConfig === undefined ||
      this._ipService === undefined ||
      this._policyMatcher === undefined
    ) {
      return [];
    }

    return this._policyMatcher.matchContentModPolicies(this._policyConfig, categoryIds);
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Manages all of the data used to provide Guardian functionality.
   */
  public readonly blocklist: Blocklist;

  /**
   * The policy config to use when making a decision.
   */
  private _policyConfig?: IPolicyConfig;

  /**
   * Handles matching the policies.
   */
  private readonly _policyMatcher?: PolicyMatcher;

  /**
   * Manages getting the device's ip addresses.
   */
  private readonly _ipService?: IpService;

  /**
   * Gets the filter mode for the guardian class.
   */
  private readonly _filterMode: FilterMode;

  /**
   * Used to access the localGroups for policy matching.
   */
  private _provisioningInfo?: ProvisioningInfo;
}

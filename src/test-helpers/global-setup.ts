/*
This file defines a function which performs one-time setup when the unit test framework starts.
We use it to start a static file server for tests which need to download blocklist files etc.

@note The server will not respond while Jest fake timers are enabled.
*/

import express from 'express';
import * as path from 'path';

// If you change the port number here, ensure you also change it in jest.config.ts
const port = 3000;

export default async (): Promise<void> => {
  const app = express();
  app.use(express.static(path.join(__dirname, '..', '..', 'test-data')));
  await new Promise<void>((resolve, reject) => {
    // @ts-expect-error Type library doesn't know about error argument in callback.
    const server = app.listen(port, (err: unknown) => {
      if (err != null) {
        reject(err);
      } else {
        resolve();
      }
    });

    // Make the server available to the teardown function so that it can be stopped.
    (globalThis as any).testServer = server;
  });
};

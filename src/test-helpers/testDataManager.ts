import * as path from 'path';
import { loadLocalBlocklist } from './blocklist-utilities';

const testDataFolder = path.join(__dirname, '..', '..', 'test-data');
const blocklistsFolder = path.join(testDataFolder, 'blocklists');
const invalidBlocklistsFolder = path.join(testDataFolder, 'invalid-blocklists');

interface BlocklistData {
  epoch: number;
  files: Record<string, string>;
}

interface DiffData {
  fromEpoch: number;
  toEpoch: number;
  files: Record<string, string>;
}

interface BlocklistWithDiff {
  from: BlocklistData;
  to: BlocklistData;
  diff: DiffData;
}

const loadBlocklistWithDiff = (
  parentFolder: string,
  fromEpoch: number,
  toEpoch: number,
): BlocklistWithDiff => {
  return Object.freeze({
    from: Object.freeze({
      epoch: fromEpoch,
      files: loadLocalBlocklist(path.join(parentFolder, `Blocklist-${fromEpoch}`)),
    }),
    to: Object.freeze({
      epoch: toEpoch,
      files: loadLocalBlocklist(path.join(parentFolder, `Blocklist-${toEpoch}`)),
    }),
    diff: Object.freeze({
      fromEpoch,
      toEpoch,
      files: loadLocalBlocklist(path.join(parentFolder, `Diff-${fromEpoch}-${toEpoch}`)),
    }),
  });
};

/**
 * Loads and caches test data locally which can be used in unit tests.
 * Unit tests can access the data via properties. It will be loaded when first accessed, and then
 *  can be reused in memory by subsequent tests.
 *
 * @note The test data is frozen so that it can't be accidentally modified by tests.
 */
class TestDataManager {
  // A real blocklist from production, containing a typical diff.
  public get blocklistWithTypicalDiff(): BlocklistWithDiff {
    if (this._blocklistWithTypicalDiff === undefined) {
      this._blocklistWithTypicalDiff = loadBlocklistWithDiff(
        blocklistsFolder,
        1711770303,
        1712803503,
      );
    }
    return this._blocklistWithTypicalDiff;
  }

  private _blocklistWithTypicalDiff?: BlocklistWithDiff;

  // A real blocklist from production, containing an unusually large diff.
  public get blocklistWithLargeDiff(): BlocklistWithDiff {
    if (this._blocklistWithLargeDiff === undefined) {
      this._blocklistWithLargeDiff = loadBlocklistWithDiff(
        blocklistsFolder,
        1737734413,
        1738598413,
      );
    }
    return this._blocklistWithLargeDiff;
  }

  private _blocklistWithLargeDiff?: BlocklistWithDiff;

  // A fake blocklist diff which is deliberately invalid and should fail to load/apply.
  public get invalidBlocklistDiff(): DiffData {
    if (this._invalidBlocklistDiff === undefined) {
      const fromEpoch = 1711770303;
      const toEpoch = 1712803503;
      this._invalidBlocklistDiff = Object.freeze({
        fromEpoch,
        toEpoch,
        files: loadLocalBlocklist(
          path.join(invalidBlocklistsFolder, `InvalidDiff-${fromEpoch}-${toEpoch}`),
        ),
      });
    }
    return this._invalidBlocklistDiff;
  }

  private _invalidBlocklistDiff?: DiffData;

  // A fake blocklist which deliberately has an invalid JSON file and should fail to load.
  public get blocklistWithInvalidJson(): BlocklistData {
    if (this._blocklistWithInvalidJson === undefined) {
      const epoch = 1711770303;
      this._blocklistWithInvalidJson = Object.freeze({
        epoch,
        files: loadLocalBlocklist(
          path.join(invalidBlocklistsFolder, `InvalidJsonBlocklistFile-${epoch}`),
        ),
      });
    }
    return this._blocklistWithInvalidJson;
  }

  private _blocklistWithInvalidJson?: BlocklistData;
}

const testDataManager = new TestDataManager();
export default testDataManager;

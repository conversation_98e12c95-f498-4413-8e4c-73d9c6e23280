/* eslint-disable @typescript-eslint/consistent-type-assertions */
/* eslint-disable @typescript-eslint/strict-boolean-expressions */
/*
 * This file sets up global mocks for tests which hit browser APIs.
 * It should not be imported directly. It should be imported as part of the test configuration.
 */

/*
 * Spy on the fetch API so that we can intercept web requests.
 * By default, it will always run the original fetch implementation when called.
 * Each test suite should set up its own mock implementation if needed, e.g.:
 *  fetchSpy.mockImplementation(() => { ... });
 */

// First check if fetch exists in globalThis, if not, add a mock implementation
if ((globalThis as any).fetch === undefined) {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  (globalThis as any).fetch = jest.fn().mockImplementation(
    async () =>
      await Promise.resolve({
        ok: true,
        status: 200,
        json: async () => ({}),
        text: async () => '',
        headers: new Headers(),
        redirected: false,
        statusText: 'OK',
        type: 'basic',
        url: '',
        clone: () => new Response(),
        body: null,
        bodyUsed: false,
        arrayBuffer: async () => new ArrayBuffer(0),
        blob: async () => new Blob(),
        formData: async () => new FormData(),
      } as Response),
  );
}

// Now we can safely spy on fetch
(globalThis as any).fetchSpy = jest.spyOn(globalThis, 'fetch');

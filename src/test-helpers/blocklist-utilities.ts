import fs from 'fs';
import path from 'path';

/**
 * Read blocklist or blocklist diff files from a directory into a collection of strings in memory.
 * This will look for a manifest file within the directory, and loads all the files specified in
 *  there.
 *
 * @warning This uses the NodeJS filesystem API so it can only be offline in unit tests and scripts.
 *
 * @param directory The path of the directory containing the blocklist or blocklist diff. There
 *  should be a manifest file (called "manifest.json") within the directory listing all the other
 *  files to be loaded.
 * @return Returns an object containing the blocklist files. Each key will be the name of a file,
 *  and each value will be the file's contents as a raw string. This is the same format as returned
 *  by BlocklistDownloader.
 * @throws {Error} The blocklist could not be loaded. This could mean the directory wasn't found,
 *  the manifest file wasn't found or was invalid, or one or more blocklist files specified in the
 *  manifest file wasn't found.
 */
export const loadLocalBlocklist = (directory: string): Record<string, string> => {
  const manifest = JSON.parse(
    fs.readFileSync(path.join(directory, 'manifest.json'), { encoding: 'utf-8' }),
  );
  const output: Record<string, string> = {};
  (manifest.files as string[]).forEach((filename: string) => {
    output[filename] = fs.readFileSync(path.join(directory, filename), { encoding: 'utf-8' });
  });
  return Object.freeze(output);
};

/**
 * Make an abridged copy of an existing set of blocklist files.
 * This does a deep copy, then removes most of the weighted phrases.
 * The weighted phrases are by far the most computationally expensive component to load, by multiple
 *  orders of magnitude, so shortening it can be helpful to avoid unit tests taking a long time.
 *
 * @param files The blocklist files to abridge. This must match the structure returned by
 *  loadLocalBlocklist(). It should be a full blocklist. It will have no effect on a diff.
 * @param maxItemsToKeep The maximum number of items to keep in the weighted phrases file.
 * @return Returns a deep copy of the blocklist files, with most of the weighted phrases removed.
 *
 * @warning Only use this for tests where the weighted phrase content doesn't matter.
 */
export const makeAbridgedBlocklistFiles = (
  files: Record<string, string>,
  maxItemsToKeep: number = 20,
): Record<string, string> => {
  const output = JSON.parse(JSON.stringify(files));
  if (output.weightedphrases != null) {
    output.weightedphrases = JSON.stringify(
      Object.fromEntries(
        Object.entries(JSON.parse(output.weightedphrases)).slice(0, maxItemsToKeep),
      ),
    );
  }
  return Object.freeze(output);
};

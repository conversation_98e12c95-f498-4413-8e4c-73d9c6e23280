/*
This file defines a function which performs one-time tear-down after the unit test framework ends.
We use it to stop the test server running.
*/

import * as http from 'http';

export default async (): Promise<void> => {
  await new Promise<void>((resolve) => {
    // The server global was defined in the global setup function.
    const server = (globalThis as any).testServer as http.Server | undefined;
    if (server == null) {
      resolve();
    } else {
      server.close(() => {
        resolve();
      });
    }
  });
};

import LogSeverityLevel from 'models/LogSeverityLevel';
import PlatformConfig from 'models/PlatformConfig';
import ITelemetryService from '../services/ITelemetryService';
import ProvisioningInfo from 'provisioning/ProvisioningInfo';
import MetricTelemetry from 'models/MetricTelemetry';

export default class MockTelemetryService implements ITelemetryService {
  public readonly initStandardParameters = (
    provisioningInfo: ProvisioningInfo,
    platformConfig: PlatformConfig,
  ): void => {};

  public readonly logEvent = (name: string, customParameters?: any): void => {};

  public readonly logMetric = (metric: MetricTelemetry): void => {};

  public readonly logError = (
    name: string,
    exception: Error | string,
    customParameters?: any,
    severity?: LogSeverityLevel | undefined,
  ): void => {};

  public readonly logTrace = (name: string, customParameters?: any): void => {};

  public readonly setDeviceId = (deviceId: string): void => {};
}

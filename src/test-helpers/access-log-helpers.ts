import AccessLogEntry from 'models/AccessLogEntry';

export const generateLog = (contenttype: string, url: string, logLevel: number): AccessLogEntry => {
  return {
    took: performance.now(),
    time: Math.floor(Date.now() / 1000).toString(),
    groups: [],
    categories: [],
    contenttype,
    url,
    loglevel: logLevel,
  };
};

const randomInt = (min: number, range: number): number => {
  return min + Math.floor(Math.random() * range);
};

/**
 * Create an access log entry containing random data.
 */
export const makeRandomLogEntry = (): AccessLogEntry => {
  return {
    blocked: Math.random() <= 0.5,
    url: `https://test-${randomInt(0, 10000)}.example.com`,
    time: (1704067200 + randomInt(0, 604800)).toString(),
    took: 1 + randomInt(0, 2000),
    categories: [...Array(randomInt(0, 5))].map(() => `${randomInt(1, 10000)}`),
    groups: [...Array(randomInt(0, 5))].map(() => `group-${randomInt(1, 10000)}`),
    locations: [],
    timeslots: [],
    actions: [],
    searchterms: '',
    loglevel: randomInt(1, 5),
    v: '4',
    userAgent: '',
  };
};

/**
 * Create an array of access log entries, each containing random data.
 * @param num The number of entries to create.
 */
export const makeRandomLogEntries = (num: number): AccessLogEntry[] => {
  return [...Array(num)].map(() => makeRandomLogEntry());
};

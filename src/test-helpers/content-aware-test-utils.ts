import { ContentAwareMessageTypes } from 'constants/ContentAwareTypes';
import {
  ContentAwareBaseResponseMessage,
  IContentAwareExtensionInfo,
} from 'models/ContentAwareModels';

/**
 * Utility to mock chrome.runtime.sendMessage for Content Aware extension tests
 * This helps avoid timeouts in tests by properly mocking the message passing behavior
 */
export const mockContentAwareMessaging = (
  extensionInfo: IContentAwareExtensionInfo,
  responseMap: Record<string, any> = {},
  options: {
    simulateDelay?: boolean;
    delayMs?: number;
    simulateError?: boolean;
    errorMessage?: string;
    simulateTimeout?: boolean;
  } = {},
): jest.SpyInstance => {
  // Default options
  const {
    simulateDelay = false,
    delayMs = 10,
    simulateError = false,
    errorMessage = 'Mock error',
    simulateTimeout = false,
  } = options;

  // Clear any existing mock implementation
  if (jest.isMockFunction(chrome.runtime.sendMessage)) {
    (chrome.runtime.sendMessage as jest.Mock).mockClear();
  }

  // Create a spy on chrome.runtime.sendMessage
  const sendMessageSpy = jest.spyOn(chrome.runtime, 'sendMessage');

  // Mock implementation based on options
  sendMessageSpy.mockImplementation(
    async (
      targetExtensionId: string | null | undefined,
      message: any,
      optionsOrCallback: any,
    ): Promise<any> => {
      return await new Promise((resolve) => {
        // Extract callback if it's provided directly or as part of options
        const callback =
          typeof optionsOrCallback === 'function' ? optionsOrCallback : optionsOrCallback?.callback;

        // Only handle messages to our extension
        if (targetExtensionId !== extensionInfo.id) {
          if (callback !== undefined && callback !== null) callback(undefined);
          resolve(undefined);
          return;
        }

        // Get the message type
        const messageType = message?.type as string;

        // If simulating timeout, don't call the callback at all
        if (simulateTimeout) {
          // Just resolve the promise but don't call the callback
          resolve(undefined);
          return;
        }

        // If simulating error, set chrome.runtime.lastError
        if (simulateError) {
          if (callback !== undefined && callback !== null) {
            chrome.runtime.lastError = { message: errorMessage };
            callback(undefined);
            chrome.runtime.lastError = undefined;
          }
          resolve(undefined);
          return;
        }

        // Get the appropriate response for this message type
        const response = responseMap[messageType] ?? { status: 200, message: 'Success' };

        // If simulating delay, use setTimeout to delay the response
        if (simulateDelay && callback !== undefined && callback !== null) {
          setTimeout(() => {
            callback(response);
            resolve(response);
          }, delayMs);
        } else if (callback !== undefined && callback !== null) {
          callback(response);
          resolve(response);
        } else {
          resolve(response);
        }
      });
    },
  );

  return sendMessageSpy;
};

/**
 * Helper to create a mock response map for Content Aware extension messages
 */
export const createContentAwareResponseMap = (
  responses: Partial<Record<string, any>> = {},
): Record<string, ContentAwareBaseResponseMessage> => {
  // Default responses for each message type
  const defaultResponses: Record<string, any> = {
    [ContentAwareMessageTypes.LOGIN]: {
      Success: true,
    },
    [ContentAwareMessageTypes.LOGOUT]: {
      Success: true,
    },
    [ContentAwareMessageTypes.UPDATE_CONFIG_ALL]: {
      Success: true,
    },
    [ContentAwareMessageTypes.IS_LOGGED_IN]: {
      Success: true,
      IsLoggedIn: true,
    },
  };

  // Merge default responses with provided responses
  return { ...defaultResponses, ...responses };
};

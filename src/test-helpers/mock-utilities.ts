/**
 * Utility function for waiting until a mock function has been called a certain number of times.
 * This works by adding a temporary mock implementation which wraps any other mock implementation.
 * If you're relying on the mock to have a return value or mock implementation, then you must set
 *  it before calling this function.
 *
 * @param mock The mock function to wait on.
 * @param numCallsExpected The number of calls to wait for. This relates to the total number of
 *  calls on the mock since it was created (or cleared). If the mock has already been called this
 *  number of times before this function was called, then this will finish immediately.
 * @returns A promise which resolves when the mock has been called the specified number of times
 *  in total.
 *
 * @note This doesn't work well with mockReturnValueOnce() or mockImplementationOnce(). You can
 *  still use those, but this won't detect any calls until the mock falls back on its default
 *  return value or implementation (i.e. after all the *Once() calls have been exhausted).
 */
export const waitForMockToBeCalled = async (
  mock: jest.Mock<any, any>,
  numCallsExpected: number = 1,
): Promise<void> => {
  if (mock.mock.calls.length >= numCallsExpected) {
    await Promise.resolve();
    return;
  }

  const originalImplementation = mock.getMockImplementation();
  await new Promise<void>((resolve) => {
    mock.mockImplementation((...args: any) => {
      if (mock.mock.calls.length >= numCallsExpected) {
        mock.mockImplementation(originalImplementation);
        resolve();
      }

      if (originalImplementation !== undefined) {
        return originalImplementation(...args);
      }
    });
  });
};

/**
 * Create a promise which resolves when all timers and pending promises have resolved.
 * If new timers are scheduled during this process, then this will wait for those too.
 * This only works if you have enabled Jest's fake timers in your tests; see jest.useFakeTimers().
 * This is useful if you're working with event-based asynchronous code which has a mixture of timers
 *  and promises.
 *
 * @warning This is experimental. There may be many cases where it doesn't work properly. In
 *  particular
 * @warning If you have recursive timers, i.e. a timer which schedules another timer, then this may
 *  try to keep going forever. Jest will fail the test if it goes on too long. Consider using
 *  waitForPendingPromisesAndFakeTimers() instead.
 */
export const waitForAllPromisesAndFakeTimers = async (): Promise<void> => {
  do {
    // Ensure all pending timers have been executed.
    jest.runAllTimers();

    // Schedule a promise to resolve. This should go to the back of the queue, so any other promises
    //  should resolve/reject before this one.
    // Note: When fake timers are enabled, Jest also mocks "setImmediate()". We need to use the real
    //  version here otherwise it won't resolve.
    await new Promise(jest.requireActual('timers').setImmediate);

    // More timers may have been scheduled while we were waiting for promises to resolve. Keeping
    //  going until there are no timers left.
  } while (jest.getTimerCount() > 0);
};

/**
 * Create a promise which resolves when currently pending timers and promises have resolved.
 * If new timers are scheduled during the process, then this will not wait for those.
 * This only works if you have enabled Jest's fake timers in your tests; see jest.useFakeTimers().
 *
 * This is a more controlled version of waitForAllPromisesAndFakeTimers(). You may need to call this
 *  multiple times if there are multiple timers/promises you need to wait for. Use this version
 *  mainly if you need more control, or need to avoid running infinitely recurring timers.
 *
 * @warning This is experimental. There may be many cases where it doesn't work properly.
 */
export const waitForPendingPromisesAndFakeTimers = async (): Promise<void> => {
  jest.runOnlyPendingTimers();
  await new Promise(jest.requireActual('timers').setImmediate);
};

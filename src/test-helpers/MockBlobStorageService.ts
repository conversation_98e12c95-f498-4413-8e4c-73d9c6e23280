import IBlobStorageService from 'services/IBlobStorageService';
import FileDownloadType from 'models/FileDownloadType';
import StandaloneEvent from 'utilities/StandaloneEvent';
import BlobDownloadResult from 'models/BlobDownloadResult';

export default class MockBlobStorageService implements IBlobStorageService {
  constructor(urlData: MockUrlData[]) {
    this._urlData = urlData;
  }

  public start = (url: string, fileType: FileDownloadType, eTag?: string): void => {
    let promise;

    switch (fileType) {
      case FileDownloadType.blob:
        promise = this._getBlob(url, eTag);
        break;
      case FileDownloadType.json:
        promise = this._getBlobAsJson(url, eTag);
        break;
      case FileDownloadType.text:
        promise = this._getBlobText(url, eTag);
        break;
    }

    promise
      .then((value) => {
        if (value === undefined) {
          this.onDownloadFailed.dispatch(
            new BlobDownloadResult(
              url,
              undefined,
              false,
              500,
              'Could not download the file from blob.',
            ),
          );
          return;
        }

        // Don't emit the event if the download should be cancelled.
        if (this._cancelDownload) {
          return;
        }

        if (value !== null)
          this.onDownloadSuccess.dispatch(new BlobDownloadResult(url, value, true, 200));
      })
      .catch((error: Error) => {
        // If the fetch was cancelled using the AbortController the promise will reject with the error name AbortError.
        if (error.name === 'AbortError') {
          console.debug('Blob fetch aborted.');
          return;
        }

        this.onDownloadFailed.dispatch(
          new BlobDownloadResult(url, undefined, false, 0, error.message),
        );
        console.error('Error downloading from blob.', error);
      })
      .finally(() => {
        this._cancelDownload = false;
        this._downloading = false;
      });
  };

  public stop = (): void => {
    this._cancelDownload = true;
  };

  private readonly _getBlob = async (
    url: string,
    eTag?: string,
  ): Promise<MockUrlData | undefined> => {
    const blob = this._urlData.find((b) => b.url === url);

    // await new Promise((resolve) => setTimeout(resolve, 500));

    if (blob === undefined) {
      return undefined;
    }

    return blob;
  };

  private readonly _getBlobText = async (
    url: string,
    eTag?: string,
  ): Promise<string | undefined> => {
    const blob = await this._getBlob(url, eTag);
    return blob?.data;
  };

  private readonly _getBlobAsJson = async (
    url: string,
    eTag?: string,
  ): Promise<any | undefined> => {
    const text = await this._getBlobText(url, eTag);
    return text === undefined ? undefined : JSON.parse(text);
  };

  public readonly onDownloadSuccess = new StandaloneEvent<[BlobDownloadResult]>();

  public readonly onDownloadFailed = new StandaloneEvent<[BlobDownloadResult]>();

  public readonly onETag = new StandaloneEvent<[string]>();

  public get isDownloading(): boolean {
    return this._downloading;
  }

  private _downloading = false;

  private _cancelDownload = false;

  private readonly _urlData: MockUrlData[];
}

export class MockUrlData {
  constructor(url: string, data: string) {
    this.url = url;
    this.data = data;
  }

  public url: string;
  public data: string;
}

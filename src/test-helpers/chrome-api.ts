// TODO: Possibly create mocks for all relevant Chrome APIs, and auto-load them?
/**
 * A mock implementation of the Chrome StorageArea type, with each method replaced by a spy.
 * Individual tests should instantiate and inject a mock when necessary, being careful to return a
 *  resolved promise or execute the callback as appropriate.
 */
export class StorageAreaMock implements chrome.storage.StorageArea {
  public getBytesInUse = jest.fn();
  public clear = jest.fn();
  public set = jest.fn(async (items: Record<string, any>, callback?: () => void) => {
    if (callback === undefined) {
      await Promise.resolve();
      return;
    }
    callback();
  });

  public remove = jest.fn();
  public get = jest.fn();
  public getKeys = jest.fn();
  public setAccessLevel = jest.fn();
  public onChanged = undefined as unknown as chrome.storage.StorageAreaChangedEvent;
}

/**
 * A mock implementation of Chrome's LocalStorageArea type, with each method replaced by a spy.
 * Individual tests should instantiate and inject a mock when necessary, being careful to return a
 *  resolved promise or execute the callback as appropriate.
 */
export class LocalStorageAreaMock
  extends StorageAreaMock
  implements chrome.storage.LocalStorageArea
{
  public readonly QUOTA_BYTES: number = 0;
}

/**
 * A mock implementation of Chrome's SyncStorageArea type, with each method replaced by a spy.
 * Individual tests should instantiate and inject a mock when necessary, being careful to return a
 *  resolved promise or execute the callback as appropriate.
 */
export class SyncStorageAreaMock extends StorageAreaMock implements chrome.storage.SyncStorageArea {
  public readonly MAX_SUSTAINED_WRITE_OPERATIONS_PER_MINUTE: number = 0;
  public readonly QUOTA_BYTES: number = 0;
  public readonly QUOTA_BYTES_PER_ITEM: number = 0;
  public readonly MAX_ITEMS: number = 0;
  public readonly MAX_WRITE_OPERATIONS_PER_HOUR: number = 0;
  public readonly MAX_WRITE_OPERATIONS_PER_MINUTE: number = 0;
}

/**
 * Mocks the structure of the Chrome API, as it doesn't exist at all during unit tests.
 * Each test suite should replace specific items with mocks or mocked data, as needed.
 * This deliberately isn't exported. We want calling code to rely on the Chrome API types bundled by
 *  npm.
 *
 * @todo Add more Chrome APIs to this.
 */
(globalThis as any).chrome = {
  action: {
    setIcon: jest.fn().mockResolvedValue(undefined),
  },
  gcm: {
    register: undefined,
    unregister: undefined,
    onMessage: { addListener: undefined },
    onMessagesDeleted: { addListener: undefined },
  },
  runtime: {
    // By default, indicate that no error occurred.
    lastError: undefined as any,
    getManifest: jest.fn(() => "{'version': '2.0.0'}"),
    getPlatformInfo: undefined as any,
    getURL: undefined as any,
  },
  storage: {
    local: undefined as any,
    sync: undefined as any,
    managed: undefined as any,
  },
  enterprise: {
    deviceAttributes: {} as any,
  },
  alarms: {
    onAlarm: { addListener: jest.fn },
    get: jest.fn(),
    create: jest.fn(),
  },
};

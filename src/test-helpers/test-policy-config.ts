import CONTENT_AWARE_CONSTANTS from 'constants/ContentAwareConstants';
import TenantId from 'models/TenantId';

/**
 * Returns a test policy configuration without Content Aware categories.
 * @param tenantId The tenant ID to use for tenant-specific policy items
 */
export const getTestPolicyConfig = (tenantId: TenantId): any => {
  return {
    banned: [
      {
        tenant: 'global',
        comment: 'Banned',
        id: '123',
        banexpirydate: `${(Date.now() + 100000) / 1000}`,
        enabled: '1',
        username: 'imbanned',
      },
      {
        tenant: 'global',
        comment: 'banned again',
        id: '1234',
        banexpirydate: `${(Date.now() + 5000000) / 1000}`,
        enabled: '1',
        username: 'imbanned',
      },
      {
        tenant: tenantId.toString(),
        comment: 'Tenanted Ban',
        id: '321',
        banexpirydate: `${(Date.now() + 100000) / 1000}`,
        enabled: '1',
        username: 'imalsobanned',
      },
      {
        tenant: 'global',
        comment: 'Disabled ban',
        id: '12345',
        banexpirydate: `${(Date.now() + 100000) / 1000}`,
        enabled: '0',
        username: 'imnotbanned',
      },
      {
        tenant: 'global',
        comment: 'Expired ban',
        id: '123456',
        banexpirydate: `${(Date.now() - 100000) / 1000}`,
        enabled: '1',
        username: 'imalsonotbanned',
      },
    ],
    blockpages: [],
    category_filter_groups: [
      {
        source: ['1', '2', '30'],
        comment: 'Inappropriate content',
        tenant: 'global',
        id: '5AFCC930-A80F-11EB-A051-0182081D14AE',
        name: 'Core Blocked Content',
      },
      {
        source: ['1', '2', '30'],
        comment: 'Inappropriate content',
        tenant: tenantId.toString(),
        id: '5AFCC930-A80F-11EB-A051-0182081D14AE',
        name: 'Core Blocked Content For Tenant',
      },
    ],
    cloud_content_modifications: [
      {
        id: '123',
        tenantId: 'global',
        filterType: 'include',
        groups: [],
        locations: [],
        blockmanId: '12',
        enabled: true,
      },
      {
        id: '111',
        tenantId: 'global',
        filterType: 'include',
        groups: [],
        locations: [],
        blockmanId: '12',
        enabled: false,
      },
      {
        id: '456',
        tenantId: tenantId.toString(),
        filterType: 'include',
        groups: [],
        locations: [],
        blockmanId: '34',
        enabled: true,
      },
      {
        id: '789',
        tenantId: 'D440CC1C-2F61-4FF2-A4E9-34BCC10A0ECF',
        filterType: 'include',
        groups: [],
        locations: [],
        blockmanId: '56',
        enabled: true,
      },
    ],
    cloud_filter: {
      host: '**********:6150/token',
      timeout: '600',
    },
    content_modification: [
      {
        id: '5B6AA874-A80F-11EB-A051-0182081D14AE',
        action: '6',
        what: ['5AFCC930-A80F-11EB-A051-0182081D14AE'],
        where: ['Everywhere'],
        ruleset: ['5B4E305E-A80F-11EB-A051-0182081D14AE', '5B484CFC-A80F-11EB-A051-0182081D14AE'],
        order: '1',
        enabled: 'on',
        who: ['Everyone'],
      },
    ],
    custom_categories: [
      {
        id: '5AD38700-A80F-11EB-A051-0182081D14AE',
        category_id: '546',
        from_blocklist: '1',
        tenant: 'global',
      },
      {
        id: '5AD34470-A80F-11EB-A051-0182081D14AE',
        category_id: '321',
        from_blocklist: '6',
        tenant: tenantId.toString(),
        component: {
          searchterms: ['aaaaa', 'test', '[tenant]', '[multi][tenant][test]', '(tenanted)(test)'],
        },
      },
      {
        id: '61E8BB25-CFD2-4A94-9DE8-0E2FA648EF17',
        category_id: '61e8bb25-cfd2-4a94-9de8-0e2fa648ef17',
        href: '1',
        custom_content: '1',
        name: 'Custom Category',
        description: '',
        tenant: 'global',
        component: {
          domainsurls: ['custom.com'],
          searchterms: [
            'bbbbb',
            'search test',
            '[test]',
            '[multiple][term][test]',
            '(another)(test)',
            '|pipe|',
            '|single||delimiter||test|',
          ],
        },
      },
      {
        id: '6570637d-09af-4d2a-84bb-e465f8f258a1',
        category_id: '6570637d-09af-4d2a-84bb-e465f8f258a1',
        href: '1',
        custom_content: '1',
        name: 'Custom Category Subdomain',
        description: '',
        tenant: 'global',
        component: {
          domainsurls: ['test.custom.com'],
        },
      },
      {
        id: '4d8ab083-5404-439e-bbb5-49a77de2a5b9',
        category_id: '4d8ab083-5404-439e-bbb5-49a77de2a5b9',
        href: '1',
        custom_content: '1',
        name: 'Custom Category Path',
        description: '',
        tenant: 'global',
        component: {
          domainsurls: ['custom.com/path'],
        },
      },
      {
        id: 'a9baee13-d068-4894-85d6-fa35f7b656f8',
        category_id: 'a9baee13-d068-4894-85d6-fa35f7b656f8',
        href: '1',
        custom_content: '1',
        name: 'Custom Category Uppercase',
        description: '',
        tenant: 'global',
        component: {
          domainsurls: ['TesT.EXample.com/UPPERcasetest'],
        },
      },
      // No Content Aware categories by default - use getTestPolicyConfigWithContentAware for those
    ],
    custom_content_modifiers: [],
    flattenedContentMods: [
      {
        id: '5B6AA874-A80F-11EB-A051-0182081D14AE',
        action: '6',
        what: ['5AFCC930-A80F-11EB-A051-0182081D14AE'],
        where: ['Everywhere'],
        ruleset: ['5B4E305E-A80F-11EB-A051-0182081D14AE', '5B484CFC-A80F-11EB-A051-0182081D14AE'],
        order: '1',
        enabled: 'on',
        who: ['Everyone'],
      },
    ],
    default_content_modifiers: [
      {
        id: '5B43263C-A80F-11EB-A051-0182081D14AE',
        modifier_id: '54',
      },
      {
        modifier_id: '104',
        id: '5B336E4A-A80F-11EB-A051-0182081D14AE',
      },
      {
        modifier_id: '5',
        id: '5B4E305E-A80F-11EB-A051-0182081D14AE',
      },
    ],
    google_group_users: {},
    groups: [
      {
        comment: '',
        id: 'A4416B46-9972-11EC-9FC5-91B9081D14AE',
        name: 'Test Group',
      },
      {
        comment: '',
        id: '96871BA9-8808-3074-8AFE-F88F872B0A6D',
        name: 'Default Users',
      },
      {
        comment: '',
        id: '69AAD226-0645-11EC-88C7-0ECA081D14AE',
        name: 'Students',
      },
      {
        comment: '',
        id: '7A9C36F1-D495-3ACE-9EA5-8A66D3A9A032',
        name: 'Banned Users',
      },
    ],
    group_mapping: [
      {
        id: '24A15BFA-43CB-4843-B3F7-17E32C6EB939',
        enabled: '1',
        directory_group: ['gedu.demo.smoothwall-dev.com\\/Classroom Manager (OU)'],
        local_group: 'F173C32A-DB1B-11EB-B3EE-E4D7081D14AE',
      },
      {
        id: '1EB66D62-93B2-4F53-B5AA-31338641580D',
        enabled: '1',
        directory_group: ['/Filtering (OU)'],
        local_group: 'A4416B46-9972-11EC-9FC5-91B9081D14AE',
      },
      {
        id: '6AB5A88C-1876-4096-BE15-B7CB6F507C35',
        enabled: '1',
        directory_group: ['local_group'],
        local_group: '7A9C36F1-D495-3ACE-9EA5-8A66D3A9A032',
      },
    ],
    locations: [
      {
        sources: [],
        exceptions: ['3.3.3.3'],
        id: 'BE5B0C8F-892E-470E-BC0B-1A34C6D659AA',
        name: 'Monte test',
        tenant: 'global',
      },
      {
        sources: [],
        exceptions: ['239.105.164.234'],
        id: 'D440CC1C-2F61-4FF2-A4E9-34BCC10A0ECF',
        name: 'z',
        tenant: 'global',
      },
      {
        sources: [],
        exceptions: ['1.1.1.1'],
        id: 'a0c49851-ddb2-459d-9368-e7c17ae2d802',
        name: 'Tenanted Location',
        tenant: tenantId.toString(),
      },
    ],
    metadata: {
      generated: '1662636087',
      hardware_id: 'cefd355566804ea4835973e4fc16ae73',
      version: '4',
    },
    policies: {
      before: [
        {
          what: ['PARENT'],
          when: ['Anytime'],
          who: ['PARENT'],
          where: ['PARENT'],
          order: '1',
          id: 'F8F8B15A-B7B7-11EB-8AB1-9C78081D14AE',
          name: 'Always',
          action: 'PARENT',
          enabled: 'on',
          children: [
            {
              what: ['5AD8661C-A80F-11EB-A051-0182081D14AE'],
              when: ['CHILD'],
              who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
              where: ['E78D6F86-A908-11EB-B44E-9C78081D14AE'],
              order: '1.1',
              id: 'F8F985F8-B7B7-11EB-8AB1-9C78081D14AE',
              name: 'Default Users - Archive Filetypes - Outside Premises',
              action: '0',
              enabled: 'on',
            },
            {
              what: ['5AD8661C-A80F-11EB-A051-0182081D14AE'],
              when: ['CHILD'],
              who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
              where: ['E78D6F86-A908-11EB-B44E-9C78081D14AE'],
              order: '1.2',
              id: 'F8F985F8-B7B7-11EB-8AB1-9C78081D14AE',
              name: 'Default Users - Archive Filetypes - Outside Premises',
              action: '0',
              enabled: 'on',
            },
          ],
        },
        {
          what: ['Disabled'],
          when: ['Disabled'],
          who: ['Disabled'],
          where: ['Disabled'],
          order: '2',
          id: 'Disabled',
          name: 'Disabled',
          action: '0',
          enabled: '',
        },
      ],
      tenants: {
        'f417a2c4-f99c-11ea-8caa-eb014c4bbe3b': [
          {
            what: ['854154DE-F99D-11EA-A202-DBDB4B4BBE3B'],
            when: ['Anytime'],
            who: ['Everyone'],
            where: ['Everywhere'],
            order: '3',
            id: '86584436-F99D-11EA-A202-DBDB4B4BBE3B',
            name: 'Everyone - Abortion',
            action: '0',
            enabled: 'on',
          },
        ],
        '0bc510e6-f99d-11ea-88fe-cc024c4bbe3b': [
          {
            what: ['8502DCB8-F99D-11EA-A202-DBDB4B4BBE3B'],
            when: ['BD358D22-F995-11EA-A4C0-DBDB4B4BBE3B'],
            who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
            where: ['Everywhere'],
            order: '4',
            id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
            name: 'Default Users - Amazon Prime',
            action: '0',
            enabled: 'on',
          },
        ],
      },
      after: [
        {
          what: ['8502DCB8-F99D-11EA-A202-DBDB4B4BBE3B'],
          when: ['BD358D22-F995-11EA-A4C0-DBDB4B4BBE3B'],
          who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
          where: ['Everywhere'],
          order: '6',
          id: '86596A3C-F99D-11EA-A202-DBDB4B4BBE3B',
          name: 'Default Users - Amazon Prime',
          action: '0',
          enabled: 'on',
        },
        {
          what: ['5AD8661C-A80F-11EB-A051-0182081D14AE'],
          when: ['CHILD'],
          who: ['96871BA9-8808-3074-8AFE-F88F872B0A6D'],
          where: ['E78D6F86-A908-11EB-B44E-9C78081D14AE'],
          order: '5',
          id: 'F8F985F8-B7B7-11EB-8AB1-9C78081D14AE',
          name: 'Default Users - Archive Filetypes - Outside Premises',
          action: '0',
          enabled: 'on',
        },
        {
          what: ['61E8BB25-CFD2-4A94-9DE8-0E2FA648EF17'],
          when: ['Anytime'],
          who: ['Everyone'],
          where: ['Everywhere'],
          order: '7',
          id: 'CD8A5B9F-C74D-4D26-B36A-0A34941B2BAF',
          name: 'Block custom category',
          action: '0',
          enabled: 'on',
        },
      ],
      locations: [],
    },
    quotas: [
      {
        quota_reset_time: '04:00',
        quota_unit_time: '10',
        who: ['Everyone'],
        enabled: 'on',
        quota_time: '60',
        id: '5B6CD8CE-A80F-11EB-A051-0182081D14AE',
      },
    ],
    tenants: [
      {
        id: 'f417a2c4-f99c-11ea-8caa-eb014c4bbe3b',
        name: 'Test tenant 1',
        addresses: ['test1'],
      },
    ],
    time_slots: [
      {
        id: '5A9BAE5C-A80F-11EB-A051-0182081D14AE',
        name: 'Lunchtime',
        comment: 'Weekday lunch period',
        tenant: 'global',
        times: [
          ['129600', '133199'],
          ['216000', '219599'],
          ['302400', '305999'],
          ['388800', '392399'],
          ['475200', '478799'],
        ],
      },
      {
        id: '5A9BAS45-A80F-11EB-A051-0182081D14AE',
        name: 'Tenanted Lunchtime',
        comment: 'Weekday lunch period',
        tenant: tenantId.toString(),
        times: [
          ['129600', '133199'],
          ['216000', '219599'],
          ['302400', '305999'],
          ['388800', '392399'],
          ['475200', '478799'],
        ],
      },
    ],
    users: [
      {
        id: 'B82007AA-2682-4650-BF78-9AB270FB48DD',
        name: '<EMAIL>',
      },
    ],
  };
};

/**
 * Returns a test policy configuration that includes Content Aware categories.
 * This is specifically for ContentAwareService tests.
 * @param tenantId The tenant ID to use for tenant-specific policy items
 */
export const getTestPolicyConfigWithContentAware = (tenantId: TenantId): any => {
  // Get the base policy config
  const policyConfig = getTestPolicyConfig(tenantId);

  // Add Content Aware Allow list categories
  policyConfig.custom_categories = [
    ...policyConfig.custom_categories,
    {
      id: 'b1c2d3e4-f5g6-7h8i-9j0k-l1m2n3o4p5q6',
      category_id: 'b1c2d3e4-f5g6-7h8i-9j0k-l1m2n3o4p5q6',
      href: '1',
      custom_content: '1',
      name: CONTENT_AWARE_CONSTANTS.ALLOW_LIST_CATEGORY_NAME,
      description: 'URLs exempt from Content Aware filtering',
      tenant: 'global',
      component: {
        domainsurls: ['example.com', 'trusted-site.org', 'school.edu'],
      },
    },
    {
      id: 'c2d3e4f5-g6h7-i8j9-k0l1-m2n3o4p5q6r7',
      category_id: 'c2d3e4f5-g6h7-i8j9-k0l1-m2n3o4p5q6r7',
      href: '1',
      custom_content: '1',
      name: CONTENT_AWARE_CONSTANTS.ALLOW_LIST_CATEGORY_NAME,
      description: 'Tenant-specific URLs exempt from Content Aware filtering',
      tenant: tenantId.toString(),
      component: {
        domainsurls: ['tenant-specific.com', 'tenant-trusted.org'],
      },
    },
  ];

  return policyConfig;
};

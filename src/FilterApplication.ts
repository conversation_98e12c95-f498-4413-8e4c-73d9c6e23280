import AccessLogStorage from 'access-logs/AccessLogStorage';
import DeviceType from 'constants/DeviceType';
import DiagnosticsPage from 'constants/DiagnosticsPage';
import FilterMode from 'constants/FilterMode';
import OperatingMode from 'constants/OperatingMode';
import RealTimeLogsPage from 'constants/RealTimeLogsPage';
import CloudConnectionController from 'controllers/CloudConnectionController';
import CustomerDataController from 'controllers/CustomerDataController';
import FilterController from 'controllers/FilterController';
import OffscreenDocumentController from 'controllers/OffscreenDocumentController';
import { Firestore } from 'firebase/firestore';
import AppInsightsConfig from 'models/AppInsightsConfig';
import DiagnosticsInfo from 'models/DiagnosticsInfo';
import HeartbeatData from 'models/HeartbeatData';
import IExtensionConfig from 'models/IExtensionConfig';
import LicenseResult from 'models/LicenseResult';
import OnInstalledOrUpdatedDetails from 'models/OnInstalledOrUpdatedDetails';
import PlatformConfig from 'models/PlatformConfig';
import ProvisioningInfo from 'provisioning/ProvisioningInfo';
import ProvisioningManager from 'provisioning/ProvisioningManager';
import AlarmService from 'services/AlarmService';
import AppInsightsTelemetryService from 'services/AppInsightsTelemetryService';
import ContentAwareService from 'services/ContentAwareService';
import DiagnosticsService from 'services/DiagnosticsService';
import IpService from 'services/IpService';
import ITelemetryService from 'services/ITelemetryService';
import KeepAliveService from 'services/KeepAliveService';
import LocalCacheService from 'services/LocalCacheService';
import OmniboxService from 'services/OmniboxService';
import SecretKnockService from 'services/SecretKnockService';
import {
  DmsSoftwareData,
  extractNativeAgentSoftwareData,
  getBadgeTitle,
  getExtensionVersion,
  hardResetExtension,
  setBadgeTitle,
  simplifyUserAgentString,
} from 'utilities/Helpers';

import extensionConfig from './config.json';
import { TelemetryEventType } from './constants/TelemetryEventType';

// This is defined in the Webpack config at build time.
declare const TELEMETRY_CONNECTION_STRING: string;

export default class FilterApplication {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  public constructor() {
    this._filterController.filterService.loadMiniFilter();
    this._keepAliveService.start();
  }

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Register listeners for browser events.
   * This should be called exactly once, before start().
   *
   * IMPORTANT: This must be called during the first event loop iteration, as soon as possible after
   *  the service worker is activated. If listeners are registered later then our service worker may
   *  not wake up properly if it becomes inactive.
   */
  public readonly addListeners = (): void => {
    this._filterController.addListeners();
    chrome.runtime.onConnect.addListener(this._onConnect);

    addEventListener('error', (event) => {
      if (this._telemetryService !== undefined) {
        this._telemetryService.logError(TelemetryEventType.UncaughtError, event.error);
      }
    });

    chrome.runtime.onInstalled.addListener((details) => {
      if (details.reason === 'install' || details.reason === 'update') {
        const eventDetails = {
          reason: details.reason,
          previousVersion: details.previousVersion ?? '',
          newVersion: getExtensionVersion(),
        };

        if (eventDetails.previousVersion?.startsWith('1.')) {
          console.debug(
            `Extension updated from version ${eventDetails.previousVersion} resetting.`,
          );
          hardResetExtension().catch(() => {
            console.debug('Could not clear the local storage while resetting the extension.');
          });
        }

        if (this._provisioningManager.isProvisioned) {
          this._logInstalledOrUpdatedEvent(eventDetails);
          return;
        }

        this._onInstalledOrUpdatedDetails = eventDetails;
      }
    });

    chrome.runtime.onUpdateAvailable.addListener((details) => {
      this._telemetryService.logEvent(TelemetryEventType.ExtensionUpdateAvailable, {
        availableVersion: details.version,
      });

      // Reload to make sure we get the update.
      chrome.runtime.reload();
    });

    // If managed storage changes while we're running, clear all data and restart the extension.
    chrome.storage.onChanged.addListener(
      (changes: Record<string, chrome.storage.StorageChange>, areaName: string): void => {
        if (areaName === 'managed') {
          console.log('Change detected in managed storage. Resetting extension...');
          this._onHardResetRequired().catch(console.warn);
        }
      },
    );

    // If all windows have been closed then explicitly close the offscreen document.
    // This should help in reducing the risk of the extension freezing.
    chrome.windows.onRemoved.addListener(() => {
      chrome.windows
        .getAll()
        .then((windows) => {
          if (windows.length === 0) {
            this._offscreenDocumentController.stop().catch(console.warn);
          }
        })
        .catch(console.warn);
    });

    this._provisioningManager.addEventListeners();
    this._provisioningManager.onProvisioned.addListener(this._onProvisioned);
    this._provisioningManager.onProvisioningUpdated.addListener(this._onProvisioningUpdated);
    this._provisioningManager.onProvisioningFailed.addListener(this._onProvisioningFailed);
    this._provisioningManager.onIpAddresses.addListener(this._onIpAddresses);
    this._provisioningManager.onHardResetRequired.addListener(this._onHardResetRequired);

    this._provisioningManager.addEventListeners();
    this._provisioningManager.onProvisioned.addListener(this._onProvisioned);
    this._provisioningManager.onProvisioningUpdated.addListener(this._onProvisioningUpdated);
    this._provisioningManager.onProvisioningFailed.addListener(this._onProvisioningFailed);
    this._provisioningManager.onIpAddresses.addListener(this._onIpAddresses);
    this._provisioningManager.onHardResetRequired.addListener(this._onHardResetRequired);

    this._cloudConnectionController.onReady.addListener(this._onCloudConnectionReady);
    this._cloudConnectionController.onFailed.addListener(this._onCloudConnectionFailed);
    this._cloudConnectionController.onUnlicensed.addListener(this._onUnlicensed);
    this._cloudConnectionController.onHeartbeatAlarm.addListener(this._onHeartbeatAlarm);
    this._cloudConnectionController.heartbeatService.onPublicIpAddressesReceived.addListener(
      this._ipService.onPublicIpAddresses,
    );

    this._filterController.remoteLogViewerService.onSessionStart.addListener(
      this._startRemoteLogViewerSession,
    );
    this._filterController.remoteLogViewerService.onSessionEnd.addListener(
      this._endRemoteLogViewerSession,
    );

    this._filterController.filterService.onFilterModeChanged.addListener(this._onFilterModeChanged);
  };

  /**
   * Load the extension configuration, filtering data, and initialise connections.
   * This should be called exactly once, after addListeners().
   * Unlike addListeners(), this doesn't have to be called on the first event loop iteration.
   *
   * @return Returns a promise which resolves when initialisation has successfully finished.
   */
  public readonly start = async (): Promise<void> => {
    // Do nothing if we've already started.
    if (this._platformConfig !== undefined) {
      return;
    }

    this._platformConfig = await PlatformConfig.load();

    // IMPORTANT: Ensure the extension is allowed to run before we take any actions or send any data
    //  to the cloud.
    if (!this._platformConfig.isAllowedToRun) {
      console.warn('This device is unmanaged. Filtering is disabled by extension policy.');
      chrome.management.setEnabled(chrome.runtime.id, false).catch(console.error);
      return;
    }

    // Add pages to the omnibox.
    this._omniboxService.setDestination(
      'Diagnostics Page',
      'Smoothwall Diagnostics',
      chrome.runtime.getURL('views/diagnostics.html'),
    );

    this._omniboxService.setDestination(
      'Real Time Filtering Logs',
      'Smoothwall Real Time Filtering Log Viewer',
      chrome.runtime.getURL('views/realtime-filtering-logs.html'),
    );

    try {
      // Extension v3.0.1 substantially changed how we store access logs in memory and on disk.
      // Earlier versions stored them in a single contiguous array. Under certain circumstances,
      //  that array could grow to a size which triggered an unexpected error in the browser when
      //  performing certain storage operations, causing a fatal crash in the extension service
      //  worker. The size limit is undocumented, but appears to be lower in mv3 extensions than it
      //  was in mv2. This means there is a risk of repeatedly crashing on startup when loading data
      //  from storage if the extension has just been updated from v3.0.0 or earlier.
      // To safeguard against this, we'll check if the newer access log data structure is present in
      //  storage. If it is present then we should be safe to proceed. If it's not present, then it
      //  may mean we've just updated from a legacy version. We'll check the total amount of data in
      //  storage, and if it's over a known safe threshold then we'll purge it before proceeding.
      // This potentially sacrifices access log data to salvage essential functionality.
      const partialData = await chrome.storage.local.get(AccessLogStorage.activeBufferName);
      const size = await chrome.storage.local.getBytesInUse();
      const safetyLimit = 250000000; // <-- bytes
      if (!Array.isArray(partialData[AccessLogStorage.activeBufferName]) && size > safetyLimit) {
        console.error(
          `Clearing local storage as its size exceeds the safety limit. Size: ${size} bytes.`,
        );
        await chrome.storage.local.clear();
      } else {
        // Load all data from storage in a single operation then split it up into different areas.
        // This ensures we don't lose track of dynamically-named properties such as access logs.
        const storageData = await chrome.storage.local.get();
        this._localCacheService.populateAll(storageData);
        await this._filterController.accessLogManager.populate(storageData);
      }
    } catch (e: any) {
      console.error('Error while loading cached data.', e);
      // Carry on with whatever data we have.
    }

    const licenseIsValid = this._localCacheService.licenseStatus.getBoolean('isValid');

    // If the license is currently invalid make sure we don't do any filtering, secret knocks, etc.
    if (licenseIsValid === false) {
      await this._onUnlicensed();
    }

    if (this._platformConfig.managedPolicy.DisableMiniFilter === true) {
      this._filterController.filterService.disableMiniFilter = true;
    }

    // Note: This must be done after we load cache data above.
    await this._provisioningManager.start(this._platformConfig);
  };

  // -----------------------------------------------------------------------------------------------
  // Event handlers.

  /**
   * Triggered when full provisioning info has been obtained from the cache or external source.
   * This should only be called once. If there are any major changes to the provisioning info then
   *  the provisioning controller will reset the extension.
   *
   * @param provisioningInfo The newly obtained provisioning info. The latest provisioning info can
   *  always be retrieved from the provisioning controller as well, so there's no need to store this
   *  separately.
   */
  private readonly _onProvisioned = async (provisioningInfo: ProvisioningInfo): Promise<void> => {
    console.debug('Initialising extension with provisioning information.');

    this._initTelemetryParameters(provisioningInfo);
    this._logExtensionLaunchEvent();
    await this._updateDiagnostics();

    if (this._onInstalledOrUpdatedDetails !== undefined) {
      this._logInstalledOrUpdatedEvent(this._onInstalledOrUpdatedDetails);
      this._onInstalledOrUpdatedDetails = undefined;
    }

    if (this._platformConfig === undefined) {
      console.error('Cannot initialize customer data controller: Platform config not loaded.');
      return;
    }

    await this._customerDataController.initialise(
      this._filterController.filterService.fullBlocklist,
      provisioningInfo,
      this._platformConfig.managedPolicy,
    );

    this._customerDataController.onCustomerDataLoaded.addListener(this._onCustomerDataLoaded);
    this._customerDataController.onUnlicensed.addListener(this._onUnlicensed);

    await this._ipService.start();

    // Check the licensing using the cached product config.
    await this._customerDataController.checkProductLicensingInfo();

    // Start the customer data download now if we already have a product config. Otherwise it will get started when the config is updated.
    await this._customerDataController.start();

    await this._cloudConnectionController.start(provisioningInfo);

    this._filterController.loadProvisioningInfo(provisioningInfo);
  };

  /**
   * Triggered when there's a minor update to the existing provisioning info.
   * This will only be triggered after _onProvisioned(), and may be triggered multiple times.
   * Minor updates will generally mean things like local group changes. This could impact policy
   *  decisions, but don't require us to re-register or download new config.
   *
   * @param provisioningInfo The updated provisioning info. The latest provisioning info can always
   *  be retrieved from the provisioning controller as well, so there's no need to store this
   *  separately.
   */
  private readonly _onProvisioningUpdated = async (
    provisioningInfo: ProvisioningInfo,
  ): Promise<void> => {
    this._initTelemetryParameters(provisioningInfo);
    this._filterController.loadProvisioningInfo(provisioningInfo);
  };

  /**
   * Triggered if there is a fatal error with loading the provisioning info.
   *
   * This will ensure we don't use mini filter when we don't have the correct provisioning info.
   */
  private readonly _onProvisioningFailed = (): void => {
    this._filterController.filterService.setFilteringDisabled(true);
    this._keepAliveService.stop();
  };

  /**
   * Triggered when our connection to the cloud has been successfully established.
   * This means Firestore can be accessed for customer config.
   *
   * @param firestore An initialised and authenticated instance of Firestore which we can use to
   *  access documents.
   * @param userDocumentPath The path of a Firestore document containing user-specific information
   *  and configuration. This may be empty if the path is not available from the API yet.
   */
  private readonly _onCloudConnectionReady = async (
    firestore: Firestore,
    userDocumentPath: string,
  ): Promise<void> => {
    // If we had any cached customer data, then it should have been loaded already.
    if (this._provisioningManager.provisioningInfo?.serialId !== undefined) {
      await this._customerDataController?.onFirestoreReady(firestore, userDocumentPath);
    }
  };

  /**
   * Triggered if our connection to the cloud has failed fatally.
   */
  private readonly _onCloudConnectionFailed = (): void => {
    // TODO: Send an App Insights exception with details of why it failed.
  };

  /**
   * Triggered if we detect that the customer isn't licensed.
   */
  private readonly _onUnlicensed = async (license?: LicenseResult): Promise<void> => {
    console.error('Customer is not licensed.');

    this._filterController.filterService.setFilteringDisabled(true);

    this._localCacheService.licenseStatus.set('isValid', false);
    await this._localCacheService.licenseStatus.save();
    this._secretKnockService.disable();

    // Stop the Content Aware service if it's running
    if (this._contentAwareService !== undefined) {
      console.debug('FilterApplication - Stopping Content Aware service due to invalid license');
      await this._contentAwareService.stop();
      this._contentAwareService = undefined;
    }

    await this._ipService.stop();

    this._filterController.accessLogManager.stop();

    await setBadgeTitle('License Error: Could not find a valid license.', true);
  };

  /**
   * Triggered when the heartbeat alarm elapses.
   *
   * Gets the required information to make a heartbeat request and starts the sending process.
   */
  private readonly _onHeartbeatAlarm = async (): Promise<void> => {
    const data = await this._getHeartbeatData();
    await this._cloudConnectionController.sendHeartbeat(data);
  };

  /**
   * Gathers the required data for a heartbeat request.
   *
   * @returns A promise that resolves to a HeartbeatData object with the required info.
   */
  private readonly _getHeartbeatData = async (): Promise<HeartbeatData> => {
    const blocklistEpoch = this._filterController.filterService.fullBlocklist.epoch ?? 0;
    const software: DmsSoftwareData[] = [
      {
        name: 'sw-extension',
        version: getExtensionVersion(),
        userAgent: this._userAgent,
      },
    ];

    // If we are (or have been) connected to a native agent, and it provided a version string, then
    //  send information about it to DMS.
    const agentSoftware = extractNativeAgentSoftwareData(
      this._provisioningManager.provisioningInfo?.agentVersion ?? '',
    );
    if (agentSoftware !== undefined) {
      software.push(agentSoftware);
    }

    return {
      checkInTime: Math.floor(Date.now() / 1000),
      logUploadCount: this._filterController.accessLogManager.getUploadCount(true),
      policyName: this._customerDataController.policyService?.policyName ?? '',
      publicIpAddresses: this._ipService.publicIpAddresses,
      privateIpAddresses: this._ipService.privateIpAddresses,
      software,
      mappedGroups: this._customerDataController.policyService?.mappedGroups ?? [],
      policyDownloadedAt:
        this._customerDataController.policyService?.policyDownloadedAt?.toString(),
      categorisation: blocklistEpoch > 0 ? blocklistEpoch.toString() : undefined,
    };
  };

  /**
   * Triggered when all of the customer data processes have finished loading.
   */
  private readonly _onCustomerDataLoaded = async (): Promise<void> => {
    const licenseInfo = this._customerDataController.getLicensingInfo();
    if (
      this._customerDataController.policyService?.policyConfig === undefined ||
      this._customerDataController.cloudGroupsService?.cloudGroups === undefined ||
      this._customerDataController.productConfig === undefined ||
      this._customerDataController.clientSettingsService === undefined ||
      this._provisioningManager.provisioningInfo === undefined || // <-- this check shouldn't be needed, but it keeps TypeScript happy
      !licenseInfo.cldfltLicenseValid
    ) {
      return;
    }

    // If we get to here we know the customer is licensed and filtering can be reenabled.
    this._filterController.filterService.setFilteringDisabled(false);
    this._localCacheService.licenseStatus.set('isValid', true);

    // Provisioning info won't be undefined for either of these.
    // If it were, we wouldn't even know who the customer is to get their data.
    await this._customerDataController.policyService.updateGroupMapping(
      this._provisioningManager.provisioningInfo?.localGroups ?? [],
      this._customerDataController.cloudGroupsService.cloudGroups,
    );

    this._secretKnockService.configureFromPolicyConfig(
      this._provisioningManager.provisioningInfo.serialId,
      this._customerDataController.policyService.cloudFilter,
    );

    await this._filterController.setCustomerData(
      this._customerDataController.policyService.policyConfig,
      licenseInfo,
      this._customerDataController.clientSettingsService?.useAdvancedBlockPage ?? false,
      this._customerDataController.policyService?.mappedGroups ?? [],
      this._customerDataController.clientSettingsService.safeguardingAlertResource,
      this._customerDataController.clientSettingsService.safeguardingAlertSecurityToken,
      this._customerDataController.clientSettingsService.safeguardingGlobalToggle,
    );

    // Get the custom search terms and add them to the full filter blocklist.
    const customSearchTerms =
      this._customerDataController.policyService.policyConfig.customSearchTerms;

    if (customSearchTerms !== undefined) {
      this._filterController.filterService.fullFilter.blocklist.customSearchTermCategoriser.setSearchTerms(
        customSearchTerms,
      );
    }

    // Initialize and start the Content Aware service if it hasn't been started yet
    if (this._contentAwareService === undefined) {
      console.debug('FilterApplication - Initializing Content Aware service');

      // Check if required services are available
      if (
        this._customerDataController.productConfigService !== undefined &&
        this._customerDataController.policyService !== undefined
      ) {
        this._contentAwareService = new ContentAwareService(
          this._customerDataController.productConfigService,
          this._customerDataController.policyService,
          this._telemetryService,
        );

        // Start the Content Aware service
        await this._contentAwareService.start();
      } else {
        console.warn(
          'FilterApplication - Cannot initialize Content Aware service: required services are undefined',
        );
      }
    }

    await this._updateDiagnostics();
  };

  /**
   * This is called when a port has been connected to the extension.
   * For example, when the diagnostics page has opened a connection to request data.
   *
   * @param port The port that has connected.
   */
  private readonly _onConnect = (port: chrome.runtime.Port): void => {
    console.debug('Log viewer connection', port.name);
    if (port.name.startsWith(DiagnosticsPage.portName)) {
      this._diagnosticsService.registerPort(port);
      port.onMessage.addListener((message: any, sender: chrome.runtime.Port) => {
        if (message === DiagnosticsPage.requestString) {
          this._getDiagnosticsInfo()
            .then((info) => {
              sender.postMessage(info.toLocal);
            })
            .catch((e) => {
              console.debug('Could not load diagnostic info.', e);
            });
        }
      });
    }

    if (port.name === RealTimeLogsPage.portName) {
      this._filterController.localLogViewerService.start(port);
    }
  };

  /**
   * Called when the extension needs to be hard reset because provisioning information has changed.
   */
  private readonly _onHardResetRequired = async (): Promise<void> => {
    // Stop the Content Aware service if it's running
    if (this._contentAwareService !== undefined) {
      console.debug('FilterApplication - Stopping Content Aware service due to hard reset');
      await this._contentAwareService.stop();
      this._contentAwareService = undefined;
    }

    await this._cloudConnectionController.stop(true);
    await hardResetExtension();
  };

  /**
   * Called whenever the filtering mode changes.
   *
   * @param newMode The mode we're changing to.
   * @param oldMode The mode we're changing from.
   */
  private readonly _onFilterModeChanged = async (
    newMode: FilterMode,
    oldMode: FilterMode,
  ): Promise<void> => {
    // Only send secret knock if we're in full filter mode (aka mode 2).
    // This is to ensure the device stays protected by the on-premise filter (if applicable) until
    //  the extension is ready to take over complete filtering responsibilities. Sometimes, the
    //  extension can be stuck in mini filter mode (aka mode 1) for some time. We don't want to
    //  bypass the on-premise filter during that time.
    if (newMode === FilterMode.full) {
      this._secretKnockService.enable();
    } else {
      this._secretKnockService.disable();
    }

    await this._updateDiagnostics();
  };

  /**
   * Pushes a message out to any diagnostics pages with updated information.
   *
   * The diagnostics page will still update automatically, this should be used for more important updates.
   */
  private readonly _updateDiagnostics = async (): Promise<void> => {
    const diagnosticsInfo = await this._getDiagnosticsInfo();
    this._diagnosticsService.broadcastMessage(diagnosticsInfo);
    this._filterController.remoteLogViewerService.onDiagnostics(diagnosticsInfo);
  };

  /**
   * Returns a populated instance of a cache of diagnostics information.
   */
  private readonly _getDiagnosticsInfo = async (): Promise<DiagnosticsInfo> => {
    return new DiagnosticsInfo(
      this._ipService,
      this._secretKnockService,
      this._filterController.accessLogManager,
      this.getConnectionState(),
      this.getFilterMode(),
      await getBadgeTitle(),
      this._provisioningManager.provisioningInfo,
      this._customerDataController.productConfig,
      this._customerDataController.policyService,
      this._customerDataController.cloudGroupsService,
    );
  };

  /**
   * Called when a remote log viewer session is started.
   *
   * This will start an interval to update the device diagnostics regularly while the session is active.
   */
  private readonly _startRemoteLogViewerSession = (): void => {
    if (this._diagnosticsUpdateInterval !== undefined) {
      return;
    }

    this._diagnosticsUpdateInterval = setInterval(() => {
      // A sanity check to make sure the interval stops running if the session has ended.
      if (!this._filterController.remoteLogViewerService.isInSession) {
        this._endRemoteLogViewerSession();
        return;
      }

      this._updateDiagnostics().catch(console.warn);
    }, this._diagnosticsIntervalTimeout);
  };

  /**
   * Called when the remote log viewer session ends.
   *
   * This will clear the diagnostics interval.
   */
  private readonly _endRemoteLogViewerSession = (): void => {
    clearInterval(this._diagnosticsUpdateInterval);
    this._diagnosticsUpdateInterval = undefined;
  };

  /**
   * Triggered when IP addresses have been received from a native agent.
   *
   * @param localIpAddresses A list of local IP addresses assigned to this device.
   */
  private readonly _onIpAddresses = async (localIpAddresses: string[]): Promise<void> => {
    await this._ipService.updatePrivateIpAddressesFromNativeClient(localIpAddresses);
  };

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Adds standard parameters to the telemetry service using the provisioning info and platform config.
   * If the telemetry service has not been started already it will be started here.
   *
   * @todo Pass the provisioning info and platform config in as a parameter.
   */
  private readonly _initTelemetryParameters = (provisioningInfo: ProvisioningInfo): void => {
    if (this._platformConfig === undefined) {
      console.warn('Could not add standard telemetry parameters. Platform config not loaded.');
      return;
    }

    this._telemetryService.initStandardParameters(provisioningInfo, this._platformConfig);
  };

  /**
   * Logs a telemetry event for the extension-launch.
   *
   * @todo Pass the platform config in as a parameter.
   */
  private readonly _logExtensionLaunchEvent = (): void => {
    if (this._platformConfig !== undefined) {
      const data: any = {
        isManagedChromebook:
          this._platformConfig.isDeviceManaged &&
          // TODO: Avoid using the old DeviceType enumeration here:
          this._platformConfig.deviceType === DeviceType.cldfltChromebook,
        operatingMode: '',
        os: this._platformConfig.platformInfo.os,
      };

      // The operating mode probably isn't known yet if we're in native or companion mode.
      if (this._platformConfig.operatingMode !== undefined) {
        data.operatingMode = OperatingMode[this._platformConfig.operatingMode];
      }

      if (this._platformConfig.hardwarePlatform !== undefined) {
        data.model = this._platformConfig.hardwarePlatform.model;
        data.make = this._platformConfig.hardwarePlatform.manufacturer;
      }

      this._telemetryService.logEvent(TelemetryEventType.Provisioned, data);
    }
  };

  /**
   * Logs an extension installed event to the telemetry service.
   *
   * @param details The details to log with the event.
   */
  private readonly _logInstalledOrUpdatedEvent = (details: OnInstalledOrUpdatedDetails): void => {
    this._telemetryService.logEvent(TelemetryEventType.ExtensionInstalled, details);
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Get a string describing the current connection state of the extension.
   * This will be displayed in the diagnostics page.
   *
   * @todo Add the following states:
   *   - connecting = We're in the process of trying to connect.
   *   - failed = Connection failed due to an error, and we won't retry.
   */
  public readonly getConnectionState = (): string => {
    if (this._cloudConnectionController.isReady) {
      return 'connected';
    }

    // TODO: Only use "disconnected" if we're not licensed or startup failed.
    return 'disconnected';
  };

  /**
   * Get a string describing the filtering mode of the extension.
   * This will be displayed in the diagnostics page.
   */
  public readonly getFilterMode = (): string => {
    switch (this._filterController.filterService.filterMode) {
      case FilterMode.unknown:
        // TODO: This is a new state which wasn't in mv2. Confirm if we want to display it.
        return 'Mode 0';

      case FilterMode.mini:
        return 'Mode 1';

      case FilterMode.full:
        return 'Mode 2';

      case FilterMode.disabled:
        // Deliberately don't show any filter mode if the customer isn't licensed.
        return '';

      default:
        console.warn('FilterApplication - Cannot determined filter mode for diagnostics page.');
        return '';
    }
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * Handles user input in the omnibox (address bar) and adds autocomplete suggestions.
   */
  private readonly _omniboxService: OmniboxService = new OmniboxService();

  /**
   * Stores details about the system we're running on and our operating mode.
   */
  private _platformConfig: PlatformConfig | undefined;

  /**
   * Stores the config details for the extension.
   */
  private readonly _extensionConfig: IExtensionConfig = extensionConfig;

  /**
   * Handles any logging that is needed in the extension.
   */
  private readonly _telemetryService: ITelemetryService = new AppInsightsTelemetryService(
    new AppInsightsConfig(TELEMETRY_CONNECTION_STRING),
  );

  /**
   * Manages a local copy of data about the device, customer, user, and blocklist.
   */
  private readonly _localCacheService = new LocalCacheService(chrome.storage.local);

  /**
   * Manages use of chrome alarms API while taking into account the service worker lifecycle.
   */
  private readonly _alarmService = new AlarmService();

  /**
   * The user agent string for the browser the extension is running in.
   */
  private readonly _userAgent: string = simplifyUserAgentString(navigator.userAgent);

  /**
   * Manages the process of retrieving and parsing provisioning info, and monitoring for changes.
   */
  private readonly _provisioningManager = new ProvisioningManager(
    this._localCacheService.provisioningInfo,
    this._telemetryService,
    extensionConfig,
  );

  /**
   * Manages the offscreen document, which is responsible for querying the public IP address.
   */
  private readonly _offscreenDocumentController = new OffscreenDocumentController();

  /**
   * Manages getting and caching the device's ip addresses.
   */
  private readonly _ipService: IpService = new IpService(
    this._offscreenDocumentController,
    this._localCacheService.ipAddresses,
  );

  /**
   * Manages retrieving and storing customer data, such as the client settings and policy.
   */
  private readonly _customerDataController: CustomerDataController = new CustomerDataController(
    this._extensionConfig,
    this._localCacheService,
    this._telemetryService,
    this._ipService,
  );

  /**
   * Manages our connection to the cloud, including Firebase authentication and device registration.
   */
  private readonly _cloudConnectionController = new CloudConnectionController(
    this._extensionConfig,
    this._localCacheService.firebase,
    this._localCacheService.deviceRegistration,
    this._alarmService,
    this._telemetryService,
    this._ipService,
  );

  /**
   * Url and content analysis functionality.
   *
   * @note This must be constructed *after* _customerDataController.
   */
  private readonly _filterController: FilterController = new FilterController(
    this._extensionConfig,
    this._telemetryService,
    this._alarmService,
    this._ipService,
    this._localCacheService.safeguardingContext,
    this._customerDataController.userDocumentListener,
    performance.now(),
  );

  /**
   * Performs secret knock to notify upstream on-prem filters that the extension is being used.
   */
  private readonly _secretKnockService: SecretKnockService = new SecretKnockService(
    this._localCacheService.secretKnock,
    this._ipService,
    this._telemetryService,
  );

  /**
   * Keeps the service worker alive by periodically writing to local storage.
   */
  private readonly _keepAliveService: KeepAliveService = new KeepAliveService(
    this._localCacheService.keepAlive,
  );

  /**
   * Manages the local diagnostics page connections and messages.
   */
  private readonly _diagnosticsService: DiagnosticsService = new DiagnosticsService();

  /**
   * Manages communication with the Content Aware extension.
   * This is initialized in _onCustomerDataLoaded when we have all the required data.
   */
  private _contentAwareService?: ContentAwareService;

  /**
   * Holds details from the chrome onInstalled event if it was triggered and the extension has been installed or updated.
   * If nothing needs to be logged then this will be undefined. Otherwise the details should be logged once we have the provisioning info.
   */
  private _onInstalledOrUpdatedDetails: OnInstalledOrUpdatedDetails | undefined;

  /**
   * An interval for regularly updating the diagnostics info when a remote session is active.
   */
  private _diagnosticsUpdateInterval?: ReturnType<typeof setInterval>;

  /**
   * The time between diagnostic updates.
   */
  private readonly _diagnosticsIntervalTimeout: number = 60000;
}

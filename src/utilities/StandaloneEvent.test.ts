import { waitForMockToBeCalled } from 'test-helpers/mock-utilities';
import StandaloneEvent from './StandaloneEvent';

describe('StandaloneEvent', () => {
  const dummyErrorReporter = (e: any): void => {};

  // -----------------------------------------------------------------------------------------------

  it('is independent of any other instances', () => {
    const listener1 = jest.fn();
    const testEvent1 = new StandaloneEvent();
    testEvent1.addListener(listener1);

    const listener2 = jest.fn();
    const testEvent2 = new StandaloneEvent();
    testEvent2.addListener(listener2);

    testEvent1.dispatch();
    expect(listener1).toHaveBeenCalled();
    expect(listener2).not.toHaveBeenCalled();
  });

  // -----------------------------------------------------------------------------------------------

  describe('constructor()', () => {
    it('allows a custom error reporter to be omitted', () => {
      expect(() => new StandaloneEvent()).not.toThrow();
    });

    it('allows a custom error reporter to be specified', () => {
      const testErrorReporter = (e: any): void => {};
      expect(() => new StandaloneEvent(testErrorReporter)).not.toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('addListener()', () => {
    it('registers a callback which will be called when the event is triggered', () => {
      const listener = jest.fn();
      const testEvent = new StandaloneEvent();
      testEvent.addListener(listener);
      testEvent.dispatch();
      expect(listener).toHaveBeenCalled();
    });

    it('registers a chained event which will be triggered when the original event is triggered', () => {
      const listener = jest.fn();
      const chainedEvent = new StandaloneEvent();
      chainedEvent.addListener(listener);

      const originalEvent = new StandaloneEvent();
      originalEvent.addListener(chainedEvent);

      originalEvent.dispatch();
      expect(listener).toHaveBeenCalled();
    });

    it('does nothing if the same listener has already been added', () => {
      const listener = jest.fn();
      const testEvent = new StandaloneEvent();
      testEvent.addListener(listener);
      testEvent.addListener(listener);
      expect(testEvent.length).toEqual(1);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('removeListener()', () => {
    it('prevents the callback from being called if the event is triggered in future', () => {
      const listener = jest.fn();
      const testEvent = new StandaloneEvent();
      testEvent.addListener(listener);
      testEvent.removeListener(listener);
      testEvent.dispatch();
      expect(listener).not.toHaveBeenCalled();
    });

    it('prevents the chained event from being called if the original event is triggered in future', () => {
      const listener = jest.fn();
      const chainedEvent = new StandaloneEvent();
      chainedEvent.addListener(listener);

      const originalEvent = new StandaloneEvent();
      originalEvent.addListener(chainedEvent);
      originalEvent.removeListener(chainedEvent);

      originalEvent.dispatch();
      expect(listener).not.toHaveBeenCalled();
    });

    it('does not affect any other listeners', () => {
      const listener1 = jest.fn();
      const listener2 = jest.fn();
      const listener3 = jest.fn();

      const testEvent = new StandaloneEvent();
      testEvent.addListener(listener1);
      testEvent.addListener(listener2);
      testEvent.addListener(listener3);

      testEvent.removeListener(listener1);
      testEvent.dispatch();

      expect(listener1).not.toHaveBeenCalled();
      expect(listener2).toHaveBeenCalled();
      expect(listener3).toHaveBeenCalled();
    });

    it('does nothing if the specified callback has not been added', () => {
      const listener = jest.fn();
      const testEvent = new StandaloneEvent();
      expect(() => {
        testEvent.removeListener(listener);
      }).not.toThrow();
    });

    it('does nothing if the specified callback has already been removed', () => {
      const listener = jest.fn();
      const testEvent = new StandaloneEvent();
      testEvent.addListener(listener);
      testEvent.removeListener(listener);
      expect(() => {
        testEvent.removeListener(listener);
      }).not.toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('clearListeners()', () => {
    it('removes all listeners which have been added', () => {
      const listener1 = jest.fn();
      const listener2 = jest.fn();
      const listener3 = jest.fn();

      const testEvent = new StandaloneEvent();
      testEvent.addListener(listener1);
      testEvent.addListener(listener2);
      testEvent.addListener(listener3);

      testEvent.clearListeners();
      testEvent.dispatch();

      expect(listener1).not.toHaveBeenCalled();
      expect(listener2).not.toHaveBeenCalled();
      expect(listener3).not.toHaveBeenCalled();
    });

    it('does nothing if no listeners have been added', () => {
      const testEvent = new StandaloneEvent();
      expect(() => {
        testEvent.clearListeners();
      }).not.toThrow();
    });

    it('does nothing if the listeners have already been cleared', () => {
      const testEvent = new StandaloneEvent();
      testEvent.addListener(() => {});
      testEvent.clearListeners();
      expect(() => {
        testEvent.clearListeners();
      }).not.toThrow();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('length()', () => {
    it('returns 0 if no listeners have been added yet', () => {
      const testEvent = new StandaloneEvent();
      expect(testEvent.length).toEqual(0);
    });

    it('returns the number of listeners which have been added', () => {
      const testEvent = new StandaloneEvent();
      testEvent.addListener(() => {});
      testEvent.addListener(() => {});
      testEvent.addListener(new StandaloneEvent());
      expect(testEvent.length).toEqual(3);
    });

    it('returns the number of listeners which have been added and not yet removed', () => {
      const testEvent = new StandaloneEvent();

      const listener1 = (): void => {};
      const listener2 = (): void => {};
      const listener3 = (): void => {};

      testEvent.addListener(listener1);
      testEvent.addListener(listener2);
      testEvent.addListener(listener3);

      testEvent.removeListener(listener2);

      expect(testEvent.length).toEqual(2);
    });

    it('returns 0 if the listeners have been cleared', () => {
      const testEvent = new StandaloneEvent();
      testEvent.addListener(() => {});
      testEvent.addListener(() => {});
      testEvent.addListener(() => {});
      testEvent.clearListeners();
      expect(testEvent.length).toEqual(0);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('dispatch()', () => {
    it('does nothing if no listeners have been added', () => {
      const testEvent = new StandaloneEvent();
      testEvent.dispatch();
    });

    it('calls all listeners', () => {
      const listener1 = jest.fn();
      const listener2 = jest.fn();

      const chainedListener = jest.fn();
      const chainedEvent = new StandaloneEvent();
      chainedEvent.addListener(chainedListener);

      const testEvent = new StandaloneEvent();
      testEvent.addListener(listener1);
      testEvent.addListener(listener2);
      testEvent.addListener(chainedEvent);

      testEvent.dispatch();
      expect(listener1).toHaveBeenCalled();
      expect(listener2).toHaveBeenCalled();
      expect(chainedListener).toHaveBeenCalled();
    });

    it('calls listeners in the order they were added', () => {
      const listener1 = jest.fn();
      const listener2 = jest.fn();

      const chainedListener = jest.fn();
      const chainedEvent = new StandaloneEvent();
      chainedEvent.addListener(chainedListener);

      const testEvent = new StandaloneEvent();
      testEvent.addListener(listener1);
      testEvent.addListener(listener2);
      testEvent.addListener(chainedEvent);

      testEvent.dispatch();

      expect(listener1).toHaveBeenCalledBefore(listener2);
      expect(listener2).toHaveBeenCalledBefore(chainedListener);
      expect(chainedListener).toHaveBeenCalled();
    });

    it('does not invoke a listener more than once', () => {
      const listener = jest.fn();

      const testEvent = new StandaloneEvent();
      testEvent.addListener(listener);
      testEvent.addListener(listener);
      testEvent.addListener(listener);

      testEvent.dispatch();
      expect(listener).toHaveBeenCalled();
      expect(listener).toHaveBeenCalledTimes(1);
    });

    it('passes the specified arguments to listeners', () => {
      const listener = jest.fn((s: string, n: number) => undefined);
      const testEvent = new StandaloneEvent<[string, number]>();
      testEvent.addListener(listener);
      testEvent.dispatch('foobar', 246);

      expect(listener).toHaveBeenCalledWith('foobar', 246);
    });

    it('allows synchronous and asynchronous listeners', () => {
      const listener1 = jest.fn(async () => {
        await Promise.resolve();
      });
      const listener2 = jest.fn();

      const testEvent = new StandaloneEvent();
      testEvent.addListener(listener1);
      testEvent.addListener(listener2);

      testEvent.dispatch();
      expect(listener1).toHaveBeenCalled();
      expect(listener2).toHaveBeenCalled();
    });

    it('does not stop or fail if a callback fails by throwing an exception', () => {
      const listener1 = jest.fn(() => {
        throw new Error();
      });
      const listener2 = jest.fn();

      const testEvent = new StandaloneEvent(dummyErrorReporter);
      testEvent.addListener(listener1);
      testEvent.addListener(listener2);

      expect(() => {
        testEvent.dispatch();
      }).not.toThrow();
      expect(listener2).toHaveBeenCalled();
    });

    it('does not stop if a callback fails by rejecting a promise', () => {
      const listener1 = jest.fn(async () => await Promise.reject(new Error()));
      const listener2 = jest.fn();

      const testEvent = new StandaloneEvent(dummyErrorReporter);
      testEvent.addListener(listener1);
      testEvent.addListener(listener2);

      expect(() => {
        testEvent.dispatch();
      }).not.toThrow();
      expect(listener2).toHaveBeenCalled();
    });

    it('passes callback errors to the custom error reporter if specified', () => {
      const listener = (): void => {
        throw new Error('this is a test error');
      };

      const errorReporter = jest.fn();

      const testEvent = new StandaloneEvent(errorReporter);
      testEvent.addListener(listener);

      testEvent.dispatch();
      expect(errorReporter).toHaveBeenCalledWith(new Error('this is a test error'));
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('deferDispatch()', () => {
    it('defers dispatch until a subsequent event loop iteration', async () => {
      const standaloneEvent = new StandaloneEvent();
      const listener = jest.fn();
      standaloneEvent.addListener(listener);
      standaloneEvent.deferDispatch();
      expect(listener).not.toHaveBeenCalled();
      await waitForMockToBeCalled(listener);
      expect(listener).toHaveBeenCalled();
    });

    it('passes arguments to the listeners', async () => {
      const standaloneEvent = new StandaloneEvent<[string, number]>();
      const listener = jest.fn();
      standaloneEvent.addListener(listener);
      standaloneEvent.deferDispatch('foobar', 123);
      await waitForMockToBeCalled(listener);
      expect(listener).toHaveBeenCalledWith('foobar', 123);
    });

    it('does not execute callbacks which are removed before the deferral', async () => {
      const standaloneEvent = new StandaloneEvent();
      const listener1 = jest.fn();
      const listener2 = jest.fn();
      standaloneEvent.addListener(listener1);
      standaloneEvent.addListener(listener2);
      standaloneEvent.deferDispatch();
      standaloneEvent.removeListener(listener1);
      await waitForMockToBeCalled(listener2);
      expect(listener1).not.toHaveBeenCalled();
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('deferDispatchAndWait()', () => {
    it('defers dispatch until a subsequent event loop iteration', async () => {
      const standaloneEvent = new StandaloneEvent();
      const listener = jest.fn();
      standaloneEvent.addListener(listener);
      const deferral = standaloneEvent.deferDispatchAndWait();
      expect(listener).not.toHaveBeenCalled();
      await deferral;
      expect(listener).toHaveBeenCalled();
    });

    it('passes arguments to the listeners', async () => {
      const standaloneEvent = new StandaloneEvent<[string, number]>();
      const listener = jest.fn();
      standaloneEvent.addListener(listener);
      await standaloneEvent.deferDispatchAndWait('foobar', 123);
      expect(listener).toHaveBeenCalledWith('foobar', 123);
    });

    it('does not execute callbacks which are removed before the deferral', async () => {
      const standaloneEvent = new StandaloneEvent();
      const listener = jest.fn();
      standaloneEvent.addListener(listener);
      const deferral = standaloneEvent.deferDispatchAndWait();
      standaloneEvent.clearListeners();
      await deferral;
      expect(listener).not.toHaveBeenCalled();
    });

    it('does not reject the returned promise if a callback fails', async () => {
      const listener = async (): Promise<void> => {
        await Promise.reject(new Error('test error'));
      };
      const standaloneEvent = new StandaloneEvent(dummyErrorReporter);
      standaloneEvent.addListener(listener);
      await expect(standaloneEvent.deferDispatchAndWait()).toResolve();
    });
  });
});

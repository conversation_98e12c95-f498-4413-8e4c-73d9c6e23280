/**
 * A generator function which extracts text one line at a time from a string object.
 * This is useful for situations where you need to sequentially process each line of a very large
 *  string. It avoids the potential memory overhead of string.split().
 *
 * @param text The string to extract lines from.
 *
 * Example usage:
 *
 * @code
 *    for (const line of readLineByLine('very large string')) {
 *      // process line here...
 *    }
 * @endcode
 *
 * @note Using string.split() to divide the entire string into an array of lines in one operation is
 *  probably faster. However, it uses more memory, meaning it may not be suitable for very large
 *  strings.
 */
export const readLineByLine = function* (text: string): Generator<string> {
  const textLength = text.length;
  let lineStart = 0;
  // Read character-by-character until we find the end of a line.
  for (let index = 0; index < textLength; ++index) {
    const currentCharacter = text[index];
    if (currentCharacter === '\n' || currentCharacter === '\r') {
      // Return everything except the line break character.
      yield text.slice(lineStart, index);

      // Windows-style line endings consist of two characters: carriage return then linefeed (\r\n).
      // Ensure we skip both otherwise we'll output a spurious blank line.
      const nextCharacter = text[index + 1];
      if (currentCharacter === '\r' && nextCharacter === '\n') {
        index += 1;
      }

      // The next line starts after the line break.
      lineStart = index + 1;
    }
  }

  // If the last character in the string wasn't a line break then there will be some text left that
  //  we haven't yielded yet. Ensure it doesn't get missed.
  if (lineStart < textLength) {
    yield text.slice(lineStart);
  }
};

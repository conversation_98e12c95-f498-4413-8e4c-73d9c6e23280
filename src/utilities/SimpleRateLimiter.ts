import RateLimiter from './RateLimiter';

/**
 * A simple global rate limiter that allows a specified number of requests
 * within a given rolling time window.
 */
export default class SimpleRateLimiter implements RateLimiter {
  /**
   * The timestamps of the requests made within the current window.
   * This is a sorted array to allow efficient removal of old timestamps.
   * In ascending order.
   */
  private readonly _requestTimestamps: number[] = [];

  constructor(
    /** Maximum number of requests allowed within the window. */
    private readonly _maxRequests: number,
    /** Time window in milliseconds. */
    private readonly _windowMs: number,
  ) {
    if (_maxRequests <= 0) {
      throw new Error('maxRequests must be greater than 0.');
    }
    if (_windowMs <= 0) {
      throw new Error('windowMs must be greater than 0.');
    }
  }

  /**
   * Checks if a slot in the rate limiter is available.
   * If yes, it records the request and consumes a slot.
   * @returns True if a slot was successfully consumed, false otherwise.
   */
  public tryConsume = (): boolean => {
    const now = Date.now();
    const windowStart = now - this._windowMs;

    const firstValidIdx = this._findFirstValidIndex(windowStart);

    if (firstValidIdx > 0) {
      this._requestTimestamps.splice(0, firstValidIdx);
    }

    // Check if the limit is exceeded
    if (this._requestTimestamps.length < this._maxRequests) {
      this._requestTimestamps.push(now);
      return true;
    }

    return false;
  };

  /**
   * Returns the number of requests that can still be made within the current window.
   * @returns The number of remaining requests.
   */
  public getRemainingRequests = (): number => {
    const now = Date.now();
    const windowStart = now - this._windowMs;

    const firstValidIdx = this._findFirstValidIndex(windowStart);
    const recentRequestsCount = this._requestTimestamps.length - firstValidIdx;

    return Math.max(0, this._maxRequests - recentRequestsCount);
  };

  /**
   * Returns the time in milliseconds until the next reset of the rate limit.
   * @returns The time until the next reset, or 0 if no limit is currently applied.
   */
  public getResetTime = (): number => {
    const now = Date.now();
    const windowStart = now - this._windowMs;

    // Find the oldest request that's still counting towards the limit
    const firstValidIdx = this._findFirstValidIndex(windowStart);
    const relevantRequests = this._requestTimestamps.slice(firstValidIdx);

    if (relevantRequests.length >= this._maxRequests && relevantRequests.length > 0) {
      // The reset time is when the oldest request in the full window expires
      return relevantRequests[0] + this._windowMs - now;
    }

    return 0; // Should not happen if already limited, but as a fallback
  };

  /**
   * Finds the first index in the request timestamps that is greater than the window start time.
   * This is used to efficiently remove old timestamps that are no longer valid.
   * @param windowStart The start time of the current window.
   * @returns The index of the first valid timestamp.
   */
  private _findFirstValidIndex(windowStart: number): number {
    let idx = 0;
    while (idx < this._requestTimestamps.length && this._requestTimestamps[idx] <= windowStart) {
      idx++;
    }
    return idx;
  }
}

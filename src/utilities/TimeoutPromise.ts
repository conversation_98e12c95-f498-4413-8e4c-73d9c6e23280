import DeferredPromise from './DeferredPromise';

/**
 * A promise wrapper for a timeout which can be cancelled.
 * This is designed to make it safe and easy to include a delay in a promise chain, but have the
 *  ability to cancel the delay cleanly without having to reject the promises.
 *
 * Example usage:
 *
 * @code
 *  const timeout = new TimeoutPromise(1000);
 *  timeout.cancel();
 *  const timeoutElapsed = await timeout.promise;
 *  console.log(timeoutElapsed ? 'Timeout elapsed' : 'Timeout cancelled');
 * @endcode
 *
 * Example usage with an abort signal:
 *
 * @code
 *  const abortController = new AbortController();
 *  const timeout = new TimeoutPromise(1000, abortController.signal);
 *  abortController.abort();
 *  const timeoutElapsed = await timeout.promise;
 *  console.log(timeoutElapsed ? 'Timeout elapsed' : 'Timeout cancelled');
 * @endcode
 *
 * @note The promise always resolves to a boolean. It will resolve to true if the timeout elapsed,
 *  or false if the timeout was cancelled.
 *
 * @todo Refactor this to resolve to an enum instead of a boolean, so that it's clearer?
 */
export default class TimeoutPromise {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Start a new timed promise, with an optional cancellation signal.
   *
   * @param ms The time (in milliseconds) to wait before resolving the promise. If the time elapses
   *  fully, without being cancelled, then the promise will resolve to true.
   * @param signal Optional abort signal which will cancel the timeout when triggered. This can be
   *  provided so that cancellation can be invoked separately. It isn't essential though. You can
   *  simply call cancel() instead.
   */
  public constructor(ms: number, signal?: AbortSignal) {
    // If the signal was already aborted before we start, then there's nothing to wait for.
    if (signal?.aborted === true) {
      this._deferredPromise.resolve(false);
      return;
    }

    this._timeout = setTimeout(this._onTimeout, ms);
    signal?.addEventListener('abort', this.cancel);
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Get the underlying promise which is wrapped by this object.
   * This allows the caller to chain other promises after the timer.
   * The promise will resolve to true if the timer elapsed, or false if it was cancelled.
   *
   * @returns The underlying wrapped promise which will resolve when the timer elapses or is
   *  cancelled.
   */
  public get promise(): Promise<boolean> {
    return this._deferredPromise.promise;
  }

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Cancel the timer.
   * If the promise hasn't already resolved, then it will resolve immediately to false.
   * This will have no effect if the promise has already resolved or has already been cancelled.
   */
  public readonly cancel = (): void => {
    if (this._timeout !== undefined) {
      clearTimeout(this._timeout);
      this._timeout = undefined;
    }

    this._deferredPromise.resolve(false);
  };

  // -----------------------------------------------------------------------------------------------
  // Internal operations.

  /**
   * Internal handler for the timer event.
   * This will resolve the promise to true, if it hasn't already been cancelled.
   */
  private readonly _onTimeout = (): void => {
    this._deferredPromise.resolve(true);
    this._timeout = undefined;
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * This wraps the promise which we'll resolve when the timer elapses or is cancelled.
   * The underlying promise can be access through the promise property on this class.
   */
  private readonly _deferredPromise = new DeferredPromise<boolean>();

  /**
   * A handle for the timer which will resolve the promise.
   * This is initialised in the constructor, and cleared when the timer elapses or is cancelled.
   */
  private _timeout?: ReturnType<typeof setTimeout>;
}

/**
 * Convenience function which wraps the creation of a timeout promise.
 * Note that this returns the inner promise rather than the TimeoutPromise instance. This means it's
 *  only possible to cancel the timeout if you provide an abort signal.
 *
 * Example usage:
 *
 * @code
 *  const abortController = new AbortController();
 *  const timeoutElapsed = await setTimeoutPromise(1000, abortController.signal);
 *  console.log(timeoutElapsed ? 'Timeout elapsed' : 'Timeout cancelled');
 * @endcode
 *
 * In the above example, you would cancel the timeout by calling `abortController.abort()`.
 *
 * @param ms The amount of time (in milliseconds) to wait before resolving the promise.
 * @param signal Optional signal which can be used to cancel the timeout. If this is not provided
 *  then there is no way to cancel the timeout.
 * @returns A promise which resolves to a boolean value. The boolean will be true if the timeout
 *  elapsed, or false if it was cancelled. The promise is never expected to reject.
 */
export const setTimeoutPromise = async (ms: number, signal?: AbortSignal): Promise<boolean> => {
  return await new TimeoutPromise(ms, signal).promise;
};

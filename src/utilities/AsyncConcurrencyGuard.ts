/**
 * A utility to prevent an asynchronous operation from being executed multiple times simultaneously.
 * This is similar to mutual exclusion in languages which support multi-threading. If a call is
 *  already in progress, then a new call will wait until the previous one has finished before
 *  proceeding. If multiple calls are waiting then they will be executed in the order that they
 *  started.
 *
 * A single instance of this class provides one mutex-like guard. You can use it to protect any
 *  number of different functions. However, only a single invocation of any of those functions will
 *  be allowed at one time.
 *
 * The guard only works if you call the protected function via this class. It won't have any effect
 *  if you call the original function directly.
 *
 * @warning It is the caller's responsibility to ensure that guard code doesn't try to call itself,
 *  i.e. an asynchronous operation protected by call() or wrap() must not await another invocation
 *  of call() or wrap() on the same instance of this class, either directly or indirectly. That
 *  would result in a deadlock which never resolves because the code is waiting for itself in a
 *  circular dependency.
 */
export default class AsyncConcurrencyGuard {
  /**
   * Make a guarded call to the specified function.
   * This will call the function only after any previous guarded calls from this instance have
   *  finished. This is convenient for guarding ad hoc calls to a function, but it can look a little
   *  ugly. Consider using wrap() instead if you're going to call the function from multiple places.
   *
   * @param fn The function to call.
   * @param args The arguments to pass to the function.
   * @returns Returns a promise which resolves when the specified function has finished. The
   *  resolved value will be the return value of the function (if it was synchronous), or the value
   *  it resolved to (if it was asynchronous). The promise will reject if the specified function
   *  threw an error or returned a rejected promise.
   *
   * @todo Add an optional timeout which will abandon the operation if it waits too long for
   *  previous guarded calls to finish. This will help protect against deadlock.
   *
   * @note This can guard calls to both synchronous and asynchronous functions. It's only useful for
   *  synchronous functions if there are other asynchronous functions which are also guarded and
   *  which might be in progress as the same time.
   */
  public readonly call = async <T extends (...args: any[]) => any>(
    fn: T,
    ...args: Parameters<T>
  ): Promise<Awaited<ReturnType<T>>> => {
    this._lastAsyncOperation = this._lastAsyncOperation
      .catch(() => {})
      .then(async (): Promise<Awaited<ReturnType<T>>> => await Promise.resolve(fn(...args)));
    return await this._lastAsyncOperation;
  };

  /**
   * Create a wrapper function which guards the specified function.
   * This doesn't change the specified function itself. It returns a new function which acts exactly
   *  like the specified function, but implicitly makes a guarded call when invoked. The guarded
   *  call will wait until any previous guarded calls from this instance have finished before
   *  proceeding.
   * You can store the returned wrapper function, e.g. as a class property, allowing it to be called
   *  easily from multiple places.
   *
   * @param fn The function to wrap.
   * @returns Returns a wrapper function. When called, it will invoke the specified function with a
   *  concurrency guard, and return the resulting promise. The promise will resolve to whatever
   *  value the function returned (if synchronous) or resolved to (if asynchronous). It will reject
   *  if the function threw an error or returned a rejected promise.
   *
   * @todo Add an optional timeout which will abandon the operation if it waits too long for
   *  previous guarded calls to finish. This will help protect against deadlock.
   *
   * @note This can wrap calls to both synchronous and asynchronous functions. It's only useful for
   *  synchronous functions if there are other asynchronous functions which are also guarded and
   *  which might be in progress as the same time.
   */
  public readonly wrap = <T extends (...args: any[]) => any>(
    fn: T,
  ): ((...args: Parameters<T>) => Promise<Awaited<ReturnType<T>>>) => {
    return async (...args: Parameters<T>): Promise<Awaited<ReturnType<T>>> => {
      return await this.call(fn, ...args);
    };
  };

  /**
   * A promise which settles when all existing guarded calls have settled.
   * When a new guarded call is made, it gets chained onto the end of this promise. The result of
   *  that new chain is then stored here, meaning subsequent guarded calls extend the chain. This
   *  ensures each call waits for the previous calls, and that they are executed in order.
   *
   * @note If a guarded call rejects then this promise will reject too. It's important to ensure
   *  that situation is handled such that it doesn't prevent subsequent guarded calls from going
   *  ahead.
   */
  private _lastAsyncOperation: Promise<any> = Promise.resolve();
}

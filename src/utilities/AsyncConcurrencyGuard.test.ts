import AsyncConcurrencyGuard from './AsyncConcurrencyGuard';

const resolveAfterDelay = async (delay: number): Promise<void> => {
  await new Promise<void>((resolve) => {
    setTimeout(resolve, delay);
  });
};

describe('AsyncConcurrencyGuard', () => {
  // -----------------------------------------------------------------------------------------------

  afterEach(() => {
    jest.useRealTimers();
  });

  // -----------------------------------------------------------------------------------------------

  describe('call()', () => {
    it('passes the arguments through to the specified function', async () => {
      const guard = new AsyncConcurrencyGuard();
      const fn = jest.fn().mockResolvedValue(undefined);
      await guard.call(fn, 'testing', 123);
      expect(fn).toHaveBeenCalledWith('testing', 123);
    });

    it('returns the result of the specified function if it resolves', async () => {
      const guard = new AsyncConcurrencyGuard();
      const fn = jest.fn().mockResolvedValue('hello world');
      await expect(guard.call(fn)).resolves.toEqual('hello world');
    });

    it('returns a promise which resolves to the result of the specified function if it is synchronous', async () => {
      const guard = new AsyncConcurrencyGuard();
      const fn = jest.fn().mockReturnValue('hello world');
      await expect(guard.call(fn)).resolves.toEqual('hello world');
    });

    it('passes back the rejection reason of the specified function if it rejects', async () => {
      const guard = new AsyncConcurrencyGuard();
      const fn = jest.fn().mockRejectedValue(new Error('test'));
      await expect(guard.call(fn)).rejects.toThrow('test');
    });

    it('returns a promise which rejects with the error thrown by the specified function if it is synchronous', async () => {
      const guard = new AsyncConcurrencyGuard();
      const fn = jest.fn().mockImplementation(() => {
        throw new Error('test');
      });
      await expect(guard.call(fn)).rejects.toThrow('test');
    });

    it('calls the specified function immediately if there are no other pending operations', async () => {
      const guard = new AsyncConcurrencyGuard();
      const fn = jest.fn().mockResolvedValue(undefined);
      await guard.call(fn);
      expect(fn).toHaveBeenCalled();
    });

    it('calls the specified function immediately if previous calls have already finished', async () => {
      const guard = new AsyncConcurrencyGuard();
      const fn = jest.fn().mockResolvedValue(undefined);
      await guard.call(fn);
      await guard.call(fn);
      await guard.call(fn);
      expect(fn).toHaveBeenCalledTimes(3);
    });

    it('calls the specified function after any current calls have finished', async () => {
      jest.useFakeTimers();
      const guard = new AsyncConcurrencyGuard();
      const fn = jest.fn(resolveAfterDelay);
      const promises = [guard.call(fn, 100), guard.call(fn, 0)];

      // Advance time far enough for the first call to start but not finish.
      await jest.advanceTimersByTimeAsync(50);
      expect(fn).toHaveBeenCalledTimes(1);
      // Advance time far enough for the second call to happen.
      await jest.runAllTimersAsync();
      expect(fn).toHaveBeenCalledTimes(2);
      await Promise.all(promises);
    });

    it('calls the specified function even if a previous call rejects', async () => {
      const guard = new AsyncConcurrencyGuard();
      const fn = jest.fn().mockResolvedValue(undefined);
      // Deliberately queue up two calls before awaiting either.
      const promise1 = guard.call(Promise.reject);
      const promise2 = guard.call(fn);
      await expect(promise1).toReject();
      await expect(promise2).toResolve();
      expect(fn).toHaveBeenCalled();
    });

    it('executes concurrent calls in the order they were originally called', async () => {
      const guard = new AsyncConcurrencyGuard();
      const fn1 = jest.fn().mockResolvedValue(undefined);
      const fn2 = jest.fn().mockResolvedValue(undefined);
      const fn3 = jest.fn().mockResolvedValue(undefined);
      await Promise.all([guard.call(fn1), guard.call(fn2), guard.call(fn3)]);
      expect(fn1).toHaveBeenCalled();
      expect(fn2).toHaveBeenCalledAfter(fn1);
      expect(fn3).toHaveBeenCalledAfter(fn2);
    });

    it('allows synchronous functions to be guarded as well', async () => {
      const guard = new AsyncConcurrencyGuard();
      const fn1 = jest.fn().mockResolvedValue(undefined);
      const fn2 = jest.fn(); // <-- synchronous
      const fn3 = jest.fn().mockResolvedValue(undefined);
      await Promise.all([guard.call(fn1), guard.call(fn2), guard.call(fn3)]);
      expect(fn1).toHaveBeenCalled();
      expect(fn2).toHaveBeenCalledAfter(fn1);
      expect(fn3).toHaveBeenCalledAfter(fn2);
    });

    it('does not wait for subsequent concurrent calls to finish before resolving', async () => {
      jest.useFakeTimers();
      const guard = new AsyncConcurrencyGuard();
      const promise1 = guard.call(resolveAfterDelay, 100);
      const promise2 = guard.call(resolveAfterDelay, 100);

      // Only the first call should resolve here:
      await jest.advanceTimersByTimeAsync(100);
      await expect(promise1).toResolve();

      // The second call shouldn't resolve until here:
      await jest.advanceTimersByTimeAsync(100);
      await expect(promise2).toResolve();
    });

    it('prevents multiple concurrent calls from overlapping via call() or wrap()', async () => {
      // This test ensures that call() and wrap() share the same concurrency protection.
      let isCallInProgress = false;

      // Simulate a function which takes a short time to do some work.
      const fn = async (): Promise<void> => {
        expect(isCallInProgress).toBeFalse();
        isCallInProgress = true;
        await resolveAfterDelay(10);
        isCallInProgress = false;
      };

      const guard = new AsyncConcurrencyGuard();
      // Start multiple calls at the same time, some via call() and some via wrap(), then wait until
      //  they've all finished.
      const wrapped = guard.wrap(fn);
      await Promise.all([guard.call(fn), wrapped(), guard.call(fn), wrapped()]);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('wrap()', () => {
    // Most of the wrap() function is already covered by the call() unit tests.

    it('returns a wrapper function which prevents concurrent calls to the specified asynchronous function', async () => {
      jest.useFakeTimers();
      const guard = new AsyncConcurrencyGuard();
      // Note: We need to wrap the mock so that we can see when the mock actually gets called.
      // Doing it the other way round (mocking the wrapper) wouldn't do anything useful.
      const fn = jest.fn(resolveAfterDelay);
      const wrapper = guard.wrap(fn);
      const promises = [wrapper(100), wrapper(100)];

      // Advance time far enough for the first call to start but not finish.
      await jest.advanceTimersByTimeAsync(50);
      expect(fn).toHaveBeenCalledTimes(1);
      // Advance time far enough for the second call to happen.
      await jest.runAllTimersAsync();
      expect(fn).toHaveBeenCalledTimes(2);
      await Promise.all(promises);
    });

    it('returns a wrapper function which passes the arguments through to the specified function', async () => {
      const guard = new AsyncConcurrencyGuard();
      const fn = jest.fn().mockResolvedValue(undefined);
      const wrapper = guard.wrap(fn);
      await wrapper('testing', 123);
      expect(fn).toHaveBeenCalledWith('testing', 123);
    });

    it('returns a wrapper function which returns the result of the specified function if it resolves', async () => {
      const guard = new AsyncConcurrencyGuard();
      const fn = jest.fn().mockResolvedValue('hello world');
      const wrapper = guard.wrap(fn);
      await expect(wrapper()).resolves.toEqual('hello world');
    });

    it('returns a wrapper function which returns a promise which resolves to the result of the specified function if it is synchronous', async () => {
      const guard = new AsyncConcurrencyGuard();
      const fn = jest.fn().mockReturnValue('hello world');
      const wrapper = guard.wrap(fn);
      await expect(wrapper()).resolves.toEqual('hello world');
    });

    it('returns a wrapper function which passes back the rejection reason of the specified function if it rejects', async () => {
      const guard = new AsyncConcurrencyGuard();
      const fn = jest.fn().mockRejectedValue(new Error('test'));
      const wrapper = guard.wrap(fn);
      await expect(wrapper()).rejects.toThrow('test');
    });

    it('returns a wrapper function which returns a promise which rejects with the error thrown by the specified function if it is synchronous', async () => {
      const guard = new AsyncConcurrencyGuard();
      const fn = jest.fn().mockImplementation(() => {
        throw new Error('test');
      });
      const wrapper = guard.wrap(fn);
      await expect(wrapper()).rejects.toThrow('test');
    });
  });
});

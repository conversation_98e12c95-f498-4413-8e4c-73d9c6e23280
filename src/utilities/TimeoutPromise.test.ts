import TimeoutPromise, { setTimeoutPromise } from './TimeoutPromise';

describe('TimeoutPromise', () => {
  describe('promise', () => {
    it('resolves to true if the timer elapsed', async () => {
      const timeout = new TimeoutPromise(10);
      await expect(timeout.promise).resolves.toBeTrue();
    });

    it('resolves after the specified time', async () => {
      const startTime = Date.now();
      const timeout = new TimeoutPromise(100);
      await timeout.promise;
      expect(Date.now() - startTime).toBeGreaterThanOrEqual(99); // <-- allow for rounding
    });

    it('can safely be resolve multiple times', async () => {
      const timeout = new TimeoutPromise(10);
      await expect(timeout.promise).resolves.toBeTrue();
      await expect(timeout.promise).resolves.toBeTrue();
      await expect(timeout.promise).resolves.toBeTrue();
    });

    it('resolves to false if cancel was called', async () => {
      const timeout = new TimeoutPromise(100000); // <-- deliberately longer than test timeout
      timeout.cancel();
      await expect(timeout.promise).resolves.toBeFalse();
    });

    it('resolves to false if an abort signal was already triggered before construction', async () => {
      const abortController = new AbortController();
      abortController.abort();
      const timeoutPromise = new TimeoutPromise(100000, abortController.signal);
      await expect(timeoutPromise.promise).resolves.toBeFalse();
    });

    it('resolves to false if an abort signal was triggered after construction', async () => {
      const abortController = new AbortController();
      const timeoutPromise = new TimeoutPromise(100000, abortController.signal);
      abortController.abort();
      await expect(timeoutPromise.promise).resolves.toBeFalse();
    });

    it('resolve to true if an abort signal was provided but never triggered', async () => {
      const abortController = new AbortController();
      const timeoutPromise = new TimeoutPromise(10, abortController.signal);
      await expect(timeoutPromise.promise).resolves.toBeTrue();
    });
  });

  describe('cancel()', () => {
    it('can safely be called multiple times', async () => {
      const timeout = new TimeoutPromise(100000);
      timeout.cancel();
      timeout.cancel();
      timeout.cancel();
      await expect(timeout.promise).resolves.toBeFalse();
    });

    it('does not affect the promise result if called after the promise has resolved', async () => {
      const timeout = new TimeoutPromise(100);
      await expect(timeout.promise).resolves.toBeTrue();
      timeout.cancel();
      await expect(timeout.promise).resolves.toBeTrue();
    });
  });

  describe('setTimeoutPromise()', () => {
    it('returns a promise which resolves to true if the timeout was not cancelled', async () => {
      await expect(setTimeoutPromise(10)).resolves.toBeTrue();
    });

    it('returns a promise which resolves after the specified time', async () => {
      const startTime = Date.now();
      await setTimeoutPromise(100);
      expect(Date.now() - startTime).toBeGreaterThanOrEqual(99); // <-- allow for rounding
    });

    it('returns a promise which resolves to false if the timeout is cancelled after it starts', async () => {
      const abortController = new AbortController();
      const promise = setTimeoutPromise(1000, abortController.signal);
      abortController.abort();
      await expect(promise).resolves.toBeFalse();
    });

    it('returns a promise which resolves to false if the timeout was cancelled before it started', async () => {
      const abortController = new AbortController();
      abortController.abort();
      const promise = setTimeoutPromise(1000, abortController.signal);
      await expect(promise).resolves.toBeFalse();
    });

    it('returns a promise which resolves before the time elapses if the timeout is cancelled', async () => {
      const delay = 100000;
      const abortController = new AbortController();
      const startTime = Date.now();
      const promise = setTimeoutPromise(delay, abortController.signal);
      abortController.abort();
      await promise;
      expect(Date.now() - startTime).toBeLessThan(delay);
    });

    it('does not cancel the timeout if an abort signal was provided but never triggered', async () => {
      const abortController = new AbortController();
      await expect(setTimeoutPromise(10, abortController.signal)).resolves.toBeTrue();
    });
  });
});

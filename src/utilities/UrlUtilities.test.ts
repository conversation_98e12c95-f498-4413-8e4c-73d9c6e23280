import {
  extractEmbeddedUrl,
  hasTrackingParameters,
  isTrackingDomain,
  isTrackingUrl,
  matchesTrackingPattern,
  TRACKING_PATTERNS,
} from './UrlUtilities';

describe('UrlUtilities', () => {
  describe('isTrackingUrl', () => {
    test('should identify Google Analytics URLs as tracking', () => {
      const trackingUrls = [
        'https://www.google-analytics.com/collect?v=1&tid=UA-123456-1',
        'https://ssl.google-analytics.com/collect?v=1&t=event',
        'https://analytics.google.com/analytics/web/provision/embed',
        'https://www.google.com/ccm/collect?en=page_view',
      ];

      trackingUrls.forEach((url) => {
        expect(isTrackingUrl(url)).toBe(true);
      });
    });

    test('should identify DoubleClick URLs as tracking', () => {
      const doubleClickUrls = [
        'https://rd.doubleclick.net/dts/rtlactivity/fledge#1',
        'https://stats.g.doubleclick.net/j/collect?t=dc',
      ];

      doubleClickUrls.forEach((url) => {
        expect(isTrackingUrl(url)).toBe(true);
      });
    });

    test('should identify Facebook tracking URLs', () => {
      const facebookUrls = [
        'https://connect.facebook.net/en_US/fbevents.js',
        'https://www.facebook.com/tr?id=123456789&ev=PageView',
      ];

      facebookUrls.forEach((url) => {
        expect(isTrackingUrl(url)).toBe(true);
      });
    });

    test('should identify URLs with tracking patterns', () => {
      const patternUrls = [
        'https://example.com/analytics/track?event=pageview',
        'https://example.com/collect?utm_source=google',
        'https://example.com/pixel?ref=https%3A%2F%2Fexample.com',
      ];

      patternUrls.forEach((url) => {
        expect(isTrackingUrl(url)).toBe(true);
      });
    });

    test('should not identify legitimate URLs as tracking', () => {
      const legitimateUrls = [
        'https://www.samaritans.org/how-we-can-help/',
        'https://webchat.samaritans.org/api/service-status/',
        'https://example.com/contact',
        'https://news.bbc.co.uk/article',
      ];

      legitimateUrls.forEach((url) => {
        expect(isTrackingUrl(url)).toBe(false);
      });
    });

    test('should handle invalid URLs gracefully', () => {
      const invalidUrls = ['not-a-url', '', 'ftp://invalid', 'javascript:alert(1)'];

      invalidUrls.forEach((url) => {
        expect(() => isTrackingUrl(url)).not.toThrow();
        expect(isTrackingUrl(url)).toBe(false);
      });
    });
  });

  describe('isTrackingDomain', () => {
    test('should identify tracking domains', () => {
      const trackingUrls = [
        'https://google-analytics.com/test',
        'https://sub.doubleclick.net/path',
        'https://mixpanel.com/api',
      ];

      trackingUrls.forEach((url) => {
        expect(isTrackingDomain(url)).toBe(true);
      });
    });

    test('should not identify non-tracking domains', () => {
      const nonTrackingUrls = [
        'https://example.com/analytics',
        'https://legitimate-site.org/track',
      ];

      nonTrackingUrls.forEach((url) => {
        expect(isTrackingDomain(url)).toBe(false);
      });
    });
  });

  describe('matchesTrackingPattern', () => {
    test('should match tracking patterns', () => {
      const patternUrls = [
        'https://example.com/collect?data=test',
        'https://site.com/analytics/report',
        'https://domain.com/track?id=123',
        'https://test.com/dts/rtlactivity/test',
      ];

      patternUrls.forEach((url) => {
        expect(matchesTrackingPattern(url)).toBe(true);
      });
    });

    test('should not match non-tracking patterns', () => {
      const nonPatternUrls = [
        'https://example.com/contact',
        'https://site.com/about',
        'https://domain.com/products',
      ];

      nonPatternUrls.forEach((url) => {
        expect(matchesTrackingPattern(url)).toBe(false);
      });
    });
  });

  describe('hasTrackingParameters', () => {
    test('should identify URLs with tracking parameters and embedded URLs', () => {
      const trackingParamUrls = [
        'https://example.com/redirect?url=https%3A%2F%2Ftarget.com&utm_source=google',
        'https://site.com/track?ref=https%3A%2F%2Foriginal.com&gclid=abc123',
      ];

      trackingParamUrls.forEach((url) => {
        expect(hasTrackingParameters(url)).toBe(true);
      });
    });

    test('should not identify URLs without both embedded URLs and tracking params', () => {
      const nonTrackingUrls = [
        'https://example.com/page?utm_source=google', // tracking params but no embedded URL
        'https://site.com/redirect?url=https%3A%2F%2Ftarget.com', // embedded URL but no tracking params
        'https://domain.com/page?id=123', // neither
      ];

      nonTrackingUrls.forEach((url) => {
        expect(hasTrackingParameters(url)).toBe(false);
      });
    });
  });

  describe('extractEmbeddedUrl', () => {
    test('should extract embedded URLs from tracking URLs', () => {
      const testCases = [
        {
          input: 'https://example.com/redirect?url=https%3A%2F%2Ftarget.com%2Fpage',
          expected: 'https://target.com/page',
        },
        {
          input: 'https://tracker.com/click?u=https%3A%2F%2Fdestination.org',
          expected: 'https://destination.org',
        },
        {
          input: 'https://analytics.com/track?ref=https%3A%2F%2Foriginal.com%2Fpath',
          expected: 'https://original.com/path',
        },
      ];

      testCases.forEach(({ input, expected }) => {
        expect(extractEmbeddedUrl(input)).toBe(expected);
      });
    });

    test('should return undefined for URLs without embedded URLs', () => {
      const urlsWithoutEmbedded = [
        'https://example.com/page',
        'https://site.com/track?id=123',
        'https://domain.com/analytics?event=click',
      ];

      urlsWithoutEmbedded.forEach((url) => {
        expect(extractEmbeddedUrl(url)).toBeUndefined();
      });
    });

    test('should handle invalid URLs gracefully', () => {
      expect(extractEmbeddedUrl('not-a-url')).toBeUndefined();
      expect(extractEmbeddedUrl('')).toBeUndefined();
    });
  });

  describe('domain-based tracking detection', () => {
    test('should identify common tracking domains', () => {
      const commonTrackingDomains = [
        'https://google-analytics.com/test',
        'https://doubleclick.net/path',
        'https://facebook.com/tr',
        'https://mixpanel.com/api',
        'https://hotjar.com/track',
        'https://segment.com/analytics',
        'https://amplitude.com/events',
        'https://newrelic.com/beacon',
      ];

      commonTrackingDomains.forEach((url) => {
        expect(isTrackingDomain(url)).toBe(true);
        expect(isTrackingUrl(url)).toBe(true);
      });
    });

    test('should identify subdomains of tracking domains', () => {
      const subdomainUrls = [
        'https://sub.google-analytics.com/test',
        'https://stats.g.doubleclick.net/collect',
        'https://connect.facebook.net/en_US/fbevents.js',
        'https://cdn.logrocket.io/script.js',
      ];

      subdomainUrls.forEach((url) => {
        expect(isTrackingDomain(url)).toBe(true);
        expect(isTrackingUrl(url)).toBe(true);
      });
    });

    test('TRACKING_PATTERNS should contain expected patterns', () => {
      expect(TRACKING_PATTERNS.some((pattern) => pattern.test('/collect?test=1'))).toBe(true);
      expect(TRACKING_PATTERNS.some((pattern) => pattern.test('/analytics/report'))).toBe(true);
      expect(TRACKING_PATTERNS.some((pattern) => pattern.test('/dts/rtlactivity/test'))).toBe(true);
    });

    test('should correctly handle edge cases in subdomain matching', () => {
      // Test that partial matches don't create false positives
      expect(isTrackingDomain('https://notgoogle-analytics.com/test')).toBe(false);
      expect(isTrackingDomain('https://google-analytics.com.evil.com/test')).toBe(false);

      // Test that legitimate subdomains are correctly identified
      expect(isTrackingDomain('https://www.google-analytics.com/test')).toBe(true);
      expect(isTrackingDomain('https://ssl.google-analytics.com/test')).toBe(true);
      expect(isTrackingDomain('https://deep.sub.google-analytics.com/test')).toBe(true);

      // Test exact matches still work
      expect(isTrackingDomain('https://google-analytics.com/test')).toBe(true);
      expect(isTrackingDomain('https://doubleclick.net/test')).toBe(true);
    });

    test('should handle malformed URLs gracefully', () => {
      // These should not throw errors and should return false
      expect(isTrackingDomain('not-a-url')).toBe(false);
      expect(isTrackingDomain('')).toBe(false);
      expect(isTrackingUrl('not-a-url')).toBe(false);
      expect(isTrackingUrl('')).toBe(false);
    });
  });
});

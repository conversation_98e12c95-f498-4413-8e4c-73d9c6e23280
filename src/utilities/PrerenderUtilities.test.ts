import { DocumentLifecycle, PRERENDER_WINDOW_ID } from '../constants/PrerenderConstants';
import {
  isActiveRequest,
  isEffectiveTopLevelRequest,
  isPrefetchRequest,
  isPrerenderedRequest,
  shouldSuppressBlockPage,
  shouldSuppressLogging,
} from './PrerenderUtilities';

describe('PrerenderUtilities', () => {
  describe('isPrerenderedRequest', () => {
    it('should return true when documentLifecycle is prerender', () => {
      const details = {
        documentLifecycle: DocumentLifecycle.PRERENDER,
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
      } as any;

      expect(isPrerenderedRequest(details)).toBe(true);
    });

    it('should return true when windowId is WINDOW_ID_NONE', () => {
      const details = {
        windowId: PRERENDER_WINDOW_ID,
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
      } as any;

      expect(isPrerenderedRequest(details)).toBe(true);
    });

    it('should return false for normal requests', () => {
      const details = {
        documentLifecycle: DocumentLifecycle.ACTIVE,
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
      } as any;

      expect(isPrerenderedRequest(details)).toBe(false);
    });

    it('should return false when no prerender indicators are present', () => {
      const details = {
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
      } as any;

      expect(isPrerenderedRequest(details)).toBe(false);
    });
  });

  describe('isActiveRequest', () => {
    it('should return true when documentLifecycle is active', () => {
      const details = {
        documentLifecycle: DocumentLifecycle.ACTIVE,
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
      } as any;

      expect(isActiveRequest(details)).toBe(true);
    });

    it('should return false when documentLifecycle is prerender', () => {
      const details = {
        documentLifecycle: DocumentLifecycle.PRERENDER,
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
      } as any;

      expect(isActiveRequest(details)).toBe(false);
    });

    it('should return true when documentLifecycle is undefined (backward compatibility)', () => {
      const details = {
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
      } as any;

      expect(isActiveRequest(details)).toBe(true);
    });
  });

  describe('shouldSuppressLogging', () => {
    it('should return true for prerendered requests', () => {
      const details = {
        documentLifecycle: DocumentLifecycle.PRERENDER,
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
      } as any;

      expect(shouldSuppressLogging(details)).toBe(true);
    });

    it('should return false for active requests', () => {
      const details = {
        documentLifecycle: DocumentLifecycle.ACTIVE,
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
      } as any;

      expect(shouldSuppressLogging(details)).toBe(false);
    });
  });

  describe('shouldSuppressBlockPage', () => {
    it('should return true for prerendered requests', () => {
      const details = {
        documentLifecycle: DocumentLifecycle.PRERENDER,
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
      } as any;

      expect(shouldSuppressBlockPage(details)).toBe(true);
    });

    it('should return false for active requests', () => {
      const details = {
        documentLifecycle: DocumentLifecycle.ACTIVE,
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
      } as any;

      expect(shouldSuppressBlockPage(details)).toBe(false);
    });
  });

  describe('isPrefetchRequest', () => {
    it('should return true for main_frame requests with documentId', () => {
      const details = {
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
        documentId: 'some-document-id',
      } as any;

      expect(isPrefetchRequest(details)).toBe(true);
    });

    it('should return false for main_frame requests without documentId', () => {
      const details = {
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
      } as any;

      expect(isPrefetchRequest(details)).toBe(false);
    });

    it('should return false for non-main_frame requests even with documentId', () => {
      const details = {
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'sub_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
        documentId: 'some-document-id',
      } as any;

      expect(isPrefetchRequest(details)).toBe(false);
    });

    it('should return false for main_frame requests with null documentId', () => {
      const details = {
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
        documentId: null,
      } as any;

      expect(isPrefetchRequest(details)).toBe(false);
    });
  });

  describe('isEffectiveTopLevelRequest', () => {
    it('should return true for traditional top-level requests that are not prefetch', () => {
      const details = {
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
      } as any;

      expect(isEffectiveTopLevelRequest(details)).toBe(true);
    });

    it('should return false for prefetch requests even if they are main_frame', () => {
      const details = {
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
        documentId: 'some-document-id',
      } as any;

      expect(isEffectiveTopLevelRequest(details)).toBe(false);
    });

    it('should return false for non-main_frame requests', () => {
      const details = {
        url: 'https://example.com',
        method: 'GET',
        frameId: 1,
        parentFrameId: 0,
        tabId: 1,
        type: 'sub_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
      } as any;

      expect(isEffectiveTopLevelRequest(details)).toBe(false);
    });
  });
});

import * as <PERSON><PERSON> from 'bowser';
import HardwarePlatformInfo from 'models/HardwarePlatformInfo';

/**
 * Performs a deep comparison between the two objects, returning true if they are identical.
 * @param x First object to compare.
 * @param y Second object to compare.
 * @returns If the two objects are equal.
 */
export const deepEqual = (x: any, y: any): boolean => {
  // Handle null cases first to prevent Object.keys(null) errors
  if (x === null && y === null) return true;
  if (x === null || y === null) return false;

  const ok = Object.keys;
  const tx = typeof x;
  const ty = typeof y;
  return tx === 'object' && tx === ty
    ? ok(x).length === ok(y).length && ok(x).every((key) => deepEqual(x[key], y[key]))
    : x === y;
};

/**
 * Gets the version number of the extension from the version field in the manifest file.
 * @returns The version as specified in the manifest.
 */
export const getExtensionVersion = (): string => {
  const details = chrome.runtime.getManifest();
  if (details !== undefined) {
    return details.version;
  }
  return '';
};

/**
 * Gets the manifest version for the extension from the manifest file.
 * @returns The manifest version in the manifest.
 */
export const getManifestVersion = (): string => {
  const details = chrome.runtime.getManifest();
  if (details !== undefined) {
    return details.manifest_version.toString();
  }

  return '';
};

/**
 * Gets the extension's id.
 * @returns The extension's id.
 */
export const getExtensionId = (): string => {
  return chrome.runtime.id;
};

/**
 * Gets the hardware info for the device. If it could not be fetched then undefined will be returned.
 *
 * This uses the enterprise chrome api so it will only work on an enrolled and managed chromebook.
 *
 * @see https://developer.chrome.com/docs/extensions/reference/enterprise_hardwarePlatform/
 * @returns A promise resolving to the hardware info object. Or undefined if it could not be fetched.
 */
export const getHardwarePlatformInfo = async (): Promise<HardwarePlatformInfo | undefined> => {
  const enterprise = chrome.enterprise as any;
  if (enterprise?.hardwarePlatform?.getHardwarePlatformInfo === undefined) {
    return undefined;
  }

  try {
    const platformInfo = await enterprise.hardwarePlatform.getHardwarePlatformInfo();
    return platformInfo;
  } catch (error) {
    return undefined;
  }
};

/**
 * Check if two arrays contain identical elements in the same order.
 * This will not descend into nested arrays or objects.
 *
 * @param array1 The first array to compare.
 * @param array2 The second array to compare.
 * @returns True if the arrays contain identical elements in the same order. False if not.
 */
export const areArraysEqual = <T>(
  array1: T[],
  array2: T[],
  checkOrder: boolean = true,
): boolean => {
  // Sanity-check that the parameters are actually arrays.
  if (!Array.isArray(array1) || !Array.isArray(array2)) {
    return false;
  }

  if (array1.length !== array2.length) {
    return false;
  }

  if (array1 === array2) {
    return true;
  }

  // Copy the arrays so sorting does not affect the original object.
  const compareArray1 = deepCopy(array1);
  const compareArray2 = deepCopy(array2);

  if (!checkOrder) {
    // A sorting function that can handle strings and numbers.
    const sortingFunction = (a: T, b: T): number => {
      if (a < b) {
        return -1;
      }

      if (a > b) {
        return 1;
      }

      return 0;
    };
    compareArray1.sort(sortingFunction);
    compareArray2.sort(sortingFunction);
  }

  for (let i = 0; i < compareArray1.length; ++i) {
    if (compareArray1[i] !== compareArray2[i]) {
      return false;
    }
  }

  return true;
};

/**
 * Gets the current badge title for the extension.
 */
export const getBadgeTitle = async (): Promise<string> => {
  return await chrome.action.getTitle({});
};

/**
 * Sets the extension badge title. If isError is true then it will also set the badge text to '!'.
 * @param The title for the extension badge.
 * @param isError If true a '!' will appear over the extension badge in the browser.
 */
export const setBadgeTitle = async (title: string, isError: boolean = false): Promise<void> => {
  if (isError) {
    await chrome.action.setBadgeText({ text: '!' });
  } else {
    await chrome.action.setBadgeText({ text: '' });
  }

  await chrome.action.setTitle({ title });
};

/**
 * Resets the badge title and text back to the default.
 */
export const resetBadgeTitle = async (): Promise<void> => {
  await setBadgeTitle(getDefaultBadgeTitle());
};

/**
 * Gets the default badge title using the default_title in the manifest if it exists and the version number.
 */
export const getDefaultBadgeTitle = (): string =>
  `${
    (chrome.runtime.getManifest().action?.default_title as string) ?? 'Smoothwall Cloud Filter'
  } ${getExtensionVersion()}`;

/**
 * Clear all alarms, local, and session storage and restart the extension.
 */
export const hardResetExtension = async (): Promise<void> => {
  try {
    await chrome.storage.local.clear();
  } catch (e: any) {
    console.warn(e);
  }

  try {
    await chrome.storage.session.clear();
  } catch (e: any) {
    console.warn(e);
  }

  try {
    await chrome.alarms.clearAll();
  } catch (e: any) {
    console.warn(e);
  }

  chrome.runtime.reload();
};

/**
 * Generates a rendom integer between the given min and max.
 * @param min The minimum number (inclusive).
 * @param max The maxumum number (exclusive).
 * @returns A random integer between the min and max.
 */
export const getRandomInt = (min: number, max: number): number => {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min) + min);
};

/**
 * Performs a deep copy of the given object.
 *
 * Code from here: https://stackoverflow.com/a/43188775/7696257
 * @param obj The object to copy.
 * @returns A deep copy of the object.
 */
export const deepCopy = (obj: any): any => {
  let copy: any;

  // Handle the 3 simple types, and null or undefined
  if (obj == null || typeof obj !== 'object') return obj;

  // Handle Date
  if (obj instanceof Date) {
    copy = new Date();
    copy.setTime(obj.getTime());
    return copy;
  }

  // Handle Array
  if (obj instanceof Array) {
    copy = [];
    for (let i = 0, len = obj.length; i < len; i++) {
      copy[i] = deepCopy(obj[i]);
    }
    return copy;
  }

  // Handle Object
  if (obj instanceof Object) {
    copy = {};
    for (const attr in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, attr)) copy[attr] = deepCopy(obj[attr]);
    }
    return copy;
  }

  throw new Error("Unable to copy obj! Its type isn't supported.");
};

/**
 * Merge the parts of the blob url into a single url.
 * @param resource The url to the resource.
 * @param path The path for the resource
 * @param sas The sas token, with or without the ?.
 * @returns A string with all parts of the url combined.
 */
export const mergeBlobUrl = (resource: string, path: string = '', sas: string = ''): string => {
  return `${resource}/${path}${sas.startsWith('?') ? sas : '?' + sas}`;
};

/**
 * Checks the given arrays for any repeat values and returns them.
 *
 * Code from: https://stackoverflow.com/a/37041756/7696257
 * @param array1 The first array of strings to check.
 * @param array2 The second array of strings to check.
 * @returns Any values that intersect in the arrays.
 */
export const getArrayIntersect = (array1: string[], array2: string[]): string[] => {
  const setA = new Set(array1);
  const setB = new Set(array2);
  const intersection = [...setA].filter((x) => setB.has(x));
  return intersection;
};

/**
 * Check if this extension is currently running on Edge.
 *
 * @returns True if we're running on Edge, or false if not.
 */
export const isRunningOnEdge = (): boolean => {
  // This is an ugly hack, but there's no official way to get this information.
  return chrome.runtime.getURL('').startsWith('edge');
};

/**
 * Removes =, /, and + from base64 of username for Ingest v3 cloud name.
 */
export const base64UrlSafe = (s: string): string => {
  return btoa(s).replace(/=/g, '').replace(/\//g, '_').replace(/\+/g, '-');
};

/**
 * Used for Ingest v3 cloud file name.
 */
export const getUnixTime = (): number => Math.floor(Date.now() / 1000);

/**
 * Converts the given unix time epoch into a javascript date object.
 * @param epoch The unix epoch.
 * @returns A date object.
 */
export const epochToDate = (epoch: number): Date => new Date(epoch * 1000);

export const cleanRegexp = (rawRegexp: string, usesCaptureGroups: boolean): string => {
  let toReturn = rawRegexp.replace('(?i)', '').replace('"', '').replace("'", '');

  if (usesCaptureGroups) {
    toReturn = toReturn
      .replace(/\\1/, '$1')
      .replace(/\\2/, '$2')
      .replace(/\\3/, '$3')
      .replace(/\\4/, '$4')
      .replace(/\\5/, '$5')
      .replace(/\\6/, '$6')
      .replace(/\\7/, '$7')
      .replace(/\\8/, '$8')
      .replace(/\\9/, '$9');
  }

  return toReturn;
};

export const capitalise = (word: string): string => {
  return word.charAt(0).toUpperCase() + word.slice(1);
};

/**
 * Performs case-insensitive string comparison.
 * @param str1 First string to compare.
 * @param str2 Second string to compare.
 * @returns True if strings are equal ignoring case, false otherwise.
 */
export const equalsIgnoreCase = (str1: string, str2: string): boolean => {
  return str1.toLowerCase() === str2.toLowerCase();
};

/**
 * Boolean flag indicating if the extension is targeting the manifest v2 (mv2) standard.
 * This is stored for convenience and brevity.
 */
export const isManifestV2 = chrome?.runtime?.getManifest?.().manifest_version === 2;

/**
 * Converts a string representing an IPv4 address into its decimal equivalent.
 *  Each octect is zero-fill left shifted 8 bits and the next octet infills the zero-fill by adding.
 *  The final zero-fill right shift of 0 bits converts the 64 bit number to 32 bit then back again.
 */
export const ipToInt = (ip: string): number => {
  const octets: number[] = ip.split('.').map((oct) => +oct);
  if (octets.length !== 4 || octets.some((octet) => octet > 255 || octet < 0)) {
    return Number.NaN;
  } else {
    return octets.reduce((acc, cur) => cur + (acc << 8)) >>> 0;
  }
};

/**
 * Convert a Firestore timestamp to a numeric timestamp.
 *
 * @param value The Firestore timestamp to convert.
 * @return The numeric (millisecond) equivalent of the input timestamp. If the input timestamp
 *  does not appear to be a Firestore timestamp then 0 will be returned.
 */
export const firestoreTimestampToNumericTimestamp = (value: any): number => {
  if (typeof value !== 'object' || value == null) {
    return 0;
  }

  // The Firestore timestamp should contains a "toMillis()" function. Use it if possible.
  if (typeof value.toMillis === 'function') {
    try {
      const millis = value.toMillis();
      if (typeof millis === 'number') {
        return Math.round(millis);
      }
      console.warn('Firestore timestamp toMillis() returned non-numeric type.');
    } catch (e: any) {
      console.warn('Exception thrown while calling Firestore timestamp toMillis(): ', e);
    }
  }

  // As a fall-back, the Firestore timestamp should contain a "seconds" property. Try to convert
  //  it to milliseconds.
  if (typeof value.seconds === 'number' && !isNaN(value.seconds)) {
    return value.seconds * 1000;
  }

  console.warn('Failed to convert Firestore timestamp to numeric timestamp.', value);
  return 0;
};

/**
 * Describes a piece of client-side software running on this system, such as the extension or agent.
 * This information is sent to DMS during registration and check-in requests. From there, it is used
 *  to populate the Endpoint Manager portal.
 */
export interface DmsSoftwareData {
  /**
   * The name of the piece of software which is running.
   */
  name: string;

  /**
   * The version number of the piece of software which is running.
   */
  version: string;

  /**
   * The user agent string of the browser the software is running in, if applicable.
   * If the software described by this instance is the native agent running on Windows/macOS, then
   *  this will contain an extract of the agent's version string.
   */
  userAgent: string;
}

/**
 * Parse a version string from the native agent to extract data required for DMS.
 *
 * The agent version string is not currently in a standard format so we need custom
 * logic to extract the information out of it. The custom string is used to
 * make it more user friendly for customers, since the endpoint manager shows
 * this string as-is currently. Once we have formatted the user agent field on
 * endpoint manager with the ticket:
 * https://familyzone.atlassian.net/browse/CLU-197
 * we can use the standard user agent format here.
 *
 * TODO: This is very much a temporary solution for Qoria beta. Once the
 * efficiencies are added only the Agent would register to DMS and we won't
 * need this logic.
 * --- But we can keep around the user agent field for reporting and debugging ---
 *
 * @param agentVersion The full version string reported by the native agent. For example:
 *  "Qoria/4.2.0 Windows 10 Pro (10.0.22621); a:amd64 rv:88+g698cf"
 * @returns If the agent version string was parsed successfully, then this returns a DMS-compatible
 *  data structure describing the native agent. Otherwise, this returns undefined.
 */
export const extractNativeAgentSoftwareData = (
  agentVersion: string,
): DmsSoftwareData | undefined => {
  if (agentVersion === '') {
    return undefined;
  }

  try {
    const appName = agentVersion.split('/')[0];
    const version = agentVersion.split(' ')[0].split('/')[1];
    const revision = agentVersion.split(' rv:')[1];
    const userAgent = agentVersion.split(' rv:')[0];

    return {
      name: `${appName} App`,
      version: `v${version} ${revision}`,
      userAgent,
    };
  } catch (error: any) {
    console.error('Error parsing the native agent version string: ', error);
    return undefined;
  }
};

/**
 * Get a string which identifies the browser name and version in a human-readable way.
 * This is intended as a temporary measure to make Endpoint Manager more user friendly. In future,
 *  the extension should be changed back to reporting the full user agent string to DMS. The
 *  Endpoint Manager portal will need to be responsible for generating the user friendly version
 *  itself.
 *
 * @param userAgentString The original user agent string, as reported by the browser.
 * @returns If the user agent string was parsed successfully then this returns a string containing
 *  the browser name and version number, separated by a dash. For example, "Chrome-133.1.2.3" or
 *  "MicrosoftEdge-132.3.2.1". If the version number cannot be determined then the returned string
 *  will contain only the browser name. If the user agent string cannot be parsed at all then it
 *  will be returned as-is.
 */
export const simplifyUserAgentString = (userAgentString: string): string => {
  try {
    const browser = Bowser.parse(userAgentString).browser;
    if (browser.name === undefined || browser.name === '') {
      console.warn(
        'FilterApplication - Unable to identify browser from user agent string: ',
        userAgentString,
      );
      return userAgentString;
    }

    // Remove any spaces and dashes in the name so that the output is consistently formatted, e.g.
    //  "Microsoft Edge" becomes "MicrosoftEdge".
    let output = browser.name.replace(/[\s-]+/g, '');
    if (browser.version !== undefined && browser.version !== '') {
      output += `-${browser.version}`;
    }

    return output;
  } catch (e: any) {
    console.warn('FilterApplication - Failed to parse browser user agent string: ', e);
    return userAgentString;
  }
};

/**
 * Utilities for detecting prerender state in content scripts.
 * Based on the Speculation Rules API documentation.
 */

/* cspell:ignore prerendered prerendering prerenderingchange onprerenderingchange */

/**
 * Checks if the current document is being prerendered.
 * @returns True if the document is prerendered, false otherwise
 */
export const isDocumentPrerendered = (): boolean => {
  // Check if document.prerendering is available (Chrome 108+)
  if ('prerendering' in document) {
    return (document as any).prerendering === true;
  }
  
  // Fallback: check if we're in a hidden state that might indicate prerender
  // This is less reliable but provides some backward compatibility
  if (document.visibilityState === 'hidden' && !document.hidden) {
    // Document is hidden but not via the Page Visibility API
    // This could indicate prerender, though it's not definitive
    return true;
  }
  
  return false;
};

/**
 * Sets up a listener for when a prerendered page becomes active.
 * @param callback Function to call when the page activates
 * @returns Cleanup function to remove the listener
 */
export const onPrerenderActivation = (callback: () => void): (() => void) => {
  // Check if prerenderingchange event is supported
  if ('onprerenderingchange' in document) {
    const handler = (): void => {
      if (!(document as any).prerendering) {
        callback();
      }
    };
    
    document.addEventListener('prerenderingchange', handler);
    return () => document.removeEventListener('prerenderingchange', handler);
  }
  
  // Fallback: use visibility change as an approximation
  const visibilityHandler = (): void => {
    if (document.visibilityState === 'visible') {
      callback();
    }
  };
  
  document.addEventListener('visibilitychange', visibilityHandler);
  return () => document.removeEventListener('visibilitychange', visibilityHandler);
};

/**
 * Checks if prerender detection is supported in the current browser.
 * @returns True if prerender detection APIs are available
 */
export const isPrerenderDetectionSupported = (): boolean => {
  return 'prerendering' in document || 'onprerenderingchange' in document;
};
/**
 * @jest-environment jsdom
 */

/* cspell:ignore prerendered prerendering prerenderingchange onprerenderingchange */

import {
  isDocumentPrerendered,
  onPrerenderActivation,
  isPrerenderDetectionSupported,
} from './ContentScriptPrerenderDetection';

describe('ContentScriptPrerenderDetection', () => {
  // Store original values
  const originalPrerendering = (document as any).prerendering;
  const originalOnprerenderingchange = (document as any).onprerenderingchange;
  const originalVisibilityState = Object.getOwnPropertyDescriptor(document, 'visibilityState');
  const originalHidden = Object.getOwnPropertyDescriptor(document, 'hidden');
  const originalAddEventListener = document.addEventListener;
  const originalRemoveEventListener = document.removeEventListener;

  beforeEach(() => {
    // Reset document to a clean state
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Restore original document properties
    if (originalPrerendering !== undefined) {
      (document as any).prerendering = originalPrerendering;
    } else {
      delete (document as any).prerendering;
    }
    
    if (originalOnprerenderingchange !== undefined) {
      (document as any).onprerenderingchange = originalOnprerenderingchange;
    } else {
      delete (document as any).onprerenderingchange;
    }
    
    if (originalVisibilityState) {
      Object.defineProperty(document, 'visibilityState', originalVisibilityState);
    }
    
    if (originalHidden) {
      Object.defineProperty(document, 'hidden', originalHidden);
    }
    
    document.addEventListener = originalAddEventListener;
    document.removeEventListener = originalRemoveEventListener;
  });

  describe('isDocumentPrerendered', () => {
    it('should return true when document.prerendering is true', () => {
      (document as any).prerendering = true;
      expect(isDocumentPrerendered()).toBe(true);
    });

    it('should return false when document.prerendering is false', () => {
      (document as any).prerendering = false;
      expect(isDocumentPrerendered()).toBe(false);
    });

    it('should return false when document.prerendering is not available and page is visible', () => {
      delete (document as any).prerendering;
      Object.defineProperty(document, 'visibilityState', {
        value: 'visible',
        configurable: true,
      });
      Object.defineProperty(document, 'hidden', {
        value: false,
        configurable: true,
      });
      
      expect(isDocumentPrerendered()).toBe(false);
    });

    it('should return true when document is hidden but not via Page Visibility API (fallback)', () => {
      delete (document as any).prerendering;
      Object.defineProperty(document, 'visibilityState', {
        value: 'hidden',
        configurable: true,
      });
      Object.defineProperty(document, 'hidden', {
        value: false,
        configurable: true,
      });
      
      expect(isDocumentPrerendered()).toBe(true);
    });

    it('should return false when document is hidden via Page Visibility API', () => {
      delete (document as any).prerendering;
      Object.defineProperty(document, 'visibilityState', {
        value: 'hidden',
        configurable: true,
      });
      Object.defineProperty(document, 'hidden', {
        value: true,
        configurable: true,
      });
      
      expect(isDocumentPrerendered()).toBe(false);
    });

    it('should return false when visibility state is not hidden', () => {
      delete (document as any).prerendering;
      Object.defineProperty(document, 'visibilityState', {
        value: 'visible',
        configurable: true,
      });
      
      expect(isDocumentPrerendered()).toBe(false);
    });
  });

  describe('onPrerenderActivation', () => {
    it('should set up prerenderingchange event listener when supported', () => {
      (document as any).onprerenderingchange = null;
      (document as any).prerendering = true;
      
      const addEventListenerSpy = jest.spyOn(document, 'addEventListener');
      const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');
      
      const callback = jest.fn();
      const cleanup = onPrerenderActivation(callback);

      // Verify event listener was added
      expect(addEventListenerSpy).toHaveBeenCalledWith('prerenderingchange', expect.any(Function));
      
      // Get the handler that was registered
      const handler = addEventListenerSpy.mock.calls[0][1] as EventListener;

      // Simulate prerender becoming inactive
      (document as any).prerendering = false;
      handler(new Event('prerenderingchange'));

      // Callback should be called
      expect(callback).toHaveBeenCalled();

      // Test cleanup
      cleanup();
      expect(removeEventListenerSpy).toHaveBeenCalledWith('prerenderingchange', handler);
    });

    it('should not call callback when prerendering is still true', () => {
      (document as any).onprerenderingchange = null;
      (document as any).prerendering = true;
      
      const addEventListenerSpy = jest.spyOn(document, 'addEventListener');
      
      const callback = jest.fn();
      onPrerenderActivation(callback);

      // Get the handler that was registered
      const handler = addEventListenerSpy.mock.calls[0][1] as EventListener;

      // Simulate prerenderingchange event but still prerendering
      handler(new Event('prerenderingchange'));

      // Callback should NOT be called
      expect(callback).not.toHaveBeenCalled();
    });

    it('should fallback to visibilitychange event when prerenderingchange is not supported', () => {
      delete (document as any).onprerenderingchange;
      delete (document as any).prerendering;
      Object.defineProperty(document, 'visibilityState', {
        value: 'hidden',
        configurable: true,
      });
      
      const addEventListenerSpy = jest.spyOn(document, 'addEventListener');
      const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');
      
      const callback = jest.fn();
      const cleanup = onPrerenderActivation(callback);

      // Verify visibilitychange listener was added (fallback)
      expect(addEventListenerSpy).toHaveBeenCalledWith('visibilitychange', expect.any(Function));
      
      // Get the handler that was registered
      const handler = addEventListenerSpy.mock.calls[0][1] as EventListener;

      // Simulate page becoming visible
      Object.defineProperty(document, 'visibilityState', {
        value: 'visible',
        configurable: true,
      });
      handler(new Event('visibilitychange'));

      // Callback should be called
      expect(callback).toHaveBeenCalled();

      // Test cleanup
      cleanup();
      expect(removeEventListenerSpy).toHaveBeenCalledWith('visibilitychange', handler);
    });

    it('should not call callback when page remains hidden (fallback)', () => {
      delete (document as any).onprerenderingchange;
      delete (document as any).prerendering;
      Object.defineProperty(document, 'visibilityState', {
        value: 'hidden',
        configurable: true,
      });
      
      const addEventListenerSpy = jest.spyOn(document, 'addEventListener');
      
      const callback = jest.fn();
      onPrerenderActivation(callback);

      // Get the handler that was registered
      const handler = addEventListenerSpy.mock.calls[0][1] as EventListener;

      // Simulate visibilitychange but page still hidden
      handler(new Event('visibilitychange'));

      // Callback should NOT be called
      expect(callback).not.toHaveBeenCalled();
    });

    it('should handle cleanup function correctly', () => {
      (document as any).onprerenderingchange = null;
      
      const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');
      
      const callback = jest.fn();
      const cleanup = onPrerenderActivation(callback);
      
      // Call cleanup
      cleanup();
      
      // Should have called removeEventListener
      expect(removeEventListenerSpy).toHaveBeenCalled();
    });
  });

  describe('isPrerenderDetectionSupported', () => {
    it('should return true when prerendering property is available', () => {
      (document as any).prerendering = false;
      delete (document as any).onprerenderingchange;
      
      expect(isPrerenderDetectionSupported()).toBe(true);
    });

    it('should return true when onprerenderingchange property is available', () => {
      delete (document as any).prerendering;
      (document as any).onprerenderingchange = null;
      
      expect(isPrerenderDetectionSupported()).toBe(true);
    });

    it('should return true when both properties are available', () => {
      (document as any).prerendering = false;
      (document as any).onprerenderingchange = null;
      
      expect(isPrerenderDetectionSupported()).toBe(true);
    });

    it('should return false when neither property is available', () => {
      delete (document as any).prerendering;
      delete (document as any).onprerenderingchange;
      
      expect(isPrerenderDetectionSupported()).toBe(false);
    });
  });

  describe('edge cases and integration', () => {
    it('should handle complete prerender lifecycle', () => {
      (document as any).prerendering = true;
      (document as any).onprerenderingchange = null;
      
      const addEventListenerSpy = jest.spyOn(document, 'addEventListener');
      const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');

      // Verify initial state
      expect(isDocumentPrerendered()).toBe(true);
      expect(isPrerenderDetectionSupported()).toBe(true);

      // Set up activation listener
      const activationCallback = jest.fn();
      const cleanup = onPrerenderActivation(activationCallback);

      // Get the registered handler
      const handler = addEventListenerSpy.mock.calls[0][1] as EventListener;

      // Simulate activation
      (document as any).prerendering = false;
      handler(new Event('prerenderingchange'));

      // Verify callback was called
      expect(activationCallback).toHaveBeenCalledTimes(1);

      // Verify state after activation
      expect(isDocumentPrerendered()).toBe(false);

      // Clean up
      cleanup();
      expect(removeEventListenerSpy).toHaveBeenCalled();
    });

    it('should handle browsers without prerender support gracefully', () => {
      delete (document as any).prerendering;
      delete (document as any).onprerenderingchange;
      Object.defineProperty(document, 'visibilityState', {
        value: 'visible',
        configurable: true,
      });
      Object.defineProperty(document, 'hidden', {
        value: false,
        configurable: true,
      });
      
      const addEventListenerSpy = jest.spyOn(document, 'addEventListener');
      const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');

      // Should report as not prerendered
      expect(isDocumentPrerendered()).toBe(false);
      expect(isPrerenderDetectionSupported()).toBe(false);

      // Should still set up some kind of listener (fallback)
      const callback = jest.fn();
      const cleanup = onPrerenderActivation(callback);

      expect(addEventListenerSpy).toHaveBeenCalledWith('visibilitychange', expect.any(Function));

      // Cleanup should work
      cleanup();
      expect(removeEventListenerSpy).toHaveBeenCalled();
    });

    it('should handle multiple activation listeners', () => {
      (document as any).onprerenderingchange = null;
      (document as any).prerendering = true;
      
      const addEventListenerSpy = jest.spyOn(document, 'addEventListener');
      const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');

      const callback1 = jest.fn();
      const callback2 = jest.fn();

      const cleanup1 = onPrerenderActivation(callback1);
      const cleanup2 = onPrerenderActivation(callback2);

      // Both listeners should be registered
      expect(addEventListenerSpy).toHaveBeenCalledTimes(2);

      // Get both handlers
      const handler1 = addEventListenerSpy.mock.calls[0][1] as EventListener;
      const handler2 = addEventListenerSpy.mock.calls[1][1] as EventListener;

      // Simulate activation
      (document as any).prerendering = false;
      handler1(new Event('prerenderingchange'));
      handler2(new Event('prerenderingchange'));

      // Both callbacks should be called
      expect(callback1).toHaveBeenCalled();
      expect(callback2).toHaveBeenCalled();

      // Cleanup
      cleanup1();
      cleanup2();
      expect(removeEventListenerSpy).toHaveBeenCalledTimes(2);
    });
  });
});
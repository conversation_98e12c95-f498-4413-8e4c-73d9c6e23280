import { DocumentLifecycle, PRERENDER_WINDOW_ID } from '../constants/PrerenderConstants';

/**
 * Utility functions for handling Chrome's prerender functionality.
 * These functions help detect and manage prerendered pages to ensure
 * appropriate logging and block page behavior.
 */

/**
 * Determines if a webRequest is for a prerendered page.
 *
 * @param details The webRequest details object
 * @returns True if the request is for a prerendered page, false otherwise
 */
export const isPrerenderedRequest = (
  details: chrome.webRequest.OnBeforeRequestDetails,
): boolean => {
  // Check if documentLifecycle is available (Chrome 106+) and indicates prerender
  if (details.documentLifecycle === DocumentLifecycle.PRERENDER) {
    return true;
  }

  // Additional check: prerendered pages may have windowId of WINDOW_ID_NONE
  // This is a fallback for cases where documentLifecycle might not be available
  if ('windowId' in details && details.windowId === PRERENDER_WINDOW_ID) {
    return true;
  }

  return false;
};

/**
 * Determines if a webRequest is for an active (non-prerendered) page.
 *
 * @param details The webRequest details object
 * @returns True if the request is for an active page, false otherwise
 */
export const isActiveRequest = (details: chrome.webRequest.OnBeforeRequestDetails): boolean => {
  // If documentLifecycle is available, check if it's active
  if (details.documentLifecycle !== undefined) {
    return details.documentLifecycle === DocumentLifecycle.ACTIVE;
  }

  // If documentLifecycle is not available, assume it's active (backward compatibility)
  // unless we can detect it's prerendered through other means
  return !isPrerenderedRequest(details);
};

/**
 * Determines if logging should be suppressed for a request.
 * Logging should be suppressed for prerendered pages until they become active.
 *
 * @param details The webRequest details object
 * @returns True if logging should be suppressed, false otherwise
 */
export const shouldSuppressLogging = (
  details: chrome.webRequest.OnBeforeRequestDetails,
): boolean => {
  return isPrerenderedRequest(details);
};

/**
 * Determines if block page display should be suppressed for a request.
 * Block pages should not be shown for prerendered pages until they become active.
 *
 * @param details The webRequest details object
 * @returns True if block page display should be suppressed, false otherwise
 */
export const shouldSuppressBlockPage = (
  details: chrome.webRequest.OnBeforeRequestDetails,
): boolean => {
  return isPrerenderedRequest(details);
};

/**
 * Determines if a webRequest is for a prefetch request.
 * Chrome v134+ changed prefetch behavior so that document-level prefetch requests
 * now have type "main_frame" instead of "other", making them indistinguishable
 * from genuine user navigation. The presence of documentId indicates this is
 * a prefetch from an existing document rather than a new navigation.
 *
 * @param details The webRequest details object
 * @returns True if the request is likely a prefetch request, false otherwise
 */
export const isPrefetchRequest = (details: chrome.webRequest.OnBeforeRequestDetails): boolean => {
  // Check if this is a main_frame request with a documentId
  // If the user has genuinely navigated to a new document, it should not have a document ID yet
  // If a document-level prefetch is being made, it will have the ID of the currently loaded document
  return (
    details.type === 'main_frame' &&
    'documentId' in details &&
    details.documentId !== undefined &&
    details.documentId !== null
  );
};

/**
 * Determines if a request should be treated as a top-level request for logging and blockpage purposes.
 * This considers both the traditional top-level request criteria and excludes prefetch requests.
 *
 * @param details The webRequest details object
 * @returns True if the request should be treated as a top-level request, false otherwise
 */
export const isEffectiveTopLevelRequest = (
  details: chrome.webRequest.OnBeforeRequestDetails,
): boolean => {
  const isTraditionalTopLevel =
    details.frameId === 0 && details.type === 'main_frame' && details.parentFrameId === -1;

  // Exclude prefetch requests from being treated as top-level
  return isTraditionalTopLevel && !isPrefetchRequest(details);
};

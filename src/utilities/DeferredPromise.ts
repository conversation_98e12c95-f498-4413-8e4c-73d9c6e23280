/**
 * The signature of a resolve() method for a given promise type.
 * If the promise type is void (i.e. Promise<void>) then the resolve() method takes no parameter, or
 *  optionally a void promise.
 * In all other cases, the resolve method takes a parameter of type (T), or a promise wrapper around
 *  type T.
 *
 * @note In the condition, it's important that [T] and [void] are wrapped in square brackets. This
 *  prevents undesirable type distribution over union types, such as boolean.
 */
// eslint-disable-next-line @typescript-eslint/no-invalid-void-type
type Resolver<T> = [T] extends [void]
  ? (value?: Promise<void>) => void
  : (value: T | PromiseLike<T>) => void;

/**
 * A wrapper for a promise which can be resolved or rejected outside the original callback.
 * This can be helpful in situations where we want to await an event which cannot be practically
 *  wrapped by a conventional promise chain.
 *
 * @warning This is usually bad practice! Do not use this unless you are confident there isn't a
 *  better and safer approach.
 */
export default class DeferredPromise<T> {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new deferred promise.
   * The promise is wrapped inside this instance. Access it via the "promise" property.
   */
  public constructor() {
    this.promise = new Promise<T>((resolve, reject) => {
      this._resolve = resolve;
      this._reject = reject;
    });
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Check if the wrapped promise has been fulfilled (i.e. resolved).
   * This is effectively an alias for isResolved.
   *
   * @returns True if the promise has been fulfilled. False if it is pending or has been rejected.
   */
  public get isFulfilled(): boolean {
    return this._isFulfilled;
  }

  /**
   * Check if the wrapped promise has been fulfilled (i.e. resolved).
   * This is effectively an alias for isFulfilled.
   *
   * @returns True if the promise has been fulfilled. False if it is pending or has been rejected.
   */
  public get isResolved(): boolean {
    return this._isFulfilled;
  }

  /**
   * Check if the wrapped promise has been rejected.
   *
   * @returns True if the promise has been rejected. False if it is pending or has been fulfilled.
   */
  public get isRejected(): boolean {
    return this._isRejected;
  }

  /**
   * Check if the wrapped promise is still pending.
   *
   * @returns True if the promise is still pending. False if it has been fulfilled or reject.
   */
  public get isPending(): boolean {
    return !this._isFulfilled && !this._isRejected;
  }

  /**
   * Check if the wrapped promise has been settled (i.e. fulfilled or rejected).
   *
   * @returns True if the promise has been settled. False if it is still pending.
   */
  public get isSettled(): boolean {
    return this._isFulfilled || this._isRejected;
  }

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Resolve (aka fulfil) the wrapped promise with the specified value (if applicable).
   * This has no effect if the promise has already been fulfilled or rejected.
   *
   * @param value The value to resolve the promise with, or another promise which yields the value
   *  to resolve with. If the promise type is void, then the value can be omitted.
   *
   * @note This method has an overload signature provided by Resolver<T>. This provides type safety
   *  based on the promise type.
   */
  public readonly resolve = ((value: any): void => {
    if (this._resolve !== undefined) {
      this._resolve(value);
      this._isFulfilled = true;
    }
    this._resolve = undefined;
    this._reject = undefined;
  }) as Resolver<T>;

  /**
   * Reject the wrapped promise with the specified reason/error.
   * This has no effect if the promise has already been fulfilled or rejected.
   *
   * @param reason Optional reason for the rejection. This can be any type, although it's usually
   *  good practice to make it an instance or sub-class of Error.
   */
  public readonly reject = (reason?: any): void => {
    if (this._reject !== undefined) {
      this._reject(reason);
      this._isRejected = true;
    }
    this._resolve = undefined;
    this._reject = undefined;
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The wrapped promise which can be fulfilled or rejected via the resolve() and reject() methods.
   * This can be accessed directly from outside for promise chaining.
   */
  public readonly promise: Promise<T>;

  /**
   * The internal callback function which will fulfil the wrapped promise.
   * It will be undefined after the promise has been fulfilled or rejected.
   */
  private _resolve?: (value: T | PromiseLike<T>) => void;

  /**
   * The internal callback function which will reject the wrapped promise.
   * It will be undefined after the promise has been fulfilled or rejected.
   */
  private _reject?: (reason: any) => void;

  /**
   * Indicates if the wrapped promise has been fulfilled.
   */
  private _isFulfilled = false;

  /**
   * Indicates if the wrapped promise has been rejected.
   */
  private _isRejected = false;
}

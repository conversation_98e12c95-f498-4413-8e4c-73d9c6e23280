/**
 * This is a promisified wrapper around chrome.tabs.sendMessage().
 * The Chrome extension API supposedly has a promisified version of this function, but it's still
 *  not exposed in the @types library.
 *
 * @param tabId Identifies the tab to sent the message to.
 * @param message The message to send. This should be a JSON-ifiable object.
 * @param frameId Optionally specify which frame within the tab should receive the message. By
 *  default, the message will only be sent to the top-level main frame (i.e. frame 0). A frame ID
 *  can be specified here to sent it to a different frame. Alternatively,
 * @returns Returns a promise which resolves with the response from the tab, if successful.
 */
export const sendMessageToTab = async (
  tabId: number,
  message: any,
  frameId: number | undefined = 0,
): Promise<any> => {
  return await new Promise((resolve, reject) => {
    chrome.tabs.sendMessage(tabId, message, { frameId }, (response: any): void => {
      if (chrome.runtime.lastError !== undefined) {
        reject(chrome.runtime.lastError);
      } else {
        resolve(response);
      }
    });
  });
};

/**
 * This is a promisified wrapper around chrome.storage.X.get().
 * X can be local, sync, or managed.
 * The Chrome extension API claims to have a promised version of this function, but it doesn't seem
 *  to work reliably.
 *
 * @param storageArea The storage area to retrieve items from. This should be chrome.storage.X,
 *  where X is local, sync, or managed.
 * @param keys The name of the key or keys to fetch from storage. If this is an object then the
 *  property names are the keys to fetch and values are the defaults which will be returned if the
 *  key does not exist. If null then all keys will be retrieved.
 * @return Returns a promise which resolve to an object containing the requested keys and their
 *  values. Returns a promise which rejects with an error if something failed. This does not reject
 *  if a specified key could not be found.
 */
export const getFromStorage = async (
  storageArea: chrome.storage.StorageArea,
  keys: string | string[] | object | null,
): Promise<Record<string, any>> => {
  return await new Promise<Record<string, any>>((resolve, reject) => {
    storageArea.get(keys, (items): void => {
      if (chrome.runtime.lastError === undefined) {
        resolve(items);
      } else {
        reject(chrome.runtime.lastError);
      }
    });
  });
};

/**
 * Get the device serial number, if possible.
 * This is the serial associated with the hardware device. It's not the UNCL serial which identifies
 *  the filtering customer.
 *
 * This only works if we're running on a fully enterprise enrolled and managed Chromebook, and the
 *  extension has the appropriate enterprise permission.
 * An enterprise enrolled / managed device is separate from a managed user account. You can login
 *  into an unmanaged device using a managed account, but that doesn't make the device managed.
 *
 * @returns Returns a promise resolving to the device serial number, if possible. It will resolve to
 *  an empty string if the serial number isn't available for any reason.
 */
export const getDeviceSerialNumber = async (): Promise<string> => {
  if (chrome.enterprise?.deviceAttributes?.getDeviceSerialNumber === undefined) {
    return '';
  }

  return await new Promise((resolve): void => {
    chrome.enterprise.deviceAttributes.getDeviceSerialNumber((deviceSerialNumber: string): void => {
      resolve(deviceSerialNumber);
    });
  });
};

/**
 * Get details about the user who is currently logged into the browser.
 * This is a promise-wrapper around chrome.identity.getProfileUserInfo().
 * This will look for an 'ANY' account rather than 'SYNC', if possible.
 */
export const getProfileUserInfo = async (): Promise<chrome.identity.ProfileUserInfo> => {
  return await new Promise((resolve, reject) => {
    const callback = (userInfo: chrome.identity.ProfileUserInfo): void => {
      if (chrome.runtime.lastError !== undefined) {
        reject(chrome.runtime.lastError);
        return;
      }
      resolve(userInfo);
    };

    // The ability to filter accounts by status was only added in Chrome v84.
    // For older versions, we need to leave that parameter out.
    if (chrome.identity?.AccountStatus?.ANY === undefined) {
      chrome.identity.getProfileUserInfo(callback);
      return;
    }

    chrome.identity.getProfileUserInfo(
      { accountStatus: chrome.identity.AccountStatus.ANY },
      callback,
    );
  });
};

/**
 * URL utility functions for analyzing and categorizing URLs
 */

/**
 * Common tracking and analytics domains that should not generate safeguarding alerts
 */
const TRACKING_DOMAINS = new Set([
  'google-analytics.com',
  'googletagmanager.com',
  'doubleclick.net',
  'googlesyndication.com',
  'googleadservices.com',
  'facebook.com',
  'connect.facebook.net',
  'analytics.google.com',
  'stats.g.doubleclick.net',
  'www.google-analytics.com',
  'ssl.google-analytics.com',
  'google.com/analytics',
  'google.com/ccm',
  'gstatic.com',
  'youtube.com/api',
  'youtube.com/youtubei',
  'hotjar.com',
  'mixpanel.com',
  'segment.com',
  'amplitude.com',
  'fullstory.com',
  'logrocket.com',
  'logrocket.io',
  'cdn.logrocket.io',
  'newrelic.com',
  'bugsnag.com',
  'sentry.io',
  'rollbar.com',
  'trackjs.com',
  'raygun.io',
  'pingdom.net',
  'gtm.start.bg',
]);

/**
 * URL patterns that indicate tracking/analytics requests
 */
export const TRACKING_PATTERNS = [
  /\/collect\?/i,
  /\/analytics\//i,
  /\/tracking\//i,
  /\/track\?/i,
  /\/pixel\?/i,
  /\/beacon\?/i,
  /\/events\?/i,
  /\/metrics\?/i,
  /\/stats\?/i,
  /\/telemetry\?/i,
  /\/impression\?/i,
  /\/click\?/i,
  /\/conversion\?/i,
  /\/pageview\?/i,
  /\/gtm\.js/i,
  /\/ga\.js/i,
  /\/analytics\.js/i,
  /\/fbevents\.js/i,
  /\/gtag\//i,
  /\/dts\/rtlactivity/i,
];

/**
 * Helper function to check if a hostname matches any tracking domain
 * @param hostname The hostname to check
 * @returns True if the hostname matches a tracking domain
 */
const isHostnameTrackingDomain = (hostname: string): boolean => {
  // Check exact match first (O(1) lookup)
  if (TRACKING_DOMAINS.has(hostname)) {
    return true;
  }

  // Check subdomain matches - convert Set to Array for iteration
  const domainArray = Array.from(TRACKING_DOMAINS);
  for (const domain of domainArray) {
    const suffix = `.${domain}`;
    // Guard against false negatives: ensure hostname is longer than suffix
    // and that the suffix match is at a domain boundary (starts with a dot)
    if (hostname.length > suffix.length && hostname.endsWith(suffix)) {
      return true;
    }
  }

  return false;
};

/**
 * Determines if a URL is likely a tracking or analytics request that should not generate safeguarding alerts
 * @param url The URL to check
 * @returns True if the URL appears to be a tracking/analytics request
 */
export const isTrackingUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();
    const fullUrl = url.toLowerCase();

    // Check if the domain is a known tracking domain
    if (isHostnameTrackingDomain(hostname)) {
      return true;
    }

    // Check if the URL matches tracking patterns
    const matchesTrackingPattern = TRACKING_PATTERNS.some((pattern) => pattern.test(fullUrl));

    if (matchesTrackingPattern) {
      return true;
    }

    // Check for URLs that contain tracking parameters with embedded URLs
    // These often contain the original page URL in query parameters
    const hasEmbeddedUrl = /[?&](url|u|ref|referrer|redirect|dest|destination)=/i.test(fullUrl);
    const hasTrackingParams = /[?&](utm_|ga_|fb_|gclid|dclid|msclkid|_ga|_gid)/i.test(fullUrl);

    return hasEmbeddedUrl && hasTrackingParams;
  } catch {
    // If URL parsing fails, err on the side of caution and don't filter
    return false;
  }
};

/**
 * Checks if a URL is from a known analytics or tracking domain
 * @param url The URL to check
 * @returns True if the URL is from a tracking domain
 */
export const isTrackingDomain = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();

    return isHostnameTrackingDomain(hostname);
  } catch {
    return false;
  }
};

/**
 * Checks if a URL matches common tracking URL patterns
 * @param url The URL to check
 * @returns True if the URL matches tracking patterns
 */
export const matchesTrackingPattern = (url: string): boolean => {
  const fullUrl = url.toLowerCase();
  return TRACKING_PATTERNS.some((pattern) => pattern.test(fullUrl));
};

/**
 * Checks if a URL contains tracking parameters and embedded URLs
 * @param url The URL to check
 * @returns True if the URL has both embedded URLs and tracking parameters
 */
export const hasTrackingParameters = (url: string): boolean => {
  const fullUrl = url.toLowerCase();
  const hasEmbeddedUrl = /[?&](url|u|ref|referrer|redirect|dest|destination)=/i.test(fullUrl);
  const hasTrackingParams = /[?&](utm_|ga_|fb_|gclid|dclid|msclkid|_ga|_gid)/i.test(fullUrl);

  return hasEmbeddedUrl && hasTrackingParams;
};

/**
 * Extracts the embedded URL from a tracking URL if present
 * @param url The tracking URL that may contain an embedded URL
 * @returns The embedded URL if found, otherwise undefined
 */
export const extractEmbeddedUrl = (url: string): string | undefined => {
  try {
    const urlObj = new URL(url);
    const params = urlObj.searchParams;

    // Common parameter names for embedded URLs
    const urlParams = ['url', 'u', 'ref', 'referrer', 'redirect', 'dest', 'destination', 'dl'];

    for (const param of urlParams) {
      const value = params.get(param);
      if (value !== null) {
        try {
          // Try to decode and validate the embedded URL
          const decodedUrl = decodeURIComponent(value);
          // Validate it's a proper URL
          // eslint-disable-next-line no-new
          new URL(decodedUrl);
          return decodedUrl;
        } catch {
          // If decoding or URL validation fails, continue to next parameter
          continue;
        }
      }
    }

    return undefined;
  } catch {
    return undefined;
  }
};

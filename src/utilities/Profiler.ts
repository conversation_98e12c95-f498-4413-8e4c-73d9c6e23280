/**
 * Contains information about an individual event which occurred in a profile.
 */
export interface Event {
  /**
   * Identifies the type of event which was profiled.
   * This is used to identify related occurrences, and to group them together for aggregate
   *  statistics.
   */
  readonly name: string;

  /**
   * Milliseconds timestamp indicating when the event began.
   * This should usually be a high resolution timer (e.g. performance.now()), meaning it will
   *  probably be relative to when the extension started, not the Unix epoch.
   */
  readonly start: number;

  /**
   * The time (in milliseconds) that the event lasted.
   */
  readonly duration: number;
}

/**
 * Contains aggregate statistics about an individual type of event.
 */
export class EventStatistics {
  /**
   * Constructs a new object containing aggregated statistics about a type of event.
   *
   * @param count The number of times the event occurred.
   * @param sumDuration The total amount of time spent running the event (i.e. the sum of all the
   *  durations). Measured in milliseconds.
   * @param maxDuration The longest duration seen for this event. Measured in milliseconds.
   * @param meanDuration The mean (average) duration of the event. Measured in milliseconds.
   */
  public constructor(
    public count: number,
    public sumDuration: number,
    public maxDuration: number,
    public meanDuration: number,
  ) {}
}

/**
 * A utility class to help track how long code takes to execute.
 *
 * @warning Only use this during development and testing. Profiling adds overhead so do not use it
 *  in production.
 */
export class Profiler {
  /**
   * Construct a new profiler instance.
   *
   * @param startGathering If true, any profiling events generated after construction will be
   *  stored. If false, no events will be gathered until isGathering is set to true separately.
   */
  public constructor(startGathering: boolean = true) {
    this.isGathering = startGathering;
  }

  /**
   * Immediately call a function and measure how long it takes to execute synchronously.
   * The measurement will be added to the internal list of events.
   *
   * @param name Identifies the type of event being profiled. This is used to identify related
   *  occurrences, and to group them together for aggregate statistics.
   * @param callee The function to call. It will be called immediately, and the result will be
   *  returned.
   * @param args The arguments to pass to the function, if any.
   * @returns Returns the result of calling the provided function. If the callee returns a promise
   *  then it will not be awaited.
   *
   * @warning This only measures synchronous execution time. It will not await promises.
   * @note This only stores the measurement if isGathering is true.
   *
   * @todo Add the ability to measure asynchronous calls.
   * @todo Capture the time taken even if the callee throws/rejects.
   *
   * @example profiler.profileCall('output', console.log, 'hello world');
   */
  public readonly profileCall = <T extends (...args: any[]) => any>(
    name: string,
    callee: T,
    ...args: Parameters<T>
  ): ReturnType<T> => {
    const start = performance.now();
    const result = callee(...args);
    this.addEvent(name, start, performance.now() - start);
    return result;
  };

  /**
   * Wrap a callback so that its execution time is measured (synchronously) whenever it is called.
   * This is a higher-order function. Calling this function alone doesn't measure anything or add
   *  any events to the profile. Measurements are only taken when the returned function is called.
   *
   * @param name Identifies the type of event being profiled. This is used to identify related
   *  occurrences, and to group them together for aggregate statistics.
   * @param callback The callback to wrap. Whenever the returned function is callback, this callback
   *  will be invoked, measured, and the result will be returned.
   * @returns Returns a function which wraps the provided callback. The wrapper can be used exactly
   *  the same way as the original callback would have been. It will forward arguments through and
   *  return the result.
   *
   * @warning This only measures synchronous execution time. It will not await promises.
   * @note This only stores events if isGathering is true.
   *
   * @todo Add the ability to measure asynchronous calls.
   * @todo Capture the time taken even if the callback throws/rejects.
   *
   * @example chrome.runtime.onMessage.addListener( profiler.profileCallback('message-handling', onMessage) );
   */
  public readonly profileCallback = <T extends (...args: any[]) => any>(
    name: string,
    callback: T,
  ): ((...args: Parameters<T>) => ReturnType<T>) => {
    return (...args: Parameters<T>): ReturnType<T> => {
      return this.profileCall(name, callback, ...args);
    };
  };

  /**
   * Add a single event to the profile, and update the associated aggregate statistics.
   *
   * @param name Identifies the type of event being profiled. This is used to identify related
   *  occurrences, and to group them together for aggregate statistics.
   * @param start Milliseconds timestamp indicating when the event began. Usually a performance
   *  timer, not a Unix timestamp.
   * @param duration The time (in milliseconds) that the event lasted.
   *
   * @note This will do nothing if isGathering is false.
   */
  public readonly addEvent = (name: string, start: number, duration: number): void => {
    if (!this.isGathering) {
      return;
    }

    this._events.push({ name, start, duration });

    const statistics = this._statistics.get(name);
    if (statistics === undefined) {
      this._statistics.set(name, new EventStatistics(1, duration, duration, duration));
      return;
    }

    statistics.count++;
    statistics.sumDuration += duration;
    statistics.meanDuration = statistics.sumDuration / statistics.count;
    if (duration > statistics.maxDuration) {
      statistics.maxDuration = duration;
    }
  };

  /**
   * Output information about all the events and aggregate statistics to the console.
   */
  public readonly print = (): void => {
    console.log(`Profiler gathered ${this._events.length} event(s).`);
    console.log('Profiler statistics', '\n' + this.getStatisticsCsv());
    console.log('Profiler raw data', '\n' + this.getEventsCsv());
  };

  /**
   * Write all events to a CSV string.
   * @returns A CSV string containing all the events which have been recorded.
   */
  public readonly getEventsCsv = (): string => {
    let output = 'name,start,duration\n';
    this._events.forEach((event: Event) => {
      output += `${event.name},${event.start},${event.duration}\n`;
    });
    return output;
  };

  /**
   * Write aggregate statistics to a CSV string.
   * @returns A CSV string containing aggregate statistics.
   */
  public readonly getStatisticsCsv = (): string => {
    let output = 'name,count,maxDuration,meanDuration\n';
    this._statistics.forEach((eventStatistics: EventStatistics, name: string) => {
      output += `${name},${eventStatistics.count},${eventStatistics.maxDuration},${eventStatistics.meanDuration}\n`;
    });
    return output;
  };

  /**
   * Discard all recorded events and aggregate statistics.
   */
  public readonly clear = (): void => {
    this._events.length = 0;
    this._statistics.clear();
  };

  /**
   * Indicates if events are currently being stored.
   * If false, no new events or statistics will be stored.
   */
  public isGathering: boolean;

  /**
   * All the events gathered so far by this instance.
   */
  private readonly _events: Event[] = [];

  /**
   * The aggregate statistics gathered so far by this instance.
   */
  private readonly _statistics = new Map<string, EventStatistics>();
}

/**
 * A shared instance of the profiler to be used anywhere in the extension.
 */
const profiler = new Profiler();
export default profiler;

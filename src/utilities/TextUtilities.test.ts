import { readLineByLine } from './TextUtilities';

describe('TextUtilities', () => {
  describe('readLineByLine()', () => {
    it('returns a generator which reads a file one line at a time, excluding line break characters', () => {
      const generator = readLineByLine('blah\nfoo\nbar\n');
      expect(generator.next().value).toEqual('blah');
      expect(generator.next().value).toEqual('foo');
      expect(generator.next().value).toEqual('bar');
      expect(generator.next().done).toBeTrue();
    });

    it('handles Unix style line endings correctly', () => {
      const output = [...readLineByLine('blah\nfoo\nbar\n')];
      expect(output).toEqual(['blah', 'foo', 'bar']);
    });

    it('handles old Mac style line endings correctly', () => {
      const output = [...readLineByLine('blah\rfoo\rbar\r')];
      expect(output).toEqual(['blah', 'foo', 'bar']);
    });

    it('handles Windows style line endings correctly', () => {
      const output = [...readLineByLine('blah\r\nfoo\r\nbar\r\n')];
      expect(output).toEqual(['blah', 'foo', 'bar']);
    });

    it('does not yield anything if the string is empty', () => {
      const output = [...readLineByLine('')];
      expect(output).toBeEmpty();
    });

    it('yields the entire string at once if it contains no line breaks', () => {
      const output = [...readLineByLine('hello world')];
      expect(output).toEqual(['hello world']);
    });

    it('yields the last line correctly when the string ends with a line break', () => {
      const generator = readLineByLine('blah\nfoo\nbar\n');
      const output = [...generator];
      expect(output[output.length - 1]).toEqual('bar');
    });

    it('yields the last line correctly when the string does not end with a line break', () => {
      const output = [...readLineByLine('blah\nfoo\nbar')];
      expect(output[output.length - 1]).toEqual('bar');
    });

    it('yields an empty string if there are two line breaks in a row', () => {
      const output = [...readLineByLine('blah\n\nfoo\nbar')];
      expect(output[1]).toEqual('');
    });
  });
});

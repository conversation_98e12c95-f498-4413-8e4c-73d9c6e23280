/**
 * Declares the type callback function which can be invoked by an instance of StandaloneEvent.
 * The generic parameters of the event class determine which arguments the listener must accept.
 * The function can either return nothing, or it can return a void promise. This means async
 *  listeners are allowed.
 *
 * @see StandaloneEvent
 */
export type Listener<Arguments extends readonly any[]> = (
  ...args: Arguments
) => void | Promise<void>;

/**
 * Declares the type of function which will be used to report or log errors during callbacks.
 * This is used by StandaloneEvent.
 */
export type ErrorReporter = (e: any) => void;

/**
 * The default error reporting function used by StandaloneEvent.
 * This will output the error to the console.
 *
 * @param e The error which occurred. This should usually be an Error object.
 */
const defaultErrorReporter: ErrorReporter = (e: any): void => {
  console.error('StandaloneEvent: An error occurred during a callback.');
  if (e !== undefined) {
    console.error(e);
  }
};

/**
 * Manages a collection of callbacks which can be triggered to signify an event occurring.
 * This event is standalone; i.e. one instance of this class represents one type of event which can
 *  be triggered any number of times. There is no centralised dispatch loop or similar, so each
 *  instance is entirely independent of any other instances and events.
 * The generic parameters determine the arguments which will be passed to the callback(s). All
 *  callbacks must accept the expected parameters. Callbacks can be synchronous (i.e. return void)
 *  or asynchronous (i.e. return Promise<void>).
 *
 * @example
 * ```typescript
 *  const myListener = (s: string, n: number): void => {
 *    console.log(`Event has occurred. s=${s}, n=${n}.`);
 *  };
 *
 *  const myEvent = new StandaloneEvent<[string, number]>();
 *  myEvent.addListener(myListener);
 *  myEvent.dispatch('blah', 123);
 * ```
 */
export default class StandaloneEvent<Arguments extends readonly any[] = []> {
  /**
   * Construct a new instance, optionally providing a custom error reporting function.
   *
   * @param errorReporter An optional function which will be invoked each time a callback throws an
   *  error or rejects a promise. The associated error object will be passed in as a parameter. This
   *  is intended for logging errors if necessary. If this parameter is undefined then errors will
   *  be logged to the console by default.
   */
  public constructor(errorReporter?: ErrorReporter) {
    this._errorReporter = errorReporter ?? defaultErrorReporter;
  }

  /**
   * Add a callback or chained event which will be invoked when this event is triggered.
   * If the same listener has already been added then this will have no effect; i.e. the same
   *  listener will not be added again, nor will it be invoked multiple times.
   * A chained event is simply another instance
   *
   * @param listener The callback function or chained event to add to this event. The parameter list
   *  must match the parameters expected by this event (see the Arguments generic parameter). If a
   *  function is specified, it can optionally be synchronous or asynchronous. Any errors thrown
   *  will be logged but discarded.
   * @see removeListener()
   */
  public readonly addListener = (
    listener: Listener<Arguments> | StandaloneEvent<Arguments>,
  ): void => {
    if (listener === this) {
      throw new Error('Cannot chain an event to itself.');
    }

    if (listener instanceof StandaloneEvent) {
      this._listeners.add(listener.dispatch);
    } else {
      this._listeners.add(listener);
    }
  };

  /**
   * Remove a callback or chained event from this event.
   * This prevents it from being called when this event is triggered.
   * This has no effect if the specified listener has already been removed or was never added.
   *
   * @param listener The callback function or chained event to remove from this event.
   * @see addListener()
   * @see clearListeners()
   */
  public readonly removeListener = (
    listener: Listener<Arguments> | StandaloneEvent<Arguments>,
  ): void => {
    if (listener instanceof StandaloneEvent) {
      this._listeners.delete(listener.dispatch);
    } else {
      this._listeners.delete(listener);
    }
  };

  /**
   * Remove all callbacks, preventing any of them from being called when the event is triggered.
   * This has no effect if no listeners have been added, or they have all been removed already.
   */
  public readonly clearListeners = (): void => {
    this._listeners.clear();
  };

  /**
   * Get the number of callbacks which are currently listening for this event.
   */
  public get length(): number {
    return this._listeners.size;
  }

  /**
   * Synchronously invoke all the callbacks which have been added to this event.
   * The callbacks will be called in the order they were added.
   * This will not wait for asynchronous callbacks to complete.
   * If any callback throws an error or returns a rejected promise, then the error will be passed
   *  to the error reporter. Any subsequent callbacks will then be invoked.
   * The default error reporter outputs the error to the console. A custom reported can be specified
   *  in the constructor.
   *
   * @param args The arguments to pass to each callback. The number and types of argument must match
   *  the Arguments generic parameter used to instantiate this object.
   *
   * @see deferDispatch()
   */
  public readonly dispatch = (...args: Arguments): void => {
    // Operate on a local copy in case any callback modifies the listeners as we go along.
    const listeners = new Set(this._listeners);
    listeners.forEach((listener) => {
      try {
        const result = listener(...args);

        // If the function returned a rejected promise then log the error.
        if (result instanceof Promise) {
          result.catch(this._errorReporter);
        }
      } catch (e: any) {
        // If the function threw an error then log it.
        this._errorReporter(e);
      }
    });
  };

  /**
   * Asynchronously invoke all the listeners which have been added to this event.
   * This will defer invocation until the next available event loop iteration. This is useful if you
   *  want to allow current synchronous code to finish before the listeners are triggered.
   * If any callback throws an error or returns a rejected promise, then the error will be passed
   *  to the error reporter. Any subsequent callbacks will then be invoked.
   * The default error reporter outputs the error to the console. A custom reported can be specified
   *  in the constructor.
   *
   * @param args The arguments to pass to each callback. The number and types of argument must match
   *  the Arguments generic parameter used to instantiate this object.
   *
   * @warning This will only execute the callbacks which are registered at the time of deferred
   *  invocation. If any callbacks are removed after calling deferDispatch(), but before the
   *  dispatch actually occurs, then they will not be executed.
   *
   * @see dispatch()
   */
  public readonly deferDispatch = (...args: Arguments): void => {
    setTimeout(() => {
      this.dispatch(...args);
    }, 0);
  };

  /**
   * Asynchronously invoke all the listeners, and wait until dispatch has finished.
   * This is similar to deferDispatch(), but it returns a promise which waits until all of the
   *  listeners have been called. This might be useful if you want to attach a then() clause after
   *  dispatch, but don't want other code to wait for dispatch to finish.
   *
   * @param args The arguments to pass to each callback. The number and types of argument must match
   *  the Arguments generic parameter used to instantiate this object.
   * @returns A promise which resolves when all of the listeners have been called. It is never
   *  expected to reject.
   *
   * @note In most case, you probably want to use deferDispatch() instead.
   * @see deferDispatch()
   */
  public readonly deferDispatchAndWait = async (...args: Arguments): Promise<void> => {
    await new Promise<void>((resolve) => {
      setTimeout(() => {
        this.dispatch(...args);
        resolve();
      }, 0);
    });
  };

  /**
   * A collection of callbacks which will be triggered when the event occurs.
   */
  private readonly _listeners = new Set<Listener<Arguments>>();

  /**
   * A function which will report or log details of an error which occurred in a callback.
   * By default, this will log errors to the console. However, a custom function can be specified in
   *  the constructor.
   */
  private readonly _errorReporter: ErrorReporter;
}

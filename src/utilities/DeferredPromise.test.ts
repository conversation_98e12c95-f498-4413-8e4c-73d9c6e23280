import DeferredPromise from './DeferredPromise';

describe('DeferredPromise', () => {
  describe('isFulfilled', () => {
    it('returns false if the promise is pending', async () => {
      const deferredPromise = new DeferredPromise<void>();
      expect(deferredPromise.isFulfilled).toBeFalse();
      deferredPromise.resolve();
      await deferredPromise.promise;
    });

    it('returns true if the promise has resolved', async () => {
      const deferredPromise = new DeferredPromise<void>();
      deferredPromise.resolve();
      await deferredPromise.promise;
      expect(deferredPromise.isFulfilled).toBeTrue();
    });

    it('returns false if the promise has rejected', async () => {
      const deferredPromise = new DeferredPromise<void>();
      deferredPromise.reject();
      await expect(deferredPromise.promise).toReject();
      expect(deferredPromise.isFulfilled).toBeFalse();
    });

    it('returns true if the caller tries to reject the promise after it has resolved', async () => {
      const deferredPromise = new DeferredPromise<void>();
      deferredPromise.resolve();
      deferredPromise.reject();
      await deferredPromise.promise;
      expect(deferredPromise.isFulfilled).toBeTrue();
    });
  });

  describe('isRejected', () => {
    it('returns false if the promise is pending', async () => {
      const deferredPromise = new DeferredPromise<void>();
      expect(deferredPromise.isRejected).toBeFalse();
      deferredPromise.resolve();
      await deferredPromise.promise;
    });

    it('returns false if the promise has resolved', async () => {
      const deferredPromise = new DeferredPromise<void>();
      deferredPromise.resolve();
      await deferredPromise.promise;
      expect(deferredPromise.isRejected).toBeFalse();
    });

    it('returns true if the promise has rejected', async () => {
      const deferredPromise = new DeferredPromise<void>();
      deferredPromise.reject();
      await expect(deferredPromise.promise).toReject();
      expect(deferredPromise.isRejected).toBeTrue();
    });

    it('returns true if the caller tries to resolve the promise after it has rejected', async () => {
      const deferredPromise = new DeferredPromise<void>();
      deferredPromise.reject();
      deferredPromise.resolve();
      await expect(deferredPromise.promise).toReject();
      expect(deferredPromise.isRejected).toBeTrue();
    });
  });

  describe('isPending', () => {
    it('returns true if the promise is pending', async () => {
      const deferredPromise = new DeferredPromise<void>();
      expect(deferredPromise.isPending).toBeTrue();
      deferredPromise.resolve();
      await deferredPromise.promise;
    });

    it('returns false if the promise has resolved', async () => {
      const deferredPromise = new DeferredPromise<void>();
      deferredPromise.resolve();
      await deferredPromise.promise;
      expect(deferredPromise.isPending).toBeFalse();
    });

    it('returns false if the promise has rejected', async () => {
      const deferredPromise = new DeferredPromise<void>();
      deferredPromise.reject();
      await expect(deferredPromise.promise).toReject();
      expect(deferredPromise.isPending).toBeFalse();
    });
  });

  describe('isSettled', () => {
    it('returns false if the promise is pending', async () => {
      const deferredPromise = new DeferredPromise<void>();
      expect(deferredPromise.isSettled).toBeFalse();
      deferredPromise.resolve();
      await deferredPromise.promise;
    });

    it('returns true if the promise has resolved', async () => {
      const deferredPromise = new DeferredPromise<void>();
      deferredPromise.resolve();
      await deferredPromise.promise;
      expect(deferredPromise.isSettled).toBeTrue();
    });

    it('returns true if the promise has rejected', async () => {
      const deferredPromise = new DeferredPromise<void>();
      deferredPromise.reject();
      await expect(deferredPromise.promise).toReject();
      expect(deferredPromise.isSettled).toBeTrue();
    });
  });

  describe('resolve()', () => {
    it('resolves the wrapped promise if it is void', async () => {
      const deferredPromise = new DeferredPromise<void>();
      deferredPromise.resolve();
      await expect(deferredPromise.promise).toResolve();
    });

    it('resolves the wrapped promise to the specified value if the type is boolean', async () => {
      let deferredPromise = new DeferredPromise<boolean>();
      deferredPromise.resolve(false);
      await expect(deferredPromise.promise).resolves.toBeFalse();

      deferredPromise = new DeferredPromise<boolean>();
      deferredPromise.resolve(true);
      await expect(deferredPromise.promise).resolves.toBeTrue();
    });

    it('resolves the wrapped promise to the specified value if the type is number', async () => {
      const deferredPromise = new DeferredPromise<number>();
      deferredPromise.resolve(12345);
      await expect(deferredPromise.promise).resolves.toEqual(12345);
    });

    it('resolves the wrapped promise to the specified value if the type is string', async () => {
      const deferredPromise = new DeferredPromise<string>();
      deferredPromise.resolve('xyzzy');
      await expect(deferredPromise.promise).resolves.toEqual('xyzzy');
    });

    it('resolves the wrapped promise to the specified value if the type is object', async () => {
      const data = {
        hello: 'world',
        testing: 123,
      };
      const deferredPromise = new DeferredPromise<object>();
      deferredPromise.resolve(data);
      await expect(deferredPromise.promise).resolves.toEqual(data);
    });

    it('resolves the wrapped promise if another promise is passed in which also resolves', async () => {
      const deferredPromise = new DeferredPromise<void>();
      deferredPromise.resolve(Promise.resolve());
      await expect(deferredPromise.promise).toResolve();
    });

    it('resolves the wrapped promise to the value provided by another promise', async () => {
      const deferredPromise = new DeferredPromise<number>();
      deferredPromise.resolve(Promise.resolve(12345));
      await expect(deferredPromise.promise).resolves.toEqual(12345);
    });

    it('rejects the wrapped promise using the reason provide by another rejected promise', async () => {
      const deferredPromise = new DeferredPromise<number>();
      deferredPromise.resolve(Promise.reject(new Error('test error')));
      await expect(deferredPromise.promise).rejects.toEqual(new Error('test error'));
    });

    it('has no effect if called after the promise has resolved', async () => {
      const deferredPromise = new DeferredPromise<number>();
      deferredPromise.resolve(123);
      deferredPromise.resolve(456);
      await expect(deferredPromise.promise).resolves.toEqual(123);
    });

    it('has no effect if called after the promise has rejected', async () => {
      const deferredPromise = new DeferredPromise<number>();
      deferredPromise.reject(new Error('testing'));
      deferredPromise.resolve(456);
      await expect(deferredPromise.promise).toReject();
    });
  });

  describe('reject()', () => {
    it('rejects the wrapped promise', async () => {
      const deferredPromise = new DeferredPromise<void>();
      deferredPromise.reject();
      await expect(deferredPromise.promise).toReject();
    });

    it('rejects the wrapped promise with the specified value', async () => {
      const deferredPromise = new DeferredPromise<void>();
      deferredPromise.reject('test rejection');
      await expect(deferredPromise.promise).rejects.toEqual('test rejection');
    });

    it('has no effect if the promise has already resolved', async () => {
      const deferredPromise = new DeferredPromise<void>();
      deferredPromise.resolve();
      deferredPromise.reject('test rejection');
      await expect(deferredPromise.promise).toResolve();
    });

    it('has no effect if the promise has already rejected', async () => {
      const deferredPromise = new DeferredPromise<void>();
      deferredPromise.reject('test rejection 1');
      deferredPromise.reject('test rejection 2');
      await expect(deferredPromise.promise).rejects.toEqual('test rejection 1');
    });
  });
});

import SimpleRateLimiter from './SimpleRateLimiter';

describe('SimpleRateLimiter', () => {
  describe('constructor()', () => {
    it('throws if maxRequests is 0 or negative', () => {
      expect(() => new SimpleRateLimiter(0, 1000)).toThrow();
      expect(() => new SimpleRateLimiter(-1, 1000)).toThrow();
    });

    it('throws if windowMs is 0 or negative', () => {
      expect(() => new SimpleRateLimiter(1, 0)).toThrow();
      expect(() => new SimpleRateLimiter(1, -100)).toThrow();
    });
  });

  describe('tryConsume()', () => {
    it('allows requests within the limit', () => {
      const limiter = new SimpleRateLimiter(3, 1000);
      expect(limiter.tryConsume()).toBeTrue();
      expect(limiter.tryConsume()).toBeTrue();
      expect(limiter.tryConsume()).toBeTrue();
    });

    it('blocks requests exceeding the limit', () => {
      const limiter = new SimpleRateLimiter(2, 1000);
      limiter.tryConsume();
      limiter.tryConsume();
      expect(limiter.tryConsume()).toBeFalse();
    });

    it('allows requests again after the window resets', async () => {
      jest.useFakeTimers();
      const limiter = new SimpleRateLimiter(1, 1000);
      limiter.tryConsume();
      jest.advanceTimersByTime(1000);
      expect(limiter.tryConsume()).toBeTrue();
      jest.useRealTimers();
    });

    it('handles no requests gracefully', () => {
      const limiter = new SimpleRateLimiter(5, 1000);
      expect(limiter.tryConsume()).toBeTrue();
    });

    it('handles rapid calls at the same millisecond', () => {
      jest.useFakeTimers();
      const limiter = new SimpleRateLimiter(2, 1000);
      expect(limiter.tryConsume()).toBeTrue();
      expect(limiter.tryConsume()).toBeTrue();
      expect(limiter.tryConsume()).toBeFalse();
      jest.useRealTimers();
    });

    it('handles a very large window', () => {
      const limiter = new SimpleRateLimiter(2, 1000000);
      expect(limiter.tryConsume()).toBeTrue();
      expect(limiter.tryConsume()).toBeTrue();
      expect(limiter.tryConsume()).toBeFalse();
    });

    it('handles a very small window', () => {
      jest.useFakeTimers();
      const limiter = new SimpleRateLimiter(1, 1);
      expect(limiter.tryConsume()).toBeTrue();
      expect(limiter.tryConsume()).toBeFalse();
      jest.advanceTimersByTime(1);
      expect(limiter.tryConsume()).toBeTrue();
      jest.useRealTimers();
    });

    it('does not allow more than maxRequests even with time travel', () => {
      jest.useFakeTimers();
      const limiter = new SimpleRateLimiter(1, 1000);
      expect(limiter.tryConsume()).toBeTrue();
      jest.advanceTimersByTime(999);
      expect(limiter.tryConsume()).toBeFalse();
      jest.useRealTimers();
    });
  });

  describe('getRemainingRequests()', () => {
    it('returns the correct number of remaining requests', () => {
      const limiter = new SimpleRateLimiter(3, 1000);
      limiter.tryConsume();
      expect(limiter.getRemainingRequests()).toEqual(2);
    });

    it('returns 0 when the limit is reached', () => {
      const limiter = new SimpleRateLimiter(2, 1000);
      limiter.tryConsume();
      limiter.tryConsume();
      expect(limiter.getRemainingRequests()).toEqual(0);
    });

    it('returns the full limit after the window resets', async () => {
      jest.useFakeTimers();
      const limiter = new SimpleRateLimiter(2, 1000);
      limiter.tryConsume();
      limiter.tryConsume();
      jest.advanceTimersByTime(1000);
      expect(limiter.getRemainingRequests()).toEqual(2);
      jest.useRealTimers();
    });
  });

  describe('getResetTime()', () => {
    it('returns 0 if not limited', () => {
      const limiter = new SimpleRateLimiter(3, 1000);
      expect(limiter.getResetTime()).toEqual(0);
    });

    it('returns the correct reset time when limited', () => {
      jest.useFakeTimers();
      const limiter = new SimpleRateLimiter(1, 1000);
      limiter.tryConsume();
      expect(limiter.getResetTime()).toBeGreaterThan(0);
      jest.useRealTimers();
    });

    it('returns 0 after the window resets', async () => {
      jest.useFakeTimers();
      const limiter = new SimpleRateLimiter(1, 1000);
      limiter.tryConsume();
      jest.advanceTimersByTime(1000);
      expect(limiter.getResetTime()).toEqual(0);
      jest.useRealTimers();
    });
  });
});

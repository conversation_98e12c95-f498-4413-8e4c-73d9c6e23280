import { Timestamp } from 'firebase/firestore';

import {
  areArraysEqual,
  deepEqual,
  equalsIgnoreCase,
  firestoreTimestampToNumericTimestamp,
  simplifyUserAgentString,
} from './Helpers';

describe('Helpers', () => {
  beforeEach(() => {
    chrome.runtime.getURL = jest.fn(() => 'chrome-extension://xyzzy');
    chrome.runtime.getPlatformInfo = jest.fn(
      async () =>
        await Promise.resolve({
          arch: 'x86-64',
          nacl_arch: 'x86-64',
          os: 'win',
        }),
    );

    // Prevent all debug messages from being logged to the console.
    jest.spyOn(console, 'debug').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  describe('areArraysEqual()', () => {
    it('returns false if either item is not an array', () => {
      expect(areArraysEqual('blah' as unknown as string[], ['blah'])).toBeFalse();
      expect(areArraysEqual(['blah'], 'blah' as unknown as string[])).toBeFalse();
    });

    it('returns false if arrays have different sizes', () => {
      expect(areArraysEqual([1, 2], [1, 2, 3])).toBeFalse();
    });

    it('returns false if arrays contain any different items', () => {
      expect(areArraysEqual([1, 2, 3], [1, 0, 3])).toBeFalse();
    });

    it('returns false if arrays contain same items but with different numbers of duplicates', () => {
      // This test guards against naive comparisons which assume items are unique.
      expect(areArraysEqual([1, 1, 2], [1, 2, 2])).toBeFalse();
    });

    it('returns false if arrays contain same values with different types', () => {
      expect(areArraysEqual([1, 2, 3], ['1', '2', '3'] as unknown as number[])).toBeFalse();
    });

    it('returns false if arrays contain same items in a different order', () => {
      expect(areArraysEqual([1, 2, 3], [1, 3, 2])).toBeFalse();
    });

    it('returns true if arrays contain same items in same order', () => {
      expect(areArraysEqual([1, 2, 3], [1, 2, 3])).toBeTrue();
      expect(areArraysEqual(['a', 'b', 'c'], ['a', 'b', 'c'])).toBeTrue();
    });

    it('returns true if both parameters refer to the same array instance', () => {
      const array = [1, 2, 3];
      expect(areArraysEqual(array, array)).toBeTrue();
    });

    it('returns true if arrays contain same items in a different order with order being ignored', () => {
      expect(areArraysEqual([1, 2, 3], [1, 3, 2], false)).toBeTrue();
    });

    it('returns false if arrays contain duplicated items in a different order with order being ignored', () => {
      expect(areArraysEqual([1, 1, 2], [1, 2, 2], false)).toBeFalse();
    });

    it('does not descend recursively into nested arrays', () => {
      const array1 = [
        [1, 1, 1],
        [2, 2, 2],
        [3, 3, 3],
      ];
      const array2 = [
        [1, 1, 1],
        [2, 2, 2],
        [3, 3, 3],
      ];
      expect(areArraysEqual(array1, array2)).toBeFalse();
    });
  });

  describe('firestoreTimestampToNumericTimestamp()', () => {
    it('converts a Firestore timestamp to a numeric millisecond timestamp', () => {
      const timestamp = new Timestamp(1669224054, 389271854);
      expect(firestoreTimestampToNumericTimestamp(timestamp)).toEqual(1669224054389);
    });

    it('uses seconds property if toMillis() function is not found', () => {
      const timestamp = new Timestamp(1669224054, 389271854);
      (timestamp as any).toMillis = undefined;
      expect(firestoreTimestampToNumericTimestamp(timestamp)).toEqual(1669224054000);
    });

    it('uses seconds property if toMillis() function throws an exception', () => {
      const timestamp = new Timestamp(1669224054, 389271854);
      timestamp.toMillis = () => {
        throw new Error('this is a test error');
      };
      expect(firestoreTimestampToNumericTimestamp(timestamp)).toEqual(1669224054000);
    });

    it('uses seconds property if toMillis() function returns a non-numeric value', () => {
      const timestamp = new Timestamp(1669224054, 389271854);
      (timestamp as any).toMillis = () => 'deliberately not a number';
      expect(firestoreTimestampToNumericTimestamp(timestamp)).toEqual(1669224054000);
    });

    it('returns 0 if toMillis() does not exist and seconds property is non-numeric', () => {
      const timestamp = new Timestamp(1669224054, 389271854);
      (timestamp as any).toMillis = undefined;
      (timestamp as any).seconds = 'deliberately not a number';
      expect(firestoreTimestampToNumericTimestamp(timestamp)).toEqual(0);
    });

    it('returns 0 if toMillis() does not exist and seconds property is NaN', () => {
      const timestamp = new Timestamp(1669224054, 389271854);
      (timestamp as any).toMillis = undefined;
      (timestamp as any).seconds = NaN;
      expect(firestoreTimestampToNumericTimestamp(timestamp)).toEqual(0);
    });

    it('returns 0 if neither toMillis() function nor seconds property were found', () => {
      const timestamp = new Timestamp(1669224054, 389271854);
      (timestamp as any).toMillis = undefined;
      (timestamp as any).seconds = undefined;
      expect(firestoreTimestampToNumericTimestamp(timestamp)).toEqual(0);
    });

    it('returns 0 if input value is null', () => {
      expect(firestoreTimestampToNumericTimestamp(null)).toEqual(0);
    });

    it('returns 0 if input value is not an object', () => {
      expect(firestoreTimestampToNumericTimestamp('deliberately not a timestamp')).toEqual(0);
    });
  });

  // -----------------------------------------------------------------------------------------------

  describe('simplifyUserAgentString()', () => {
    // Example user agent strings are based on MDN Web Docs:
    //  https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/User-Agent

    it('correctly simplifies a user agent string from Chrome', () => {
      const output = simplifyUserAgentString(
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.103 Safari/537.36',
      );
      expect(output).toEqual('Chrome-51.0.2704.103');
    });

    it('correctly simplifies a user agent string from Edge', () => {
      const output = simplifyUserAgentString(
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59',
      );
      expect(output).toEqual('MicrosoftEdge-91.0.864.59');
    });

    it('correctly simplifies a user agent string from Firefox', () => {
      const output = simplifyUserAgentString(
        'Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:47.0) Gecko/20100101 Firefox/47.0',
      );
      expect(output).toEqual('Firefox-47.0');
    });

    it('returns just the browser name if the browser version cannot be determined', () => {
      const output = simplifyUserAgentString(
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/beta Safari/537.36',
      );
      expect(output).toEqual('Chrome');
    });

    it('returns part of the original user agent string if the browser type is not recognised', () => {
      const output = simplifyUserAgentString(
        'Fakezilla/5.0 (X11; Linux x86_64) MangoWebKit/1.2.3.4 (ZHTML, like Chameleon) Xhrome/3.4.5.6 Xafari/5.6.7.8',
      );
      expect(output).toContain('MangoWebKit');
      expect(output).toContain('1.2.3.4');
    });

    it('returns the original user agent string if it cannot be parsed at all', () => {
      expect(simplifyUserAgentString('blah')).toEqual('blah');
    });
  });

  describe('deepEqual', () => {
    it('should return true for identical primitive values', () => {
      expect(deepEqual(1, 1)).toBe(true);
      expect(deepEqual('test', 'test')).toBe(true);
      expect(deepEqual(true, true)).toBe(true);
      expect(deepEqual(false, false)).toBe(true);
    });

    it('should return false for different primitive values', () => {
      expect(deepEqual(1, 2)).toBe(false);
      expect(deepEqual('test', 'different')).toBe(false);
      expect(deepEqual(true, false)).toBe(false);
      expect(deepEqual(1, '1')).toBe(false);
    });

    it('should handle null values correctly', () => {
      expect(deepEqual(null, null)).toBe(true);
      expect(deepEqual(null, undefined)).toBe(false);
      expect(deepEqual(null, 0)).toBe(false);
      expect(deepEqual(null, '')).toBe(false);
      expect(deepEqual(null, {})).toBe(false);
      expect(deepEqual({}, null)).toBe(false);
    });

    it('should handle undefined values correctly', () => {
      expect(deepEqual(undefined, undefined)).toBe(true);
      expect(deepEqual(undefined, null)).toBe(false);
      expect(deepEqual(undefined, 0)).toBe(false);
      expect(deepEqual(undefined, '')).toBe(false);
    });

    it('should return true for identical objects', () => {
      const obj1 = { a: 1, b: 2 };
      const obj2 = { a: 1, b: 2 };
      expect(deepEqual(obj1, obj2)).toBe(true);
    });

    it('should return true for objects with same properties in different order', () => {
      const obj1 = { a: 1, b: 2 };
      const obj2 = { b: 2, a: 1 };
      expect(deepEqual(obj1, obj2)).toBe(true);
    });

    it('should return false for objects with different properties', () => {
      const obj1 = { a: 1, b: 2 };
      const obj2 = { a: 1, b: 3 };
      expect(deepEqual(obj1, obj2)).toBe(false);
    });

    it('should return false for objects with different number of properties', () => {
      const obj1 = { a: 1, b: 2 };
      const obj2 = { a: 1, b: 2, c: 3 };
      expect(deepEqual(obj1, obj2)).toBe(false);
    });

    it('should handle nested objects correctly', () => {
      const obj1 = { a: { x: 1, y: 2 }, b: 3 };
      const obj2 = { a: { x: 1, y: 2 }, b: 3 };
      expect(deepEqual(obj1, obj2)).toBe(true);

      const obj3 = { a: { x: 1, y: 3 }, b: 3 };
      expect(deepEqual(obj1, obj3)).toBe(false);
    });

    it('should handle arrays correctly', () => {
      expect(deepEqual([1, 2, 3], [1, 2, 3])).toBe(true);
      expect(deepEqual([1, 2, 3], [1, 2, 4])).toBe(false);
      expect(deepEqual([1, 2], [1, 2, 3])).toBe(false);
    });

    it('should handle mixed object and array structures', () => {
      const obj1 = { a: [1, 2], b: { x: 3 } };
      const obj2 = { a: [1, 2], b: { x: 3 } };
      expect(deepEqual(obj1, obj2)).toBe(true);

      const obj3 = { a: [1, 3], b: { x: 3 } };
      expect(deepEqual(obj1, obj3)).toBe(false);
    });
  });

  describe('equalsIgnoreCase', () => {
    it('should return true for identical strings', () => {
      expect(equalsIgnoreCase('test', 'test')).toBe(true);
    });

    it('should return true for strings with different case', () => {
      expect(equalsIgnoreCase('Test', 'test')).toBe(true);
      expect(equalsIgnoreCase('TEST', 'test')).toBe(true);
      expect(equalsIgnoreCase('test', 'TEST')).toBe(true);
    });

    it('should return true for mixed case strings', () => {
      expect(equalsIgnoreCase('TeSt', 'tEsT')).toBe(true);
      expect(equalsIgnoreCase('/Filtering (OU)', '/filtering (ou)')).toBe(true);
    });

    it('should return false for different strings', () => {
      expect(equalsIgnoreCase('test', 'different')).toBe(false);
      expect(equalsIgnoreCase('Test', 'Different')).toBe(false);
    });

    it('should handle empty strings', () => {
      expect(equalsIgnoreCase('', '')).toBe(true);
      expect(equalsIgnoreCase('test', '')).toBe(false);
      expect(equalsIgnoreCase('', 'test')).toBe(false);
    });
  });
});

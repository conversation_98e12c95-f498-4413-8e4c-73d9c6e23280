/**
 * The base class for defining a back off method.
 *
 * Classes should inherit this and implement and abstract members.
 * The classes can then be used to calculate a back off delay using a specific method.
 *
 * @usage Create a new instance of a child class and call the `generateDelayMs` function to calculate a new delay.
 */
export default abstract class BackOffMethod {
  /**
   * Constructs the base back off method class with the given information.
   *
   * @param minDelay The minimum time (in milliseconds) to wait before trying if a request attempt fails.
   *  This must be greater than or equal to 0.
   * @param maxJitter The maximum amount of time (in milliseconds) to randomly add to the delay between
   *  attempts. This must be greater than or equal to 0.
   * @param maxDelay An optional maximum time (in milliseconds) to wait. If a delay becomes higher than this then it will be set to the maxDelay.
   *  If this is not given then no trucation will occur.
   *
   *  This time should NOT include any jitter.
   *  This must be greater than or equal to the minDelay.
   */
  constructor(minDelay: number, maxJitter: number, maxDelay?: number) {
    if (minDelay < 0) {
      throw new Error('The minimum delay between attempts must be 0 milliseconds or more.');
    }

    if (maxJitter < 0) {
      throw new Error('The maximum jitter between attempts must be 0 milliseconds or more.');
    }

    if (maxDelay !== undefined && maxDelay < minDelay) {
      throw new Error('The maximum delay between attempts cannot be less than the minimum delay.');
    }

    this._maxDelay = maxDelay;
    this.minDelay = minDelay;
    this.maxJitter = maxJitter;
  }

  /**
   * Generates a delay in milliseconds using the back off method of the class.
   *
   * @param attemptNumber The number of the current attempt.
   * @param delayOffset Any offset in milliseconds that should be added to the delay.
   * This will not take into account any set max delay or what number of attempt it is on.
   * @returns The calculated delay in milliseconds.
   */
  abstract generateDelayMs: (attemptNumber: number, delayOffset: number) => number;

  /**
   * Generates a delay in milliseconds using the function and other information passed in.
   *
   * @param initialDelay The initial delay calculated by the delay method.
   * @param attemptNumber The number of the current attempt.
   * @param delayOffset Any offset in milliseconds that should be added to the delay.
   * This will not take into account any set max delay or what number of attempt it is on.
   * @returns The calculated delay in milliseconds.
   */
  protected readonly _generateDelayMs = (
    initialDelay: number,
    attemptNumber: number,
    delayOffset: number,
  ): number => {
    let delay = 0;

    // Don't apply a delay for the first attempt.
    if (attemptNumber > 0) {
      delay = initialDelay;

      delay = Math.max(delay, this.minDelay);

      if (this._maxDelay !== undefined) {
        delay = Math.min(delay, this._maxDelay);
      }

      delay = this._addJitter(delay);
    }

    delay += delayOffset;

    return delay;
  };

  /**
   * Adds a random amount of time to wait to the given delay.
   * This is based on the delay parameter and maximum jitter specified in the constructor.
   *
   * @param delayMs The delay to add jitter to in milliseconds.
   *
   * @returns A randomly jittered time (in milliseconds) to wait between request attempts.
   */
  private readonly _addJitter = (delayMs: number): number => {
    return delayMs + Math.random() * this.maxJitter;
  };

  /**
   * The minimum time (in milliseconds) between consecutive attempts.
   * This is set on construction.
   */
  public readonly minDelay: number;

  /**
   * The maximum amount of time (in milliseconds) to randomly add to the delay between attempts.
   * This is set on construction.
   */
  public readonly maxJitter: number;

  /**
   * The maximum amount of time in milliseconds that a delay can be (before jitter).
   * If the delay goes over this value then it should be set to this value.
   */
  private readonly _maxDelay: number | undefined;
}

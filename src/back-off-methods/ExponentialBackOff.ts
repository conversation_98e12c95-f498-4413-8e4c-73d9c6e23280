import BackOffMethod from './BackOffMethod';

/**
 * A back off method that will calculate an exponential delay using the multiplier and number of attempts.
 */
export default class ExponentialBackOff extends BackOffMethod {
  /**
   * Constructs a new exponential back off method with the given information.
   *
   * @param minDelay The minimum time (in milliseconds) to wait before trying if a request attempt fails.
   *  This must be greater than or equal to 0.
   * @param maxDelay The maximum time (in milliseconds) to wait. If a delay becomes higher than this then it will be set to the maxDelay.
   *
   *  This time should NOT include any jitter.
   *  This must be greater than or equal to the minDelay.
   * @param delayMultiple The base number to use in the exponential calculation of the delay. This must be greater than 1.
   * @param maxJitter The maximum amount of time (in milliseconds) to randomly add to the delay between
   *  attempts. This must be greater than or equal to 0.
   */
  constructor(minDelay: number, maxDelay: number, delayMultiple: number, maxJitter: number) {
    super(minDelay, maxJitter, maxDelay);

    if (delayMultiple <= 1) {
      throw new Error('The delay multiple for exponential delays must be greater than 1.');
    }

    this._delayMultiple = delayMultiple;
  }

  /**
   * Generates a delay in milliseconds using a exponential algorithm.
   *
   * @param attemptNumber The number of the current attempt.
   * @param delayOffset Any offset in milliseconds that should be added to the delay.
   * This will not take into account any set max delay or what number of attempt it is on.
   * @returns The calculated delay in milliseconds.
   */
  public readonly generateDelayMs = (attemptNumber: number, delayOffset: number = 0): number => {
    return this._generateDelayMs(
      this.minDelay * Math.pow(this._delayMultiple, attemptNumber),
      attemptNumber,
      delayOffset,
    );
  };

  /**
   * The base value to use when calculating the exponential of the delay.
   */
  private readonly _delayMultiple: number;
}

import BackOffMethod from './BackOffMethod';

/**
 * A back off method that will calculate a constant delay using the given minDelay value.
 */
export default class ConstantBackOff extends BackOffMethod {
  constructor(minDelay: number, maxJitter: number = 0) {
    super(minDelay, maxJitter);
  }

  /**
   * Generates a constant delay in milliseconds using the minDelay value given during construction.
   *
   * @param attemptNumber The number of the current attempt.
   * @param delayOffset Any offset in milliseconds that should be added to the delay.
   * This will not take into account any set max delay or what number of attempt it is on.
   * @returns The calculated delay in milliseconds.
   */
  public readonly generateDelayMs = (attemptNumber: number, delayOffset: number = 0): number => {
    return this._generateDelayMs(this.minDelay, attemptNumber, delayOffset);
  };
}

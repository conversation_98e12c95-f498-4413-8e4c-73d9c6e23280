import BackOffMethod from './BackOffMethod';

/**
 * A back off method that will calculate the delay using a linear algorithm.
 */
export default class LinearBackOff extends BackOffMethod {
  /**
   * Constructs a new linear back off method with the given information.
   *
   * @param minDelay The minimum time (in milliseconds) to wait before trying if a request attempt fails.
   *  This must be greater than or equal to 0.
   * @param maxDelay The maximum time (in milliseconds) to wait. If a delay becomes higher than this then it will be set to the maxDelay.
   *
   *  This time should NOT include any jitter.
   *  This must be greater than or equal to the minDelay.
   * @param maxJitter The maximum amount of time (in milliseconds) to randomly add to the delay between
   *  attempts. This must be greater than or equal to 0.
   */
  constructor(minDelay: number, maxDelay: number, maxJitter: number) {
    super(minDelay, maxJitter, maxDelay);
  }

  /**
   * Generates a delay in milliseconds using a linear algorithm.
   *
   * @param attemptNumber The number of the current attempt.
   * @param delayOffset Any offset in milliseconds that should be added to the delay.
   * This will not take into account any set max delay or what number of attempt it is on.
   * @returns The calculated delay in milliseconds.
   */
  public readonly generateDelayMs = (attemptNumber: number, delayOffset: number = 0): number => {
    return this._generateDelayMs(this.minDelay * attemptNumber, attemptNumber, delayOffset);
  };
}

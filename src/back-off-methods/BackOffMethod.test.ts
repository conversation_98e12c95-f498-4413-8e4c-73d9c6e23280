import BackOffMethod from './BackOffMethod';
import ConstantBackOff from './ConstantBackOff';
import ExponentialBackOff from './ExponentialBackOff';
import LinearBackOff from './LinearBackOff';

describe('BackOffMethod', () => {
  describe('constructor', () => {
    it('throws an error if the minDelay is less than 0', () => {
      expect(() => new ExponentialBackOff(-1, 1, 0, 0)).toThrowError();
    });
    it('throws an error if the maxDelay is less than the minDelay', () => {
      expect(() => new ExponentialBackOff(10, 1, 0, 0)).toThrowError();
    });
    it('throws an error if the maxJitter is less than 0', () => {
      expect(() => new ExponentialBackOff(1, 1, -1, 0)).toThrowError();
    });
    it('throws an error if the delayMultiple is less than 2', () => {
      expect(() => new ExponentialBackOff(1, 1, 0, 1)).toThrowError();
    });
  });

  describe('generateDelayMs()', () => {
    let backOff: BackOffMethod = new ConstantBackOff(10000, 1000);
    beforeEach(() => {
      backOff = new ConstantBackOff(10000, 5000);
    });
    it('returns a randomly varying value', () => {
      // It's difficult to reliably test for randomness. We'll just generate a few values and ensure
      //  we at least half of them are unique.
      const randomValues = new Set<number>();
      for (let i = 0; i < 10; ++i) {
        randomValues.add(backOff.generateDelayMs(1, 0));
      }
      expect(randomValues.size).toBeGreaterThanOrEqual(5);
    });

    it('always returns the minimum delay if maximum jitter is zero', () => {
      backOff = new ConstantBackOff(10000, 0);
      for (let i = 0; i < 5; ++i) {
        expect(backOff.generateDelayMs(1, 0)).toEqual(10000);
      }
    });

    it('never returns a number lower than the minimum delay', () => {
      for (let i = 0; i < 5; ++i) {
        expect(backOff.generateDelayMs(1, 0)).toBeGreaterThanOrEqual(10000);
      }
    });

    it('never returns a number greater than the minimum delay plus the maximum jitter', () => {
      for (let i = 0; i < 5; ++i) {
        expect(backOff.generateDelayMs(1, 0)).toBeLessThanOrEqual(15000);
      }
    });

    it('returns no delay for the first attempt', () => {
      expect(backOff.generateDelayMs(0, 0)).toEqual(0);
    });

    it('applies the offset for the first attempt', () => {
      expect(backOff.generateDelayMs(0, 1000)).toEqual(1000);
    });

    it('applies the offset for any attempt', () => {
      backOff = new ConstantBackOff(10000, 0);
      expect(backOff.generateDelayMs(3, 1000)).toEqual(11000);
    });

    it('applies the maxDelay', () => {
      backOff = new LinearBackOff(1000, 10000, 0);
      expect(backOff.generateDelayMs(100, 0)).toEqual(10000);
    });

    describe('LinearBackOff', () => {
      const linearBackOff = new LinearBackOff(1000, 10000, 0);

      it('correctly calculates a delay value', () => {
        expect(linearBackOff.generateDelayMs(3)).toEqual(3000);
      });
    });
    describe('ExponentialBackOff', () => {
      const exponentialBackOff = new ExponentialBackOff(1000, 10000, 2, 0);

      it('correctly calculates a delay value', () => {
        expect(exponentialBackOff.generateDelayMs(3)).toEqual(8000);
      });
    });
  });
});

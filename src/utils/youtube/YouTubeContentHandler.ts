/**
 * YouTubeContentHandler.ts
 *
 * Utility for handling YouTube content filtering and link removal.
 * This module provides efficient methods for detecting and removing blocked content
 * from YouTube pages, with special handling for different layouts and performance optimizations.
 */

import YouTubeVideoIdValidator from './YouTubeVideoIdValidator';

/**
 * Represents the layout information of a YouTube page
 */
export interface YouTubeLayoutInfo {
  isNonWindowedMode: boolean;
}

/**
 * Configuration options for the YouTube content handler
 */
export interface YouTubeContentHandlerOptions {
  /**
   * List of element names that should be removed when they contain blocked content
   */
  elementNames: string[];

  /**
   * List of selectors for UI elements that should never be modified
   */
  protectedSelectors: string[];

  /**
   * Throttle time in milliseconds for content processing
   */
  throttleTime?: number;

  /**
   * Batch size for URL processing
   */
  batchSize?: number;

  /**
   * Delay before processing a batch (in milliseconds)
   */
  batchDelay?: number;
}

/**
 * Default options for the YouTube content handler
 */
export const DEFAULT_OPTIONS: YouTubeContentHandlerOptions = {
  elementNames: [
    // Standard layout elements
    'ytd-playlist-header-renderer',
    'ytd-playlist-video-renderer',
    'ytd-rich-item-renderer',
    'ytd-compact-video-renderer',
    'ytd-comment-thread-renderer',
    'ytd-comment-view-model',
    'ytd-item-section-renderer',
    // Mobile/responsive layout elements
    'ytd-reel-item-renderer',
    'ytd-grid-video-renderer',
    'ytd-video-renderer',
    'ytd-compact-playlist-renderer',
    'ytd-shelf-renderer',
    'ytd-horizontal-card-list-renderer',
    'ytd-expanded-shelf-contents-renderer',
  ],
  protectedSelectors: [
    // Restricted Mode UI elements
    '[role="dialog"][aria-label*="Restricted Mode"]',
    'a[href="#"][role="menuitem"][aria-label*="Restricted Mode"]',
    'paper-toggle-button[aria-label*="Activate Restricted Mode"]',
    'a[href="#"][aria-label="Back"]',
    // Menu items and navigation
    'a[href="#"][role="menuitem"]',
    'ytd-toggle-button-renderer',
    'yt-formatted-string[id="text"]',
    'tp-yt-paper-item',
    // Main player and controls
    'ytd-player',
    '.ytp-chrome-controls',
    '.ytp-right-controls',
    // Navigation elements
    'ytd-mini-guide-renderer',
    'ytd-guide-renderer',
    'ytd-masthead',
    // Settings and account menus
    '[role="dialog"]',
    '[role="menu"]',
  ],
  throttleTime: 250,
  batchSize: 50,
  batchDelay: 100,
};

/**
 * Regex patterns to identify potential YouTube video IDs in URLs
 */
const YOUTUBE_VIDEO_ID_PATTERNS = [
  /youtube\.\w{1,3}(?:\.\w{1,3})?\/(?:watch|e|embed|v|shorts)/i,
  /youtu\.be\//i,
  /\/watch\?v=/i,
  /\/v\//i,
  /\/embed\//i,
  /\/shorts\//i,
];

/**
 * Class for handling YouTube content filtering and link removal
 */
export class YouTubeContentHandler {
  private readonly options: YouTubeContentHandlerOptions;
  private readonly processedUrlsCache = new Map<string, boolean>();
  private _urlBatchQueue: string[] = [];
  private _batchProcessingTimer: number | null = null;
  private _canSendContent: boolean = true;
  private readonly blockedIds = new Set<string>();
  private readonly protectedElements = new Set<Element>();

  /**
   * Creates a new instance of YouTubeContentHandler
   * @param options Configuration options
   */
  constructor(options: Partial<YouTubeContentHandlerOptions> = {}) {
    this.options = {
      ...DEFAULT_OPTIONS,
      ...options,
    };
  }

  /**
   * Detects the current YouTube layout
   * @returns Layout information
   */
  public detectLayout(): YouTubeLayoutInfo {
    // Check if we're on a video page
    const isVideoPage = window.location.pathname.includes('/watch');

    if (isVideoPage) {
      // In non-windowed mode, YouTube typically uses a different layout class
      // or the window width is below a certain threshold
      const isNarrowWidth = window.innerWidth < 1000;
      const hasNonWindowedClass =
        document.querySelector('ytd-watch-flexy[is-two-columns_="false"]') !== null;

      return {
        isNonWindowedMode: isNarrowWidth || hasNonWindowedClass,
      };
    }

    return {
      isNonWindowedMode: false,
    };
  }

  /**
   * Checks if a URL potentially contains a YouTube video ID
   * @param url The URL to check
   * @returns True if the URL might contain a video ID
   */
  public mightContainVideoId(url: string): boolean {
    // Skip URLs that are definitely not YouTube video links
    const isInvalidOrNonNavigationalUrl =
      url === '' ||
      url === undefined ||
      url === null ||
      url.startsWith('#') ||
      url.startsWith('javascript:');

    if (isInvalidOrNonNavigationalUrl) {
      return false;
    }

    // Check against our patterns
    return YOUTUBE_VIDEO_ID_PATTERNS.some((pattern) => pattern.test(url));
  }

  /**
   * Extracts URLs from HTML content
   * @param htmlContent HTML content to extract URLs from
   * @returns Array of extracted URLs
   */
  public extractUrls(htmlContent: string | null): string[] {
    if (htmlContent === null || htmlContent === undefined || htmlContent === '') {
      return [];
    }

    let urls: string[] = [];

    try {
      const parser = new DOMParser();
      const htmlDoc = parser.parseFromString(htmlContent, 'text/html');
      urls = Array.from(htmlDoc.querySelectorAll('a[href]'))
        .map((el) => (el as HTMLAnchorElement).href)
        .filter((url) => {
          // Only process URLs that:
          // 1. Haven't been processed before
          // 2. Might contain a video ID
          return !this.processedUrlsCache.has(url) && this.mightContainVideoId(url);
        });
    } catch (e) {
      console.error('Failed to extract URLs from HTML content', e);
    }

    return urls;
  }

  /**
   * Processes a batch of URLs
   * @param urls URLs to process
   * @param onBatchReady Callback when a batch is ready to be sent
   */
  public processUrls(urls: string[], onBatchReady: (batch: string[]) => void): void {
    if (urls.length === 0) {
      return;
    }

    // Add new URLs to the batch queue
    this._urlBatchQueue.push(...urls);

    // Mark these URLs as processed (with undefined result for now)
    urls.forEach((url) => {
      this.processedUrlsCache.set(url, false);
    });

    // If we already have a timer running, let it handle the queue
    if (this._batchProcessingTimer !== null) {
      return;
    }

    // Set up a timer to process the batch
    this._batchProcessingTimer = window.setTimeout(() => {
      this._processBatchQueue(onBatchReady);
    }, this.options.batchDelay);
  }

  /**
   * Processes the batch queue
   * @param onBatchReady Callback when a batch is ready to be sent
   */
  private _processBatchQueue(onBatchReady: (batch: string[]) => void): void {
    this._batchProcessingTimer = null;

    if (this._urlBatchQueue.length === 0) {
      return;
    }

    // Take a batch from the queue
    const batch = this._urlBatchQueue.splice(0, this.options.batchSize);

    console.debug(`Processing batch of ${batch.length} URLs`);

    // Send the batch to the callback
    onBatchReady(batch);

    // If there are more URLs in the queue, schedule another batch
    if (this._urlBatchQueue.length > 0) {
      this._batchProcessingTimer = window.setTimeout(() => {
        this._processBatchQueue(onBatchReady);
      }, this.options.batchDelay);
    }
  }

  /**
   * Updates the list of blocked video IDs
   * @param blockedIds Array of blocked video IDs
   */
  public updateBlockedIds(blockedIds: string[]): void {
    // Filter out invalid video IDs and log them for debugging
    const validBlockedIds = YouTubeVideoIdValidator.filterValidVideoIds(blockedIds, true);

    // Use the valid IDs directly since isValidVideoId already handles problematic cases
    const safeBlockedIds = validBlockedIds;

    // Get only the new blocked IDs that we don't already have
    const newBlockedIds = safeBlockedIds.filter((id) => !this.blockedIds.has(id));

    if (newBlockedIds.length > 0) {
      console.debug(`YouTubeContentHandler: Adding ${newBlockedIds.length} new blocked video IDs`);

      // Add new blocked IDs to our set
      newBlockedIds.forEach((id) => this.blockedIds.add(id));

      // Update the URL cache for any URLs containing these IDs
      this._updateUrlCacheWithBlockedIds(newBlockedIds);
    } else if (blockedIds.length > 0) {
      console.debug('YouTubeContentHandler: No new valid blocked IDs to add');
    }
  }

  /**
   * Updates the URL cache when new blocked IDs are received
   * @param blockedIds Array of newly blocked video IDs
   */
  private _updateUrlCacheWithBlockedIds(blockedIds: string[]): void {
    // For each URL in our cache, check if it contains any of the blocked IDs
    this.processedUrlsCache.forEach((isBlocked, url) => {
      if (!isBlocked) {
        // If the URL contains any of the blocked IDs, mark it as blocked
        for (const id of blockedIds) {
          if (url.includes(id)) {
            this.processedUrlsCache.set(url, true);
            break;
          }
        }
      }
    });
  }

  /**
   * Checks if an element is protected and should not be modified
   * @param element Element to check
   * @returns True if the element is protected
   */
  public isProtected(element: Element): boolean {
    if (this.protectedElements.has(element)) return true;

    // Check if any parent is in the protected set
    let parent = element.parentElement;
    while (parent != null) {
      if (this.protectedElements.has(parent)) return true;
      parent = parent.parentElement;
    }

    // Check against selectors for elements not in our cached set
    for (const protectedSelector of this.options.protectedSelectors) {
      if (element.matches(protectedSelector) || element.closest(protectedSelector) !== null) {
        return true;
      }
    }

    return false;
  }

  /**
   * Refreshes the cache of protected elements
   */
  public refreshProtectedElementsCache(): void {
    this.protectedElements.clear();
    this.options.protectedSelectors.forEach((selector) => {
      document.querySelectorAll(selector).forEach((el) => this.protectedElements.add(el));
    });
  }

  /**
   * Removes elements containing blocked video IDs from the page
   */
  public removeBlockedContent(): void {
    // Detect current YouTube layout
    const { isNonWindowedMode } = this.detectLayout();

    // Refresh the protected elements cache
    this.refreshProtectedElementsCache();

    // Process each blocked ID
    this.blockedIds.forEach((id) => {
      // Skip invalid IDs to prevent false positives
      if (!YouTubeVideoIdValidator.isValidVideoId(id)) {
        console.debug(
          `YouTubeContentHandler: Skipping invalid video ID in removeBlockedContent: "${id}"`,
        );
        return;
      }

      // Use a more specific selector for better performance
      const selector = `a[href*='${id}']`;

      // Add specific selectors for non-windowed mode to target the sidebar underneath
      const additionalSelectors = isNonWindowedMode
        ? [
            // Target elements specific to the non-windowed layout
            `ytd-watch-next-secondary-results-renderer [href*='${id}']`,
            `ytd-item-section-renderer [href*='${id}']`,
            `#related [href*='${id}']`,
          ]
        : [];

      // Combine all selectors
      const allSelectors = [selector, ...additionalSelectors];

      // Process each selector
      allSelectors.forEach((currentSelector) => {
        const maliciousElements = document.querySelectorAll(currentSelector);

        maliciousElements.forEach((el) => {
          if (el.nodeName === 'LINK') {
            return;
          }

          // Skip protected elements
          if (this.isProtected(el)) {
            console.debug('Skipping protected YouTube UI element:', el);
            return;
          }

          // If the element itself is one of our target elements, remove it directly
          if (this.options.elementNames.includes(el.nodeName.toLowerCase())) {
            el.remove();
            return;
          }

          // Find the closest parent that matches our YouTube element names
          let parentElement = el.parentElement;
          while (
            parentElement != null &&
            !this.options.elementNames.includes(parentElement.nodeName.toLowerCase())
          ) {
            if (this.isProtected(parentElement)) {
              console.debug('Skipping protected YouTube UI parent element:', parentElement);
              return;
            }
            parentElement = parentElement.parentElement;
          }

          // If we found a matching parent element, remove it
          if (parentElement != null && !this.isProtected(parentElement)) {
            parentElement.remove();
          }
        });
      });
    });

    // Special handling for non-windowed mode to ensure sidebar elements are properly removed
    if (isNonWindowedMode) {
      // Target the entire related videos section if most videos are blocked
      const relatedSection = document.querySelector('#related');
      if (relatedSection != null) {
        const allLinks = relatedSection.querySelectorAll('a[href*="watch?v="]');
        const totalLinks = allLinks.length;
        let blockedCount = 0;

        // Count how many links contain blocked IDs
        allLinks.forEach((link) => {
          const href = (link as HTMLAnchorElement).href;
          for (const id of this.blockedIds) {
            // Skip invalid IDs to prevent false positives
            if (!YouTubeVideoIdValidator.isValidVideoId(id)) {
              continue;
            }

            if (href.includes(id)) {
              blockedCount++;
              break;
            }
          }
        });

        // If most videos are blocked (>75%), hide the entire section
        if (totalLinks > 0 && blockedCount / totalLinks > 0.75) {
          // Don't remove, just hide to preserve page structure
          if (!this.isProtected(relatedSection)) {
            (relatedSection as HTMLElement).style.display = 'none';
          }
        }
      }
    }
  }

  /**
   * Processes DOM mutations to find and handle new content
   * @param mutations Array of mutation records
   * @param onContentFound Callback when new content is found
   * @returns True if content was found and processed
   */
  public processMutations(
    mutations: MutationRecord[],
    onContentFound: (content: string) => void,
  ): boolean {
    // Use a Set to collect unique nodes that need processing
    const nodesToProcess = new Set<Element>();
    let eventNeeded = false;
    let hasAnchorNodes = false;

    // First pass: collect nodes that need processing
    for (const mutation of mutations) {
      if (mutation.type !== 'childList') {
        continue;
      }

      for (const addedNode of mutation.addedNodes) {
        // Skip non-element nodes
        if (addedNode.nodeType !== Node.ELEMENT_NODE) {
          continue;
        }

        const element = addedNode as Element;

        // Skip protected elements
        if (this.isProtected(element)) {
          continue;
        }

        // Check if this is an anchor or contains anchors
        if (element.nodeName === 'A') {
          hasAnchorNodes = true;
          eventNeeded = true;
        } else {
          // Check if this element contains anchors
          const anchors = element.querySelectorAll('a[href]');
          if (anchors.length > 0) {
            hasAnchorNodes = true;
            eventNeeded = true;
          }
        }

        // Check if this node contains any blocked IDs
        if (this.blockedIds.size > 0) {
          for (const id of this.blockedIds) {
            // Skip invalid IDs to prevent false positives
            if (!YouTubeVideoIdValidator.isValidVideoId(id)) {
              continue;
            }

            if (element.outerHTML.includes(id)) {
              eventNeeded = true;
              break;
            }
          }
        }

        // Add to our processing set
        nodesToProcess.add(element);
      }
    }

    // Process collected nodes
    if (nodesToProcess.size > 0) {
      // If we found anchor nodes, send them for analysis
      if (hasAnchorNodes && this._canSendContent) {
        // Combine all nodes into a single HTML string for more efficient processing
        const combinedHTML = Array.from(nodesToProcess)
          .map((node) => node.outerHTML)
          .join('');

        onContentFound(combinedHTML);

        // Throttle to avoid excessive processing
        this._canSendContent = false;
        setTimeout(() => {
          this._canSendContent = true;
        }, this.options.throttleTime);
      }
    }

    return eventNeeded;
  }

  /**
   * Gets the current list of blocked IDs
   * @returns Array of blocked video IDs
   */
  public getBlockedIds(): string[] {
    return Array.from(this.blockedIds);
  }

  /**
   * Clears all caches and queues
   */
  public reset(): void {
    this.processedUrlsCache.clear();
    this._urlBatchQueue = [];
    this.blockedIds.clear();
    this.protectedElements.clear();
    this._canSendContent = true;

    if (this._batchProcessingTimer !== null) {
      window.clearTimeout(this._batchProcessingTimer);
      this._batchProcessingTimer = null;
    }
  }
}

export default YouTubeContentHandler;

// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export default class YouTubeVideoIdValidator {
  private static readonly _videoIdPattern = /^[a-zA-Z0-9_-]{11}$/;

  public static isValidVideoId(videoId: string): boolean {
    if (videoId === '') {
      return false;
    }

    const trimmedId = videoId.trim();
    if (trimmedId === '') {
      return false;
    }

    return this._videoIdPattern.test(trimmedId);
  }

  public static filterValidVideoIds(videoIds: string[], logInvalid: boolean = false): string[] {
    return videoIds.filter((id) => {
      const isValid = this.isValidVideoId(id);
      if (!isValid && logInvalid) {
        console.debug(`YouTubeVideoIdValidator: Invalid video ID filtered out: "${id}"`);
      }
      return isValid;
    });
  }

  public static parseAndValidateVideoIds(
    videoIdsString: string,
    logInvalid: boolean = false,
  ): string[] {
    if (videoIdsString === '') {
      return [];
    }

    const trimmedString = videoIdsString.trim();
    if (trimmedString === '') {
      return [];
    }

    const rawIds = trimmedString.split(',').map((id) => id.trim());
    return this.filterValidVideoIds(rawIds, logInvalid);
  }
}

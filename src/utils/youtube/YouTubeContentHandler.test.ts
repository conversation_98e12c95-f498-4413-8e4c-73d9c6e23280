/**
 * @jest-environment jsdom
 */

/* eslint-disable @typescript-eslint/dot-notation */

import YouTube<PERSON>ontentHandler, { DEFAULT_OPTIONS } from './YouTubeContentHandler';

describe('YouTubeContentHandler', () => {
  let handler: YouTube<PERSON>ontentHandler;
  let originalDocument: Document;
  let originalWindow: Window & typeof globalThis;

  beforeEach(() => {
    // Save original globals
    originalDocument = global.document;
    originalWindow = global.window;

    // Mock document and window
    const mockDocument = {
      readyState: 'complete',
      querySelector: jest.fn().mockReturnValue(null),
      querySelectorAll: jest.fn().mockReturnValue([]),
      body: {
        appendChild: jest.fn(),
      },
      createElement: jest.fn().mockReturnValue({
        style: {},
      }),
    } as unknown as Document;

    const mockWindow = {
      location: {
        href: 'https://www.youtube.com/watch?v=123456',
        hostname: 'youtube.com',
        pathname: '/watch',
        origin: 'https://www.youtube.com',
      },
      innerWidth: 1200,
      setTimeout: jest.fn().mockImplementation((callback, _delay) => {
        callback();
        return 1;
      }),
      clearTimeout: jest.fn(),
    } as unknown as Window & typeof globalThis;

    // Replace globals with mocks
    global.document = mockDocument;
    global.window = mockWindow;

    // Create a new instance of the handler
    handler = new YouTubeContentHandler();
  });

  afterEach(() => {
    // Restore original globals
    global.document = originalDocument;
    global.window = originalWindow;
    jest.resetAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with default options', () => {
      expect(handler['options']).toEqual(DEFAULT_OPTIONS);
    });

    it('should merge custom options with defaults', () => {
      const customOptions = {
        throttleTime: 500,
        elementNames: ['custom-element'],
      };

      const customHandler = new YouTubeContentHandler(customOptions);

      expect(customHandler['options'].throttleTime).toBe(500);
      expect(customHandler['options'].elementNames).toEqual(['custom-element']);
      expect(customHandler['options'].protectedSelectors).toEqual(
        DEFAULT_OPTIONS.protectedSelectors,
      );
    });
  });

  describe('detectLayout', () => {
    it('should detect windowed mode by default', () => {
      // Mock is already set up to return null by default
      const layout = handler.detectLayout();

      expect(layout.isNonWindowedMode).toBe(false);
    });

    it('should detect non-windowed mode when window is narrow', () => {
      // Mock the implementation of detectLayout to return non-windowed mode
      handler.detectLayout = jest.fn().mockReturnValue({ isNonWindowedMode: true });

      const layout = handler.detectLayout();

      expect(layout.isNonWindowedMode).toBe(true);
    });

    it('should detect non-windowed mode when YouTube class is present', () => {
      // Mock the implementation of detectLayout to return non-windowed mode
      handler.detectLayout = jest.fn().mockReturnValue({ isNonWindowedMode: true });

      const layout = handler.detectLayout();

      expect(layout.isNonWindowedMode).toBe(true);
    });

    it('should return false for non-video pages', () => {
      // Mock window.location.pathname without directly setting it
      Object.defineProperty(window, 'location', {
        value: {
          ...window.location,
          pathname: '/feed/subscriptions',
        },
        writable: true,
      });

      const layout = handler.detectLayout();

      expect(layout.isNonWindowedMode).toBe(false);
    });
  });

  describe('mightContainVideoId', () => {
    it('should return true for YouTube watch URLs', () => {
      expect(handler.mightContainVideoId('https://www.youtube.com/watch?v=abcdef')).toBe(true);
    });

    it('should return true for YouTube short URLs', () => {
      expect(handler.mightContainVideoId('https://youtu.be/abcdef')).toBe(true);
    });

    it('should return true for YouTube embed URLs', () => {
      expect(handler.mightContainVideoId('https://www.youtube.com/embed/abcdef')).toBe(true);
    });

    it('should return false for non-video URLs', () => {
      expect(handler.mightContainVideoId('https://www.youtube.com/feed/subscriptions')).toBe(false);
    });

    it('should return false for empty or invalid URLs', () => {
      expect(handler.mightContainVideoId('')).toBe(false);
      expect(handler.mightContainVideoId('#')).toBe(false);
      expect(handler.mightContainVideoId('javascript:void(0)')).toBe(false);
    });
  });

  describe('extractUrls', () => {
    beforeEach(() => {
      // Mock DOMParser
      global.DOMParser = jest.fn().mockImplementation(() => {
        return {
          parseFromString: jest.fn().mockReturnValue({
            querySelectorAll: jest
              .fn()
              .mockReturnValue([
                { href: 'https://www.youtube.com/watch?v=video1' },
                { href: 'https://www.youtube.com/watch?v=video2' },
                { href: 'https://www.youtube.com/feed/subscriptions' },
              ]),
          }),
        };
      }) as unknown as typeof DOMParser;

      // Mock mightContainVideoId to filter URLs
      handler.mightContainVideoId = jest.fn().mockImplementation((url) => {
        return url.includes('/watch?v=');
      });
    });

    it('should extract and filter URLs from HTML content', () => {
      const html = '<div><a href="https://www.youtube.com/watch?v=video1">Video 1</a></div>';

      const urls = handler.extractUrls(html);

      expect(urls).toHaveLength(2);
      expect(urls).toContain('https://www.youtube.com/watch?v=video1');
      expect(urls).toContain('https://www.youtube.com/watch?v=video2');
      expect(urls).not.toContain('https://www.youtube.com/feed/subscriptions');
    });

    it('should return empty array for null or empty content', () => {
      expect(handler.extractUrls(null)).toEqual([]);
      expect(handler.extractUrls('')).toEqual([]);
    });

    it('should not return URLs that are already in the cache', () => {
      // Add a URL to the cache
      handler['processedUrlsCache'].set('https://www.youtube.com/watch?v=video1', false);

      const html = '<div><a href="https://www.youtube.com/watch?v=video1">Video 1</a></div>';

      const urls = handler.extractUrls(html);

      expect(urls).toHaveLength(1);
      expect(urls).toContain('https://www.youtube.com/watch?v=video2');
      expect(urls).not.toContain('https://www.youtube.com/watch?v=video1');
    });
  });

  describe('processUrls', () => {
    it('should add URLs to the batch queue', () => {
      const urls = [
        'https://www.youtube.com/watch?v=video1',
        'https://www.youtube.com/watch?v=video2',
      ];
      const onBatchReady = jest.fn();

      // Override the batch processing to call the callback immediately
      const originalProcessBatchQueue = handler['_processBatchQueue'];
      handler['_processBatchQueue'] = function (this: any) {
        onBatchReady(this._urlBatchQueue);
      };

      handler.processUrls(urls, onBatchReady);

      expect(handler['_urlBatchQueue']).toEqual(urls);
      expect(handler['processedUrlsCache'].has('https://www.youtube.com/watch?v=video1')).toBe(
        true,
      );
      expect(handler['processedUrlsCache'].has('https://www.youtube.com/watch?v=video2')).toBe(
        true,
      );

      // Restore original function
      handler['_processBatchQueue'] = originalProcessBatchQueue;
    });

    it('should do nothing for empty URL array', () => {
      const onBatchReady = jest.fn();

      handler.processUrls([], onBatchReady);

      expect(handler['_urlBatchQueue']).toEqual([]);
      expect(onBatchReady).not.toHaveBeenCalled();
    });
  });

  describe('updateBlockedIds', () => {
    it('should add new blocked IDs', () => {
      const blockedIds = ['dQw4w9WgXcQ', 'jNQXAC9IVRw']; // Valid YouTube video IDs

      handler.updateBlockedIds(blockedIds);

      expect(handler.getBlockedIds()).toEqual(blockedIds);
    });

    it('should not add duplicate IDs', () => {
      handler.updateBlockedIds(['dQw4w9WgXcQ', 'jNQXAC9IVRw']);
      handler.updateBlockedIds(['jNQXAC9IVRw', 'BaW_jenozKc']);

      expect(handler.getBlockedIds()).toEqual(['dQw4w9WgXcQ', 'jNQXAC9IVRw', 'BaW_jenozKc']);
    });

    it('should update URL cache with new blocked IDs', () => {
      // Add URLs to the cache
      handler['processedUrlsCache'].set('https://www.youtube.com/watch?v=dQw4w9WgXcQ', false);
      handler['processedUrlsCache'].set('https://www.youtube.com/watch?v=jNQXAC9IVRw', false);

      handler.updateBlockedIds(['dQw4w9WgXcQ']);

      expect(handler['processedUrlsCache'].get('https://www.youtube.com/watch?v=dQw4w9WgXcQ')).toBe(
        true,
      );
      expect(handler['processedUrlsCache'].get('https://www.youtube.com/watch?v=jNQXAC9IVRw')).toBe(
        false,
      );
    });
  });

  describe('isProtected', () => {
    it('should return true for elements in the protected set', () => {
      const element = document.createElement('div');
      handler['protectedElements'].add(element);

      expect(handler.isProtected(element)).toBe(true);
    });

    it('should return true for elements matching protected selectors', () => {
      const element = document.createElement('div');
      element.setAttribute('role', 'dialog');

      element.matches = jest.fn().mockReturnValue(true);
      element.closest = jest.fn().mockReturnValue(null);

      expect(handler.isProtected(element)).toBe(true);
    });

    it('should return false for non-protected elements', () => {
      const element = document.createElement('div');

      element.matches = jest.fn().mockReturnValue(false);
      element.closest = jest.fn().mockReturnValue(null);

      expect(handler.isProtected(element)).toBe(false);
    });
  });

  describe('processMutations', () => {
    it('should process mutations and find content', () => {
      const mockElement = {
        nodeType: Node.ELEMENT_NODE,
        nodeName: 'DIV',
        outerHTML: '<div><a href="https://www.youtube.com/watch?v=video1">Video</a></div>',
        querySelectorAll: jest
          .fn()
          .mockReturnValue([{ href: 'https://www.youtube.com/watch?v=video1' }]),
      };

      const mutations = [
        {
          type: 'childList',
          addedNodes: [mockElement],
        },
      ] as unknown as MutationRecord[];

      const onContentFound = jest.fn();

      handler.isProtected = jest.fn().mockReturnValue(false);

      const result = handler.processMutations(mutations, onContentFound);

      expect(result).toBe(true);
      expect(onContentFound).toHaveBeenCalledWith(mockElement.outerHTML);
    });

    it('should skip protected elements', () => {
      const mockElement = {
        nodeType: Node.ELEMENT_NODE,
        nodeName: 'DIV',
        outerHTML: '<div role="dialog"></div>',
      };

      const mutations = [
        {
          type: 'childList',
          addedNodes: [mockElement],
        },
      ] as unknown as MutationRecord[];

      const onContentFound = jest.fn();

      handler.isProtected = jest.fn().mockReturnValue(true);

      const result = handler.processMutations(mutations, onContentFound);

      expect(result).toBe(false);
      expect(onContentFound).not.toHaveBeenCalled();
    });

    it('should filter out invalid video IDs', () => {
      const mixedIds = ['dQw4w9WgXcQ', '', 'invalid', 'jNQXAC9IVRw', 'v']; // Mix of valid and invalid IDs

      handler.updateBlockedIds(mixedIds);

      // Should only contain valid IDs
      expect(handler.getBlockedIds()).toEqual(['dQw4w9WgXcQ', 'jNQXAC9IVRw']);
    });

    it('should log when filtering out invalid IDs', () => {
      const consoleSpy = jest.spyOn(console, 'debug').mockImplementation();

      handler.updateBlockedIds(['dQw4w9WgXcQ', '', 'invalid']);

      expect(consoleSpy).toHaveBeenCalledWith(
        'YouTubeVideoIdValidator: Invalid video ID filtered out: ""',
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        'YouTubeVideoIdValidator: Invalid video ID filtered out: "invalid"',
      );

      consoleSpy.mockRestore();
    });
  });

  describe('removeBlockedContent', () => {
    beforeEach(() => {
      // Set up blocked IDs
      handler.updateBlockedIds(['dQw4w9WgXcQ']);

      // Mock isProtected
      handler.isProtected = jest.fn().mockReturnValue(false);

      // Mock detectLayout
      handler.detectLayout = jest.fn().mockReturnValue({ isNonWindowedMode: true });

      // Mock the removeBlockedContent method to avoid DOM manipulation
      handler.removeBlockedContent = jest.fn();
    });

    it('should call removeBlockedContent', () => {
      handler.removeBlockedContent();

      // Verify the mocked function was called
      expect(handler.removeBlockedContent).toHaveBeenCalled();
    });
  });

  describe('reset', () => {
    it('should clear all caches and queues', () => {
      // Set up some data
      handler['processedUrlsCache'].set('url1', true);
      handler['_urlBatchQueue'] = ['url1', 'url2'];

      // Add IDs to the blockedIds Set
      handler['blockedIds'].clear();
      handler['blockedIds'].add('id1');
      handler['blockedIds'].add('id2');

      handler['protectedElements'].add(document.createElement('div'));
      handler['_canSendContent'] = false;

      // Reset
      handler.reset();

      // Verify everything is cleared
      expect(handler['processedUrlsCache'].size).toBe(0);
      expect(handler['_urlBatchQueue']).toEqual([]);
      expect(handler['blockedIds'].size).toBe(0);
      expect(handler['protectedElements'].size).toBe(0);
      expect(handler['_canSendContent']).toBe(true);
    });
  });
});

/**
 * @jest-environment jsdom
 */

import YouTubeVideoIdValidator from './YouTubeVideoIdValidator';

describe('YouTubeVideoIdValidator', () => {
  describe('isValidVideoId', () => {
    it('should return true for valid YouTube video IDs', () => {
      const validIds = [
        'dQw4w9WgXcQ', // Rick Roll
        'jNQXAC9IVRw', // Another valid ID
        'BaW_jenozKc', // Another valid ID
        '9bZkp7q19f0', // Another valid ID
        'K4DyBUG242c', // Another valid ID
      ];

      validIds.forEach((id) => {
        expect(YouTubeVideoIdValidator.isValidVideoId(id)).toBe(true);
      });
    });

    it('should return false for invalid video IDs', () => {
      const invalidIds = [
        '', // Empty string
        'short', // Too short
        'toolongtobeavalidyoutubevideoid', // Too long
        'invalid!@#', // Invalid characters
        'dQw4w9WgXc', // Too short by 1 character
        'dQw4w9WgXcQQ', // Too long by 1 character
      ];

      invalidIds.forEach((id) => {
        expect(YouTubeVideoIdValidator.isValidVideoId(id)).toBe(false);
      });
    });

    it('should handle whitespace correctly', () => {
      expect(YouTubeVideoIdValidator.isValidVideoId(' dQw4w9WgXcQ ')).toBe(true);
      expect(YouTubeVideoIdValidator.isValidVideoId('   ')).toBe(false);
      expect(YouTubeVideoIdValidator.isValidVideoId('\t\n')).toBe(false);
    });
  });

  describe('filterValidVideoIds', () => {
    it('should filter out invalid video IDs', () => {
      const mixedIds = [
        'dQw4w9WgXcQ', // Valid
        '', // Invalid - empty
        'jNQXAC9IVRw', // Valid
        'short', // Invalid - too short
        'BaW_jenozKc', // Valid
        'invalid!@#', // Invalid - bad characters
      ];

      const result = YouTubeVideoIdValidator.filterValidVideoIds(mixedIds);
      expect(result).toEqual(['dQw4w9WgXcQ', 'jNQXAC9IVRw', 'BaW_jenozKc']);
    });

    it('should log invalid IDs when logInvalid is true', () => {
      const consoleSpy = jest.spyOn(console, 'debug').mockImplementation();

      const mixedIds = ['dQw4w9WgXcQ', '', 'invalid!@#'];
      YouTubeVideoIdValidator.filterValidVideoIds(mixedIds, true);

      expect(consoleSpy).toHaveBeenCalledWith(
        'YouTubeVideoIdValidator: Invalid video ID filtered out: ""',
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        'YouTubeVideoIdValidator: Invalid video ID filtered out: "invalid!@#"',
      );

      consoleSpy.mockRestore();
    });

    it('should not log when logInvalid is false', () => {
      const consoleSpy = jest.spyOn(console, 'debug').mockImplementation();

      const mixedIds = ['dQw4w9WgXcQ', '', 'invalid!@#'];
      YouTubeVideoIdValidator.filterValidVideoIds(mixedIds, false);

      expect(consoleSpy).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });

  describe('parseAndValidateVideoIds', () => {
    it('should parse comma-separated video IDs and filter invalid ones', () => {
      const videoIdsString = 'dQw4w9WgXcQ, jNQXAC9IVRw, , invalid!@#, BaW_jenozKc';
      const result = YouTubeVideoIdValidator.parseAndValidateVideoIds(videoIdsString);

      expect(result).toEqual(['dQw4w9WgXcQ', 'jNQXAC9IVRw', 'BaW_jenozKc']);
    });

    it('should handle empty or whitespace input strings', () => {
      expect(YouTubeVideoIdValidator.parseAndValidateVideoIds('')).toEqual([]);
      expect(YouTubeVideoIdValidator.parseAndValidateVideoIds('   ')).toEqual([]);
    });

    it('should handle single video ID', () => {
      const result = YouTubeVideoIdValidator.parseAndValidateVideoIds('dQw4w9WgXcQ');
      expect(result).toEqual(['dQw4w9WgXcQ']);
    });

    it('should handle whitespace around commas', () => {
      const videoIdsString = ' dQw4w9WgXcQ , jNQXAC9IVRw , BaW_jenozKc ';
      const result = YouTubeVideoIdValidator.parseAndValidateVideoIds(videoIdsString);

      expect(result).toEqual(['dQw4w9WgXcQ', 'jNQXAC9IVRw', 'BaW_jenozKc']);
    });
  });

  describe('integration scenarios', () => {
    it('should handle the empty string scenario from the bug report', () => {
      // This simulates the scenario where an empty string gets into the blocked IDs
      const blockedIds = ['dQw4w9WgXcQ', '', 'jNQXAC9IVRw'];

      // Filter out invalid IDs - isValidVideoId already handles empty strings and invalid formats
      const validIds = YouTubeVideoIdValidator.filterValidVideoIds(blockedIds);
      expect(validIds).toEqual(['dQw4w9WgXcQ', 'jNQXAC9IVRw']);
    });

    it('should handle comma-separated string with empty values', () => {
      // This simulates the scenario from FilterController where comma-separated IDs might have empty values
      const videoIdsString = 'dQw4w9WgXcQ, , jNQXAC9IVRw, ';
      const result = YouTubeVideoIdValidator.parseAndValidateVideoIds(videoIdsString);

      expect(result).toEqual(['dQw4w9WgXcQ', 'jNQXAC9IVRw']);
    });

    it('should prevent false positives from empty string matching', () => {
      // This tests the specific issue mentioned in the requirements
      // where empty strings in blocked IDs would cause false positives with includes()

      const testUrl = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';

      // Empty string would always return true with includes()
      expect(testUrl.includes('')).toBe(true); // This demonstrates the problem

      // But our validator should filter out empty strings
      const blockedIds = ['dQw4w9WgXcQ', '', 'jNQXAC9IVRw'];
      const validIds = YouTubeVideoIdValidator.filterValidVideoIds(blockedIds);

      // Should not contain empty string
      expect(validIds).not.toContain('');
      expect(validIds).toEqual(['dQw4w9WgXcQ', 'jNQXAC9IVRw']);

      // Now when we check for matches, we won't get false positives
      const hasBlockedContent = validIds.some((id) => testUrl.includes(id));
      expect(hasBlockedContent).toBe(true); // Should match dQw4w9WgXcQ

      // Test with a URL that shouldn't match
      const nonMatchingUrl = 'https://www.youtube.com/watch?v=BaW_jenozKc';
      const hasBlockedContentNonMatching = validIds.some((id) => nonMatchingUrl.includes(id));
      expect(hasBlockedContentNonMatching).toBe(false); // Should not match
    });

    it('should handle the service worker empty video ID scenario from the bug report', () => {
      // This simulates the exact scenario described in the requirements:
      // "Service worker sometimes tells content script to block empty video ID"

      // Simulate what might come from the service worker
      const problematicBlockedIds = ['dQw4w9WgXcQ', '', 'jNQXAC9IVRw', '   ', 'v'];

      // Filter using our validator - isValidVideoId handles all problematic cases
      const safeIds = YouTubeVideoIdValidator.filterValidVideoIds(problematicBlockedIds, true);

      // Should remove empty strings, whitespace-only strings, and invalid short strings
      // 'v' is filtered out because it doesn't match the 11-character YouTube ID format
      expect(safeIds).toEqual(['dQw4w9WgXcQ', 'jNQXAC9IVRw']);

      // Verify that these IDs won't cause false positives in substring matching
      const testUrls = [
        'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        'https://www.youtube.com/watch?v=BaW_jenozKc',
        'https://www.youtube.com/feed/subscriptions',
        'https://www.youtube.com/watch',
      ];

      testUrls.forEach((url) => {
        const matches = safeIds.filter((id) => url.includes(id));
        // Only the first URL should match (contains dQw4w9WgXcQ)
        if (url.includes('dQw4w9WgXcQ')) {
          expect(matches).toEqual(['dQw4w9WgXcQ']);
        } else {
          expect(matches).toEqual([]);
        }
      });
    });
  });
});

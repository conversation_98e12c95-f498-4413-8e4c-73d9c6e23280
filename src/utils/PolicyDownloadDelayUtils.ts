/**
 * Utility functions for managing policy download delays to reduce traffic spikes.
 */

import ManagedPolicy from '../models/ManagedPolicy';
import TimeoutPromise from '../utilities/TimeoutPromise';

/**
 * Generates a random delay between 0 and the specified maximum.
 * @param maxDelaySeconds The maximum delay in seconds.
 * @returns A random delay in milliseconds between 0 and maxDelaySeconds * 1000.
 */
export const generateRandomDelay = (maxDelaySeconds: number): number => {
  if (maxDelaySeconds <= 0) {
    return 0;
  }
  return Math.floor(Math.random() * maxDelaySeconds * 1000);
};

/**
 * Creates a cancellable delay using the existing TimeoutPromise utility.
 * @param delayMs The delay in milliseconds.
 * @returns A TimeoutPromise instance that can be cancelled.
 */
export const createCancellableDelay = (delayMs: number): TimeoutPromise => {
  return new TimeoutPromise(delayMs);
};

/**
 * Reads the MaxPolicyDownloadDelay setting from managed storage.
 * @param managedPolicy The managed policy object containing configuration.
 * @returns The maximum delay in seconds, or 15 (default) if not specified.
 */
export const getMaxPolicyDownloadDelay = (managedPolicy: ManagedPolicy): number => {
  const delay = managedPolicy?.MaxPolicyDownloadDelay;

  if (typeof delay === 'number' && delay >= 0) {
    return delay;
  }

  // Default to 15 seconds if not specified or invalid
  return 15;
};

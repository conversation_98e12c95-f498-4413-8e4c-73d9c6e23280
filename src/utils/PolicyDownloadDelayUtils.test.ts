import {
  createCancellableDelay,
  generateRandomDelay,
  getMaxPolicyDownloadDelay,
} from './PolicyDownloadDelayUtils';

describe('PolicyDownloadDelayUtils', () => {
  describe('generateRandomDelay', () => {
    it('should return 0 for maxDelaySeconds <= 0', () => {
      expect(generateRandomDelay(0)).toBe(0);
      expect(generateRandomDelay(-5)).toBe(0);
    });

    it('should return a value between 0 and maxDelaySeconds * 1000', () => {
      const maxDelaySeconds = 10;
      const maxDelayMs = maxDelaySeconds * 1000;

      // Test multiple times to ensure randomness
      for (let i = 0; i < 100; i++) {
        const delay = generateRandomDelay(maxDelaySeconds);
        expect(delay).toBeGreaterThanOrEqual(0);
        expect(delay).toBeLessThanOrEqual(maxDelayMs);
      }
    });

    it('should return different values on multiple calls', () => {
      const delays = new Set();
      const maxDelaySeconds = 60;

      // Generate multiple delays and check they're not all the same
      for (let i = 0; i < 50; i++) {
        delays.add(generateRandomDelay(maxDelaySeconds));
      }

      // Should have at least some variation (very unlikely to get all same values)
      expect(delays.size).toBeGreaterThan(1);
    });
  });

  describe('createCancellableDelay', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should return a TimeoutPromise instance', () => {
      const delay = createCancellableDelay(1000);
      expect(delay).toHaveProperty('promise');
      expect(delay).toHaveProperty('cancel');
      expect(typeof delay.cancel).toBe('function');
    });

    it('should resolve to true for delay <= 0', async () => {
      const delay = createCancellableDelay(0);

      // Fast-forward timers to ensure setTimeout(0) resolves
      jest.advanceTimersByTime(0);
      await jest.runOnlyPendingTimersAsync();

      const result = await delay.promise;
      expect(result).toBe(true);

      const delay2 = createCancellableDelay(-100);

      // Fast-forward timers for negative delay too
      jest.advanceTimersByTime(0);
      await jest.runOnlyPendingTimersAsync();

      const result2 = await delay2.promise;
      expect(result2).toBe(true);
    });

    it('should resolve to true after the specified delay', async () => {
      const delayMs = 5000;
      const delay = createCancellableDelay(delayMs);

      let resolved = false;
      void delay.promise.then((result) => {
        resolved = result;
      });

      // Should not be resolved yet
      expect(resolved).toBe(false);

      // Fast-forward time
      jest.advanceTimersByTime(delayMs);
      await Promise.resolve(); // Allow promise to resolve

      expect(resolved).toBe(true);
    });

    it('should resolve to false when cancelled before timeout', async () => {
      const delayMs = 5000;
      const delay = createCancellableDelay(delayMs);

      // Cancel before timeout
      delay.cancel();

      // Promise should resolve to false
      const result = await delay.promise;
      expect(result).toBe(false);
    });

    it('should handle multiple cancel calls gracefully', async () => {
      const delayMs = 5000;
      const delay = createCancellableDelay(delayMs);

      delay.cancel();
      delay.cancel(); // Second cancel should not throw

      const result = await delay.promise;
      expect(result).toBe(false);
    });
  });

  describe('getMaxPolicyDownloadDelay', () => {
    it('should return the specified delay when valid', () => {
      expect(getMaxPolicyDownloadDelay({ MaxPolicyDownloadDelay: 30 })).toBe(30);
      expect(getMaxPolicyDownloadDelay({ MaxPolicyDownloadDelay: 0 })).toBe(0);
      expect(getMaxPolicyDownloadDelay({ MaxPolicyDownloadDelay: 120 })).toBe(120);
    });

    it('should return default (15) when not specified', () => {
      expect(getMaxPolicyDownloadDelay({})).toBe(15);
      expect(getMaxPolicyDownloadDelay(undefined as any)).toBe(15);
      expect(getMaxPolicyDownloadDelay(null as any)).toBe(15);
    });

    it('should return default (15) when invalid values', () => {
      expect(getMaxPolicyDownloadDelay({ MaxPolicyDownloadDelay: -5 })).toBe(15);
      expect(getMaxPolicyDownloadDelay({ MaxPolicyDownloadDelay: 'invalid' as any })).toBe(15);
      expect(getMaxPolicyDownloadDelay({ MaxPolicyDownloadDelay: null as any })).toBe(15);
      expect(getMaxPolicyDownloadDelay({ MaxPolicyDownloadDelay: undefined })).toBe(15);
    });

    it('should handle other properties in managed policy', () => {
      const managedPolicy = {
        MaxPolicyDownloadDelay: 45,
        Smoothwall: { Serial: 'test' },
        DocumentHiderExclusions: 'example.com',
      };

      expect(getMaxPolicyDownloadDelay(managedPolicy)).toBe(45);
    });
  });
});

{"name": "Smoothwall Cloud Filter", "version": "", "manifest_version": 3, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}, "permissions": ["alarms", "webRequest", "webRequestBlocking", "tabs", "identity", "identity.email", "storage", "unlimitedStorage", "nativeMessaging", "system.memory", "scripting", "enterprise.deviceAttributes", "gcm", "offscreen", "management"], "host_permissions": ["*://*/*"], "action": {"default_icon": {"16": "images/smoothwall-icon-16x16.png", "128": "images/smoothwall-icon-128x128.png"}, "default_title": "Smoothwall Cloud Filter"}, "background": {"service_worker": "main.js"}, "omnibox": {"keyword": "smoothwall://"}, "icons": {"16": "images/smoothwall-icon-16x16.png", "128": "images/smoothwall-icon-128x128.png"}, "content_scripts": [{"matches": ["*://*/*"], "js": ["./content-script.js"], "run_at": "document_start", "all_frames": true}], "storage": {"managed_schema": "managed-storage-schema.json"}, "minimum_chrome_version": "110"}
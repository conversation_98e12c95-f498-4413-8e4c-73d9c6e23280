import AccessLogManager from 'access-logs/AccessLogManager';
import AccessLogStorage from 'access-logs/AccessLogStorage';
import FilterMode from 'constants/FilterMode';
import AcknowledgementMessage from 'content-script-messages/AcknowledgementMessage';
import AllowContentMessage from 'content-script-messages/AllowContentMessage';
import ContentScriptMessage from 'content-script-messages/ContentScriptMessage';
import EnableYoutubeHandlingMessage from 'content-script-messages/EnableYoutubeHandlingMessage';
import PageContentMessage from 'content-script-messages/PageContentMessage';
import RequestPageContentMessage from 'content-script-messages/RequestPageContentMessage';
import YoutubeBlockedIdsMessage from 'content-script-messages/YoutubeBlockedIdsMessage';
import YoutubePageHrefsMessage from 'content-script-messages/YoutubePageHrefsMessage';
import FilterDecision from 'guardian/models/FilterDecision';
import safeguardingRules from 'mini-blocklist-data/safeguardingRules';
import AccessLogEntry from 'models/AccessLogEntry';
import BlockPageDetails from 'models/BlockPageDetails';
import IExtensionConfig from 'models/IExtensionConfig';
import LicenseResult from 'models/LicenseResult';
import MetricTelemetry from 'models/MetricTelemetry';
import { TelemetryEventType } from '../constants/TelemetryEventType';
import {
  ICustomCategory,
  IFilterPolicyFlat,
  IGroup,
  IPolicyConfig,
} from 'models/PolicyConfigModels';
import UserDocument from 'models/UserDocument';
import ProvisioningInfo from 'provisioning/ProvisioningInfo';
import AlarmService from 'services/AlarmService';
import FilterService from 'services/FilterService';
import FirestoreDocumentListener from 'services/FirestoreDocumentListener';
import ITelemetryService from 'services/ITelemetryService';
import LocalLogViewerService from 'services/LocalLogViewerService';
import RemoteLogViewerService from 'services/RemoteLogViewerService';
import SafeguardingService from 'services/SafeguardingService';
import StorageService from 'services/StorageService';
import YoutubeHandlingService from 'services/YoutubeHandlingService';

import BlockContentMessage from '../content-script-messages/BlockContentMessage';
import PolicyMatcher from '../guardian/PolicyMatcher';
import IpService from '../services/IpService';

import { epochToDate, isManifestV2 } from '../utilities/Helpers';
import { isPrefetchRequest, isPrerenderedRequest } from '../utilities/PrerenderUtilities';
import SimpleRateLimiter from '../utilities/SimpleRateLimiter';
import YouTubeVideoIdValidator from '../utils/youtube/YouTubeVideoIdValidator';

/**
 * Orchestrates filtering operations at a high level.
 * This handles request and navigation events, and applies filtering rules to them.
 */
export default class FilterController {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance of the controller.
   *
   * @param extensionConfig Contains hard-coded configuration information for the extension.
   * @param telemetryService Sends log messages and other events to the cloud.
   * @param alarmService Used for creating and listening to alarms.
   * @param ipService The ip service to use when getting the device ip addresses.
   * @param safeguardingContextStorageService Responsible persisting safeguarding alert information
   *  between runs.
   * @param userDocumentListener Subscribes to updates from a user-specific document in Firestore.
   *  This is used for RTLV.
   */
  constructor(
    extensionConfig: IExtensionConfig,
    telemetryService: ITelemetryService,
    alarmService: AlarmService,
    ipService: IpService,
    safeguardingContextStorageService: StorageService,
    userDocumentListener: FirestoreDocumentListener<UserDocument>,
    startupDurationStart: number,
  ) {
    this._telemetryService = telemetryService;
    this._startUpDurationStart = startupDurationStart;
    this.filterService = new FilterService(ipService);
    this.accessLogManager = new AccessLogManager(
      extensionConfig,
      telemetryService,
      new AccessLogStorage(),
      new SimpleRateLimiter(
        this.ACCESS_LOG_RATE_LIMIT_MAX_REQUESTS,
        this.ACCESS_LOG_RATE_LIMIT_WINDOW_MS,
      ),
    );
    this._ipService = ipService;
    this._safeguardingService = new SafeguardingService(
      safeguardingContextStorageService,
      alarmService,
      telemetryService,
      this.accessLogManager,
    );
    this._safeguardingService.loadRules(safeguardingRules);
    this.remoteLogViewerService = new RemoteLogViewerService(
      userDocumentListener,
      telemetryService,
    );

    // Start the timers which persist and upload access logs. Nothing will actually be uploaded
    //  until / unless we have valid customer data.
    this.accessLogManager.start().catch(console.warn);

    // Scan any tabs that could have been opened while the extension was initialising.
    this._scanExistingTabs(true).catch(console.warn);

    setInterval(this._processMetrics, this._metricInterval);
  }

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Store the specified provisioning information.
   * This is used for information purposes when displaying the block-page.
   *
   * @param provisioningInfo The provisioning information to store.
   */
  public readonly loadProvisioningInfo = (provisioningInfo: ProvisioningInfo): void => {
    this._provisioningInfo = provisioningInfo;
    this.filterService.fullFilter.provisioningInfo = provisioningInfo;
    this._safeguardingService.setProvisioningInfo(
      provisioningInfo.serialId,
      provisioningInfo.tenantId,
    );
  };

  /**
   * Set the information provided by the customer data controller.
   *
   * @param policyConfig Holds all of the information parsed out from the policy config file.
   * @param licensingInfo Information about the current license status.
   * @param useAdvancedBlockPage Toggle for whether to provide details to the block page.
   * @param mappedGroups Array of groups that have been mapped from directory groups.
   * @param safeguardingAlertResource URI for the safeguarding POST.
   * @param safeguardingAlertSecurityToken Bearer token for safeguarding headers.
   * @param safeguardingGlobalToggle Boolean indicating whether safeguarding alerts are enabled.
   */
  public readonly setCustomerData = async (
    policyConfig: IPolicyConfig,
    licensingInfo: LicenseResult,
    useAdvancedBlockPage: boolean,
    mappedGroups: IGroup[],
    safeguardingAlertResource: string | undefined,
    safeguardingAlertSecurityToken: string | undefined,
    safeguardingGlobalToggle: boolean,
  ): Promise<void> => {
    this._safeguardingService.setClientSettings(
      safeguardingAlertResource,
      safeguardingAlertSecurityToken,
      safeguardingGlobalToggle,
    );

    this.filterService.setCustomerData(policyConfig);
    this._useAdvancedBlockPage = useAdvancedBlockPage;
    this._mappedGroups = mappedGroups;

    this._shouldHandleYoutube = this._determineYouTubeHandling(
      policyConfig.flattenedPolicies,
      policyConfig.custom_categories,
    );

    if (this._shouldHandleYoutube) {
      console.debug('[YOUTUBE HANDLING] Resetting Youtube handling due to customer data change');
      this._youtubeHandlingService.removeAllTabsFromLookupTable(); // Clear this array as we need to re-check all Youtube tabs
    }

    // We must have provisioning info at this stage.
    if (this._provisioningInfo !== undefined) {
      await this.accessLogManager.configure(
        this._provisioningInfo?.hardwareId,
        this._provisioningInfo?.tenantId,
        licensingInfo,
      );
    }

    if (this.filterService.filterMode === FilterMode.full) {
      this._scanExistingTabsWithDebounce();
    }
  };

  /**
   * Register listeners for browser events.
   * This must be called during the first event loop iteration.
   */
  public readonly addListeners = (): void => {
    // Intercept requests and navigation, potentially blocking them.
    // TODO: Optionally add a non-blocking listener to facilitate testing on ChromeOS.
    const filter: chrome.webRequest.RequestFilter = {
      urls: ['http://*/*', 'https://*/*'],
    };
    chrome.webRequest.onBeforeRequest.addListener(this._onBeforeRequest, filter, ['blocking']);
    chrome.runtime.onMessage.addListener(this._onMessage);

    this.filterService.onFilterModeChanged.addListener(this._onFilterModeChanged);

    chrome.tabs.onUpdated.addListener(this._onTabUpdated);
    chrome.tabs.onRemoved.addListener(this._onTabRemoved);
  };

  /**
   * Go through all existing tabs and block the URLs/content if necessary (aka "rescan").
   * This is called whenever the filter data finishes loading or updating. This ensures the latest
   *  filter is applied to anything we might have missed while the service worker wasn't running.
   * This will also ensure that the content script is injected into any tabs which are missing it.
   * (Tabs which were open before our service worker started may not have been injected.)
   *
   * @param isOnStartUp A flag to indicate if this is being called during extension start up.
   */
  private readonly _scanExistingTabs = async (isOnStartUp: boolean = false): Promise<void> => {
    console.debug('Scanning all existing tabs');
    // Only go through tabs with an http/https URL. This omits things like built-in browser tabs.
    (await chrome.tabs.query({ url: ['http://*/*', 'https://*/*'] })).forEach((tab) => {
      // We're deliberately not awaiting each tab as it's important to run through and start them
      //  all rescanning as quickly as possible.
      this._scanTab(tab, true, isOnStartUp).catch(console.warn);
    });
  };

  /*
   * Used to trigger a debounced scan with a duration defined in the debounce duration variable.
   * The debounce is used to absorb any additional scans that may be triggered during the filter mode
   * change or customer data update. By absorbing the scans and not immediately trigger them the
   * debounce will increase peformance by not performing continuous scans that will end up producing
   * the same end result
   */
  private readonly _scanExistingTabsWithDebounce = (): void => {
    if (this._scanDebounceTimer !== null) {
      console.debug('Scan debounce already set, ignoring additional request to scan existing tabs');
      return;
    }

    this._scanDebounceTimer = setTimeout(() => {
      this._scanDebounceTimer = null;
      this._scanExistingTabs().catch(console.warn);
    }, this._debounceDuration);

    console.debug('Scan debounce set');
  };

  /**
   * Retroactively check that the given tab's URL is allowed, and block it if not.
   * The analysis happens synchronously, and the tab is immediately redirected to the block page if
   *  necessary.
   *
   * @param tab Contains details about the tab to check. The id and url properties must be set.
   * @param applyUrlMods A flag to indicate if url mods should be applied.
   * @returns True if scanning can proceed to content analysis, or false if not. Content analysis
   *  can only be performed if the tab is scannable, it doesn't bypass filtering, and it isn't
   *  blocked by URL analysis.
   */
  private readonly _scanTabUrl = (tab: chrome.tabs.Tab, applyUrlMods: boolean): boolean => {
    const tabId = tab.id;
    if (tabId === undefined || tab.url === undefined) {
      // We can't do content analysis on an unscannable tab.
      return false;
    }

    // Ignore requests to the file system.
    if (tab.url.startsWith('filesystem:')) {
      return false;
    }

    console.debug(`Re-scanning URL for tab ${tabId}`);

    // Ensure the URL is allowed.
    if (!this._handleExistingUrl(new URL(tab.url), tabId, 0, applyUrlMods)) {
      // The URL was blocked so there's no point analysing the content.
      return false;
    }

    return true;
  };

  /**
   * Retroactively check that the given tab's content is allowed, and block it if not.
   * This requests the content from the tab and all its frames. The content analysis happens
   *  asynchronously when the content is received.
   *
   * @param tab Contains details about the tab to check. The id property must be set.
   * @param injectContentScript A flag to indicate if the content script should be injected into the
   *  tab if necessary. This should only be done if we're scanning during first startup.
   * @returns A promise which resolves when the request for content has been sent to the tab, or
   *  when the content script has been injected (if necessary). Content analysis will probably not
   *  have been done by the time this resolves.
   */
  private readonly _scanTabContent = async (
    tab: chrome.tabs.Tab,
    injectContentScript: boolean,
  ): Promise<void> => {
    if (tab.id === undefined) {
      // We can't do anything without a tab ID.
      return;
    }

    // Ask all frames in the tab to send their URL and content back to us for analysis. We're
    //  sending this via standalone message because this is a handy way to check whether the content
    //  script is running in the tab.
    // Note: The content script won't send the data directly back as a reply to this message. It
    //  will send a separate standalone message back to us when it's ready.
    try {
      await chrome.tabs.sendMessage(tab.id, new RequestPageContentMessage());
    } catch (e: any) {
      // We failed to send the message. That means there probably isn't a content script in the
      //  tab, so we need to inject one. It will automatically send its content when ready.
      // Only do this if the extension is initialising, otherwise we run the risk of injecting the
      //  content script twice.
      if (injectContentScript) {
        await this._injectContentScript(tab.id);
      }
    }
  };

  /**
   * Retroactively check that the given tab's URL and content are allowed, and block them if not.
   *
   * @param tab Contains details about the tab to check. The id and url properties must be set.
   * @param applyUrlMods A flag to indicate if url mods should be applied.
   * @param injectContentScript A flag to indicate if the content script should be injected into the
   *  tab if necessary. This should only be done if we're scanning during first startup.
   */
  private readonly _scanTab = async (
    tab: chrome.tabs.Tab,
    applyUrlMods: boolean,
    injectContentScript: boolean = false,
  ): Promise<void> => {
    if (this._scanTabUrl(tab, applyUrlMods)) {
      await this._scanTabContent(tab, injectContentScript);
    }
  };

  /**
   * Try to inject our content script into the specified tab.
   *
   * @param tabId The ID of the tab to inject the content script into.
   * @returns The returned promise will resolve even if the content script could not be injected.
   *  The error will be logged to the console though.
   */
  private readonly _injectContentScript = async (tabId: number): Promise<void> => {
    try {
      if (isManifestV2) {
        await chrome.tabs.executeScript(tabId, {
          allFrames: true,
          file: 'content-script.js',
        });
      } else {
        await chrome.scripting.executeScript({
          target: {
            tabId,
            allFrames: true,
          },
          files: ['content-script.js'],
        });
      }

      console.debug(`Injected content script into tab ${tabId}`);
    } catch (e: any) {
      console.warn(`Failed to inject content script into tab ${tabId}:`, e);
    }
  };

  /**
   * Redirect a tab to point at the specified URL.
   * This is asynchronous -- the function will return before the tab has been redirected.
   *
   * @param tabId The tab whose URL will be modified.
   * @param url The URL to show in the tab.
   */
  private readonly _setTabUrl = (tabId: number, url: URL): void => {
    chrome.tabs.update(tabId, { url: url.toString() }, (): void => {
      if (chrome.runtime.lastError?.message !== undefined) {
        console.warn(
          `Failed to redirect tab ${tabId} to URL "${url.toString()}. ` +
            `Error: ${chrome.runtime.lastError.message}`,
        );
      }
    });
  };

  /**
   * Redirect a tab to point at the block page (i.e. the page showing that content was blocked).
   * This is asynchronous -- the function will return before the tab has been redirected.
   *
   * @param tabId The tab to be redirected to the block page.
   * @param details Information for the advanced block page.
   * @param isIwfPage Indicates if the page blocked is an iwf page. If it is then the Iwf block page will be displayed.
   */
  private readonly _showBlockPage = (
    tabId: number,
    details: BlockPageDetails,
    isIwfPage: boolean,
  ): void => {
    if (isIwfPage) {
      this._setTabUrl(tabId, FilterController.getBlockPageUrl(undefined, true));
      return;
    }

    const params = new URLSearchParams({
      username: details.username,
      timestamp: details.timestamp,
      url: details.url,
      location: JSON.stringify(details.location),
      categories: JSON.stringify(details.categories),
      groups: JSON.stringify(details.groups),
    });
    this._setTabUrl(
      tabId,
      FilterController.getBlockPageUrl(this._useAdvancedBlockPage ? params : undefined),
    );
  };

  /**
   * Retroactively check and filter the URL of a tab or frame which the user has already requested.
   * This is mainly used when we rescan tabs which were already open when the extension started.
   * It's also used by the navigation handler, which is a fall-back for cases which the main
   *  web-request handler misses.
   *
   * @param url The URL which was requested.
   * @param tabId The ID of the tab which the URL occurred in.
   * @param frameId The ID of the frame which the URL occurred in. 0 means the top-level frame (i.e.
   *  the main document).
   * @param applyUrlMods A flag to indicate if url mods should be applied.
   * @returns Returns true if the URL is allowed, or false if it's blocked.
   */
  private readonly _handleExistingUrl = (
    url: URL,
    tabId: number,
    frameId: number,
    applyUrlMods: boolean,
  ): boolean => {
    const decision = this.filterService.analyse(url);

    if (decision.videoIds !== '') {
      this._youtubeHandlingService.saveKeyToLookup(
        tabId,
        frameId,
        this._youtubeHandlingService.urlKey,
        url.toString(),
      );
    }

    if (decision.log) {
      // TODO: Add the URL to the access log. Ensure we use the modified URL if specified.
      // NOTE: If this is the URL of a sub-frame then we might have already logged it during a
      //        previous rescan. We need to avoid this if possible.
    }

    if (!decision.allow) {
      // Only show the block page if we're checking the URL of the main document.
      if (frameId === 0) {
        this._showBlockPage(
          tabId,
          {
            username: this._provisioningInfo?.user ?? '',
            timestamp: new Date().toString(),
            url: url.toString(),
            location: [decision.location ?? ''],
            categories: this.filterService.getCategoryNames([...decision.categoryIds]),
            groups: this._mappedGroups.map((group) => {
              return { id: group.id, name: group.name };
            }),
          },
          decision.isIwfPage,
        );
        return false;
      }

      // TODO: Tell the content script to hide the page contents?
      //       We can't do anything else to block the sub-frame.
      return false;
    }

    if (
      decision.modifiedUrl === undefined ||
      decision.modifiedUrl.toString() === url.toString() ||
      !applyUrlMods
    ) {
      // The URL is allowed without modification or mods should not be applied. Do nothing.
      return true;
    }

    // The URL of a sub-frame can't be modified if it's already been loaded. It only be done in
    //  advance by the web-request handler, which will usually catch most cases anyway.
    if (frameId !== 0) {
      console.warn('URL modification cannot be applied retrospectively to a sub-frame');
      return true;
    }

    // If navigating to the modified URL would result in another modified URL then we may be at risk
    //  of getting stuck in a redirect loop. Ignore the URL modification in that case.
    // Note that this problem doesn't affect the webRequest handler. Redirections at that level
    //  don't result in the event handler being called again.
    const redirectedDecision = this.filterService.analyse(decision.modifiedUrl);
    if (redirectedDecision?.allow && redirectedDecision.modifiedUrl !== undefined) {
      console.warn(`URL modification would cause a potential redirection loop: ${url.toString()}`);
      return true;
    }

    console.debug(
      `Redirecting tab ${tabId} from "${url.toString()}"` +
        ` to "${decision.modifiedUrl.toString()}"`,
    );
    this._setTabUrl(tabId, decision.modifiedUrl);

    return true;
  };

  // -----------------------------------------------------------------------------------------------
  // Event handlers.

  /**
   * This is called immediately before an outgoing request is initiated by the browser.
   * This gives us a chance to check the URL and potentially block the request before the user has
   *  a chance to see anything. It's the main means by which we filter out undesirable navigation.
   *
   * @warning Currently, this event WILL NOT be triggered if our service worker is inactive when the
   *  request is initiated. This is a limitation of the manifest v3 specification. We try to
   *  mitigate it by keeping the service worker active as much as possible, and by responding to
   *  other events which might occur just before or after the request.
   *
   * @param {chrome.webRequest.OnBeforeRequestDetails} details Contains information about the request
   *  which is about to be initiated.
   * @return {chrome.webRequest.BlockingResponse} Returns an object which indicates whether the
   *  request should be cancelled (i.e. blocked).
   */
  private readonly _onBeforeRequest = (
    details: chrome.webRequest.OnBeforeRequestDetails,
  ): chrome.webRequest.BlockingResponse => {
    // Ignore any requests initiated by our extension.
    if (details.initiator?.startsWith(this._extensionUrl) === true) {
      return { cancel: false };
    }

    const startTime = performance.now();
    try {
      return this._processRequest(details);
    } finally {
      const endTime = performance.now();
      const timeTaken = endTime - startTime;

      // Don't log any metrics for requests from the extension or if we are not in full filter mode.
      if (this.filterService.filterMode === FilterMode.full && timeTaken > 1) {
        this._requestHandlerMetric.addMetric(timeTaken);
      }
    }
  };

  /**
   * Processes the details of a request and determines if it should be cancelled, i.e blocked.
   *
   * @see _onBeforeRequest for more information.
   * @param details Information about the request.
   * @returns An object that indicates if the request should be cancelled.
   */
  private readonly _processRequest = (
    details: chrome.webRequest.OnBeforeRequestDetails,
  ): chrome.webRequest.BlockingResponse => {
    const isTopLevelRequest =
      details.frameId === 0 && details.type === 'main_frame' && details.parentFrameId === -1;

    // Check if this is a prerendered request
    const isPrerendered = this._isPrerenderedRequest(details);

    // Check if this is a prefetch request (Chrome v134+ issue)
    const isPrefetch = isPrefetchRequest(details);

    // Determine if this should be treated as an effective top-level request
    // Prefetch requests should not trigger blockpages or be logged as top-level
    const isEffectiveTopLevel = isTopLevelRequest && !isPrefetch;

    let mainFrameUrl: URL | undefined;
    if (!isTopLevelRequest) {
      const mainFrameString =
        this._youtubeHandlingService.getUrlForTabFrame(details.tabId, details.frameId) ?? '';

      if (mainFrameString !== '') {
        mainFrameUrl = new URL(mainFrameString);
      }
    }

    const decision = this.filterService.analyse(details.url, mainFrameUrl);

    // For prefetch requests, disable logging to prevent misleading logs
    // The request will still be blocked if necessary, but won't appear in logs
    if (isPrefetch) {
      decision.log = false;
    }

    // Youtube uses a service worker for making some requests. Blocking those will break the page.
    // Because a service worker is not tied to a tab the look up table cannot be used.
    // The page must have been allowed for the service worker to exist so allow any youtube specific service worker requests.
    if (details.tabId === -1 && details.parentFrameId === -1) {
      if (details.initiator?.startsWith('https://www.youtube.com') === true) {
        decision.allow = true;
      }
    }

    // If the request analysis has returned video ids,
    // record them against the tab/subframe
    if (decision.videoIds !== '') {
      this._youtubeHandlingService.saveKeyToLookup(
        details.tabId,
        details.frameId,
        this._youtubeHandlingService.urlKey,
        details.url,
      );
    }

    // Only log access for non-prerendered and non-prefetch requests to avoid misleading logs
    if (!isPrerendered && !isPrefetch) {
      this._handleAccessLog(
        decision.modifiedUrl !== undefined ? decision.modifiedUrl : new URL(details.url),
        decision,
        true,
        details,
      ).catch((error) => {
        console.error('FilterController - Failed to handle access log:', error);
      });
    }

    if (decision.allow) {
      if (decision.modifiedUrl === undefined || decision.modifiedUrl.toString() === details.url) {
        // Allow the request through as-is.
        return { cancel: false };
      }

      console.debug(`Redirecting request to URL: ${decision.modifiedUrl.toString()}`);

      // Redirect the request to the modified URL.
      return { redirectUrl: decision.modifiedUrl.toString() };
    }

    // Only show the block page if the main document was being requested and it's not prerendered or prefetch.
    // Requests for sub-page elements like images and frames will simply be cancelled, allowing the
    //  remainder of the page to be visible.
    // Prerendered pages should not show block pages until the user actually navigates to them.
    // Prefetch requests should not show block pages as the user hasn't actually navigated to the page.
    if (isEffectiveTopLevel && !isPrerendered) {
      this._showBlockPage(
        details.tabId,
        {
          username: this._provisioningInfo?.user ?? '',
          timestamp: new Date().toString(),
          url: details.url,
          location: [decision.location ?? ''],
          categories: this.filterService.getCategoryNames([...decision.categoryIds]),
          groups: this._mappedGroups.map((group) => {
            return { id: group.id, name: group.name };
          }),
        },
        decision.isIwfPage,
      );
    }
    // Note: For prerendered requests, we just block them without tracking.
    // When the prerender becomes active (tab completes loading), we'll re-analyze the URL.

    return { cancel: true };
  };

  /**
   * Determines if a webRequest is for a prerendered page.
   * Prerendered pages should not trigger logging or block page display until the user actually navigates to them.
   *
   * @param details The webRequest details object
   * @returns True if the request is for a prerendered page, false otherwise
   */
  private readonly _isPrerenderedRequest = (
    details: chrome.webRequest.OnBeforeRequestDetails,
  ): boolean => {
    return isPrerenderedRequest(details);
  };

  /**
   * Called when any tab is updated (for instance, when a single page application updates the tab).
   *  Pages like YouTube perform background requests then update the page and the displayed URL.
   *  By forcing a rescan we're able to perform filtering after these psuedo-navigation events.
   *
   * @param {tabId} The ID of the updated tab.
   * @param {changeInfo} Object with optional properties indicating how the tab has changed.
   * @param {tab} The tab which has been updated.
   */
  private readonly _onTabUpdated = (
    tabId: number,
    changeInfo: chrome.tabs.TabChangeInfo,
    tab: chrome.tabs.Tab,
  ): void => {
    // Don't log metrics for tabs that are not webpages (chrome settings, local files, etc). Or if we are not in full filter mode.
    if (
      changeInfo.status === 'loading' &&
      tab.url?.startsWith('http') === true &&
      this.filterService.filterMode === FilterMode.full
    ) {
      this._tabLoadingLookup.set(tabId, performance.now());
    }

    if (changeInfo.status === 'complete') {
      const performanceInfo = this._tabLoadingLookup.get(tabId);

      if (performanceInfo !== undefined) {
        const endTime = performance.now();
        const timeTaken = endTime - performanceInfo;

        if (timeTaken > 1 && this.filterService.filterMode === FilterMode.full) {
          this._tabLoadingMetric.addMetric(timeTaken);
        }

        this._tabLoadingLookup.delete(tabId);
      }
    }

    if (changeInfo.url !== undefined) {
      // The tab URL has changed. Ensure we remove the existing entry from the tab URL lookup
      //  table so that we don't have stale data. We'll re-populate it below.
      this._youtubeHandlingService.removeTabEntryFromLookupTable(tabId);

      if (changeInfo.status === 'loading') {
        // The URL changed while the tab was still loading. Analyse the URL again to ensure the
        //  lookup table gets populated again. We don't trigger content analysis here because it
        //  would be redundant. The content script will initiate content analysis itself when it
        //  loads.
        this._scanTabUrl(tab, false);
      } else {
        // The URL has changed after the page had fully loaded. This probably means it's a Single
        //  Page Application or similar which updates its content dynamically without triggering
        //  a navigation. We need to do a full re-scan of URL and content to ensure any changes are
        //  filtering.
        this._scanTab(tab, false, false).catch(console.warn);
      }
    } else if (changeInfo.status === 'complete' && tab.url !== undefined) {
      // Handle potential prerender activation - when any tab completes loading, analyze the URL
      // to catch cases where a prerendered page becomes active (URL doesn't change but page becomes visible)
      // Only do this if we haven't already done a full scan due to URL change above
      this._scanTabUrl(tab, false);
    }
  };

  /**
   * Called when a tab is removed
   */
  private readonly _onTabRemoved = (tabId: number, removeInfo: chrome.tabs.TabRemoveInfo): void => {
    this._youtubeHandlingService.removeTabEntryFromLookupTable(tabId);
    this._tabLoadingLookup.delete(tabId);
  };

  /**
   * This is called when we receive a message from a content script.
   *
   * @param rawMessage The message which was received.
   * @param sender Contains details about which tab/frame the message came from.
   * @param sendResponse A callback which can be invoked to send a response.
   */
  private readonly _onMessage = (
    rawMessage: any,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response: any) => void,
  ): boolean => {
    const tabId = sender?.tab?.id;
    const frameId = sender?.frameId;

    if (tabId === undefined || frameId === undefined) {
      // This may be a message to/from the offscreen document. Ignore it.
      return false;
    }

    const message = ContentScriptMessage.load(rawMessage);
    if (message instanceof PageContentMessage) {
      const startTime = performance.now();
      this._onPageContentMessageReceived(message, tabId, frameId)
        .then(async (response) => {
          sendResponse(response);
        })
        .catch((error) => {
          console.error('An error occurred processing the page content message.', error);
        })
        .finally(() => {
          const endTime = performance.now();
          const timeTaken = endTime - startTime;

          // Don't log any metrics if we are not in full filter mode.
          if (this.filterService.filterMode === FilterMode.full && timeTaken > 1) {
            this._pageContentHandlerMetric.addMetric(timeTaken);
          }
        });

      if (
        this._shouldHandleYoutube &&
        !this._youtubeHandlingService.getIsHandlingYoutubeForTabFrame(tabId, frameId)
      ) {
        this._enableYouTubeHandling(tabId, frameId).catch((e) => {
          console.warn(
            'An error occurred when sending the EnableYoutubeHandlingMessage - If the tab has been closed this can be ignored.',
            e,
          );
        });
      }

      // Return true so chrome knows the response will be async.
      return true;
    }

    if (message instanceof YoutubePageHrefsMessage) {
      this._handleYoutubePageHrefsMessage(message, tabId, frameId)
        .then((res: YoutubeBlockedIdsMessage | null) => {
          if (res !== null && res.blockedIds.length > 0) {
            sendResponse(res);
          } else {
            sendResponse(new AcknowledgementMessage()); // SendResponse needs to be called even if there are no video IDs to send
          }
        })
        .catch((error) => {
          console.error(
            'An error occurred processing the YoutubePageHrefsMessage - If the tab has been closed this can be ignored.',
            error,
          );
        });
      // Return true so chrome knows the response will be async.
      return true;
    }

    sendResponse(new AcknowledgementMessage());
    return false;
  };

  private readonly _enableYouTubeHandling = async (
    tabId: number,
    frameId: number,
  ): Promise<void> => {
    if (tabId === undefined) {
      return;
    }

    const tab = await chrome.tabs.get(tabId);

    if (tab.url === undefined || tab.id === undefined) {
      return;
    }

    if (!new URL(tab.url).hostname.includes('youtube.com')) {
      return;
    }

    console.debug(
      `[YOUTUBE HANDLING] Sending enable Youtube handling message to tab ${tab.id} frame ${frameId}`,
    );
    await chrome.tabs.sendMessage(tab.id, new EnableYoutubeHandlingMessage());
    this._youtubeHandlingService.saveKeyToLookup(
      tab.id,
      frameId,
      this._youtubeHandlingService.isHandlingYoutubeKey,
      'true',
    );
  };

  private async _handleYoutubePageHrefsMessage(
    message: YoutubePageHrefsMessage,
    tabId: number,
    frameId: number,
  ): Promise<YoutubeBlockedIdsMessage | null> {
    if (!this._shouldHandleYoutube) {
      return null;
    }

    if (
      message.hrefs.length > 0 &&
      tabId !== undefined &&
      this._youtubeHandlingService.getIsHandlingYoutubeForTabFrame(tabId, frameId)
    ) {
      return this._getYoutubeBlockedIds(message);
    }

    return null;
  }

  private _getYoutubeBlockedIds(message: YoutubePageHrefsMessage): YoutubeBlockedIdsMessage | null {
    const youtubeHrefDecisions = message.hrefs.map((href) => this.filterService.analyse(href));
    const youtubeIds = youtubeHrefDecisions.filter((d) => !d.allow).map((d) => d.videoIds);

    // Because the decision returns all video ids for a url we need to figure out which ones should actually be blocked.
    const youtubeBlockedIds: string[] = [];
    youtubeIds.forEach((idString) => {
      // Skip empty strings to prevent false positives
      if (idString.trim() === '') {
        console.debug('FilterController: Skipping empty video ID string');
        return;
      }

      // Parse and validate the video IDs using the validator
      const validIds = YouTubeVideoIdValidator.parseAndValidateVideoIds(idString, true);

      if (validIds.length > 1) {
        youtubeBlockedIds.push(...this._getBlockedIds(validIds, youtubeHrefDecisions));
      } else if (validIds.length === 1) {
        youtubeBlockedIds.push(validIds[0]);
      }
    });

    // Apply final validation filtering
    const safeBlockedIds = youtubeBlockedIds.filter((id) => {
      const isValid = YouTubeVideoIdValidator.isValidVideoId(id);

      if (!isValid) {
        console.debug(`FilterController: Filtered out invalid video ID: "${id}"`);
        return false;
      }

      return true;
    });

    if (safeBlockedIds.length > 0) {
      console.debug(
        `FilterController: Sending ${safeBlockedIds.length} blocked video IDs to content script`,
      );
      return new YoutubeBlockedIdsMessage(safeBlockedIds);
    }

    return null;
  }

  /**
   * This is called when we have received page content from a content script.
   *
   * @param message The page content message which was received.
   * @param tabId The ID of the tab which sent the content.
   * @param frameId The ID of the frame which sent the content. This will be 0 if it was the main
   *  frame.
   * @return Returns the message object which should be sent back to the sender as a response.
   */
  private readonly _onPageContentMessageReceived = async (
    message: PageContentMessage,
    tabId: number,
    frameId: number,
  ): Promise<any> => {
    console.debug(`Page content received from tab ${tabId}, frame ${frameId}`);

    const decision = this.filterService.analyse(message.url, undefined, message.content);
    this._handleAccessLog(new URL(message.url), decision, false, undefined, message.title).catch(
      (error) => {
        console.warn(error);
      },
    );

    // If the analysis has returned video ids,
    // record them against the tab/subframe
    if (decision.videoIds !== '') {
      this._youtubeHandlingService.saveKeyToLookup(
        tabId,
        frameId,
        this._youtubeHandlingService.urlKey,
        message.url,
      );
    }

    if (decision.allow) {
      // Tell the content script to un-hide the page if necessary.
      return new AllowContentMessage(decision.contentModIds);
    }

    // Only show the blockpage if the content is in the main frame.
    if (frameId === 0) {
      // Telling the content script to block the page shouldn't be necessary. This is just useful in
      //  case the block page doesn't appear right away.
      this._showBlockPage(
        tabId,
        {
          username: this._provisioningInfo?.user ?? '',
          timestamp: new Date().toString(),
          url: message.url,
          location: [decision.location ?? ''],
          categories: this.filterService.getCategoryNames([...decision.categoryIds]),
          groups: this._mappedGroups.map((group) => {
            return { id: group.id, name: group.name };
          }),
        },
        decision.isIwfPage,
      );
    }
    return new BlockContentMessage();
  };

  /**
   * Triggered when the filter service changes to a different mode.
   *
   * @param newMode The new filter mode we've changed to.
   * @param oldMode The old filter mode we've changed from.
   */
  private readonly _onFilterModeChanged = async (
    newMode: FilterMode,
    oldMode: FilterMode,
  ): Promise<void> => {
    if (newMode !== oldMode) {
      const data: any = {
        from: FilterMode[oldMode],
        to: FilterMode[newMode],
      };

      if (newMode === FilterMode.full && this.filterService.fullBlocklist.epoch !== undefined) {
        data.epoch = this.filterService.fullBlocklist.epoch;
        data.epochDate = epochToDate(this.filterService.fullBlocklist.epoch).toISOString();
      }

      this._telemetryService.logEvent(TelemetryEventType.FilterModeChanged, data);
    }

    // If the full filter has just been loaded or updated, or we've just reached mini-filter mode
    //  after starting for the first time, then go back to ensure all existing tabs are still
    //  allowed.
    // Note: We deliberately don't do this if we move back from full filter to mini-filter because
    //  that might only be a temporary change, and we don't want inconsistent filtering.
    if (newMode === FilterMode.full) {
      if (!this._hasSentStartupTelemetry) {
        this._sendStartupMetricTelemetry();
      }
      this._scanExistingTabsWithDebounce();

      // TODO: ensure any tabs which are stuck in hidden mode get re-scanned too?
      return;
    }

    if (newMode === FilterMode.disabled) {
      // TODO: Might be better to handle this from an "onUnlicensed" event?
      // TODO: clear the URL queue (might need to be done inside FilterService?)
      // TODO: tell all content scripts to un-hide content
      // TODO: stop injecting content scripts?
    }
  };

  // -----------------------------------------------------------------------------------------------
  // Helpers.

  /**
   * Get the URL of the block page.
   */
  public static readonly getBlockPageUrl = (
    params?: URLSearchParams,
    isIwfPage: boolean = false,
  ): URL => {
    if (isIwfPage) {
      return new URL(chrome.runtime.getURL('views/iwf-block-page.html'));
    }

    let details: string = '';
    if (params !== undefined) details = `?data=${encodeURI(btoa(params.toString()))}`;
    return new URL(chrome.runtime.getURL('views/block-page.html' + details));
  };

  /**
   * Determines if a decision should be logged. If so a new access log will be created and also send for safeguarding and RTLV.
   * @param url The url that was analysed.
   * @param decision The decision that came from the analysis.
   * @param urlAnalysed Indicates if the url was analysed or if the decision came from content analysis.
   * @param requestDetails The details of the web request if it was url analysis.
   * @param messageTitle The title that came from content analysis.
   */
  private readonly _handleAccessLog = async (
    url: URL,
    decision: FilterDecision,
    urlAnalysed: boolean,
    requestDetails: chrome.webRequest.OnBeforeRequestDetails | undefined = undefined,
    messageTitle: string | undefined = undefined,
  ): Promise<void> => {
    if (decision.log) {
      let log: AccessLogEntry = this._createNewAccessLog(
        url,
        decision,
        requestDetails,
        messageTitle,
      );

      try {
        log = await this._safeguardingService.handleSafeguarding(log);
      } catch (error) {
        console.error('FilterController - failed to handle safeguarding for access log:', error);
      }
      this.accessLogManager.storeAccessLog(log, urlAnalysed);
      this.remoteLogViewerService.onLog(log);
      this.localLogViewerService.streamLog(log);
    }
  };

  /**
   * Returns an access log entry.
   *
   * @param url Possibly modified request URL.
   * @param decision FilterService analyse() output.
   * @param details OnBeforeRequestDetails or undefined if log is generate by content analysis.
   */
  private readonly _createNewAccessLog = (
    url: URL,
    decision: FilterDecision,
    details?: chrome.webRequest.OnBeforeRequestDetails,
    title?: string,
  ): AccessLogEntry => {
    const log: AccessLogEntry = {
      clientip: this._ipService.publicIpAddresses.join(','),
      tenant: this._provisioningInfo?.tenantId?.toString(),
      httpcode: 200,
      took: Math.floor(performance.now()),
      time: Math.floor(Date.now() / 1000).toString(),
      groups: this._mappedGroups.map((group) => group.name),
      username: this._provisioningInfo?.user,
      clienthostname: this._provisioningInfo?.domain,
      https: url.protocol === 'https:',
      method: details?.method ?? 'GET',
      producerid: this._provisioningInfo?.domain, // In MV2 this is the same as clienthostname
      blocked: !decision.allow,
      categories: this.filterService.getCategoryNames([...decision.categoryIds]),
      destdomain: url.hostname,
      url: url.href,
      userAgent: navigator.userAgent.toString(),
      locations: decision.location !== undefined ? [decision.location] : [],
      timeslots: decision.timeslot !== undefined ? [decision.timeslot] : [],
      actions: [decision.allow ? 'allow' : 'block'],
      searchterms: decision.searchTerms,
      safeguardinglevel: 0, // Set by SafeguardingService
      loglevel: 3,
      safeguardingtheme: '', // Set by SafeguardingService
      title: title ?? '',
      contenttype: details?.type ?? 'main_frame',
      videoids: decision.videoIds,
      v: '3',
      ruleId: decision.ruleId,
      policy: decision.policy,
    };

    log.loglevel = this.filterService.fullFilter.blocklist.logLevelService.applyLogLevel(log);

    return log;
  };

  /**
   * Determines whether or not the content script is to provide additional Youtube handling to facilitate video ID processing.
   *
   * The return value is determined by checking if any enabled policies containing video IDs are applicable to the current
   * user's groups, location, and timeslot.
   *
   * @param flattenedPolicies The flattened policies from the policy config
   * @param customCategories The custom categories from the policy config
   */
  private _determineYouTubeHandling(
    flattenedPolicies: IFilterPolicyFlat[],
    customCategories: ICustomCategory[],
  ): boolean {
    // Get all enabled policies that contain video IDs
    const videoIdPolicies = this._getVideoIdPolicies(flattenedPolicies, customCategories);

    if (videoIdPolicies.length === 0) {
      console.debug('No video ID policies found - YouTube handling will be disabled');
      return false;
    }

    // Check if any of the video ID policies are applicable to the current context
    const hasApplicablePolicy = this._evaluateYouTubePolicyApplicability(videoIdPolicies);

    console.debug(
      `YouTube handling will be ${hasApplicablePolicy ? 'enabled' : 'disabled'} - ` +
        `Found ${videoIdPolicies.length} video ID policies, ${
          hasApplicablePolicy ? 'at least one is' : 'none are'
        } applicable to current user/context`,
    );

    return hasApplicablePolicy;
  }

  /**
   * Gets all enabled policies that contain video IDs in their custom categories.
   *
   * @param flattenedPolicies The flattened policies from the policy config
   * @param customCategories The custom categories from the policy config
   * @returns Array of policies that are enabled and contain video IDs
   */
  private _getVideoIdPolicies(
    flattenedPolicies: IFilterPolicyFlat[],
    customCategories: ICustomCategory[],
  ): IFilterPolicyFlat[] {
    return flattenedPolicies.filter((policy) => {
      // Only consider enabled policies (enabled is a string: 'on' or 'off')
      if (policy.enabled !== 'on') {
        return false;
      }

      // Check if any of the policy's 'what' categories contain video IDs
      return policy.what.some((categoryId) => {
        const category = customCategories.find((cat) => cat.id === categoryId);
        return (
          category?.component?.videoids !== undefined &&
          category.component.videoids !== null &&
          category.component.videoids.length > 0
        );
      });
    });
  }

  /**
   * Evaluates whether any of the given video ID policies are applicable to the current user context.
   * This checks user groups, location, and timeslot constraints.
   *
   * @param videoIdPolicies Array of policies containing video IDs
   * @returns True if at least one policy is applicable to the current context
   */
  private _evaluateYouTubePolicyApplicability(videoIdPolicies: IFilterPolicyFlat[]): boolean {
    const policyConfig = this.filterService.fullFilter.policyConfig;
    if (policyConfig == null) {
      console.debug('No policy config available for YouTube policy evaluation');
      return false;
    }

    // Create a PolicyMatcher instance to evaluate each policy's applicability
    const policyMatcher = new PolicyMatcher(this._ipService);

    // Set the current username if we have provisioning info
    if (this._provisioningInfo?.user !== undefined && this._provisioningInfo.user !== '') {
      policyMatcher.currentUsername = this._provisioningInfo.user.toLowerCase();
    }

    const currentTime = this._getCurrentTimeInterval();

    return videoIdPolicies.some((policy) => {
      // Check user group (who) constraint
      const groupMatch = policyMatcher.whoMatches(policy.who, policyConfig);
      if (groupMatch === undefined) {
        return false;
      }

      // Check location (where) constraint
      const locationMatch = policyMatcher.whereMatches(policy.where, policyConfig);
      if (locationMatch === undefined) {
        return false;
      }

      // Check timeslot (when) constraint
      const timeslotMatch = policyMatcher.whenMatches(policy.when, policyConfig, currentTime);
      if (timeslotMatch === undefined) {
        return false;
      }

      console.debug(
        `YouTube policy ${policy.id} is applicable: group=${groupMatch}, location=${locationMatch}, timeslot=${timeslotMatch}`,
      );
      return true;
    });
  }

  /**
   * Gets the current time interval for timeslot matching.
   * This is the same calculation used by PolicyMatcher.
   *
   * @returns The time interval in seconds since the previous Sunday
   */
  private _getCurrentTimeInterval(): number {
    const now = new Date();
    const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const secondsInDay = 24 * 60 * 60;
    const secondsInHour = 60 * 60;
    const secondsInMinute = 60;

    // Calculate seconds since the start of the current week (Sunday 00:00:00)
    const secondsSinceSunday =
      dayOfWeek * secondsInDay +
      now.getHours() * secondsInHour +
      now.getMinutes() * secondsInMinute +
      now.getSeconds();

    return secondsSinceSunday;
  }

  /**
   * Checks if any of the given video ids are used in a block policy for the decisions.
   *
   * @param ids An array of video id strings to be checked.
   * @param decisions An array of decisions that will be looped through to find any blocked categories for the video id.
   * @returns An array of video ids that have been blocked based on the category.
   */
  private readonly _getBlockedIds = (ids: string[], decisions: FilterDecision[]): string[] => {
    // First validate and filter the input IDs
    const validIds = YouTubeVideoIdValidator.filterValidVideoIds(ids, true);

    const blockedIds: string[] = [];
    validIds.forEach((id) => {
      // Get the categories that contain the video id and match those to any blocking policies.
      const categories =
        this.filterService.fullFilter.policyConfig?.custom_categories.filter(
          (c) => c.component?.videoids !== undefined && c.component.videoids.includes(id),
        ) ?? [];

      if (categories.length > -1) {
        categories.forEach((c) => {
          if (c.category_id === undefined) {
            return;
          }

          // The undefined check is done above but the linter fails to pick that up.
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          if (decisions.filter((d) => d.categoryIds.has(c.category_id!) && !d.allow).length > -1) {
            blockedIds.push(id);
          }
        });
      }
    });
    return blockedIds;
  };

  /**
   * Sends the current state of the metrics to the telemetry service and clears the values for reuse.
   */
  private readonly _processMetrics = (): void => {
    this._telemetryService.logMetric(this._requestHandlerMetric);
    this._telemetryService.logMetric(this._pageContentHandlerMetric);
    this._telemetryService.logMetric(this._tabLoadingMetric);
    this._requestHandlerMetric.clearValues();
    this._pageContentHandlerMetric.clearValues();
    this._tabLoadingMetric.clearValues();
  };

  /*
   * Calculates the startup duration (until blocklist is loaded and full filter) and sends to AppInsights via the TelemetryService
   */
  private readonly _sendStartupMetricTelemetry = (): void => {
    const startupDurationMetric: MetricTelemetry = new MetricTelemetry('start-up-duration');
    startupDurationMetric.addMetric(performance.now() - this._startUpDurationStart);
    startupDurationMetric.addCustomProperty(
      'blocklistSource',
      this.filterService.fullFilter.blocklist.blocklistSource,
    );
    this._telemetryService.logMetric(startupDurationMetric);
    console.debug('Extension startup metric: ', startupDurationMetric);
    this._hasSentStartupTelemetry = true;
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The maximum number of access logs that can be sent within the rate limit window.
   */
  private readonly ACCESS_LOG_RATE_LIMIT_MAX_REQUESTS = 30;

  /**
   * The time window in which the maximum number of access logs can be sent.
   */
  private readonly ACCESS_LOG_RATE_LIMIT_WINDOW_MS = 60000; // 1 min

  /**
   * Stores a copy of the extension URL without any trailing slashes.
   * For example, "chrome-extension://xyzzy".
   */
  private readonly _extensionUrl: string = (chrome.runtime.getURL('') ?? '').replace(/\/+$/, '');

  /**
   * Manages sending log messages to the cloud (e.g. App Insights).
   */
  private readonly _telemetryService: ITelemetryService;

  /**
   * Wraps all of our URL/content filtering functionality.
   */
  public readonly filterService: FilterService;

  /**
   * Handles the logic for the remote real time log viewer streaming.
   */
  public readonly remoteLogViewerService: RemoteLogViewerService;

  /**
   * Handles the logic for the local real time log viewer streaming.
   */
  public readonly localLogViewerService = new LocalLogViewerService();

  /**
   * Stores the customer and user data.
   * This isn't populated immediately on startup so it may not always be available.
   */
  private _provisioningInfo: ProvisioningInfo | undefined;

  /**
   * Manages the storage and upload of access logs.
   */
  public readonly accessLogManager: AccessLogManager;

  /**
   * Provides safeguarding alerts based on access log entries.
   */
  private readonly _safeguardingService: SafeguardingService;

  /**
   * Manages getting and caching the ip addresses needed for policy matching.
   */
  private readonly _ipService: IpService;

  /**
   * The interval in which metrics should be uploaded.
   */
  private readonly _metricInterval: number = 900000; // 15 mins

  /**
   * Keeps a metric for the performance of the onBeforeRequest handler.
   */
  private readonly _requestHandlerMetric = new MetricTelemetry('request-handler-duration');

  /**
   * Keeps a metric for the performance of the onMessage handler.
   */
  private readonly _pageContentHandlerMetric = new MetricTelemetry('content-handler-duration');

  /**
   * Keeps a metric for the loading times of tabs.
   */
  private readonly _tabLoadingMetric = new MetricTelemetry('tab-loading-duration');

  /**
   * A key value pair of the tab id and loading start time for the metrics.
   */
  private readonly _tabLoadingLookup = new Map<number, number>();

  /**
   * Toggle for whether to provide details to the block page.
   */
  private _useAdvancedBlockPage: boolean = false;

  /*
   * Toggle for whether the extension should turn on Youtube handling for content scripts.
   */
  private _shouldHandleYoutube: boolean = false;

  /**
   * Mapped groups to display on advanced block page.
   */
  private _mappedGroups: IGroup[] = [];

  /**
   * Service for managing context information for the Video ID feature
   */
  private readonly _youtubeHandlingService: YoutubeHandlingService = new YoutubeHandlingService();

  /*
   * Variable to hold the instance/identifier of the debounce timer, used when checking if the timeout has already been set
   */
  private _scanDebounceTimer: ReturnType<typeof setTimeout> | null = null;

  /*
   * The total duration that should be waited after a non-startup scan is triggered before triggering the actual scan
   */
  private readonly _debounceDuration: number = 5000;

  /*
   * Startup telemetry should only be sent once per extension init
   */
  private _hasSentStartupTelemetry: boolean = false;

  /*
   * Variable to store the starting value of the performance metrics
   */
  private readonly _startUpDurationStart: number = -1;
}

import { isPrefetchRequest } from '../utilities/PrerenderUtilities';

// Test the prefetch detection logic from PrerenderUtilities
describe('FilterController - Prefetch Detection Logic', () => {
  describe('prefetch request detection using utility function', () => {
    it('should detect prefetch requests with documentId', () => {
      const details = {
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
        documentId: 'some-document-id',
        initiator: 'https://google.com',
      } as any;

      expect(isPrefetchRequest(details)).toBe(true);
    });

    it('should not detect genuine navigation as prefetch', () => {
      const details = {
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
        initiator: 'https://google.com',
      } as any;

      expect(isPrefetchRequest(details)).toBe(false);
    });

    it('should not detect non-main_frame requests as prefetch even with documentId', () => {
      const details = {
        url: 'https://example.com',
        method: 'GET',
        frameId: 1,
        parentFrameId: 0,
        tabId: 1,
        type: 'sub_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
        documentId: 'some-document-id',
        initiator: 'https://google.com',
      } as any;

      expect(isPrefetchRequest(details)).toBe(false);
    });

    it('should not detect requests with null documentId as prefetch', () => {
      const details = {
        url: 'https://example.com',
        method: 'GET',
        frameId: 0,
        parentFrameId: -1,
        tabId: 1,
        type: 'main_frame',
        timeStamp: Date.now(),
        requestId: '123',
        requestBody: null,
        documentId: null,
        initiator: 'https://google.com',
      } as any;

      expect(isPrefetchRequest(details)).toBe(false);
    });
  });
});

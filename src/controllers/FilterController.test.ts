/* eslint-disable */
import FilterMode from 'constants/FilterMode';
import IExtensionConfig from 'models/IExtensionConfig';
import AlarmService from 'services/AlarmService';
import FirestoreDocumentListener from 'services/FirestoreDocumentListener';
import IpService from 'services/IpService';
import StorageService from 'services/StorageService';
import YoutubeHandlingService from 'services/YoutubeHandlingService';

import SerialId from '../models/SerialId';
import TenantId from '../models/TenantId';
import {
  LocalStorageAreaMock,
  StorageAreaMock,
  SyncStorageAreaMock,
} from '../test-helpers/chrome-api';
import MockTelemetryService from '../test-helpers/MockTelemetryService';
import FilterController from './FilterController';

// Mock YoutubeHandlingService to be able to spy on it
jest.mock('../services/YoutubeHandlingService', () => {
  // Create a mock constructor that returns an object with all the required methods
  const mockYoutubeHandlingServiceInstance = {
    removeTabEntryFromLookupTable: jest.fn(),
    removeAllTabsFromLookupTable: jest.fn(),
    saveKeyToLookup: jest.fn(),
    getUrlForTabFrame: jest.fn().mockReturnValue(null),
    getAllUrlsForTab: jest.fn().mockReturnValue([]),
    getIsHandlingYoutubeForTabFrame: jest.fn().mockReturnValue(false),
    removeFrameEntryFromTab: jest.fn().mockReturnValue(true),
    urlKey: 'url',
    isHandlingYoutubeKey: 'isHandlingYoutube',
  };

  return jest.fn().mockImplementation(() => mockYoutubeHandlingServiceInstance);
});

// Mock ContentScriptService to avoid DOMParser not defined error
jest.mock('../services/ContentScriptService', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      init: jest.fn().mockResolvedValue(undefined),
      sendYoutubeContentToExtension: jest.fn().mockResolvedValue(undefined),
      sendEnableYoutubeHandlingMessage: jest.fn().mockResolvedValue(undefined),
    })),
  };
});

// Set up Chrome API mocks before any tests run
beforeAll(() => {
  jest.useFakeTimers();

  // Set up storage area mocks
  const localStorageMock = new LocalStorageAreaMock();
  const syncStorageMock = new SyncStorageAreaMock();
  const managedStorageMock = new StorageAreaMock();

  // Configure Chrome API with our mocks
  global.chrome = {
    runtime: {
      id: 'mock-extension-id',
      getURL: jest.fn((path: string) => `chrome-extension://mock-extension-id/${path}`),
      onMessage: {
        addListener: jest.fn(),
        removeListener: jest.fn(),
      },
      sendMessage: jest.fn(),
    },
    tabs: {
      onUpdated: {
        addListener: jest.fn(),
        removeListener: jest.fn(),
      },
      onRemoved: {
        addListener: jest.fn(),
        removeListener: jest.fn(),
      },
      get: jest.fn(),
      sendMessage: jest.fn(),
      query: jest.fn().mockImplementation(() => Promise.resolve([])),
      update: jest.fn(),
      executeScript: jest.fn(),
    },
    storage: {
      local: localStorageMock,
      sync: syncStorageMock,
      managed: managedStorageMock,
    },
    webRequest: {
      onBeforeRequest: {
        addListener: jest.fn(),
        removeListener: jest.fn(),
      },
    },
    scripting: {
      executeScript: jest.fn(),
    },
    action: {
      setIcon: jest.fn().mockResolvedValue(undefined),
    },
  } as unknown as typeof chrome;
});

// Clean up after tests
afterAll(() => {
  jest.useRealTimers();
  jest.restoreAllMocks();
});

describe('FilterController YouTube URL handling', () => {
  let filterController: FilterController;
  let mockFilterService: any;
  let mockYoutubeHandlingService: any;
  let mockConsoleDebug: jest.SpyInstance;
  let mockConsoleWarn: jest.SpyInstance;

  beforeEach(() => {
    jest.clearAllTimers();
    jest.clearAllMocks();

    mockConsoleDebug = jest.spyOn(console, 'debug').mockImplementation();
    mockConsoleWarn = jest.spyOn(console, 'warn').mockImplementation();

    mockFilterService = {
      filterMode: FilterMode.full,
      analyse: jest.fn().mockReturnValue({ allow: true }),
      getCategoryNames: jest.fn().mockReturnValue([]),
      onFilterModeChanged: {
        addListener: jest.fn(),
      },
      fullFilter: {
        policyConfig: undefined, // Will be set in test setup
      },
    };

    const mockIpService = {
      isDeviceInLocation: jest.fn().mockReturnValue(true),
    } as unknown as IpService;
    const mockConfig = {} as IExtensionConfig;
    const mockTelemetryService = new MockTelemetryService();
    const mockAlarmService = {
      addListener: jest.fn(),
      create: jest.fn(),
      remove: jest.fn(),
      dispatch: jest.fn(),
    } as unknown as AlarmService;
    const mockStorageService = {} as StorageService;
    const mockDocumentListener = {
      onAdded: {
        addListener: jest.fn(),
      },
      onModified: {
        addListener: jest.fn(),
      },
      onRemoved: {
        addListener: jest.fn(),
      },
      isStarted: false,
      data: null,
      start: jest.fn(),
      stop: jest.fn(),
      set: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      get path() {
        return undefined;
      },
      get exists() {
        return false;
      },
      get isSubscribed() {
        return false;
      },
    } as unknown as FirestoreDocumentListener<any>;
    const startupDurationStart = 0;

    filterController = new FilterController(
      mockConfig,
      mockTelemetryService,
      mockAlarmService,
      mockIpService,
      mockStorageService,
      mockDocumentListener,
      startupDurationStart,
    );

    // Replace the filter service with our mock
    Object.defineProperty(filterController, 'filterService', {
      value: mockFilterService,
      writable: true,
    });

    mockYoutubeHandlingService =
      (YoutubeHandlingService as jest.Mock).mock.results[0]?.value ||
      new (YoutubeHandlingService as jest.Mock)();

    Object.defineProperty(filterController, '_youtubeHandlingService', {
      value: mockYoutubeHandlingService,
      writable: true,
    });

    if (!mockYoutubeHandlingService.removeTabEntryFromLookupTable) {
      mockYoutubeHandlingService.removeTabEntryFromLookupTable = jest.fn();
    }

    // @ts-expect-error - Accessing private method for testing
    filterController._scanTab = jest.fn().mockImplementation(async () => {
      await Promise.resolve();
    });
    // @ts-expect-error - Accessing private method for testing
    filterController._scanTabUrl = jest.fn().mockReturnValue(true);

    // @ts-expect-error - Accessing private method for testing
    const originalOnTabUpdated = filterController._onTabUpdated;
    // @ts-expect-error - Accessing private method for testing
    filterController._onTabUpdated = async (tabId, changeInfo, tab) => {
      if (changeInfo.url?.includes('youtube.com')) {
        console.debug(`YouTube URL changed to ${changeInfo.url}`, tab);
      }
      // Call original but don't await - the original method doesn't return a promise
      originalOnTabUpdated.call(filterController, tabId, changeInfo, tab);
    };
  });

  afterEach(() => {
    // Clear any intervals/timeouts
    jest.clearAllTimers();
    jest.clearAllMocks();

    // Restore console mocks
    mockConsoleDebug.mockRestore();
    mockConsoleWarn.mockRestore();

    // Clean up the FilterController
    if (filterController) {
      // Clear any intervals that might have been set up
      // @ts-expect-error - Accessing private property for testing
      if (filterController._scanDebounceTimer) {
        // @ts-expect-error - Accessing private property for testing
        clearTimeout(filterController._scanDebounceTimer);
        // @ts-expect-error - Accessing private property for testing
        filterController._scanDebounceTimer = null;
      }
    }
  });

  describe('_onTabUpdated', () => {
    it('should properly handle YouTube URL changes to process dynamic navigation', async () => {
      const tab = {
        id: 123,
        url: 'https://www.youtube.com/watch?v=new-video-id',
      } as chrome.tabs.Tab;

      const changeInfo = {
        url: 'https://www.youtube.com/watch?v=new-video-id',
        status: 'complete',
      } as chrome.tabs.TabChangeInfo;

      // @ts-expect-error - Accessing private method for testing
      await filterController._onTabUpdated(tab.id, changeInfo, tab);

      // Verify the lookup table entry was removed
      expect(mockYoutubeHandlingService.removeTabEntryFromLookupTable).toHaveBeenCalledWith(tab.id);

      // @ts-expect-error - Accessing private property for testing
      expect(filterController._scanTab).toHaveBeenCalledWith(tab, false, false);

      expect(mockConsoleDebug).toHaveBeenCalledWith(
        expect.stringContaining('YouTube URL changed to'),
        expect.anything(),
      );
    });

    it('should still handle non-YouTube URL changes correctly', async () => {
      // Mock tab with non-YouTube URL
      const tab = {
        id: 123,
        url: 'https://example.com/page',
      } as chrome.tabs.Tab;

      // Create change info with URL change but tab not in loading state
      const changeInfo = {
        url: 'https://example.com/page',
        status: 'complete',
      } as chrome.tabs.TabChangeInfo;

      // @ts-expect-error - Accessing private method for testing
      await filterController._onTabUpdated(tab.id, changeInfo, tab);

      expect(mockYoutubeHandlingService.removeTabEntryFromLookupTable).toHaveBeenCalledWith(tab.id);
      // @ts-expect-error - Accessing private property for testing
      expect(filterController._scanTab).toHaveBeenCalledWith(tab, false, false);

      // Verify debug logging wasn't called for non-YouTube URL
      expect(mockConsoleDebug).not.toHaveBeenCalledWith(
        expect.stringContaining('YouTube URL changed to'),
        expect.anything(),
      );
    });

    it('should only scan URL for loading state changes', async () => {
      // Mock tab with YouTube URL
      const tab = {
        id: 123,
        url: 'https://www.youtube.com/watch?v=video-id',
      } as chrome.tabs.Tab;

      // Create change info with URL change and loading state
      const changeInfo = {
        url: 'https://www.youtube.com/watch?v=video-id',
        status: 'loading',
      } as chrome.tabs.TabChangeInfo;

      // @ts-expect-error - Accessing private method for testing
      await filterController._onTabUpdated(tab.id, changeInfo, tab);

      // Verify the lookup table entry was removed
      expect(mockYoutubeHandlingService.removeTabEntryFromLookupTable).toHaveBeenCalledWith(tab.id);

      // Verify only URL scan was triggered for loading state
      // @ts-expect-error - Accessing private property for testing
      expect(filterController._scanTabUrl).toHaveBeenCalledWith(tab, false);
      // @ts-expect-error - Accessing private property for testing
      expect(filterController._scanTab).not.toHaveBeenCalled();
    });

    it('should call _scanTabUrl for tab completion without URL change (prerender activation)', async () => {
      // Mock tab that completes loading without URL change (prerender activation scenario)
      const tab = {
        id: 456,
        url: 'https://example.com/prerendered-page',
      } as chrome.tabs.Tab;

      // Create change info with only status change (no URL change)
      const changeInfo = {
        status: 'complete',
      } as chrome.tabs.TabChangeInfo;

      // @ts-expect-error - Accessing private method for testing
      await filterController._onTabUpdated(tab.id, changeInfo, tab);

      // Verify that _scanTabUrl is called for prerender activation
      // @ts-expect-error - Accessing private property for testing
      expect(filterController._scanTabUrl).toHaveBeenCalledWith(tab, false);

      // Verify that _scanTab is NOT called (since there was no URL change)
      // @ts-expect-error - Accessing private property for testing
      expect(filterController._scanTab).not.toHaveBeenCalled();

      // Verify lookup table is NOT cleared (since there was no URL change)
      expect(mockYoutubeHandlingService.removeTabEntryFromLookupTable).not.toHaveBeenCalled();
    });

    it('should not call _scanTabUrl twice when both URL changes and status becomes complete', async () => {
      // Mock tab with URL change AND status complete (potential duplicate scenario)
      const tab = {
        id: 789,
        url: 'https://example.com/new-page',
      } as chrome.tabs.Tab;

      // Create change info with both URL change and complete status
      const changeInfo = {
        url: 'https://example.com/new-page',
        status: 'complete',
      } as chrome.tabs.TabChangeInfo;

      // @ts-expect-error - Accessing private method for testing
      await filterController._onTabUpdated(tab.id, changeInfo, tab);

      // Verify the lookup table entry was removed due to URL change
      expect(mockYoutubeHandlingService.removeTabEntryFromLookupTable).toHaveBeenCalledWith(tab.id);

      // Verify _scanTab is called for URL change (not loading state)
      // @ts-expect-error - Accessing private property for testing
      expect(filterController._scanTab).toHaveBeenCalledWith(tab, false, false);

      // Verify _scanTabUrl is NOT called (prevented duplicate analysis)
      // @ts-expect-error - Accessing private property for testing
      expect(filterController._scanTabUrl).not.toHaveBeenCalled();
    });
  });

  describe('YouTube handling decision logic', () => {
    let mockPolicyConfig: any;
    let mockFlattenedPolicies: any[];
    let mockCustomCategories: any[];

    beforeEach(() => {
      // Create mock policy config with required structure
      mockPolicyConfig = {
        flattenedPolicies: [],
        custom_categories: [],
        mappedGroups: [
          { id: 'group1', name: 'Test Group 1' },
          { id: 'Everyone', name: 'Everyone' },
        ],
        users: [{ id: 'user1', name: '<EMAIL>' }],
        locations: [
          { id: 'loc1', name: 'Test Location', sources: ['192.168.1.0/24'] },
          { id: 'Everywhere', name: 'Everywhere' },
        ],
        time_slots: [
          {
            id: 'Anytime',
            name: 'Anytime',
            times: [['00:00:00', '23:59:59']],
            tenant: 'test',
          },
        ],
      };

      // Mock custom categories with video IDs
      mockCustomCategories = [
        {
          id: 'cat1',
          category_id: 'video-category-1',
          component: {
            videoids: ['dQw4w9WgXcQ', 'abc123def456'],
          },
          name: 'Video Category 1',
        },
        {
          id: 'cat2',
          category_id: 'video-category-2',
          component: {
            videoids: ['xyz789uvw012'],
          },
          name: 'Video Category 2',
        },
        {
          id: 'cat3',
          category_id: 'no-video-category',
          component: {
            domainsurls: ['example.com'],
          },
          name: 'Non-Video Category',
        },
      ];

      // Mock flattened policies
      mockFlattenedPolicies = [
        {
          id: 'policy1',
          enabled: 'on',
          what: ['cat1'], // Contains video IDs
          who: ['Everyone'],
          where: ['Everywhere'],
          when: ['Anytime'],
          action: 'block',
          order: '1',
        },
        {
          id: 'policy2',
          enabled: 'on',
          what: ['cat2'], // Contains video IDs
          who: ['group1'],
          where: ['loc1'],
          when: ['Anytime'],
          action: 'block',
          order: '2',
        },
        {
          id: 'policy3',
          enabled: 'off', // Disabled policy
          what: ['cat1'],
          who: ['Everyone'],
          where: ['Everywhere'],
          when: ['Anytime'],
          action: 'block',
          order: '3',
        },
        {
          id: 'policy4',
          enabled: 'on',
          what: ['cat3'], // No video IDs
          who: ['Everyone'],
          where: ['Everywhere'],
          when: ['Anytime'],
          action: 'block',
          order: '4',
        },
      ];

      // Set up the mock filter service to return our test data
      mockFilterService.fullFilter.policyConfig = mockPolicyConfig;
      mockPolicyConfig.flattenedPolicies = mockFlattenedPolicies;
      mockPolicyConfig.custom_categories = mockCustomCategories;

      // Mock provisioning info for user context
      // @ts-expect-error - Accessing private property for testing
      filterController._provisioningInfo = {
        user: '<EMAIL>',
        tenantId: new TenantId('3a4007ea-a817-11eb-9c60-3d943bce7542'),
        hardwareId: 'test-hardware',
        serialId: new SerialId('UNCLTEST7GCBRNZG'),
        deviceName: 'test-device',
        agentVersion: '1.0.0',
        domain: 'test-domain.com',
        localGroups: ['test-group'],
        validTenantUuid: true,
      };
    });

    describe('_determineYouTubeHandling', () => {
      it('should enable YouTube handling when applicable video ID policies exist', () => {
        // @ts-expect-error - Accessing private method for testing
        const result = filterController._determineYouTubeHandling(
          mockFlattenedPolicies,
          mockCustomCategories,
        );

        expect(result).toBe(true);
        expect(mockConsoleDebug).toHaveBeenCalledWith(
          expect.stringContaining('YouTube handling will be enabled'),
        );
      });

      it('should disable YouTube handling when no video ID policies exist', () => {
        // Remove all video IDs from custom categories
        const categoriesWithoutVideoIds = mockCustomCategories.map((cat) => ({
          ...cat,
          component: { domainsurls: ['example.com'] },
        }));

        // @ts-expect-error - Accessing private method for testing
        const result = filterController._determineYouTubeHandling(
          mockFlattenedPolicies,
          categoriesWithoutVideoIds,
        );

        expect(result).toBe(false);
        expect(mockConsoleDebug).toHaveBeenCalledWith(
          'No video ID policies found - YouTube handling will be disabled',
        );
      });

      it('should disable YouTube handling when video ID policies exist but none are applicable', () => {
        // Create policies that won't match current user context
        const inapplicablePolicies = [
          {
            id: 'policy1',
            enabled: 'on',
            what: ['cat1'], // Contains video IDs
            who: ['nonexistent-group'], // Won't match
            where: ['Everywhere'],
            when: ['Anytime'],
            action: 'block',
            order: '1',
          },
        ];

        // @ts-expect-error - Accessing private method for testing
        const result = filterController._determineYouTubeHandling(
          inapplicablePolicies,
          mockCustomCategories,
        );

        expect(result).toBe(false);
        expect(mockConsoleDebug).toHaveBeenCalledWith(
          expect.stringContaining('YouTube handling will be disabled'),
        );
      });
    });

    describe('_getVideoIdPolicies', () => {
      it('should return only enabled policies containing video IDs', () => {
        // @ts-expect-error - Accessing private method for testing
        const result = filterController._getVideoIdPolicies(
          mockFlattenedPolicies,
          mockCustomCategories,
        );

        expect(result).toHaveLength(2); // policy1 and policy2
        expect(result.map((p: any) => p.id)).toEqual(['policy1', 'policy2']);
      });

      it('should exclude disabled policies', () => {
        const allDisabledPolicies = mockFlattenedPolicies.map((policy) => ({
          ...policy,
          enabled: false,
        }));

        // @ts-expect-error - Accessing private method for testing
        const result = filterController._getVideoIdPolicies(
          allDisabledPolicies,
          mockCustomCategories,
        );

        expect(result).toHaveLength(0);
      });

      it('should exclude policies without video IDs', () => {
        const policiesWithoutVideoIds = [mockFlattenedPolicies[3]]; // policy4 - no video IDs

        // @ts-expect-error - Accessing private method for testing
        const result = filterController._getVideoIdPolicies(
          policiesWithoutVideoIds,
          mockCustomCategories,
        );

        expect(result).toHaveLength(0);
      });
    });

    describe('_evaluateYouTubePolicyApplicability', () => {
      it('should return true when policies match user groups, location, and timeslot', () => {
        const videoIdPolicies = [mockFlattenedPolicies[0]]; // policy1 with Everyone

        // @ts-expect-error - Accessing private method for testing
        const result = filterController._evaluateYouTubePolicyApplicability(videoIdPolicies);

        expect(result).toBe(true);
      });

      it('should return false when no policy config is available', () => {
        mockFilterService.fullFilter.policyConfig = undefined;
        const videoIdPolicies = [mockFlattenedPolicies[0]];

        // @ts-expect-error - Accessing private method for testing
        const result = filterController._evaluateYouTubePolicyApplicability(videoIdPolicies);

        expect(result).toBe(false);
        expect(mockConsoleDebug).toHaveBeenCalledWith(
          'No policy config available for YouTube policy evaluation',
        );
      });

      it('should return false when user group does not match', () => {
        const inapplicablePolicy = {
          ...mockFlattenedPolicies[0],
          who: ['nonexistent-group'],
        };

        // @ts-expect-error - Accessing private method for testing
        const result = filterController._evaluateYouTubePolicyApplicability([inapplicablePolicy]);

        expect(result).toBe(false);
      });

      it('should return false when location does not match', () => {
        // Mock IP service to return false for location matching
        const mockIpService = {
          isDeviceInLocation: jest.fn().mockReturnValue(false),
        } as unknown as IpService;

        // Create a new FilterController with the failing IP service
        const testFilterController = new FilterController(
          {} as IExtensionConfig,
          new MockTelemetryService(),
          {
            addListener: jest.fn(),
            create: jest.fn(),
            remove: jest.fn(),
            dispatch: jest.fn(),
          } as unknown as AlarmService,
          mockIpService,
          {} as StorageService,
          {
            onAdded: { addListener: jest.fn() },
            onModified: { addListener: jest.fn() },
            onRemoved: { addListener: jest.fn() },
            isStarted: false,
            data: null,
            start: jest.fn(),
            stop: jest.fn(),
            set: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            get path() {
              return undefined;
            },
            get exists() {
              return false;
            },
            get isSubscribed() {
              return false;
            },
          } as unknown as FirestoreDocumentListener<any>,
          0,
        );

        // Set up the same mocks
        Object.defineProperty(testFilterController, 'filterService', {
          value: mockFilterService,
          writable: true,
        });
        // @ts-expect-error - Accessing private property for testing
        testFilterController._provisioningInfo = {
          user: '<EMAIL>',
          tenantId: new TenantId('3a4007ea-a817-11eb-9c60-3d943bce7542'),
          hardwareId: 'test-hardware',
          serialId: new SerialId('UNCLTEST7GCBRNZG'),
          deviceName: 'test-device',
          agentVersion: '1.0.0',
          domain: 'test-domain.com',
          localGroups: ['test-group'],
          validTenantUuid: true,
        };

        const locationSpecificPolicy = {
          ...mockFlattenedPolicies[0],
          where: ['loc1'], // Specific location that won't match
        };

        // @ts-expect-error - Accessing private method for testing
        const result = testFilterController._evaluateYouTubePolicyApplicability([
          locationSpecificPolicy,
        ]);

        expect(result).toBe(false);
      });

      it('should handle multiple policies and return true if any match', () => {
        const mixedPolicies = [
          {
            ...mockFlattenedPolicies[0],
            who: ['nonexistent-group'], // Won't match
          },
          mockFlattenedPolicies[0], // Will match
        ];

        // @ts-expect-error - Accessing private method for testing
        const result = filterController._evaluateYouTubePolicyApplicability(mixedPolicies);

        expect(result).toBe(true);
      });
    });

    describe('_getCurrentTimeInterval', () => {
      it('should return a valid time interval', () => {
        // @ts-expect-error - Accessing private method for testing
        const result = filterController._getCurrentTimeInterval();

        expect(typeof result).toBe('number');
        expect(result).toBeGreaterThanOrEqual(0);
        // Should be less than a week in seconds (7 * 24 * 60 * 60 = 604800)
        expect(result).toBeLessThan(604800);
      });
    });

    describe('Integration tests', () => {
      it('should handle policy updates and re-evaluate YouTube handling', () => {
        // Initial setup with video ID policies
        // @ts-expect-error - Accessing private method for testing
        let result = filterController._determineYouTubeHandling(
          mockFlattenedPolicies,
          mockCustomCategories,
        );
        expect(result).toBe(true);

        // Update to remove all video IDs
        const categoriesWithoutVideoIds = mockCustomCategories.map((cat) => ({
          ...cat,
          component: { domainsurls: ['example.com'] },
        }));

        // @ts-expect-error - Accessing private method for testing
        result = filterController._determineYouTubeHandling(
          mockFlattenedPolicies,
          categoriesWithoutVideoIds,
        );
        expect(result).toBe(false);
      });

      it('should handle empty policies and categories gracefully', () => {
        // @ts-expect-error - Accessing private method for testing
        const result = filterController._determineYouTubeHandling([], []);

        expect(result).toBe(false);
        expect(mockConsoleDebug).toHaveBeenCalledWith(
          'No video ID policies found - YouTube handling will be disabled',
        );
      });
    });
  });
});

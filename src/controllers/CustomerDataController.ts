import BlocklistManager from 'blocklist-manager/BlocklistManager';
import BlocklistStatus from 'constants/BlocklistStatus';
import { Firestore } from 'firebase/firestore';
import Blocklist from 'guardian/Blocklist';
import IExtensionConfig from 'models/IExtensionConfig';
import { IProductConfig } from 'models/IProductConfig';
import LicenseResult from 'models/LicenseResult';
import ManagedPolicy from 'models/ManagedPolicy';
import TemplateString from 'models/TemplateString';
import UserDocument from 'models/UserDocument';
import ProvisioningInfo from 'provisioning/ProvisioningInfo';
import BlobStorageService from 'services/BlobStorageService';
import ClientSettingsService from 'services/ClientSettingsService';
import CloudGroupsService from 'services/CloudGroupsService';
import FirestoreDocumentListener from 'services/FirestoreDocumentListener';
import IpService from 'services/IpService';
import ITelemetryService from 'services/ITelemetryService';
import LocalCacheService from 'services/LocalCacheService';
import PolicyService from 'services/PolicyService';
import ProductConfigService from 'services/ProductConfigService';
import { resetBadgeTitle } from 'utilities/Helpers';
import StandaloneEvent from 'utilities/StandaloneEvent';

/**
 * Manages retrieving the required customer data (client settings and policy) from the cloud.
 *
 * Once all customer data has been downloaded and stored the `onCustomerDataLoaded` event will be triggered.
 * The data can then be accessed through the service getters.
 */
export default class CustomerDataController {
  /**
   * Construct a new controller instance, operating on the specified data.
   *
   * @param extensionConfig Contains basic hard-coded configuration information about the software.
   * @param localCacheService Contains various cache objects used to wrap local storage.
   * @param telemetryService Used to send log messages to the cloud.
   * @param ipService Manages getting the IP address and determining the location of this device.
   */
  constructor(
    extensionConfig: IExtensionConfig,
    localCacheService: LocalCacheService,
    telemetryService: ITelemetryService,
    ipService: IpService,
  ) {
    this._extensionConfig = extensionConfig;
    this._localCacheService = localCacheService;
    this._ipService = ipService;
    this._telemetryService = telemetryService;
    this.userDocumentListener = new FirestoreDocumentListener<UserDocument>(telemetryService);
  }

  /**
   * Initialises all of the services required to fetch the customer data.
   *
   * This must be called before using `start()` or `onFirestoreReady()`.
   *
   * @param fullBlocklist The main blocklist which will be populated when downloading customer data.
   * @param provisioningInfo Customer and user data.
   * @param managedPolicy The managed policy configuration containing extension settings.
   */
  public readonly initialise = async (
    fullBlocklist: Blocklist,
    provisioningInfo: ProvisioningInfo,
    managedPolicy: ManagedPolicy,
  ): Promise<void> => {
    this._clientSettingsService = new ClientSettingsService(
      new BlobStorageService(),
      this._localCacheService.clientSettings,
      this._telemetryService,
      provisioningInfo.tenantId,
    );
    this._policyService = new PolicyService(
      new BlobStorageService(),
      this._localCacheService.policyConfig,
      this._ipService,
      this._telemetryService,
      provisioningInfo.user,
      provisioningInfo.tenantId,
      provisioningInfo.localGroups,
      managedPolicy,
    );

    this._blocklistManager = new BlocklistManager(this._telemetryService, fullBlocklist);

    this._productConfigService = new ProductConfigService(
      this._localCacheService.productConfig,
      provisioningInfo.serialId,
      this._telemetryService,
    );

    this._cloudGroupsService = new CloudGroupsService(
      new TemplateString(this._extensionConfig.cloudGroupsUrlTemplate),
      this._telemetryService,
      this._localCacheService.cloudGroups,
      provisioningInfo.tenantId,
      provisioningInfo.user,
    );

    this._clientSettingsService.onClientSettingsLoaded.addListener(this._loadedEventHandler);
    this._policyService.onPolicyLoaded.addListener(this._loadedEventHandler);
    this._blocklistManager.onBlocklistLoaded.addListener(this._loadedEventHandler);
    this._productConfigService.onProductConfigChanged.addListener(this._productConfigLoaded);
    this._productConfigService.onFirestoreResubscribe.addListener(this._onFirestoreResubscribe);
    this._cloudGroupsService.onGroupsReady.addListener(this._loadedEventHandler);
  };

  /**
   * Starts the process of asynchronously downloading customer data from blob storage.
   * The customer data can only be loaded if the product config has been downloaded.
   *
   * The onCustomerDataLoaded event will be fired once all of the processes have finished running.
   *
   * @param isFirstRun Whether this is the first time receiving Firestore data (not from cache).
   * @todo Load all components from cache when the extension is provisioned.
   * @todo Use more granular event handlers for everything else, e.g. product config changed.
   */
  public readonly start = async (isFirstRun: boolean = true): Promise<void> => {
    if (this.productConfig?.cldflt !== undefined && this.productConfig.core !== undefined) {
      this._activeDownloads = this._getCustomerDataServices();

      // Load the blocklist from cache, if it's not already loaded.
      // TODO: Do this before product config is available, if possible.
      if (this._blocklistManager?.blocklist.status === BlocklistStatus.NotLoaded) {
        try {
          await this._blocklistManager?.loadFromCache();
        } catch (e: any) {
          console.warn('CustomerDataController - Failed to load blocklist from cache.', e);
        }
      }

      this._clientSettingsService?.start(this.productConfig.cldflt.configuration.clientSettings);
      await this._policyService?.start(this.productConfig.cldflt.configuration.config, isFirstRun);

      // TODO: In future, load the cached groups once on start-up, as soon as provisioning info is
      //  available. Call setConfig() whenever product config is loaded or updated. Be careful
      //  though as we currently need to ensure the _loadedEventHandler() still gets triggered.
      await this._cloudGroupsService?.loadFromCache();
      this._cloudGroupsService?.setConfig(this.productConfig.core.configuration.directory);
    }
  };

  /**
   * Stops any running processes.
   */
  public readonly stop = async (): Promise<void> => {
    this._clientSettingsService?.stop();
    this._policyService?.stop();
    this._activeDownloads = [];
    await this._blocklistManager?.clear();
  };

  /**
   * Runs any tasks that require firestore, such as downloading the product config if required.
   *
   * @param firestore The active firestore instance.
   * @param userDocumentPath The path of a document in Firestore which contains user-specific
   *  information and configuration. This may be an empty string if the information isn't available
   *  from the API yet.
   */
  public readonly onFirestoreReady = async (
    firestore: Firestore,
    userDocumentPath: string,
  ): Promise<void> => {
    this._firestore = firestore;

    this._productConfigService?.start(firestore);

    if (userDocumentPath !== '') {
      this.userDocumentListener?.start(firestore, userDocumentPath);
    }
  };

  /**
   * Restarts the product config service after a firestore listener failure.
   * @param alarm The chrome alarm used to delay resubscription/
   */
  private readonly _onFirestoreResubscribe = (): void => {
    if (this._firestore === undefined) {
      console.warn('Firestore listener failed but firestore not defined to resubscribe');
      return;
    }

    this._productConfigService?.start(this._firestore);
  };

  /**
   * Checks the licensing info and determines if the cldflt license is valid.
   *
   * @returns A boolean indicating if the user has a valid license.
   */
  public readonly getLicensingInfo = (): LicenseResult => {
    if (this._productConfigService === undefined || !this._productConfigService.isLoaded) {
      // We haven't loaded the config yet so continue as normal.
      const result = new LicenseResult();
      result.cldfltLicenseValid = true;
      return result;
    }

    return this._productConfigService.getLicensingInfo();
  };

  /**
   * Checks that the licensing info we have is valid.
   * If the cldflt license is not present or has expired then the `onUnlicensed` event will be triggered.
   **/
  public readonly checkProductLicensingInfo = async (): Promise<void> => {
    const info = this.getLicensingInfo();
    if (!info.cldfltLicenseValid) {
      // Unsubscribe from the firestore listeners if they have been set up.
      this._productConfigService?.firestoreUnsubscribe?.();
      this.onUnlicensed.dispatch(info);
      return;
    }

    await resetBadgeTitle();
  };

  /**
   * To be called when a service is finished loading.
   *
   * Once all services have finished loading it will emit the onCustomerDataLoaded event.
   */
  private readonly _loadedEventHandler = (serviceName: string): void => {
    const index = this._activeDownloads.indexOf(serviceName);

    if (index > -1) {
      this._activeDownloads.splice(index, 1);
    }

    // The blocklist should be loaded after the policy.
    // This avoids any race conditions between the blocklist and policy for any filtering that happens while both are being updated.
    // See Jira ticket: CHROME-453
    // TODO: Find a better way to handle this situation. We cannot be guaranteed that a blocklist
    //  download will actually work in a reasonable amount of time. If we have a blocklist in the
    //  cache then we should go ahead and use it if possible.
    if (
      this.productConfig?.cldflt !== undefined &&
      serviceName === this.policyService?.constructor.name
    ) {
      this._blocklistManager?.setConfig(this.productConfig.cldflt.configuration.blocklist);
    }

    if (
      (this._policyService?.isLoaded ?? false) &&
      this._blocklistManager?.blocklist.status === BlocklistStatus.Ready &&
      this._cloudGroupsService?.cloudGroups !== undefined &&
      this._activeDownloads.length === 0
    ) {
      this.onCustomerDataLoaded.dispatch();
    }
  };

  /**
   * Called if the product config saved in local storage has changed.
   *
   * It checks that the product licensing is valid and redownloads the customer data using the new settings.
   */
  private readonly _productConfigLoaded = async (): Promise<void> => {
    await this.checkProductLicensingInfo();
    await this.start(false); // Not first run since this is a config change
  };

  /**
   * The names of services that run when loading customer data.
   *
   * This should only be called when all of the services have been created. Otherwise it will return an empty array.
   *
   * @returns An array with the names of the customer data services. If one of the services is not ready an empty array will be returned.
   */
  private readonly _getCustomerDataServices = (): string[] => {
    if (
      this._clientSettingsService === undefined ||
      this._policyService === undefined ||
      this._blocklistManager === undefined ||
      this._cloudGroupsService === undefined
    ) {
      return [];
    }

    return [
      this._clientSettingsService.constructor.name,
      this._policyService.constructor.name,
      this._blocklistManager.constructor.name,
      this._cloudGroupsService.constructor.name,
    ];
  };

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Gets the current client settings service.
   */
  public get clientSettingsService(): ClientSettingsService | undefined {
    return this._clientSettingsService;
  }

  /**
   * Gets the current policy service.
   */
  public get policyService(): PolicyService | undefined {
    return this._policyService;
  }

  /**
   * Gets the current cloud groups service.
   */
  public get cloudGroupsService(): CloudGroupsService | undefined {
    return this._cloudGroupsService;
  }

  /**
   * Gets the product config from local storage.
   *
   * If the config has not yet been downloaded then it will return undefined.
   */
  public get productConfig(): IProductConfig | undefined {
    return this._productConfigService?.productConfig;
  }

  /**
   * Gets the product config service.
   */
  public get productConfigService(): ProductConfigService | undefined {
    return this._productConfigService;
  }

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The extension configuration which was passed into the constructor.
   */
  private readonly _extensionConfig: IExtensionConfig;

  /**
   * Triggered once all of the customer data has been loaded.
   */
  public readonly onCustomerDataLoaded: StandaloneEvent = new StandaloneEvent();

  /**
   * Triggered if the customer isn't licensed for cldflt using the data from firestore.
   */
  public readonly onUnlicensed = new StandaloneEvent<[LicenseResult | undefined]>();

  /**
   * Stores the local cache service that can be shared between services.
   */
  private readonly _localCacheService: LocalCacheService;

  /**
   * Manages ip fetching and actions.
   */
  private readonly _ipService: IpService;

  /**
   * The telemetry service for logging events.
   */
  private readonly _telemetryService: ITelemetryService;

  /**
   * Manages the download and parsing of the client settings.
   */
  private _clientSettingsService?: ClientSettingsService;

  /**
   * Manages the download and parsing of the policy.
   */
  private _policyService?: PolicyService;

  /**
   * Manages downloading and caching the standard blocklist files.
   */
  private _blocklistManager?: BlocklistManager;

  /**
   * Manages downloading the product config from firestore.
   */
  private _productConfigService?: ProductConfigService;

  /**
   * This will listen for the user-specific document in Firestore.
   */
  public readonly userDocumentListener: FirestoreDocumentListener<UserDocument>;

  /**
   * Manages downloading the cloud groups based on user and tenant
   */
  private _cloudGroupsService?: CloudGroupsService;

  /**
   * Holds a local copy of firestore.
   */
  private _firestore?: Firestore;

  /**
   * Keeps track of all the currently active downloads.
   */
  private _activeDownloads: string[] = [];
}

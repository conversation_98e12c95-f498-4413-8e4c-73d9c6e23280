import DeviceRegistrationService from 'services/DeviceRegistrationService';
import FirebaseAppService from 'services/FirebaseAppService';
import FirebaseAuthService from 'services/FirebaseAuthService';
import StorageService from 'services/StorageService';
import { FirebaseApp } from 'firebase/app';
import { Firestore } from 'firebase/firestore';
import StandaloneEvent from 'utilities/StandaloneEvent';
import Jwt from 'models/Jwt';
import DeviceId from 'models/DeviceId';
import IExtensionConfig from 'models/IExtensionConfig';
import ProvisioningInfo from 'provisioning/ProvisioningInfo';
import TemplateString from 'models/TemplateString';
import ProductCode from 'constants/ProductCode';
import HeartbeatService from 'services/HeartbeatService';
import AlarmService from 'services/AlarmService';
import ITelemetryService from 'services/ITelemetryService';
import IpService from 'services/IpService';
import HeartbeatData from 'models/HeartbeatData';

/**
 * Manages the setup and authentication of our main connection to the cloud.
 * This includes setting up Firebase and registering with the device management API.
 */
export default class CloudConnectionController {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance of the cloud connection controller.
   * This does not connect to the cloud or load anything from cache. You must call start() after
   *  construction for that.
   *
   * @param extensionConfig Details of static configuration information, such as URLs.
   * @param firebaseStorage Wrapper object for the caching Firebase configuration.
   * @param deviceRegistrationStorage Wrapper object for caching device registration details.
   */
  public constructor(
    extensionConfig: IExtensionConfig,
    firebaseStorage: StorageService,
    deviceRegistrationStorage: StorageService,
    alarmService: AlarmService,
    telemetryService: ITelemetryService,
    ipService: IpService,
  ) {
    this._extensionConfig = extensionConfig;
    this._telemetryService = telemetryService;

    // Firebase app service and listeners.
    this._firebaseAppService = new FirebaseAppService(firebaseStorage, telemetryService);
    this._firebaseAppService.onInitialised.addListener(this._onFirebaseAppInitialised);
    this._firebaseAppService.onFailed.addListener(this.onFailed);

    // Device registration service and listeners.
    this._deviceRegistrationService = new DeviceRegistrationService(
      deviceRegistrationStorage,
      telemetryService,
      ipService,
    );
    this._deviceRegistrationService.onRegistered.addListener(this._onDeviceRegistered);
    this._deviceRegistrationService.onFailed.addListener(this.onFailed);
    this._deviceRegistrationService.onUnlicensed.addListener(this.onUnlicensed);

    // Firebase authentication service and listeners.
    this._firebaseAuthService = new FirebaseAuthService(telemetryService);
    this._firebaseAuthService.onAuthenticated.addListener(this._onFirebaseAuthenticated);
    this._firebaseAuthService.onAuthenticationRequired.addListener(
      this._onFirebaseAuthenticationRequired,
    );
    this._firebaseAuthService.onAuthenticationFailed.addListener(this.onFailed);

    this.heartbeatService = new HeartbeatService(alarmService);
    this.heartbeatService.onHeartbeatAlarm.addListener(this.onHeartbeatAlarm);
    this.heartbeatService.onUnregistered.addListener(this._deviceRegistrationService.reregister);
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Check if this controller is running; i.e. start() has been called.
   */
  public get isRunning(): boolean {
    return this._firebaseAppService.isRunning;
  }

  /**
   * Check if this controller has finished setup and authentication successfully.
   * Note that it may have initialised from cached data.
   */
  public get isReady(): boolean {
    return this._firebaseAuthService.isAuthenticated;
  }

  /**
   * Get the underlying Firebase app instance.
   * Returns undefined if it hasn't been initialised yet.
   */
  public get firebaseApp(): FirebaseApp | undefined {
    return this._firebaseAppService.firebaseApp;
  }

  /**
   * Get the device ID assigned to this instance by the device management API.
   * Returns undefined if we have not successfully registered yet.
   */
  public get deviceId(): DeviceId | undefined {
    return this._deviceRegistrationService.deviceId;
  }

  /**
   * Get the active firestore instance.
   * Returns undefined if it hasn't been initialised yet.
   */
  public get firestore(): Firestore | undefined {
    return this._firebaseAppService.firestore;
  }

  // -----------------------------------------------------------------------------------------------
  // Events.

  /**
   * Triggered when the cloud connection has been established and we are successfully authenticated.
   * The first parameter is the intialised and authenticated instance of Firestore.
   * The second is the path of a Firestore document containing user-specific information and
   *  configuration. It may be empty if that information isn't available from the API yet.
   */
  public readonly onReady = new StandaloneEvent<[Firestore, string]>();

  /**
   * Triggered if the cloud connection has failed and cannot (or will not) recover.
   * This is not used in the case of the customer not being licensed. See onUnlicensed for that.
   */
  public readonly onFailed = new StandaloneEvent();

  /**
   * Triggered if the connection failed because the customer isn't licensed.
   * Note: Even if registration succeeds (and this event isn't triggered), you cannot assume that
   *  the customer has a valid license. Be sure to check the licence information retrieved from
   *  Firestore.
   */
  public readonly onUnlicensed = new StandaloneEvent();

  /**
   * Triggered when the heartbeat alarm has elapsed and the extension needs to send a heartbeat request.
   */
  public readonly onHeartbeatAlarm = new StandaloneEvent();

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Initiate Firebase and the connection to the cloud.
   * This will do as much as possible from the cache.
   * When the connection has successfully been set up, the onReady event will be triggered.
   * The caller must wait for this function to finish before calling it again, and before calling
   *  stop().
   *
   * @param provisioningInfo Customer/user details which we need for device registration.
   */
  public readonly start = async (provisioningInfo: ProvisioningInfo): Promise<void> => {
    await this.stop();
    this._provisioningInfo = provisioningInfo;

    // Start initialising Firebase. This will initialise from cache if possible, but will request
    //  config from the cloud if necessary. When this is ready, we'll start Firebase messaging,
    //  authentication, and device registration.
    await this._firebaseAppService.start(
      new TemplateString(this._extensionConfig.configFirebaseUrlTemplate),
      provisioningInfo.serialId,
    );
  };

  /**
   * Close the connection to the cloud, and de-initialise Firebase.
   * Multiple calls to stop() at the same time are safe. However, the caller must wait until stop()
   *  has finished before calling start() again.
   *
   *  @param isHardReset If true we will sign out of firebase and clear the firestore cache.
   *
   *  @warning Hard resetting will result in the current firestore instance being terminated.
   */
  public readonly stop = async (isHardReset: boolean = false): Promise<void> => {
    await this._firebaseAuthService.stop(isHardReset);
    await this._deviceRegistrationService.stop();
    await this._firebaseAppService.stop(isHardReset);
    this._provisioningInfo = undefined;
    this._authAttempts = 0;
  };

  public readonly sendHeartbeat = async (data: HeartbeatData): Promise<void> => {
    await this.heartbeatService.sendHeartbeat(data);
  };

  // -----------------------------------------------------------------------------------------------
  // Event handlers.

  /**
   * Triggered when FirebaseAppService has successfully initialised the Firebase core.
   *
   * @param firebaseApp The Firebase core which has been initialised.
   * @param messagingSenderId The Firebase messaging sender which was extracted from the Firebase
   *  config.
   */
  private readonly _onFirebaseAppInitialised = async (
    firebaseApp: FirebaseApp,
    messagingSenderId: string,
  ): Promise<void> => {
    if (this._provisioningInfo === undefined) {
      console.error(
        'CloudConnectionController cannot continue initialisation as provisioning info is not set.',
      );
      return;
    }

    // Register this device with the cloud if necessary. This will load registration details from
    //  cache if possible.
    await this._deviceRegistrationService.start(
      new TemplateString(this._extensionConfig.registerDeviceUrlTemplate),
      this._provisioningInfo,
      [ProductCode.cldflt],
    );

    // Begin authentication if possible. If we've previously been authenticated, then Firebase
    //  should automatically use cache authentication details. Otherwise, it will have to wait until
    //  we get a JWT from the DeviceRegistrationService.
    await this._firebaseAuthService.start(firebaseApp);
  };

  /**
   * Triggered when we have retrieved device registration details from the cloud or cache.
   *
   * @param jwt A JWT which can be used by the FirebaseAuthService to authenticate with the Firebase
   *  back-end. If this has been loaded from the cache then it may have expired. If we're aren't
   *  already authenticated with Firebase then a new one can be requested.
   * @param deviceId A unique identifier assigned to this instance by the device management API.
   * @param userDocumentPath The path of a Firestore document containing user-specific information
   *  and configuration. This may be an empty string if the information is available from the API
   *  yet.
   */
  private readonly _onDeviceRegistered = async (
    jwt: Jwt,
    deviceId: DeviceId,
    userDocumentPath: string,
  ): Promise<void> => {
    if (this._provisioningInfo !== undefined) {
      await this.heartbeatService.start(
        new TemplateString(this._extensionConfig.heartbeatUrlTemplate),
        this._provisioningInfo,
        deviceId,
      );

      this.heartbeatService.resetHeartbeatData();
    }

    this._telemetryService.setDeviceId(deviceId.toString());

    // Use the JWT to authenticate with Firebase if possible.
    // The Firebase auth service will trigger _onFirebaseAuthenticationRequired() later if it isn't
    //  running yet.
    if (!this._firebaseAuthService.isRunning) {
      console.debug(
        'CloudConnectionController - Received device registration details. Waiting for Firebase authentication service to be ready.',
      );
      return;
    }

    // If the JWT is still valid then try to use it. This probably isn't essential if we were
    //  already authenticated, but it's useful to ensure our authentication details are up-to-date.
    if (!jwt.willHaveExpiredSoon(1)) {
      console.debug('CloudConnectionController - Authenticating with JWT.');
      this._firebaseAuthService.authenticate(jwt);
      return;
    }

    // The JWT has already expired, or will expire very soon. This means it's probably an old one
    //  loaded from the cache. If we're already authenticate with Firebase then ignore it.
    if (this._firebaseAuthService.isAuthenticated) {
      console.debug(
        'CloudConnectionController - Ignoring cached authentication token as Firebase is already authenticated.',
      );
      return;
    }

    // If we have tried and failed to get a new auth token too many times we should try and continue with the token we currently have.
    if (this._authAttempts >= this._maxAuthAttempts) {
      this._authAttempts = 0;

      // Try and use the token anyway.
      this._firebaseAuthService.authenticate(jwt);
    }

    console.debug(
      'CloudConnectionController - Authentication token has expired. Requesting a new one.',
    );
    await this._deviceRegistrationService.requestNewAuthenticationToken();
    this._authAttempts++;
  };

  /**
   * Triggered when the FirebaseAuthService starts and doesn't have cached authentication.
   * It's also triggered if our authentication gets revoked remotely.
   */
  private readonly _onFirebaseAuthenticationRequired = async (): Promise<void> => {
    if (!this._deviceRegistrationService.isRunning) {
      // This shouldn't happen. The device registration service should have been started before the
      //  Firebase auth service. If it does happen, then the device registration service will
      //  trigger _onDeviceRegistered() if it does get started later.
      console.warn(
        'CloudConnectionController - Cannot authenticate firebase as the device registration service is not running.',
      );
      return;
    }

    // Get the existing authentication token. If we don't have one, or it expires soon, then request
    //  a new one.
    const jwt = this._deviceRegistrationService.jwt;
    if (jwt === undefined || jwt.willHaveExpiredSoon(1)) {
      console.debug(
        'CloudConnectionController - No authentication token, or it has expired. Requesting a new one.',
      );

      // This will trigger _onDeviceRegistered later if it succeeds.
      await this._deviceRegistrationService.requestNewAuthenticationToken();
      return;
    }

    this._firebaseAuthService.authenticate(jwt);
  };

  /**
   * Triggered when the authentication with firebase has been completed.
   */
  private readonly _onFirebaseAuthenticated = (): void => {
    if (this.firestore !== undefined) {
      this.onReady.dispatch(this.firestore, this._deviceRegistrationService.userDocumentPath ?? '');
    }
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The extension configuration which was passed into the constructor.
   */
  private readonly _extensionConfig: IExtensionConfig;

  /**
   * Requests Firebase config from the cloud, and uses it to initialise the Firebase core.
   * This must be initialised before the FirebaseAuthService can authenticate.
   */
  private readonly _firebaseAppService: FirebaseAppService;

  /**
   * Registers this device with the cloud.
   * This retrieves a JWT which can be used by the FirebaseAuthService to authenticate.
   */
  private readonly _deviceRegistrationService: DeviceRegistrationService;

  /**
   * Uses a JWT from the device registration service to authenticate with the Firebase back-end.
   * Authentication is necessary for accessing protected Firestore documents.
   * The Firestore core must be initialised (by FirebaseAppService) before authentication can occur.
   */
  private readonly _firebaseAuthService: FirebaseAuthService;

  /**
   * Sends heartbeat signal to device management API.
   */
  public readonly heartbeatService: HeartbeatService;

  /**
   * The telemetry service for logging events.
   */
  private readonly _telemetryService: ITelemetryService;

  /**
   * The provisioning info which was passed into start().
   * This will be undefined if start() hasn't been called, or stop() has been called since then.
   */
  private _provisioningInfo?: ProvisioningInfo;

  /**
   * Tracks the number of auth attempts made in a run.
   */
  private _authAttempts = 0;

  /**
   * The max number of auth attempts should be made in a run.
   */
  private readonly _maxAuthAttempts = 5;
}

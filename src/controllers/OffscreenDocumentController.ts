import ContentScriptMessage from 'content-script-messages/ContentScriptMessage';
import { isManifestV2 } from 'utilities/Helpers';
import StandaloneEvent from 'utilities/StandaloneEvent';

/**
 * Opens and manages communications with an off-screen document.
 * Off-screen documents can be used for various functionality, such as accessing front-end APIs
 *  which aren't available directly within the service worker.
 * A service worker can only have a single off-screen document at a time. It still safe to create
 *  multiple instances of this class. However, they will all communicate with the same document,
 *  and will all receive copies of any messages it sends.
 */
export default class OffscreenDocumentController {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance of the controller -- this adds any necessary event handlers.
   * This should be called during the first event loop iteration to ensure the events are handled
   *  correctly.
   */
  public constructor() {
    // This constructor gets called during unit tests. Ensure the Chrome API actually exists before
    //  trying to use it.
    if (chrome?.runtime?.onMessage !== undefined) {
      chrome.runtime.onMessage.addListener(this._onMessage);
      chrome.storage.managed.get('DisableOffScreenDocument', (isDisabled) => {
        if (isDisabled === undefined || isDisabled.DisableOffScreenDocument === undefined) {
          return;
        }

        this._isDisabled = isDisabled.DisableOffScreenDocument as boolean;

        console.debug(`Offscreen document is disabled: ${this._isDisabled.toString()}`);
      });
    }
  }

  // -----------------------------------------------------------------------------------------------
  // Accessors.

  /**
   * Get the complete URL of the off-screen document.
   * This includes the extension URL and the document path.
   */
  public get url(): string {
    return chrome.runtime.getURL(this.path);
  }

  // -----------------------------------------------------------------------------------------------
  // Operations.

  /**
   * Create the off-screen document, if it doesn't already exist.
   * The caller should wait for this to resolve before calling it again, and before calling stop().
   *
   * @return Returns a promise which resolves when the document has been created, or if it already
   *  exists. The promise may reject with an error if creation of the document fails.
   *
   * @note It isn't necessary to call this before calling sendMessage().
   * @note This function does nothing in mv2.
   */
  public readonly start = async (): Promise<void> => {
    if (isManifestV2 || !(await this._canCreateDocument())) {
      return;
    }

    // The offscreen API can cause a race condition. We have to check hasDocument() above before
    //  calling createDocument() below. However, because they're both async functions, multiple
    //  calls in quick succession can get interleaved, resulting in multiple attempts to create the
    //  document. We guard against this by storing the promise returned by createDocument(). If any
    //  subsequent calls come in while creation is in progress, then they'll all await the result of
    //  the first call.
    if (this._documentCreationPromise !== undefined) {
      await this._documentCreationPromise;
      return;
    }

    try {
      this._documentCreationPromise = chrome.offscreen.createDocument({
        justification: 'Used for querying the public IP address of the device.',
        // Note: The list of reasons should be manually updated when necessary.
        // It may affect how long the document remains active.
        reasons: [chrome.offscreen.Reason.WEB_RTC],
        url: this.url,
      });

      await this._documentCreationPromise;

      console.debug('Offscreen document created at URL:', this.url);
    } finally {
      // Important: Clear the stored promise so that nothing else waits on it later.
      this._documentCreationPromise = undefined;
    }
  };

  /**
   * Close the off-screen document, if it's currently open.
   * The caller should wait for this to resolve before calling it again, and before calling start().
   *
   * @return The returned promise will resolve when the offscreen document has been closed, or when
   *  it has failed. If closing the document fails then a warning will be logged to the console, but
   *  the promise is not expected to reject.
   * 
    @note This function does nothing in mv2.
   */
  public readonly stop = async (): Promise<void> => {
    if (isManifestV2) {
      return;
    }

    // This can suffer from the same race condition outlined in start(). However, we don't expect
    //  many calls to this function so it's probably not worth adding in the code to guard against
    //  it. The worst case scenario is a spurious warning in the console.
    try {
      if (await this._hasOffScreenDocument()) {
        await chrome.offscreen.closeDocument();
      }
    } catch (e: any) {
      console.warn('Failed to close offscreen document.', e);
    }
  };

  /**
   * Send the given message to the offscreen document.
   * This will create the offscreen document first if necessary.
   *
   * @param message The message to send to the offscreen document.
   * @return Returns a promise which resolves when the message has been sent, and (if applicable)
   *  when a response has been received. If the offscreen document sent a response, then the
   *  resolved value will be the response message, assuming it could be parsed. If it didn't send a
   *  response, then the resolved value will be undefined. The promise may reject if the message
   *  couldn't be sent.
   *
   * @note This uses the ContentScriptMessage type because it's not possible to direct messages
   *  specifically to the offscreen document. Other handlers could see the message too.
   *
   * @note This function does nothing in mv2.
   */
  public readonly sendMessage = async (
    message: ContentScriptMessage,
  ): Promise<ContentScriptMessage | undefined> => {
    if (isManifestV2) {
      return;
    }

    await this.start();
    const response = await chrome.runtime.sendMessage('', message);
    if (response === undefined) {
      this._startTimeout();
      return;
    }

    // Ensure we can parse the response successfully.
    try {
      this._startTimeout();
      return ContentScriptMessage.load(response);
    } catch (e: any) {
      console.warn('Failed to parse response message from offscreen document.', e);
      return undefined;
    }
  };

  // -----------------------------------------------------------------------------------------------
  // Event handlers.

  /**
   * Triggered when a standalone message is received.
   *
   * @param rawMessage The message which was received.
   */
  private readonly _onMessage = (rawMessage: any, sender: chrome.runtime.MessageSender): void => {
    // Ignore messages which come from tabs (i.e. content scripts) or other extensions.
    if (sender.tab !== undefined || sender.id !== chrome.runtime.id) {
      return;
    }

    try {
      this._startTimeout();

      this.onMessage.dispatch(ContentScriptMessage.load(rawMessage));
    } catch (e: any) {
      console.warn('OffscreenDocumentController failed to parse a standalone message.', e);
    }
  };

  /**
   * Checks if the offscreen document exists.
   *
   * @note The `chrome.runtime.getContexts function` used to check if a document has already been created is only available in chrome versions > 115.
   * If the function does not exist then false will be returned.
   * @returns A boolean indicating if a new offscreen document can be created.
   */
  private readonly _hasOffScreenDocument = async (): Promise<boolean> => {
    if ('getContexts' in chrome.runtime) {
      const contexts = await chrome.runtime.getContexts({
        contextTypes: [chrome.runtime.ContextType.OFFSCREEN_DOCUMENT],
        documentUrls: [this.url],
      });

      if (contexts.length > 0) {
        console.debug('An offscreen document already exists.');
        return true;
      }

      return false;
    }
    return false;
  };

  /**
   * Checks if a new offscreen document can be created.
   *
   * This will check that a document does not already exist, the `chrome.runtime.getContexts` function exists,
   * and that the offscreen document has not been disabled via the managed storage option,
   *
   * @note The `chrome.runtime.getContexts` function used to check if a document has already been created is only available in chrome versions > 115.
   * If the function does not exist then a new offscreen document should NOT be created.
   * @returns A boolean indicating if a new offscreen document can be created.
   */
  private readonly _canCreateDocument = async (): Promise<boolean> => {
    return (
      !(await this._hasOffScreenDocument()) && 'getContexts' in chrome.runtime && !this._isDisabled
    );
  };

  /**
   * Starts a timeout to close the offscreen document if no more messages are sent or received in the set time.
   */
  private readonly _startTimeout = (): void => {
    if (this._lastMessageTimeout !== undefined) {
      clearTimeout(this._lastMessageTimeout);
    }

    this._lastMessageTimeout = setTimeout(() => {
      this.stop().catch(console.error);
    }, this._timeoutDuration);
  };

  // -----------------------------------------------------------------------------------------------
  // Events.

  /**
   * This event is triggered when a message is received from the offscreen document.
   * The parameter will be the message which was received.
   *
   * @note This uses ContentScriptMessages because content scripts and offscreen documents
   *  effectively use the same message channel.
   */
  public readonly onMessage = new StandaloneEvent<[ContentScriptMessage]>();

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The path of the off-screen document, relative to the extension URL.
   */
  public readonly path = 'views/offscreen-document.html';

  /**
   * A boolean indicating if the offscreen document should always be disabled.
   * This should only ever be set using the DisableOffScreenDocument provisioning option.
   */
  private _isDisabled: boolean = false;

  /**
   * A promise which will resolve when we have finished creating the offscreen document.
   * This is stored to help guard against race conditions.
   * It will be undefined if an offscreen document has not been created, or we've finished creating
   *  it (i.e. it will only be populated while creation is in progress).
   */
  private _documentCreationPromise?: Promise<void>;

  /**
   * A timeout set whenever a message is sent/received for the offscreen document.
   *
   * Once the timeout expires the offscreen document should be closed.
   */
  private _lastMessageTimeout: NodeJS.Timeout | undefined;

  /**
   * The length in ms for the message timeout.
   */
  private readonly _timeoutDuration = 30000;
}

/**
 * @file Adds a compatibility layer which allows mv3 code to run as mv2.
 *
 * This affects the Chromium browser extension APIs (i.e. "chrome.*" functions). It mainly adds
 *  promise wrappers to functions which only had callback versions in mv2, although it also adds
 *  some aliases and stubs where appropriate. The intention is to allow us to develop the extension
 *  primarily for mv3, while having the ability to build it for mv2 instead with little or no extra
 *  development effort.
 *
 * @note To be effective, this file must be imported exactly once during the first event loop
 *  iteration, before any other imports and before any other code is executed.
 *
 * @warning This file assumes the extension permissions don't change at runtime. If optional
 *  permissions are granted by the user after the extension has started then any newly available
 *  functions will not be mv3 compatible.
 */

import { api } from './ApiEntry';

// If this isn't mv2 then do nothing.
if (chrome.runtime.getManifest().manifest_version !== 2) {
  // @ts-expect-error Exit the script early.
  return;
}

console.info('Applying manifest v2 shim.');

// -------------------------------------------------------------------------------------------------
// Promise wrappers.

if (chrome.accessibilityFeatures != null) {
  api('chrome.accessibilityFeatures.animationPolicy.clear')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.animationPolicy.get')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.animationPolicy.set')?.addPromiseWrapper();
}

if (chrome.accessibilityFeatures?.autoclick != null) {
  // ChromeOS only.
  api('chrome.accessibilityFeatures.autoclick.clear')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.autoclick.get')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.autoclick.set')?.addPromiseWrapper();

  api('chrome.accessibilityFeatures.caretHighlight.clear')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.caretHighlight.get')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.caretHighlight.set')?.addPromiseWrapper();

  api('chrome.accessibilityFeatures.cursorColor.clear')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.cursorColor.get')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.cursorColor.set')?.addPromiseWrapper();

  api('chrome.accessibilityFeatures.cursorHighlight.clear')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.cursorHighlight.get')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.cursorHighlight.set')?.addPromiseWrapper();

  api('chrome.accessibilityFeatures.dictation.clear')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.dictation.get')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.dictation.set')?.addPromiseWrapper();

  api('chrome.accessibilityFeatures.dockedMagnifier.clear')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.dockedMagnifier.get')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.dockedMagnifier.set')?.addPromiseWrapper();

  api('chrome.accessibilityFeatures.focusHighlight.clear')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.focusHighlight.get')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.focusHighlight.set')?.addPromiseWrapper();

  api('chrome.accessibilityFeatures.highContrast.clear')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.highContrast.get')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.highContrast.set')?.addPromiseWrapper();

  api('chrome.accessibilityFeatures.largeCursor.clear')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.largeCursor.get')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.largeCursor.set')?.addPromiseWrapper();

  api('chrome.accessibilityFeatures.screenMagnifier.clear')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.screenMagnifier.get')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.screenMagnifier.set')?.addPromiseWrapper();

  api('chrome.accessibilityFeatures.selectToSpeak.clear')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.selectToSpeak.get')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.selectToSpeak.set')?.addPromiseWrapper();

  api('chrome.accessibilityFeatures.spokenFeedback.clear')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.spokenFeedback.get')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.spokenFeedback.set')?.addPromiseWrapper();

  api('chrome.accessibilityFeatures.stickyKeys.clear')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.stickyKeys.get')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.stickyKeys.set')?.addPromiseWrapper();

  api('chrome.accessibilityFeatures.switchAccess.clear')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.switchAccess.get')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.switchAccess.set')?.addPromiseWrapper();

  api('chrome.accessibilityFeatures.virtualKeyboard.clear')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.virtualKeyboard.get')?.addPromiseWrapper();
  api('chrome.accessibilityFeatures.virtualKeyboard.set')?.addPromiseWrapper();
}

if (chrome.alarms != null) {
  // We're deliberately not shimming chrome.alarms.create(). Prior to Chrome v111, that function
  //  didn't accept a callback at all. Shimming it would add a callback, meaning it would fail on
  //  older browser versions. It's safe to await a call to the non-shimmed version.

  api('chrome.alarms.getAll')?.addPromiseWrapper();
  api('chrome.alarms.clearAll')?.addPromiseWrapper();
  api('chrome.alarms.clear')?.addPromiseWrapper();
  api('chrome.alarms.get')?.addPromiseWrapper();
}

if ((chrome as any).audio != null) {
  // ChromeOS only.
  api('chrome.audio.getDevices')?.addPromiseWrapper();
  api('chrome.audio.getMute')?.addPromiseWrapper();
  api('chrome.audio.setActiveDevices')?.addPromiseWrapper();
  api('chrome.audio.setMute')?.addPromiseWrapper();
  api('chrome.audio.setProperties')?.addPromiseWrapper();
}

if (chrome.bookmarks != null) {
  api('chrome.bookmarks.create')?.addPromiseWrapper();
  api('chrome.bookmarks.get')?.addPromiseWrapper();
  api('chrome.bookmarks.getChildren')?.addPromiseWrapper();
  api('chrome.bookmarks.getRecent')?.addPromiseWrapper();
  api('chrome.bookmarks.getSubTree')?.addPromiseWrapper();
  api('chrome.bookmarks.getTree')?.addPromiseWrapper();
  api('chrome.bookmarks.move')?.addPromiseWrapper();
  api('chrome.bookmarks.remove')?.addPromiseWrapper();
  api('chrome.bookmarks.removeTree')?.addPromiseWrapper();
  api('chrome.bookmarks.search')?.addPromiseWrapper();
  api('chrome.bookmarks.update')?.addPromiseWrapper();
}

if (chrome.browserAction != null) {
  api('chrome.browserAction.disable')?.addPromiseWrapper();
  api('chrome.browserAction.enable')?.addPromiseWrapper();
  api('chrome.browserAction.getBadgeBackgroundColor')?.addPromiseWrapper();
  api('chrome.browserAction.getBadgeText')?.addPromiseWrapper();
  api('chrome.browserAction.getPopup')?.addPromiseWrapper();
  api('chrome.browserAction.getTitle')?.addPromiseWrapper();
  api('chrome.browserAction.setBadgeBackgroundColor')?.addPromiseWrapper();
  api('chrome.browserAction.setBadgeText')?.addPromiseWrapper();
  api('chrome.browserAction.setIcon')?.addPromiseWrapper();
  api('chrome.browserAction.setPopup')?.addPromiseWrapper();
  api('chrome.browserAction.setTitle')?.addPromiseWrapper();
}

if (chrome.browsingData != null) {
  api('chrome.browsingData.remove')?.addPromiseWrapper();
  api('chrome.browsingData.removeAppcache')?.addPromiseWrapper();
  api('chrome.browsingData.removeCache')?.addPromiseWrapper();
  api('chrome.browsingData.removeCacheStorage')?.addPromiseWrapper();
  api('chrome.browsingData.removeCookies')?.addPromiseWrapper();
  api('chrome.browsingData.removeDownloads')?.addPromiseWrapper();
  api('chrome.browsingData.removeFileSystems')?.addPromiseWrapper();
  api('chrome.browsingData.removeFormData')?.addPromiseWrapper();
  api('chrome.browsingData.removeHistory')?.addPromiseWrapper();
  api('chrome.browsingData.removeIndexedDB')?.addPromiseWrapper();
  api('chrome.browsingData.removeLocalStorage')?.addPromiseWrapper();
  api('chrome.browsingData.removePasswords')?.addPromiseWrapper();
  api('chrome.browsingData.removePluginData')?.addPromiseWrapper();
  api('chrome.browsingData.removeServiceWorkers')?.addPromiseWrapper();
  api('chrome.browsingData.removeWebSQL')?.addPromiseWrapper();
  api('chrome.browsingData.settings')?.addPromiseWrapper();
}

if ((chrome as any).certificateProvider != null) {
  // ChromeOS only.
  api('chrome.certificateProvider.reportSignature')?.addPromiseWrapper();
  api('chrome.certificateProvider.requestPin')?.addPromiseWrapper();
  api('chrome.certificateProvider.setCertificates')?.addPromiseWrapper();
  api('chrome.certificateProvider.stopPinRequest')?.addPromiseWrapper();
}

if (chrome.commands != null) {
  api('chrome.commands.getAll')?.addPromiseWrapper();
}

if (chrome.contentSettings != null) {
  api('chrome.contentSettings.automaticDownloads.clear')?.addPromiseWrapper();
  api('chrome.contentSettings.automaticDownloads.get')?.addPromiseWrapper();
  api('chrome.contentSettings.automaticDownloads.getResourceIdentifiers')?.addPromiseWrapper();
  api('chrome.contentSettings.automaticDownloads.set')?.addPromiseWrapper();

  // Chrome v113+
  api('chrome.contentSettings.autoVerify.clear')?.addPromiseWrapper();
  api('chrome.contentSettings.autoVerify.get')?.addPromiseWrapper();
  api('chrome.contentSettings.autoVerify.getResourceIdentifiers')?.addPromiseWrapper();
  api('chrome.contentSettings.autoVerify.set')?.addPromiseWrapper();

  api('chrome.contentSettings.camera.clear')?.addPromiseWrapper();
  api('chrome.contentSettings.camera.get')?.addPromiseWrapper();
  api('chrome.contentSettings.camera.getResourceIdentifiers')?.addPromiseWrapper();
  api('chrome.contentSettings.camera.set')?.addPromiseWrapper();

  api('chrome.contentSettings.cookies.clear')?.addPromiseWrapper();
  api('chrome.contentSettings.cookies.get')?.addPromiseWrapper();
  api('chrome.contentSettings.cookies.getResourceIdentifiers')?.addPromiseWrapper();
  api('chrome.contentSettings.cookies.set')?.addPromiseWrapper();

  api('chrome.contentSettings.fullscreen.clear')?.addPromiseWrapper();
  api('chrome.contentSettings.fullscreen.get')?.addPromiseWrapper();
  api('chrome.contentSettings.fullscreen.getResourceIdentifiers')?.addPromiseWrapper();
  api('chrome.contentSettings.fullscreen.set')?.addPromiseWrapper();

  api('chrome.contentSettings.images.clear')?.addPromiseWrapper();
  api('chrome.contentSettings.images.get')?.addPromiseWrapper();
  api('chrome.contentSettings.images.getResourceIdentifiers')?.addPromiseWrapper();
  api('chrome.contentSettings.images.set')?.addPromiseWrapper();

  api('chrome.contentSettings.javascript.clear')?.addPromiseWrapper();
  api('chrome.contentSettings.javascript.get')?.addPromiseWrapper();
  api('chrome.contentSettings.javascript.getResourceIdentifiers')?.addPromiseWrapper();
  api('chrome.contentSettings.javascript.set')?.addPromiseWrapper();

  api('chrome.contentSettings.location.clear')?.addPromiseWrapper();
  api('chrome.contentSettings.location.get')?.addPromiseWrapper();
  api('chrome.contentSettings.location.getResourceIdentifiers')?.addPromiseWrapper();
  api('chrome.contentSettings.location.set')?.addPromiseWrapper();

  api('chrome.contentSettings.microphone.clear')?.addPromiseWrapper();
  api('chrome.contentSettings.microphone.get')?.addPromiseWrapper();
  api('chrome.contentSettings.microphone.getResourceIdentifiers')?.addPromiseWrapper();
  api('chrome.contentSettings.microphone.set')?.addPromiseWrapper();

  api('chrome.contentSettings.mouselock.clear')?.addPromiseWrapper();
  api('chrome.contentSettings.mouselock.get')?.addPromiseWrapper();
  api('chrome.contentSettings.mouselock.getResourceIdentifiers')?.addPromiseWrapper();
  api('chrome.contentSettings.mouselock.set')?.addPromiseWrapper();

  api('chrome.contentSettings.notifications.clear')?.addPromiseWrapper();
  api('chrome.contentSettings.notifications.get')?.addPromiseWrapper();
  api('chrome.contentSettings.notifications.getResourceIdentifiers')?.addPromiseWrapper();
  api('chrome.contentSettings.notifications.set')?.addPromiseWrapper();

  api('chrome.contentSettings.plugins.clear')?.addPromiseWrapper();
  api('chrome.contentSettings.plugins.get')?.addPromiseWrapper();
  api('chrome.contentSettings.plugins.getResourceIdentifiers')?.addPromiseWrapper();
  api('chrome.contentSettings.plugins.set')?.addPromiseWrapper();

  api('chrome.contentSettings.popups.clear')?.addPromiseWrapper();
  api('chrome.contentSettings.popups.get')?.addPromiseWrapper();
  api('chrome.contentSettings.popups.getResourceIdentifiers')?.addPromiseWrapper();
  api('chrome.contentSettings.popups.set')?.addPromiseWrapper();

  api('chrome.contentSettings.unsandboxedPlugins.clear')?.addPromiseWrapper();
  api('chrome.contentSettings.unsandboxedPlugins.get')?.addPromiseWrapper();
  api('chrome.contentSettings.unsandboxedPlugins.getResourceIdentifiers')?.addPromiseWrapper();
  api('chrome.contentSettings.unsandboxedPlugins.set')?.addPromiseWrapper();
}

if (chrome.contextMenus != null) {
  api('chrome.contextMenus.create')?.addPromiseWrapper();
  api('chrome.contextMenus.remove')?.addPromiseWrapper();
  api('chrome.contextMenus.removeAll')?.addPromiseWrapper();
  api('chrome.contextMenus.update')?.addPromiseWrapper();
}

if (chrome.cookies != null) {
  api('chrome.cookies.get')?.addPromiseWrapper();
  api('chrome.cookies.getAll')?.addPromiseWrapper();
  api('chrome.cookies.getAllCookieStores')?.addPromiseWrapper();
  api('chrome.cookies.remove')?.addPromiseWrapper();
  api('chrome.cookies.set')?.addPromiseWrapper();
}

if (chrome.debugger != null) {
  api('chrome.debugger.attach')?.addPromiseWrapper();
  api('chrome.debugger.detach')?.addPromiseWrapper();
  api('chrome.debugger.getTargets')?.addPromiseWrapper();
  api('chrome.debugger.sendCommand')?.addPromiseWrapper();
}

if (chrome.declarativeNetRequest != null) {
  api('chrome.declarativeNetRequest.getAvailableStaticRuleCount')?.addPromiseWrapper();
  api('chrome.declarativeNetRequest.getDynamicRules')?.addPromiseWrapper();
  api('chrome.declarativeNetRequest.getEnabledRulesets')?.addPromiseWrapper();
  api('chrome.declarativeNetRequest.getMatchedRules')?.addPromiseWrapper();
  api('chrome.declarativeNetRequest.getSessionRules')?.addPromiseWrapper();
  api('chrome.declarativeNetRequest.isRegexSupported')?.addPromiseWrapper();
  api('chrome.declarativeNetRequest.setExtensionActionOptions')?.addPromiseWrapper();
  api('chrome.declarativeNetRequest.testMatchOutcome')?.addPromiseWrapper();
  api('chrome.declarativeNetRequest.updateDynamicRules')?.addPromiseWrapper();
  api('chrome.declarativeNetRequest.updateEnabledRulesets')?.addPromiseWrapper();
  api('chrome.declarativeNetRequest.updateSessionRules')?.addPromiseWrapper();
}

if (chrome.desktopCapture != null) {
  // This function has multiple callback arguments and there's no official promise version yet:
  api('chrome.desktopCapture.chooseDesktopMedia')?.addPromiseWrapper();
}

if (chrome.documentScan != null) {
  api('chrome.documentScan.scan')?.addPromiseWrapper();
}

if (chrome.downloads != null) {
  api('chrome.downloads.acceptDanger')?.addPromiseWrapper();
  api('chrome.downloads.cancel')?.addPromiseWrapper();
  api('chrome.downloads.download')?.addPromiseWrapper();
  api('chrome.downloads.erase')?.addPromiseWrapper();
  api('chrome.downloads.getFileIcon')?.addPromiseWrapper();
  api('chrome.downloads.pause')?.addPromiseWrapper();
  api('chrome.downloads.removeFile')?.addPromiseWrapper();
  api('chrome.downloads.resume')?.addPromiseWrapper();
  api('chrome.downloads.search')?.addPromiseWrapper();
  api('chrome.downloads.setUiOptions')?.addPromiseWrapper();
}

if (chrome.enterprise?.deviceAttributes != null) {
  // ChromeOS only
  api('chrome.enterprise.deviceAttributes.getDeviceAnnotatedLocation')?.addPromiseWrapper();
  api('chrome.enterprise.deviceAttributes.getDeviceAssetId')?.addPromiseWrapper();
  api('chrome.enterprise.deviceAttributes.getDeviceHostname')?.addPromiseWrapper();
  api('chrome.enterprise.deviceAttributes.getDeviceSerialNumber')?.addPromiseWrapper();
  api('chrome.enterprise.deviceAttributes.getDirectoryDeviceId')?.addPromiseWrapper();
}

if ((chrome as any).enterprise?.hardwarePlatform != null) {
  api('chrome.enterprise.hardwarePlatform.getHardwarePlatformInfo')?.addPromiseWrapper();
}

if (chrome.enterprise?.networkingAttributes != null) {
  api('chrome.enterprise.networkingAttributes.getNetworkDetails')?.addPromiseWrapper();
}

if (chrome.enterprise?.platformKeys != null) {
  api('chrome.enterprise.platformKeys.challengeKey')?.addPromiseWrapper();
  api('chrome.enterprise.platformKeys.challengeMachineKey')?.addPromiseWrapper();
  api('chrome.enterprise.platformKeys.challengeUserKey')?.addPromiseWrapper();
  api('chrome.enterprise.platformKeys.getCertificates')?.addPromiseWrapper();
  api('chrome.enterprise.platformKeys.getTokens')?.addPromiseWrapper();
  api('chrome.enterprise.platformKeys.importCertificate')?.addPromiseWrapper();
  api('chrome.enterprise.platformKeys.removeCertificate')?.addPromiseWrapper();
}

if (chrome.extension != null) {
  api('chrome.extension.isAllowedFileSchemeAccess')?.addPromiseWrapper();
  api('chrome.extension.isAllowedIncognitoAccess')?.addPromiseWrapper();
}

if (chrome.fileSystemProvider != null) {
  api('chrome.fileSystemProvider.get')?.addPromiseWrapper();
  api('chrome.fileSystemProvider.getAll')?.addPromiseWrapper();
  api('chrome.fileSystemProvider.mount')?.addPromiseWrapper();
  api('chrome.fileSystemProvider.notify')?.addPromiseWrapper();
  api('chrome.fileSystemProvider.unmount')?.addPromiseWrapper();
}

if (chrome.fontSettings != null) {
  api('chrome.fontSettings.clearDefaultFixedFontSize')?.addPromiseWrapper();
  api('chrome.fontSettings.clearDefaultFontSize')?.addPromiseWrapper();
  api('chrome.fontSettings.clearFont')?.addPromiseWrapper();
  api('chrome.fontSettings.clearMinimumFontSize')?.addPromiseWrapper();
  api('chrome.fontSettings.getDefaultFixedFontSize')?.addPromiseWrapper();
  api('chrome.fontSettings.getDefaultFontSize')?.addPromiseWrapper();
  api('chrome.fontSettings.getFont')?.addPromiseWrapper();
  api('chrome.fontSettings.getFontList')?.addPromiseWrapper();
  api('chrome.fontSettings.getMinimumFontSize')?.addPromiseWrapper();
  api('chrome.fontSettings.setDefaultFixedFontSize')?.addPromiseWrapper();
  api('chrome.fontSettings.setDefaultFontSize')?.addPromiseWrapper();
  api('chrome.fontSettings.setFont')?.addPromiseWrapper();
  api('chrome.fontSettings.setMinimumFontSize')?.addPromiseWrapper();
}

if (chrome.gcm != null) {
  api('chrome.gcm.register')?.addPromiseWrapper();
  api('chrome.gcm.unregister')?.addPromiseWrapper();
  api('chrome.gcm.send')?.addPromiseWrapper();
}

if (chrome.history != null) {
  api('chrome.history.addUrl')?.addPromiseWrapper();
  api('chrome.history.deleteAll')?.addPromiseWrapper();
  api('chrome.history.deleteRange')?.addPromiseWrapper();
  api('chrome.history.deleteUrl')?.addPromiseWrapper();
  api('chrome.history.getVisits')?.addPromiseWrapper();
  api('chrome.history.search')?.addPromiseWrapper();
}

if (chrome.i18n != null) {
  api('chrome.i18n.detectLanguage')?.addPromiseWrapper();
  api('chrome.i18n.getAcceptLanguages')?.addPromiseWrapper();
}

if (chrome.identity != null) {
  api('chrome.identity.clearAllCachedAuthTokens')?.addPromiseWrapper();
  // Combine the two callback arguments into one object which the promise can resolve.
  api('chrome.identity.getAuthToken')?.addPromiseWrapper((token, grantedScopes) => ({
    token,
    grantedScopes,
  }));
  api('chrome.identity.getProfileUserInfo')?.addPromiseWrapper();
  api('chrome.identity.launchWebAuthFlow')?.addPromiseWrapper();
  api('chrome.identity.removeCachedAuthToken')?.addPromiseWrapper();
}

if (chrome.idle != null) {
  api('chrome.idle.getAutoLockDelay')?.addPromiseWrapper(); // <- ChromeOS only.
  api('chrome.idle.queryState')?.addPromiseWrapper();
}

if (chrome.input?.ime != null) {
  api('chrome.input.ime.clearComposition')?.addPromiseWrapper();
  api('chrome.input.ime.commitText')?.addPromiseWrapper();
  api('chrome.input.ime.deleteSurroundingText')?.addPromiseWrapper();
  api('chrome.input.ime.sendKeyEvents')?.addPromiseWrapper();
  api('chrome.input.ime.setAssistiveWindowButtonHighlighted')?.addPromiseWrapper();
  api('chrome.input.ime.setAssistiveWindowProperties')?.addPromiseWrapper();
  api('chrome.input.ime.setCandidates')?.addPromiseWrapper();
  api('chrome.input.ime.setCandidateWindowProperties')?.addPromiseWrapper();
  api('chrome.input.ime.setComposition')?.addPromiseWrapper();
  api('chrome.input.ime.setCursorPosition')?.addPromiseWrapper();
  api('chrome.input.ime.setMenuItems')?.addPromiseWrapper();
  api('chrome.input.ime.updateMenuItems')?.addPromiseWrapper();
}

if (chrome.loginState != null) {
  api('chrome.loginState.getProfileType')?.addPromiseWrapper();
  api('chrome.loginState.getSessionState')?.addPromiseWrapper();
}

// These management functions are always available.
api('chrome.management.getPermissionWarningsByManifest')?.addPromiseWrapper();
api('chrome.management.getSelf')?.addPromiseWrapper();
api('chrome.management.uninstallSelf')?.addPromiseWrapper();

if (chrome.management?.get != null) {
  // These management functions only exist if the management permission is present.
  api('chrome.management.createAppShortcut')?.addPromiseWrapper();
  api('chrome.management.generateAppForLink')?.addPromiseWrapper();
  api('chrome.management.get')?.addPromiseWrapper();
  api('chrome.management.getAll')?.addPromiseWrapper();
  api('chrome.management.getPermissionWarningsById')?.addPromiseWrapper();
  api('chrome.management.launchApp')?.addPromiseWrapper();
  api('chrome.management.setEnabled')?.addPromiseWrapper();
  api('chrome.management.setLaunchType')?.addPromiseWrapper();
  api('chrome.management.uninstall')?.addPromiseWrapper();
}

if (chrome.notifications != null) {
  api('chrome.notifications.clear')?.addPromiseWrapper();
  api('chrome.notifications.create')?.addPromiseWrapper();
  api('chrome.notifications.getAll')?.addPromiseWrapper();
  api('chrome.notifications.getPermissionLevel')?.addPromiseWrapper();
  api('chrome.notifications.update')?.addPromiseWrapper();
}

if (chrome.omnibox != null) {
  api('chrome.omnibox.setDefaultSuggestion')?.addPromiseWrapper();
}

if (chrome.pageCapture != null) {
  api('chrome.pageCapture.saveAsMHTML')?.addPromiseWrapper();
}

if (chrome.permissions != null) {
  api('chrome.permissions.contains')?.addPromiseWrapper();
  api('chrome.permissions.getAll')?.addPromiseWrapper();
  api('chrome.permissions.remove')?.addPromiseWrapper();
  api('chrome.permissions.request')?.addPromiseWrapper();
}

if (chrome.platformKeys != null) {
  // ChromeOS only
  api('chrome.platformKeys.getKeyPair')?.addPromiseWrapper(); // <-- callback has multiple arguments; there is no actual promise wrapper yet
  api('chrome.platformKeys.getKeyPairBySpki')?.addPromiseWrapper(); // <-- callback has multiple arguments; there is no actual promise wrapper yet
  api('chrome.platformKeys.selectClientCertificates')?.addPromiseWrapper();
  api('chrome.platformKeys.subtleCrypto')?.addPromiseWrapper();
  api('chrome.platformKeys.verifyTLSServerCertificate')?.addPromiseWrapper();
}

if (chrome.power != null) {
  // ChromeOS only
  api('chrome.power.releaseKeepAwake')?.addPromiseWrapper();

  // Chrome v113+ only
  api('chrome.power.reportActivity')?.addPromiseWrapper();
}

if ((chrome as any).printing != null) {
  // ChromeOS only
  api('chrome.printing.cancelJob')?.addPromiseWrapper();
  api('chrome.printing.getPrinterInfo')?.addPromiseWrapper();
  api('chrome.printing.getPrinters')?.addPromiseWrapper();
  api('chrome.printing.submitJob')?.addPromiseWrapper();
}

if ((chrome as any).printingMetrics != null) {
  // ChromeOS only
  api('chrome.printingMetrics.getPrintJobs')?.addPromiseWrapper();
}

if (chrome.privacy != null) {
  api('chrome.privacy.network.networkPredictionEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.network.networkPredictionEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.network.networkPredictionEnabled.set')?.addPromiseWrapper();

  api('chrome.privacy.network.webRTCIPHandlingPolicy.clear')?.addPromiseWrapper();
  api('chrome.privacy.network.webRTCIPHandlingPolicy.get')?.addPromiseWrapper();
  api('chrome.privacy.network.webRTCIPHandlingPolicy.set')?.addPromiseWrapper();

  api('chrome.privacy.services.alternateErrorPagesEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.services.alternateErrorPagesEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.services.alternateErrorPagesEnabled.set')?.addPromiseWrapper();

  api('chrome.privacy.services.autofillAddressEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.services.autofillAddressEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.services.autofillAddressEnabled.set')?.addPromiseWrapper();

  api('chrome.privacy.services.autofillCreditCardEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.services.autofillCreditCardEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.services.autofillCreditCardEnabled.set')?.addPromiseWrapper();

  api('chrome.privacy.services.autofillEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.services.autofillEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.services.autofillEnabled.set')?.addPromiseWrapper();

  api('chrome.privacy.services.passwordSavingEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.services.passwordSavingEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.services.passwordSavingEnabled.set')?.addPromiseWrapper();

  api('chrome.privacy.services.safeBrowsingEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.services.safeBrowsingEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.services.safeBrowsingEnabled.set')?.addPromiseWrapper();

  api('chrome.privacy.services.safeBrowsingExtendedReportingEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.services.safeBrowsingExtendedReportingEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.services.safeBrowsingExtendedReportingEnabled.set')?.addPromiseWrapper();

  api('chrome.privacy.services.searchSuggestEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.services.searchSuggestEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.services.searchSuggestEnabled.set')?.addPromiseWrapper();

  api('chrome.privacy.services.spellingServiceEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.services.spellingServiceEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.services.spellingServiceEnabled.set')?.addPromiseWrapper();

  api('chrome.privacy.services.translationServiceEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.services.translationServiceEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.services.translationServiceEnabled.set')?.addPromiseWrapper();

  // Chrome v111+
  api('chrome.privacy.websites.adMeasurementEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.websites.adMeasurementEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.websites.adMeasurementEnabled.set')?.addPromiseWrapper();

  api('chrome.privacy.websites.doNotTrackEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.websites.doNotTrackEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.websites.doNotTrackEnabled.set')?.addPromiseWrapper();

  api('chrome.privacy.websites.fledgeEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.websites.fledgeEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.websites.fledgeEnabled.set')?.addPromiseWrapper();

  api('chrome.privacy.websites.hyperlinkAuditingEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.websites.hyperlinkAuditingEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.websites.hyperlinkAuditingEnabled.set')?.addPromiseWrapper();

  // Chrome v90+
  api('chrome.privacy.websites.privacySandboxEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.websites.privacySandboxEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.websites.privacySandboxEnabled.set')?.addPromiseWrapper();

  // Windows and ChromeOS only
  api('chrome.privacy.websites.protectedContentEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.websites.protectedContentEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.websites.protectedContentEnabled.set')?.addPromiseWrapper();

  api('chrome.privacy.websites.referrersEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.websites.referrersEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.websites.referrersEnabled.set')?.addPromiseWrapper();

  api('chrome.privacy.websites.thirdPartyCookiesAllowed.clear')?.addPromiseWrapper();
  api('chrome.privacy.websites.thirdPartyCookiesAllowed.get')?.addPromiseWrapper();
  api('chrome.privacy.websites.thirdPartyCookiesAllowed.set')?.addPromiseWrapper();

  // Chrome v111+
  api('chrome.privacy.websites.topicsEnabled.clear')?.addPromiseWrapper();
  api('chrome.privacy.websites.topicsEnabled.get')?.addPromiseWrapper();
  api('chrome.privacy.websites.topicsEnabled.set')?.addPromiseWrapper();
}

if (chrome.proxy != null) {
  api('chrome.proxy.settings.clear')?.addPromiseWrapper();
  api('chrome.proxy.settings.get')?.addPromiseWrapper();
  api('chrome.proxy.settings.set')?.addPromiseWrapper();
}

// The runtime APIs are always available.
api('chrome.runtime.getBackgroundPage')?.addPromiseWrapper();
api('chrome.runtime.getPackageDirectoryEntry')?.addPromiseWrapper();
api('chrome.runtime.getPlatformInfo')?.addPromiseWrapper();
api('chrome.runtime.openOptionsPage')?.addPromiseWrapper();
api('chrome.runtime.requestUpdateCheck')?.addPromiseWrapper();
api('chrome.runtime.restartAfterDelay')?.addPromiseWrapper();
api('chrome.runtime.sendMessage')?.addPromiseWrapper();
api('chrome.runtime.sendNativeMessage')?.addPromiseWrapper();
api('chrome.runtime.setUninstallURL')?.addPromiseWrapper();

if (chrome.scripting != null) {
  api('chrome.scripting.executeScript')?.addPromiseWrapper();
  api('chrome.scripting.getRegisteredContentScripts')?.addPromiseWrapper();
  api('chrome.scripting.insertCSS')?.addPromiseWrapper();
  api('chrome.scripting.registerContentScripts')?.addPromiseWrapper();
  api('chrome.scripting.removeCSS')?.addPromiseWrapper();
  api('chrome.scripting.unregisterContentScripts')?.addPromiseWrapper();
  api('chrome.scripting.updateContentScripts')?.addPromiseWrapper();
}

if (chrome.search != null) {
  api('chrome.search.query')?.addPromiseWrapper();
}

if (chrome.sessions != null) {
  api('chrome.sessions.getDevices')?.addPromiseWrapper();
  api('chrome.sessions.getRecentlyClosed')?.addPromiseWrapper();
  api('chrome.sessions.restore')?.addPromiseWrapper();
}

if (chrome.storage != null) {
  api('chrome.storage.local.getBytesInUse')?.addPromiseWrapper();
  api('chrome.storage.local.clear')?.addPromiseWrapper();
  api('chrome.storage.local.set')?.addPromiseWrapper();
  api('chrome.storage.local.remove')?.addPromiseWrapper();
  api('chrome.storage.local.get')?.addPromiseWrapper();

  api('chrome.storage.managed.getBytesInUse')?.addPromiseWrapper();
  api('chrome.storage.managed.clear')?.addPromiseWrapper();
  api('chrome.storage.managed.set')?.addPromiseWrapper();
  api('chrome.storage.managed.remove')?.addPromiseWrapper();
  api('chrome.storage.managed.get')?.addPromiseWrapper();

  api('chrome.storage.sync.getBytesInUse')?.addPromiseWrapper();
  api('chrome.storage.sync.clear')?.addPromiseWrapper();
  api('chrome.storage.sync.set')?.addPromiseWrapper();
  api('chrome.storage.sync.remove')?.addPromiseWrapper();
  api('chrome.storage.sync.get')?.addPromiseWrapper();

  // Note: chrome.storage.session doesn't exist in mv2.
}

if (chrome.system?.cpu != null) {
  api('chrome.system.cpu.getInfo')?.addPromiseWrapper();
}

if (chrome.system?.display != null) {
  api('chrome.system.display.getDisplayLayout')?.addPromiseWrapper();
  api('chrome.system.display.getInfo')?.addPromiseWrapper();
  api('chrome.system.display.setDisplayLayout')?.addPromiseWrapper();
  api('chrome.system.display.setDisplayProperties')?.addPromiseWrapper();
  api('chrome.system.display.setMirrorMode')?.addPromiseWrapper();
  api('chrome.system.display.showNativeTouchCalibration')?.addPromiseWrapper();
}

if (chrome.system?.memory != null) {
  api('chrome.system.memory.getInfo')?.addPromiseWrapper();
}

if (chrome.system?.storage != null) {
  api('chrome.system.storage.ejectDevice')?.addPromiseWrapper();
  api('chrome.system.storage.getInfo')?.addPromiseWrapper();
}

if (chrome.tabCapture != null) {
  api('chrome.tabCapture.capture')?.addPromiseWrapper();
  api('chrome.tabCapture.getCapturedTabs')?.addPromiseWrapper();
  api('chrome.tabCapture.getMediaStreamId')?.addPromiseWrapper();
}

if (chrome.tabGroups != null) {
  api('chrome.tabGroups.get')?.addPromiseWrapper();
  api('chrome.tabGroups.move')?.addPromiseWrapper();
  api('chrome.tabGroups.query')?.addPromiseWrapper();
  api('chrome.tabGroups.update')?.addPromiseWrapper();
}

if (chrome.tabs != null) {
  api('chrome.tabs.executeScript')?.addPromiseWrapper();
  api('chrome.tabs.get')?.addPromiseWrapper();
  api('chrome.tabs.getAllInWindow')?.addPromiseWrapper();
  api('chrome.tabs.getCurrent')?.addPromiseWrapper();
  api('chrome.tabs.getSelected')?.addPromiseWrapper();
  api('chrome.tabs.create')?.addPromiseWrapper();
  api('chrome.tabs.move')?.addPromiseWrapper();
  api('chrome.tabs.update')?.addPromiseWrapper();
  api('chrome.tabs.remove')?.addPromiseWrapper();
  api('chrome.tabs.captureVisibleTab')?.addPromiseWrapper();
  api('chrome.tabs.reload')?.addPromiseWrapper();
  api('chrome.tabs.duplicate')?.addPromiseWrapper();
  api('chrome.tabs.insertCSS')?.addPromiseWrapper();
  api('chrome.tabs.highlight')?.addPromiseWrapper();
  api('chrome.tabs.query')?.addPromiseWrapper();
  api('chrome.tabs.detectLanguage')?.addPromiseWrapper();
  api('chrome.tabs.getZoom')?.addPromiseWrapper();
  api('chrome.tabs.getZoomSettings')?.addPromiseWrapper();
  api('chrome.tabs.discard')?.addPromiseWrapper();
  api('chrome.tabs.goForward')?.addPromiseWrapper();
  api('chrome.tabs.goBack')?.addPromiseWrapper();
  api('chrome.tabs.group')?.addPromiseWrapper();
  api('chrome.tabs.ungroup')?.addPromiseWrapper();
}

if (chrome.topSites != null) {
  api('chrome.topSites.get')?.addPromiseWrapper();
}

if (chrome.tts != null) {
  api('chrome.tts.getVoices')?.addPromiseWrapper();
  api('chrome.tts.isSpeaking')?.addPromiseWrapper();
  api('chrome.tts.speak')?.addPromiseWrapper();
}

if (chrome.vpnProvider != null) {
  // ChromeOS only
  api('chrome.vpnProvider.createConfig')?.addPromiseWrapper();
  api('chrome.vpnProvider.destroyConfig')?.addPromiseWrapper();
  api('chrome.vpnProvider.notifyConnectionStateChanged')?.addPromiseWrapper();
  api('chrome.vpnProvider.sendPacket')?.addPromiseWrapper();
  api('chrome.vpnProvider.setParameters')?.addPromiseWrapper();
}

if (chrome.wallpaper != null) {
  // ChromeOS only
  api('chrome.wallpaper.setWallpaper')?.addPromiseWrapper();
}

if (chrome.webRequest != null) {
  api('chrome.webRequest.handlerBehaviorChanged')?.addPromiseWrapper();
}

if (chrome.windows != null) {
  api('chrome.windows.create')?.addPromiseWrapper();
  api('chrome.windows.get')?.addPromiseWrapper();
  api('chrome.windows.getAll')?.addPromiseWrapper();
  api('chrome.windows.getCurrent')?.addPromiseWrapper();
  api('chrome.windows.getLastFocused')?.addPromiseWrapper();
  api('chrome.windows.remove')?.addPromiseWrapper();
  api('chrome.windows.update')?.addPromiseWrapper();
}

// -------------------------------------------------------------------------------------------------
// Aliases.

// Create aliases for functions which changed names between mv2 and mv3.
// These should be added after the promise wrappers above to ensure the correct items are aliased.
if (chrome.browserAction != null) {
  api('chrome.browserAction.disable')?.addAlias('chrome.action.disable');
  api('chrome.browserAction.enable')?.addAlias('chrome.action.enable');
  api('chrome.browserAction.getBadgeBackgroundColor')?.addAlias(
    'chrome.action.getBadgeBackgroundColor',
  );
  api('chrome.browserAction.getBadgeText')?.addAlias('chrome.action.getBadgeText');
  api('chrome.browserAction.getPopup')?.addAlias('chrome.action.getPopup');
  api('chrome.browserAction.getTitle')?.addAlias('chrome.action.getTitle');
  api('chrome.browserAction.setBadgeBackgroundColor')?.addAlias(
    'chrome.action.setBadgeBackgroundColor',
  );
  api('chrome.browserAction.setBadgeText')?.addAlias('chrome.action.setBadgeText');
  api('chrome.browserAction.setIcon')?.addAlias('chrome.action.setIcon');
  api('chrome.browserAction.setPopup')?.addAlias('chrome.action.setPopup');
  api('chrome.browserAction.setTitle')?.addAlias('chrome.action.setTitle');
}

// Note: chrome.pageAction no longer exists in mv3.

// -------------------------------------------------------------------------------------------------
// Stubs.

api('chrome.action.getBadgeTextColor')?.addStub([0, 0, 0, 1]);
api('chrome.action.getUserSettings')?.addStub({ isOnToolbar: false });
api('chrome.action.isEnabled')?.addStub(true);
api('chrome.action.openPopup')?.addStub();
api('chrome.action.setBadgeTextColor')?.addStub();

api('chrome.system.storage.getAvailableCapacity')?.addPromiseWrapper();

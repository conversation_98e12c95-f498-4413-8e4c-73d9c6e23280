/**
 * Represents a named item (usually a function) in the Chrome extension API.
 * This provides properties to access the current and original values, and modify the current value.
 * An instance of this class can only be created if the enclosing namespace hierarchy actually
 *  exists. However, the API entry itself doesn't need to exist.
 *
 * Do not construct this directly. Call ApiEntry.find() instead, or use the api() helper below.
 */
export default class ApiEntry {
  /**
   * Construct a new instance referring to the specified API entry.
   * Do not construct this object directly. Call ApiEntry.find() instead.
   *
   * @param fullName The fully qualified name of the API entry, including all levels of the
   *  namespace. and the name of the actual API item (function etc). For example, this could be
   *  "chrome.storage.local.get". This is only stored for information and logging purposes. It
   *  should be have been parsed and processed by the caller.
   * @param parent A reference to the object containing the named API entry. For example, if the
   *  full name is "chrome.storage.local.get", then this will be the object storage at
   *  "chrome.storage.local". It must exist.
   * @param name The unqualified name of the API item; this is typically the function name. For
   *  example, if the full name is "chrome.storage.local.get", then this name would be "get".
   */
  private constructor(fullName: string, parent: object, targetName: string) {
    this.fullName = fullName;
    this.parent = parent;
    this.name = targetName;
    this.original = this.value;
  }

  /**
   * Parse a fully qualified name to locate the corresponding API entry.
   *
   * @param fullName The fully qualified name of the API entry, including all namespace levels. For
   *  example, "chrome.storage.local.get".
   * @param createMissingNamespaces If true, any missing namespaces will be created. This will not
   *  create the actual API entry though.
   * @returns If all the namespace levels exist and are valid objects, or they didn't exist and were
   *  created, then this returns an object referencing the named API entry. Otherwise, returns
   *  undefined.
   */
  public static readonly find = (
    fullName: string,
    createMissingNamespaces: boolean = false,
  ): ApiEntry | undefined => {
    if (fullName === '') {
      throw new Error('The fully qualified name of an API entry cannot be an empty string.');
    }

    const parentPath = fullName.split('.');
    const name = parentPath.pop() as string;

    // Recursively descend through scope levels until we get to the object containing the target.
    let parent: any = this._global;
    for (const part of parentPath) {
      // Ensure this level of the hierarchy exists and is an object.
      if (Object.prototype.hasOwnProperty.call(parent, part)) {
        if (typeof parent[part] !== 'object') {
          console.warn(`mv2-shim: Invalid API path: "${fullName}". "${part}" is not an object.`);
          return undefined;
        }
      } else if (createMissingNamespaces) {
        parent[part] = {};
      } else {
        return undefined;
      }

      parent = parent[part];
    }

    return new ApiEntry(fullName, parent, name);
  };

  /**
   * Replace a callback-based API function with a dynamic promise wrapper.
   * The function will continue to work as it originally did if a callback parameter is provided
   *  when called. Otherwise, it will return a promise instead. This allows mv2 API functions to be
   *  called as though they were the mv3 equivalents.
   *
   * @param resultsProcessor An optional function which takes the arguments passed to the callback
   *  and condenses them to a single argument, e.g. by putting them into an object. This is useful
   *  for APIs which have multiple result values. A promise can only resolve to a single value, so
   *  those functions need special wrapper handling. If this function is not specified then the
   *  first argument in the callback is passed to the promise.
   */
  public readonly addPromiseWrapper = (resultsProcessor?: (...results: any[]) => any): void => {
    if (this.original == null) {
      // The function may not be defined if it isn't applicable to this platform, or we don't have
      //  the appropriate permission or deployment method.
      console.debug('mv2-shim: Skipping promise wrapper. Named function not found.', this.fullName);
      return;
    }

    if (typeof this.original !== 'function') {
      console.warn(
        'mv2-shim: Cannot create a promise wrapper. Named item is not a function.',
        this.fullName,
      );
      return;
    }

    // Replace the actual function with a wrapper.
    this.value = (...args: any[]): any => {
      // If the last argument is a function, then we'll assume it's a callback. The caller isn't
      //  expecting a promisified function, so pass the call through unmodified.
      if (args.length > 0 && typeof args[args.length - 1] === 'function') {
        return this.original.call(this.parent, ...args);
      }

      // Add an implicit callback and promise wrapper.
      return new Promise<any>((resolve, reject) => {
        this.original.call(this.parent, ...args, (...results: any[]) => {
          if (chrome.runtime.lastError != null) {
            reject(chrome.runtime.lastError);
            return;
          }

          if (resultsProcessor !== undefined) {
            resolve(resultsProcessor(results));
            return;
          }

          if (results.length === 0) {
            resolve(undefined);
            return;
          }

          if (results.length > 1) {
            console.warn(
              'shim: Promise wrapper cannot resolve multiple results automatically. ' +
                'A results processor function is required.',
              this.fullName,
            );
          }

          resolve(results[0]);
        });
      });
    };
  };

  /**
   * Create a reference to this API entry in another location.
   * This is useful where an API exists in both mv2 and mv3, but under different names/locations.
   * Note: The alias points to the current value of this API entry, at the time this function is
   *  called. That means if you added a promise wrapper first then the alias will point to the
   *  promise wrapper.
   *
   * @param fullAlias The fully qualified name of the API location at which the alias should be
   *  created.
   */
  public readonly addAlias = (fullAlias: string): void => {
    if (this.original == null) {
      console.debug(`shim: Skipping alias. Original API entry does not exist.`, this.fullName);
      return;
    }

    const aliasEntry = ApiEntry.find(fullAlias, true);
    if (aliasEntry === undefined) {
      console.debug('shim: Skipping alias. Alias does not specify a valid location.', fullAlias);
      return;
    }

    if (aliasEntry.value != null) {
      console.warn(
        'shim: Skipping alias. A value already exists at the alias location.',
        fullAlias,
      );
    }

    aliasEntry.value = this.value;
  };

  /**
   * Create a non-functional stub in this API entry.
   * This is used for functionality which exists in mv3 but isn't available at all in mv2. When
   *  invoked on mv2, the stub won't do anything.
   * This will do nothing if this API entry already exists.
   *
   * @param result The value which should be returned by the stub function.
   * @param logWarning If true, a console warning will be logged the first time the stub is called.
   * @param isAsync If true, an asynchronous stub function will be created. The result value will be
   *  passed to a callback or returned in a resolved promise, depending on how the stub is called.
   *  If false, the stub function will be synchronous; i.e. it will return the result directly.
   */
  public readonly addStub = (
    result: any = undefined,
    logWarning: boolean = false,
    isAsync: boolean = true,
  ): void => {
    if (this.value != null) {
      console.warn(
        'shim: Skipping stub. A value already exists at the API location.',
        this.fullName,
      );
      return;
    }

    this.value = (...args: []): any => {
      // Optionally log a warning the first time this is called.
      if (logWarning && !ApiEntry.stubWarningsLogged.has(this.fullName)) {
        console.warn(
          'shim: Extension called a stubbed API which is not available in mv2.',
          this.fullName,
        );
        ApiEntry.stubWarningsLogged.add(this.fullName);
      }

      if (!isAsync) {
        return result;
      }

      // If a function was passed as the last argument then treat is as a callback.
      // Otherwise, return a promise.
      if (args.length > 0 && typeof args[args.length - 1] === 'function') {
        // eslint-disable-next-line @typescript-eslint/ban-types
        const callback = args[args.length - 1] as Function;
        callback(result);
        return;
      }

      return Promise.resolve(result);
    };
  };

  /**
   * Get the current value of the API entry.
   */
  public get value(): any {
    return (this.parent as any)[this.name];
  }

  /**
   * Update the current value of the API entry.
   * Any code which subsequently uses the API by its original path will see this value instead.
   */
  public set value(newValue: any) {
    (this.parent as any)[this.name] = newValue;
  }

  /**
   * The fully qualified name of the API entry, including all scope levels.
   * This is only stored for information and logging purposes.
   */
  public readonly fullName: string;

  /**
   * The object containing the API entry.
   * For example, if the full name is "chrome.storage.local.get", then this will store a reference
   *  to the object at "chrome.storage.local".
   */
  public readonly parent: object;

  /**
   * The unqualified name of the API entry.
   * For example, if the full name is "chrome.storage.local.get", then this will store the string
   *  "get".
   */
  public readonly name: string;

  /**
   * Contains the original value of the API entry at the time this object was constructed.
   */
  public readonly original: any;

  /**
   * This will contain the fully qualified names of all the API stubs which we've logged warnings for.
   * This is used to prevent us logging them multiple times.
   */
  public static readonly stubWarningsLogged = new Set<string>();

  /**
   * A reference to the appropriate global object which we can used to modify global variables.
   * On very old versions of Chrome (e.g. v64), "globalThis" doesn't exist, and the standard
   * polyfills haven't had a chance to run by this stage. We need to explicitly fall-back on the
   *  window object instead, without a triggering a "not defined" error.
   */
  private static readonly _global = typeof globalThis !== 'undefined' ? globalThis : window;
}

export const api = (
  fullName: string,
  createMissingNamespaces: boolean = false,
): ApiEntry | undefined => {
  return ApiEntry.find(fullName, createMissingNamespaces);
};

{"weightedphrases": [{"op": "remove", "path": "/extrememath"}, {"op": "add", "path": "/حركات الإباحية العربية", "value": {"catsAndScores": [{"category": 39, "score": "25"}], "children": []}}, {"op": "add", "path": "/bitlife", "value": {"catsAndScores": [], "children": [{"catsAndScores": {"category": 19, "score": "25"}, "phrases": ["life simulator"]}, {"catsAndScores": {"category": 19, "score": "25"}, "phrases": ["candywriter"]}]}}, {"op": "add", "path": "/بزازها الكبيرة", "value": {"catsAndScores": [{"category": 39, "score": "25"}], "children": []}}, {"op": "move", "from": "/featured games", "path": "/run of life"}, {"op": "add", "path": "/solar smash", "value": {"catsAndScores": [{"category": 19, "score": "10"}], "children": []}}, {"op": "add", "path": "/heardle diplo", "value": {"catsAndScores": [{"category": 19, "score": "10"}], "children": []}}, {"op": "add", "path": "/elite2site", "value": {"catsAndScores": [{"category": 120, "score": "25"}], "children": []}}, {"op": "add", "path": "/god simulator", "value": {"catsAndScores": [{"category": 19, "score": "10"}], "children": []}}, {"op": "add", "path": "/إباحية صلبة مجانية", "value": {"catsAndScores": [{"category": 39, "score": "25"}], "children": []}}, {"op": "add", "path": "/featured game", "value": {"catsAndScores": [{"category": 19, "score": "15"}], "children": []}}, {"op": "add", "path": "/mario heardle", "value": {"catsAndScores": [{"category": 19, "score": "5"}], "children": []}}, {"op": "add", "path": "/heardle 80s", "value": {"catsAndScores": [{"category": 19, "score": "15"}], "children": []}}, {"op": "add", "path": "/أنبوب إباحي", "value": {"catsAndScores": [{"category": 39, "score": "25"}], "children": []}}, {"op": "add", "path": "/~1shield~1", "value": {"catsAndScores": [], "children": [{"catsAndScores": {"category": 120, "score": "40"}, "phrases": ["website unblocker"]}]}}, {"op": "add", "path": "/life: the game", "value": {"catsAndScores": [{"category": 19, "score": "10"}], "children": []}}, {"op": "add", "path": "/الاباحية الصلبة الحرة", "value": {"catsAndScores": [{"category": 39, "score": "25"}], "children": []}}, {"op": "add", "path": "/فيديو إباحية", "value": {"catsAndScores": [{"category": 39, "score": "25"}], "children": []}}, {"op": "add", "path": "/heardle 50s", "value": {"catsAndScores": [{"category": 19, "score": "15"}], "children": []}}, {"op": "add", "path": "/hyper life", "value": {"catsAndScores": [{"category": 19, "score": "5"}], "children": []}}, {"op": "add", "path": "/bitlife game", "value": {"catsAndScores": [{"category": 19, "score": "20"}], "children": []}}, {"op": "add", "path": "/teacher simulator", "value": {"catsAndScores": [{"category": 19, "score": "10"}], "children": []}}, {"op": "add", "path": "/crossy road", "value": {"catsAndScores": [{"category": 19, "score": "10"}], "children": []}}, {"op": "add", "path": "/الجنس بقوة", "value": {"catsAndScores": [{"category": 39, "score": "25"}], "children": []}}, {"op": "add", "path": "/not my neighbor", "value": {"catsAndScores": [{"category": 19, "score": "10"}], "children": []}}, {"op": "add", "path": "/couteau de poche", "value": {"catsAndScores": [{"category": 181, "score": "20"}], "children": []}}, {"op": "add", "path": "/vortice", "value": {"catsAndScores": [{"category": 120, "score": "10"}], "children": [{"catsAndScores": {"category": 120, "score": "20"}, "phrases": ["frost", "ruby", "christmas"]}, {"catsAndScores": {"category": 120, "score": "20"}, "phrases": ["royal", "winter", "orange"]}, {"catsAndScores": {"category": 120, "score": "20"}, "phrases": ["night", "grass", "lemon"]}]}}, {"op": "add", "path": "/heardle kpop", "value": {"catsAndScores": [{"category": 19, "score": "10"}], "children": []}}, {"op": "add", "path": "/bitlife update", "value": {"catsAndScores": [{"category": 19, "score": "10"}], "children": []}}, {"op": "add", "path": "/spotlight game", "value": {"catsAndScores": [{"category": 19, "score": "15"}], "children": []}}, {"op": "add", "path": "/short life", "value": {"catsAndScores": [{"category": 19, "score": "5"}], "children": []}}, {"op": "add", "path": "/outil de coupe", "value": {"catsAndScores": [{"category": 181, "score": "10"}], "children": []}}, {"op": "add", "path": "/bitlife online", "value": {"catsAndScores": [{"category": 19, "score": "20"}], "children": []}}, {"op": "add", "path": "/الفيديو الإباحية", "value": {"catsAndScores": [{"category": 39, "score": "25"}], "children": []}}, {"op": "add", "path": "/doodle jump", "value": {"catsAndScores": [{"category": 19, "score": "5"}], "children": []}}, {"op": "add", "path": "/couteaux de poche", "value": {"catsAndScores": [{"category": 181, "score": "20"}], "children": []}}, {"op": "replace", "path": "/retro bowl/catsAndScores/0/score", "value": "10"}, {"op": "add", "path": "/apps/children/1", "value": {"catsAndScores": {"category": 19, "score": "50"}, "phrases": ["chatbox", "extrememath", "games"]}}, {"op": "replace", "path": "/proxy/children/0/catsAndScores/score", "value": "20"}, {"op": "add", "path": "/proxy/children/0/phrases/0", "value": "games"}, {"op": "add", "path": "/proxy/children/0/phrases/1", "value": "discord"}, {"op": "move", "from": "/proxy/children/0/phrases/2", "path": "/proxy/children/1/phrases/0"}, {"op": "replace", "path": "/proxy/children/2/catsAndScores/score", "value": "25"}, {"op": "remove", "path": "/proxy/children/2/phrases/0"}, {"op": "move", "from": "/proxy/children/1/phrases/1", "path": "/proxy/children/2/phrases/0"}, {"op": "add", "path": "/proxy/children/3", "value": {"catsAndScores": {"category": 120, "score": "35"}, "phrases": ["titaniumnetwork"]}}], "searchterms": [{"op": "add", "path": "/ bitlife ", "value": {"catsAndScores": [{"category": 19, "score": 100}], "children": []}}, {"op": "add", "path": "/fleshlight", "value": {"catsAndScores": [{"category": 11, "score": 100}], "children": []}}, {"op": "add", "path": "/erection", "value": {"catsAndScores": [{"category": 528, "score": 100}], "children": []}}], "category_data": [{"op": "replace", "path": "/3/component/domainsurls", "value": 37618}, {"op": "replace", "path": "/211/component/domainsurls", "value": 4006}, {"op": "replace", "path": "/235/component/domainsurls", "value": 39493}, {"op": "replace", "path": "/199/component/domainsurls", "value": 17461}, {"op": "replace", "path": "/23/component/domainsurls", "value": 1155}, {"op": "replace", "path": "/41/component/domainsurls", "value": 3036}, {"op": "replace", "path": "/747/component/domainsurls", "value": 16007}, {"op": "replace", "path": "/192/component/domainsurls", "value": 3777}, {"op": "replace", "path": "/754/component/domainsurls", "value": 4006}, {"op": "replace", "path": "/201/component/domainsurls", "value": 4085}, {"op": "replace", "path": "/752/component/domainsurls", "value": 26099}, {"op": "replace", "path": "/104/component/domainsurls", "value": 11952}, {"op": "replace", "path": "/19/component/weightedphrases", "value": 1640}, {"op": "replace", "path": "/19/component/searchterms", "value": 124}, {"op": "replace", "path": "/19/component/domainsurls", "value": 24940}, {"op": "replace", "path": "/213/component/domainsurls", "value": 61616}, {"op": "replace", "path": "/753/component/domainsurls", "value": 87438}, {"op": "replace", "path": "/20/component/domainsurls", "value": 5087}, {"op": "replace", "path": "/438/component/domainsurls", "value": 58474}, {"op": "replace", "path": "/214/component/domainsurls", "value": 971}, {"op": "replace", "path": "/230/component/domainsurls", "value": 170}, {"op": "replace", "path": "/1225/component/domainsurls", "value": 114615}, {"op": "replace", "path": "/56/component/domainsurls", "value": 9281}, {"op": "replace", "path": "/9/component/domainsurls", "value": 9675}, {"op": "replace", "path": "/182/component/domainsurls", "value": 7749}, {"op": "replace", "path": "/193/component/domainsurls", "value": 1479}, {"op": "replace", "path": "/237/component/domainsurls", "value": 58}, {"op": "replace", "path": "/549/component/domainsurls", "value": 1224}, {"op": "replace", "path": "/528/component/searchterms", "value": 1663}, {"op": "replace", "path": "/435/component/domainsurls", "value": 7026}, {"op": "replace", "path": "/13/component/domainsurls", "value": 17669}, {"op": "replace", "path": "/11/component/searchterms", "value": 105}, {"op": "replace", "path": "/11/component/domainsurls", "value": 589330}, {"op": "replace", "path": "/202/component/domainsurls", "value": 27963}, {"op": "replace", "path": "/2/component/domainsurls", "value": 19746}, {"op": "replace", "path": "/184/component/domainsurls", "value": 13811}, {"op": "replace", "path": "/6/component/domainsurls", "value": 1454}, {"op": "replace", "path": "/513/component/domainsurls", "value": 2703}, {"op": "replace", "path": "/218/component/domainsurls", "value": 2275}, {"op": "replace", "path": "/474/component/domainsurls", "value": 1651}, {"op": "replace", "path": "/114/component/domainsurls", "value": 23816}, {"op": "replace", "path": "/654/component/domainsurls", "value": 6949}, {"op": "replace", "path": "/44/component/domainsurls", "value": 2963}, {"op": "replace", "path": "/606/component/domainsurls", "value": 359}, {"op": "replace", "path": "/189/component/domainsurls", "value": 569}, {"op": "replace", "path": "/34/component/domainsurls", "value": 164442}, {"op": "replace", "path": "/511/component/domainsurls", "value": 1283}, {"op": "replace", "path": "/180/component/domainsurls", "value": 976}, {"op": "replace", "path": "/1238/component/domainsurls", "value": 6016}, {"op": "replace", "path": "/760/component/domainsurls", "value": 3036}, {"op": "replace", "path": "/30/component/domainsurls", "value": 2017}, {"op": "replace", "path": "/39/component/weightedphrases", "value": 15438}, {"op": "replace", "path": "/39/component/domainsurls", "value": 393913}, {"op": "replace", "path": "/7/component/domainsurls", "value": 149146}, {"op": "replace", "path": "/120/component/weightedphrases", "value": 1255}, {"op": "replace", "path": "/120/component/domainsurls", "value": 66123}, {"op": "replace", "path": "/212/component/domainsurls", "value": 2032}, {"op": "replace", "path": "/27/component/domainsurls", "value": 7840}, {"op": "replace", "path": "/53/component/domainsurls", "value": 157149}, {"op": "replace", "path": "/24/component/domainsurls", "value": 13490}, {"op": "replace", "path": "/761/component/domainsurls", "value": 5076}, {"op": "replace", "path": "/124/component/domainsurls", "value": 8276}, {"op": "replace", "path": "/242/component/domainsurls", "value": 154779}, {"op": "replace", "path": "/195/component/domainsurls", "value": 6705}, {"op": "replace", "path": "/755/component/domainsurls", "value": 137}, {"op": "replace", "path": "/43/component/domainsurls", "value": 140515}, {"op": "replace", "path": "/190/component/domainsurls", "value": 6856}, {"op": "replace", "path": "/187/component/domainsurls", "value": 7994}, {"op": "replace", "path": "/751/component/domainsurls", "value": 32537}, {"op": "replace", "path": "/759/component/domainsurls", "value": 8951}, {"op": "replace", "path": "/183/component/domainsurls", "value": 5597}, {"op": "replace", "path": "/18/component/domainsurls", "value": 2706}, {"op": "replace", "path": "/498/component/domainsurls", "value": 331}, {"op": "replace", "path": "/750/component/domainsurls", "value": 61}, {"op": "replace", "path": "/758/component/domainsurls", "value": 16714}, {"op": "replace", "path": "/244/component/domainsurls", "value": 232}, {"op": "replace", "path": "/191/component/domainsurls", "value": 38139}, {"op": "replace", "path": "/21/component/domainsurls", "value": 12662}, {"op": "replace", "path": "/37/component/domainsurls", "value": 3580}, {"op": "replace", "path": "/38/component/domainsurls", "value": 1036}, {"op": "replace", "path": "/28/component/domainsurls", "value": 4838}, {"op": "replace", "path": "/185/component/domainsurls", "value": 12598}, {"op": "replace", "path": "/55/component/domainsurls", "value": 26406}, {"op": "replace", "path": "/1228/component/domainsurls", "value": 3920}, {"op": "replace", "path": "/181/component/domainsurls", "value": 8951}, {"op": "replace", "path": "/181/component/weightedphrases", "value": 1605}, {"op": "replace", "path": "/397/component/domainsurls", "value": 20964}, {"op": "replace", "path": "/188/component/domainsurls", "value": 848}, {"op": "replace", "path": "/200/component/domainsurls", "value": 16713}, {"op": "replace", "path": "/482/component/domainsurls", "value": 3012}, {"op": "replace", "path": "/512/component/domainsurls", "value": 2755}, {"op": "replace", "path": "/233/component/domainsurls", "value": 2767}, {"op": "replace", "path": "/762/component/domainsurls", "value": 24660}], "loglevelrules": []}
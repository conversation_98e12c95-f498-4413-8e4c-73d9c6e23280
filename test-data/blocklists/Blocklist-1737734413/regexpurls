(?i)(687474702f|68747470732f|687474703n2s2s|687474702s|6874747073(?!**********************************||3a2f2f7777772e676f6f676c|********************************)|756767633a2f2f|756767632f|7567676366)	120
(?i)(?<!coursera\.org)/event\.ng/	1
(?i)(anal|busty|dirty|horny|nasty|nude|sexy|tight|topless|xxx)(-|_)?(babe|girl|lesbian|model|slut|teen|girl).*?\.(png|jpe?g|gif|wmv|mpe?g)$	39
(?i)(anonymitychecker|proxy-?list)	120
(?i)(cream-?charge|n2o-?patron|whipper-?charge|graddsifon|grädd<PERSON><PERSON><PERSON>|isi-?professional)	1518
(?i)(deepminer|cryptonight)\.(js|wasm)	512
(?i)(https?://)?1177\.se/[-\w]*/sjukdomar--besvar/psykiska-sjukdomar-och-besvar/sjalvmordstankar	553
(?i)(online|flash)[a-z0-9-]*gam(e|ing)	19
(?i)(what-?is-?|whats|show|locate)-?my-?ip	626
(?i)(young|tight|teen)(-|_)?(pussy).*?\.(png|jpe?g|gif|wmv|mpe?g)$	39
(?i)/(clicktrack|hittrack)	1
(?i)/(smartbanner|apfbanners|realmedia/ads|pics/banner)/	1
(?i)/abortion/	223
(?i)/ads/(media/images|adview\.php\?)	1
(?i)/bbs/	124
(?i)/cgi-bin/nph-adclick\.exe	1
(?i)/denjihou\.js	120
(?i)/denpa\.js	120
(?i)/fetch/http	120
(?i)/forumdisplay\.php	124
(?i)/images\.go2net\.com/go2net/ads/	1
(?i)/iweibo/	28
(?i)/jwplayer/	56
(?i)/login/fetchfreeserversversion2	120
(?i)/login/fetchprotocolversion2	120
(?i)/login/validatekeyversion2	120
(?i)/proxyladie/	120
(?i)/toys/	201
(?i)/viewcgi?pool=	1
(?i)1girl1pitcher\.swf	39
(?i)1guy2needles\.flv	11
(?i)1man1jar\.swf	39
(?i)1pixelout_player\.swf	56
(?i)1priest1nun\.swf	39
(?i)2guys1stump\.swf	39
(?i)3guys1hammer\.swf	39
(?i)4girlsfingerpaint\.swf	39
(?i)68747470733a2f2f(?!7777772e69636f2e6f72672e756b|7777772e7468656965742e6f7267||7777772e676f6f676c|73697465732e676f6f676c652e636f)	120
(?i)[0-9]+(?<!%20)games?\.	19
(?i)[^cb]lock[-_]?pick(?!.*?\.png)	227
(?i)\.(jpe?g|gif|png|css|js|ico|svg|json|woff2?)(\?|$|&)	550
(?i)\b(nude|erotic|sexual)[^/](wo)?m[aey]n	11
(?i)^(?!(https?://)?(findajob\.dwp\.gov\.uk|lexiacore5\.com|ed\.ted\.com|s3\.amazonaws\.com|content\.twinkl\.co\.uk/resource)).*/(ad|(?<!jobs/)advert|advertisment)s?/	1
(?i)^(https?:\/\/)?(?:[0-9]{1,3}\.){3}[0-9]{1,3}\/kindle-wifi\/wifistub\.html	488
(?i)^(https?:\/\/)?a[1-9][0-9]{0,2}avodsl(s3|ns)eu-s\.akamaihd\.net	488
(?i)^[^#]*?\.3gp$	63
(?i)^[^#]*?\.7z$	255
(?i)^[^#]*?\.7zip$	255
(?i)^[^#]*?\.acc$	58
(?i)^[^#]*?\.ace$	255
(?i)^[^#]*?\.ade$	60
(?i)^[^#]*?\.adp$	60
(?i)^[^#]*?\.adts$	58
(?i)^[^#]*?\.ai$	62
(?i)^[^#]*?\.aif$	58
(?i)^[^#]*?\.aifc$	58
(?i)^[^#]*?\.aiff$	58
(?i)^[^#]*?\.amr$	58
(?i)^[^#]*?\.apk$	59
(?i)^[^#]*?\.app$	59
(?i)^[^#]*?\.arj$	255
(?i)^[^#]*?\.asf$	61
(?i)^[^#]*?\.asp$	65
(?i)^[^#]*?\.aspx$	65
(?i)^[^#]*?\.asx$	61
(?i)^[^#]*?\.au$	58
(?i)^[^#]*?\.avi$	61
(?i)^[^#]*?\.bas$	59
(?i)^[^#]*?\.bat$	59	255
(?i)^[^#]*?\.bin$	255
(?i)^[^#]*?\.bmp$	65
(?i)^[^#]*?\.bwf$	58
(?i)^[^#]*?\.bz2$	57	255
(?i)^[^#]*?\.cab$	59	255
(?i)^[^#]*?\.caf$	58
(?i)^[^#]*?\.cdda$	58
(?i)^[^#]*?\.cdr$	57
(?i)^[^#]*?\.cel$	63
(?i)^[^#]*?\.cgi$	65
(?i)^[^#]*?\.cgm$	62
(?i)^[^#]*?\.chm$	59
(?i)^[^#]*?\.cmd$	59
(?i)^[^#]*?\.cmx$	65
(?i)^[^#]*?\.cod$	65
(?i)^[^#]*?\.com$	255
(?i)^[^#]*?\.cpl$	59
(?i)^[^#]*?\.crt$	255
(?i)^[^#]*?\.css$	65
(?i)^[^#]*?\.cue$	64
(?i)^[^#]*?\.deb$	59
(?i)^[^#]*?\.dif$	63
(?i)^[^#]*?\.divx$	63
(?i)^[^#]*?\.dll$	255
(?i)^[^#]*?\.dmg$	57
(?i)^[^#]*?\.doc$	60	255
(?i)^[^#]*?\.dpx$	62
(?i)^[^#]*?\.dv$	63
(?i)^[^#]*?\.dxf$	62
(?i)^[^#]*?\.emf$	62
(?i)^[^#]*?\.exe$	59	255
(?i)^[^#]*?\.fh$	62
(?i)^[^#]*?\.flc$	63
(?i)^[^#]*?\.fli$	63
(?i)^[^#]*?\.flv$	61	63
(?i)^[^#]*?\.gif$	61
(?i)^[^#]*?\.gsm$	58
(?i)^[^#]*?\.gz$	57	255
(?i)^[^#]*?\.hlp$	59	255
(?i)^[^#]*?\.hqx$	57	255
(?i)^[^#]*?\.hta$	59	255
(?i)^[^#]*?\.htm$	65
(?i)^[^#]*?\.html$	65
(?i)^[^#]*?\.ico$	61
(?i)^[^#]*?\.ief$	65
(?i)^[^#]*?\.img$	255
(?i)^[^#]*?\.inf$	59	255
(?i)^[^#]*?\.ini$	59	255
(?i)^[^#]*?\.ins$	59
(?i)^[^#]*?\.iso$	64	255
(?i)^[^#]*?\.isp$	59
(?i)^[^#]*?\.ivf$	63
(?i)^[^#]*?\.jfif$	65
(?i)^[^#]*?\.jpe$	65
(?i)^[^#]*?\.jpeg$	61
(?i)^[^#]*?\.jpg$	61
(?i)^[^#]*?\.kar$	58
(?i)^[^#]*?\.lnk$	59	255
(?i)^[^#]*?\.m3u$	58
(?i)^[^#]*?\.m3url$	58
(?i)^[^#]*?\.m4v$	61
(?i)^[^#]*?\.mda$	255
(?i)^[^#]*?\.mdb$	60	255
(?i)^[^#]*?\.mde$	60	255
(?i)^[^#]*?\.mid$	58
(?i)^[^#]*?\.midi$	58
(?i)^[^#]*?\.mjpeg$	61
(?i)^[^#]*?\.mjpg$	61
(?i)^[^#]*?\.mov$	61
(?i)^[^#]*?\.mp2$	63
(?i)^[^#]*?\.mp3$	58
(?i)^[^#]*?\.mp4$	61
(?i)^[^#]*?\.mpeg$	61
(?i)^[^#]*?\.mpg$	61
(?i)^[^#]*?\.mqv$	63
(?i)^[^#]*?\.msc$	59
(?i)^[^#]*?\.msi$	59
(?i)^[^#]*?\.msp$	59
(?i)^[^#]*?\.mst$	59
(?i)^[^#]*?\.nim$	63
(?i)^[^#]*?\.nrg$	255
(?i)^[^#]*?\.ogg$	58
(?i)^[^#]*?\.ops$	59
(?i)^[^#]*?\.p2p$	64
(?i)^[^#]*?\.pbm$	65
(?i)^[^#]*?\.pcd$	59	255
(?i)^[^#]*?\.pdf$	255
(?i)^[^#]*?\.pgm$	65
(?i)^[^#]*?\.php$	65
(?i)^[^#]*?\.pif$	59
(?i)^[^#]*?\.pl$	65
(?i)^[^#]*?\.png$	61
(?i)^[^#]*?\.pnm$	65
(?i)^[^#]*?\.ppm$	65
(?i)^[^#]*?\.ps1$	59
(?i)^[^#]*?\.qcp$	58
(?i)^[^#]*?\.qt$	63
(?i)^[^#]*?\.ra$	61
(?i)^[^#]*?\.ram$	61
(?i)^[^#]*?\.rar$	57	255
(?i)^[^#]*?\.ras$	65
(?i)^[^#]*?\.reg$	59
(?i)^[^#]*?\.rgb$	65
(?i)^[^#]*?\.rm$	61
(?i)^[^#]*?\.rpm$	59
(?i)^[^#]*?\.rts$	61
(?i)^[^#]*?\.rtsp$	61
(?i)^[^#]*?\.rtx$	65
(?i)^[^#]*?\.scr$	59
(?i)^[^#]*?\.sct$	59
(?i)^[^#]*?\.sd2$	58
(?i)^[^#]*?\.sdp$	61
(?i)^[^#]*?\.sea$	57
(?i)^[^#]*?\.sh$	59	255
(?i)^[^#]*?\.shb$	59
(?i)^[^#]*?\.shs$	59
(?i)^[^#]*?\.shtml$	65
(?i)^[^#]*?\.sit$	57	255
(?i)^[^#]*?\.smf$	58
(?i)^[^#]*?\.smi$	57
(?i)^[^#]*?\.snd$	58
(?i)^[^#]*?\.stm$	65
(?i)^[^#]*?\.svg$	62
(?i)^[^#]*?\.svgz$	62
(?i)^[^#]*?\.swa$	58
(?i)^[^#]*?\.swf$	259
(?i)^[^#]*?\.sxw$	255
(?i)^[^#]*?\.sys$	59
(?i)^[^#]*?\.tar$	57	255
(?i)^[^#]*?\.tgz$	57	255
(?i)^[^#]*?\.tif$	61
(?i)^[^#]*?\.tiff$	61
(?i)^[^#]*?\.ts$	63
(?i)^[^#]*?\.txt$	65
(?i)^[^#]*?\.ulw$	58
(?i)^[^#]*?\.unityweb$	19
(?i)^[^#]*?\.vb$	59
(?i)^[^#]*?\.vbe$	59
(?i)^[^#]*?\.vbs$	59	255
(?i)^[^#]*?\.vfw$	63
(?i)^[^#]*?\.vxd$	59
(?i)^[^#]*?\.wav$	58
(?i)^[^#]*?\.wax$	58
(?i)^[^#]*?\.wm$	63
(?i)^[^#]*?\.wma$	58
(?i)^[^#]*?\.wmf$	62
(?i)^[^#]*?\.wmv$	61
(?i)^[^#]*?\.wsc$	59
(?i)^[^#]*?\.wsf$	59
(?i)^[^#]*?\.wsh$	59
(?i)^[^#]*?\.wvx$	63
(?i)^[^#]*?\.xar$	62
(?i)^[^#]*?\.xbm$	65
(?i)^[^#]*?\.xls$	60	255
(?i)^[^#]*?\.xml$	65
(?i)^[^#]*?\.xpm$	65
(?i)^[^#]*?\.xsl$	65
(?i)^[^#]*?\.xwd$	65
(?i)^[^#]*?\.zip$	57	255
(?i)^https://([^/]*\.)?yt\d\.ggpht\.com	628
(?i)^https://[^/]*?(free|unblock|online)[^/]*?game[^/]*?\.weebly\.com	19
(?i)^https?://(?!(www\.)?thelaminitissite\.org)[^/]*?(hot|lesbian|big|cute|cyber|fake|firm|hard|huge|little|mega|mini|naughty|(?<!peteg)old|pure|real|small|serious|soft|super|tiny|young|amateur)+(?!clitheroe|analytics)(anal|babe(?!-hotsauce)|bharath|boob|breast|busen|busty|clit|cunt|dick|fetish|fuck|hooter|lez|lust|naked|nude|oral|orgy|porno?|pupper|pussy|rotten|shit|smutpump|teen|tissit|tit(?!anstudios)|topp?les|vixen|xxx|nudist|milf)+	39
(?i)^https?://(?![^/]*?encrypted-v?tbn\d)[^/]*?\.?gstatic\.com	471
(?i)^https?://(?![^/]*?youtubei)[^/]*?\.?googleapis\.com	471
(?i)^https?://(?![^/]*\.(mixtubeapp\.com|relaxtubes\.com))[^/]*(gay|fuck|x)-?tube	39
(?i)^https?://(?:[^/]*?\.)?files\.wordpress\.com/.*\.(jpg|jpeg|png|bitmap|tif|gif|bmp|gifv)$	198
(?i)^https?://(?:[^/]*?\.)?yospace\.com/csm/extlive/itv	607
(?i)^https?://(?:[^/]*?\.)?youtube\.com.*?=adunit&	1
(?i)^https?://(?:www\.)?baidu\.com/sugrec\?.*?sugsid=	228
(?i)^https?://(?:www\.)?google\.\w{1,3}(?:\.\w{1,3})?(/?$|/search|/\?)	50	639
(?i)^https?://(?:www\.)?google\.\w{1,3}(?:\.\w{1,3})?/maps	189
(?i)^https?://([-a-z0-9]+\.)?(?!yoursexualhealthmatters\.org\.uk)[-a-z0-9]*(you|twink|gay|free|tube)[-a-z0-9]*(porn|xxx|(?<!es)(?<!sus)sex)	39
(?i)^https?://([-a-z0-9]+\.)?[-a-z0-9]*(xnxx|porn|xxx|sex)[-a-z0-9]*(tube|video|free|teen|gay)	39
(?i)^https?://([^/]*)?amazon-\d{1,4}\.vo\.llnwd\.net	56
(?i)^https?://([^/]*)?fbcdn\.net.*?live-dash	518
(?i)^https?://([^/]*)?fbcdn\.net.*live-dash	518
(?i)^https?://([^/]*)?google\.\w{1,3}(?:\.\w{1,3})?/gwt/n	120
(?i)^https?://([^/]*)?googleusercontent\.com/.*?/video/mp4	56
(?i)^https?://([^/]*)?googlevideo\.com.*?\/yt_live_broadcast\/	518
(?i)^https?://([^/]*)?hulu-\d{1,3}\.fcod\.llnwd\.net	56
(?i)^https?://([^/]*)?netflix-\d+\.vo\.llnwd\.net	411
(?i)^https?://([^/]*)?netflix-\d{3}\.vo\.llnwd\.net	56
(?i)^https?://([^/]*)?smashcast\.tv/api/media/live/	518
(?i)^https?://([^/]*)?ttvnw\.net.*?\/segment\/	518
(?i)^https?://([^/]*)?tv-angered\.se/flv/[^\.]+\.flv	56
(?i)^https?://([^/]*)?youtube\.com.*?&live=1	518
(?i)^https?://([^/]*)?youtube\.com.*?&live=live	518
(?i)^https?://([^/]*?\.)?facebook\.com/xti\.php\?.*?suicide_prevention	553
(?i)^https?://([^/]*\.)?(twitter|x)\.com/[^/]*?(xxx|XXX)	39
(?i)^https?://([^/]*\.)?blogger\.\w{1,3}(?:\.\w{1,3})?($|/)	187
(?i)^https?://([^/]*\.)?blogger\.\w{1,3}(?:\.\w{1,3})?/\?guestAuth=	11
(?i)^https?://([^/]*\.)?blogspot\.\w{1,3}(?:\.\w{1,3})?($|/)	187
(?i)^https?://([^/]*\.)?blogspot\.\w{1,3}(?:\.\w{1,3})?/\?guestAuth=	11
(?i)^https?://([^/]*\.)?cnn\.com/(.+?)/sport/	53
(?i)^https?://([^/]*\.)?cnn\.com/(\d{4}/)?health/	7
(?i)^https?://([^/]*\.)?dailyrecord\.co.uk/(.*?)/?sport/	53
(?i)^https?://([^/]*\.)?google(\.\w{2,3}){1,2}/pacman	19
(?i)^https?://([^/]*\.)?google\.\w{1,3}(\.\w{1,3})?/logos/2013/bday13/bday13\.js	19
(?i)^https?://([^/]*\.)?onlinecollege\d{4}\.info/	120
(?i)^https?://([^/]*\.)?rammerhead	120
(?i)^https?://([^/]*\.)?sanctioned-?suicide	214
(?i)^https?://([^/]*\.)?temu\.com/(?=.*?ws-titan-request-sign)	550
(?i)^https?://([^/]*\.)?wikipedia.org/w/index\.php\?title=special:userlogin	403
(?i)^https?://([^/]*\.)?wikipedia\.org*(.+)action=edit	403
(?i)^https?://([^/]*\.)?xn--	517
(?i)^https?://(ads?|adserver|k5ads)\.(?!cmail19\.com|createsend1\.com)	1
(?i)^https?://(heads|audio)-[a-z]+-spotify-com\.akamaized\.net	613
(?i)^https?://(www\.)?(twitter|x)\.com/(porn_?vid|(?<!car)(?<!earth)porn_?pic|gapeporn|cumshot|porn_?hub|xvideo|legalporn|sex_?vid|porn_?star|porn_?cam|gay_?porn|manyvids|creampie|cum_?slut|anal_?sex|hot_?sex_?video|porn_?fidelity|mia_?khalifa|big_?boob|huge_?boobs|milf_?porn|boobs_?babes|riley_?reid|lesbian_?porn|brazzers?|bangbros?)	39
(?i)^https?://(www\.)?amazon\.\w{1,3}(?:\.\w{1,3})?($|/)	34
(?i)^https?://(www\.)?archive\.org/download/.*?\.jpg	198
(?i)^https?://(www\.)?bol.com/\w+/\w+/ajax/.*?[?&]forceUpdate=true	11
(?i)^https?://(www\.)?collegeonline[0-9]{2,}\.info	120
(?i)^https?://(www\.)?collproxsec\d+\.info	120
(?i)^https?://(www\.)?duckduckgo\.com/\?[\w=&]+iax?=images	54
(?i)^https?://(www\.)?gomovies\.	24
(?i)^https?://(www\.)?google\.\w{1,3}(?:\.\w{1,3})?/(images|imghp)[^/]	54
(?i)^https?://(www\.)?google\.\w{1,3}(?:\.\w{1,3})?/(m/)?search\?.*tbm=isch	54
(?i)^https?://(www\.)?google\.\w{1,3}(?:\.\w{1,3})?/search\?.*?tbs=(simg|sbi)	393
(?i)^https?://(www\.)?google\.\w{1,3}(?:\.\w{1,3})?/searchbyimage	393
(?i)^https?://(www\.)?google\.\w{1,3}(\.\w{1,3})?/async/translate	215
(?i)^https?://(www\.)?google\.\w{1,3}(\.\w{1,3})?/complete/search	228
(?i)^https?://(www\.)?google\.\w{1,3}(\.\w{1,3})?/language_tools	215
(?i)^https?://(www\.)?google\.com/complete/search.*?client=onepick-web-image	54
(?i)^https?://(www\.)?google\.com/intl/[-a-z]{5}(_all)?/drive/	632
(?i)^https?://(www\.)?google\.com\/search\?.*?moreresultscontainer	550
(?i)^https?://(www\.)?line\.me	18
(?i)^https?://(www\.)?my\.mail\.ru/mail/\w+/video/	198
(?i)^https?://(www\.)?nitter\.	28
(?i)^https?://(www\.)?onionplay\.\w{1,3}(\.\w{1,3})?	24
(?i)^https?://(www\.)?replit\.com/@[^/]*/[^/]*(chrome|chromium|firefox|browser|proxy|unblock|ultraviolet-node|uv-instance|hwhelp)	120
(?i)^https?://(www\.)?searx\.(?!github\.io)	50
(?i)^https?://(www\.)?vk\.com/video-	11
(?i)^https?://(www\.)?vumoo\.	24
(?i)^https?://(www\.)?watch-?series\.	24
(?i)^https?://(www\.)?yesmovies\d?\.	24
(?i)^https?://(www\.)?youtube\.com/(?:api/stats|get_video_info).*?adunit	1
(?i)^https?://(www\.)?youtube\.com/(get|api)_video_info	262
(?i)^https?://(www\.)?youtube\.com/[^\s]*spf=navigate	262
(?i)^https?://(www\.)?youtube\.com/playlist	262
(?i)^https?://103\.122\.16[67]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://103\.15\.1[6-9]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://104\.146\.(25[0-5]|2[0-4][0-9]|1[3-9][0-9]|12[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	444
(?i)^https?://104\.47\.(12[0-7]|1[01][0-9]|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	444
(?i)^https?://104\.4[0-7](\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	501
(?i)^https?://108\.160\.16[0-9]\.\d{1,3}	193	420
(?i)^https?://108\.160\.17[0-5]\.\d{1,3}	193	420
(?i)^https?://108\.177\.15\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420	617
(?i)^https?://111\.221\.(12[0-7]|1[01][0-9]|[7-9][0-9]|6[4-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	501
(?i)^https?://111\.221\.57\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://115\.114\.131\.(6[0-3]|[1-5]?[0-9])	565
(?i)^https?://115\.114\.56\.(25[0-5]|2[0-4][0-9]|19[2-9])	565
(?i)^https?://120\.29\.148\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://131\.253\.33\.215	444
(?i)^https?://132\.245(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	444
(?i)^https?://13\.107\.(12[0-7]|1[01][0-9]|[7-9][0-9]|6[4-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	501
(?i)^https?://13\.107\.(13[01]|12[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	444
(?i)^https?://13\.107\.13[6-9]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	444
(?i)^https?://13\.107\.140\.6	444
(?i)^https?://13\.107\.18\.15	444
(?i)^https?://13\.107\.18\.1[01]	444
(?i)^https?://13\.107\.3\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	501
(?i)^https?://13\.107\.6\.15[23]	444
(?i)^https?://13\.107\.6\.171	444
(?i)^https?://13\.107\.6\.192	444
(?i)^https?://13\.107\.9\.192	444
(?i)^https?://13\.52\.6\.(25[0-5]|2[0-4][0-9]|1[3-9][0-9]|12[89])	565
(?i)^https?://143\.166(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	420
(?i)^https?://150\.171\.3[2-5]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	444
(?i)^https?://150\.171\.4[0-3]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	444
(?i)^https?://157\.5[45](\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	501
(?i)^https?://157\.5[6-9](\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	501
(?i)^https?://157\.60(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	501
(?i)^https?://158\.120\.(3[01]|2[0-9]|1[6-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://162\.12\.23[2-5]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://162\.250\.6[0-3]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://162\.255\.3[6-9]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://169\.45\.(25[0-5]|2[0-4][0-9]|19[2-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://173\.199\.(6[0-3]|[1-5]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://185\.36\.2[0-3]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://185\.63\.147\.\d{2,3}	28
(?i)^https?://188\.66\.4[0-7]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://18\.205\.93\.(25[0-5]|2[0-4][0-9]|1[3-9][0-9]|12[89])	565
(?i)^https?://192\.204\.1[2-5]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://199\.101\.163\.\d{2,3}	28
(?i)^https?://199\.36\.(25[01]|24[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://199\.47\.21[6-9]\.\d{1,3}	193	420
(?i)^https?://199\.87\.12[0-3]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://202\.173\.(3[01]|2[4-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://202\.177\.207\.(1[3-5][0-9]|12[89])	565
(?i)^https?://202\.177\.213\.(12[0-7]|1[01][0-9]|9[6-9])	565
(?i)^https?://204\.141\.(3[01]|2[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://204\.79\.197\.215	444
(?i)^https?://206\.183\.10[0-3]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://207\.226\.132\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://209\.9\.211\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://209\.9\.215\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://20\.190\.(19[01]|1[3-8][0-9]|12[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	444
(?i)^https?://20\.20\.(6[0-3]|[45][0-9]|3[2-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	444
(?i)^https?://20\.231\.(1[3-5][0-9]|12[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	444
(?i)^https?://210\.57\.55\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://213\.19\.144\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://213\.19\.153\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://213\.244\.140\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://216\.115\.(22[0-3]|21[0-9]|20[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://216\.219\.(12[0-7]|11[2-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://221\.122\.88\.(25[0-5]|2[0-4][0-9]|1[3-9][0-9]|12[89])	565
(?i)^https?://221\.122\.89\.(25[0-5]|2[0-4][0-9]|1[3-9][0-9]|12[89])	565
(?i)^https?://23\.103\.(17[0-5]|16[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	444
(?i)^https?://23\.239\.(25[0-5]|2[34][0-9]|22[4-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://23\.9[6-9](\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	501
(?i)^https?://3\.120\.121\.(12[0-7]|1[01][0-9]|[1-9]?[0-9])	565
(?i)^https?://40\.(10[0-3]|9[6-9])(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	444
(?i)^https?://40\.(11[01]|10[0-9]|9[6-9])(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	501
(?i)^https?://40\.(9[0-5]|8[0-9])(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	501
(?i)^https?://40\.107(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	444
(?i)^https?://40\.108\.(25[0-5]|2[0-4][0-9]|1[3-9][0-9]|12[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	444
(?i)^https?://40\.10[45](\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	444
(?i)^https?://40\.11[2-9](\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	501
(?i)^https?://40\.124(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	501
(?i)^https?://40\.125\.(12[0-7]|1[01][0-9]|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	501
(?i)^https?://40\.126\.(6[0-3]|[1-5]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	444
(?i)^https?://40\.12[0-3](\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	501
(?i)^https?://40\.7[45](\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	501
(?i)^https?://40\.7[6-9](\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	501
(?i)^https?://40\.9[23](\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	444
(?i)^https?://45\.12\.19[6-9]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://46\.16\.32\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	120
(?i)^https?://46\.16\.35\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	120
(?i)^https?://52\.(11[01]|10[89])(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	444
(?i)^https?://52\.10[0-3](\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	444
(?i)^https?://52\.10[4-7](\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	444
(?i)^https?://52\.11[2-5](\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	501
(?i)^https?://52\.202\.62\.(25[0-5]|2[0-4][0-9]|19[2-9])	565
(?i)^https?://52\.215\.168\.(12[0-7]|1[01][0-9]|[1-9]?[0-9])	565
(?i)^https?://52\.238\.78\.88	444
(?i)^https?://52\.244\.37\.168	444
(?i)^https?://52\.9[6-9](\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	444
(?i)^https?://64\.211\.144\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://64\.4\.(6[0-3]|[1-5]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	501
(?i)^https?://64\.69\.74\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://64\.74\.103\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://64\.74\.17\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://64\.74\.1[89]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://64\.94\.18\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://64\.94\.4[67]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://64\.95\.12[89]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://65\.39\.152\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://65\.5[2-5](\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	501
(?i)^https?://66\.150\.108\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://67\.217\.(9[0-5]|[78][0-9]|6[4-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://68\.64\.(3[01]|[12]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://69\.174\.(11[01]|10[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://69\.174\.57\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://69\.25\.247\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://69\.25\.2[01]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://74\.125\.140\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420	617
(?i)^https?://74\.125\.250\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420	617
(?i)^https?://78\.108\.(12[0-7]|11[2-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://8\.5\.12[89]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	565
(?i)^https?://91\.190\.21[67]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	501
(?i)^https?://91\.190\.21[89]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	501
(?i)^https?://95\.172\.70\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	420
(?i)^https?://?(static|img|images?|assets?)(\d{1,5})?\.	205
(?i)^https?://?(www\.)?linkedin\.com/company/[0-9a-z]*?/careers	37
(?i)^https?://?(www\.)?youtube(\.[a-z]{2,4})?\.[a-z]{2,4}	262
(?i)^https?://[-\w]+?\.googlevideo\.com/(get_video.*?fmt|videoplayback.*?itag|preload.*?itag)=(22|37|38|45|46|84|85|95|96|102|136|137|138|169|247|248|264|266|271|272|298|299|302|303|308|313|315|334|335|336|337)	409
(?i)^https?://[-\w]+?\.youtube\.com/(get_video.*?fmt|videoplayback.*?itag|preload.*?itag)=(22|37|38|45|46|84|85|95|96|102|136|137|138|169|247|248|264|266|271|272|298|299|302|303|308|313|315|334|335|336|337)	409
(?i)^https?://[-\w]+tiktok[-\w]+\.akamaized\.net	612
(?i)^https?://[-\w]+tiktok[-\w]+\.ibyteimg\.com	612
(?i)^https?://[-a-z0-9]*\.vk\.me	11
(?i)^https?://[-a-z]+?\.ask\.com/query	228
(?i)^https?://[-a-z]+?\.search\.yahoo\.com/sugg/	228
(?i)^https?://[-a-z]+?\.wikipedia\.org/w/api\.php\?.*?(action=query|gpssearch|action=opensearch)	228
(?i)^https?://[1-4]\.bp\.blogspot\.com.*(?:jpe?g|png|gif)	198
(?i)^https?://[\w-]*hudlvid\.s3\.amazonaws\.com	497
(?i)^https?://[^/?]*(adultsight|adultsite|adultsonly|adultweb|blow-?job|(?<!vaga)bondage|centerfold|cumshot|cyberlust|cybercore(?!tech)|(?<!orc)(?<!happyhappy)hardcore(?!hockey|tokyo|droid|hobbies|-?gamer|gaming|hammers|prawnlawn|-?bands|-?brands|fishingtackle)|masturbat|pedophil|pedofil|pornstar|sexdream|showgirl(?!fitness)|softcore|striptease)	11	39
(?i)^https?://[^/]*(?<!scara)manga(?!rhealth|club|hotels|-capetown|l)	474
(?i)^https?://[^/]*(chrome|chromium|firefox|browser|proxy|unblock|ultraviolet-node|uv-instance|hwhelp)[^/]*\.repl\.co	120
(?i)^https?://[^/]*(soap2day|s2dfree)	24
(?i)^https?://[^/]*(the)?fappening	11
(?i)^https?://[^/]*(youporn|xvideos|xhamster|youjizz|redtube)	39
(?i)^https?://[^/]*?(?:miniroyale|krunker|battleroyale|happywheels|slither|minigiants|zombsroyale|swordz)	19
(?i)^https?://[^/]*?(?<!aquarius-)pro-?life(?!(collectief|ration|stage\.|\.nl))	223
(?i)^https?://[^/]*?(cosmetic|plastic)-?surge(ry|on)	7
(?i)^https?://[^/]*?(pro-?ana)(?!tomy)	214
(?i)^https?://[^/]*?(torrent|tracker)[^/]*?/announce	52
(?i)^https?://[^/]*?/(banners|httpads|advertising|ads/banners)/	1
(?i)^https?://[^/]*?123[^/]*?(free|movie|stream)	24
(?i)^https?://[^/]*?\.craigslist\.\w{1,3}(?:\.\w{1,3})?/(?:ppp|stp|w4w|w4m|m4w|m4m|msr|cas|mis|rnr)	20
(?i)^https?://[^/]*?\.craigslist\.\w{1,3}(?:\.\w{1,3})?/i/personals	20
(?i)^https?://[^/]*?\.craigslist\.\w{1,3}(?:\.\w{1,3})?/search/(?:ppp|stp|w4w|w4m|m4w|m4m|msr|cas|mis|rnr)	20
(?i)^https?://[^/]*?\.google(?:apis|syndication|usercontent|video)?\.com/videoplayback[^/]+?source=youtube	262
(?i)^https?://[^/]*?\.gvt1\.com/videoplayback[^/]+?source=youtube	262
(?i)^https?://[^/]*?\.k12\.\w\w\.us($|/)	397
(?i)^https?://[^/]*?abortion	223
(?i)^https?://[^/]*?agar-?io	19
(?i)^https?://[^/]*?breast-?(reconstruction|augmentation|enlargement|enhancement|surge|implant)	7
(?i)^https?://[^/]*?cdn	205
(?i)^https?://[^/]*?chat\.facebook\.com	18	398
(?i)^https?://[^/]*?dating	20
(?i)^https?://[^/]*?fortnite	104
(?i)^https?://[^/]*?meatspin	39
(?i)^https?://[^/]*?pay-?day-?loan	419
(?i)^https?://[^/]*?speed-?test	433
(?i)^https?://[^/]*?tattoo(studio|parlour|shop)	218
(?i)^https?://[^/]*?wedding	212
(?i)^https?://[^/]*\.gvt1.com/[-_./a-zA-Z0-9]*chrome(webstore|_component)[-_./a-zA-Z0-9]+\.crx\d?	147
(?i)^https?://[^/]*\bapis?	205
(?i)^https?://[^/]*textadviser\.com/\w+/chat	654
(?i)^https?://[^/]+/api[s\d]?/(?!doc|guide)	205
(?i)^https?://[^/]+/blog	187
(?i)^https?://[a-z]{1,3}[0-9]{1,3}\.eset\.com	147
(?i)^https?://\w+\.cloudfront\.net/assets/learnium-\w+\.js	200
(?i)^https?://accounts\.google\.\w{1,3}(\.\w{1,3})?	471
(?i)^https?://api\.twitter\.com/\d(?:\.\d)?/\w+\/\w+\.json$	418
(?i)^https?://archive\.org/details/(arcade|atari|sg|gg|segasms|apfm1000|sa|svision|advision|gamepocket|socrates|sv8000|gx4000|intv|ngp|seuck|arcade|msdos)_	19
(?i)^https?://archive\.org/stream/arcade_	19
(?i)^https?://backcn\d{1,3}\.appspot\.com	120
(?i)^https?://base[0-9]+-sv\.diltwo\.com	120
(?i)^https?://base\d+-hx\.cnt2\.net	120
(?i)^https?://bbs\.	124
(?i)^https?://beacons\d?\.(?:\w{1,3}\.)?gvt\d\.com	471
(?i)^https?://blogs?\.	187
(?i)^https?://bp[1-4]\.blogger\.com.*(?:jpe?g|png|gif)	198
(?i)^https?://cache-\d{3,4}\.appspot\.com	120
(?i)^https?://careers\.	37
(?i)^https?://celeb(s|rities|rity)	192
(?i)^https?://classdojo-\d{2,}.pubnub.com	572
(?i)^https?://clients[0-9]\.google\.\w{1,3}(?:\.\w{1,3})?/webpagethumbnail	251
(?i)^https?://duckduckgo.com\/\?q=.*?&ia=chat&duckai=1	654
(?i)^https?://fbcdn-(?:creative|dragon|photos|profile|sphotos)-[-_\w]+\.akamaihd\.net	28	238
(?i)^https?://fbcdn-\w+-.-.\.akamaihd\.net/	238
(?i)^https?://fbcdn-\w+-.\.akamaihd\.net/	238
(?i)^https?://fbcdn-\w+-\w(-\w)?\.akamaihd\.net	238
(?i)^https?://fbexternal-.\.akamaihd\.net/	238
(?i)^https?://fbexternal-\w\.akamaihd\.net	238
(?i)^https?://fbstatic-.\.akamaihd\.net/	238
(?i)^https?://fbstatic-\w\.akamaihd.net	238
(?i)^https?://forums?\.	124
(?i)^https?://groceries\.morrisons\.com/browse/beer-wines-spirits	114
(?i)^https?://hangouts\.clients\d\.google\.com	617
(?i)^https?://host[^/]*?cnt2\.net	120
(?i)^https?://images\.google\.	54
(?i)^https?://jobs\.	37
(?i)^https?://lh\d\.ggpht\.com	198
(?i)^https?://local(dates|hookup)[1-9]{1,2}\.com	39
(?i)^https?://m\.youtube\.com	262
(?i)^https?://photos[1-4]\.blogger\.com.*(?:jpe?g|png|gif)	198
(?i)^https?://pirate[^/]*?proxy	24
(?i)^https?://reddpics\.com/\?r=	11
(?i)^https?://return\.\w\w\.domainnamesales\.com	237
(?i)^https?://translate\.google\.\w{1,3}(?:\.\w{1,3})?	215
(?i)^https?://translate\.google\.\w{1,3}(?:\.\w{1,3})?/translate[-_?&=a-zA-Z0-9.]+?&u=	120
(?i)^https?://translate\.googleusercontent\.\w{1,3}(?:\.\w{1,3})?/translate[-_?&=a-zA-Z0-9.]+?&u=	120
(?i)^https?://ts[\d]\.mm\.bing\.net/th\?id=OMB\d?\.[\w\d+/%]+&pid=2\.1	56
(?i)^https?://twitter\.com/i/\d+/lists	418
(?i)^https?://ultrasurf\.\w{7,}\.info	120
(?i)^https?://update\d*\.services\.openoffice\.org	147
(?i)^https?://watchdocumentaries\.com/[^/]+-game/	19
(?i)^https?://weather\.	38
(?i)^https?://webmail\d?\.	44
(?i)^https?://whatsapp\-cdn\-shv\-\d+\-\w+\.fbcdn\.net	614
(?i)^https?://whatsapp\-chatd\-edge\-shv\-\d+\-\w+\.facebook\.com	614
(?i)^https?://www\.amazon\.\w{1,3}(\.\w{1,3})?/.*?signin.*atv_primeLPsignin	488
(?i)^https?://www\.amazon\.\w{1,3}(\.\w{1,3})?/.*\baiv-web-player	56
(?i)^https?://www\.amazon\.\w{1,3}(\.\w{1,3})?/.*\binstant-video	56
(?i)^https?://www\.amazon\.\w{1,3}(\.\w{1,3})?/.*\bref=\w\w_atv_	56	488
(?i)^https?://www\.amazon\.\w{1,3}(\.\w{1,3})?/.*\bref=atv_	56	488
(?i)^https?://www\.amazon\.\w{1,3}(\.\w{1,3})?/.*\bref=dv_web	488
(?i)^https?://www\.amazon\.\w{1,3}(\.\w{1,3})?/dp/[A-Z0-9]{10}/\?autoplay	488
(?i)^https?://www\.amazon\.\w{1,3}(\.\w{1,3})?/gp/video	56	488
(?i)^https?://www\.amazon\.com/(?!clouddrive|ap/signin)	34
(?i)_\?l21vyy5	120
(?i)abortion\.htm	223
(?i)alt\.suicide\.bus\.stop	214
(?i)alt\.suicide\.holiday	214
(?i)alt\.suicide\.methods	214
(?i)behead(?!-the-duck|er)	111
(?i)behead.*?isis	111
(?i)bestproxyever	120
(?i)beyondblox(\d+)\.info	120
(?i)blastbilliards	19
(?i)blocksoff(\d+)\.info	120
(?i)blue.?waffle	11
(?i)bowlgirl\.swf	39
(?i)buy[-_\+]?steroids	9
(?i)cgi-sys/suspendedpage\.cgi$	237
(?i)clownsong\.swf	39
(?i)copter_game	19
(?i)coptergame	19
(?i)cunt(-|_|%20)?muffin	11
(?i)dabiq.?magazine	35
(?i)dadparty\.swf	39
(?i)domainpark\.cgi	237
(?i)drinking[-_]?game	114
(?i)eatyoursoup\.flv	11
(?i)eelsoup\.swf	39
(?i)eyeglass\.php	120
(?i)firoxy	120
(?i)flash[a-z0-9-]+gam(e|ing)	19
(?i)freeit\d+\.info	120
(?i)gadgets/proxy	120
(?i)go2-vn\d+\.appspot\.com	120
(?i)goatse\.jpg	39
(?i)goatsegirl\.swf	39
(?i)goork\.swf	39
(?i)horde/imp	44
(?i)how-to-crack(?!-the-class-ceiling)	30
(?i)https?://(?![^/]+\.gov\.uk).*?687474703a2f2f(?!7777772e6e6f7774762e636f6d|7777772e69636f2e6f72672e756b|7777772e7468656965742e6f7267|69636f2e6f72672e756b|73632d6c70732d63612e7363686f6f6c6c6f6f702e636f6d|68652d6b73642d69642e7363686f6f6c6c6f6f702e636f6d|6634747261636b|676f2e736b792e|31302e31362e)	120
(?i)ig/proxy	120
(?i)inspire.?magazine	35
(?i)isis.*?behead	111
(?i)iweibo\.php	28
(?i)jarsquatter\.flv	39
(?i)jarsquatter\.swf	39
(?i)kids_in_sandbox\.swf	39
(?i)kidsinsandbox\.swf	39
(?i)kill-?(my|your)self	587
(?i)lemonparty	11
(?i)meatspin\.swf	11	39
(?i)mercadolibre\.com\.co/adultos	11
(?i)merryholidays\.swf	39
(?i)networking4all\.com/../registered/	237
(?i)niggerobama\.jpg	35
(?i)nph-SCAREWAR\.cgi	120
(?i)nph-proxy\.cgi	120
(?i)nutabuse\.swf	39
(?i)octopusgirl\.swf	39
(?i)pay-?day-?advance	419
(?i)phonejapan\.swf	39
(?i)pro-?choice	223
(?i)proboards[\d]*.com	124
(?i)rattegangspodden	11
(?i)ruff.nude	39
(?i)ruff.thomas	26
(?i)sahnekapseln	1518
(?i)same-?day-?loan	419
(?i)source=yt_live_broadcast	518
(?i)stickcricket	19
(?i)suckdude\.swf	39
(?i)the(%20|[-_+])?great(%20|[-_+])?replacement[.-](docx?|pdf)	35
(?i)the-torture-game\.swf	6
(?i)thomas.ruff	26
(?i)tor2web	120
(?i)travian	19
(?i)tubgirl\.jpg	11
(?i)unblocked-?games	19
(?i)unblocked-?movies	24
(?i)vomitgirl\.swf	39
(?i)wage-?day-?loan	419
(?i)whipcrack\.swf	39
/hvtr%3A-%2F	120
/hvtrs8%2F-	120
/main\.1caa512e\.js	120
102\.132\.100\.6[01]	614
102\.132\.101\.6[01]	614
102\.132\.102\.6[01]	614
102\.132\.103\.6[01]	614
102\.132\.104\.6[01]	614
102\.132\.105\.6[01]	614
102\.132\.106\.6[01]	614
102\.132\.107\.6[01]	614
102\.132\.108\.6[01]	614
102\.132\.109\.6[01]	614
102\.132\.110\.6[01]	614
102\.132\.111\.6[01]	614
102\.132\.96\.5[45]	614
102\.132\.97\.5[45]	614
102\.132\.98\.6[01]	614
102\.132\.99\.6[01]	614
103\.115\.185\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
104\.224\.4[0-7]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	569
107\.(12[0-7]|1[01][0-9]|[7-9][0-9]|6[4-9])(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	1421
108\.161\.147\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1321
108\.168\.174\.(3[01]|[12]?[0-9])	614
108\.168\.176\.(25[0-5]|2[0-4][0-9]|19[2-9])	614
108\.168\.177\.(3[01]|[12]?[0-9])	614
108\.168\.254\.65	614
108\.168\.255\.224	614
108\.168\.255\.227	614
128\.116\.(12[0-7]|1[01][0-9]|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1465
129\.192(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	1421
139\.28\.216\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
139\.28\.217\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
139\.28\.218\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
139\.28\.219\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
141\.207(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	1421
141\.98\.100\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
141\.98\.101\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
141\.98\.102\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
141\.98\.103\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
149\.154\.(17[01]|16[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1416
149\.154\.175\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1416
149\.154\.17[01]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1416
152\.89\.160\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
152\.89\.161\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
152\.89\.162\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
152\.89\.163\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
154\.83\.21\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
154\.83\.6\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
154\.83\.7\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
154\.95\.34\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
154\.95\.35\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
154\.95\.36\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
154\.95\.37\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
156\.237\.12\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
156\.237\.13\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
156\.237\.14\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
156\.237\.15\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
157\.240\.0\.6[01]	614
157\.240\.10\.53	614
157\.240\.10\.54	614
157\.240\.11\.53	614
157\.240\.11\.54	614
157\.240\.12\.53	614
157\.240\.12\.54	614
157\.240\.13\.5[45]	614
157\.240\.14\.5[23]	614
157\.240\.15\.53	614
157\.240\.15\.54	614
157\.240\.16\.5[23]	614
157\.240\.17\.6[01]	614
157\.240\.18\.5[23]	614
157\.240\.192\.52	614
157\.240\.192\.55	614
157\.240\.193\.50	614
157\.240\.193\.55	614
157\.240\.194\.5[45]	614
157\.240\.195\.54	614
157\.240\.195\.56	614
157\.240\.196\.6[01]	614
157\.240\.19\.53	614
157\.240\.19\.54	614
157\.240\.1\.53	614
157\.240\.1\.54	614
157\.240\.201\.6[01]	614
157\.240\.202\.6[01]	614
157\.240\.203\.6[01]	614
157\.240\.204\.6[01]	614
157\.240\.205\.6[01]	614
157\.240\.206\.6[01]	614
157\.240\.207\.6[01]	614
157\.240\.208\.6[01]	614
157\.240\.209\.6[01]	614
157\.240\.20\.5[23]	614
157\.240\.210\.6[01]	614
157\.240\.211\.6[01]	614
157\.240\.212\.6[01]	614
157\.240\.213\.6[01]	614
157\.240\.214\.6[01]	614
157\.240\.215\.6[01]	614
157\.240\.216\.6[01]	614
157\.240\.217\.6[01]	614
157\.240\.218\.6[01]	614
157\.240\.219\.6[01]	614
157\.240\.21\.5[23]	614
157\.240\.220\.6[01]	614
157\.240\.221\.6[01]	614
157\.240\.222\.6[01]	614
157\.240\.223\.6[01]	614
157\.240\.22\.53	614
157\.240\.22\.54	614
157\.240\.23\.53	614
157\.240\.23\.54	614
157\.240\.24\.5[45]	614
157\.240\.25\.5[45]	614
157\.240\.26\.5[45]	614
157\.240\.27\.5[45]	614
157\.240\.28\.51	614
157\.240\.28\.55	614
157\.240\.29\.53	614
157\.240\.29\.54	614
157\.240\.2\.53	614
157\.240\.2\.54	614
157\.240\.30\.5[45]	614
157\.240\.31\.6[01]	614
157\.240\.3\.5[45]	614
157\.240\.4\.6[01]	614
157\.240\.5\.6[01]	614
157\.240\.6\.53	614
157\.240\.6\.54	614
157\.240\.7\.53	614
157\.240\.7\.54	614
157\.240\.8\.53	614
157\.240\.8\.54	614
157\.240\.9\.53	614
157\.240\.9\.54	614
158\.115\.(1[3-5][0-9]|12[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1321
158\.85\.0\.(12[0-7]|1[01][0-9]|9[6-9])	614
158\.85\.224\.(19[01]|1[6-8][0-9])	614
158\.85\.233\.(6[0-3]|[45][0-9]|3[2-9])	614
158\.85\.46\.(1[3-5][0-9]|12[89])	614
158\.85\.48\.(25[0-5]|2[34][0-9]|22[4-9])	614
158\.85\.58\.(12[0-7]|1[01][0-9]|9[6-9])	614
158\.85\.58\.(6[0-3]|[45][0-9]|3[2-9])	614
158\.85\.5\.(22[0-3]|2[01][0-9]|19[2-9])	614
158\.85\.61\.(22[0-3]|2[01][0-9]|19[2-9])	614
162\.115(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	1421
165\.138(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	946
165\.138\.169\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
165\.138\.251\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
165\.138\.88\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
165\.139(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	946
165\.139\.(21[01]|20[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
165\.139\.157\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
165\.139\.32\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
165\.27\.248\.(19[01]|1[3-8][0-9]|12[89])	1421
166\.(25[0-5]|2[0-4][0-9]|1[3-9][0-9]|12[89])(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	1421
168\.213\.25[45]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
169\.139\.(1[0-5]|[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
169\.139\.[0-7]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
169\.44\.36\.(6[0-3]|[1-5]?[0-9])	614
169\.44\.80\.(6[0-3]|[45][0-9]|3[2-9])	614
169\.44\.82\.(12[0-7]|1[01][0-9]|9[6-9])	614
169\.44\.84\.(1[3-5][0-9]|12[89])	614
169\.45\.169\.(22[0-3]|2[01][0-9]|19[2-9])	614
169\.45\.210\.(9[0-5]|[78][0-9]|6[4-9])	614
169\.45\.214\.(25[0-5]|2[34][0-9]|22[4-9])	614
169\.45\.219\.(25[0-5]|2[34][0-9]|22[4-9])	614
169\.45\.238\.(6[0-3]|[45][0-9]|3[2-9])	614
169\.45\.242\.(25[0-5]|2[0-4][0-9]|19[2-9])	614
169\.45\.248\.(12[0-7]|1[01][0-9]|9[6-9])	614
169\.45\.248\.(19[01]|1[6-8][0-9])	614
169\.45\.71\.(6[0-3]|[45][0-9]|3[2-9])	614
169\.45\.87\.(1[3-5][0-9]|12[89])	614
169\.47\.130\.(12[0-7]|1[01][0-9]|9[6-9])	614
169\.47\.153\.(25[0-5]|2[0-4][0-9]|1[3-9][0-9]|12[89])	614
169\.47\.212\.(19[01]|1[6-8][0-9])	614
169\.47\.35\.(6[0-3]|[45][0-9]|3[2-9])	614
169\.47\.40\.(1[3-5][0-9]|12[89])	614
169\.47\.42\.(12[0-7]|1[01][0-9]|9[6-9])	614
169\.47\.42\.(19[01]|1[6-8][0-9])	614
169\.47\.42\.(25[0-5]|2[0-4][0-9]|19[2-9])	614
169\.47\.5\.(25[0-5]|2[0-4][0-9]|19[2-9])	614
169\.47\.6\.(9[0-5]|[78][0-9]|6[4-9])	614
169\.53\.71\.(25[0-5]|2[34][0-9]|22[4-9])	614
169\.53\.81\.(9[0-5]|[78][0-9]|6[4-9])	614
169\.54\.222\.(1[3-5][0-9]|12[89])	614
169\.54\.51\.(6[0-3]|[45][0-9]|3[2-9])	614
169\.54\.55\.(22[0-3]|2[01][0-9]|19[2-9])	614
169\.55\.60\.148	614
169\.55\.60\.170	614
169\.55\.67\.(25[0-5]|2[34][0-9]|22[4-9])	614
169\.55\.69\.(19[01]|1[6-8][0-9])	614
169\.55\.74\.(6[0-3]|[45][0-9]|3[2-9])	614
169\.55\.75\.(12[0-7]|1[01][0-9]|9[6-9])	614
169\.60\.147\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	614
169\.60\.75\.(25[0-5]|2[0-4][0-9]|1[3-9][0-9]|12[89])	614
169\.60\.79\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	614
169\.61\.101\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	614
169\.62\.159\.(3[01]|2[0-9]|1[6-9])	614
169\.63\.64\.(14[0-3]|13[0-9]|12[89])	614
169\.63\.73\.(12[0-7]|1[01][0-9]|[7-9][0-9]|6[4-9])	614
169\.63\.73\.(6[0-3]|[45][0-9]|3[2-9])	614
169\.63\.76\.(12[0-7]|1[01][0-9]|[1-9]?[0-9])	614
172\.111\.143\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
172\.111\.153\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
172\.111\.167\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
172\.111\.222\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
172\.111\.243\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
172\.94\.104\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
172\.94\.114\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
172\.94\.53\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
172\.94\.65\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
172\.94\.89\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
173\.192\.162\.(6[0-3]|[45][0-9]|3[2-9])	614
173\.192\.222\.(19[01]|1[6-8][0-9])	614
173\.193\.230\.(1[3-5][0-9]|12[89])	614
173\.193\.239\.(3[01]|[12]?[0-9])	614
173\.227\.69\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
173\.243\.(14[0-3]|13[0-9]|12[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1275
174\.36\.208\.(1[3-5][0-9]|12[89])	614
174\.36\.210\.(6[0-3]|[45][0-9]|3[2-9])	614
174\.37\.243\.(9[0-5]|[78][0-9]|6[4-9])	614
176\.10\.82\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
176\.10\.83\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
176\.10\.84\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
176\.10\.86\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
176\.10\.87\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
176\.10\.8[0-7]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
176\.10\.8[01]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
176\.10\.8[4-7]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
176\.113\.72\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
176\.113\.73\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
176\.113\.74\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
176\.113\.75\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
179\.60\.192\.49	614
179\.60\.192\.51	614
179\.60\.193\.5[23]	614
179\.60\.194\.53	614
179\.60\.194\.54	614
179\.60\.195\.49	614
179\.60\.195\.51	614
184(\.173){2}\.116	614
184\.173\.136\.(9[0-5]|[78][0-9]|6[4-9])	614
184\.173\.147\.(6[0-3]|[45][0-9]|3[2-9])	614
184\.173\.161\.64	614
185\.104\.184\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.104\.185\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.104\.186\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.104\.187\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.104\.18[4-7]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.114\.33\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.120\.14[4-7]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.123\.14[01]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.123\.14[23]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.128\.24\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.128\.25\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.128\.26\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.128\.27\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.130\.184\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.130\.186\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.141\.119\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.156\.172\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.156\.173\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.156\.174\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.156\.175\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.163\.10[89]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.163\.11[01]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.181\.10[01]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.181\.10[23]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.183\.104\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.183\.105\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.183\.106\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.183\.107\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.189\.112\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.189\.113\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.189\.114\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.189\.115\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.195\.200\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.195\.201\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.195\.202\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.200\.116\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.200\.117\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.200\.118\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.200\.119\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.206\.224\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.206\.225\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.206\.227\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.210\.217\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.210\.219\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.210\.21[67]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.212\.168\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.212\.169\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.212\.170\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.212\.171\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.216\.33\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.216\.34\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.216\.35\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.217\.69\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.217\.70\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.217\.71\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.220\.68\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.220\.69\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.220\.70\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.220\.71\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.227\.40\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.227\.41\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.227\.42\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.227\.43\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.230\.124\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.230\.125\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.230\.126\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.230\.127\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.232\.20\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.232\.21\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.232\.22\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.232\.23\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.236\.201\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.236\.202\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.236\.203\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.242\.4\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.242\.6\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.242\.7\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.244\.212\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.244\.213\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.244\.214\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.244\.215\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.245\.84\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.245\.85\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.245\.86\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.253\.96\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.253\.97\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.253\.98\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.253\.99\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.45\.14\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.45\.15\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.45\.1[23]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.60\.216\.53	614
185\.60\.216\.54	614
185\.60\.217\.53	614
185\.60\.217\.54	614
185\.60\.218\.53	614
185\.60\.218\.54	614
185\.60\.219\.53	614
185\.60\.219\.54	614
185\.91\.23[67]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.92\.175\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.93\.180\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.93\.181\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.93\.182\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.93\.183\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.93\.18[0-3]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.94\.(19[01]|18[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.94\.188\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.94\.189\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.94\.190\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.94\.192\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.94\.193\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.94\.194\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.94\.19[2-5]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.9\.18\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.9\.19\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
185\.9\.1[6-9]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
188\.213\.242\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
188\.240\.220\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
192(\.253){2}\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
192\.145\.124\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
192\.145\.125\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
192\.145\.126\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
192\.145\.127\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
192\.145\.69\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
192\.155\.212\.(22[0-3]|2[01][0-9]|19[2-9])	614
192\.253\.240\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
193\.148\.16\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
193\.148\.17\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
193\.148\.18\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
193\.148\.19\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
193\.164\.128\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
193\.176\.84\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
193\.176\.85\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
193\.176\.86\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
193\.176\.87\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
193\.189\.7[45]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
193\.37\.252\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
193\.37\.253\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
193\.37\.254\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
193\.37\.255\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
193\.9\.112\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
193\.9\.113\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
193\.9\.114\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
193\.9\.115\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
194\.187\.(25[01]|24[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
194\.187\.248\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
194\.187\.249\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
194\.187\.251\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
194\.36\.108\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
194\.36\.110\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
194\.36\.111\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
194\.59\.248\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
194\.59\.249\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
194\.59\.250\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
194\.59\.251\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
194\.79\.(3[01]|2[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
194\.99\.104\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
194\.99\.105\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
194\.99\.106\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
194\.99\.107\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
195\.12\.48\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
195\.12\.49\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
195\.12\.4[89]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
195\.12\.50\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
195\.206\.105\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
195\.206\.106\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
195\.206\.107\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
195\.242\.213\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
195\.242\.21[2-5]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
195\.8\.19[67]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
198\.11\.193\.18[23]	614
198\.11\.251\.(6[0-3]|[45][0-9]|3[2-9])	614
198\.230\.(23[0-9]|22[4-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1421
198\.23\.80\.(3[01]|[12]?[0-9])	614
199\.200\.5[01]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	803
199\.231\.78\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1321
199\.58\.255\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
199\.60\.116\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	803
202\.177\.213\.(12[0-7]|1[01][0-9]|9[6-9])	565
203\.96\.(22[0-3]|2[01][0-9]|19[2-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1421
204\.63\.(18[0-3]|17[6-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
206\.123\.140\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
207\.191\.(18[0-3]|17[6-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
207\.191\.(19[01]|18[0-9]|17[6-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
207\.191\.(19[01]|18[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
207\.191\.18[4-7]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
208\.119(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	946
208\.43\.115\.(22[0-3]|2[01][0-9]|19[2-9])	614
208\.54(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	1421
208\.91\.11[2-5]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1275
208\.91\.15[6-9]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	803
209\.206\.(6[0-3]|5[0-9]|4[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1321
212\.103\.48\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
212\.103\.49\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
212\.103\.50\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
212\.103\.51\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
213\.230\.203\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
216\.151\.(1[3-5][0-9]|12[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1419
216\.157\.(14[0-3]|13[0-9]|12[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1321
217\.138\.203\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
217\.151\.(11[01]|10[0-9]|9[6-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
217\.64\.(12[0-7]|11[2-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
217\.64\.113\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
217\.64\.114\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
217\.64\.127\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
2\.58\.44\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
2\.58\.45\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
2\.58\.46\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
2\.58\.47\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
31\.13\.188\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
31\.13\.189\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
31\.13\.190\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
31\.13\.191\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
31\.13\.64\.51	614
31\.13\.64\.53	614
31\.13\.65\.49	614
31\.13\.65\.50	614
31\.13\.66\.51	614
31\.13\.66\.56	614
31\.13\.67\.5[23]	614
31\.13\.68\.48	614
31\.13\.68\.52	614
31\.13\.69\.6[01]	614
31\.13\.70\.49	614
31\.13\.70\.50	614
31\.13\.71\.49	614
31\.13\.71\.50	614
31\.13\.72\.48	614
31\.13\.72\.52	614
31\.13\.73\.5[23]	614
31\.13\.74\.5[23]	614
31\.13\.75\.6[01]	614
31\.13\.76\.6[01]	614
31\.13\.77\.6[01]	614
31\.13\.78\.6[01]	614
31\.13\.79\.53	614
31\.13\.79\.54	614
31\.13\.80\.48	614
31\.13\.80\.53	614
31\.13\.81\.48	614
31\.13\.81\.53	614
31\.13\.82\.51	614
31\.13\.82\.55	614
31\.13\.83\.49	614
31\.13\.83\.51	614
31\.13\.84\.49	614
31\.13\.84\.51	614
31\.13\.85\.49	614
31\.13\.85\.51	614
31\.13\.86\.49	614
31\.13\.86\.51	614
31\.13\.87\.48	614
31\.13\.87\.51	614
31\.13\.88\.6[01]	614
31\.13\.89\.53	614
31\.13\.89\.54	614
31\.13\.90\.49	614
31\.13\.90\.51	614
31\.13\.91\.6[01]	614
31\.13\.92\.48	614
31\.13\.92\.52	614
31\.13\.93\.53	614
31\.13\.93\.54	614
31\.13\.94\.52	614
31\.13\.94\.54	614
31\.13\.95\.50	614
31\.13\.95\.63	614
31\.14\.252\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.129\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.130\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.131\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.132\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.133\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.134\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.135\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.136\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.137\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.138\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.139\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.140\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.141\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.142\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.143\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.144\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.145\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.146\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.148\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.149\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.151\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.152\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.153\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.154\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.155\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.156\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.158\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.159\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.192\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.193\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.194\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.195\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.196\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.197\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.198\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.199\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.200\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.201\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.202\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.203\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.204\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.205\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.206\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.207\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.208\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.209\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.210\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.211\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.212\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.213\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.215\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.216\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.217\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.218\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.219\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.221\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.222\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.120\.223\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.156\.184\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1318
37\.156\.185\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1318
37\.156\.187\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1318
37\.156\.18[45]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1318
37\.221\.112\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.221\.113\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.221\.114\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
37\.221\.115\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
38\.132\.125\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
38\.29\.170\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
45\.12\.220\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.12\.221\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.12\.222\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.12\.223\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.130\.176\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.130\.177\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.130\.179\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.133\.181\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.133\.182\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.133\.183\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.141\.152\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.141\.153\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.152\.180\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.152\.181\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.152\.182\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.152\.183\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.74\.36\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.83\.88\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.83\.90\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.83\.91\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.87\.213\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.87\.214\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.89\.172\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.89\.174\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.89\.175\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.92\.32\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.92\.33\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.92\.34\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.92\.35\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.9\.248\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.9\.249\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.9\.250\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
45\.9\.251\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
46\.243\.223\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
50\.22\.240\.(19[01]|1[6-8][0-9])	614
50\.23\.90\.(1[3-5][0-9]|12[89])	614
5\.181\.232\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
5\.181\.233\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
5\.181\.234\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
5\.181\.235\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
5\.253\.204\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
5\.253\.205\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
5\.253\.206\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
5\.253\.207\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
66\.111\.48\.12	614
66\.111\.49\.12	614
66\.250\.19[01]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
66\.94\.(3[01]|[12]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1421
69\.171\.239\.11	614
69\.171\.250\.52	614
69\.171\.250\.54	614
69\.171\.255\.11	614
72\.162\.13[6-9]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
77\.243\.(19[01]|18[0-9]|17[6-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
77\.243\.176\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
77\.243\.183\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
77\.243\.187\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
77\.243\.188\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
77\.243\.189\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
77\.243\.190\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
77\.243\.191\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
77\.81\.98\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
81\.92\.20[0-3]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
81\.92\.20[4-7]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
82\.102\.16\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
82\.102\.17\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
82\.102\.18\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
82\.102\.19\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
82\.102\.20\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
82\.102\.21\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
82\.102\.22\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
82\.102\.23\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
82\.102\.24\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
82\.102\.25\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
82\.102\.27\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
82\.102\.28\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
83\.143\.243\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
83\.143\.245\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
83\.143\.246\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
83\.143\.247\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
83\.143\.24[0-3]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
83\.143\.24[0-7]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
83\.143\.24[45]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
83\.97\.22\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
83\.97\.23\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
84\.39\.112\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
84\.39\.113\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
84\.39\.115\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
84\.39\.118\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
84\.39\.119\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
84\.39\.11[2-9]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
84\.39\.11[45]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
85\.204\.124\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
86\.105\.25\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
86\.105\.9\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
86\.106\.137\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
87\.101\.92\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
87\.101\.93\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
87\.101\.94\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
87\.101\.95\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
88\.218\.100\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
88\.218\.101\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89(\.40){2}\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.238\.(19[01]|1[3-8][0-9]|12[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.238\.12[89]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.238\.130\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.238\.13[2-5]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.238\.13[6-9]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.238\.14[0-3]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.238\.150\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.238\.154\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.238\.163\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.238\.176\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.238\.177\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.238\.178\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.238\.180\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.238\.183\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.238\.184\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.238\.187\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.238\.191\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.249\.(7[0-9]|6[4-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.249\.65\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.249\.73\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.249\.74\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.33\.246\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.33\.8\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.36\.224\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.40\.181\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.40\.219\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.40\.71\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.45\.10\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
89\.46\.10[0-3]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
8\.28\.12[45]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	803
91\.102\.(7[01]|6[4-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
91\.108\.1[6-9]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1416
91\.108\.56\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1416
91\.108\.5[67]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1416
91\.132\.136\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
91\.132\.137\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
91\.132\.138\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
91\.132\.139\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
91\.195\.9[89]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
91\.207\.10[23]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
91\.207\.172\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
91\.207\.173\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
91\.207\.174\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
91\.207\.56\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
91\.207\.57\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
91\.207\.5[67]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
91\.232\.15[01]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
92\.119\.178\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
93\.115\.7\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
93\.120\.(5[0-5]|4[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
93\.120\.27\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
93\.120\.5[67]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
93\.177\.72\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
93\.177\.73\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
93\.177\.74\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
93\.177\.75\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
94\.176\.148\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
95\.174\.64\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
95\.174\.65\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
95\.174\.66\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
95\.174\.67\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	1234
96\.4\.(12[0-7]|1[01][0-9]|[7-9][0-9]|6[4-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.4\.(19[01]|1[3-8][0-9]|12[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.4\.(1[0-5]|[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.4\.(25[0-5]|2[0-4][0-9]|19[2-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.4\.(6[0-3]|[1-5]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.4\.18[4-7]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.4\.21[01]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.(10[0-3]|9[6-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.(14[0-3]|13[0-9]|12[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.(18[0-3]|17[6-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.(20[0-7]|19[2-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.(21[0-5]|20[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.(22[0-3]|21[6-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.(23[01]|22[4-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.(25[0-5]|24[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.(6[0-3]|[1-5]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.(9[0-5]|8[89])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.(9[0-5]|[78][0-9]|6[4-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.10[89]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.11[01]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.11[2-9]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.120\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.125\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.12[0-7]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.14[4-7]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.15[6-9]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.16[0-7]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.5[6-9]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.96\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.5\.[89]\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])	946
96\.[45](\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){2}	946
^(https?://)?(www\.)?google\.[a-z]{2,4}(?:\.[a-z]{2,4})?/search\?.*?udm=2(&|$)	54
^https?://[^/]+:(81|44[01])/modules/(guardian3|monitor|auth|tunnel|reverseproxy|sip|snmp|ids|dhcp|heartbeat|zone|routing)/cgi-bin/	603
^https?://www\.google\.[a-z]{2,4}(?:\.[a-z]{2,4})?/travel/	242
apis*\.smoothwall\.com	147	603
encrypted-tbn\d\.gstatic\.com/images	550
researchgate.net/publication/\w+/figure/.*?\.jpg	11
sw(cldflt(logs)?|crmgt)prd(\w{3,4})sacc\.blob\.core\.windows\.net	603
sw-(cldflt(logs)?|crmgt)-prd-(\w{3,4})-\d-windowstelemetryfapp\.azurewebsites\.net	603
sw-(shared|devicemgt)-(prd|dev|qa)-.*iothub(?:-\d+)?\.azure-devices\.net	147	603
sw-cldflt-(prd|qa|dev)-[a-z]{3}-[0-9]-policymanagement-api\.azurewebsites\.net	603
sw-global-(dev|qa|prd)-[a-z]{3}-[0-9]-configsignalr\.service\.signalr\.net	603

[{"level": 5, "categories": ["Pornography"], "exclusions": ["Advertising", "Education", " Education"], "name": "Porn"}, {"level": 5, "categories": ["Violence", "Weapons", "Criminal Activity"], "exclusions": ["Advertising", "Military", "Education", " Education"], "name": "Violence"}, {"level": 5, "categories": ["Intolerance", "Terrorism"], "exclusions": ["Advertising", "Education", " Education"], "name": "Intolerance"}, {"level": 5, "categories": ["Drugs", "Alcohol and Tobacco"], "exclusions": ["Advertising", "Education", " Education", "Medical Information"], "name": "Drugs"}, {"level": 5, "categories": ["Self Harm", "Child Abuse"], "exclusions": ["Advertising"], "name": "Self Harm"}, {"level": 2, "categories": ["Search Suggestions", "Advertising"], "exclusions": [], "name": "Search suggestions, Ads"}, {"level": 2, "httpcode": ["40.", "50.", "204"], "exclusions": [], "name": "204, 4xx, 5xx"}, {"level": 2, "categories": ["Tracking", " Tracking", "Content Delivery", " Content Delivery", "URL Shortening"], "contenttype": ["ping"], "exclusions": [], "name": "Content, Shorterning & Site Stats"}, {"level": 2, "contenttype": ["application/json", "application/javascript", "application/x-javascript", "text/javascript", "text/css", "application/xml", "text/xml", "stylesheet", "script", "object", "xmlhttprequest", "websocket"], "exclusions": [], "name": "css, js, json, xml"}, {"level": 2, "contenttype": ["application/font-woff", "font/otf", "font/sfnt", "font/ttf", "font/woff", "font/woff2", "font"], "exclusions": [], "name": "Fonts"}, {"level": 2, "contenttype": ["image/png", "image/x-icon", "image/jpg", "image/jpeg", "image/gif", "image/svg+xml", "image/webp", "image", "media"], "exclusions": [], "name": "Images"}, {"level": 1, "method": ["OPTIONS"], "httpcode": ["30.", "407"], "exclusions": [], "name": "Options"}, {"level": 2, "categories": ["DNS over HTTPS"], "exclusions": [], "name": "DNS over HTTPS"}, {"level": 2, "categories": ["Microsoft Office 365", "Custom allowed content"], "exclusions": [], "name": "O365 & allowed"}, {"level": 1, "categories": ["Smoothwall Products", " Software Updates", "SSL \\/ CRL"], "exclusions": [], "name": "Sw prod & Software updates"}, {"level": 4, "searchterms": [".+"], "exclusions": ["Search Suggestions", "YouTube"], "name": "Searches"}, {"level": 4, "title": [".+"], "exclusions": [], "name": "Title"}, {"level": 1, "categories": ["Transparent HTTPS incompatible sites"], "exclusions": ["Facebook"], "name": "THIS"}, {"level": 2, "categories": ["iTunes/App Store"], "exclusions": [], "name": "Temp iTunes"}, {"level": 2, "categories": ["Connect for Chromebooks"], "exclusions": [], "name": "Background Google requests"}, {"level": 2, "method": ["POST"], "exclusions": [], "name": "POST"}, {"level": 2, "categories": ["Background Requests"], "exclusions": [], "name": "Background Requests"}, {"level": 5, "safeguardingtheme": [".+"], "exclusions": [], "name": "Safeguarding"}]
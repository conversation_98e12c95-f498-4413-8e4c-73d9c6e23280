{"1": {"component": {"domainsurls": 30306, "regexpurls": 12, "weightedphrases": 2}, "example_url": "ads.whatsonstage.com", "filename": "ads", "id": "1", "ignoreme": "0", "name": "Advertising", "new_description": "Advert servers and advert URLs", "newname": "Advertising", "parent": "241", "test_url": "test.mysmoothwall.net/ads"}, "1000": {"component": {"domainsurls": 6}, "example_url": "kahoot.it", "filename": "sphirewall-application-kahoot", "id": "1000", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "Join Kahoot!, where over 30 million empowered educators and captivated learners connect in real-time to create a social, fun and...", "newname": "<PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-kahoot"}, "1001": {"component": {"domainsurls": 1}, "example_url": "kamar.nz", "filename": "sphirewall-applicaiton-kamar-nz", "id": "1001", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "School Administration, Markbook and Reports", "newname": "<PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-kamar-nz"}, "1002": {"component": {"domainsurls": 1}, "example_url": "kctcdata.org", "filename": "sphirewall-application-kctc", "id": "1002", "ignoreme": "0", "name": "KCTC", "new_description": "", "newname": "KCTC", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-kctc"}, "1003": {"component": {"domainsurls": 9}, "example_url": "support.kerboodle.com", "filename": "sphirewall-application-kerboodle", "id": "1003", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-kerboodle"}, "1004": {"component": {"domainsurls": 3}, "example_url": "https://www.kiddom.co/", "filename": "sphirewall-application-kiddom", "id": "1004", "ignoreme": "0", "name": "Kiddom", "new_description": "The Kiddom Education Platform connects curriculum, instruction, and assessment in one place with integrated tools for communication. With Kiddom, schools and districts stay prepared for teaching and learning in any scenario – blended learning, hybrid, or distance learning. \n", "newname": "Kiddom", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-kiddom"}, "1005": {"component": {"domainsurls": 1}, "example_url": "kidexplorer.com", "filename": "sphirewall-application-kidexplorer", "id": "1005", "ignoreme": "0", "name": "Kid Explorer", "new_description": "", "newname": "Kid Explorer", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-kidexplorer"}, "1006": {"component": {"domainsurls": 1}, "example_url": "kidsdiscover.com", "filename": "sphirewall-application-kidsdiscover-com", "id": "1006", "ignoreme": "0", "name": "Kids Discover", "new_description": "", "newname": "Kids Discover", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-kidsdiscover-com"}, "1007": {"component": {"domainsurls": 1}, "example_url": "kidztype.com", "filename": "sphirewall-applicaiton-kidztype-com", "id": "1007", "ignoreme": "0", "name": "KidzType", "new_description": "", "newname": "KidzType", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-kidztype-com"}, "1008": {"component": {"domainsurls": 3}, "example_url": "ksassessments.org", "filename": "sphirewall-application-kiteassesments", "id": "1008", "ignoreme": "0", "name": "Kite Assessments", "new_description": "", "newname": "Kite Assessments", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-kiteassesments"}, "1009": {"component": {"domainsurls": 1}, "example_url": "kiwikidsnews.co.nz", "filename": "sphirewall-application-kiwikidnews", "id": "1009", "ignoreme": "0", "name": "Kiwi Kid News", "new_description": "News that is out of this world.", "newname": "Kiwi Kid News", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-kiwikidnews"}, "1010": {"component": {"domainsurls": 3}, "example_url": "languagesonline.org.uk", "filename": "sphirewall-application-languagesonline", "id": "1010", "ignoreme": "0", "name": "Languages Online", "new_description": "love learning language\n", "newname": "Languages Online", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-languagesonline"}, "1011": {"component": {"domainsurls": 1}, "example_url": "learning.com", "filename": "sphirewall-application-learning", "id": "1011", "ignoreme": "0", "name": "Learning", "new_description": "", "newname": "Learning", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-learning"}, "1012": {"component": {"domainsurls": 1}, "example_url": "learningally.org", "filename": "sphirewall-application-learningally", "id": "1012", "ignoreme": "0", "name": "Learning Ally", "new_description": "", "newname": "Learning Ally", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-learningally"}, "1013": {"component": {"domainsurls": 6}, "example_url": "learninga-z.com", "filename": "sphirewall-application-learningaz", "id": "1013", "ignoreme": "0", "name": "Learning a-z", "new_description": "", "newname": "Learning a-z", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-learningaz"}, "1014": {"component": {"domainsurls": 1}, "example_url": "learningblade.com", "filename": "sphirewall-application-learningblade", "id": "1014", "ignoreme": "0", "name": "Learning Blade", "new_description": "", "newname": "Learning Blade", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-learningblade"}, "1015": {"component": {"domainsurls": 4}, "example_url": "learningexpresshub.com", "filename": "sphirewall-application-learningexpresshub", "id": "1015", "ignoreme": "0", "name": "Learning Express Hub", "new_description": "", "newname": "Learning Express Hub", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-learningexpresshub"}, "1016": {"component": {"domainsurls": 3}, "example_url": "lwtears.com", "filename": "sphirewall-application-lwtears", "id": "1016", "ignoreme": "0", "name": "Learning Without Tears", "new_description": "", "newname": "Learning Without Tears", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-lwtears"}, "1017": {"component": {"domainsurls": 3}, "example_url": "learnosity.com", "filename": "sphirewall-application-learnosity", "id": "1017", "ignoreme": "0", "name": "Learnosity", "new_description": "", "newname": "Learnosity", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-learnosity"}, "1018": {"component": {"domainsurls": 6}, "example_url": "learnzillion.com", "filename": "sphirewall-application-learnzillion", "id": "1018", "ignoreme": "0", "name": "Learn <PERSON>", "new_description": "", "newname": "Learn <PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-learnzillion"}, "1019": {"component": {"domainsurls": 3}, "example_url": "frontend.letsgolearn.com/login", "filename": "sphirewall-application-<PERSON><PERSON><PERSON><PERSON>", "id": "1019", "ignoreme": "0", "name": "Lets Go Learn", "new_description": "Diagnostic Assessments and Personalized Learning for Special Education and K-12", "newname": "Lets Go Learn", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-letsgolearn"}, "1020": {"component": {"domainsurls": 1}, "example_url": "lexiapowerup.com", "filename": "sphirewall-application-lexiapowerup", "id": "1020", "ignoreme": "0", "name": "Lexiapowerup", "new_description": "", "newname": "Lexiapowerup", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-lexiapowerup"}, "1021": {"component": {"domainsurls": 4}, "example_url": "lexialearning.com", "filename": "sphirewall-application-lexiacore5-com", "id": "1021", "ignoreme": "0", "name": "Lexia Reading Core", "new_description": "", "newname": "Lexia Reading Core", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-lexiacore5-com"}, "1022": {"component": {"domainsurls": 3}, "example_url": "lifeprint.com", "filename": "sphirewall-application-lifeprint", "id": "1022", "ignoreme": "0", "name": "Life Print", "new_description": "", "newname": "Life Print", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-lifeprint"}, "1023": {"component": {}, "example_url": "linc-ed.com", "filename": "sphirewall-application-linc-ed-com", "id": "1023", "ignoreme": "0", "name": "LINC-ED", "new_description": "", "newname": "LINC-ED", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-linc-ed-com"}, "1024": {"component": {"domainsurls": 1}, "example_url": "linguascope.com", "filename": "sphirewall-application-linguascope", "id": "1024", "ignoreme": "0", "name": "Linguascope", "new_description": "The World's Number 1 Interactive Language Learning Platform for Schools – Learn French, Spanish, German, Italian, Russian.", "newname": "Linguascope", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-linguascope"}, "1025": {"component": {"domainsurls": 3}, "example_url": "http://www.literacyplanet.com", "filename": "sphirewall-applicaiton-literacyplanet-com", "id": "1025", "ignoreme": "0", "name": "Literacy Planet", "new_description": "", "newname": "Literacy Planet", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-literacyplanet-com"}, "1026": {"component": {"domainsurls": 3}, "example_url": "literacyshed.com", "filename": "sphirewall-application-literacyshed", "id": "1026", "ignoreme": "0", "name": "LiteracyShed", "new_description": "A collection of Reading and Literacy resources for teachers and students", "newname": "LiteracyShed", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-literacyshed"}, "1027": {"component": {"domainsurls": 5}, "example_url": "littlealchemy.com", "filename": "sphirewall-applicaiton-littlealchemy-com", "id": "1027", "ignoreme": "0", "name": "Little Alchemy", "new_description": "", "newname": "Little Alchemy", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-littlealchemy-com"}, "1028": {"component": {"domainsurls": 1}, "example_url": "learn-locorobo.com", "filename": "sphirewall-application-locorobo", "id": "1028", "ignoreme": "0", "name": "LocoRobo", "new_description": "LocoRobo.com", "newname": "LocoRobo", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-locorobo"}, "1029": {"component": {"domainsurls": 1}, "example_url": "lumoslearning.com", "filename": "sphirewall-application-lumoslearning", "id": "1029", "ignoreme": "0", "name": "lumoslearning", "new_description": "", "newname": "lumoslearning", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-lumoslearning"}, "1030": {"component": {"domainsurls": 1}, "example_url": "mad4maths.com", "filename": "sphirewall-application-mad4math", "id": "1030", "ignoreme": "0", "name": "Mad4Maths", "new_description": "A fun ks2 numeracy website to test numeracy skills.", "newname": "Mad4Maths", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mad4math"}, "1031": {"component": {"domainsurls": 1}, "example_url": "mangahigh.com", "filename": "sphirewall-application-mangahigh", "id": "1031", "ignoreme": "0", "name": "Mangahigh", "new_description": "", "newname": "Mangahigh", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mangahigh"}, "1032": {"component": {}, "example_url": "sso.mapnwea.org", "filename": "sphirewall-application-mapnwea", "id": "1032", "ignoreme": "0", "name": "mapnwea", "new_description": "", "newname": "mapnwea", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mapnwea"}, "1033": {"component": {"domainsurls": 1}, "example_url": "masteryconnect.com", "filename": "sphirewall-application-masteryconnect", "id": "1033", "ignoreme": "0", "name": "MasteryConnect", "new_description": "", "newname": "MasteryConnect", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-masteryconnect"}, "1034": {"component": {"domainsurls": 1}, "example_url": "mathblaster.com", "filename": "sphirewall-application-mathblaster", "id": "1034", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON><PERSON>", "new_description": "MathBlaster.com is a cool, online math virtual world filled with wacky aliens, cool gadgets, and fun math games for boys and girls.", "newname": "<PERSON><PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mathblaster"}, "1035": {"component": {"domainsurls": 1}, "example_url": "mathematicshed.com", "filename": "sphirewall-application-mathematicshed", "id": "1035", "ignoreme": "0", "name": "MathematicShed", "new_description": "A website to inspire creativity in maths", "newname": "MathematicShed", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mathematicshed"}, "1036": {"component": {"domainsurls": 7}, "example_url": "mathletics.com", "filename": "sphirewall-application-mathletics", "id": "1036", "ignoreme": "0", "name": "Mathletics", "new_description": "Mathletics is the next generation in online math learning platform, helping students enjoy math and improve their results.", "newname": "Mathletics", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mathletics"}, "1037": {"component": {"domainsurls": 3}, "example_url": "mathnation.com", "filename": "sphirewall-application-mathnation", "id": "1037", "ignoreme": "0", "name": "Math Nation", "new_description": "", "newname": "Math Nation", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mathnation"}, "1038": {"component": {"domainsurls": 1}, "example_url": "mathpapa.com", "filename": "sphirewall-application-mathpapa", "id": "1038", "ignoreme": "0", "name": "<PERSON>", "new_description": "", "newname": "<PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mathpapa"}, "1039": {"component": {"domainsurls": 1}, "example_url": "mathplayground.com", "filename": "sphirewall-application-mathplayground", "id": "1039", "ignoreme": "0", "name": "MathPlayground", "new_description": "Play with math and give your brain a workout! Math Playground is filled with 100s of math games, logic puzzles and math word problems.", "newname": "MathPlayground", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mathplayground"}, "104": {"component": {"domainsurls": 11909, "regexpurls": 1, "searchterms": 32, "weightedphrases": 1356}, "example_url": "www.ign.com", "filename": "games", "id": "104", "ignoreme": "0", "name": " Gaming Resources", "new_description": "Sites related to computer games but not in browser playable games", "newname": " Gaming Resources", "parent": "1471", "test_url": "test.mysmoothwall.net/games"}, "1040": {"component": {"domainsurls": 1}, "example_url": "mathsbot.com", "filename": "sphirewall-application-mathsbot", "id": "1040", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mathsbot"}, "1041": {"component": {"domainsurls": 5}, "example_url": "mathsbuddy.co.nz", "filename": "sphirewall-application-mathsbuddy", "id": "1041", "ignoreme": "0", "name": "Maths Buddy", "new_description": "", "newname": "Maths Buddy", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mathsbuddy"}, "1042": {"component": {"domainsurls": 1}, "example_url": "mathfactspro.com", "filename": "sphirewall-application-mathsfactspro", "id": "1042", "ignoreme": "0", "name": "Maths Facts Pro", "new_description": "A paid subscription Mathematics education platform", "newname": "Maths Facts Pro", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mathsfactspro"}, "1043": {"component": {"domainsurls": 1}, "example_url": "mathsgenie.co.uk", "filename": "sphirewall-application-mathsgenie", "id": "1043", "ignoreme": "0", "name": "Maths <PERSON>ie", "new_description": "Everyone can learn maths. People can feel that they are not good at maths when they are learning the wrong topics, or when they move on too quickly.", "newname": "Maths <PERSON>ie", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mathsgenie"}, "1044": {"component": {"domainsurls": 1}, "example_url": "mathsisfun.com", "filename": "sphirewall-application-mathsisfun-com", "id": "1044", "ignoreme": "0", "name": "Maths is Fun", "new_description": "", "newname": "Maths is Fun", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mathsisfun-com"}, "1045": {"component": {"domainsurls": 1}, "example_url": "mathspace.co", "filename": "sphirewall-application-mathspace-co", "id": "1045", "ignoreme": "0", "name": "Mathspace", "new_description": "", "newname": "Mathspace", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mathspace-co"}, "1046": {"component": {"domainsurls": 1}, "example_url": "mathspad.co.uk", "filename": "sphirewall-application-mathspad", "id": "1046", "ignoreme": "0", "name": "Mathspad", "new_description": "MathsPad is a growing collection of interactive and paper-based resources designed to support an engaging and rigorous experience of mathematics for pupils from upper KS2 to KS4.", "newname": "Mathspad", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mathspad"}, "1047": {"component": {"domainsurls": 1}, "example_url": "whizz.com", "filename": "sphirewall-application-mathswizz", "id": "1047", "ignoreme": "0", "name": "Maths Wizz", "new_description": "Maths-Whizz improves students' confidence and ability in maths. Find out more about the virtual online maths tutor for 5 - 13 year olds.", "newname": "Maths Wizz", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mathswizz"}, "1048": {"component": {"domainsurls": 3}, "example_url": "mathxl.com", "filename": "sphirewall-application-mathxl", "id": "1048", "ignoreme": "0", "name": "Math XL", "new_description": "Needed for Focus", "newname": "Math XL", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mathxl"}, "1049": {"component": {"domainsurls": 1}, "example_url": "matific.com", "filename": "sphirewall-applicaiton-matific", "id": "1049", "ignoreme": "0", "name": "Matific", "new_description": "Math games and worksheets for kids.", "newname": "Matific", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-matific"}, "1050": {"component": {"domainsurls": 1}, "example_url": "membean.com", "filename": "sphirewall-application-membean", "id": "1050", "ignoreme": "0", "name": "membean", "new_description": "Membean provides guided, engaging, multimodal vocabulary instruction while our Adaptive Reinforcement Engine helps students retain what they learn.", "newname": "membean", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-membean"}, "1051": {"component": {"domainsurls": 5}, "example_url": "mentoringminds.com", "filename": "sphirewall-application-mentoringminds", "id": "1051", "ignoreme": "0", "name": "Mentoring Minds", "new_description": "", "newname": "Mentoring Minds", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mentoringminds"}, "1052": {"component": {"domainsurls": 14}, "example_url": "my.mheducation.com/login", "filename": "sphirewall-application-mheducation", "id": "1052", "ignoreme": "0", "name": "mheducation", "new_description": "", "newname": "mheducation", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mheducation"}, "1053": {"component": {"domainsurls": 1}, "example_url": "mindplay.com", "filename": "sphirewall-application-mindplay", "id": "1053", "ignoreme": "0", "name": "Mindplay", "new_description": "Educational software teaching reading and mathematics delivering structured and systematic learning instruction to students", "newname": "Mindplay", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mindplay"}, "1054": {"component": {"domainsurls": 8}, "example_url": "mobymax.com", "filename": "sphirewall-application-mobymax", "id": "1054", "ignoreme": "0", "name": "mobymax", "new_description": "", "newname": "mobymax", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mobymax"}, "1055": {"component": {"domainsurls": 5}, "example_url": "moodle.org.nz", "filename": "sphirewall-application-moodle", "id": "1055", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "Moodle is an Internet based system for delivering e-Learning programmes for educational and training organisations. ", "newname": "<PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-moodle"}, "1056": {"component": {"domainsurls": 1}, "example_url": "mosamack.com", "filename": "sphirewall-application-mosamack", "id": "1056", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mosamack"}, "1057": {"component": {"domainsurls": 1}, "example_url": "mrnussbaum.com", "filename": "sphirewall-application-mrnussbaum-com", "id": "1057", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mrnussbaum-com"}, "1058": {"component": {"domainsurls": 3}, "example_url": "mymaths.co.uk", "filename": "sphirewall-application-mymaths", "id": "1058", "ignoreme": "0", "name": "MyMaths", "new_description": "", "newname": "MyMaths", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mymaths"}, "1059": {"component": {"domainsurls": 1}, "example_url": "myopenmath.com", "filename": "sphirewall-application-myopenmath", "id": "1059", "ignoreme": "0", "name": "MyOpenMath", "new_description": "", "newname": "MyOpenMath", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-myopenmath"}, "1060": {"component": {"domainsurls": 3}, "example_url": "mysigningtime.com", "filename": "sphirewall-application-mysigningtime", "id": "1060", "ignoreme": "0", "name": "My Signing Time", "new_description": "", "newname": "My Signing Time", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-mysigningtime"}, "1061": {"component": {"domainsurls": 1}, "example_url": "myvrspot.com", "filename": "sphirewall-application-myvrspot", "id": "1061", "ignoreme": "0", "name": "Myvrspot", "new_description": "", "newname": "Myvrspot", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-myvrspot"}, "1062": {"component": {"domainsurls": 89}, "example_url": "www.nap.edu.au", "filename": "sphirewall-application-naplan", "id": "1062", "ignoreme": "0", "name": "NAPLAN", "new_description": "The National Assessment Program – Literacy and Numeracy is a series of tests focused on basic skills that are administered annually to Australian students.", "newname": "NAPLAN", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-naplan"}, "1063": {"component": {"domainsurls": 1}, "example_url": "naviance.com", "filename": "sphirewall-application-naviance", "id": "1063", "ignoreme": "0", "name": "Naviance", "new_description": "", "newname": "Naviance", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-naviance"}, "1064": {"component": {"domainsurls": 1}, "example_url": "nessy.com", "filename": "sphirewall-applicaiton-nessy-com", "id": "1064", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-nessy-com"}, "1065": {"component": {"domainsurls": 1}, "example_url": "net-ref.com", "filename": "sphirewall-application-net-ref", "id": "1065", "ignoreme": "0", "name": "Net-Ref", "new_description": "", "newname": "Net-Ref", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-net-ref"}, "1066": {"component": {"domainsurls": 3}, "example_url": "newsela.com", "filename": "sphirewall-application-newsela", "id": "1066", "ignoreme": "0", "name": "newsela", "new_description": "", "newname": "newsela", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-newsela"}, "1067": {"component": {"domainsurls": 1}, "example_url": "noredink.com", "filename": "sphirewall-application-noredink", "id": "1067", "ignoreme": "0", "name": "noredink", "new_description": "", "newname": "noredink", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-noredink"}, "1068": {"component": {"domainsurls": 7}, "example_url": "nzceronline.org.nz", "filename": "sphirewall-applicaiton-nzcer", "id": "1068", "ignoreme": "0", "name": "NZCER", "new_description": "", "newname": "NZCER", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-nzcer"}, "1069": {"component": {"domainsurls": 5}, "example_url": "nzqa.govt.nz", "filename": "sphirewall-application-nzqa", "id": "1069", "ignoreme": "0", "name": "NZQA", "new_description": "", "newname": "NZQA", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-nzqa"}, "1070": {"component": {"domainsurls": 32}, "example_url": "ocde.us", "filename": "sphirewall-application-ocde-us", "id": "1070", "ignoreme": "0", "name": "Orange County Department of Education", "new_description": "", "newname": "Orange County Department of Education", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-ocde-us"}, "1071": {"component": {"domainsurls": 1}, "example_url": "otus.com", "filename": "sphirewall-application-otus", "id": "1071", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-otus"}, "1072": {"component": {"domainsurls": 3}, "example_url": "our-hero.com", "filename": "sphirewall-application-ourhero", "id": "1072", "ignoreme": "0", "name": "Our Hero", "new_description": "", "newname": "Our Hero", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-ourhero"}, "1073": {"component": {"domainsurls": 1}, "example_url": "oxfordowl.co.uk", "filename": "sphirewall-application-oxfordowl-co-uk", "id": "1073", "ignoreme": "0", "name": "Oxford Owl", "new_description": "", "newname": "Oxford Owl", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-oxfordowl-co-uk"}, "1074": {"component": {"domainsurls": 1}, "example_url": "panoramaed.com", "filename": "sphirewall-application-panoramaeducation", "id": "1074", "ignoreme": "0", "name": "Panorama Education", "new_description": "\n\n", "newname": "Panorama Education", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-panoramaeducation"}, "1075": {"component": {"domainsurls": 3}, "example_url": "pbisrewards.com", "filename": "sphirewall-application-pbisrewards", "id": "1075", "ignoreme": "0", "name": "PBIS Rewards", "new_description": "", "newname": "PBIS Rewards", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-pbisrewards"}, "1076": {"component": {"domainsurls": 4}, "example_url": "pebblego.com", "filename": "sphirewall-application-pebblego", "id": "1076", "ignoreme": "0", "name": "Pebble Go", "new_description": "", "newname": "Pebble Go", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-pebblego"}, "1077": {"component": {"domainsurls": 1}, "example_url": "peergrade.io", "filename": "sphirewall-application-peergrade-io", "id": "1077", "ignoreme": "0", "name": "Peergrade", "new_description": "", "newname": "Peergrade", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-peergrade-io"}, "1078": {"component": {"domainsurls": 3}, "example_url": "phet.colorado.edu", "filename": "sphirewall-application-phet", "id": "1078", "ignoreme": "0", "name": "PhET", "new_description": "PhET sims are based on extensive education research and engage students through an intuitive, game-like environment where students learn through exploration and discovery.", "newname": "PhET", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-phet"}, "1079": {"component": {"domainsurls": 1}, "example_url": "phonicshero.com", "filename": "sphirewall-applicaiton-phonicshero-com", "id": "1079", "ignoreme": "0", "name": "PhonicsHero", "new_description": "", "newname": "PhonicsHero", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-phonicshero-com"}, "1080": {"component": {"domainsurls": 4}, "example_url": "physicsandmathstutor.com", "filename": "sphirewall-application-physicsandmathstutor", "id": "1080", "ignoreme": "0", "name": "Physics and Maths Tutor", "new_description": "Revision notes, key points, worksheets and questions by topic from past papers", "newname": "Physics and Maths Tutor", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-physicsandmathstutor"}, "1081": {"component": {"domainsurls": 1}, "example_url": "playposit.com", "filename": "sphirewall-application-playposit", "id": "1081", "ignoreme": "0", "name": "PlayPosit", "new_description": "", "newname": "PlayPosit", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-playposit"}, "1082": {"component": {"domainsurls": 1}, "example_url": "pltw.org", "filename": "sphirewall-application-pltw", "id": "1082", "ignoreme": "0", "name": "pltw", "new_description": "", "newname": "pltw", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-pltw"}, "1083": {"component": {"domainsurls": 1}, "example_url": "popplet.com", "filename": "sphirewall-application-popplet-com", "id": "1083", "ignoreme": "0", "name": "Popplet", "new_description": "", "newname": "Popplet", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-popplet-com"}, "1084": {"component": {"domainsurls": 5}, "example_url": "prezi.com", "filename": "sphirewall-application-prezi", "id": "1084", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-prezi"}, "1085": {"component": {"domainsurls": 1}, "example_url": "sso.prodigygame.com", "filename": "sphirewall-applicaiton-prodigygame", "id": "1085", "ignoreme": "0", "name": "Prodigy Game", "new_description": "The most engaging math platform in the world.", "newname": "Prodigy Game", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-prodigygame"}, "1086": {"component": {"domainsurls": 1}, "example_url": "proquest.com", "filename": "sphirewall-application-proquest", "id": "1086", "ignoreme": "0", "name": "ProQuest", "new_description": "", "newname": "ProQuest", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-proquest"}, "1087": {"component": {"domainsurls": 3}, "example_url": "proxlearn.com", "filename": "sphirewall-application-proxlearn", "id": "1087", "ignoreme": "0", "name": "Proxlearn", "new_description": "", "newname": "Proxlearn", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-proxlearn"}, "1088": {"component": {"domainsurls": 5}, "example_url": "quavered.com", "filename": "sphirewall-application-quavered", "id": "1088", "ignoreme": "0", "name": "QuaverEd", "new_description": "", "newname": "QuaverEd", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-quavered"}, "1089": {"component": {"domainsurls": 1}, "example_url": "quia.com", "filename": "sphirewall-application-quia", "id": "1089", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-quia"}, "1090": {"component": {"domainsurls": 1}, "example_url": "quizizz.com", "filename": "sphirewall-application-quizizz", "id": "1090", "ignoreme": "0", "name": "quizizz", "new_description": "", "newname": "quizizz", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-quizizz"}, "1091": {"component": {"domainsurls": 1}, "example_url": "quizlet.com", "filename": "sphirewall-application-quizlet-com", "id": "1091", "ignoreme": "0", "name": "quizlet.com", "new_description": "", "newname": "quizlet.com", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-quizlet-com"}, "1092": {"component": {"domainsurls": 1}, "example_url": "ratemyteachers.com", "filename": "sphirewall-application-ratemyteachers-com", "id": "1092", "ignoreme": "0", "name": "Rate My Teachers", "new_description": "", "newname": "Rate My Teachers", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-ratemyteachers-com"}, "1093": {"component": {"domainsurls": 1}, "example_url": "readinga-z.com", "filename": "sphirewall-application-readingaz", "id": "1093", "ignoreme": "0", "name": "Reading a-z", "new_description": "", "newname": "Reading a-z", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-readingaz"}, "1094": {"component": {"domainsurls": 8}, "example_url": "readingeggs.com", "filename": "sphirewall-application-readingeggs", "id": "1094", "ignoreme": "0", "name": "Reading Eggs", "new_description": "Reading Eggs teaches children aged 3-13 how to read with fun and interactive online reading lessons and phonics games. ", "newname": "Reading Eggs", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-readingeggs"}, "1095": {"component": {"domainsurls": 4}, "example_url": "readinghorizons.com", "filename": "sphirewall-application-readinghorizons", "id": "1095", "ignoreme": "0", "name": "Reading Horizons", "new_description": "", "newname": "Reading Horizons", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-readinghorizons"}, "1096": {"component": {"domainsurls": 1}, "example_url": "readingplus.com", "filename": "sphirewall-applicaiton-readingplus", "id": "1096", "ignoreme": "0", "name": "Reading Plus", "new_description": "Reading Plus simultaneously develops all three domains of reading – physical, cognitive, and emotional  – by integrating them in one personalized online reading program. Rooted in eight decades of research, Reading Plus is proven to increase achievement in students from elementary grades through college.", "newname": "Reading Plus", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-readingplus"}, "1097": {"component": {"domainsurls": 1}, "example_url": "readnaturally.com", "filename": "sphirewall-application-readnaturally", "id": "1097", "ignoreme": "0", "name": "readnaturally", "new_description": "https://readlive.readnaturally.com/", "newname": "readnaturally", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-readnaturally"}, "1098": {"component": {"domainsurls": 1}, "example_url": "readtheory.org", "filename": "sphirewall-application-readtheory", "id": "1098", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON><PERSON>", "new_description": "Online reading activities for all levels.Improve your reading ability using this fun, interactive, educationaltool in school or at home.", "newname": "<PERSON><PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-readtheory"}, "1099": {"component": {"domainsurls": 1}, "example_url": "readworks.org", "filename": "sphirewall-application-readworks", "id": "1099", "ignoreme": "0", "name": "readworks.org", "new_description": "Reading comprehension instruction that works", "newname": "readworks.org", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-readworks"}, "11": {"component": {"domainsurls": 589328, "regexpurls": 18, "searchterms": 104, "videoids": 2, "weightedphrases": 434}, "example_url": "www.urbandictionary.com", "filename": "adult", "id": "11", "ignoreme": "0", "name": " Adult Mixed Content", "new_description": "Content which is primarily adult in nature, content such as bad language or adult jokes, but not pornography.", "newname": " Adult Mixed Content", "parent": "1248", "test_url": "test.mysmoothwall.net/adult"}, "1100": {"component": {"domainsurls": 1}, "example_url": "remind.com", "filename": "sphirewall-application-remind", "id": "1100", "ignoreme": "0", "name": "Remind", "new_description": "", "newname": "Remind", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-remind"}, "1101": {"component": {"domainsurls": 15}, "example_url": "renaissance.com", "filename": "sphirewall-application-renaissance", "id": "1101", "ignoreme": "0", "name": "Renaissance Learning", "new_description": "", "newname": "Renaissance Learning", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-renaissance"}, "1102": {"component": {"domainsurls": 1}, "example_url": "readingplus.com", "filename": "sphirewall-application-responsivelearning", "id": "1102", "ignoreme": "0", "name": "Responsive Learning", "new_description": "", "newname": "Responsive Learning", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-responsivelearning"}, "1103": {"component": {"domainsurls": 1}, "example_url": "roomrecess.com", "filename": "sphirewall-application-roomrecess-com", "id": "1103", "ignoreme": "0", "name": "Room Recess", "new_description": "", "newname": "Room Recess", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-roomrecess-com"}, "1104": {"component": {"domainsurls": 5}, "example_url": "sadlierconnect.com", "filename": "sphirewall-application-sadlierconnect", "id": "1104", "ignoreme": "0", "name": "Sadlier Connect", "new_description": "", "newname": "Sadlier Connect", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-sadlierconnect"}, "1105": {"component": {"domainsurls": 13}, "example_url": "saplinglearning.com", "filename": "sphirewall-application-saplinglearning", "id": "1105", "ignoreme": "0", "name": "Sapling Learning", "new_description": "", "newname": "Sapling Learning", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-saplinglearning"}, "1106": {"component": {"domainsurls": 22}, "example_url": "savvas.com", "filename": "sphirewall-application-savvas", "id": "1106", "ignoreme": "0", "name": "Savvas - <PERSON> EasyBridge", "new_description": "", "newname": "Savvas - <PERSON> EasyBridge", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-savvas"}, "1107": {"component": {"domainsurls": 3}, "example_url": "sc-alt.portal.airast.org", "filename": "sphirewall-application-sc-alt-portal-airast-org", "id": "1107", "ignoreme": "0", "name": "sc-alt portal", "new_description": "", "newname": "sc-alt portal", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-sc-alt-portal-airast-org"}, "1108": {"component": {"domainsurls": 5}, "example_url": "scholastic.com", "filename": "sphirewall-application-scholastic", "id": "1108", "ignoreme": "0", "name": "Scholastic", "new_description": "Education site", "newname": "Scholastic", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-scholastic"}, "1109": {"component": {"domainsurls": 3}, "example_url": "scholastic.co.nz", "filename": "sphirewall-application-scholastic-co-nz", "id": "1109", "ignoreme": "0", "name": "Scholastic NZ", "new_description": "", "newname": "Scholastic NZ", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-scholastic-co-nz"}, "111": {"component": {"domainsurls": 668, "regexpurls": 3, "searchterms": 32, "weightedphrases": 201}, "example_url": "", "filename": "gore", "id": "111", "ignoreme": "0", "name": "<PERSON>", "new_description": "Sites describing or displaying gory content.", "newname": "<PERSON>", "parent": "436", "test_url": "test.mysmoothwall.net/gore"}, "1110": {"component": {"domainsurls": 1}, "example_url": "school-links.org.nz", "filename": "sphirewall-applicaiton-school-links", "id": "1110", "ignoreme": "0", "name": "School Links", "new_description": "", "newname": "School Links", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-school-links"}, "1111": {"component": {"domainsurls": 3}, "example_url": "sharpschool.com", "filename": "sphirewall-application-sharpschool-com", "id": "1111", "ignoreme": "0", "name": "School Messenger", "new_description": "", "newname": "School Messenger", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-sharpschool-com"}, "1112": {"component": {"domainsurls": 3}, "example_url": "schooltalk.co.nz", "filename": "sphirewall-application-schooltalk", "id": "1112", "ignoreme": "0", "name": "SchoolTalk", "new_description": "", "newname": "SchoolTalk", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-schooltalk"}, "1113": {"component": {"domainsurls": 4}, "example_url": "schooltube.com", "filename": "sphirewall-applicaiton-schooltube", "id": "1113", "ignoreme": "0", "name": "SchoolTube", "new_description": "", "newname": "SchoolTube", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-schooltube"}, "1114": {"component": {"domainsurls": 1}, "example_url": "sciencebuddies.org", "filename": "sphirewall-application-sciencebuddies", "id": "1114", "ignoreme": "0", "name": "ScienceBuddies", "new_description": "Science fair project ideas, step by step how to do a science fair project.", "newname": "ScienceBuddies", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-sciencebuddies"}, "1115": {"component": {"domainsurls": 1}, "example_url": "sciencekids.co.nz", "filename": "sphirewall-application-sciencekids", "id": "1115", "ignoreme": "0", "name": "ScienceKids", "new_description": "Science kids is the home of science & technology on the internet for children around the world.", "newname": "ScienceKids", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-sciencekids"}, "1116": {"component": {"domainsurls": 3}, "example_url": "scipadonline.co.nz", "filename": "sphirewall-application-scipad", "id": "1116", "ignoreme": "0", "name": "scipad", "new_description": "", "newname": "scipad", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-scipad"}, "1117": {"component": {"domainsurls": 4}, "example_url": "sc-rally.org", "filename": "sphirewall-application-sc-rally", "id": "1117", "ignoreme": "0", "name": "sc-rally", "new_description": "", "newname": "sc-rally", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-sc-rally"}, "1118": {"component": {"domainsurls": 1}, "example_url": "scratch.mit.edu", "filename": "sphirewall-application-scratch", "id": "1118", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "Scratch is a free programming language and online community where you can create your own interactive stories, games, and animations.", "newname": "<PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-scratch"}, "1119": {"component": {"domainsurls": 1}, "example_url": "secondstep.org", "filename": "sphirewall-application-secondstep", "id": "1119", "ignoreme": "0", "name": "Second Step", "new_description": "", "newname": "Second Step", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-secondstep"}, "1120": {"component": {"domainsurls": 3}, "example_url": "seesaw.com", "filename": "sphirewall-applicaiton-seesaw-me", "id": "1120", "ignoreme": "0", "name": "SeeSaw", "new_description": "", "newname": "SeeSaw", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-seesaw-me"}, "1121": {"component": {"domainsurls": 1}, "example_url": "senecalearning.com", "filename": "sphirewall-application-seneca", "id": "1121", "ignoreme": "0", "name": "Seneca", "new_description": "Join 6,500,000 students using Seneca as the funnest way to learn at KS2, KS3, GCSE & A Level. And it's free!", "newname": "Seneca", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-seneca"}, "1122": {"component": {"domainsurls": 6}, "example_url": "sentencebuilders.com", "filename": "sphirewall-application-sentencebuilders", "id": "1122", "ignoreme": "0", "name": "SentenceBuilders", "new_description": "SentenceBuilders is a website for teachers and learners of languages.", "newname": "SentenceBuilders", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-sentencebuilders"}, "1123": {"component": {"domainsurls": 1}, "example_url": "seppo.io", "filename": "sphirewall-application-seppo", "id": "1123", "ignoreme": "0", "name": "seppo", "new_description": "", "newname": "seppo", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-seppo"}, "1124": {"component": {"domainsurls": 3}, "example_url": "satchelone.com", "filename": "sphirewall-application-showmyhomework-co-uk", "id": "1124", "ignoreme": "0", "name": "Show My Homework", "new_description": "", "newname": "Show My Homework", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-showmyhomework-co-uk"}, "1125": {"component": {"domainsurls": 1}, "example_url": "sketchup.com", "filename": "sphirewall-application-sketchup", "id": "1125", "ignoreme": "0", "name": "Sketchup", "new_description": "", "newname": "Sketchup", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-sketchup"}, "1126": {"component": {"domainsurls": 1}, "example_url": "skoolbo.com", "filename": "sphirewall-applicaiton-skoolbo-com", "id": "1126", "ignoreme": "0", "name": "SkoolBo", "new_description": "", "newname": "SkoolBo", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-skoolbo-com"}, "1127": {"component": {"domainsurls": 37}, "example_url": "smartmusic.com", "filename": "sphirewall-application-smartmusic", "id": "1127", "ignoreme": "0", "name": "smartmusic", "new_description": "", "newname": "smartmusic", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-smartmusic"}, "1128": {"component": {"domainsurls": 1}, "example_url": "smartpass.app", "filename": "sphirewall-application-smartpass", "id": "1128", "ignoreme": "0", "name": "Smartpass", "new_description": "Smartpass hallway pass system / student tracker", "newname": "Smartpass", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-smartpass"}, "1129": {"component": {"domainsurls": 3}, "example_url": "snow.live", "filename": "sphirewall-application-snowflake", "id": "1129", "ignoreme": "0", "name": "Snowflake", "new_description": "Signature for Snowflake, which contains educational videos for students.", "newname": "Snowflake", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-snowflake"}, "1130": {"component": {"domainsurls": 1}, "example_url": "socrative.com", "filename": "sphirewall-application-socrative", "id": "1130", "ignoreme": "0", "name": "socrative", "new_description": "", "newname": "socrative", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-socrative"}, "1131": {"component": {"domainsurls": 5}, "example_url": "soraapp.com", "filename": "sphirewall-application-sora", "id": "1131", "ignoreme": "0", "name": "Sora <PERSON>", "new_description": "", "newname": "Sora <PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-sora"}, "1132": {"component": {"domainsurls": 1}, "example_url": "so-rummet.se", "filename": "sphirewall-application-sorummet", "id": "1132", "ignoreme": "0", "name": "SO-rummet", "new_description": "Swedish digital learning resource", "newname": "SO-rummet", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-sorummet"}, "1133": {"component": {"domainsurls": 1}, "example_url": "spanishdict.com", "filename": "sphirewall-application-spanishdict", "id": "1133", "ignoreme": "0", "name": "SpanishDict", "new_description": "", "newname": "SpanishDict", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-spanishdict"}, "1134": {"component": {"domainsurls": 3}, "example_url": "spellingcity.com", "filename": "sphirewall-application-spellingcity", "id": "1134", "ignoreme": "0", "name": "spellingcity", "new_description": "", "newname": "spellingcity", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-spellingcity"}, "1135": {"component": {"domainsurls": 1}, "example_url": "spellingstars.com", "filename": "sphirewall-application-spellingstarscom", "id": "1135", "ignoreme": "0", "name": "spellingstars.com", "new_description": "", "newname": "spellingstars.com", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-spellingstarscom"}, "1136": {"component": {"domainsurls": 1}, "example_url": "splashlearn.com", "filename": "sphirewall-application-splashlearn", "id": "1136", "ignoreme": "0", "name": "Splash Learn", "new_description": "", "newname": "Splash Learn", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-splashlearn"}, "1137": {"component": {"domainsurls": 1}, "example_url": "sporcle.com", "filename": "sphirewall-application-sporcle", "id": "1137", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "Trivia games in a diverse range of subjects. Quizzes can be played interactively in unusual ways.", "newname": "<PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-sporcle"}, "1138": {"component": {"domainsurls": 1}, "example_url": "stapplet.com", "filename": "sphirewall-application-stapplet", "id": "1138", "ignoreme": "0", "name": "Stapplet", "new_description": "", "newname": "Stapplet", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-stapplet"}, "1139": {"component": {"domainsurls": 1}, "example_url": "starfall.com", "filename": "sphirewall-applicaiton-starfall-com", "id": "1139", "ignoreme": "0", "name": "Starfall", "new_description": "", "newname": "Starfall", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-starfall-com"}, "114": {"component": {"domainsurls": 23804, "regexpurls": 2, "searchterms": 75, "weightedphrases": 618}, "example_url": "www.guinness.com/", "filename": "legaldrugs", "id": "114", "ignoreme": "0", "name": "Alcohol and Tobacco", "new_description": "Sites pertaining to the use, sale, production and promotion of alcoholic drinks and tobacco (including e-cigarettes)", "newname": "Alcohol and Tobacco", "parent": "436", "test_url": "test.mysmoothwall.net/legaldrugs"}, "1140": {"component": {"domainsurls": 6}, "example_url": "statsmedic.com", "filename": "sphirewall-application-statsmedic", "id": "1140", "ignoreme": "0", "name": "Stats Medic", "new_description": "", "newname": "Stats Medic", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-statsmedic"}, "1141": {"component": {"domainsurls": 1}, "example_url": "stemscopes.com", "filename": "sphirewall-application-stemscopes", "id": "1141", "ignoreme": "0", "name": "STEMscopes", "new_description": "", "newname": "STEMscopes", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-stemscopes"}, "1142": {"component": {"domainsurls": 1}, "example_url": "stepsweb.com", "filename": "sphirewall-application-stepsweb-com", "id": "1142", "ignoreme": "0", "name": "Steps Web", "new_description": "", "newname": "Steps Web", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-stepsweb-com"}, "1143": {"component": {"domainsurls": 7}, "example_url": "stmath.com", "filename": "sphirewall-application-stmath", "id": "1143", "ignoreme": "0", "name": "ST Math", "new_description": "", "newname": "ST Math", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-stmath"}, "1144": {"component": {"domainsurls": 1}, "example_url": "storybird.com", "filename": "sphirewall-application-storybird", "id": "1144", "ignoreme": "0", "name": "StoryBird", "new_description": "Discover an endless library of free books, picture books, & poetry or use simple tools to create books in minutes.", "newname": "StoryBird", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-storybird"}, "1145": {"component": {"domainsurls": 9}, "example_url": "studiesweekly.com", "filename": "sphirewall-application-studiesweekly", "id": "1145", "ignoreme": "0", "name": "Studies Weekly", "new_description": "", "newname": "Studies Weekly", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-studiesweekly"}, "1146": {"component": {"domainsurls": 1}, "example_url": "studocu.com", "filename": "sphirewall-application-studocu", "id": "1146", "ignoreme": "0", "name": "StuD<PERSON><PERSON>", "new_description": "", "newname": "StuD<PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-studocu"}, "1147": {"component": {"domainsurls": 1}, "example_url": "edmentum.com", "filename": "sphirewall-application-studyisland", "id": "1147", "ignoreme": "0", "name": "Study Island", "new_description": "Edmentum is the leading provider of K-12 digital curriculum, assessments, and services to 43,000 schools in all 50 US states and over 100 countries worldwide. We partner with educators to create instructional technology that is proven, easy-to-use, individualized, and aligned to state standards.", "newname": "Study Island", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-studyisland"}, "1148": {"component": {"domainsurls": 4}, "example_url": "studyladder.co.nz", "filename": "sphirewall-application-studyladder", "id": "1148", "ignoreme": "0", "name": "Study Ladder", "new_description": "Used by over 70,000 teachers & 1 million students at home and school. Studyladder is an online english literacy & mathematics learning tool.", "newname": "Study Ladder", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-studyladder"}, "1149": {"component": {"domainsurls": 1}, "example_url": "studyspanish.com", "filename": "sphirewall-application-studyspanish", "id": "1149", "ignoreme": "0", "name": "StudySpanish", "new_description": "", "newname": "StudySpanish", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-studyspanish"}, "1150": {"component": {"domainsurls": 3}, "example_url": "studysync.com", "filename": "sphirewall-application-studysync", "id": "1150", "ignoreme": "0", "name": "Studysync", "new_description": "", "newname": "Studysync", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-studysync"}, "1151": {"component": {"domainsurls": 3}, "example_url": "stukent.com", "filename": "sphirewall-application-stukent", "id": "1151", "ignoreme": "0", "name": "Stukent", "new_description": "", "newname": "Stukent", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-stukent"}, "1152": {"component": {"domainsurls": 1}, "example_url": "sumdog.com", "filename": "sphirewall-application-sumdog", "id": "1152", "ignoreme": "0", "name": "SumDog", "new_description": "Math & ELA standards aligned adaptive learning for grades K-8. Loved by students, parents and teachers.", "newname": "SumDog", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-sumdog"}, "1153": {"component": {"domainsurls": 1}, "example_url": "summitlearning.org", "filename": "sphirewall-application-summitlearning", "id": "1153", "ignoreme": "0", "name": "Summit Learning", "new_description": "", "newname": "Summit Learning", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-summitlearning"}, "1154": {"component": {"domainsurls": 5}, "example_url": "sunshineonline.com.au", "filename": "sphirewall-applicaiton-sunshineonline", "id": "1154", "ignoreme": "0", "name": "Sunshine Online", "new_description": "", "newname": "Sunshine Online", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-sunshineonline"}, "1155": {"component": {"domainsurls": 4}, "example_url": "teachable.com", "filename": "sphirewall-application-teachable", "id": "1155", "ignoreme": "0", "name": "teachable", "new_description": "", "newname": "teachable", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-teachable"}, "1156": {"component": {"domainsurls": 1}, "example_url": "teacherspayteachers.com", "filename": "sphirewall-applicaiton-teacherspayteachers-com", "id": "1156", "ignoreme": "0", "name": "Teachers Pay Teachers", "new_description": "", "newname": "Teachers Pay Teachers", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-teacherspayteachers-com"}, "1157": {"component": {"domainsurls": 5}, "example_url": "teachingstrategies.com", "filename": "sphirewall-application-teachingstrategies-com", "id": "1157", "ignoreme": "0", "name": "Teaching Strategies", "new_description": "", "newname": "Teaching Strategies", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-teachingstrategies-com"}, "1158": {"component": {"domainsurls": 1}, "example_url": "teachtci.com", "filename": "sphirewall-application-tci", "id": "1158", "ignoreme": "0", "name": "Teach TCI", "new_description": "", "newname": "Teach TCI", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-tci"}, "1159": {"component": {"domainsurls": 1}, "example_url": "teachtown.com", "filename": "sphirewall-application-teachtown", "id": "1159", "ignoreme": "0", "name": "TeachTown", "new_description": "", "newname": "TeachTown", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-teachtown"}, "1160": {"component": {"domainsurls": 1}, "example_url": "teachyourmonstertoread.com", "filename": "sphirewall-application-teachyourmonstertoread", "id": "1160", "ignoreme": "0", "name": "Teach Your Monster To Read", "new_description": "", "newname": "Teach Your Monster To Read", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-teachyourmonstertoread"}, "1161": {"component": {"domainsurls": 1}, "example_url": "thatquiz.org", "filename": "sphirewall-application-thatquiz", "id": "1161", "ignoreme": "0", "name": "that quiz", "new_description": "", "newname": "that quiz", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-thatquiz"}, "1162": {"component": {"domainsurls": 1}, "example_url": "theatrefolk.com", "filename": "sphirewall-application-theatrefolk", "id": "1162", "ignoreme": "0", "name": "theatrefolk", "new_description": "", "newname": "theatrefolk", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-theatrefolk"}, "1163": {"component": {"domainsurls": 1}, "example_url": "themindlab.com", "filename": "sphirewall-applicationthemindlab-com", "id": "1163", "ignoreme": "0", "name": "The Mind Lab", "new_description": "", "newname": "The Mind Lab", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicationthemindlab-com"}, "1164": {"component": {"domainsurls": 1}, "example_url": "tinkercad.com", "filename": "sphirewall-application-tinkercad", "id": "1164", "ignoreme": "0", "name": "Tinkercad", "new_description": "", "newname": "Tinkercad", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-tinkercad"}, "1165": {"component": {"domainsurls": 1}, "example_url": "tki.org.nz", "filename": "sphirewall-applicaiton-tki-org-nz", "id": "1165", "ignoreme": "0", "name": "tki.org.nz", "new_description": "", "newname": "tki.org.nz", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-tki-org-nz"}, "1166": {"component": {"domainsurls": 1}, "example_url": "transferology.com", "filename": "sphirewall-application-transferology", "id": "1166", "ignoreme": "0", "name": "Transferology", "new_description": "", "newname": "Transferology", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-transferology"}, "1167": {"component": {"domainsurls": 4}, "example_url": "transum.com", "filename": "sphirewall-application-transum", "id": "1167", "ignoreme": "0", "name": "Transum Maths", "new_description": "", "newname": "Transum Maths", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-transum"}, "1168": {"component": {"domainsurls": 6}, "example_url": "tumblebooks.com", "filename": "sphirewall-application-tumblebooks", "id": "1168", "ignoreme": "0", "name": "Tumble Books", "new_description": "", "newname": "Tumble Books", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-tumblebooks"}, "1169": {"component": {"domainsurls": 1}, "example_url": "turningtechnologies.com", "filename": "sphirewall-application-turningtechnologies", "id": "1169", "ignoreme": "0", "name": "Turning Technologies", "new_description": "", "newname": "Turning Technologies", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-turningtechnologies"}, "1170": {"component": {"domainsurls": 4}, "example_url": "turnitin.com", "filename": "sphirewall-applicaiton-turnitin-com", "id": "1170", "ignoreme": "0", "name": "Turnitin", "new_description": "", "newname": "Turnitin", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-turnitin-com"}, "1171": {"component": {"domainsurls": 8}, "example_url": "twigscience.com", "filename": "sphirewall-application-twigscience", "id": "1171", "ignoreme": "0", "name": "Twig science", "new_description": "", "newname": "Twig science", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-twigscience"}, "1172": {"component": {"domainsurls": 1}, "example_url": "twinkl.co.uk", "filename": "sphirewall-applicaiton-twinkl-co-uk", "id": "1172", "ignoreme": "0", "name": "Twinkl", "new_description": "", "newname": "Twinkl", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-twinkl-co-uk"}, "1173": {"component": {"domainsurls": 1}, "example_url": "tynker.com", "filename": "sphirewall-application-tynker", "id": "1173", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "<PERSON><PERSON><PERSON> makes it fun and easy to learn computer programming.", "newname": "<PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-tynker"}, "1174": {"component": {"domainsurls": 3}, "example_url": "typing.com", "filename": "sphirewall-applicaiton-typing-com", "id": "1174", "ignoreme": "0", "name": "Typing", "new_description": "", "newname": "Typing", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-typing-com"}, "1175": {"component": {"domainsurls": 1}, "example_url": "typingclub.com", "filename": "sphirewall-applicaiton-typingclub-com", "id": "1175", "ignoreme": "0", "name": "Typing Club", "new_description": "", "newname": "Typing Club", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-typingclub-com"}, "1176": {"component": {"domainsurls": 3}, "example_url": "", "filename": "sphirewall-application-ultranet", "id": "1176", "ignoreme": "0", "name": "Ultranet", "new_description": "", "newname": "Ultranet", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-ultranet"}, "1177": {"component": {"domainsurls": 6}, "example_url": "unsw.edu.au", "filename": "sphirewall-application-unsw-global", "id": "1177", "ignoreme": "0", "name": "UNSW Global", "new_description": "", "newname": "UNSW Global", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-unsw-global"}, "1178": {"component": {"domainsurls": 1}, "example_url": "usatestprep.com", "filename": "sphirewall-application-usatestprep", "id": "1178", "ignoreme": "0", "name": "USA Test Prep", "new_description": "", "newname": "USA Test Prep", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-usatestprep"}, "1179": {"component": {"domainsurls": 3}, "example_url": "varsitytutors.com", "filename": "sphirewall-application-varsitytutors", "id": "1179", "ignoreme": "0", "name": "Varsity Tutors", "new_description": "", "newname": "Varsity Tutors", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-varsitytutors"}, "1180": {"component": {"domainsurls": 1}, "example_url": "vistahigherlearning.com", "filename": "sphirewall-application-vistahigherlearning", "id": "1180", "ignoreme": "0", "name": "Vista Higher Learning", "new_description": "", "newname": "Vista Higher Learning", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-vistahigherlearning"}, "1181": {"component": {"domainsurls": 3}, "example_url": "vocabulary.com", "filename": "sphirewall-application-vocabulary", "id": "1181", "ignoreme": "0", "name": "Vocabulary", "new_description": "", "newname": "Vocabulary", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-vocabulary"}, "1182": {"component": {"domainsurls": 15}, "example_url": "voyagersopris.com", "filename": "sphirewall-application-voyagersopris", "id": "1182", "ignoreme": "0", "name": "Voyager Sopris", "new_description": "No matter where learning happens, we have you covered with evidence-based, K–12 literacy, math, professional learning, and assessment solutions for remote, hybrid, and in-person learning environments.", "newname": "Voyager Sopris", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-voyagersopris"}, "1183": {"component": {"domainsurls": 1}, "example_url": "walsworthyearbooks.com", "filename": "sphirewall-application-walsworthyearbooks", "id": "1183", "ignoreme": "0", "name": "walsworthyearbooks", "new_description": "", "newname": "walsworthyearbooks", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-walsworthyearbooks"}, "1184": {"component": {"domainsurls": 5}, "example_url": "waterford.org", "filename": "sphirewall-application-waterford", "id": "1184", "ignoreme": "0", "name": "Waterford", "new_description": "", "newname": "Waterford", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-waterford"}, "1185": {"component": {"domainsurls": 4}, "example_url": "webassign.net", "filename": "sphirewall-application-webassign", "id": "1185", "ignoreme": "0", "name": "Webassign", "new_description": "", "newname": "Webassign", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-webassign"}, "1186": {"component": {"domainsurls": 1}, "example_url": "webrtc.org", "filename": "sphirewall-application-webrtc", "id": "1186", "ignoreme": "0", "name": "WebRTC", "new_description": "", "newname": "WebRTC", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-webrtc"}, "1187": {"component": {"domainsurls": 1}, "example_url": "wincrsystem.com", "filename": "sphirewall-application-wincrsystem", "id": "1187", "ignoreme": "0", "name": "wincrsystem", "new_description": "", "newname": "wincrsystem", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-wincrsystem"}, "1188": {"component": {"domainsurls": 1}, "example_url": "wordwall.net", "filename": "sphirewall-application-wordwall", "id": "1188", "ignoreme": "0", "name": "Wordwall", "new_description": "Wordwall.net allows teachers to create interactive games and printed materials for their students. Teachers simply enter the content they want and we automate the rest.", "newname": "Wordwall", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-wordwall"}, "1189": {"component": {"domainsurls": 3}, "example_url": "writescore.com", "filename": "sphirewall-application-writescore", "id": "1189", "ignoreme": "0", "name": "Write Score", "new_description": "", "newname": "Write Score", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-writescore"}, "1190": {"component": {"domainsurls": 3}, "example_url": "xtramath.org", "filename": "sphirewall-application-xtramath", "id": "1190", "ignoreme": "0", "name": "Xtramath", "new_description": "", "newname": "Xtramath", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-xtramath"}, "1191": {"component": {"domainsurls": 7}, "example_url": "zearn.org", "filename": "sphirewall-application-zearn", "id": "1191", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-zearn"}, "1192": {"component": {"domainsurls": 1}, "example_url": "zingylearning.com", "filename": "sphirewall-application-zingylearning", "id": "1192", "ignoreme": "0", "name": "zingylearning", "new_description": "", "newname": "zingylearning", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-zingylearning"}, "1193": {"component": {"domainsurls": 1}, "example_url": "zipgrade.com", "filename": "sphirewall-application-zipgrade", "id": "1193", "ignoreme": "0", "name": "zipgrade", "new_description": "", "newname": "zipgrade", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-zipgrade"}, "1194": {"component": {"domainsurls": 4}, "example_url": "ziptales.com", "filename": "sphirewall-application-ziptales-com", "id": "1194", "ignoreme": "0", "name": "Ziptales", "new_description": "", "newname": "Ziptales", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-ziptales-com"}, "1195": {"component": {"domainsurls": 1}, "example_url": "zooniverse.org", "filename": "sphirewall-application-zooniverse", "id": "1195", "ignoreme": "0", "name": "Zooniverse", "new_description": "", "newname": "Zooniverse", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-zooniverse"}, "1196": {"component": {"domainsurls": 44}, "example_url": "autodesk.com", "filename": "sphirewall-application-autodesk", "id": "1196", "ignoreme": "0", "name": "Autodesk", "new_description": "\nAutodesk is a multinational software corporation that makes software services for the architecture, engineering, construction, manufacturing, media, education, and entertainment industries.", "newname": "Autodesk", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-autodesk"}, "1197": {"component": {"domainsurls": 10}, "example_url": "classvr.com", "filename": "sphirewall-application-classvr", "id": "1197", "ignoreme": "0", "name": "ClassVR", "new_description": "", "newname": "ClassVR", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-classvr"}, "1198": {"component": {"domainsurls": 3}, "example_url": "Mote.com", "filename": "sphirewall-application-motecom", "id": "1198", "ignoreme": "0", "name": "Mote.com", "new_description": "The audio toolkit for educators and learners everywhere", "newname": "Mote.com", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-motecom"}, "1199": {"component": {"domainsurls": 3}, "example_url": "pixlr.com", "filename": "sphirewall-application-pixlr", "id": "1199", "ignoreme": "0", "name": "Pixlr", "new_description": "Photo Editor Site", "newname": "Pixlr", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-pixlr"}, "12": {"component": {"domainsurls": 1796}, "example_url": "www.gov.uk", "filename": "government", "id": "12", "ignoreme": "0", "name": "Government", "new_description": "Government websites and websites related to governmental organisations", "newname": "Government", "parent": "437", "test_url": "test.mysmoothwall.net/government"}, "120": {"component": {"domainsurls": 66121, "regexpurls": 41, "searchterms": 37, "weightedphrases": 1248}, "example_url": "www.proxysite.com", "filename": "proxies", "id": "120", "ignoreme": "0", "name": "Web Proxies", "new_description": "Web proxy sites and other tools designed to circumvent filtering", "newname": "Web Proxies", "parent": "1226", "test_url": "test.mysmoothwall.net/proxies"}, "1200": {"component": {"domainsurls": 1}, "example_url": "ptable.com", "filename": "sphirewall-application-ptable", "id": "1200", "ignoreme": "0", "name": "Ptable", "new_description": "Ptable shines when used as a true application, more interactive and dynamic than any application out there.", "newname": "Ptable", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-ptable"}, "1201": {"component": {"domainsurls": 4}, "example_url": "speechstream.com", "filename": "sphirewall-application-speechstream", "id": "1201", "ignoreme": "0", "name": "SpeechStream", "new_description": "SpeechStream is a cloud based, language and literacy support toolbar", "newname": "SpeechStream", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-speechstream"}, "1202": {"component": {"domainsurls": 1}, "example_url": "wordreference.com", "filename": "sphirewall-application-wordreference", "id": "1202", "ignoreme": "0", "name": "WordReference", "new_description": "The WordReference language forum is the largest repository of knowledge and advice about the English language, as well as a number of other languages.", "newname": "WordReference", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-wordreference"}, "1203": {"component": {"domainsurls": 1}, "example_url": "wordtune.com", "filename": "sphirewall-application-wordtune", "id": "1203", "ignoreme": "0", "name": "Wordtune", "new_description": "Your thoughts\nin words\nSay exactly what you mean through clear, compelling and authentic writing.", "newname": "Wordtune", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-wordtune"}, "1204": {"component": {}, "example_url": "www.slashdot.org", "filename": "computing-parent", "id": "1204", "ignoreme": "0", "name": "Computing and Electronics", "new_description": "Sites related to computing but not games. e.g programming languages", "newname": "Computing and Electronics", "parent": "396", "test_url": "test.mysmoothwall.net/computing-parent"}, "1205": {"component": {"domainsurls": 3}, "example_url": "itopia.com", "filename": "sphirewall-application-itopia", "id": "1205", "ignoreme": "0", "name": "itopia", "new_description": "itopia application", "newname": "itopia", "parent": "1204", "test_url": "test.mysmoothwall.net/sphirewall-application-itopia"}, "1206": {"component": {}, "example_url": "thesaurus.com", "filename": "dictionaries-and-encyclopedias-parent", "id": "1206", "ignoreme": "0", "name": "Dictionaries and Encyclopedias", "new_description": "Reference material and Resources regarding general topics.", "newname": "Dictionaries and Encyclopedias", "parent": "396", "test_url": "test.mysmoothwall.net/dictionaries-and-encyclopedias-parent"}, "1207": {"component": {"domainsurls": 15}, "example_url": "britannica.com", "filename": "sphirewall-application-britannica", "id": "1207", "ignoreme": "0", "name": "Encyclopedia Britannica", "new_description": "", "newname": "Encyclopedia Britannica", "parent": "1206", "test_url": "test.mysmoothwall.net/sphirewall-application-britannica"}, "1208": {"component": {"domainsurls": 3}, "example_url": "historylink.org", "filename": "sphirewall-application-historylink", "id": "1208", "ignoreme": "0", "name": "History Link", "new_description": "", "newname": "History Link", "parent": "1206", "test_url": "test.mysmoothwall.net/sphirewall-application-historylink"}, "1209": {"component": {"domainsurls": 1}, "example_url": "ne.se", "filename": "sphirewall-application-neforsweden", "id": "1209", "ignoreme": "0", "name": "National Encyclopedia for Sweden", "new_description": "NE is the National Encyclopedia for Sweden. ", "newname": "National Encyclopedia for Sweden", "parent": "1206", "test_url": "test.mysmoothwall.net/sphirewall-application-neforsweden"}, "1210": {"component": {"domainsurls": 1}, "example_url": "teara.govt.nz", "filename": "sphirewall-application-tearanz", "id": "1210", "ignoreme": "0", "name": "Te Ara Encyclopedia of NZ", "new_description": "The Encyclopedia of New Zealand is an online encyclopedia established in 2001 by the New Zealand Government's Ministry for Culture and Heritage. ", "newname": "Te Ara Encyclopedia of NZ", "parent": "1206", "test_url": "test.mysmoothwall.net/sphirewall-application-tearanz"}, "1211": {"component": {"domainsurls": 21}, "example_url": "wikipedia.org", "filename": "sphirewall-application-wiki", "id": "1211", "ignoreme": "0", "name": "Wikipedia", "new_description": "", "newname": "Wikipedia", "parent": "1206", "test_url": "test.mysmoothwall.net/sphirewall-application-wiki"}, "1212": {"component": {}, "example_url": "www.wikihow.com", "filename": "instructional-parent", "id": "1212", "ignoreme": "0", "name": "Knowledge Sharing", "new_description": "How To, instructional and Q&A sites", "newname": "Knowledge Sharing", "parent": "396", "test_url": "test.mysmoothwall.net/instructional-parent"}, "1213": {"component": {"domainsurls": 10}, "example_url": "fandom.com", "filename": "sphirewall-application-fandom", "id": "1213", "ignoreme": "0", "name": "Fandom", "new_description": "", "newname": "Fandom", "parent": "1212", "test_url": "test.mysmoothwall.net/sphirewall-application-fandom"}, "1214": {"component": {"domainsurls": 1}, "example_url": "giphy.com", "filename": "sphirewall-application-giphy", "id": "1214", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "GIPHY is an online database and search engine that allows users to search, share, and discover GIFs.", "newname": "<PERSON><PERSON><PERSON>", "parent": "1212", "test_url": "test.mysmoothwall.net/sphirewall-application-giphy"}, "1215": {"component": {"domainsurls": 11}, "example_url": "stackoverflow.com", "filename": "sphirewall-application-stack-overflow", "id": "1215", "ignoreme": "0", "name": "Stack Overflow", "new_description": "Stack Overflow is one of the largest, most trusted online community for developers to learn, share their programming knowledge, and build their careers.", "newname": "Stack Overflow", "parent": "1212", "test_url": "test.mysmoothwall.net/sphirewall-application-stack-overflow"}, "1216": {"component": {"domainsurls": 4}, "example_url": "quora.com", "filename": "sphirewall-application-quora", "id": "1216", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "Quora is a question-and-answer website created, edited, and organized by its owners. Quora enables people from all over the world to come together to share and learn across millions of common interests", "newname": "<PERSON><PERSON><PERSON>", "parent": "1212", "test_url": "test.mysmoothwall.net/sphirewall-application-quora"}, "1217": {"component": {}, "example_url": "www.google.com", "filename": "searchengines-parent", "id": "1217", "ignoreme": "0", "name": "Search Engines", "new_description": "Sites providing web search functionality", "newname": "Search Engines", "parent": "396", "test_url": "test.mysmoothwall.net/searchengines-parent"}, "1218": {"component": {"domainsurls": 14}, "example_url": "baidu.com", "filename": "sphirewall-application-baidu", "id": "1218", "ignoreme": "0", "name": "Baidu", "new_description": "Baidu is a Chinese website and search engine that enables individuals to obtain information and find what they need.", "newname": "Baidu", "parent": "1217", "test_url": "test.mysmoothwall.net/sphirewall-application-baidu"}, "1219": {"component": {"domainsurls": 1}, "example_url": "duckduckgo.com", "filename": "sphirewall-application-duckduckgo", "id": "1219", "ignoreme": "0", "name": "DuckDuckGo", "new_description": "", "newname": "DuckDuckGo", "parent": "1217", "test_url": "test.mysmoothwall.net/sphirewall-application-duckduckgo"}, "1220": {"component": {"domainsurls": 21}, "example_url": "yandex.com", "filename": "sphirewall-application-yandex", "id": "1220", "ignoreme": "0", "name": "Yandex Search", "new_description": "Yandex is a Russian search engine and web portal. Yandex offers internet search and other services like maps, images and videos.", "newname": "Yandex Search", "parent": "1217", "test_url": "test.mysmoothwall.net/sphirewall-application-yandex"}, "1221": {"component": {"domainsurls": 21}, "example_url": "bing.com", "filename": "sphirewall-application-bing", "id": "1221", "ignoreme": "0", "name": "<PERSON>", "new_description": "Bing helps you turn information into action, making it faster and easier to go from searching to doing.", "newname": "<PERSON>", "parent": "1217", "test_url": "test.mysmoothwall.net/sphirewall-application-bing"}, "1222": {"component": {}, "example_url": "climatecooling.org", "filename": "environment-parent", "id": "1222", "ignoreme": "0", "name": "Environment", "new_description": "Information and studies related to living organisms and their natural habitat.", "newname": "Environment", "parent": "396", "test_url": "test.mysmoothwall.net/environment-parent"}, "1223": {"component": {"domainsurls": 1}, "example_url": "", "filename": "sphirewall-application-electricgarden", "id": "1223", "ignoreme": "0", "name": "Electric Garden", "new_description": "", "newname": "Electric Garden", "parent": "1222", "test_url": "test.mysmoothwall.net/sphirewall-application-electricgarden"}, "1224": {"component": {}, "example_url": "", "filename": "malhacking-child", "id": "1224", "ignoreme": "1", "name": " Malware and Hacking", "new_description": "Hacking, warez and phishing sites, including sites containing information on how to bypass web filters", "newname": " Malware and Hacking", "parent": "252", "test_url": "test.mysmoothwall.net/malhacking-child"}, "1225": {"component": {"domainsurls": 114672}, "example_url": "anonymousvpn.org", "filename": "proxies-vpns", "id": "1225", "ignoreme": "0", "name": " Proxies and VPNs", "new_description": "Sites and services that allow users to gain access to content in any way that bypasses URL filtering or monitoring.", "newname": " Proxies and VPNs", "parent": "1226", "test_url": "test.mysmoothwall.net/proxies-vpns"}, "1226": {"component": {}, "example_url": "anonymousvpn.org", "filename": "proxies-vpns-parent", "id": "1226", "ignoreme": "0", "name": "Proxies and VPNs", "new_description": "Sites and services that allow users to gain access to content in any way that bypasses URL filtering or monitoring.", "newname": "Proxies and VPNs", "parent": "155", "test_url": "test.mysmoothwall.net/proxies-vpns-parent"}, "1227": {"component": {"domainsurls": 537}, "example_url": "vpnreviewz.com", "filename": "sphirewall-application-blocklist-ipproxies", "id": "1227", "ignoreme": "0", "name": "AI-IP Detected Proxy", "new_description": "", "newname": "AI-IP Detected Proxy", "parent": "1226", "test_url": "test.mysmoothwall.net/sphirewall-application-blocklist-ipproxies"}, "1228": {"component": {"domainsurls": 3999}, "example_url": "", "filename": "sphirewall-application-analytic_vpns", "id": "1228", "ignoreme": "0", "name": "Analytic VPNs", "new_description": "VPNs detected by analytics on crowd sourced connections", "newname": "Analytic VPNs", "parent": "1226", "test_url": "test.mysmoothwall.net/sphirewall-application-analytic_vpns"}, "1229": {"component": {"domainsurls": 7}, "example_url": "betternet.co", "filename": "sphirewall-applicaiton-betternet", "id": "1229", "ignoreme": "0", "name": "BetterNet VPN", "new_description": "", "newname": "BetterNet VPN", "parent": "1226", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-betternet"}, "1230": {"component": {"domainsurls": 5}, "example_url": "brave.com", "filename": "sphirewall-application-brave", "id": "1230", "ignoreme": "0", "name": "Brave", "new_description": "", "newname": "Brave", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-brave"}, "1231": {"component": {"domainsurls": 1000}, "example_url": "veepn.com", "filename": "sphirewall-application-chromevpns", "id": "1231", "ignoreme": "0", "name": "Chrome VPNs", "new_description": "Circumvention extensions which can be downloaded from the Google Chrome store.", "newname": "Chrome VPNs", "parent": "1226", "test_url": "test.mysmoothwall.net/sphirewall-application-chromevpns"}, "1232": {"component": {"domainsurls": 5}, "example_url": "freevpnplanet.com", "filename": "sphirewall-application-freevpnplanet", "id": "1232", "ignoreme": "0", "name": "Free VPN Planet", "new_description": "Free VPN and ProxY", "newname": "Free VPN Planet", "parent": "1226", "test_url": "test.mysmoothwall.net/sphirewall-application-freevpnplanet"}, "1233": {"component": {"domainsurls": 92}, "example_url": "hoxx.com", "filename": "sphirewall-application-hoxxvpn", "id": "1233", "ignoreme": "0", "name": "Hoxx VPN", "new_description": "", "newname": "Hoxx VPN", "parent": "1226", "test_url": "test.mysmoothwall.net/sphirewall-application-hoxxvpn"}, "1234": {"component": {"regexpurls": 442}, "example_url": "m247.com", "filename": "sphirewall-application-m247", "id": "1234", "ignoreme": "0", "name": "M247 Cloud Services", "new_description": "Mostly VPN cloud service provider", "newname": "M247 Cloud Services", "parent": "1226", "test_url": "test.mysmoothwall.net/sphirewall-application-m247"}, "1235": {"component": {"domainsurls": 9}, "example_url": "https://www.opera.com/computer/features/free-vpn", "filename": "sphirewall-application-operavpn", "id": "1235", "ignoreme": "0", "name": "Opera VPN", "new_description": "Free VPN in the Opera browser - surf the web with enhanced privacy.", "newname": "Opera VPN", "parent": "1226", "test_url": "test.mysmoothwall.net/sphirewall-application-operavpn"}, "1236": {"component": {"domainsurls": 3}, "example_url": "www.expressvpn.com", "filename": "sphirewall-application-puffin", "id": "1236", "ignoreme": "0", "name": "Puffin Browser/VPN", "new_description": "", "newname": "Puffin Browser/VPN", "parent": "1226", "test_url": "test.mysmoothwall.net/sphirewall-application-puffin"}, "1237": {"component": {"domainsurls": 12}, "example_url": "skyvpn.net", "filename": "sphirewall-application-skyvpn", "id": "1237", "ignoreme": "0", "name": "SkyVPN", "new_description": "", "newname": "SkyVPN", "parent": "1226", "test_url": "test.mysmoothwall.net/sphirewall-application-skyvpn"}, "1238": {"component": {"domainsurls": 5996}, "example_url": "torproject.org", "filename": "sphirewall-tor", "id": "1238", "ignoreme": "0", "name": "Tor Traffic", "new_description": "The Tor Browser is a web broswer that anonymizes your web traffic using the Tor network, making it easy to protect your identity online.", "newname": "Tor Traffic", "parent": "1226", "test_url": "test.mysmoothwall.net/sphirewall-tor"}, "1239": {"component": {"domainsurls": 15}, "example_url": "vpnproxymaster.com", "filename": "sphirewall-application-vpnproxymaster", "id": "1239", "ignoreme": "0", "name": "VPN Proxy Master", "new_description": "VPN Proxy Master: Most Secure VPN for Work From Home", "newname": "VPN Proxy Master", "parent": "1226", "test_url": "test.mysmoothwall.net/sphirewall-application-vpnproxymaster"}, "124": {"component": {"domainsurls": 8257, "regexpurls": 5, "weightedphrases": 147}, "example_url": "www.edugeek.net/forums", "filename": "forums", "id": "124", "ignoreme": "0", "name": "Discussion Forums", "new_description": "Sites dedicated to discussion forums, e.g. phpBB", "newname": "Discussion Forums", "parent": "159", "test_url": "test.mysmoothwall.net/forums"}, "1240": {"component": {"domainsurls": 36997}, "example_url": "wireguard.com", "filename": "sphirewall-application-vpntunnel", "id": "1240", "ignoreme": "0", "name": "VPN Tunnels", "new_description": "IPs detected by AI analysis.", "newname": "VPN Tunnels", "parent": "1226", "test_url": "test.mysmoothwall.net/sphirewall-application-vpntunnel"}, "1241": {"component": {"domainsurls": 4}, "example_url": "https://*******", "filename": "sphirewall-application-warp", "id": "1241", "ignoreme": "0", "name": "Warp VPN", "new_description": "A VPN privacy app by Cloudflare", "newname": "Warp VPN", "parent": "1226", "test_url": "test.mysmoothwall.net/sphirewall-application-warp"}, "1242": {"component": {}, "example_url": "pirateproxy-bay.com", "filename": "warez-parent", "id": "1242", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "Sites containing pirated copyrighted material for illegal download or streaming", "newname": "<PERSON><PERSON>", "parent": "155", "test_url": "test.mysmoothwall.net/warez-parent"}, "1243": {"component": {"domainsurls": 12}, "example_url": "y2mate.com", "filename": "sphirewall-application-y2mate", "id": "1243", "ignoreme": "0", "name": "y2mate", "new_description": "", "newname": "y2mate", "parent": "1242", "test_url": "test.mysmoothwall.net/sphirewall-application-y2mate"}, "1244": {"component": {"domainsurls": 76}, "example_url": "123movies.net", "filename": "sphirewall-application-123movies", "id": "1244", "ignoreme": "0", "name": "123movies", "new_description": "", "newname": "123movies", "parent": "1242", "test_url": "test.mysmoothwall.net/sphirewall-application-123movies"}, "1245": {"component": {}, "example_url": "", "filename": "nbc-child", "id": "1245", "ignoreme": "1", "name": " Mature and Explicit", "new_description": "Sites which could cause legal or liability issues", "newname": " Mature and Explicit", "parent": "252", "test_url": "test.mysmoothwall.net/nbc-child"}, "1246": {"component": {}, "example_url": "www.ukessays.com", "filename": "cheating-parent", "id": "1246", "ignoreme": "0", "name": "Academic Dishonesty", "new_description": "Sites offering paid help and/or pre-written homework material for students", "newname": "Academic Dishonesty", "parent": "436", "test_url": "test.mysmoothwall.net/cheating-parent"}, "1247": {"component": {"domainsurls": 1}, "example_url": "quillbot.com", "filename": "sphirewall-application-quillbot", "id": "1247", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON><PERSON>", "new_description": "Quillbot is a website that provides an AI-powered writing assistant, which can help users with grammar, style, and word choice. It can also assist with paraphrasing and summarizing text.", "newname": "<PERSON><PERSON><PERSON><PERSON>", "parent": "1246", "test_url": "test.mysmoothwall.net/sphirewall-application-quillbot"}, "1248": {"component": {}, "example_url": "www.urbandictionary.com", "filename": "adult-parent", "id": "1248", "ignoreme": "0", "name": "Adult Mixed Content", "new_description": "Content which is primarily adult in nature, content such as bad language or adult jokes, but not pornography.", "newname": "Adult Mixed Content", "parent": "436", "test_url": "test.mysmoothwall.net/adult-parent"}, "1249": {"component": {"domainsurls": 8}, "example_url": "camsurf.com", "filename": "sphirewall-applicaiton-camsurf", "id": "1249", "ignoreme": "0", "name": "Camsurf", "new_description": "Anonymous video chat application which allows you to connect with strangers from around the world.", "newname": "Camsurf", "parent": "1248", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-camsurf"}, "1250": {"component": {"domainsurls": 9}, "example_url": "deviantart.com", "filename": "sphirewall-application-deviantart", "id": "1250", "ignoreme": "0", "name": "Deviant Art", "new_description": "DeviantArt is the largest online social community for artists and art enthusiasts, allowing people to connect through the creation and sharing of art.", "newname": "Deviant Art", "parent": "1248", "test_url": "test.mysmoothwall.net/sphirewall-application-deviantart"}, "1251": {"component": {"domainsurls": 14}, "example_url": "omegle.com", "filename": "sphirewall-applicaiton-omegle", "id": "1251", "ignoreme": "0", "name": "Omegle", "new_description": "Anonymous chat with strangers", "newname": "Omegle", "parent": "1248", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-omegle"}, "1252": {"component": {}, "example_url": "", "filename": "infrastructure-child", "id": "1252", "ignoreme": "1", "name": " Web and Infrastructure", "new_description": "Web infrastructure and miscellaneous domains.", "newname": " Web and Infrastructure", "parent": "252", "test_url": "test.mysmoothwall.net/infrastructure-child"}, "1253": {"component": {}, "example_url": "www.akamai.com", "filename": "contentdelivery-parent", "id": "1253", "ignoreme": "0", "name": "Content Delivery", "new_description": "Content Delivery Networks and supplementary infrastructure servers for various existing sites", "newname": "Content Delivery", "parent": "241", "test_url": "test.mysmoothwall.net/contentdelivery-parent"}, "1254": {"component": {"domainsurls": 6}, "example_url": "akamai.com", "filename": "sphirewall-application-akamihd", "id": "1254", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON><PERSON>", "parent": "1253", "test_url": "test.mysmoothwall.net/sphirewall-application-akamihd"}, "1255": {"component": {"domainsurls": 9}, "example_url": "aws.amazon.com", "filename": "sphirewall-application-amazonaws", "id": "1255", "ignoreme": "0", "name": "AmazonAWS", "new_description": "Amazon Web Services offers reliable, scalable, and inexpensive cloud computing services. Free to join, pay only for what you use.", "newname": "AmazonAWS", "parent": "1253", "test_url": "test.mysmoothwall.net/sphirewall-application-amazonaws"}, "1256": {"component": {"domainsurls": 24}, "example_url": "azure.net", "filename": "sphirewall-application-azure", "id": "1256", "ignoreme": "0", "name": "Azure", "new_description": "", "newname": "Azure", "parent": "1253", "test_url": "test.mysmoothwall.net/sphirewall-application-azure"}, "1257": {"component": {"domainsurls": 3}, "example_url": "bootstrapcdn.com", "filename": "sphirewall-application-bootstrapcdn", "id": "1257", "ignoreme": "0", "name": "BootstrapCDN", "new_description": "", "newname": "BootstrapCDN", "parent": "1253", "test_url": "test.mysmoothwall.net/sphirewall-application-bootstrapcdn"}, "1258": {"component": {"domainsurls": 15}, "example_url": "cloudflare.com", "filename": "sphirewall-application-cloudflare", "id": "1258", "ignoreme": "0", "name": "CloudFlare", "new_description": "CloudFlare is a free global CDN and DNS provider that can speed up and protect any site online", "newname": "CloudFlare", "parent": "1253", "test_url": "test.mysmoothwall.net/sphirewall-application-cloudflare"}, "1259": {"component": {"domainsurls": 6}, "example_url": "cloudfront.net", "filename": "sphirewall-application-cloudfront", "id": "1259", "ignoreme": "0", "name": "CloudFront", "new_description": "", "newname": "CloudFront", "parent": "1253", "test_url": "test.mysmoothwall.net/sphirewall-application-cloudfront"}, "1260": {"component": {"domainsurls": 1}, "example_url": "", "filename": "sphirewall-application-googleappengine", "id": "1260", "ignoreme": "0", "name": "Google App Engine", "new_description": "", "newname": "Google App Engine", "parent": "1253", "test_url": "test.mysmoothwall.net/sphirewall-application-googleappengine"}, "1261": {"component": {"domainsurls": 17}, "example_url": "cloud.google.com/cdn", "filename": "sphirewall-application-googlecdn", "id": "1261", "ignoreme": "0", "name": "Google CDN", "new_description": "Google services used to host static resources or general content delivery that doesn't fit into a single Google product.", "newname": "Google CDN", "parent": "1253", "test_url": "test.mysmoothwall.net/sphirewall-application-googlecdn"}, "1262": {"component": {"domainsurls": 1}, "example_url": "cloud.huawei.com", "filename": "sphirewall-application-hicloud", "id": "1262", "ignoreme": "0", "name": "Huawei Cloud", "new_description": "", "newname": "Huawei Cloud", "parent": "1253", "test_url": "test.mysmoothwall.net/sphirewall-application-hicloud"}, "1263": {"component": {"domainsurls": 4}, "example_url": "netlify.app", "filename": "sphirewall-application-netlify", "id": "1263", "ignoreme": "0", "name": "Netlify", "new_description": "", "newname": "Netlify", "parent": "1253", "test_url": "test.mysmoothwall.net/sphirewall-application-netlify"}, "1264": {"component": {"domainsurls": 3}, "example_url": "scaleway.com", "filename": "sphirewall-application-scaleway", "id": "1264", "ignoreme": "0", "name": "Scaleway", "new_description": "Scaleway is a cloud provider.", "newname": "Scaleway", "parent": "1253", "test_url": "test.mysmoothwall.net/sphirewall-application-scaleway"}, "1265": {"component": {"domainsurls": 1}, "example_url": "", "filename": "sphirewall-applicaiton-vidible-tv", "id": "1265", "ignoreme": "0", "name": "Vidible", "new_description": "", "newname": "Vidible", "parent": "1253", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-vidible-tv"}, "1266": {"component": {"domainsurls": 2376, "weightedphrases": 3}, "example_url": "", "filename": "software-child", "id": "1266", "ignoreme": "0", "name": " Software", "new_description": "Domains and URLs used by common software packages.", "newname": " Software", "parent": "489", "test_url": "test.mysmoothwall.net/software-child"}, "1267": {"component": {"domainsurls": 6}, "example_url": "atlassian.com", "filename": "sphirewall-application-atlassian", "id": "1267", "ignoreme": "0", "name": "Atlassian", "new_description": "\nAtlassian products are used every day for improving software development, project management, collaboration, and code quality.", "newname": "Atlassian", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-atlassian"}, "1268": {"component": {"domainsurls": 4}, "example_url": "bit.ly", "filename": "sphirewall-application-bitly", "id": "1268", "ignoreme": "0", "name": "bitly", "new_description": "A URL shortener built with powerful tools to help you grow and protect your brand.", "newname": "bitly", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-bitly"}, "1269": {"component": {"domainsurls": 1}, "example_url": "bitmoji.com", "filename": "sphirewall-application-bitmoji", "id": "1269", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "Bitmoji is a secondary social media app that people use to create little cartoon versions of themselves, which they then use on their various social media accounts. It's a very simple service: You simply create an avatar of yourself, and assemble various comics, GIFs, expressions, and reactions that use this avatar.", "newname": "<PERSON><PERSON><PERSON>", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-bitmoji"}, "1270": {"component": {"domainsurls": 15}, "example_url": "", "filename": "sphirewall-application-captiveportal", "id": "1270", "ignoreme": "0", "name": "Captive Portal", "new_description": "Sites used by operating systems and browsers trying to detect a captive portal. Don't block but also don't allow unauthenticated access to these.", "newname": "Captive Portal", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-captiveportal"}, "1271": {"component": {"domainsurls": 1}, "example_url": "csfirst.withgoogle.com", "filename": "sphirewall-application-csfirst", "id": "1271", "ignoreme": "0", "name": "CS First", "new_description": "A computer science curriculum that makes coding easy to teach and fun to learn.", "newname": "CS First", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-csfirst"}, "1272": {"component": {"domainsurls": 10}, "example_url": "dell.com", "filename": "sphirewall-application-dell", "id": "1272", "ignoreme": "0", "name": "Dell", "new_description": "", "newname": "Dell", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-dell"}, "1273": {"component": {"domainsurls": 3}, "example_url": "embed.ly", "filename": "sphirewall-application-embed-ly", "id": "1273", "ignoreme": "0", "name": "Embed.ly", "new_description": "", "newname": "Embed.ly", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-embed-ly"}, "1274": {"component": {"domainsurls": 4}, "example_url": "filestack.com", "filename": "sphirewall-applicaiton-filestack", "id": "1274", "ignoreme": "0", "name": "Filestack", "new_description": "", "newname": "Filestack", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-filestack"}, "1275": {"component": {"domainsurls": 3, "regexpurls": 2}, "example_url": "fortinet.com", "filename": "sphirewall-application-fortinet", "id": "1275", "ignoreme": "0", "name": "Fortinet", "new_description": "", "newname": "Fortinet", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-fortinet"}, "1276": {"component": {"domainsurls": 13}, "example_url": "github.com", "filename": "sphirewall-application-github", "id": "1276", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "GitHub is a web-based Git or version control repository and Internet hosting service. It offers all of the distributed version control and source code management (SCM) functionality of Git as well as adding its own features.", "newname": "<PERSON><PERSON><PERSON>", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-github"}, "1277": {"component": {"domainsurls": 1}, "example_url": "goguardian.com", "filename": "sphirewall-application-goguardian", "id": "1277", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON><PERSON>", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-goguardian"}, "1278": {"component": {"domainsurls": 19}, "example_url": "", "filename": "sphirewall-application-google", "id": "1278", "ignoreme": "0", "name": "Google", "new_description": "", "newname": "Google", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-google"}, "1279": {"component": {"domainsurls": 1}, "example_url": "", "filename": "sphirewall-application-googlechrome", "id": "1279", "ignoreme": "0", "name": "Google Chrome", "new_description": "", "newname": "Google Chrome", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-googlechrome"}, "1280": {"component": {"domainsurls": 4}, "example_url": "heroku.com", "filename": "sphirewall-application-heroku", "id": "1280", "ignoreme": "0", "name": "Heroku", "new_description": "", "newname": "Heroku", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-heroku"}, "1281": {"component": {"domainsurls": 10}, "example_url": "hubspot.com", "filename": "sphirewall-application-hubspot", "id": "1281", "ignoreme": "0", "name": "Hubspot", "new_description": "", "newname": "Hubspot", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-hubspot"}, "1282": {"component": {"domainsurls": 3}, "example_url": "icivics.org", "filename": "sphirewall-application-icivics", "id": "1282", "ignoreme": "0", "name": "iCivics", "new_description": "", "newname": "iCivics", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-icivics"}, "1283": {"component": {}, "example_url": "simpplr.com", "filename": "sphirewall-application-intranetservers", "id": "1283", "ignoreme": "0", "name": "Intranet Servers", "new_description": "The Intranet Servers category indicates the remote server is not located on the Internet. The Content Filtering servers will not be able to scan these sites for content. These are normally servers located behind firewalls, on your local area network as well as private IP Addresses or sites which are not top-level domain.", "newname": "Intranet Servers", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-intranetservers"}, "1284": {"component": {"domainsurls": 1}, "example_url": "lastpass.com", "filename": "sphirewall-application-lastpass", "id": "1284", "ignoreme": "0", "name": "LastPass", "new_description": "LastPass is a freemium password manager that stores encrypted passwords online. The standard version of LastPass comes with a web interface, but also includes plugins for various web browsers and apps for many smartphones.", "newname": "LastPass", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-lastpass"}, "1285": {"component": {"domainsurls": 1}, "example_url": "", "filename": "sphirewall-applicaiton-loopster", "id": "1285", "ignoreme": "0", "name": "Loopster", "new_description": "Loopster is Free Video Editing Software for business, education programs & personal use.", "newname": "Loopster", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-loopster"}, "1286": {"component": {"domainsurls": 1}, "example_url": "mailchimp.com", "filename": "sphirewall-application-mailchimp", "id": "1286", "ignoreme": "0", "name": "Mailchimp", "new_description": "", "newname": "Mailchimp", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-mailchimp"}, "1287": {"component": {"domainsurls": 117}, "example_url": "microsoft.com", "filename": "sphirewall-application-microsoft", "id": "1287", "ignoreme": "0", "name": "Microsoft", "new_description": "", "newname": "Microsoft", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-microsoft"}, "1288": {"component": {"domainsurls": 4}, "example_url": "miradore.com", "filename": "sphirewall-application-miradore", "id": "1288", "ignoreme": "0", "name": "Miradore", "new_description": "", "newname": "Miradore", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-miradore"}, "1289": {"component": {"domainsurls": 1}, "example_url": "justmote.me", "filename": "sphirewall-application-mote", "id": "1289", "ignoreme": "0", "name": "Mote", "new_description": "", "newname": "Mote", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-mote"}, "1290": {"component": {"domainsurls": 1}, "example_url": "moxtra.com", "filename": "sphirewall-application-moxtra", "id": "1290", "ignoreme": "0", "name": "Moxtra", "new_description": "", "newname": "Moxtra", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-moxtra"}, "1291": {"component": {"domainsurls": 1}, "example_url": "mtcaptcha.com", "filename": "sphirewall-application-mtcaptcha", "id": "1291", "ignoreme": "0", "name": "MTCaptcha", "new_description": "", "newname": "MTCaptcha", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-mtcaptcha"}, "1292": {"component": {"domainsurls": 1}, "example_url": "onshape.com", "filename": "sphirewall-application-onshape", "id": "1292", "ignoreme": "0", "name": "onshape", "new_description": "Onshape is the only Software-as-a-Service (SaaS) product development platform that combines CAD, built-in data management, real-time collaboration tools, and business analytics.", "newname": "onshape", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-onshape"}, "1293": {"component": {"domainsurls": 1}, "example_url": "papercut.com", "filename": "sphirewall-application-papercut", "id": "1293", "ignoreme": "0", "name": "PaperCut", "new_description": "Signature to allow the main network TCP ports used by PaperCut.", "newname": "PaperCut", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-papercut"}, "1294": {"component": {"domainsurls": 3}, "example_url": "powtoon.com", "filename": "sphirewall-application-powtoon", "id": "1294", "ignoreme": "0", "name": "Powtoon", "new_description": "PowToon is a company which sells cloud-based software for creating animated presentations and animated explainer videos.", "newname": "Powtoon", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-powtoon"}, "1295": {"component": {"domainsurls": 3}, "example_url": "pusher.com", "filename": "sphirewall-application-pusher", "id": "1295", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "Realtime application development framework", "newname": "<PERSON><PERSON><PERSON>", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-pusher"}, "1296": {"component": {"domainsurls": 5}, "example_url": "replit.com", "filename": "sphirewall-application-replit", "id": "1296", "ignoreme": "0", "name": "Replit", "new_description": "Code, create, and learn together", "newname": "Replit", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-replit"}, "1297": {"component": {"domainsurls": 6}, "example_url": "responsivevoice.com", "filename": "sphirewall-application-reponsivevoice", "id": "1297", "ignoreme": "0", "name": "Responsive Voice", "new_description": "", "newname": "Responsive Voice", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-reponsivevoice"}, "1298": {"component": {"domainsurls": 1}, "example_url": "ring.com", "filename": "sphirewall-application-ring", "id": "1298", "ignoreme": "0", "name": "Ring", "new_description": "", "newname": "Ring", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-ring"}, "1299": {"component": {"domainsurls": 1}, "example_url": "snapengage.com", "filename": "sphirewall-application-snapengage", "id": "1299", "ignoreme": "0", "name": "Snapengage", "new_description": "", "newname": "Snapengage", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-snapengage"}, "13": {"component": {"domainsurls": 17641, "searchterms": 60, "weightedphrases": 1439}, "example_url": "www.888.com", "filename": "gambling", "id": "13", "ignoreme": "0", "name": "Gambling", "new_description": "Sites providing gambling and gambling related services, casino's, sports betting etc..", "newname": "Gambling", "parent": "436", "test_url": "test.mysmoothwall.net/gambling"}, "1300": {"component": {"domainsurls": 7}, "example_url": "unity.com", "filename": "sphirewall-application-unity3d", "id": "1300", "ignoreme": "0", "name": "Unity Real Time Development", "new_description": "", "newname": "Unity Real Time Development", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-unity3d"}, "1301": {"component": {"domainsurls": 1}, "example_url": "vendhq.com", "filename": "sphirewall-application-vend", "id": "1301", "ignoreme": "0", "name": "Vend", "new_description": "", "newname": "Vend", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-vend"}, "1302": {"component": {"domainsurls": 4}, "example_url": "vistab.co.nz", "filename": "sphirewall-application-vistab", "id": "1302", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON>", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-vistab"}, "1303": {"component": {"domainsurls": 9}, "example_url": "yahoo.com", "filename": "sphirewall-application-yahoo", "id": "1303", "ignoreme": "0", "name": "Yahoo", "new_description": "Yahoo services and news network", "newname": "Yahoo", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-yahoo"}, "1304": {"component": {"domainsurls": 1}, "example_url": "yammer.com", "filename": "sphirewall-application-yammer", "id": "1304", "ignoreme": "0", "name": "Yammer", "new_description": "Yammer is an enterprise social networking service that is part of the Microsoft 365 family of products. It is used mainly for private communication within organizations but is also used for networks spanning various organizations.", "newname": "Yammer", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-yammer"}, "1305": {"component": {"domainsurls": 12}, "example_url": "zscaler.com", "filename": "sphirewall-application-zscaler", "id": "1305", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON>", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-zscaler"}, "1306": {"component": {}, "example_url": "doubleclick.net", "filename": "trackingstats-parent", "id": "1306", "ignoreme": "0", "name": "Tracking", "new_description": "Sites known to track visitor statistics for analytics and reporting purposes", "newname": "Tracking", "parent": "241", "test_url": "test.mysmoothwall.net/trackingstats-parent"}, "1307": {"component": {"domainsurls": 1}, "example_url": "flurry.com", "filename": "sphirewall-application-flurry", "id": "1307", "ignoreme": "0", "name": "Flurry", "new_description": "Measure, track and analyze app performance, user acquisition and activity with Flurry Analytics.", "newname": "Flurry", "parent": "1306", "test_url": "test.mysmoothwall.net/sphirewall-application-flurry"}, "1308": {"component": {"domainsurls": 1}, "example_url": "privy.com", "filename": "sphirewall-applicaiton-privy", "id": "1308", "ignoreme": "0", "name": "Privy", "new_description": "", "newname": "Privy", "parent": "1306", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-privy"}, "1309": {"component": {}, "example_url": "qq.com", "filename": "portals-parent", "id": "1309", "ignoreme": "0", "name": "Portals", "new_description": "Website integrating diverse sources, emails, online forums, and search engines.", "newname": "Portals", "parent": "241", "test_url": "test.mysmoothwall.net/portals-parent"}, "1310": {"component": {"domainsurls": 3}, "example_url": "naver.com", "filename": "sphirewall-applications-naver", "id": "1310", "ignoreme": "0", "name": "Naver", "new_description": "", "newname": "Naver", "parent": "1309", "test_url": "test.mysmoothwall.net/sphirewall-applications-naver"}, "1311": {"component": {"domainsurls": 7}, "example_url": "qq.com", "filename": "sphirewall-applications-qq", "id": "1311", "ignoreme": "0", "name": "QQ", "new_description": "Tencent QQ, popularly known as QQ, is an instant messaging software service based in China. Nearly 1 billion active accounts use QQ for chat, gaming, music, shopping and more.", "newname": "QQ", "parent": "1309", "test_url": "test.mysmoothwall.net/sphirewall-applications-qq"}, "1312": {"component": {}, "example_url": "www.teamviewer.com", "filename": "remotedesktop-parent", "id": "1312", "ignoreme": "0", "name": "Remote Access Tools", "new_description": "Sites offering remote desktop and remote administration software tools", "newname": "Remote Access Tools", "parent": "241", "test_url": "test.mysmoothwall.net/remotedesktop-parent"}, "1313": {"component": {"domainsurls": 1}, "example_url": "anydesk.com", "filename": "sphirewall-application-anydesk", "id": "1313", "ignoreme": "0", "name": "AnyDesk", "new_description": "", "newname": "AnyDesk", "parent": "1312", "test_url": "test.mysmoothwall.net/sphirewall-application-anydesk"}, "1314": {"component": {"domainsurls": 1}, "example_url": "atera.com", "filename": "sphirewall-application-atera", "id": "1314", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "RMM software used by schools for remote tools access", "newname": "<PERSON><PERSON>", "parent": "1312", "test_url": "test.mysmoothwall.net/sphirewall-application-atera"}, "1315": {"component": {"domainsurls": 4}, "example_url": "connectwise.com", "filename": "sphirewall-application-connectwise", "id": "1315", "ignoreme": "0", "name": "ConnectWise", "new_description": "", "newname": "ConnectWise", "parent": "1312", "test_url": "test.mysmoothwall.net/sphirewall-application-connectwise"}, "1316": {"component": {}, "example_url": "windowsupdate.microsoft.com", "filename": "windowsupdates-parent", "id": "1316", "ignoreme": "0", "name": "Software Updates", "new_description": "Domains used to download software updates", "newname": "Software Updates", "parent": "241", "test_url": "test.mysmoothwall.net/windowsupdates-parent"}, "1317": {"component": {"domainsurls": 1}, "example_url": "aerohive.com", "filename": "sphirewall-application-aerohive", "id": "1317", "ignoreme": "0", "name": "Aerohive", "new_description": "Aerohive is a wireless networking equipment vendor offering cloud managed wireless.", "newname": "Aerohive", "parent": "1316", "test_url": "test.mysmoothwall.net/sphirewall-application-aerohive"}, "1318": {"component": {"domainsurls": 4, "regexpurls": 4}, "example_url": "avast.com", "filename": "sphirewall-application-avast", "id": "1318", "ignoreme": "0", "name": "Avast Antivirus", "new_description": "", "newname": "Avast Antivirus", "parent": "1316", "test_url": "test.mysmoothwall.net/sphirewall-application-avast"}, "1319": {"component": {"domainsurls": 5}, "example_url": "bitdefender.com", "filename": "sphirewall-application-bitdefender", "id": "1319", "ignoreme": "0", "name": "Bitdefender", "new_description": "", "newname": "Bitdefender", "parent": "1316", "test_url": "test.mysmoothwall.net/sphirewall-application-bitdefender"}, "1320": {"component": {"domainsurls": 29}, "example_url": "", "filename": "sphirewall-application-certificateauthorities", "id": "1320", "ignoreme": "0", "name": "Certificate Authorities", "new_description": "", "newname": "Certificate Authorities", "parent": "1316", "test_url": "test.mysmoothwall.net/sphirewall-application-certificateauthorities"}, "1321": {"component": {"domainsurls": 5, "regexpurls": 5}, "example_url": "meraki.com", "filename": "sphirewall-application-meraki", "id": "1321", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "Meraki Wireless", "newname": "<PERSON><PERSON><PERSON>", "parent": "1316", "test_url": "test.mysmoothwall.net/sphirewall-application-meraki"}, "1322": {"component": {"domainsurls": 4}, "example_url": "symantec.com", "filename": "sphirewall-application-symantec", "id": "1322", "ignoreme": "0", "name": "Symantec", "new_description": "", "newname": "Symantec", "parent": "1316", "test_url": "test.mysmoothwall.net/sphirewall-application-symantec"}, "1323": {"component": {"domainsurls": 13}, "example_url": "", "filename": "sphirewall-application-windowsupdate", "id": "1323", "ignoreme": "0", "name": "Windows Updates", "new_description": "", "newname": "Windows Updates", "parent": "1316", "test_url": "test.mysmoothwall.net/sphirewall-application-windowsupdate"}, "1324": {"component": {}, "example_url": "www.mega.io", "filename": "filehosting-parent", "id": "1324", "ignoreme": "0", "name": "File Sharing", "new_description": "Sites that offer online file storage services on remote servers for backup or exchange purposes", "newname": "File Sharing", "parent": "241", "test_url": "test.mysmoothwall.net/filehosting-parent"}, "1325": {"component": {"domainsurls": 6}, "example_url": "www.4shared.com", "filename": "sphirewall-application-4shared", "id": "1325", "ignoreme": "0", "name": "4shared", "new_description": "Online file sharing and storage - 15 GB free web space. Easy registration. File upload progressor. Multiple file transfer.", "newname": "4shared", "parent": "1324", "test_url": "test.mysmoothwall.net/sphirewall-application-4shared"}, "1326": {"component": {"domainsurls": 5}, "example_url": "icloud.com", "filename": "sphirewall-application-icloud", "id": "1326", "ignoreme": "0", "name": "Apple iCloud", "new_description": "iCloud is a cloud storage and cloud computing service from Apple Inc.", "newname": "Apple iCloud", "parent": "1324", "test_url": "test.mysmoothwall.net/sphirewall-application-icloud"}, "1327": {"component": {"domainsurls": 6}, "example_url": "box.com", "filename": "sphirewall-application-box", "id": "1327", "ignoreme": "0", "name": "Box", "new_description": "Box offers secure content management and collaboration for individuals, teams and businesses, enabling secure file sharing.", "newname": "Box", "parent": "1324", "test_url": "test.mysmoothwall.net/sphirewall-application-box"}, "1328": {"component": {"domainsurls": 12}, "example_url": "https://www.constantcontact.com/", "filename": "sphirewall-application-constantcontact", "id": "1328", "ignoreme": "0", "name": "Constant Contact", "new_description": "Contact list that also allows sharing of files and sending emails.", "newname": "Constant Contact", "parent": "1324", "test_url": "test.mysmoothwall.net/sphirewall-application-constantcontact"}, "1329": {"component": {"domainsurls": 3}, "example_url": "download.com", "filename": "sphirewall-application-download-com", "id": "1329", "ignoreme": "0", "name": "download.com", "new_description": "", "newname": "download.com", "parent": "1324", "test_url": "test.mysmoothwall.net/sphirewall-application-download-com"}, "1330": {"component": {"domainsurls": 6}, "example_url": "dropbox.com", "filename": "sphirewall-application-dropbox", "id": "1330", "ignoreme": "0", "name": "Dropbox", "new_description": "Dropbox is a service that keeps your files safe, synced, and easy to share.", "newname": "Dropbox", "parent": "1324", "test_url": "test.mysmoothwall.net/sphirewall-application-dropbox"}, "1331": {"component": {"domainsurls": 1}, "example_url": "easel.ly", "filename": "sphirewall-applicaiton-easel-ly", "id": "1331", "ignoreme": "0", "name": "easel.ly", "new_description": "Create and share visual ideas 1000s of Reporting, Timeline, Resume and  Process templates to choose from.", "newname": "easel.ly", "parent": "1324", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-easel-ly"}, "1332": {"component": {"domainsurls": 1}, "example_url": "easyshare.com", "filename": "sphirewall-application-easyshare-com", "id": "1332", "ignoreme": "0", "name": "easyshare", "new_description": "", "newname": "easyshare", "parent": "1324", "test_url": "test.mysmoothwall.net/sphirewall-application-easyshare-com"}, "1333": {"component": {"domainsurls": 1}, "example_url": "filestube.com", "filename": "sphirewall-application-filestube-com", "id": "1333", "ignoreme": "0", "name": "filestube.com", "new_description": "", "newname": "filestube.com", "parent": "1324", "test_url": "test.mysmoothwall.net/sphirewall-application-filestube-com"}, "1334": {"component": {"domainsurls": 1}, "example_url": "gigeshare.com", "filename": "sphirewall-application-gigeshare-com", "id": "1334", "ignoreme": "0", "name": "gigeshare.com", "new_description": "", "newname": "gigeshare.com", "parent": "1324", "test_url": "test.mysmoothwall.net/sphirewall-application-gigeshare-com"}, "1335": {"component": {"domainsurls": 5}, "example_url": "photos.google.com", "filename": "sphirewall-application-picasa", "id": "1335", "ignoreme": "0", "name": "Google Photos", "new_description": "Organize, edit, and share your photos.", "newname": "Google Photos", "parent": "1324", "test_url": "test.mysmoothwall.net/sphirewall-application-picasa"}, "1336": {"component": {"domainsurls": 1}, "example_url": "hotfile.com", "filename": "sphirewall-application-hotfile-com", "id": "1336", "ignoreme": "0", "name": "hotfile.com", "new_description": "", "newname": "hotfile.com", "parent": "1324", "test_url": "test.mysmoothwall.net/sphirewall-application-hotfile-com"}, "1337": {"component": {"domainsurls": 1}, "example_url": "massmirror.com", "filename": "sphirewall-application-massmirror-com", "id": "1337", "ignoreme": "0", "name": "massmirror.com", "new_description": "", "newname": "massmirror.com", "parent": "1324", "test_url": "test.mysmoothwall.net/sphirewall-application-massmirror-com"}, "1338": {"component": {"domainsurls": 1}, "example_url": "mediafire.com", "filename": "sphirewall-application-mediafire-com", "id": "1338", "ignoreme": "0", "name": "mediafire.com", "new_description": "", "newname": "mediafire.com", "parent": "1324", "test_url": "test.mysmoothwall.net/sphirewall-application-mediafire-com"}, "1339": {"component": {"domainsurls": 4}, "example_url": "onedrive.com", "filename": "sphirewall-application-onedrive", "id": "1339", "ignoreme": "0", "name": "Microsoft OneDrive", "new_description": "Store photos and docs online. Access them from any PC, Mac or phone. Create and work together on Word, Excel or PowerPoint ...", "newname": "Microsoft OneDrive", "parent": "1324", "test_url": "test.mysmoothwall.net/sphirewall-application-onedrive"}, "1340": {"component": {"domainsurls": 3}, "example_url": "padlet.com", "filename": "sphirewall-application-padlet", "id": "1340", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "You'll feel like a superhero. Padlet is a collaboration tool.", "newname": "<PERSON><PERSON><PERSON>", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-application-padlet"}, "1341": {"component": {"domainsurls": 1}, "example_url": "pcloud.com", "filename": "sphirewall-application-pcloud", "id": "1341", "ignoreme": "0", "name": "PCloud", "new_description": "", "newname": "PCloud", "parent": "1324", "test_url": "test.mysmoothwall.net/sphirewall-application-pcloud"}, "1342": {"component": {"domainsurls": 1}, "example_url": "rapidshare.com", "filename": "sphirewall-application-rapidshare-com", "id": "1342", "ignoreme": "0", "name": "rapidshare.com", "new_description": "", "newname": "rapidshare.com", "parent": "1324", "test_url": "test.mysmoothwall.net/sphirewall-application-rapidshare-com"}, "1343": {"component": {"domainsurls": 12}, "example_url": "surveymonkey.com", "filename": "sphirewall-applicaiton-surveymonkey", "id": "1343", "ignoreme": "0", "name": "Survey Monkey", "new_description": "", "newname": "Survey Monkey", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-surveymonkey"}, "1344": {"component": {"domainsurls": 1}, "example_url": "upload.com", "filename": "sphirewall-application-upload-com", "id": "1344", "ignoreme": "0", "name": "upload.com", "new_description": "", "newname": "upload.com", "parent": "1324", "test_url": "test.mysmoothwall.net/sphirewall-application-upload-com"}, "1345": {"component": {"domainsurls": 3}, "example_url": "", "filename": "appindex-fallback", "id": "1345", "ignoreme": "1", "name": "Fallback", "new_description": "", "newname": "Fallback", "parent": "252", "test_url": "test.mysmoothwall.net/appindex-fallback"}, "1346": {"component": {}, "example_url": "", "filename": "appindex-safeguard", "id": "1346", "ignoreme": "1", "name": "Safe Guard", "new_description": "", "newname": "Safe Guard", "parent": "252", "test_url": "test.mysmoothwall.net/appindex-safeguard"}, "1347": {"component": {}, "example_url": "", "filename": "people-and-society-child", "id": "1347", "ignoreme": "1", "name": " People and Society", "new_description": "Various inoffensive yet non-work-related sites", "newname": " People and Society", "parent": "252", "test_url": "test.mysmoothwall.net/people-and-society-child"}, "1348": {"component": {}, "example_url": "puregym.com", "filename": "health-parent", "id": "1348", "ignoreme": "0", "name": "Health and Fitness", "new_description": "Sites dedicated to health and fitness, gyms, fitness blogs, health sites", "newname": "Health and Fitness", "parent": "153", "test_url": "test.mysmoothwall.net/health-parent"}, "1349": {"component": {"domainsurls": 3}, "example_url": "dexcom.com", "filename": "sphirewall-application-dexcom", "id": "1349", "ignoreme": "0", "name": "Dexcom", "new_description": "", "newname": "Dexcom", "parent": "1348", "test_url": "test.mysmoothwall.net/sphirewall-application-dexcom"}, "1350": {"component": {"domainsurls": 3}, "example_url": "suicidepreventionlifeline.org", "filename": "sphirewall-application-suicideprevention", "id": "1350", "ignoreme": "0", "name": "Suicide Prevention", "new_description": "The National Suicide Prevention Lifeline is a national network of local crisis centers that provides free and confidential emotional support to people in suicidal crisis or emotional distress 24 hours a day, 7 days a week. We're committed to improving crisis services and advancing suicide prevention by empowering individuals, advancing professional best practices, and building awareness.", "newname": "Suicide Prevention", "parent": "1348", "test_url": "test.mysmoothwall.net/sphirewall-application-suicideprevention"}, "1351": {"component": {}, "example_url": "socialsciences.org.au", "filename": "sphirewall-application-socialsciences", "id": "1351", "ignoreme": "0", "name": "Social Sciences", "new_description": "Content involving People, Human culture, and Civilization. Includes information about local events and community organizations.", "newname": "Social Sciences", "parent": "153", "test_url": "test.mysmoothwall.net/sphirewall-application-socialsciences"}, "1352": {"component": {}, "example_url": "sacred-texts.com", "filename": "spirewall-application-occult", "id": "1352", "ignoreme": "0", "name": "Occult", "new_description": "Mystical, supernatural, and pseudoscientific information, discussion, and resources.", "newname": "Occult", "parent": "153", "test_url": "test.mysmoothwall.net/spirewall-application-occult"}, "1353": {"component": {}, "example_url": "", "filename": "social-child", "id": "1353", "ignoreme": "1", "name": " Social Media and Communication", "new_description": "Social networking, dating and chat sites", "newname": " Social Media and Communication", "parent": "252", "test_url": "test.mysmoothwall.net/social-child"}, "1354": {"component": {}, "example_url": "www.facebook.com", "filename": "socialnetworking-parent", "id": "1354", "ignoreme": "0", "name": "Social Networking", "new_description": "Social Networking sites", "newname": "Social Networking", "parent": "159", "test_url": "test.mysmoothwall.net/socialnetworking-parent"}, "1355": {"component": {"domainsurls": 24}, "example_url": "9gag.com", "filename": "sphirewall-applicaiton-9gag", "id": "1355", "ignoreme": "0", "name": "9Gag", "new_description": "9GAG is an app that you’ll find funny posts, news articles, interesting videos and creative photos. Mostly just funny or interesting content, the problem seems to lie with users’ comments, profanity, inappropriate content and pornography. Risks include sexual content, offensive language and bullying.", "newname": "9Gag", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-9gag"}, "1356": {"component": {"domainsurls": 3}, "example_url": "ask.fm", "filename": "sphirewall-applicaiton-ask-fm", "id": "1356", "ignoreme": "0", "name": "ask.fm", "new_description": "Find out what people want to know about you. Ask questions anonymously and get answers on any topic!", "newname": "ask.fm", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-ask-fm"}, "1357": {"component": {"domainsurls": 2}, "example_url": "band.us", "filename": "sphirewall-application-band", "id": "1357", "ignoreme": "0", "name": "Band.us", "new_description": "Organize your groups!", "newname": "Band.us", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-band"}, "1358": {"component": {"domainsurls": 5}, "example_url": "bere.al", "filename": "sphirewall-application-bereal", "id": "1358", "ignoreme": "0", "name": "BeReal", "new_description": "", "newname": "BeReal", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-bereal"}, "1359": {"component": {"domainsurls": 24}, "example_url": "bytedance.com", "filename": "sphirewall-application-bytedance", "id": "1359", "ignoreme": "0", "name": "ByteDance", "new_description": "", "newname": "ByteDance", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-bytedance"}, "1360": {"component": {"domainsurls": 1}, "example_url": "becandid.com", "filename": "sphirewall-application-candid", "id": "1360", "ignoreme": "0", "name": "Candid", "new_description": "Candid - Speak Your Mind Freely", "newname": "Candid", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-candid"}, "1361": {"component": {"domainsurls": 1}, "example_url": "digg.com", "filename": "sphirewall-application-digg", "id": "1361", "ignoreme": "0", "name": "digg", "new_description": "Digg is the homepage of the internet, featuring the best articles, videos, and original content that the web is talking about right now.", "newname": "digg", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-digg"}, "1362": {"component": {"domainsurls": 6}, "example_url": "disqus.com", "filename": "sphirewall-application-disqus", "id": "1362", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON><PERSON>", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-disqus"}, "1363": {"component": {"domainsurls": 17}, "example_url": "douyin.com", "filename": "sphirewall-application-douyin", "id": "1363", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON>", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-douyin"}, "1364": {"component": {"domainsurls": 4}, "example_url": "graph.facebook.com", "filename": "sphirewall-application-facebookgraph", "id": "1364", "ignoreme": "0", "name": "Facebook Graph", "new_description": "", "newname": "Facebook Graph", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-facebookgraph"}, "1365": {"component": {"domainsurls": 13}, "example_url": "flickr.com", "filename": "sphirewall-application-flickr", "id": "1365", "ignoreme": "0", "name": "<PERSON>lickr", "new_description": "Flickr is almost certainly the best online photo management and sharing application in the world. Show off your favorite photos and more", "newname": "<PERSON>lickr", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-flickr"}, "1366": {"component": {"domainsurls": 5}, "example_url": "", "filename": "sphirewall-application-giggle", "id": "1366", "ignoreme": "0", "name": "<PERSON><PERSON>gle", "new_description": "", "newname": "<PERSON><PERSON>gle", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-giggle"}, "1367": {"component": {"domainsurls": 4}, "example_url": "gravatar.com", "filename": "sphirewall-application-gravatar", "id": "1367", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON><PERSON>", "new_description": "Your Gravatar is an image that follows you from site to site appearing beside your name when you do things like comment or post on a blog. Avatars help identify your posts on blogs and web forums, so why not on any site?", "newname": "<PERSON><PERSON><PERSON><PERSON>", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-gravatar"}, "1368": {"component": {"domainsurls": 9}, "example_url": "houseparty.com", "filename": "sphirewall-application-houseparty", "id": "1368", "ignoreme": "0", "name": "Houseparty", "new_description": "‘Houseparty’ is a communication platform where users make video calls with up to 7 other people. Rather than making and receiving calls, users are encouraged to “dip” in and out of any chats, as long as they are a friend of at least one person in the given call. Houseparty offers a messaging feature and has many games included in the app.", "newname": "Houseparty", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-houseparty"}, "1369": {"component": {"domainsurls": 1}, "example_url": "imageshack.com", "filename": "sphirewall-application-imageshack", "id": "1369", "ignoreme": "0", "name": "ImageShack", "new_description": "Image hosting service, upload your images for free.", "newname": "ImageShack", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-imageshack"}, "1370": {"component": {"domainsurls": 1}, "example_url": "img.ly", "filename": "sphirewall-application-imgly", "id": "1370", "ignoreme": "0", "name": "img.ly", "new_description": "img.ly is a photo sharing service for twitter. Less characters, more user experience, that's it!.", "newname": "img.ly", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-imgly"}, "1371": {"component": {"domainsurls": 4}, "example_url": "imgur.com", "filename": "sphirewall-application-imgur", "id": "1371", "ignoreme": "0", "name": "imgur", "new_description": "The Internet's visual storytelling community. Explore, share, and discuss the best visual stories the Internet has to offer.", "newname": "imgur", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-imgur"}, "1372": {"component": {"domainsurls": 4}, "example_url": "secure.imvu.com", "filename": "sphirewall-applicaiton-imvu-com", "id": "1372", "ignoreme": "0", "name": "imvu.com", "new_description": "Make new friends & chat in 3D. IMVU is the #1 avatar social community.", "newname": "imvu.com", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-imvu-com"}, "1373": {"component": {"domainsurls": 12}, "example_url": "kik.com", "filename": "sphirewall-application-kik", "id": "1373", "ignoreme": "0", "name": "Kik", "new_description": "Online messaging application", "newname": "Kik", "parent": "1407", "test_url": "test.mysmoothwall.net/sphirewall-application-kik"}, "1374": {"component": {"domainsurls": 8}, "example_url": "likee.com", "filename": "sphirewall-application-likee", "id": "1374", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON>", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-likee"}, "1375": {"component": {"domainsurls": 9}, "example_url": "linkedin.com", "filename": "sphirewall-application-linkedin", "id": "1375", "ignoreme": "0", "name": "LinkedIn", "new_description": "Manage your professional identity. Build and engage with your professional network.", "newname": "LinkedIn", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-linkedin"}, "1376": {"component": {"domainsurls": 1}, "example_url": "momentumdash.com", "filename": "sphirewall-applicaiton-momentumdash", "id": "1376", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON><PERSON>", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-momentumdash"}, "1377": {"component": {"domainsurls": 3}, "example_url": "immomo.com", "filename": "sphirewall-application-momo", "id": "1377", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "Momo is a fun way to discover, chat and engage with new people and communities near you.", "newname": "<PERSON><PERSON>", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-momo"}, "1378": {"component": {"domainsurls": 3}, "example_url": "myspace.com", "filename": "sphirewall-application-myspace", "id": "1378", "ignoreme": "0", "name": "Myspace", "new_description": "Myspace is a social networking website.", "newname": "Myspace", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-myspace"}, "1379": {"component": {"domainsurls": 1}, "example_url": "neighbourly.co.nz", "filename": "sphirewall-applicaiton-neighbourly-co-nz", "id": "1379", "ignoreme": "0", "name": "Neighbourly", "new_description": "", "newname": "Neighbourly", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-neighbourly-co-nz"}, "1380": {"component": {"domainsurls": 2}, "example_url": "ning.com", "filename": "sphirewall-application-ning", "id": "1380", "ignoreme": "0", "name": "<PERSON>ng", "new_description": "Online platform for people and organizations to create custom social networks", "newname": "<PERSON>ng", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-ning"}, "1381": {"component": {"domainsurls": 1}, "example_url": "ok.ru", "filename": "sphirewall-application-ok", "id": "1381", "ignoreme": "0", "name": "OK", "new_description": "OK.ru is a social network service for classmates and old friends.", "newname": "OK", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-ok"}, "1382": {"component": {"domainsurls": 31}, "example_url": "pearltrees.com", "filename": "sphirewall-applicaiton-pearltrees-com", "id": "1382", "ignoreme": "0", "name": "PearlTrees", "new_description": "", "newname": "PearlTrees", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-pearltrees-com"}, "1383": {"component": {}, "example_url": "photobucket.com", "filename": "sphirewall-application-photobucket", "id": "1383", "ignoreme": "0", "name": "Photobucket", "new_description": "Get free image hosting, easy photo sharing, and photo editing. Upload pictures and videos, create with the online photo editor, or browse albums.", "newname": "Photobucket", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-photobucket"}, "1384": {"component": {"domainsurls": 1}, "example_url": "pic-collage.com", "filename": "sphirewall-application-pic-collage", "id": "1384", "ignoreme": "0", "name": "Pic-Collage", "new_description": "Party with your photos.", "newname": "Pic-Collage", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-pic-collage"}, "1385": {"component": {"domainsurls": 55}, "example_url": "pinterest.com", "filename": "sphirewall-application-pinterest", "id": "1385", "ignoreme": "0", "name": "Pinterest", "new_description": "Pinterest is a visual discovery tool that you can use to find ideas for all your projects and interests", "newname": "Pinterest", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-pinterest"}, "1386": {"component": {"domainsurls": 3}, "example_url": "pixie.xyz", "filename": "sphirewall-application-pixie", "id": "1386", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "Photo and video sharing social network based on Blockchain Cryptoeconomics", "newname": "<PERSON><PERSON><PERSON>", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-pixie"}, "1387": {"component": {"domainsurls": 16}, "example_url": "reddit.com", "filename": "sphirewall-application-reddit", "id": "1387", "ignoreme": "0", "name": "Reddit", "new_description": "Reddit is the most popular forum app and website which as every topic imaginable called 'subreddits'. Children can easily access adult and inappropriate content by agreeing that they are older than 18 years old. No verification is required. Reddit has many good forum topics along with a lot of bad topics. Risks include sexual content, offensive language and bullying.", "newname": "Reddit", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-reddit"}, "1388": {"component": {"domainsurls": 3}, "example_url": "sarahah.com", "filename": "sphirewall-application-sarahah", "id": "1388", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "<PERSON><PERSON> is an anonymous messaging App. <PERSON><PERSON> loosely translates to \"honesty\" in Arabic - and that's why people are worried. Users can be mean. Minimum age: 13 Risks: Cyber stalking & bullying; Screen time and Adult content", "newname": "<PERSON><PERSON>", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-sarahah"}, "1389": {"component": {"domainsurls": 1}, "example_url": "joinsaturn.com", "filename": "sphirewall-application-saturnapp", "id": "1389", "ignoreme": "0", "name": "Saturn App", "new_description": "Saturn Schedule Management and Sharing Application", "newname": "Saturn App", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-saturnapp"}, "1390": {"component": {"domainsurls": 1}, "example_url": "skout.com", "filename": "sphirewall-application-skout", "id": "1390", "ignoreme": "0", "name": "Skout", "new_description": "Location-based social networking and dating application and website.", "newname": "Skout", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-skout"}, "1391": {"component": {"domainsurls": 1}, "example_url": "tagged.com", "filename": "sphirewall-application-tagged", "id": "1391", "ignoreme": "0", "name": "Tagged", "new_description": "Tagged makes it easy to meet and socialize with new people through games, shared interests, friend suggestions, browsing profiles, and much more.", "newname": "Tagged", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-tagged"}, "1392": {"component": {"domainsurls": 5}, "example_url": "tumblr.com", "filename": "sphirewall-application-tumblr", "id": "1392", "ignoreme": "0", "name": "Tumblr", "new_description": "Post anything (from anywhere!), customize everything, and find and follow what you love. Create your own Tumblr blog today.", "newname": "Tumblr", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-tumblr"}, "1393": {"component": {"domainsurls": 3}, "example_url": "twoo.com", "filename": "sphirewall-application-twoo", "id": "1393", "ignoreme": "0", "name": "Twoo", "new_description": "Welcome to the best place to meet new people.", "newname": "Twoo", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-twoo"}, "1394": {"component": {"domainsurls": 12}, "example_url": "vk.com", "filename": "sphirewall-applicaiton-vk", "id": "1394", "ignoreme": "0", "name": "VK", "new_description": "VK is the largest European social network with more than 100 million active users. Our goal is to keep old friends, ex-classmates, neighbours and co-workers in touch.", "newname": "VK", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-vk"}, "1395": {"component": {"domainsurls": 1}, "example_url": "wattpad.com", "filename": "sphirewall-application-wattpad", "id": "1395", "ignoreme": "0", "name": "WattPad", "new_description": "Wattpad is the best place to read and share stories.", "newname": "WattPad", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-wattpad"}, "1396": {"component": {"domainsurls": 3}, "example_url": "weapp.se", "filename": "sphirewall-applicaiton-weapp", "id": "1396", "ignoreme": "0", "name": "WeApp", "new_description": "", "newname": "WeApp", "parent": "489", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-weapp"}, "1397": {"component": {"domainsurls": 4}, "example_url": "weibo.com", "filename": "sphirewall-application-weibo", "id": "1397", "ignoreme": "0", "name": "Weibo", "new_description": "", "newname": "Weibo", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-weibo"}, "1398": {"component": {"domainsurls": 3}, "example_url": "whisper.sh", "filename": "sphirewall-application-whisper", "id": "1398", "ignoreme": "0", "name": "Whisper", "new_description": "Whisper allows users to post and share photo and video messages anonymously.", "newname": "Whisper", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-whisper"}, "1399": {"component": {"domainsurls": 6}, "example_url": "getwinkapp.com", "filename": "sphirewall-application-wink", "id": "1399", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "Wink is a mixture between Snapchat and Tinder. Wink is an app where users swipe left and right on images to find new friends on Snapchat. If you swipe right on a user''s photo and they swipe right on yours, you can add each as friends on Snapchat.", "newname": "<PERSON><PERSON>", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-wink"}, "1400": {"component": {"domainsurls": 5}, "example_url": "yelp.com", "filename": "sphirewall-application-yelp", "id": "1400", "ignoreme": "0", "name": "yelp", "new_description": "", "newname": "yelp", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-yelp"}, "1401": {"component": {"domainsurls": 4}, "example_url": "yikyak.com", "filename": "sphirewall-application-yikyak", "id": "1401", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON>", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-yikyak"}, "1402": {"component": {"domainsurls": 3}, "example_url": "yubo.live", "filename": "sphirewall-applicaiton-yellw-co", "id": "1402", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON>", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-yellw-co"}, "1403": {"component": {"domainsurls": 3}, "example_url": "zhihu.com", "filename": "sphirewall-applicaiton-zhihu", "id": "1403", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON>", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-zhihu"}, "1404": {"component": {}, "example_url": "www.blogspot.com", "filename": "blog-parent", "id": "1404", "ignoreme": "0", "name": "Blogs", "new_description": "Sites hosting blog content of any kind", "newname": "Blogs", "parent": "159", "test_url": "test.mysmoothwall.net/blog-parent"}, "1405": {"component": {"domainsurls": 1}, "example_url": "sparknotes.com", "filename": "sphirewall-application-sparknotes-com", "id": "1405", "ignoreme": "0", "name": "Spark Notes", "new_description": "", "newname": "Spark Notes", "parent": "1404", "test_url": "test.mysmoothwall.net/sphirewall-application-sparknotes-com"}, "1406": {"component": {"domainsurls": 10}, "example_url": "wordpress.com", "filename": "sphirewall-application-wordpress", "id": "1406", "ignoreme": "0", "name": "Wordpress", "new_description": "", "newname": "Wordpress", "parent": "1404", "test_url": "test.mysmoothwall.net/sphirewall-application-wordpress"}, "1407": {"component": {}, "example_url": "www.trillian.im", "filename": "instantmessaging-parent", "id": "1407", "ignoreme": "0", "name": "Messaging and Conferencing", "new_description": "Sites which contain messenger clients and web-based messaging sites, as well as any form of Voice over IP based chat service", "newname": "Messaging and Conferencing", "parent": "159", "test_url": "test.mysmoothwall.net/instantmessaging-parent"}, "1408": {"component": {"domainsurls": 5}, "example_url": "2degrees.nz", "filename": "sphirewall-application-2degreeswificalling", "id": "1408", "ignoreme": "0", "name": "2 Degrees Wifi Calling", "new_description": "", "newname": "2 Degrees Wifi Calling", "parent": "1407", "test_url": "test.mysmoothwall.net/sphirewall-application-2degreeswificalling"}, "1409": {"component": {"domainsurls": 5}, "example_url": "bluejeans.com\t", "filename": "sphirewall-application-bluejeansvc", "id": "1409", "ignoreme": "0", "name": "Bluejeans Video Conferencing", "new_description": " BlueJeans is the world's leader in cloud video conferencing. Easily and securely hold live video calls, webinars, conference calls, and online meetings.", "newname": "Bluejeans Video Conferencing", "parent": "1407", "test_url": "test.mysmoothwall.net/sphirewall-application-bluejeansvc"}, "1410": {"component": {"domainsurls": 30}, "example_url": "gotomeeting.com", "filename": "sphirewall-application-gotomeeting", "id": "1410", "ignoreme": "0", "name": "Go To Meeting", "new_description": "", "newname": "Go To Meeting", "parent": "1407", "test_url": "test.mysmoothwall.net/sphirewall-application-gotomeeting"}, "1411": {"component": {"domainsurls": 1}, "example_url": "groupme.com", "filename": "sphirewall-application-groupme", "id": "1411", "ignoreme": "0", "name": "Group me", "new_description": "", "newname": "Group me", "parent": "1407", "test_url": "test.mysmoothwall.net/sphirewall-application-groupme"}, "1412": {"component": {"domainsurls": 14}, "example_url": "line.me", "filename": "sphirewall-application-line", "id": "1412", "ignoreme": "0", "name": "Line", "new_description": "LINE develops calling and messaging applications for mobile phones in Japan and internationally. It offers LINE, a smartphone application that allows users to make free calls and messaging.", "newname": "Line", "parent": "1407", "test_url": "test.mysmoothwall.net/sphirewall-application-line"}, "1413": {"component": {"domainsurls": 5}, "example_url": "monkey.cool", "filename": "sphirewall-application-monkeycool", "id": "1413", "ignoreme": "0", "name": "monkey.cool", "new_description": "", "newname": "monkey.cool", "parent": "1407", "test_url": "test.mysmoothwall.net/sphirewall-application-monkeycool"}, "1414": {"component": {"domainsurls": 4}, "example_url": "slack.com", "filename": "sphirewall-application-slack", "id": "1414", "ignoreme": "0", "name": "<PERSON><PERSON>ck", "new_description": "Slack brings all your communication together in one place. It’s real-time messaging, archiving and search for modern teams.", "newname": "<PERSON><PERSON>ck", "parent": "1407", "test_url": "test.mysmoothwall.net/sphirewall-application-slack"}, "1415": {"component": {"domainsurls": 4}, "example_url": "teamspeak.com", "filename": "sphirewall-application-teamspeak", "id": "1415", "ignoreme": "0", "name": "TeamSpeak", "new_description": "TeamSpeak is a proprietary voice-over-Internet Protocol (VoIP) application for audio communication between users on a chat channel. The app is popular in the gaming community.", "newname": "TeamSpeak", "parent": "1407", "test_url": "test.mysmoothwall.net/sphirewall-application-teamspeak"}, "1416": {"component": {"domainsurls": 6, "regexpurls": 5}, "example_url": "telegram.org", "filename": "sphirewall-application-telegram", "id": "1416", "ignoreme": "0", "name": "Telegram", "new_description": "Telegram is a non-profit cloud-based instant messaging service available for Android, iOS, Windows Phone, Windows NT, macOS and Linux. Users can send messages and exchange photos, videos, stickers, audio and files of any type.", "newname": "Telegram", "parent": "1407", "test_url": "test.mysmoothwall.net/sphirewall-application-telegram"}, "1417": {"component": {"domainsurls": 6}, "example_url": "twilio.com", "filename": "sphirewall-application-twilio", "id": "1417", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "Twilio develops products to make and receive phone calls, send and receive text messages, and perform other communication functions using its web service APIs.", "newname": "<PERSON><PERSON><PERSON>", "parent": "1407", "test_url": "test.mysmoothwall.net/sphirewall-application-twilio"}, "1418": {"component": {"domainsurls": 16}, "example_url": "vonage.com", "filename": "sphirewall-application-vonage", "id": "1418", "ignoreme": "0", "name": "Vonage", "new_description": "Cloud communications provider.", "newname": "Vonage", "parent": "1407", "test_url": "test.mysmoothwall.net/sphirewall-application-vonage"}, "1419": {"component": {"domainsurls": 26}, "example_url": "webex.com", "filename": "sphirewall-application-webex", "id": "1419", "ignoreme": "0", "name": "Webex", "new_description": "WebEx is a video conferencing solution used for online meetings and presentations, webinars, town halls, online courses and training, and online presentations.", "newname": "Webex", "parent": "1407", "test_url": "test.mysmoothwall.net/sphirewall-application-webex"}, "1421": {"component": {"domainsurls": 125, "regexpurls": 5}, "example_url": "", "filename": "sphirewall-application-wificalling", "id": "1421", "ignoreme": "0", "name": "WiFi Calling", "new_description": "WiFi Calling services", "newname": "WiFi Calling", "parent": "1407", "test_url": "test.mysmoothwall.net/sphirewall-application-wificalling"}, "1422": {"component": {}, "example_url": "www.match.com", "filename": "dating-parent", "id": "1422", "ignoreme": "0", "name": "Dating", "new_description": "Domains and URLs that provide dating and companionship services", "newname": "Dating", "parent": "159", "test_url": "test.mysmoothwall.net/dating-parent"}, "1423": {"component": {"domainsurls": 4}, "example_url": "bumble.com", "filename": "sphirewall-application-bumble", "id": "1423", "ignoreme": "0", "name": "Bumble", "new_description": "", "newname": "Bumble", "parent": "1422", "test_url": "test.mysmoothwall.net/sphirewall-application-bumble"}, "1424": {"component": {"domainsurls": 3}, "example_url": "grindr.com", "filename": "phirewall-application-grindr", "id": "1424", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "Grindr is arguably the most popular gay (male) dating app. Users set a profile picture (usually with no shirt on), set their geographical location, and set their profiles to show whether they are looking for a friendship, relationship, or something ‘right now’. There is a strong theme of casual sexual relationships on Grindr, making it a very unsafe place for kids.", "newname": "<PERSON><PERSON><PERSON>", "parent": "1422", "test_url": "test.mysmoothwall.net/phirewall-application-grindr"}, "1425": {"component": {"domainsurls": 7}, "example_url": "hinge.co", "filename": "sphirewall-application-hinge", "id": "1425", "ignoreme": "0", "name": "Hinge", "new_description": "", "newname": "Hinge", "parent": "1422", "test_url": "test.mysmoothwall.net/sphirewall-application-hinge"}, "1426": {"component": {"domainsurls": 4}, "example_url": "tinder.com", "filename": "sphirewall-application-tinder", "id": "1426", "ignoreme": "0", "name": "<PERSON>der", "new_description": "Tinder is how people meet. It's like real life, but better. Get it for free on iPhone and Android", "newname": "<PERSON>der", "parent": "1422", "test_url": "test.mysmoothwall.net/sphirewall-application-tinder"}, "1427": {"component": {}, "example_url": "", "filename": "gaming", "id": "1427", "ignoreme": "0", "name": "Gaming", "new_description": "", "newname": "Gaming", "parent": "0", "test_url": "test.mysmoothwall.net/gaming"}, "1428": {"component": {}, "example_url": "", "filename": "gaming-child", "id": "1428", "ignoreme": "1", "name": " Gaming", "new_description": "", "newname": " Gaming", "parent": "252", "test_url": "test.mysmoothwall.net/gaming-child"}, "1429": {"component": {}, "example_url": "mathgames.com", "filename": "educational-games-parent", "id": "1429", "ignoreme": "0", "name": "Educational Games", "new_description": "Online games intended for learning and self improvement.", "newname": "Educational Games", "parent": "1427", "test_url": "test.mysmoothwall.net/educational-games-parent"}, "1430": {"component": {"domainsurls": 1}, "example_url": "blockly.games", "filename": "sphirewall-application-blocklygames", "id": "1430", "ignoreme": "0", "name": "Blockly Games", "new_description": "Games for tomorrow's programmers", "newname": "Blockly Games", "parent": "1429", "test_url": "test.mysmoothwall.net/sphirewall-application-blocklygames"}, "1431": {"component": {"domainsurls": 2}, "example_url": "chess.com", "filename": "sphirewall-application-chess", "id": "1431", "ignoreme": "0", "name": "Chess", "new_description": "", "newname": "Chess", "parent": "1429", "test_url": "test.mysmoothwall.net/sphirewall-application-chess"}, "1432": {"component": {"domainsurls": 3}, "example_url": "coolmathgames.com", "filename": "sphirewall-application-coolmath-games-com", "id": "1432", "ignoreme": "0", "name": "Cool Math Games", "new_description": "", "newname": "Cool Math Games", "parent": "1429", "test_url": "test.mysmoothwall.net/sphirewall-application-coolmath-games-com"}, "1433": {"component": {"domainsurls": 3}, "example_url": "www.elevspel.se", "filename": "sphirewall-application-elevspel", "id": "1433", "ignoreme": "0", "name": "Elevspel", "new_description": "Elevspel - Sweden spelling game site", "newname": "Elevspel", "parent": "1429", "test_url": "test.mysmoothwall.net/sphirewall-application-elevspel"}, "1434": {"component": {"domainsurls": 1}, "example_url": "everyplay.com", "filename": "sphirewall-application-everyplay", "id": "1434", "ignoreme": "0", "name": "EveryPlay", "new_description": "Educational games for Math", "newname": "EveryPlay", "parent": "1429", "test_url": "test.mysmoothwall.net/sphirewall-application-everyplay"}, "1435": {"component": {"domainsurls": 1}, "example_url": "geoguessr.com", "filename": "sphirewall-application-geoguessr", "id": "1435", "ignoreme": "0", "name": "geoguessr", "new_description": "Educational site used in geography class", "newname": "geoguessr", "parent": "1429", "test_url": "test.mysmoothwall.net/sphirewall-application-geoguessr"}, "1436": {"component": {"domainsurls": 1}, "example_url": "typetastic.com", "filename": "sphirewall-application-typetastic", "id": "1436", "ignoreme": "0", "name": "Typetastic", "new_description": "", "newname": "Typetastic", "parent": "1429", "test_url": "test.mysmoothwall.net/sphirewall-application-typetastic"}, "1437": {"component": {}, "example_url": "www.playstation.com", "filename": "gamesconsoles-parent", "id": "1437", "ignoreme": "0", "name": "Game Stores and Publishers", "new_description": "Sites that provide authentication for, and content used in, games consoles", "newname": "Game Stores and Publishers", "parent": "1427", "test_url": "test.mysmoothwall.net/gamesconsoles-parent"}, "1438": {"component": {"domainsurls": 15}, "example_url": "blizzard.com", "filename": "sphirewall-application-blizzard", "id": "1438", "ignoreme": "0", "name": "Blizzard", "new_description": " Blizzard is a developer and publisher of video games, responsible for some of the most popular titles worldwide like World of Warcraft, Overwatch, the Starcraft series and the Diablo series", "newname": "Blizzard", "parent": "1437", "test_url": "test.mysmoothwall.net/sphirewall-application-blizzard"}, "1439": {"component": {"domainsurls": 7}, "example_url": "ea.com", "filename": "sphirewall-application-eagames", "id": "1439", "ignoreme": "0", "name": "EA Games", "new_description": "Electronic Arts is a leading publisher of games on Console, PC and Mobile. Games include FIFA, NHL, Madden NFL, Battlefield, The Sims, and many more.", "newname": "EA Games", "parent": "1437", "test_url": "test.mysmoothwall.net/sphirewall-application-eagames"}, "1440": {"component": {"domainsurls": 8}, "example_url": "epicgames.com", "filename": "sphirewall-application-epicgames", "id": "1440", "ignoreme": "0", "name": "Epic Games", "new_description": "", "newname": "Epic Games", "parent": "1437", "test_url": "test.mysmoothwall.net/sphirewall-application-epicgames"}, "1441": {"component": {"domainsurls": 8}, "example_url": "gamemaker.io", "filename": "sphirewall-application-gamemaker", "id": "1441", "ignoreme": "0", "name": "GameMaker", "new_description": "An App for game design.", "newname": "GameMaker", "parent": "1437", "test_url": "test.mysmoothwall.net/sphirewall-application-gamemaker"}, "1442": {"component": {"domainsurls": 3}, "example_url": "gdevelop.io", "filename": "sphirewall-application-gdevelop", "id": "1442", "ignoreme": "0", "name": "GDevelop", "new_description": "GDevelop is an open-source, free, and easy game-making app.", "newname": "GDevelop", "parent": "1437", "test_url": "test.mysmoothwall.net/sphirewall-application-gdevelop"}, "1443": {"component": {"domainsurls": 1}, "example_url": "ninjakiwi.com", "filename": "sphirewall-application-ninjakiwi", "id": "1443", "ignoreme": "0", "name": "Ninja<PERSON>iwi", "new_description": "<PERSON>, creators of the world's most awesome, original free games including Bloons, Bloons Tower Defense and SAS Zombie Assault ...", "newname": "Ninja<PERSON>iwi", "parent": "1437", "test_url": "test.mysmoothwall.net/sphirewall-application-ninjakiwi"}, "1444": {"component": {"domainsurls": 51}, "example_url": "nintendo.com", "filename": "sphirewall-application-nintendo", "id": "1444", "ignoreme": "0", "name": "Nintendo", "new_description": "Nintendo is the home of the Wii U console and Nintendo 3DS and Nintendo 2DS systems, plus new and classic games for all ages.", "newname": "Nintendo", "parent": "1437", "test_url": "test.mysmoothwall.net/sphirewall-application-nintendo"}, "1445": {"component": {"domainsurls": 52}, "example_url": "nvidia.com", "filename": "sphirewall-application-nvidia", "id": "1445", "ignoreme": "0", "name": "Nvidia", "new_description": "", "newname": "Nvidia", "parent": "1437", "test_url": "test.mysmoothwall.net/sphirewall-application-nvidia"}, "1446": {"component": {"domainsurls": 10}, "example_url": "playstation.com", "filename": "sphirewall-application-playstation", "id": "1446", "ignoreme": "0", "name": "Playstation", "new_description": "PlayStation is a gaming brand that consists of four home video game consoles, as well as a media center, an online service, a line of controllers, two handhelds and a phone, as well as multiple magazines.", "newname": "Playstation", "parent": "1437", "test_url": "test.mysmoothwall.net/sphirewall-application-playstation"}, "1447": {"component": {"domainsurls": 36}, "example_url": "pokemon.com", "filename": "sphirewall-application-pokemon", "id": "1447", "ignoreme": "0", "name": "Pokemon", "new_description": "", "newname": "Pokemon", "parent": "1437", "test_url": "test.mysmoothwall.net/sphirewall-application-pokemon"}, "1448": {"component": {"domainsurls": 3}, "example_url": "poki.com", "filename": "sphirewall-application-poki", "id": "1448", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON>", "parent": "1437", "test_url": "test.mysmoothwall.net/sphirewall-application-poki"}, "1449": {"component": {"domainsurls": 1}, "example_url": "stadia.google.com", "filename": "sphirewall-application-stadia", "id": "1449", "ignoreme": "0", "name": "Stadia", "new_description": "", "newname": "Stadia", "parent": "1437", "test_url": "test.mysmoothwall.net/sphirewall-application-stadia"}, "1450": {"component": {"domainsurls": 32}, "example_url": "steampowered.com", "filename": "sphirewall-application-steam", "id": "1450", "ignoreme": "0", "name": "Steam", "new_description": "\nSteam is a digital distribution platform developed by Valve Corporation, which offers digital rights management (DRM), multiplayer gaming, and social networking services.", "newname": "Steam", "parent": "1437", "test_url": "test.mysmoothwall.net/sphirewall-application-steam"}, "1451": {"component": {"domainsurls": 5}, "example_url": "ubisoft.com", "filename": "sphirewall-application-ubisoftconnect", "id": "1451", "ignoreme": "0", "name": "Ubisoft Connect", "new_description": "", "newname": "Ubisoft Connect", "parent": "1437", "test_url": "test.mysmoothwall.net/sphirewall-application-ubisoftconnect"}, "1452": {"component": {"domainsurls": 81}, "example_url": "xbox.com", "filename": "sphirewall-application-xbox", "id": "1452", "ignoreme": "0", "name": "Xbox", "new_description": "Xbox is a video game console owned by Microsoft. This app tracks the console itself while the Xbox Live app tracks gaming on other Xbox traffic.", "newname": "Xbox", "parent": "1437", "test_url": "test.mysmoothwall.net/sphirewall-application-xbox"}, "1453": {"component": {}, "example_url": "www.miniclip.com", "filename": "onlinegames-parent", "id": "1453", "ignoreme": "0", "name": "Video Games", "new_description": "Sites providing games that can be played in a browser", "newname": "Video Games", "parent": "1427", "test_url": "test.mysmoothwall.net/onlinegames-parent"}, "1454": {"component": {"domainsurls": 47}, "example_url": "agar.io", "filename": "sphirewall-application-agar", "id": "1454", "ignoreme": "0", "name": "Agario", "new_description": "Eat cells smaller than you and don't get eaten by the bigger ones, as an MMO.", "newname": "Agario", "parent": "1453", "test_url": "test.mysmoothwall.net/sphirewall-application-agar"}, "1455": {"component": {"domainsurls": 5}, "example_url": "animaljam.com", "filename": "sphirewall-application-animaljam", "id": "1455", "ignoreme": "0", "name": "Animaljam", "new_description": "", "newname": "Animaljam", "parent": "1453", "test_url": "test.mysmoothwall.net/sphirewall-application-animaljam"}, "1456": {"component": {"domainsurls": 1}, "example_url": "callofwar.com", "filename": "sphirewall-application-callofwar", "id": "1456", "ignoreme": "0", "name": "Call of War", "new_description": "", "newname": "Call of War", "parent": "1453", "test_url": "test.mysmoothwall.net/sphirewall-application-callofwar"}, "1457": {"component": {"domainsurls": 1}, "example_url": "clashroyale.com", "filename": "sphirewall-application-clashroyal", "id": "1457", "ignoreme": "0", "name": "ClashRoyal", "new_description": "", "newname": "ClashRoyal", "parent": "1453", "test_url": "test.mysmoothwall.net/sphirewall-application-clashroyal"}, "1458": {"component": {"domainsurls": 1}, "example_url": "clickerheroes.com", "filename": "sphirewall-application-clickerheroes", "id": "1458", "ignoreme": "0", "name": "Clicker Heroes", "new_description": "", "newname": "Clicker Heroes", "parent": "1453", "test_url": "test.mysmoothwall.net/sphirewall-application-clickerheroes"}, "1459": {"component": {"domainsurls": 203}, "example_url": "cprewritten.net", "filename": "sphirewall-application-clubpenguin", "id": "1459", "ignoreme": "0", "name": "Club Penguin", "new_description": "Club Penguin is a child friendly social media website", "newname": "Club Penguin", "parent": "1453", "test_url": "test.mysmoothwall.net/sphirewall-application-clubpenguin"}, "1460": {"component": {"domainsurls": 1}, "example_url": "strike.ngames.com", "filename": "sphirewall-application-globalstrike", "id": "1460", "ignoreme": "0", "name": "GlobalStrike", "new_description": "", "newname": "GlobalStrike", "parent": "1453", "test_url": "test.mysmoothwall.net/sphirewall-application-globalstrike"}, "1461": {"component": {"domainsurls": 6}, "example_url": "harrypotterwizardsunite.com", "filename": "sphirewall-application-harry<PERSON><PERSON><PERSON>", "id": "1461", "ignoreme": "0", "name": "<PERSON> Wizards Unite", "new_description": "", "newname": "<PERSON> Wizards Unite", "parent": "1453", "test_url": "test.mysmoothwall.net/sphirewall-application-harrypotterwu"}, "1462": {"component": {"domainsurls": 16}, "example_url": "krunker.io", "filename": "sphirewall-application-krunkerio", "id": "1462", "ignoreme": "0", "name": "KrunkerIO", "new_description": "Krunker.io is a free io Multiplayer First Person Shooter", "newname": "KrunkerIO", "parent": "1453", "test_url": "test.mysmoothwall.net/sphirewall-application-krunkerio"}, "1463": {"component": {"domainsurls": 4}, "example_url": "pubgmobile.com", "filename": "sphirewall-application-pubg", "id": "1463", "ignoreme": "0", "name": "PlayerUnknowns Battlegrounds", "new_description": "", "newname": "PlayerUnknowns Battlegrounds", "parent": "1453", "test_url": "test.mysmoothwall.net/sphirewall-application-pubg"}, "1464": {"component": {"domainsurls": 5}, "example_url": "pokemongo.com", "filename": "sphirewall-application-pokemongo", "id": "1464", "ignoreme": "0", "name": "PokemonGo", "new_description": "Pokemon Go is a free-to-play location-based augmented reality mobile game. Known Risks: Location Tracking, Physical Interaction, Predators, In-app Purchases", "newname": "PokemonGo", "parent": "1453", "test_url": "test.mysmoothwall.net/sphirewall-application-pokemongo"}, "1465": {"component": {"domainsurls": 21, "regexpurls": 1}, "example_url": "roblox.com", "filename": "sphirewall-application-roblox", "id": "1465", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "Roblox is a massively multiplayer online game creation platform that allows users to design their own games and play a wide variety of different types of games.", "newname": "<PERSON><PERSON><PERSON>", "parent": "1453", "test_url": "test.mysmoothwall.net/sphirewall-application-roblox"}, "1466": {"component": {"domainsurls": 7}, "example_url": "rocketleague.com", "filename": "sphirewall-application-rocketleague", "id": "1466", "ignoreme": "0", "name": "Rocket League", "new_description": "", "newname": "Rocket League", "parent": "1453", "test_url": "test.mysmoothwall.net/sphirewall-application-rocketleague"}, "1467": {"component": {}, "example_url": "slither.io", "filename": "sphirewall-application-slitherio", "id": "1467", "ignoreme": "0", "name": "SlitherIO", "new_description": "", "newname": "SlitherIO", "parent": "1453", "test_url": "test.mysmoothwall.net/sphirewall-application-slitherio"}, "1468": {"component": {"domainsurls": 1}, "example_url": "smitegame.com", "filename": "sphirewall-application-smitegaming", "id": "1468", "ignoreme": "0", "name": "SMITE", "new_description": "", "newname": "SMITE", "parent": "1453", "test_url": "test.mysmoothwall.net/sphirewall-application-smitegaming"}, "1469": {"component": {"domainsurls": 4}, "example_url": "tankionline.com", "filename": "sphirewall-application-tankionline-com", "id": "1469", "ignoreme": "0", "name": "Tankionline", "new_description": "Battle tanks online", "newname": "Tankionline", "parent": "1453", "test_url": "test.mysmoothwall.net/sphirewall-application-tankionline-com"}, "147": {"component": {"domainsurls": 629, "regexpurls": 5}, "example_url": "windowsupdate.microsoft.com", "filename": "windowsupdates", "id": "147", "ignoreme": "0", "name": " Software Updates", "new_description": "Domains used to download software updates", "newname": " Software Updates", "parent": "1316", "test_url": "test.mysmoothwall.net/windowsupdates"}, "1470": {"component": {"domainsurls": 1}, "example_url": "wizard101.com", "filename": "sphirewall-application-wizard101", "id": "1470", "ignoreme": "0", "name": "Wizard101", "new_description": "", "newname": "Wizard101", "parent": "1453", "test_url": "test.mysmoothwall.net/sphirewall-application-wizard101"}, "1471": {"component": {}, "example_url": "www.ign.com", "filename": "games-parent", "id": "1471", "ignoreme": "0", "name": "Gaming Resources", "new_description": "Sites related to computer games but not in browser playable games", "newname": "Gaming Resources", "parent": "1427", "test_url": "test.mysmoothwall.net/games-parent"}, "1472": {"component": {"domainsurls": 25}, "example_url": "discord.com", "filename": "sphirewall-applicaiton-discordapp", "id": "1472", "ignoreme": "0", "name": "Discord", "new_description": "Discord is a voip and web chat app that is specifically targeted towards gaming and online communities. Unlike skype were to chat with people you firstly need to know their account, discord creates servers that anyone can join. These chat groups are completely unmoderated and are often have pornography and other inappropriate content. Risks include sexual content, sexual predators, offensive language, cyberbullying, private messaging.", "newname": "Discord", "parent": "1471", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-discordapp"}, "1473": {"component": {"domainsurls": 1}, "example_url": "mulvaneystudios.com", "filename": "sphirewall-application-mulvaneystudios", "id": "1473", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "new_description": "interactive digital content games", "newname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "1471", "test_url": "test.mysmoothwall.net/sphirewall-application-mulvaneystudios"}, "1474": {"component": {"domainsurls": 1}, "example_url": "playvs.com", "filename": "sphirewall-application-playvs", "id": "1474", "ignoreme": "0", "name": "playvs", "new_description": "", "newname": "playvs", "parent": "1471", "test_url": "test.mysmoothwall.net/sphirewall-application-playvs"}, "1475": {"component": {"domainsurls": 1}, "example_url": "weblfg.org", "filename": "sphirewall-application-weblfg", "id": "1475", "ignoreme": "0", "name": "Weblfg", "new_description": "weblfg.org is a gaming site.", "newname": "Weblfg", "parent": "1471", "test_url": "test.mysmoothwall.net/sphirewall-application-weblfg"}, "1476": {"component": {"domainsurls": 5}, "example_url": "education.minecraft.net", "filename": "sphirewall-application-xboxminecraftedu", "id": "1476", "ignoreme": "0", "name": "Xbox Minecraft Education", "new_description": "", "newname": "Xbox Minecraft Education", "parent": "1471", "test_url": "test.mysmoothwall.net/sphirewall-application-xboxminecraftedu"}, "1477": {"component": {}, "example_url": "", "filename": "shopping-and-travel", "id": "1477", "ignoreme": "0", "name": "Shopping and Travel", "new_description": "", "newname": "Shopping and Travel", "parent": "0", "test_url": "test.mysmoothwall.net/shopping-and-travel"}, "1478": {"component": {}, "example_url": "", "filename": "shopping-and-travel-child", "id": "1478", "ignoreme": "1", "name": " Shopping and Travel", "new_description": "", "newname": " Shopping and Travel", "parent": "252", "test_url": "test.mysmoothwall.net/shopping-and-travel-child"}, "1479": {"component": {}, "example_url": "www.sainsburys.co.uk", "filename": "ecommerce-parent", "id": "1479", "ignoreme": "0", "name": "Shopping", "new_description": "Sites where purchases can be made, of any kind", "newname": "Shopping", "parent": "1477", "test_url": "test.mysmoothwall.net/ecommerce-parent"}, "1480": {"component": {"domainsurls": 6}, "example_url": "alibaba.com", "filename": "sphirewall-application-alibaba", "id": "1480", "ignoreme": "0", "name": "Alibaba", "new_description": "\nAlibaba is a Chinese multinational conglomerate holding company specializing in e-commerce, retail, Internet, and technology", "newname": "Alibaba", "parent": "1479", "test_url": "test.mysmoothwall.net/sphirewall-application-alibaba"}, "1481": {"component": {"domainsurls": 78}, "example_url": "amazon.com", "filename": "sphirewall-application-amazon", "id": "1481", "ignoreme": "0", "name": "Amazon", "new_description": "Online shopping from the earth's biggest selection of books, magazines, music, DVDs, videos, electronics, computers, software...", "newname": "Amazon", "parent": "1479", "test_url": "test.mysmoothwall.net/sphirewall-application-amazon"}, "1482": {"component": {"domainsurls": 18}, "example_url": "costco.com", "filename": "sphirewall-application-costco", "id": "1482", "ignoreme": "0", "name": "Costco", "new_description": "Costco is operator of discount retail stores of the type known as warehouse clubs or wholesale clubs, in which bulk quantities of merchandise are sold at deeply discounted prices to club members.", "newname": "Costco", "parent": "1479", "test_url": "test.mysmoothwall.net/sphirewall-application-costco"}, "1483": {"component": {"domainsurls": 31}, "example_url": "ebay.com", "filename": "sphirewall-application-ebay", "id": "1483", "ignoreme": "0", "name": "Ebay", "new_description": "Buy and sell electronics, cars, fashion apparel, collectibles, sporting goods, digital cameras, baby items, coupons, and everything else on...", "newname": "Ebay", "parent": "1479", "test_url": "test.mysmoothwall.net/sphirewall-application-ebay"}, "1484": {"component": {"domainsurls": 4}, "example_url": "etsy.com", "filename": "sphirewall-application-etsy", "id": "1484", "ignoreme": "0", "name": "Etsy", "new_description": "\nEtsy is an e-commerce site and a mobile app for buying and selling handmade and vintage items. Users can find products in the categories of clothing and accessories, jewelry, craft supplies and tools, and more.", "newname": "Etsy", "parent": "1479", "test_url": "test.mysmoothwall.net/sphirewall-application-etsy"}, "1485": {"component": {"domainsurls": 550}, "example_url": "www.greetingscards.co.uk", "filename": "sphirewall-application-greetingcards", "id": "1485", "ignoreme": "0", "name": "Greeting Cards", "new_description": "Online Greeting card sites.", "newname": "Greeting Cards", "parent": "1479", "test_url": "test.mysmoothwall.net/sphirewall-application-greetingcards"}, "1486": {"component": {"domainsurls": 19}, "example_url": "ikea.com", "filename": "sphirewall-application-ikea", "id": "1486", "ignoreme": "0", "name": "IKEA", "new_description": "IKEA sells furniture and home accessories that are practical, well designed and affordable.", "newname": "IKEA", "parent": "1479", "test_url": "test.mysmoothwall.net/sphirewall-application-ikea"}, "1487": {"component": {"domainsurls": 1}, "example_url": "nextdoor.com", "filename": "sphirewall-application-nextdoor", "id": "1487", "ignoreme": "0", "name": "Nextdoor.com", "new_description": "", "newname": "Nextdoor.com", "parent": "1354", "test_url": "test.mysmoothwall.net/sphirewall-application-nextdoor"}, "1488": {"component": {"domainsurls": 11}, "example_url": "shopify.com", "filename": "sphirewall-application-shopify", "id": "1488", "ignoreme": "0", "name": "Shopify", "new_description": "\nShopify is an e-commerce platform that has everything needed to sell online, on social media, or in person.", "newname": "Shopify", "parent": "1479", "test_url": "test.mysmoothwall.net/sphirewall-application-shopify"}, "1489": {"component": {"domainsurls": 18}, "example_url": "shutterfly.com", "filename": "sphirewall-application-shutterfly", "id": "1489", "ignoreme": "0", "name": "Shutterfly", "new_description": "", "newname": "Shutterfly", "parent": "1479", "test_url": "test.mysmoothwall.net/sphirewall-application-shutterfly"}, "1490": {"component": {"domainsurls": 35}, "example_url": "ticketmaster.com", "filename": "s<PERSON><PERSON>all-application-ticketmaster", "id": "1490", "ignoreme": "0", "name": "Ticketmaster", "new_description": "", "newname": "Ticketmaster", "parent": "1479", "test_url": "test.mysmoothwall.net/sphirewall-application-ticketmaster"}, "1491": {"component": {"domainsurls": 3}, "example_url": "tmall.com", "filename": "sphirewall-application-tmall", "id": "1491", "ignoreme": "0", "name": "Tmall", "new_description": "", "newname": "Tmall", "parent": "1479", "test_url": "test.mysmoothwall.net/sphirewall-application-tmall"}, "1492": {"component": {"domainsurls": 3}, "example_url": "trademe.co.nz", "filename": "sphirewall-application-trademe", "id": "1492", "ignoreme": "0", "name": "Trademe", "new_description": "New Zealand's leading online auction store. ", "newname": "Trademe", "parent": "1479", "test_url": "test.mysmoothwall.net/sphirewall-application-trademe"}, "1493": {"component": {"domainsurls": 8}, "example_url": "walmart.com", "filename": "sphirewall-application-walmart", "id": "1493", "ignoreme": "0", "name": "Walmart", "new_description": "\nWalmart is a multinational retail corporation that operates a chain of hypermarkets, discount department stores, and grocery stores.", "newname": "Walmart", "parent": "1479", "test_url": "test.mysmoothwall.net/sphirewall-application-walmart"}, "1494": {"component": {"domainsurls": 1}, "example_url": "wish.com", "filename": "sphirewall-application-wish", "id": "1494", "ignoreme": "0", "name": "Wish", "new_description": "Shopping Made Fun. Join over 300 million others that have made their shopping more smart, fun, and rewarding.", "newname": "Wish", "parent": "1479", "test_url": "test.mysmoothwall.net/sphirewall-application-wish"}, "1495": {"component": {}, "example_url": "www.nationalrail.co.uk", "filename": "travel-parent", "id": "1495", "ignoreme": "0", "name": "Travel", "new_description": "Sites related to travel, transportation, and holidaying", "newname": "Travel", "parent": "1477", "test_url": "test.mysmoothwall.net/travel-parent"}, "1496": {"component": {"domainsurls": 37}, "example_url": "expedia.com", "filename": "sphirewall-application-expedia", "id": "1496", "ignoreme": "0", "name": "Expedia", "new_description": "Plan your trip with Expedia. Buy airline tickets, read reviews & reserve a hotel. Find deals on vacations, rental cars & cruises. Great prices...", "newname": "Expedia", "parent": "1495", "test_url": "test.mysmoothwall.net/sphirewall-application-expedia"}, "1497": {"component": {"domainsurls": 7}, "example_url": "hostels.com", "filename": "sphirewall-application-hostels-com", "id": "1497", "ignoreme": "0", "name": "Hostels.com", "new_description": "Make your trip go further with Hostels.com. We offer the most comprehensive selection of hostels on the internet with over 35,000...", "newname": "Hostels.com", "parent": "1495", "test_url": "test.mysmoothwall.net/sphirewall-application-hostels-com"}, "1498": {"component": {"domainsurls": 3}, "example_url": "hotels.com", "filename": "sphirewall-application-hotels-com", "id": "1498", "ignoreme": "0", "name": "Hotels.com", "new_description": "Find cheap hotels and discounts when you book on Hotels.com. Compare hotel deals, offers and read unbiased reviews on hotels.", "newname": "Hotels.com", "parent": "1495", "test_url": "test.mysmoothwall.net/sphirewall-application-hotels-com"}, "1499": {"component": {"domainsurls": 3}, "example_url": "houseoftravel.co.nz", "filename": "sphirewall-application-houseoftravel-co-nz", "id": "1499", "ignoreme": "0", "name": "House of Travel", "new_description": "Search and compare cheap flights and holidays from all major airlines to destinations across the globe.", "newname": "House of Travel", "parent": "1495", "test_url": "test.mysmoothwall.net/sphirewall-application-houseoftravel-co-nz"}, "1500": {"component": {"domainsurls": 50}, "example_url": "kayak.com", "filename": "sphirewall-application-kayak", "id": "1500", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "KAYAK is a travel search engine. KAYAK searches hundreds of travel sites to help you find and book the flight or hotel that suits you best.", "newname": "<PERSON><PERSON>", "parent": "1495", "test_url": "test.mysmoothwall.net/sphirewall-application-kayak"}, "1501": {"component": {"domainsurls": 1}, "example_url": "orbitz.com", "filename": "sphirewall-application-orbitz", "id": "1501", "ignoreme": "0", "name": "Orbitz", "new_description": "Book cheap airline tickets, hotel reservations, car rentals, vacations and travel deals on Orbitz.", "newname": "Orbitz", "parent": "1495", "test_url": "test.mysmoothwall.net/sphirewall-application-orbitz"}, "1502": {"component": {"domainsurls": 29}, "example_url": "tripadvisor.com", "filename": "sphirewall-application-tripadvisor", "id": "1502", "ignoreme": "0", "name": "Tripadvisor", "new_description": "World's Largest Travel Site. 200 million+ unbiased traveler reviews. Search 200+ sites to find the best hotel prices.", "newname": "Tripadvisor", "parent": "1495", "test_url": "test.mysmoothwall.net/sphirewall-application-tripadvisor"}, "1503": {"component": {"domainsurls": 1}, "example_url": "uber.com", "filename": "sphirewall-application-uber", "id": "1503", "ignoreme": "0", "name": "Uber", "new_description": "", "newname": "Uber", "parent": "1495", "test_url": "test.mysmoothwall.net/sphirewall-application-uber"}, "152": {"component": {}, "example_url": "www.vimeo.com", "filename": "multimedia", "id": "152", "ignoreme": "0", "name": "Streaming Media", "new_description": "Digital video and image hosting content including TV shows and movies.", "newname": "Streaming Media", "parent": "160", "test_url": "test.mysmoothwall.net/multimedia"}, "153": {"component": {}, "example_url": "", "filename": "people-and-society", "id": "153", "ignoreme": "0", "name": "People and Society", "new_description": "Various inoffensive yet non-work-related sites", "newname": "People and Society", "parent": "0", "test_url": "test.mysmoothwall.net/people-and-society"}, "155": {"component": {}, "example_url": "", "filename": "malhacking", "id": "155", "ignoreme": "0", "name": "Malware and Hacking", "new_description": "Hacking, warez and phishing sites, including sites containing information on how to bypass web filters", "newname": "Malware and Hacking", "parent": "0", "test_url": "test.mysmoothwall.net/malhacking"}, "159": {"component": {}, "example_url": "", "filename": "social", "id": "159", "ignoreme": "0", "name": "Social Media and Communication", "new_description": "Social networking, dating and chat sites", "newname": "Social Media and Communication", "parent": "0", "test_url": "test.mysmoothwall.net/social"}, "16": {"component": {"domainsurls": 1}, "example_url": "zxr-e.jaysbrand.com", "filename": "malware", "id": "16", "ignoreme": "0", "name": "Malware and Phishing", "new_description": "Sites hosting viruses, malware, adware, spyware or trojans", "newname": "Malware and Phishing", "parent": "155", "test_url": "test.mysmoothwall.net/malware"}, "160": {"component": {}, "example_url": "", "filename": "entertainment", "id": "160", "ignoreme": "0", "name": "Arts and Entertainment", "new_description": "Entertainment, sport and online games", "newname": "Arts and Entertainment", "parent": "0", "test_url": "test.mysmoothwall.net/entertainment"}, "171": {"component": {"domainsurls": 8, "searchterms": 115, "weightedphrases": 406}, "example_url": "https://www.missingkids.org/home", "filename": "childabuse", "id": "171", "ignoreme": "0", "name": "Child Abuse", "new_description": "Child abuse keywords contributed by the IWF", "newname": "Child Abuse", "parent": "436", "test_url": "test.mysmoothwall.net/childabuse"}, "18": {"component": {"domainsurls": 2703, "regexpurls": 2, "searchterms": 1, "weightedphrases": 153}, "example_url": "www.trillian.im", "filename": "instantmessaging", "id": "18", "ignoreme": "0", "name": " Messaging and Conferencing", "new_description": "Sites which contain messenger clients and web-based messaging sites, as well as any form of Voice over IP based chat service", "newname": " Messaging and Conferencing", "parent": "1407", "test_url": "test.mysmoothwall.net/instantmessaging"}, "180": {"component": {"domainsurls": 973, "searchterms": 11, "weightedphrases": 51}, "example_url": "www.nps.gov/spar", "filename": "military", "id": "180", "ignoreme": "0", "name": "Military", "new_description": "Sites describing weapons of war, including those in a historical context.", "newname": "Military", "parent": "437", "test_url": "test.mysmoothwall.net/military"}, "181": {"component": {"domainsurls": 8947, "searchterms": 94, "weightedphrases": 1602}, "example_url": "www.gunsite.com", "filename": "personalweapons", "id": "181", "ignoreme": "0", "name": "Weapons", "new_description": "Sites discussing and/or selling of weapons, including firearms, blades, explosives and incendiaries.", "newname": "Weapons", "parent": "436", "test_url": "test.mysmoothwall.net/personalweapons"}, "182": {"component": {"domainsurls": 7748, "weightedphrases": 297}, "example_url": "www.thehuntinglife.com", "filename": "huntingsporting", "id": "182", "ignoreme": "0", "name": "Hunting and Sporting", "new_description": "Hunting and target shooting weapons.", "newname": "Hunting and Sporting", "parent": "436", "test_url": "test.mysmoothwall.net/huntingsporting"}, "183": {"component": {"domainsurls": 5591, "weightedphrases": 2}, "example_url": "www.imdb.com", "filename": "moviesfilm", "id": "183", "ignoreme": "0", "name": " Movies and Film", "new_description": "Movie sites, reviews and discussion", "newname": " Movies and Film", "parent": "784", "test_url": "test.mysmoothwall.net/moviesfilm"}, "184": {"component": {"domainsurls": 13799, "searchterms": 1, "videoids": 1, "weightedphrases": 9}, "example_url": "www.ajokeaday.com", "filename": "jokeshumour", "id": "184", "ignoreme": "0", "name": "Humour and Distractions", "new_description": "Unproductive sites popular with children, that are often dedicated to humour, including jokes, video, amusing images and satire", "newname": "Humour and Distractions", "parent": "160", "test_url": "test.mysmoothwall.net/jokeshumour"}, "185": {"component": {"domainsurls": 12560, "weightedphrases": 55}, "example_url": "www.lrb.co.uk", "filename": "books", "id": "185", "ignoreme": "0", "name": " Books and Literature", "new_description": "Sites selling, reviewing or discussing books or audio books in any format", "newname": " Books and Literature", "parent": "880", "test_url": "test.mysmoothwall.net/books"}, "187": {"component": {"domainsurls": 7879, "regexpurls": 4, "weightedphrases": 1}, "example_url": "www.blogspot.com", "filename": "blog", "id": "187", "ignoreme": "0", "name": " Blogs", "new_description": "Sites hosting blog content of any kind", "newname": " Blogs", "parent": "1404", "test_url": "test.mysmoothwall.net/blog"}, "188": {"component": {"domainsurls": 814}, "example_url": "bit.ly", "filename": "urlshortening", "id": "188", "ignoreme": "0", "name": "URL Shortening", "new_description": "Sites which provide a URL shortening and forwarding / link service", "newname": "URL Shortening", "parent": "241", "test_url": "test.mysmoothwall.net/urlshortening"}, "189": {"component": {"domainsurls": 547, "regexpurls": 1}, "example_url": "google.com/maps", "filename": "mapping", "id": "189", "ignoreme": "0", "name": " Maps", "new_description": "Sites which provide geographical mapping services including those that promote or provide opportunity for travel planning", "newname": " Maps", "parent": "875", "test_url": "test.mysmoothwall.net/mapping"}, "19": {"component": {"domainsurls": 24836, "regexpurls": 17, "searchterms": 123, "weightedphrases": 1619}, "example_url": "www.miniclip.com", "filename": "onlinegames", "id": "19", "ignoreme": "0", "name": " Video Games", "new_description": "Sites providing games that can be played in a browser", "newname": " Video Games", "parent": "1453", "test_url": "test.mysmoothwall.net/onlinegames"}, "190": {"component": {"domainsurls": 6793, "weightedphrases": 37}, "example_url": "www.chicagothemusical.com", "filename": "stage", "id": "190", "ignoreme": "0", "name": "Events", "new_description": "Sites that promote and discuss live events e.g. live dramatic/theatrical productions and festivals", "newname": "Events", "parent": "160", "test_url": "test.mysmoothwall.net/stage"}, "191": {"component": {"domainsurls": 37987, "weightedphrases": 202}, "example_url": "www.lyrics.com", "filename": "music", "id": "191", "ignoreme": "0", "name": " Music and Audio", "new_description": "Sites that discuss, promote/market and distribute music.  Including fan sites, lyrics sites, playlist sites, artist and musical subjects sites", "newname": " Music and Audio", "parent": "771", "test_url": "test.mysmoothwall.net/music"}, "192": {"component": {"domainsurls": 3767, "regexpurls": 1, "searchterms": 19, "weightedphrases": 53}, "example_url": "www.perezhilton.com", "filename": "celebrity", "id": "192", "ignoreme": "0", "name": "Celebrity", "new_description": "Sites relating to any celebrity media, fan sites or news", "newname": "Celebrity", "parent": "160", "test_url": "test.mysmoothwall.net/celebrity"}, "193": {"component": {"domainsurls": 1475, "regexpurls": 3, "weightedphrases": 61}, "example_url": "www.mega.io", "filename": "filehosting", "id": "193", "ignoreme": "0", "name": " File Sharing", "new_description": "Sites that offer online file storage services on remote servers for backup or exchange purposes", "newname": " File Sharing", "parent": "1324", "test_url": "test.mysmoothwall.net/filehosting"}, "194": {"component": {"domainsurls": 640, "searchterms": 8, "weightedphrases": 20}, "example_url": "www.yummylook.com", "filename": "provocative", "id": "194", "ignoreme": "0", "name": "Intimate Apparel and Swimwear", "new_description": "Sites which contain pictures of intimate, alluring, revealing attire or other types of suggestive clothing, such as lingerie and swimsuits.", "newname": "Intimate Apparel and Swimwear", "parent": "436", "test_url": "test.mysmoothwall.net/provocative"}, "195": {"component": {"domainsurls": 6562}, "example_url": "www.vam.ac.uk", "filename": "museumsgalleries", "id": "195", "ignoreme": "0", "name": "Museums and Culture", "new_description": "Sites that promote, exhibit, and/or display works of art or objects of historical and or cultural significance", "newname": "Museums and Culture", "parent": "396", "test_url": "test.mysmoothwall.net/museumsgalleries"}, "197": {"component": {"domainsurls": 109}, "example_url": "www.photobucket.com", "filename": "moderatedimagehosting", "id": "197", "ignoreme": "0", "name": "Image Hosting: Moderated", "new_description": "Sites that provide image/gif hosting services that are vetted or controlled with a conditions of use policy or moderated by humans", "newname": "Image Hosting: Moderated", "parent": "241", "test_url": "test.mysmoothwall.net/moderatedimagehosting"}, "198": {"component": {"domainsurls": 2684, "regexpurls": 6}, "example_url": "www.imgur.com", "filename": "unmoderatedimagehosting", "id": "198", "ignoreme": "0", "name": "Image Hosting: Unmoderated", "new_description": "Sites that provide image/gif hosting services that include potentially pornographic or otherwise offensive content", "newname": "Image Hosting: Unmoderated", "parent": "241", "test_url": "test.mysmoothwall.net/unmoderatedimagehosting"}, "199": {"component": {"domainsurls": 17375, "weightedphrases": 124}, "example_url": "www.realtor.com", "filename": "realestate", "id": "199", "ignoreme": "0", "name": "Real Estate", "new_description": "Sites that discuss, promote/market and provide information on renting, buying, or selling real estate or properties", "newname": "Real Estate", "parent": "153", "test_url": "test.mysmoothwall.net/realestate"}, "2": {"component": {"domainsurls": 19670, "weightedphrases": 215}, "example_url": "www.tiffany.com", "filename": "clothing", "id": "2", "ignoreme": "0", "name": "Clothing and Appearance", "new_description": "Sites selling, promoting and discussing clothing, shoes, jewellery and fashion", "newname": "Clothing and Appearance", "parent": "1477", "test_url": "test.mysmoothwall.net/clothing"}, "20": {"component": {"domainsurls": 5085, "regexpurls": 4, "searchterms": 9, "weightedphrases": 534}, "example_url": "www.match.com", "filename": "dating", "id": "20", "ignoreme": "0", "name": " Dating", "new_description": "Domains and URLs that provide dating and companionship services", "newname": " Dating", "parent": "1422", "test_url": "test.mysmoothwall.net/dating"}, "200": {"component": {"domainsurls": 16487, "regexpurls": 2, "searchterms": 22, "videoids": 3299, "weightedphrases": 13}, "example_url": "www.sparknotes.com", "filename": "education", "id": "200", "ignoreme": "0", "name": " Education", "new_description": "Sites that provide materials and information that aid in learning", "newname": " Education", "parent": "882", "test_url": "test.mysmoothwall.net/education"}, "201": {"component": {"domainsurls": 4078, "regexpurls": 1, "weightedphrases": 93}, "example_url": "www.thetoyshop.com", "filename": "toysgames", "id": "201", "ignoreme": "0", "name": "Toys", "new_description": "Sites which promote, discuss and sell toys and games", "newname": "Toys", "parent": "153", "test_url": "test.mysmoothwall.net/toysgames"}, "202": {"component": {"domainsurls": 27776, "searchterms": 9, "weightedphrases": 671}, "example_url": "www.harley-davidson.com", "filename": "vehicles", "id": "202", "ignoreme": "0", "name": "Vehicles", "new_description": "Sites which advertise, promote, discuss and offer information on vehicles and automobiles", "newname": "Vehicles", "parent": "153", "test_url": "test.mysmoothwall.net/vehicles"}, "205": {"component": {"domainsurls": 10279, "regexpurls": 4}, "example_url": "www.akamai.com", "filename": "contentdelivery", "id": "205", "ignoreme": "0", "name": " Content Delivery", "new_description": "Content Delivery Networks and supplementary infrastructure servers for various existing sites", "newname": " Content Delivery", "parent": "1253", "test_url": "test.mysmoothwall.net/contentdelivery"}, "206": {"component": {"domainsurls": 651}, "example_url": "doubleclick.net", "filename": "trackingstats", "id": "206", "ignoreme": "0", "name": " Tracking", "new_description": "Sites known to track visitor statistics for analytics and reporting purposes", "newname": " Tracking", "parent": "1306", "test_url": "test.mysmoothwall.net/trackingstats"}, "207": {"component": {"domainsurls": 14192, "searchterms": 250, "videoids": 51}, "example_url": "", "filename": "terrorism", "id": "207", "ignoreme": "0", "name": "Terrorism", "new_description": "Sites provided by the Office of Security and Counter-Terrorism branch of the UK Home Office, containing pro-terrorism material", "newname": "Terrorism", "parent": "436", "test_url": "test.mysmoothwall.net/terrorism"}, "21": {"component": {"domainsurls": 12628, "weightedphrases": 87}, "example_url": "www.hsbc.com", "filename": "finance", "id": "21", "ignoreme": "0", "name": " Financial Services", "new_description": "Finance sites including investment advice, but not Online Banking portals", "newname": " Financial Services", "parent": "841", "test_url": "test.mysmoothwall.net/finance"}, "211": {"component": {"domainsurls": 3902, "weightedphrases": 2}, "example_url": "www.godaddy.com", "filename": "webhosting", "id": "211", "ignoreme": "0", "name": "Web hosting", "new_description": "Sites which offer domain names and web hosting services", "newname": "Web hosting", "parent": "241", "test_url": "test.mysmoothwall.net/webhosting"}, "212": {"component": {"domainsurls": 2012, "regexpurls": 1, "weightedphrases": 33}, "example_url": "www.weddingplanner.co.uk", "filename": "wedding", "id": "212", "ignoreme": "0", "name": "Weddings", "new_description": "Sites discussing weddings or selling wedding related items or services", "newname": "Weddings", "parent": "153", "test_url": "test.mysmoothwall.net/wedding"}, "213": {"component": {"domainsurls": 60572, "weightedphrases": 8}, "example_url": "www.abc.xyz", "filename": "businessgrar", "id": "213", "ignoreme": "0", "name": " Business", "new_description": "Sites for businesses and commercial organisations, where the organisation provides paid for goods or services.", "newname": " Business", "parent": "833", "test_url": "test.mysmoothwall.net/businessgrar"}, "214": {"component": {"domainsurls": 970, "regexpurls": 5, "searchterms": 321, "weightedphrases": 965}, "example_url": "www.sanctioned-suicide.org", "filename": "selfharm", "id": "214", "ignoreme": "0", "name": "Self Harm", "new_description": "Sites relating to self-harm, suicide and eating disorders", "newname": "Self Harm", "parent": "436", "test_url": "test.mysmoothwall.net/selfharm"}, "215": {"component": {"domainsurls": 177, "regexpurls": 3}, "example_url": "translate.google.com", "filename": "translation", "id": "215", "ignoreme": "0", "name": " Translation", "new_description": "Sites which provide translation services, such as blocks of text or providing entire site translation", "newname": " Translation", "parent": "878", "test_url": "test.mysmoothwall.net/translation"}, "218": {"component": {"domainsurls": 2271, "regexpurls": 1, "searchterms": 29, "weightedphrases": 213}, "example_url": "www.tattoos.com", "filename": "bodymod", "id": "218", "ignoreme": "0", "name": "Body Art", "new_description": "Sites which promote, sell, advertise or discuss any body modification such as piercing and tattoos.", "newname": "Body Art", "parent": "153", "test_url": "test.mysmoothwall.net/bodymod"}, "221": {"component": {}, "example_url": "", "filename": "comments", "id": "221", "ignoreme": "0", "name": "Comments", "new_description": "Content which allows community contributed comments.", "newname": "Comments", "parent": "257", "test_url": "test.mysmoothwall.net/comments"}, "222": {"component": {"jsregexpbody_cmod": 4, "regexpurls_cmod": 10}, "example_url": "", "filename": "youtube", "id": "222", "ignoreme": "0", "name": "YouTube Comment Removal", "new_description": "Removes comments from all YouTube videos.", "newname": "YouTube Comment Removal", "parent": "221", "test_url": "test.mysmoothwall.net/youtube"}, "223": {"component": {"domainsurls": 1407, "regexpurls": 5, "searchterms": 6, "weightedphrases": 109}, "example_url": "optionsforabortions.com", "filename": "abortion", "id": "223", "ignoreme": "0", "name": "Abortions", "new_description": "Sites which refer to abortion, including \"pro-life\" and \"pro-choice\".", "newname": "Abortions", "parent": "436", "test_url": "test.mysmoothwall.net/abortion"}, "224": {"component": {"domainsurls": 615, "searchterms": 3, "weightedphrases": 233}, "example_url": "www.sexeducationforum.org.uk", "filename": "contraception", "id": "224", "ignoreme": "0", "name": "Sex Education", "new_description": "Educational sites relating to sexual activity including reproduction, contraception and safe sex", "newname": "Sex Education", "parent": "396", "test_url": "test.mysmoothwall.net/contraception"}, "227": {"component": {"domainsurls": 870, "regexpurls": 1, "searchterms": 58, "weightedphrases": 236}, "example_url": "www.myfakeid.biz", "filename": "criminalactivity", "id": "227", "ignoreme": "0", "name": "Criminal Activity", "new_description": "Sites providing either instruction or tools facilitating illegal activity, such as lock picking, fake IDs and fraud.", "newname": "Criminal Activity", "parent": "436", "test_url": "test.mysmoothwall.net/criminalactivity"}, "228": {"component": {"domainsurls": 89, "regexpurls": 5}, "example_url": "", "filename": "suggest", "id": "228", "ignoreme": "0", "name": "Search Suggestions", "new_description": "Disables automatic search suggestions on Google, Bing, Ask and YouTube search engines", "newname": "Search Suggestions", "parent": "1217", "test_url": "test.mysmoothwall.net/suggest"}, "23": {"component": {"domainsurls": 1152, "searchterms": 15, "weightedphrases": 130}, "example_url": "www.ukessays.com", "filename": "cheating", "id": "23", "ignoreme": "0", "name": " Academic Dishonesty", "new_description": "Sites offering paid help and/or pre-written homework material for students", "newname": " Academic Dishonesty", "parent": "1246", "test_url": "test.mysmoothwall.net/cheating"}, "230": {"component": {"domainsurls": 159}, "example_url": "www.wikihow.com", "filename": "instructional", "id": "230", "ignoreme": "0", "name": " Knowledge Sharing", "new_description": "How To, instructional and Q&A sites", "newname": " Knowledge Sharing", "parent": "1212", "test_url": "test.mysmoothwall.net/instructional"}, "231": {"component": {"domainsurls": 696, "searchterms": 13, "weightedphrases": 21}, "example_url": "www.fireworks.us", "filename": "fireworks", "id": "231", "ignoreme": "0", "name": "Fireworks", "new_description": "Purchase and manufacture of fireworks & pyrotechnic devices", "newname": "Fireworks", "parent": "436", "test_url": "test.mysmoothwall.net/fireworks"}, "233": {"component": {"domainsurls": 2760}, "example_url": "www.mamasandpapas.com", "filename": "parenting", "id": "233", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON> and Baby", "new_description": "Sites providing information or products for parenting, pregnancy, babies and infants", "newname": "<PERSON><PERSON><PERSON> and Baby", "parent": "153", "test_url": "test.mysmoothwall.net/parenting"}, "235": {"component": {"domainsurls": 39347, "weightedphrases": 112}, "example_url": "www.snackworks.com", "filename": "fooddining", "id": "235", "ignoreme": "0", "name": "Food and Dining", "new_description": "Sites relating to food reviews, recipes, restaurants and catering", "newname": "Food and Dining", "parent": "1477", "test_url": "test.mysmoothwall.net/fooddining"}, "236": {"component": {"domainsurls": 623}, "example_url": "www.onlinebanking.natwest.com", "filename": "onlinebanking", "id": "236", "ignoreme": "0", "name": "Online Banking", "new_description": "Sites providing online banking access, or online credit card management", "newname": "Online Banking", "parent": "437", "test_url": "test.mysmoothwall.net/onlinebanking"}, "237": {"component": {"domainsurls": 57, "regexpurls": 4, "weightedphrases": 186}, "example_url": "sedoparking.com", "filename": "parked", "id": "237", "ignoreme": "0", "name": "Parked Domains", "new_description": "Inactive web domains \"parked\" by a registrar, advertiser or domain owner", "newname": "Parked Domains", "parent": "241", "test_url": "test.mysmoothwall.net/parked"}, "238": {"component": {"domainsurls": 21, "regexpurls": 8}, "example_url": "www.facebook.com", "filename": "facebook", "id": "238", "ignoreme": "0", "name": "Facebook", "new_description": "Domains and URLs for the Facebook network", "newname": "Facebook", "parent": "1354", "test_url": "test.mysmoothwall.net/facebook"}, "24": {"component": {"domainsurls": 13445, "regexpurls": 10, "searchterms": 61, "weightedphrases": 1384}, "example_url": "pirateproxy-bay.com", "filename": "warez", "id": "24", "ignoreme": "0", "name": " <PERSON><PERSON>", "new_description": "Sites containing pirated copyrighted material for illegal download or streaming", "newname": " <PERSON><PERSON>", "parent": "1242", "test_url": "test.mysmoothwall.net/warez"}, "241": {"component": {}, "example_url": "", "filename": "infrastructure", "id": "241", "ignoreme": "0", "name": "Web and Infrastructure", "new_description": "Web infrastructure and miscellaneous domains.", "newname": "Web and Infrastructure", "parent": "0", "test_url": "test.mysmoothwall.net/infrastructure"}, "242": {"component": {"domainsurls": 154476, "searchterms": 15, "weightedphrases": 171}, "example_url": "www.nationalrail.co.uk", "filename": "travel", "id": "242", "ignoreme": "0", "name": " Travel", "new_description": "Information or booking sites related to transportation such as trains, taxis, or airlines", "newname": " Travel", "parent": "1495", "test_url": "test.mysmoothwall.net/travel"}, "244": {"component": {"domainsurls": 231}, "example_url": "crl.verisign.com", "filename": "ssl-crl", "id": "244", "ignoreme": "0", "name": "SSL / CRL", "new_description": "Support domains for SSL services including Certificate Authorities, Certificate Revocation Lists, OCSP and Extended Validation servers", "newname": "SSL / CRL", "parent": "241", "test_url": "test.mysmoothwall.net/ssl-crl"}, "248": {"component": {"regexpurls_cmod": 1}, "example_url": "", "filename": "gisbasicmode", "id": "248", "ignoreme": "0", "name": "Google Image Search: Basic Mode", "new_description": "Forces Google image searches to display in \"Basic Mode\".", "newname": "Google Image Search: Basic Mode", "parent": "261", "test_url": "test.mysmoothwall.net/gisbasicmode"}, "251": {"component": {"domainsurls": 1, "regexpurls": 1}, "example_url": "", "filename": "googlepreview", "id": "251", "ignoreme": "0", "name": "Google Instant Previews", "new_description": "Thumbnail previews of Google web search results", "newname": "Google Instant Previews", "parent": "1217", "test_url": "test.mysmoothwall.net/googlepreview"}, "252": {"component": {}, "example_url": "", "filename": "guardian3-private", "id": "252", "ignoreme": "1", "name": "Internal", "new_description": "Internal lists used by Guardian 3 and never exposed in the product UI.", "newname": "Internal", "parent": "252", "test_url": "test.mysmoothwall.net/guardian3-private"}, "253": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-private-textual", "id": "253", "ignoreme": "1", "name": "Textual content", "new_description": "Guardian 3 \"Textual content\" category, i.e. \"stuff to phrase filter\".  Never exposed in UI.", "newname": "Textual content", "parent": "252", "test_url": "test.mysmoothwall.net/guardian3-private-textual"}, "254": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-private-unknown", "id": "254", "ignoreme": "1", "name": "Unknown content", "new_description": "Guardian 3 \"Unknown content type\" category - also stuff to phrase filter, just in case.", "newname": "Unknown content", "parent": "252", "test_url": "test.mysmoothwall.net/guardian3-private-unknown"}, "255": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-private-downloads", "id": "255", "ignoreme": "1", "name": "Guardian 3 Downloads", "new_description": "\"Downloads\" category, i.e. \"stuff to use the fancy download manager on\"", "newname": "Guardian 3 Downloads", "parent": "252", "test_url": "test.mysmoothwall.net/guardian3-private-downloads"}, "256": {"component": {}, "example_url": "", "filename": "filetypes", "id": "256", "ignoreme": "0", "name": "File Types", "new_description": "File and MIME types for file blocking.", "newname": "File Types", "parent": "241", "test_url": "test.mysmoothwall.net/filetypes"}, "257": {"component": {}, "example_url": "", "filename": "contentmods", "id": "257", "ignoreme": "0", "name": "Content Modifications", "new_description": "Rules to be used in content modification policies", "newname": "Content Modifications", "parent": "0", "test_url": "test.mysmoothwall.net/contentmods"}, "259": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-private-flash", "id": "259", "ignoreme": "1", "name": "Flash content", "new_description": "Guardian 3 \"Flash content\" category, i.e. \"stuff to flash filter\".  Never exposed in UI.", "newname": "Flash content", "parent": "252", "test_url": "test.mysmoothwall.net/guardian3-private-flash"}, "26": {"component": {"domainsurls": 680, "regexpurls": 2, "searchterms": 17, "weightedphrases": 87}, "example_url": "www.clothesfree.com", "filename": "naturism", "id": "26", "ignoreme": "0", "name": "Nudity", "new_description": "Sites which contain nudity or promote a nudist lifestyle, which are not pornographic in nature", "newname": "Nudity", "parent": "436", "test_url": "test.mysmoothwall.net/naturism"}, "261": {"component": {}, "example_url": "", "filename": "searchcmod", "id": "261", "ignoreme": "0", "name": "Search", "new_description": "Rules for controlling search engine features.", "newname": "Search", "parent": "257", "test_url": "test.mysmoothwall.net/searchcmod"}, "262": {"component": {"domainsurls": 33, "regexpurls": 7, "weightedphrases": 3}, "example_url": "www.youtube.com", "filename": "youtube-av", "id": "262", "ignoreme": "0", "name": "YouTube", "new_description": "Domains and URLs for YouTube", "newname": "YouTube", "parent": "152", "test_url": "test.mysmoothwall.net/youtube-av"}, "263": {"component": {"domainsurls": 12}, "example_url": "www.bbc.co.uk/iplayer", "filename": "iplayer-av", "id": "263", "ignoreme": "0", "name": "BBC iPlayer", "new_description": "Domains andURLs for the BBC iPlayer service", "newname": "BBC iPlayer", "parent": "152", "test_url": "test.mysmoothwall.net/iplayer-av"}, "27": {"component": {"domainsurls": 7628, "weightedphrases": 95}, "example_url": "www.crocus.co.uk", "filename": "gardening", "id": "27", "ignoreme": "0", "name": "Home and Garden", "new_description": "Sites dedicated to household DIY projects and gardening as a hobby, including sites which sell or review power-tools or gardening products", "newname": "Home and Garden", "parent": "153", "test_url": "test.mysmoothwall.net/gardening"}, "28": {"component": {"domainsurls": 4818, "regexpurls": 6, "weightedphrases": 29}, "example_url": "www.facebook.com", "filename": "socialnetworking", "id": "28", "ignoreme": "0", "name": " Social Networking", "new_description": "Social Networking sites", "newname": " Social Networking", "parent": "1354", "test_url": "test.mysmoothwall.net/socialnetworking"}, "3": {"component": {"domainsurls": 37596, "searchterms": 7, "weightedphrases": 164}, "example_url": "www.petsathome.com", "filename": "pets", "id": "3", "ignoreme": "0", "name": "Pets", "new_description": "Sites selling or discussing pets or pet related goods", "newname": "Pets", "parent": "153", "test_url": "test.mysmoothwall.net/pets"}, "30": {"component": {"domainsurls": 2015, "regexpurls": 1, "searchterms": 1, "weightedphrases": 574}, "example_url": "www.kitploit.com", "filename": "hacking", "id": "30", "ignoreme": "0", "name": "Hacking", "new_description": "Sites with the main theme of hacking/cracking, regardless of \"white-hat\" or \"black-hat\"", "newname": "Hacking", "parent": "155", "test_url": "test.mysmoothwall.net/hacking"}, "34": {"component": {"domainsurls": 164001, "regexpurls": 2, "searchterms": 4, "weightedphrases": 247}, "example_url": "www.sainsburys.co.uk", "filename": "ecommerce", "id": "34", "ignoreme": "0", "name": " Shopping", "new_description": "Sites where purchases can be made, of any kind", "newname": " Shopping", "parent": "1479", "test_url": "test.mysmoothwall.net/ecommerce"}, "35": {"component": {"domainsurls": 5068, "regexpurls": 4, "searchterms": 90, "weightedphrases": 1121}, "example_url": "www.stormfront.org", "filename": "intolerance", "id": "35", "ignoreme": "0", "name": "Intolerance", "new_description": "Sites related to or promoting intolerance of any kind", "newname": "Intolerance", "parent": "436", "test_url": "test.mysmoothwall.net/intolerance"}, "37": {"component": {"domainsurls": 3564, "regexpurls": 3}, "example_url": "www.monster.com", "filename": "jobsearch", "id": "37", "ignoreme": "0", "name": "Job Search", "new_description": "Sites for job search", "newname": "Job Search", "parent": "153", "test_url": "test.mysmoothwall.net/jobsearch"}, "38": {"component": {"domainsurls": 1025, "regexpurls": 1, "weightedphrases": 3}, "example_url": "www.weather.org", "filename": "weather", "id": "38", "ignoreme": "0", "name": "Weather", "new_description": "Sites dedicated to weather information and forecasts", "newname": "Weather", "parent": "396", "test_url": "test.mysmoothwall.net/weather"}, "39": {"component": {"domainsurls": 393906, "regexpurls": 38, "searchterms": 3792, "weightedphrases": 15430}, "example_url": "www.pornhub.com", "filename": "porn", "id": "39", "ignoreme": "0", "name": "Pornography", "new_description": "Sites dedicated to Pornographic content, whether audio/visual or textual", "newname": "Pornography", "parent": "436", "test_url": "test.mysmoothwall.net/porn"}, "392": {"component": {"jsregexpbody_cmod": 1}, "example_url": "", "filename": "ytsm", "id": "392", "ignoreme": "0", "name": "YouTube SafetyMode cookie", "new_description": "Automatically enable YouTube's Safety Mode. Apply ONLY to youtube.com", "newname": "YouTube SafetyMode cookie", "parent": "261", "test_url": "test.mysmoothwall.net/ytsm"}, "393": {"component": {"domainsurls": 5, "regexpurls": 2}, "example_url": "www.tineye.com", "filename": "reverseimg", "id": "393", "ignoreme": "0", "name": "Reverse Image Search", "new_description": "Reverse image search engines which offer query-by-image and image similarity functions", "newname": "Reverse Image Search", "parent": "1217", "test_url": "test.mysmoothwall.net/reverseimg"}, "394": {"component": {"domainsurls": 76}, "example_url": "www.apple.com", "filename": "apple", "id": "394", "ignoreme": "0", "name": "Apple", "new_description": "Apple domains and URLs", "newname": "Apple", "parent": "489", "test_url": "test.mysmoothwall.net/apple"}, "396": {"component": {}, "example_url": "", "filename": "eduref", "id": "396", "ignoreme": "0", "name": "Information and Reference", "new_description": "Informational sites and reference materials", "newname": "Information and Reference", "parent": "0", "test_url": "test.mysmoothwall.net/eduref"}, "397": {"component": {"domainsurls": 20834, "regexpurls": 3, "weightedphrases": 23}, "example_url": "www.harvard.edu", "filename": "academic", "id": "397", "ignoreme": "0", "name": "Academic Institutions", "new_description": "Sites of schools, universities, colleges and other learning centres", "newname": "Academic Institutions", "parent": "396", "test_url": "test.mysmoothwall.net/academic"}, "398": {"component": {"domainsurls": 168, "regexpurls": 1}, "example_url": "", "filename": "facebook-readonly", "id": "398", "ignoreme": "0", "name": "Facebook: Posts & Updates", "new_description": "Block this category to deny Facebook changes such as posts, comments, likes, uploading of photos, videos or notes.  Requires HTTPS interception", "newname": "Facebook: Posts & Updates", "parent": "1354", "test_url": "test.mysmoothwall.net/facebook-readonly"}, "403": {"component": {"domainsurls": 1, "regexpurls": 2}, "example_url": "https://en.wikipedia.org/w/index.php?title=Special:User<PERSON>ogin", "filename": "wikipedia-readonly", "id": "403", "ignoreme": "0", "name": "Wikipedia: Editing", "new_description": "Block this category to deny Wikipedia changes and Wikipedia logins.", "newname": "Wikipedia: Editing", "parent": "1206", "test_url": "test.mysmoothwall.net/wikipedia-readonly"}, "409": {"component": {"domainsurls": 1, "regexpurls": 2}, "example_url": "", "filename": "hdstreaming", "id": "409", "ignoreme": "0", "name": "YouTube HD streaming", "new_description": "Domains and URLs for high definition streams from YouTube", "newname": "YouTube HD streaming", "parent": "152", "test_url": "test.mysmoothwall.net/hdstreaming"}, "41": {"component": {"domainsurls": 3035, "weightedphrases": 202}, "example_url": "www.lovepanky.com", "filename": "sexuality", "id": "41", "ignoreme": "0", "name": "Sexuality", "new_description": "Content based primarily on sexuality which may be unsuitable for a younger audience.", "newname": "Sexuality", "parent": "436", "test_url": "test.mysmoothwall.net/sexuality"}, "411": {"component": {"domainsurls": 25, "regexpurls": 1}, "example_url": "www.netflix.com", "filename": "netflix", "id": "411", "ignoreme": "0", "name": "Netflix", "new_description": "Domains and URLs for Netflix", "newname": "Netflix", "parent": "152", "test_url": "test.mysmoothwall.net/netflix"}, "413": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-private-s<PERSON><PERSON><PERSON>", "id": "413", "ignoreme": "1", "name": "SSL Login Autodiscovery", "new_description": "", "newname": "SSL Login Autodiscovery", "parent": "252", "test_url": "test.mysmoothwall.net/guardian3-private-ssllogin"}, "415": {"component": {"domainsurls": 186, "weightedphrases": 14}, "example_url": "www.teamviewer.com", "filename": "remotedesktop", "id": "415", "ignoreme": "0", "name": " Remote Access Tools", "new_description": "Sites offering remote desktop and remote administration software tools", "newname": " Remote Access Tools", "parent": "1312", "test_url": "test.mysmoothwall.net/remotedesktop"}, "416": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-connect-<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "416", "ignoreme": "0", "name": "SafeSearch via CONNECT header", "new_description": "Content Modification rule to enforce SafeSearch on Google, Bing & DuckDuckGo via a CONNECT header rewrite", "newname": "SafeSearch via CONNECT header", "parent": "261", "test_url": "test.mysmoothwall.net/guardian3-connect-forcesafesearch"}, "418": {"component": {"domainsurls": 45, "regexpurls": 2}, "example_url": "", "filename": "twitreadon<PERSON>", "id": "418", "ignoreme": "0", "name": "Twitter: Updates", "new_description": "Block this category to deny Twitter changes such as tweets, retweets, follows, posting of photos, videos or account changes.  Requires HTTPS interception", "newname": "Twitter: Updates", "parent": "1354", "test_url": "test.mysmoothwall.net/twitreadonly"}, "419": {"component": {"domainsurls": 824, "regexpurls": 4, "searchterms": 23}, "example_url": "www.cashasap.co.uk", "filename": "paydayloans", "id": "419", "ignoreme": "0", "name": "Payday Loans", "new_description": "Online providers of \"Payday Loans\"; short term cash loans typically with high rates of interest", "newname": "Payday Loans", "parent": "437", "test_url": "test.mysmoothwall.net/paydayloans"}, "420": {"component": {"domainsurls": 36, "regexpurls": 36}, "example_url": "", "filename": "sni-fails", "id": "420", "ignoreme": "0", "name": "Transparent HTTPS incompatible sites", "new_description": "Sites used by clients which do not support HTTPS server name indication (SNI)", "newname": "Transparent HTTPS incompatible sites", "parent": "241", "test_url": "test.mysmoothwall.net/sni-fails"}, "422": {"component": {"domainsurls": 2}, "example_url": "", "filename": "guardian3-private-alwaysblocked", "id": "422", "ignoreme": "1", "name": "Always blocked", "new_description": "", "newname": "Always blocked", "parent": "252", "test_url": "test.mysmoothwall.net/guardian3-private-alwaysblocked"}, "43": {"component": {"domainsurls": 140488, "weightedphrases": 230}, "example_url": "www.newchristian.com", "filename": "religion", "id": "43", "ignoreme": "0", "name": "Religion", "new_description": "Sites where the main theme/content is religion", "newname": "Religion", "parent": "153", "test_url": "test.mysmoothwall.net/religion"}, "433": {"component": {"domainsurls": 3452, "regexpurls": 1}, "example_url": "www.speedtest.net", "filename": "speedtest", "id": "433", "ignoreme": "0", "name": "Internet Speed Tests", "new_description": "Public web services used to measure the speed of an internet connection", "newname": "Internet Speed Tests", "parent": "241", "test_url": "test.mysmoothwall.net/speedtest"}, "435": {"component": {"domainsurls": 6977}, "example_url": "www.slashdot.org", "filename": "computing", "id": "435", "ignoreme": "0", "name": " Computing and Electronics", "new_description": "Sites related to computing but not games. e.g programming languages", "newname": " Computing and Electronics", "parent": "1204", "test_url": "test.mysmoothwall.net/computing"}, "436": {"component": {}, "example_url": "", "filename": "nbc", "id": "436", "ignoreme": "0", "name": " Mature and Explicit", "new_description": "Sites which could cause legal or liability issues", "newname": " Mature and Explicit", "parent": "0", "test_url": "test.mysmoothwall.net/nbc"}, "437": {"component": {}, "example_url": "", "filename": "businesscorporategrar", "id": "437", "ignoreme": "0", "name": "Business and Government", "new_description": "Businesses and organisations, including government and non-profits.", "newname": "Business and Government", "parent": "0", "test_url": "test.mysmoothwall.net/businesscorporategrar"}, "438": {"component": {"domainsurls": 58324, "weightedphrases": 69}, "example_url": "www.christianaid.org.uk", "filename": "charity", "id": "438", "ignoreme": "0", "name": " Charity and Non-profit", "new_description": "Charity and non-profit organisations", "newname": " Charity and Non-profit", "parent": "839", "test_url": "test.mysmoothwall.net/charity"}, "44": {"component": {"domainsurls": 2958, "regexpurls": 2, "weightedphrases": 58}, "example_url": "mail.google.com", "filename": "webmail", "id": "44", "ignoreme": "0", "name": "Email", "new_description": "Sites dedicated to email/webmail", "newname": "Email", "parent": "241", "test_url": "test.mysmoothwall.net/webmail"}, "440": {"component": {"jsregexpbody_cmod": 1}, "example_url": "", "filename": "bbciplayer-adultcontentremoval", "id": "440", "ignoreme": "0", "name": "BBC iPlayer - Enforce Parental Guidance Lock", "new_description": "Enforces BBC iPlayer's parental lock", "newname": "BBC iPlayer - Enforce Parental Guidance Lock", "parent": "257", "test_url": "test.mysmoothwall.net/bbciplayer-adultcontentremoval"}, "441": {"component": {"jsregexpbody_cmod": 2, "regexpurls_cmod": 1}, "example_url": "", "filename": "facebookcommentremoval", "id": "441", "ignoreme": "0", "name": "Facebook comments plugin removal", "new_description": "Remove Facebook comments from websites.", "newname": "Facebook comments plugin removal", "parent": "221", "test_url": "test.mysmoothwall.net/facebookcommentremoval"}, "442": {"component": {"jsregexpbody_cmod": 1, "regexpurls_cmod": 1}, "example_url": "", "filename": "disquscommentremoval", "id": "442", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON><PERSON> Comment <PERSON>", "new_description": "Remove Disqus comments from websites", "newname": "<PERSON><PERSON><PERSON><PERSON> Comment <PERSON>", "parent": "221", "test_url": "test.mysmoothwall.net/disquscommentremoval"}, "444": {"component": {"domainsurls": 112, "regexpurls": 32}, "example_url": "www.office.com", "filename": "office365", "id": "444", "ignoreme": "0", "name": "Microsoft Office 365", "new_description": "Domains required for Microsoft Office 365 to function.Warning: Includes live.com, msn.com and outlook.com", "newname": "Microsoft Office 365", "parent": "489", "test_url": "test.mysmoothwall.net/office365"}, "457": {"component": {"jsregexpbody_cmod": 1}, "example_url": "", "filename": "bloggercommentremoval", "id": "457", "ignoreme": "0", "name": "Blogger Comment <PERSON>", "new_description": "Remove comments from Blogspot / Blogger websites", "newname": "Blogger Comment <PERSON>", "parent": "221", "test_url": "test.mysmoothwall.net/bloggercommentremoval"}, "458": {"component": {"jsregexpbody_cmod": 1}, "example_url": "", "filename": "wordpresscommentremoval", "id": "458", "ignoreme": "0", "name": "WordPress Comment Removal", "new_description": "Remove comments from WordPress websites", "newname": "WordPress Comment Removal", "parent": "221", "test_url": "test.mysmoothwall.net/wordpresscommentremoval"}, "471": {"component": {"domainsurls": 48, "regexpurls": 4}, "example_url": "googleusercontent.com", "filename": "connectforchromebooks", "id": "471", "ignoreme": "0", "name": "Connect for Chromebooks", "new_description": "Domains and URLs used for Google's service Connect for Chromebooks", "newname": "Connect for Chromebooks", "parent": "241", "test_url": "test.mysmoothwall.net/connectforchromebooks"}, "474": {"component": {"domainsurls": 1646, "regexpurls": 1, "searchterms": 21, "weightedphrases": 214}, "example_url": "www.tokyopop.com", "filename": "comics", "id": "474", "ignoreme": "0", "name": "Cartoons and Manga", "new_description": "Graphic novels, Manga, Anime, and other illustrated works, excluding Hentai as this is Pornography", "newname": "Cartoons and Manga", "parent": "160", "test_url": "test.mysmoothwall.net/comics"}, "476": {"component": {"jsregexpbody_cmod": 2, "regexpurls_cmod": 1}, "example_url": "", "filename": "youtube-disableautoplay", "id": "476", "ignoreme": "0", "name": "YouTube - Disable Auto Play", "new_description": "Disable YouTube's auto play feature and remove related videos.", "newname": "YouTube - Disable Auto Play", "parent": "257", "test_url": "test.mysmoothwall.net/youtube-disableautoplay"}, "477": {"component": {"domainsurls": 1}, "example_url": "", "filename": "remove-quic-header", "id": "477", "ignoreme": "0", "name": "Remove QUIC Header", "new_description": "Remove alternate-protocol response header", "newname": "Remove QUIC Header", "parent": "257", "test_url": "test.mysmoothwall.net/remove-quic-header"}, "482": {"component": {"domainsurls": 2952, "weightedphrases": 146}, "example_url": "puregym.com", "filename": "health", "id": "482", "ignoreme": "0", "name": " Health and Fitness", "new_description": "Sites dedicated to health and fitness, gyms, fitness blogs, health sites", "newname": " Health and Fitness", "parent": "1348", "test_url": "test.mysmoothwall.net/health"}, "486": {"component": {"domainsurls": 1}, "example_url": "https://blockman.smoothwall.com/blocklists/category/486/change/", "filename": "guardian3-connect-restrictyoutube", "id": "486", "ignoreme": "0", "name": "YouTube: Restricted Mode (Strict) via CONNECT Header", "new_description": "Apply restricted mode to youtube", "newname": "YouTube: Restricted Mode (Strict) via CONNECT Header", "parent": "257", "test_url": "test.mysmoothwall.net/guardian3-connect-restrictyoutube"}, "488": {"component": {"domainsurls": 61, "regexpurls": 8}, "example_url": "www.amazon.com/Prime-Video", "filename": "amazonprime", "id": "488", "ignoreme": "0", "name": "Amazon Prime", "new_description": "Amazon Prime instant video and Amazon Prime music", "newname": "Amazon Prime", "parent": "152", "test_url": "test.mysmoothwall.net/amazonprime"}, "489": {"component": {}, "example_url": "", "filename": "software", "id": "489", "ignoreme": "0", "name": "Software", "new_description": "Domains and URLs used by common software packages.", "newname": "Software", "parent": "241", "test_url": "test.mysmoothwall.net/software"}, "490": {"component": {"domainsurls": 218}, "example_url": "", "filename": "software-centrastage", "id": "490", "ignoreme": "0", "name": "AEM Web Portal", "new_description": "IP and URLs that are used by AEM (Autotask Endpoint Management)", "newname": "AEM Web Portal", "parent": "489", "test_url": "test.mysmoothwall.net/software-centrastage"}, "497": {"component": {"domainsurls": 3, "regexpurls": 1}, "example_url": "www.hudl.com", "filename": "software-hudl", "id": "497", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "Domains/URLs used by the sports APP Hudl", "newname": "<PERSON><PERSON>", "parent": "764", "test_url": "test.mysmoothwall.net/software-hudl"}, "498": {"component": {"domainsurls": 321}, "example_url": "www.playstation.com", "filename": "gamesconsoles", "id": "498", "ignoreme": "0", "name": " Game Stores and Publishers", "new_description": "Sites that provide authentication for, and content used in, games consoles", "newname": " Game Stores and Publishers", "parent": "1437", "test_url": "test.mysmoothwall.net/gamesconsoles"}, "50": {"component": {"domainsurls": 1272, "regexpurls": 2, "weightedphrases": 9}, "example_url": "www.google.com", "filename": "searchengines", "id": "50", "ignoreme": "0", "name": " Search Engines", "new_description": "Sites providing web search functionality", "newname": " Search Engines", "parent": "1217", "test_url": "test.mysmoothwall.net/searchengines"}, "501": {"component": {"domainsurls": 137, "regexpurls": 21}, "example_url": "www.skype.com", "filename": "software-skype", "id": "501", "ignoreme": "0", "name": "Skype", "new_description": "Domains and IPs that are used by Skype", "newname": "Skype", "parent": "1407", "test_url": "test.mysmoothwall.net/software-skype"}, "511": {"component": {"domainsurls": 1273, "searchterms": 5, "weightedphrases": 68}, "example_url": "masseyferguson.com", "filename": "agriculture", "id": "511", "ignoreme": "0", "name": "Agriculture", "new_description": "Sites related to agriculture - Machinery, livestock, crops", "newname": "Agriculture", "parent": "437", "test_url": "test.mysmoothwall.net/agriculture"}, "512": {"component": {"domainsurls": 2766, "regexpurls": 1, "weightedphrases": 1443}, "example_url": "www.bitfinex.com", "filename": "cryptocurrency", "id": "512", "ignoreme": "0", "name": "Cryptocurrency", "new_description": "Cryptocurrency websites", "newname": "Cryptocurrency", "parent": "437", "test_url": "test.mysmoothwall.net/cryptocurrency"}, "513": {"component": {"domainsurls": 2662, "weightedphrases": 67}, "example_url": "www.lawsociety.org.uk", "filename": "law", "id": "513", "ignoreme": "0", "name": "Legal", "new_description": "Lawyer/Solicitor/Barrister websites, law societies and sites whose whole theme is Law", "newname": "Legal", "parent": "437", "test_url": "test.mysmoothwall.net/law"}, "514": {"component": {"domainsurls": 1}, "example_url": "", "filename": "rangedgetservices", "id": "514", "ignoreme": "1", "name": "Trusted Ranged Get Services", "new_description": "Trusted services which make ranged get requests", "newname": "Trusted Ranged Get Services", "parent": "252", "test_url": "test.mysmoothwall.net/rangedgetservices"}, "516": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-connect-restrictyoutube-moderate", "id": "516", "ignoreme": "0", "name": "YouTube: Restricted Mode (Moderate) via CONNECT Header", "new_description": "Apply restricted mode moderate to Youtube", "newname": "YouTube: Restricted Mode (Moderate) via CONNECT Header", "parent": "257", "test_url": "test.mysmoothwall.net/guardian3-connect-restrictyoutube-moderate"}, "517": {"component": {"regexpurls": 1}, "example_url": "www.xn--strmsel-c1a.se", "filename": "punycode", "id": "517", "ignoreme": "0", "name": "Internationalised Domain Names", "new_description": "Domains that include non-ASCII characters which are translated to punycode", "newname": "Internationalised Domain Names", "parent": "155", "test_url": "test.mysmoothwall.net/punycode"}, "518": {"component": {"domainsurls": 59, "regexpurls": 8}, "example_url": "www.livestream.com", "filename": "livestreaming", "id": "518", "ignoreme": "0", "name": "Live Streaming", "new_description": "Domains and URLs that stream live video", "newname": "Live Streaming", "parent": "160", "test_url": "test.mysmoothwall.net/livestreaming"}, "52": {"component": {"domainsurls": 823, "regexpurls": 1, "weightedphrases": 60}, "example_url": "www.1337x.to", "filename": "p2p", "id": "52", "ignoreme": "0", "name": "Peer-to-Peer", "new_description": "Peer-to-peer network sites", "newname": "Peer-to-Peer", "parent": "155", "test_url": "test.mysmoothwall.net/p2p"}, "520": {"component": {"regexpurls_cmod": 4}, "example_url": "", "filename": "googlelicense", "id": "520", "ignoreme": "0", "name": "Enforce Google Image Licensing", "new_description": "Force Google to only return images that are licensed for use under the Creative Commons license", "newname": "Enforce Google Image Licensing", "parent": "261", "test_url": "test.mysmoothwall.net/googlelicense"}, "522": {"component": {"domainsurls": 51}, "example_url": "www.gotomypc.com", "filename": "gotosoftware", "id": "522", "ignoreme": "0", "name": "GoTo Software Suite", "new_description": "Contains URLs for GoTo products (GoToMeeting, GoToWebinar, GoToTraining, GoToAssist, GoToMyPC, and OpenVoice)", "newname": "GoTo Software Suite", "parent": "489", "test_url": "test.mysmoothwall.net/gotosoftware"}, "524": {"component": {"jsregexpbody_cmod": 2}, "example_url": "", "filename": "google_translate", "id": "524", "ignoreme": "0", "name": "Remove Translation From Google and Bing Search Results", "new_description": "Removing Translate from search results", "newname": "Remove Translation From Google and Bing Search Results", "parent": "261", "test_url": "test.mysmoothwall.net/google_translate"}, "525": {"component": {"jsregexpbody_cmod": 2, "regexpurls_cmod": 5}, "example_url": "", "filename": "google_games", "id": "525", "ignoreme": "0", "name": "Block Google Doodle Games", "new_description": "Remove games from Google search results e.g. Snake, <PERSON>ic tac <PERSON>", "newname": "Block Google Doodle Games", "parent": "261", "test_url": "test.mysmoothwall.net/google_games"}, "526": {"component": {"jsregexpbody_cmod": 2}, "example_url": "", "filename": "google_adverts", "id": "526", "ignoreme": "0", "name": "Remove Adverts from Google", "new_description": "Remove adverts from Google so users can't click through on them.", "newname": "Remove Adverts from Google", "parent": "261", "test_url": "test.mysmoothwall.net/google_adverts"}, "528": {"component": {"domainsurls": 3, "searchterms": 1662}, "example_url": "https://swearing.testfiltering.com/", "filename": "vulgarlanguage", "id": "528", "ignoreme": "0", "name": "Inappropriate/Vulgar Search Terms", "new_description": "Contains lists of Inappropriate/Vulgar/Swear words, only search terms, no content or URLs", "newname": "Inappropriate/Vulgar Search Terms", "parent": "436", "test_url": "test.mysmoothwall.net/vulgarlanguage"}, "53": {"component": {"domainsurls": 157013, "regexpurls": 2, "searchterms": 108, "weightedphrases": 330}, "example_url": "www.eurosport.co.uk", "filename": "sport", "id": "53", "ignoreme": "0", "name": " Sports", "new_description": "Sites discussing or promoting any sport, including sports team homepages", "newname": " Sports", "parent": "764", "test_url": "test.mysmoothwall.net/sport"}, "54": {"component": {"domainsurls": 120, "regexpurls": 4}, "example_url": "images.google.co.uk", "filename": "imagesearch", "id": "54", "ignoreme": "0", "name": "Image Search", "new_description": "URLs used for image search", "newname": "Image Search", "parent": "396", "test_url": "test.mysmoothwall.net/imagesearch"}, "549": {"component": {"domainsurls": 1218, "weightedphrases": 12}, "example_url": "labour.org.uk", "filename": "politics", "id": "549", "ignoreme": "0", "name": "Politics", "new_description": "Sites belonging to a Political party or wholly political based sites", "newname": "Politics", "parent": "396", "test_url": "test.mysmoothwall.net/politics"}, "55": {"component": {"domainsurls": 26252, "weightedphrases": 71}, "example_url": "www.bbc.co.uk/news", "filename": "news", "id": "55", "ignoreme": "0", "name": " News", "new_description": "Sites whose primary aim is to present local, national, or international news, or news relating to a specific topic.", "newname": " News", "parent": "849", "test_url": "test.mysmoothwall.net/news"}, "550": {"component": {"domainsurls": 179, "regexpurls": 4, "searchterms": 36}, "example_url": "", "filename": "safeguardingexclu", "id": "550", "ignoreme": "1", "name": "Safeguarding Exclusions", "new_description": "Domains/URLs that need to be excluded from the Safeguarding reports", "newname": "Safeguarding Exclusions", "parent": "252", "test_url": "test.mysmoothwall.net/safeguardingexclu"}, "552": {"component": {}, "example_url": "", "filename": "monitoring", "id": "552", "ignoreme": "1", "name": "Safeguarding", "new_description": "Parent category for monitoring/reporting categories", "newname": "Safeguarding", "parent": "252", "test_url": "test.mysmoothwall.net/monitoring"}, "553": {"component": {"domainsurls": 29, "regexpurls": 2, "searchterms": 414, "weightedphrases": 3}, "example_url": "", "filename": "suicidecaution", "id": "553", "ignoreme": "1", "name": "Suicide - <PERSON><PERSON><PERSON>", "new_description": "A reporting only category for Suicide", "newname": "Suicide - <PERSON><PERSON><PERSON>", "parent": "252", "test_url": "test.mysmoothwall.net/suicidecaution"}, "56": {"component": {"domainsurls": 9263, "regexpurls": 13, "searchterms": 2, "weightedphrases": 86}, "example_url": "www.vimeo.com", "filename": "audio-video", "id": "56", "ignoreme": "0", "name": " Streaming Media", "new_description": "Sites providing audio or video downloads or streaming", "newname": " Streaming Media", "parent": "152", "test_url": "test.mysmoothwall.net/audio-video"}, "565": {"component": {"domainsurls": 1, "regexpurls": 31}, "example_url": "www.zoom.us", "filename": "zoomus", "id": "565", "ignoreme": "0", "name": "Zoom", "new_description": "List of URLs & IPs provided by Zoom's KB article", "newname": "Zoom", "parent": "1407", "test_url": "test.mysmoothwall.net/zoomus"}, "566": {"component": {"domainsurls": 7}, "example_url": "instagram.com", "filename": "instagram", "id": "566", "ignoreme": "0", "name": "Instagram", "new_description": "URLs used by Instagram", "newname": "Instagram", "parent": "1354", "test_url": "test.mysmoothwall.net/instagram"}, "568": {"component": {"domainsurls": 10}, "example_url": "www.facebook.com", "filename": "facebookapp", "id": "568", "ignoreme": "0", "name": "Facebook App", "new_description": "URLs used by the Facebook Mobile App", "newname": "Facebook App", "parent": "1354", "test_url": "test.mysmoothwall.net/facebookapp"}, "569": {"component": {"domainsurls": 29, "regexpurls": 1}, "example_url": "www.twitter.com", "filename": "twitterapp", "id": "569", "ignoreme": "0", "name": "Twitter", "new_description": "URLs used by the Twitter Mobile App", "newname": "Twitter", "parent": "1354", "test_url": "test.mysmoothwall.net/twitterapp"}, "57": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-filetype-archives", "id": "57", "ignoreme": "0", "name": "Archive Filetypes", "new_description": "Compressed archives, e.g. ZIP files and tarballs.", "newname": "Archive Filetypes", "parent": "256", "test_url": "test.mysmoothwall.net/guardian3-filetype-archives"}, "570": {"component": {"domainsurls": 11}, "example_url": "www.snapchat.com", "filename": "snapchatapp", "id": "570", "ignoreme": "0", "name": "Snapchat", "new_description": "URLs used by the Snapchat Mobile App", "newname": "Snapchat", "parent": "1407", "test_url": "test.mysmoothwall.net/snapchatapp"}, "572": {"component": {"domainsurls": 11, "regexpurls": 1}, "example_url": "www.classdojo.com", "filename": "classdojoapp", "id": "572", "ignoreme": "0", "name": "ClassDojo", "new_description": "URLs used by the ClassDojo App", "newname": "ClassDojo", "parent": "882", "test_url": "test.mysmoothwall.net/classdojoapp"}, "579": {"component": {"jsregexpbody_cmod": 2}, "example_url": "", "filename": "youtube-results-google", "id": "579", "ignoreme": "0", "name": "Remove YouTube videos from Google Search", "new_description": "This content modification will remove the sections of the page that serve YouTube videos", "newname": "Remove YouTube videos from Google Search", "parent": "261", "test_url": "test.mysmoothwall.net/youtube-results-google"}, "58": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-filetype-audio", "id": "58", "ignoreme": "0", "name": "Audio Filetypes", "new_description": "Audio files, e.g. .wav, .au, and .mp3", "newname": "Audio Filetypes", "parent": "256", "test_url": "test.mysmoothwall.net/guardian3-filetype-audio"}, "582": {"component": {"searchterms": 9}, "example_url": "", "filename": "radicalisationcaution", "id": "582", "ignoreme": "1", "name": "Radicalisation - Caution", "new_description": "A reporting only category for Radicalisation", "newname": "Radicalisation - Caution", "parent": "252", "test_url": "test.mysmoothwall.net/radicalisationcaution"}, "583": {"component": {"searchterms": 6}, "example_url": "", "filename": "abusedanger", "id": "583", "ignoreme": "1", "name": "Abuse - Danger", "new_description": "A reporting only category for Abuse", "newname": "Abuse - Danger", "parent": "252", "test_url": "test.mysmoothwall.net/abusedanger"}, "584": {"component": {"domainsurls": 9, "searchterms": 172, "videoids": 9, "weightedphrases": 1}, "example_url": "", "filename": "substanceabusemonitoring", "id": "584", "ignoreme": "1", "name": "Substance abuse - Caut<PERSON>", "new_description": "A reporting only category for Substance abuse", "newname": "Substance abuse - Caut<PERSON>", "parent": "252", "test_url": "test.mysmoothwall.net/substanceabusemonitoring"}, "585": {"component": {"domainsurls": 2, "searchterms": 1, "weightedphrases": 5}, "example_url": "", "filename": "<PERSON><PERSON><PERSON>", "id": "585", "ignoreme": "1", "name": "Bullying - Danger", "new_description": "A reporting only category for Bullying", "newname": "Bullying - Danger", "parent": "252", "test_url": "test.mysmoothwall.net/bullyingdanger"}, "586": {"component": {"domainsurls": 29}, "example_url": "", "filename": "adultcontentdanger", "id": "586", "ignoreme": "1", "name": "Adult Content - Danger", "new_description": "A reporting only category for Adult Content", "newname": "Adult Content - Danger", "parent": "252", "test_url": "test.mysmoothwall.net/adultcontentdanger"}, "587": {"component": {"domainsurls": 7, "regexpurls": 1, "searchterms": 577, "weightedphrases": 26}, "example_url": "", "filename": "<PERSON><PERSON><PERSON>", "id": "587", "ignoreme": "1", "name": "Suicide - Danger", "new_description": "A reporting only category for Suicide", "newname": "Suicide - Danger", "parent": "252", "test_url": "test.mysmoothwall.net/suicidedanger"}, "588": {"component": {"domainsurls": 116, "searchterms": 89}, "example_url": "", "filename": "suicideadvisory", "id": "588", "ignoreme": "1", "name": "Suicide - Advisory", "new_description": "A reporting only category for Suicide", "newname": "Suicide - Advisory", "parent": "252", "test_url": "test.mysmoothwall.net/suicideadvisory"}, "589": {"component": {"domainsurls": 2, "searchterms": 4}, "example_url": "", "filename": "radicalisationdanger", "id": "589", "ignoreme": "1", "name": "Radicalisation - Danger", "new_description": "A reporting only category for Radicalisation", "newname": "Radicalisation - Danger", "parent": "252", "test_url": "test.mysmoothwall.net/radicalisationdanger"}, "59": {"component": {"domainsurls": 2}, "example_url": "", "filename": "guardian3-filetype-executables", "id": "59", "ignoreme": "0", "name": "Executable Files", "new_description": "Executable files, would include .exe, .dll, etc.", "newname": "Executable Files", "parent": "256", "test_url": "test.mysmoothwall.net/guardian3-filetype-executables"}, "590": {"component": {"searchterms": 113, "weightedphrases": 1}, "example_url": "", "filename": "radicalisationadvisory", "id": "590", "ignoreme": "1", "name": "Radicalisation - Advisory", "new_description": "A reporting only category for Radicalisation", "newname": "Radicalisation - Advisory", "parent": "252", "test_url": "test.mysmoothwall.net/radicalisationadvisory"}, "591": {"component": {"domainsurls": 3, "searchterms": 4}, "example_url": "", "filename": "abusecaution", "id": "591", "ignoreme": "1", "name": "Abuse - Caution", "new_description": "A reporting only category for Abuse", "newname": "Abuse - Caution", "parent": "252", "test_url": "test.mysmoothwall.net/abusecaution"}, "592": {"component": {"domainsurls": 6, "searchterms": 8}, "example_url": "", "filename": "abuseadvisory", "id": "592", "ignoreme": "1", "name": "Abuse - Advisory", "new_description": "A reporting only category for Abuse", "newname": "Abuse - Advisory", "parent": "252", "test_url": "test.mysmoothwall.net/abuseadvisory"}, "593": {"component": {"searchterms": 337}, "example_url": "", "filename": "substanceabusedanger", "id": "593", "ignoreme": "1", "name": "Substance abuse - Danger", "new_description": "A reporting only category for Substance abuse", "newname": "Substance abuse - Danger", "parent": "252", "test_url": "test.mysmoothwall.net/substanceabusedanger"}, "594": {"component": {"domainsurls": 7, "searchterms": 59, "weightedphrases": 2}, "example_url": "", "filename": "substanceabuseadvisory", "id": "594", "ignoreme": "1", "name": "Substance abuse - Advisory", "new_description": "A reporting only category for Substance abuse", "newname": "Substance abuse - Advisory", "parent": "252", "test_url": "test.mysmoothwall.net/substanceabuseadvisory"}, "595": {"component": {"domainsurls": 1, "searchterms": 17}, "example_url": "", "filename": "bullyingcaution", "id": "595", "ignoreme": "1", "name": "Bullying - Caution", "new_description": "A reporting only category for Bullying", "newname": "Bullying - Caution", "parent": "252", "test_url": "test.mysmoothwall.net/bullyingcaution"}, "596": {"component": {"domainsurls": 15, "searchterms": 12, "weightedphrases": 9}, "example_url": "", "filename": "bullyingadvisory", "id": "596", "ignoreme": "1", "name": "Bullying - Advisory", "new_description": "A reporting only category for Bullying", "newname": "Bullying - Advisory", "parent": "252", "test_url": "test.mysmoothwall.net/bullyingadvisory"}, "597": {"component": {"searchterms": 9}, "example_url": "", "filename": "criminalactivitydanger", "id": "597", "ignoreme": "1", "name": "Criminal activity - Danger", "new_description": "A reporting only category for Criminal activity", "newname": "Criminal activity - Danger", "parent": "252", "test_url": "test.mysmoothwall.net/criminalactivitydanger"}, "598": {"component": {"searchterms": 11}, "example_url": "", "filename": "criminalactivitycaution", "id": "598", "ignoreme": "1", "name": "Criminal activity - Caution", "new_description": "A reporting only category for Criminal activity", "newname": "Criminal activity - Caution", "parent": "252", "test_url": "test.mysmoothwall.net/criminalactivitycaution"}, "599": {"component": {"searchterms": 6}, "example_url": "", "filename": "criminalactivityadvisory", "id": "599", "ignoreme": "1", "name": "Criminal activity - Advisory", "new_description": "A reporting only category for Criminal activity", "newname": "Criminal activity - Advisory", "parent": "252", "test_url": "test.mysmoothwall.net/criminalactivityadvisory"}, "6": {"component": {"domainsurls": 1453, "regexpurls": 1, "searchterms": 35, "weightedphrases": 691}, "example_url": "www.hockeyfights.com", "filename": "violence", "id": "6", "ignoreme": "0", "name": "Violence", "new_description": "Sites dedicated to violence, whether this is through sport or bullying", "newname": "Violence", "parent": "436", "test_url": "test.mysmoothwall.net/violence"}, "60": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-filetype-macros", "id": "60", "ignoreme": "0", "name": "Document Macros", "new_description": "Files with executable macros, includes .doc, .xls, .mdb, etc.  Even seemingly harmless files such as Microsoft Office files contain executable code that can infect your workstations.", "newname": "Document Macros", "parent": "256", "test_url": "test.mysmoothwall.net/guardian3-filetype-macros"}, "600": {"component": {}, "example_url": "", "filename": "adultcontentcaution", "id": "600", "ignoreme": "1", "name": "Adult Content - Caution", "new_description": "A reporting only category for Adult Content", "newname": "Adult Content - Caution", "parent": "252", "test_url": "test.mysmoothwall.net/adultcontentcaution"}, "601": {"component": {"searchterms": 8}, "example_url": "", "filename": "adultcontentadvisory", "id": "601", "ignoreme": "1", "name": "Adult Content - Advisory", "new_description": "A reporting only category for Adult Content", "newname": "Adult Content - Advisory", "parent": "252", "test_url": "test.mysmoothwall.net/adultcontentadvisory"}, "603": {"component": {"domainsurls": 115, "regexpurls": 7}, "example_url": "", "filename": "smoothwall-products", "id": "603", "ignoreme": "0", "name": "Smoothwall Products", "new_description": "This category contains the Domains that the Smoothwall Suite uses.", "newname": "Smoothwall Products", "parent": "489", "test_url": "test.mysmoothwall.net/smoothwall-products"}, "606": {"component": {"domainsurls": 406}, "example_url": "dns.google", "filename": "dnsoverhttps", "id": "606", "ignoreme": "0", "name": "DNS over HTTPS", "new_description": "Subdomains/URLs used to facilitate the use of DNS over HTTPS", "newname": "DNS over HTTPS", "parent": "241", "test_url": "test.mysmoothwall.net/dnsoverhttps"}, "607": {"component": {"domainsurls": 7, "regexpurls": 1}, "example_url": "www.itv.com/hub/itv", "filename": "itvplayer", "id": "607", "ignoreme": "0", "name": "ITV Player", "new_description": "Domains and URLs for the ITV Player service", "newname": "ITV Player", "parent": "152", "test_url": "test.mysmoothwall.net/itvplayer"}, "608": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-connect-snapchat", "id": "608", "ignoreme": "0", "name": "Snapchat URL Rewrite", "new_description": "Modify the CONNECT header of URLs used by Snapchat to allow them through Guardian", "newname": "Snapchat URL Rewrite", "parent": "257", "test_url": "test.mysmoothwall.net/guardian3-connect-snapchat"}, "61": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-filetype-safecontent", "id": "61", "ignoreme": "0", "name": "Safe Content Filetypes", "new_description": "Content which does not typically require virus scanning, or which cannot be scanned correctly (e.g. streaming video)", "newname": "Safe Content Filetypes", "parent": "256", "test_url": "test.mysmoothwall.net/guardian3-filetype-safecontent"}, "610": {"component": {"domainsurls": 10}, "example_url": "disneyplus.com", "filename": "disneyplus", "id": "610", "ignoreme": "0", "name": "Disney+", "new_description": "Sites used by Disney's streaming service Disney+ (DisneyPlus)", "newname": "Disney+", "parent": "152", "test_url": "test.mysmoothwall.net/disneyplus"}, "612": {"component": {"domainsurls": 11, "regexpurls": 2}, "example_url": "tiktok.com", "filename": "tiktok", "id": "612", "ignoreme": "0", "name": "TikTok", "new_description": "Domains and URLs used by the TikTok Android and iOS mobile application", "newname": "TikTok", "parent": "1354", "test_url": "test.mysmoothwall.net/tiktok"}, "613": {"component": {"domainsurls": 6, "regexpurls": 1}, "example_url": "spotify.com", "filename": "spotify", "id": "613", "ignoreme": "0", "name": "Spotify", "new_description": "Domains and URLs used by the Spotify client and Web app", "newname": "Spotify", "parent": "771", "test_url": "test.mysmoothwall.net/spotify"}, "614": {"component": {"domainsurls": 20, "regexpurls": 248}, "example_url": "whatsapp.com", "filename": "whatsapp", "id": "614", "ignoreme": "0", "name": "WhatsApp Messenger", "new_description": "Used to allow or block the WhatsApp mobile application", "newname": "WhatsApp Messenger", "parent": "1407", "test_url": "test.mysmoothwall.net/whatsapp"}, "615": {"component": {"jsregexpbody_cmod": 1}, "example_url": "", "filename": "vimeo-mature-filter", "id": "615", "ignoreme": "0", "name": "Vimeo: Force Mature Content Filter", "new_description": "Force the 'Mature Content Filter' when browsing Vimeo. This prevents videos marked as mature from being played, or shown in Vimeo's search results.", "newname": "Vimeo: Force Mature Content Filter", "parent": "261", "test_url": "test.mysmoothwall.net/vimeo-mature-filter"}, "616": {"component": {"domainsurls": 3}, "example_url": "vimeo.com", "filename": "vimeo", "id": "616", "ignoreme": "0", "name": "Vimeo", "new_description": "URLs used by Vimeo, to be used with Force Mature content filter content modification", "newname": "Vimeo", "parent": "152", "test_url": "test.mysmoothwall.net/vimeo"}, "617": {"component": {"domainsurls": 6, "regexpurls": 4}, "example_url": "meet.google.com", "filename": "goog<PERSON><PERSON><PERSON>", "id": "617", "ignoreme": "0", "name": "Google Meet", "new_description": "Domains and URLs used by Google Meet", "newname": "Google Meet", "parent": "1407", "test_url": "test.mysmoothwall.net/googlemeet"}, "62": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-filetype-vectorgraphics", "id": "62", "ignoreme": "0", "name": "Vector Graphics Filetypes", "new_description": "Vector graphics files would include .dpx, .dxf, .emf, .fh, .fla, .wmf etc.", "newname": "Vector Graphics Filetypes", "parent": "256", "test_url": "test.mysmoothwall.net/guardian3-filetype-vectorgraphics"}, "621": {"component": {"domainsurls": 6}, "example_url": "loom.com", "filename": "loom", "id": "621", "ignoreme": "0", "name": "Loom", "new_description": "URLs necessary for Loom Screen Recording Software to work", "newname": "Loom", "parent": "489", "test_url": "test.mysmoothwall.net/loom"}, "622": {"component": {"domainsurls": 1}, "example_url": "protect.scot", "filename": "covid-apps", "id": "622", "ignoreme": "0", "name": "Covid-19 Exposure Notification Apps", "new_description": "Mobile applications developed by public health authorities which use the Covid-19 Exposure Notification System developed by Google and Apple", "newname": "Covid-19 Exposure Notification Apps", "parent": "241", "test_url": "test.mysmoothwall.net/covid-apps"}, "626": {"component": {"domainsurls": 167, "regexpurls": 1, "searchterms": 4, "weightedphrases": 44}, "example_url": "www.myip.com", "filename": "whats-my-ip", "id": "626", "ignoreme": "0", "name": "What's my IP services", "new_description": "Sites and services which display your public IP address", "newname": "What's my IP services", "parent": "155", "test_url": "test.mysmoothwall.net/whats-my-ip"}, "627": {"component": {"domainsurls": 16}, "example_url": "lsmdm.com", "filename": "lightspeed-mdm", "id": "627", "ignoreme": "0", "name": "Lightspeed MDM", "new_description": "Domains and URLs used by Lightspeed's Mobile Device Management software", "newname": "Lightspeed MDM", "parent": "489", "test_url": "test.mysmoothwall.net/lightspeed-mdm"}, "628": {"component": {"domainsurls": 12, "regexpurls": 1}, "example_url": "", "filename": "youtube-allowed-videos", "id": "628", "ignoreme": "0", "name": "YouTube Allowed Videos and Playlists", "new_description": "Allowed YouTube video and playlist IDs", "newname": "YouTube Allowed Videos and Playlists", "parent": "152", "test_url": "test.mysmoothwall.net/youtube-allowed-videos"}, "629": {"component": {"domainsurls": 52}, "example_url": "", "filename": "adobeccendpoints", "id": "629", "ignoreme": "0", "name": "Adobe Creative Cloud", "new_description": "URLs/IPs necessary for Adobe Creative Cloud to function properly", "newname": "Adobe Creative Cloud", "parent": "489", "test_url": "test.mysmoothwall.net/adobeccendpoints"}, "63": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-filetype-video", "id": "63", "ignoreme": "0", "name": "Video Filetypes", "new_description": "Video files, includes .avi, .mpg, .mov, etc.", "newname": "Video Filetypes", "parent": "256", "test_url": "test.mysmoothwall.net/guardian3-filetype-video"}, "630": {"component": {"jsregexpbody_cmod": 3}, "example_url": "", "filename": "google-web-basic", "id": "630", "ignoreme": "0", "name": "Google Web Search: Basic Mode", "new_description": "", "newname": "Google Web Search: Basic Mode", "parent": "261", "test_url": "test.mysmoothwall.net/google-web-basic"}, "631": {"component": {"domainsurls": 1}, "example_url": "", "filename": "postwebsearch", "id": "631", "ignoreme": "1", "name": "Web Search POST", "new_description": "Search engines that use the body of POST requests to contain search terms", "newname": "Web Search POST", "parent": "252", "test_url": "test.mysmoothwall.net/postwebsearch"}, "632": {"component": {"domainsurls": 21, "regexpurls": 1}, "example_url": "drive.google.com", "filename": "gdrive", "id": "632", "ignoreme": "0", "name": "Google Docs and Drive", "new_description": "URLs used by Google Drive/Docs/Sheets/Slides", "newname": "Google Docs and Drive", "parent": "1324", "test_url": "test.mysmoothwall.net/gdrive"}, "635": {"component": {"jsregexpbody_cmod": 1}, "example_url": "", "filename": "youtubeadremove", "id": "635", "ignoreme": "0", "name": "YouTube: Remove Adverts", "new_description": "A content mod category target at YouTube category to remove adverts", "newname": "YouTube: Remove Adverts", "parent": "257", "test_url": "test.mysmoothwall.net/youtubeadremove"}, "637": {"component": {"domainsurls": 1}, "example_url": "", "filename": "youtube-restricted-strict", "id": "637", "ignoreme": "0", "name": "YouTube: Restricted Mode (Strict) via HTTP Header", "new_description": "Apply Restricted Mode (Strict) to YouTube by inserting an HTTP Header", "newname": "YouTube: Restricted Mode (Strict) via HTTP Header", "parent": "257", "test_url": "test.mysmoothwall.net/youtube-restricted-strict"}, "638": {"component": {"domainsurls": 1}, "example_url": "", "filename": "youtube-restricted-moderate", "id": "638", "ignoreme": "0", "name": "YouTube: Restricted Mode (Moderate) via HTTP Header", "new_description": "Apply Restricted Mode (Moderate) to YouTube by inserting an HTTP Header", "newname": "YouTube: Restricted Mode (Moderate) via HTTP Header", "parent": "257", "test_url": "test.mysmoothwall.net/youtube-restricted-moderate"}, "639": {"component": {"regexpurls": 1}, "example_url": "", "filename": "google-search", "id": "639", "ignoreme": "0", "name": "Google Search", "new_description": "URLs used by the Google search engine", "newname": "Google Search", "parent": "1217", "test_url": "test.mysmoothwall.net/google-search"}, "64": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-filetype-wasting", "id": "64", "ignoreme": "0", "name": "Bandwidth Wasting Filetypes", "new_description": "Time and bandwidth wasting, includes .iso, etc.", "newname": "Bandwidth Wasting Filetypes", "parent": "256", "test_url": "test.mysmoothwall.net/guardian3-filetype-wasting"}, "640": {"component": {"domainsurls": 7}, "example_url": "senso.cloud", "filename": "software-senso", "id": "640", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "Doamins used by the Senso platform", "newname": "<PERSON><PERSON>", "parent": "489", "test_url": "test.mysmoothwall.net/software-senso"}, "641": {"component": {"domainsurls": 1}, "example_url": "tumblr.com", "filename": "adultexclude", "id": "641", "ignoreme": "1", "name": "Adult Content Exclusions", "new_description": "URLs to be Excluded from Adult Content safeguarding theme", "newname": "Adult Content Exclusions", "parent": "252", "test_url": "test.mysmoothwall.net/adultexclude"}, "643": {"component": {"domainsurls": 1}, "example_url": "https://emile-education.com", "filename": "emile-education-app", "id": "643", "ignoreme": "0", "name": "Emile Education", "new_description": "", "newname": "Emile Education", "parent": "882", "test_url": "test.mysmoothwall.net/emile-education-app"}, "644": {"component": {"domainsurls": 16, "videoids": 253}, "example_url": "https://www.youhq.co.uk", "filename": "youhq", "id": "644", "ignoreme": "0", "name": "youHQ", "new_description": "Domains, URLs, and YouTube videos used by the youHQ school wellbeing platform.", "newname": "youHQ", "parent": "489", "test_url": "test.mysmoothwall.net/youhq"}, "65": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-filetype-webcontent", "id": "65", "ignoreme": "0", "name": "Web Content", "new_description": "Standard Web content (HTML, CSS, images) for unblocking when blocking all other downloads", "newname": "Web Content", "parent": "256", "test_url": "test.mysmoothwall.net/guardian3-filetype-webcontent"}, "654": {"component": {"domainsurls": 6942, "searchterms": 5}, "example_url": "openai.com", "filename": "aitools", "id": "654", "ignoreme": "0", "name": " AI Tools", "new_description": "General AI (AGI) tools are a set of software and technology that aims to simulate or replicate human-like intelligence across a wide range of tasks. These tools are designed to be flexible, adaptable and able to learn and improve on their own. They can be used for autonomous vehicles, chatbots, and virtual assistants. Examples include machine learning frameworks, NLP libraries and deep learning architectures.", "newname": " AI Tools", "parent": "846", "test_url": "test.mysmoothwall.net/aitools"}, "655": {"component": {"domainsurls": 11}, "example_url": "zoho.com/assist", "filename": "software-zoho-assist", "id": "655", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "Domains and URLs used by Z<PERSON>o Assist.", "newname": "<PERSON><PERSON><PERSON>", "parent": "489", "test_url": "test.mysmoothwall.net/software-zoho-assist"}, "656": {"component": {"domainsurls": 1}, "example_url": "www.wechat.com", "filename": "wechat", "id": "656", "ignoreme": "0", "name": "WeChat", "new_description": "WeChat is a Chinese multi-purpose messaging, social media and mobile payment app developed by Tencent.", "newname": "WeChat", "parent": "1407", "test_url": "test.mysmoothwall.net/wechat"}, "66": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-filetype-inpageexe", "id": "66", "ignoreme": "0", "name": "In-page Executables", "new_description": "In-page executables, such as Java applets", "newname": "In-page Executables", "parent": "256", "test_url": "test.mysmoothwall.net/guardian3-filetype-inpageexe"}, "67": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-filetype-octet", "id": "67", "ignoreme": "0", "name": "Octet Streams", "new_description": "Octet files, includes any other binary file such as Word documents.  These would not normally be files designed for web pages.", "newname": "Octet Streams", "parent": "256", "test_url": "test.mysmoothwall.net/guardian3-filetype-octet"}, "68": {"component": {"domainsurls": 1}, "example_url": "", "filename": "guardian3-filetype-messenger", "id": "68", "ignoreme": "0", "name": "Instant Messaging MIME Types", "new_description": "MSN and Yahoo! Messenger messages passed over HTTP", "newname": "Instant Messaging MIME Types", "parent": "256", "test_url": "test.mysmoothwall.net/guardian3-filetype-messenger"}, "69": {"component": {"jsregexpbody_cmod": 1, "regexpurls_cmod": 35}, "example_url": "", "filename": "forcesafesearch", "id": "69", "ignoreme": "0", "name": "Force SafeSearch", "new_description": "Manipulates the URL to automatically switch on safe searching in Google, Yahoo, Bing, Lycos, Hotbot, Flickr, Startpagina and Metacrawler.", "newname": "Force SafeSearch", "parent": "261", "test_url": "test.mysmoothwall.net/forcesafesearch"}, "7": {"component": {"domainsurls": 149083, "regexpurls": 3, "searchterms": 31, "weightedphrases": 146}, "example_url": "www.nhs.uk", "filename": "medical", "id": "7", "ignoreme": "0", "name": "Medical Information", "new_description": "Sites containing content about health and medical matters", "newname": "Medical Information", "parent": "437", "test_url": "test.mysmoothwall.net/medical"}, "747": {"component": {"domainsurls": 15943, "searchterms": 5, "weightedphrases": 1415}, "example_url": "leafly.com", "filename": "marijuana", "id": "747", "ignoreme": "0", "name": "Marijuana", "new_description": "Information, tools, resources, and discussion regarding the use or cultivation of marijuana.", "newname": "Marijuana", "parent": "436", "test_url": "test.mysmoothwall.net/marijuana"}, "750": {"component": {"domainsurls": 58, "searchterms": 4}, "example_url": "thesaurus.com", "filename": "dictionaries-and-encyclopedias", "id": "750", "ignoreme": "0", "name": " Dictionaries and Encyclopedias", "new_description": "Reference material and Resources regarding general topics.", "newname": " Dictionaries and Encyclopedias", "parent": "1206", "test_url": "test.mysmoothwall.net/dictionaries-and-encyclopedias"}, "751": {"component": {"domainsurls": 32525, "weightedphrases": 8}, "example_url": "broadwayworld.com", "filename": "appindex-entertainment", "id": "751", "ignoreme": "0", "name": "Entertainment", "new_description": "Sites which cover popular culture such as celebrity news and gossip", "newname": "Entertainment", "parent": "160", "test_url": "test.mysmoothwall.net/appindex-entertainment"}, "752": {"component": {"domainsurls": 25920}, "example_url": "climatecooling.org", "filename": "environment", "id": "752", "ignoreme": "0", "name": " Environment", "new_description": "Information and studies related to living organisms and their natural habitat.", "newname": " Environment", "parent": "1222", "test_url": "test.mysmoothwall.net/environment"}, "753": {"component": {"domainsurls": 88282, "weightedphrases": 43}, "example_url": "lovecrafts.com", "filename": "recreation-and-hobbies", "id": "753", "ignoreme": "0", "name": "Recreation and Hobbies", "new_description": "Outdoor activities, Arts and Crafts.", "newname": "Recreation and Hobbies", "parent": "396", "test_url": "test.mysmoothwall.net/recreation-and-hobbies"}, "754": {"component": {"domainsurls": 4041}, "example_url": "qq.com", "filename": "portals", "id": "754", "ignoreme": "0", "name": " Portals", "new_description": "Website integrating diverse sources, emails, online forums, and search engines.", "newname": " Portals", "parent": "1309", "test_url": "test.mysmoothwall.net/portals"}, "755": {"component": {"domainsurls": 132, "searchterms": 1, "weightedphrases": 24}, "example_url": "mathgames.com", "filename": "educational-games", "id": "755", "ignoreme": "0", "name": " Educational Games", "new_description": "Online games intended for learning and self improvement.", "newname": " Educational Games", "parent": "1429", "test_url": "test.mysmoothwall.net/educational-games"}, "756": {"component": {"domainsurls": 30306}, "example_url": "", "filename": "adverts-hidden", "id": "756", "ignoreme": "1", "name": "Adverts", "new_description": "Carbon copy of 'Advertising' category, set to be hidden. Used to ensure safeguarding alerts continue to work in cloud filter.", "newname": "Adverts", "parent": "252", "test_url": "test.mysmoothwall.net/adverts-hidden"}, "757": {"component": {"domainsurls": 651}, "example_url": "", "filename": "trackingstats-hidden", "id": "757", "ignoreme": "1", "name": "User tracking and Site stats", "new_description": "Carbon copy of 'Tracking' category, set to be hidden. Used to ensure safeguarding alerts continue to work in cloud filter.", "newname": "User tracking and Site stats", "parent": "252", "test_url": "test.mysmoothwall.net/trackingstats-hidden"}, "758": {"component": {"domainsurls": 16488}, "example_url": "", "filename": "education-hidden", "id": "758", "ignoreme": "1", "name": "Education and Reference", "new_description": "Carbon copy of 'Education' category, set to be hidden. Used to ensure safeguarding alerts continue to work in cloud filter.", "newname": "Education and Reference", "parent": "252", "test_url": "test.mysmoothwall.net/education-hidden"}, "759": {"component": {"domainsurls": 8947}, "example_url": "", "filename": "personalweapons-hidden", "id": "759", "ignoreme": "1", "name": "Personal Weapons", "new_description": "Carbon copy of 'Weapons' category, set to be hidden. Used to ensure safeguarding alerts continue to work in cloud filter.", "newname": "Personal Weapons", "parent": "252", "test_url": "test.mysmoothwall.net/personalweapons-hidden"}, "760": {"component": {"domainsurls": 3035}, "example_url": "", "filename": "sexuality-hidden", "id": "760", "ignoreme": "1", "name": "Sexuality Sites", "new_description": "Carbon copy of 'Sexuality' category, set to be hidden. Used to ensure safeguarding alerts continue to work in cloud filter.", "newname": "Sexuality Sites", "parent": "252", "test_url": "test.mysmoothwall.net/sexuality-hidden"}, "761": {"component": {"domainsurls": 5074}, "example_url": "", "filename": "dating-hidden", "id": "761", "ignoreme": "1", "name": "Dating and Companionship Sites", "new_description": "Carbon copy of 'Dating' category, set to be hidden. Used to ensure safeguarding alerts continue to work in cloud filter.", "newname": "Dating and Companionship Sites", "parent": "252", "test_url": "test.mysmoothwall.net/dating-hidden"}, "762": {"component": {"domainsurls": 24556}, "example_url": "", "filename": "onlinegames-hidden", "id": "762", "ignoreme": "1", "name": "Online Games", "new_description": "Carbon copy of 'Video Games' category, set to be hidden. Used to ensure safeguarding alerts continue to work in cloud filter.", "newname": "Online Games", "parent": "252", "test_url": "test.mysmoothwall.net/onlinegames-hidden"}, "763": {"component": {}, "example_url": "", "filename": "entertainment-child", "id": "763", "ignoreme": "1", "name": " Arts and Entertainment", "new_description": "Entertainment, sport and online games", "newname": " Arts and Entertainment", "parent": "252", "test_url": "test.mysmoothwall.net/entertainment-child"}, "764": {"component": {}, "example_url": "www.eurosport.co.uk", "filename": "sport-parent", "id": "764", "ignoreme": "0", "name": "Sports", "new_description": "Sites discussing or promoting any sport, including sports team homepages", "newname": "Sports", "parent": "160", "test_url": "test.mysmoothwall.net/sport-parent"}, "765": {"component": {"domainsurls": 13}, "example_url": "espn.com", "filename": "sphirewall-application-espn", "id": "765", "ignoreme": "0", "name": "ESPN", "new_description": "Visit ESPN to get up-to-the-minute sports news coverage, scores, highlights and commentary for NFL, MLB, NBA, College Football, NCAA...", "newname": "ESPN", "parent": "764", "test_url": "test.mysmoothwall.net/sphirewall-application-espn"}, "766": {"component": {"domainsurls": 4}, "example_url": "strava.com", "filename": "sphirewall-application-strava", "id": "766", "ignoreme": "0", "name": "Strava", "new_description": "", "newname": "Strava", "parent": "764", "test_url": "test.mysmoothwall.net/sphirewall-application-strava"}, "767": {"component": {"domainsurls": 1}, "example_url": "sportingnews.com", "filename": "sphirewall-application-sportingnews", "id": "767", "ignoreme": "0", "name": "sportingnews.com", "new_description": "The latest sports news, video, analysis, scores and bettor info. Covering the NFL, MLB, NBA, NHL, NASCAR, college football and basketball.", "newname": "sportingnews.com", "parent": "764", "test_url": "test.mysmoothwall.net/sphirewall-application-sportingnews"}, "768": {"component": {"domainsurls": 5}, "example_url": "cbssports.com", "filename": "sphirewall-application-cbssports", "id": "768", "ignoreme": "0", "name": "CBS Sports", "new_description": "CBSSports.com features live scoring and news for NFL football, MLB baseball, NBA basketball, NHL hockey, college basketball and football...", "newname": "CBS Sports", "parent": "764", "test_url": "test.mysmoothwall.net/sphirewall-application-cbssports"}, "769": {"component": {"domainsurls": 1}, "example_url": "foxsports.com", "filename": "sphirewall-application-foxsports", "id": "769", "ignoreme": "0", "name": "FoxSports", "new_description": "Find live scores, player & team news, videos, rumors, stats, standings, schedules & fantasy games on FOX Sports.", "newname": "FoxSports", "parent": "764", "test_url": "test.mysmoothwall.net/sphirewall-application-foxsports"}, "770": {"component": {"domainsurls": 1}, "example_url": "skysports.com", "filename": "sphirewall-application-skysports", "id": "770", "ignoreme": "0", "name": "SkySports", "new_description": "Watch the best live coverage of your favourite sports: Football, Golf, Rugby, Cricket, Tennis, F1, Boxing, plus the latest sports news, transfers...", "newname": "SkySports", "parent": "764", "test_url": "test.mysmoothwall.net/sphirewall-application-skysports"}, "771": {"component": {}, "example_url": "www.lyrics.com", "filename": "music-parent", "id": "771", "ignoreme": "0", "name": "Music and Audio", "new_description": "Sites that discuss, promote/market and distribute music.  Including fan sites, lyrics sites, playlist sites, artist and musical subjects sites", "newname": "Music and Audio", "parent": "160", "test_url": "test.mysmoothwall.net/music-parent"}, "772": {"component": {"domainsurls": 14}, "example_url": "audible.com", "filename": "sphirewall-application-audible", "id": "772", "ignoreme": "0", "name": "Audible", "new_description": "Audible is a seller and producer of spoken audio entertainment, information, and educational programming on the Internet. Audible sells digital audiobooks, radio and TV programs, and audio versions of magazines and newspapers.", "newname": "Audible", "parent": "771", "test_url": "test.mysmoothwall.net/sphirewall-application-audible"}, "773": {"component": {"domainsurls": 7}, "example_url": "audiomack.com", "filename": "sphirewall-application-audiomack", "id": "773", "ignoreme": "0", "name": "audiomack", "new_description": "", "newname": "audiomack", "parent": "771", "test_url": "test.mysmoothwall.net/sphirewall-application-audiomack"}, "774": {"component": {"domainsurls": 3}, "example_url": "groovesharks.org", "filename": "sphirewall-application-grooveshark", "id": "774", "ignoreme": "0", "name": "GrooveShark", "new_description": "", "newname": "GrooveShark", "parent": "771", "test_url": "test.mysmoothwall.net/sphirewall-application-grooveshark"}, "775": {"component": {"domainsurls": 6}, "example_url": "iheart.com", "filename": "sphirewall-application-iheart", "id": "775", "ignoreme": "0", "name": "iHeart", "new_description": "", "newname": "iHeart", "parent": "771", "test_url": "test.mysmoothwall.net/sphirewall-application-iheart"}, "776": {"component": {"domainsurls": 18}, "example_url": "itunes.com", "filename": "sphirewall-application-itunes", "id": "776", "ignoreme": "0", "name": "iTunes/App Store", "new_description": "iTunes is a media player, media library, Internet radio broadcaster, mobile device management utility, and the client app for iTunes Store, developed by Apple Inc.", "newname": "iTunes/App Store", "parent": "771", "test_url": "test.mysmoothwall.net/sphirewall-application-itunes"}, "777": {"component": {"domainsurls": 29}, "example_url": "last.fm", "filename": "sphirewall-application-lastfm", "id": "777", "ignoreme": "0", "name": "last.fm", "new_description": "The world’s largest online music catalogue, powered by your scrobbles. Free listening, videos, photos, stats, charts, biographies and concerts.", "newname": "last.fm", "parent": "771", "test_url": "test.mysmoothwall.net/sphirewall-application-lastfm"}, "778": {"component": {"domainsurls": 1}, "example_url": "lyricstraining.com", "filename": "sphirewall-application-lyricstraining", "id": "778", "ignoreme": "0", "name": "Lyrics Training", "new_description": "", "newname": "Lyrics Training", "parent": "771", "test_url": "test.mysmoothwall.net/sphirewall-application-lyricstraining"}, "779": {"component": {"domainsurls": 3}, "example_url": "www.noteflight.com", "filename": "sphirewall-application-noteflight", "id": "779", "ignoreme": "0", "name": "Noteflight", "new_description": "Online Music Notation Software", "newname": "Noteflight", "parent": "771", "test_url": "test.mysmoothwall.net/sphirewall-application-noteflight"}, "780": {"component": {"domainsurls": 3}, "example_url": "pandora.com", "filename": "sphirewall-application-pandora", "id": "780", "ignoreme": "0", "name": "Pandora", "new_description": "Pandora is free, personalized radio that plays music you'll love. Discover new music and enjoy old favorites.", "newname": "Pandora", "parent": "771", "test_url": "test.mysmoothwall.net/sphirewall-application-pandora"}, "781": {"component": {"domainsurls": 1}, "example_url": "podcastle.ai", "filename": "sphirewall-application-podcastle", "id": "781", "ignoreme": "0", "name": "Podcastle", "new_description": "Make your\nvoice heard\nJoin thousands of creators who use Podcastle to bring their stories to life. ", "newname": "Podcastle", "parent": "771", "test_url": "test.mysmoothwall.net/sphirewall-application-podcastle"}, "782": {"component": {"domainsurls": 5}, "example_url": "soundcloud.com", "filename": "sphirewall-application-soundcloud", "id": "782", "ignoreme": "0", "name": "SoundCloud", "new_description": "Explore the largest community of artists, bands, podcasters and creators of music & audio.", "newname": "SoundCloud", "parent": "771", "test_url": "test.mysmoothwall.net/sphirewall-application-soundcloud"}, "783": {"component": {"domainsurls": 1}, "example_url": "music.youtube.com", "filename": "sphirewall-application-youtubemusic", "id": "783", "ignoreme": "0", "name": "YouTube Music", "new_description": "", "newname": "YouTube Music", "parent": "771", "test_url": "test.mysmoothwall.net/sphirewall-application-youtubemusic"}, "784": {"component": {}, "example_url": "www.imdb.com", "filename": "moviesfilm-parent", "id": "784", "ignoreme": "0", "name": "Movies and Film", "new_description": "Movie sites, reviews and discussion", "newname": "Movies and Film", "parent": "160", "test_url": "test.mysmoothwall.net/moviesfilm-parent"}, "785": {"component": {"domainsurls": 1}, "example_url": "box-office.com", "filename": "sphirewall-application-box-office", "id": "785", "ignoreme": "0", "name": "box-office.com", "new_description": "", "newname": "box-office.com", "parent": "784", "test_url": "test.mysmoothwall.net/sphirewall-application-box-office"}, "786": {"component": {"domainsurls": 5}, "example_url": "imdb.com", "filename": "sphirewall-application-imdb", "id": "786", "ignoreme": "0", "name": "IMDb", "new_description": "IMDb, the world's most popular and authoritative source for movie, TV and celebrity content.", "newname": "IMDb", "parent": "784", "test_url": "test.mysmoothwall.net/sphirewall-application-imdb"}, "787": {"component": {"domainsurls": 1}, "example_url": "rogerebert.com", "filename": "sphirewall-application-rog<PERSON><PERSON>", "id": "787", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "Full and capsule film reviews from the acclaimed critic. Also includes film festival details, interviews, and essays.", "newname": "<PERSON><PERSON><PERSON>", "parent": "784", "test_url": "test.mysmoothwall.net/sphirewall-application-rogerebert"}, "788": {"component": {"domainsurls": 1}, "example_url": "rottentomatoes.com", "filename": "sphirewall-application-rottentomatoes", "id": "788", "ignoreme": "0", "name": "Rottentomatoes", "new_description": "", "newname": "Rottentomatoes", "parent": "784", "test_url": "test.mysmoothwall.net/sphirewall-application-rottentomatoes"}, "789": {"component": {"domainsurls": 13}, "example_url": "9now.com.au", "filename": "sphirewall-application-9now", "id": "789", "ignoreme": "0", "name": "9Now", "new_description": "9Now is a video on demand, catch-up TV service.", "newname": "9Now", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-9now"}, "790": {"component": {"domainsurls": 14}, "example_url": "iview.abc.net.au", "filename": "sphirewall-application-abciveiw", "id": "790", "ignoreme": "0", "name": "ABC iview", "new_description": "ABC iView is a video on demand, catch-up TV service.", "newname": "ABC iview", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-abciveiw"}, "791": {"component": {"domainsurls": 4}, "example_url": "bigo.tv", "filename": "sphirewall-application-bigo", "id": "791", "ignoreme": "0", "name": "Bigo", "new_description": "", "newname": "Bigo", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-bigo"}, "792": {"component": {"domainsurls": 16}, "example_url": "brightcove.com", "filename": "sphirewall-application-brightcove", "id": "792", "ignoreme": "0", "name": "Brightcove", "new_description": "\nBrightcove is offers cloud-based platforms to publish and distribute digital media. It offers Brightcove Video Cloud, an online video platform that enables its customers to publish and distribute video to internet-connected devices.", "newname": "Brightcove", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-brightcove"}, "793": {"component": {"domainsurls": 7}, "example_url": "https://buffstreams.watch/", "filename": "sphirewall-application-buffstream", "id": "793", "ignoreme": "0", "name": "Buffstream", "new_description": "", "newname": "Buffstream", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-buffstream"}, "794": {"component": {"domainsurls": 4}, "example_url": "crackle.com", "filename": "sphirewall-application-crackle", "id": "794", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON>", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-crackle"}, "795": {"component": {"domainsurls": 5}, "example_url": "crave.ca", "filename": "sphirewall-application-crave", "id": "795", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "Crave (formerly CraveTV) provides streaming access to TV and movies from various network for a subscription fee. Crave is owned and operated by Bell Media.", "newname": "<PERSON><PERSON>", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-crave"}, "796": {"component": {"domainsurls": 1}, "example_url": "crunchyroll.com", "filename": "sphirewall-application-crunchyroll", "id": "796", "ignoreme": "0", "name": "Crunchyroll", "new_description": "", "newname": "Crunchyroll", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-crunchyroll"}, "797": {"component": {"domainsurls": 3}, "example_url": "dacast.com", "filename": "sphirewall-application-dacast", "id": "797", "ignoreme": "0", "name": "Dacast", "new_description": "", "newname": "Dacast", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-dacast"}, "798": {"component": {"domainsurls": 49}, "example_url": "dailymotion.com", "filename": "sphirewall-application-dailymotion", "id": "798", "ignoreme": "0", "name": "Dailymotion", "new_description": "The latest music videos, short movies, tv shows, funny and extreme videos. Upload, share, and embed your videos", "newname": "Dailymotion", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-dailymotion"}, "799": {"component": {"domainsurls": 9}, "example_url": "foxtel.com.au", "filename": "sphirewall-application-foxtel", "id": "799", "ignoreme": "0", "name": "Foxtel", "new_description": "", "newname": "Foxtel", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-foxtel"}, "800": {"component": {"domainsurls": 1}, "example_url": "fubo.tv", "filename": "sphirewall-application-fubo", "id": "800", "ignoreme": "0", "name": "fubo", "new_description": "", "newname": "fubo", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-fubo"}, "801": {"component": {"domainsurls": 3}, "example_url": "giggl.app", "filename": "sphirewall-application-giggl", "id": "801", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON><PERSON>", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-giggl"}, "802": {"component": {"domainsurls": 17}, "example_url": "hbo.com", "filename": "sphirewall-application-hbogo", "id": "802", "ignoreme": "0", "name": "HBO", "new_description": "With HBOGO, enjoy instant and unlimited access to every episode of every season of the best HBO shows.", "newname": "HBO", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-hbogo"}, "803": {"component": {"domainsurls": 19, "regexpurls": 4}, "example_url": "hulu.com", "filename": "sphirewall-application-hulu", "id": "803", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "Watch current hit TV shows and acclaimed movies. Unlimited streaming available on Xbox, PS3, Apple TV, and many other devices.", "newname": "<PERSON><PERSON>", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-hulu"}, "804": {"component": {"domainsurls": 12}, "example_url": "jwplayer.com", "filename": "sphirewall-application-jwplayer", "id": "804", "ignoreme": "0", "name": "JW Player", "new_description": "We live in a Digital Video Economy, where every company is now a video company. JW Player helps video companies connect and engage with your audiences on the screens of their choice.", "newname": "JW Player", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-jwplayer"}, "805": {"component": {"domainsurls": 7}, "example_url": "kaltura.com", "filename": "sphirewall-application-kaltura", "id": "805", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON>", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-kaltura"}, "806": {"component": {"domainsurls": 3}, "example_url": "neontv.co.nz", "filename": "sphirewall-application-neontv", "id": "806", "ignoreme": "0", "name": "NeonTV", "new_description": "", "newname": "NeonTV", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-neontv"}, "807": {"component": {"domainsurls": 1}, "example_url": "nzonscreen.com", "filename": "sphirewall-application-nzonscreen", "id": "807", "ignoreme": "0", "name": "NZ On Screen", "new_description": "NZ On Screen is the showcase of NZ television, film, music video and web series", "newname": "NZ On Screen", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-nzonscreen"}, "808": {"component": {"domainsurls": 83}, "example_url": "paramountplus.com", "filename": "sphirewall-application-paramountplus", "id": "808", "ignoreme": "0", "name": "Paramount Plus", "new_description": "Paramount+ is an American subscription video on-demand over-the-top streaming service", "newname": "Paramount Plus", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-paramountplus"}, "809": {"component": {"domainsurls": 3}, "example_url": "peacocktv.com", "filename": "sphirewall-application-peacock", "id": "809", "ignoreme": "0", "name": "Peacock TV", "new_description": "", "newname": "Peacock TV", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-peacock"}, "810": {"component": {"domainsurls": 4}, "example_url": "pixellot.tv", "filename": "sphirewall-application-pixellot", "id": "810", "ignoreme": "0", "name": "pixellot", "new_description": "", "newname": "pixellot", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-pixellot"}, "811": {"component": {"domainsurls": 7}, "example_url": "plex.tv", "filename": "sphirewall-application-plex", "id": "811", "ignoreme": "0", "name": "Plex", "new_description": " Plex allows you to aggregate all your personal media and access it anywhere you go. Enjoy your own media on all your devices with the Plex Media Server.", "newname": "Plex", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-plex"}, "812": {"component": {"domainsurls": 10}, "example_url": "https://putlocker.boo/", "filename": "sphirewall-applicaiton-putlocker", "id": "812", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON><PERSON>", "parent": "1242", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-putlocker"}, "813": {"component": {"domainsurls": 14}, "example_url": "www.sbs.com.au/ondemand/", "filename": "sphirewall-application-sbsod", "id": "813", "ignoreme": "0", "name": "SBS On Demand", "new_description": "SBS on Demand is a video on demand, catch-up TV service.", "newname": "SBS On Demand", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-sbsod"}, "814": {"component": {"domainsurls": 6}, "example_url": "go.sky.com", "filename": "sphirewall-application-skygo", "id": "814", "ignoreme": "0", "name": "SkyGo", "new_description": "Watch SkyTV online.", "newname": "SkyGo", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-skygo"}, "815": {"component": {"domainsurls": 4}, "example_url": "sling.com", "filename": "sphirewall-application-sling", "id": "815", "ignoreme": "0", "name": "sling", "new_description": "", "newname": "sling", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-sling"}, "816": {"component": {"domainsurls": 7}, "example_url": "sonos.com", "filename": "sphirewall-application-sonos", "id": "816", "ignoreme": "0", "name": "Sonos", "new_description": "", "newname": "Sonos", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-sonos"}, "817": {"component": {"domainsurls": 1}, "example_url": "sproutvideo.com", "filename": "sphirewall-application-sproutvideo", "id": "817", "ignoreme": "0", "name": "Sproutvideo", "new_description": "", "newname": "Sproutvideo", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-sproutvideo"}, "818": {"component": {"domainsurls": 3}, "example_url": "stan.com.au", "filename": "sphirewall-application-stanstreaming", "id": "818", "ignoreme": "0", "name": "<PERSON>", "new_description": "Stan is a content provider. Watch Movies & TV Shows Online or Stream right to your TV.", "newname": "<PERSON>", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-stanstreaming"}, "819": {"component": {"domainsurls": 3}, "example_url": "streamable.com", "filename": "sphirewall-application-streamable-com", "id": "819", "ignoreme": "0", "name": "Streamable", "new_description": "Streamable is an internet video streaming company that powers innovative video experiences for bloggers and publishers around the world.", "newname": "Streamable", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-streamable-com"}, "820": {"component": {"domainsurls": 1}, "example_url": "streamhoster.com", "filename": "sphirewall-application-streamhoster", "id": "820", "ignoreme": "0", "name": "streamhoster", "new_description": "", "newname": "streamhoster", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-streamhoster"}, "821": {"component": {"domainsurls": 8}, "example_url": "10play.com.au", "filename": "sphirewall-application-tenplay", "id": "821", "ignoreme": "0", "name": "Tenplay", "new_description": "Ten Play is a video on demand, catch-up TV service.", "newname": "Tenplay", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-tenplay"}, "822": {"component": {"domainsurls": 3}, "example_url": "theoplayer.com", "filename": "sphirewall-application-theoplayer", "id": "822", "ignoreme": "0", "name": "THEOplayer", "new_description": "", "newname": "THEOplayer", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-theoplayer"}, "823": {"component": {"domainsurls": 11}, "example_url": "tubitv.com", "filename": "sphirewall-application-tubi", "id": "823", "ignoreme": "0", "name": "tubi TV", "new_description": "Tubi is an American over-the-top content platform and ad-supported streaming service owned by Fox Corporation offering online streaming from a library of films and television series for free.", "newname": "tubi TV", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-tubi"}, "824": {"component": {"domainsurls": 1}, "example_url": "tunein.com", "filename": "sphirewall-application-tunein", "id": "824", "ignoreme": "0", "name": "TuneIn", "new_description": "", "newname": "TuneIn", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-tunein"}, "825": {"component": {"domainsurls": 5}, "example_url": "threenow.co.nz", "filename": "sphirewall-application-tv3", "id": "825", "ignoreme": "0", "name": "TV3 OnDemand", "new_description": "Watch the latest episodes with TV On Demand.", "newname": "TV3 OnDemand", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-tv3"}, "826": {"component": {"domainsurls": 4}, "example_url": "tvnz.co.nz", "filename": "sphirewall-application-tvnzondemand", "id": "826", "ignoreme": "0", "name": "TVNZ OnDemand", "new_description": "Watch TVNZ programming online", "newname": "TVNZ OnDemand", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-tvnzondemand"}, "827": {"component": {"domainsurls": 15}, "example_url": "twitch.tv", "filename": "sphirewall-application-twitchtv", "id": "827", "ignoreme": "0", "name": "TwitchTV", "new_description": "Twitch is the world's leading video platform and community for gamers. More than 45 million gamers gather every month on Twitch to broadcast, watch and chat about gaming.", "newname": "TwitchTV", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-twitchtv"}, "828": {"component": {"domainsurls": 1}, "example_url": "uplynk.com", "filename": "sphirewall-application-uplynk", "id": "828", "ignoreme": "0", "name": "upLynk", "new_description": "", "newname": "upLynk", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-uplynk"}, "829": {"component": {"domainsurls": 1}, "example_url": "", "filename": "sphirewall-applicaiton-vine", "id": "829", "ignoreme": "0", "name": "Vine", "new_description": "", "newname": "Vine", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-vine"}, "830": {"component": {"domainsurls": 3}, "example_url": "vudu.com", "filename": "sphirewall-application-vudu", "id": "830", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON>", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-vudu"}, "831": {"component": {"domainsurls": 5}, "example_url": "wistia.com", "filename": "sphirewall-application-wistia", "id": "831", "ignoreme": "0", "name": "Wistia", "new_description": "", "newname": "Wistia", "parent": "152", "test_url": "test.mysmoothwall.net/sphirewall-application-wistia"}, "832": {"component": {}, "example_url": "", "filename": "businesscorporate<PERSON>r-child", "id": "832", "ignoreme": "1", "name": " Business and Government", "new_description": "Businesses and organisations, including government and non-profits.", "newname": " Business and Government", "parent": "252", "test_url": "test.mysmoothwall.net/businesscorporategrar-child"}, "833": {"component": {}, "example_url": "www.abc.xyz", "filename": "businessgrar-parent", "id": "833", "ignoreme": "0", "name": "Business", "new_description": "Sites for businesses and commercial organisations, where the organisation provides paid for goods or services.", "newname": "Business", "parent": "437", "test_url": "test.mysmoothwall.net/businessgrar-parent"}, "834": {"component": {"domainsurls": 8}, "example_url": "hometownticketing.com", "filename": "sphirewall-application-hometownticketing", "id": "834", "ignoreme": "0", "name": "HomeTown Ticketing", "new_description": "", "newname": "HomeTown Ticketing", "parent": "833", "test_url": "test.mysmoothwall.net/sphirewall-application-hometownticketing"}, "835": {"component": {"domainsurls": 7}, "example_url": "intercom.io", "filename": "sphirewall-application-intercom", "id": "835", "ignoreme": "0", "name": "Intercom", "new_description": "", "newname": "Intercom", "parent": "833", "test_url": "test.mysmoothwall.net/sphirewall-application-intercom"}, "836": {"component": {}, "example_url": "prometheanworld.com", "filename": "sphirewall-application-promethean", "id": "836", "ignoreme": "0", "name": "Promethean", "new_description": "Promethean smart board company. ", "newname": "Promethean", "parent": "833", "test_url": "test.mysmoothwall.net/sphirewall-application-promethean"}, "837": {"component": {"domainsurls": 6}, "example_url": "radix-int.com", "filename": "sphirewall-application-radix", "id": "837", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "Radix offers end-to-end device management solutions, consolidating all the organization devices, processes and stakeholders into one easy-to-use management platform.", "newname": "<PERSON><PERSON><PERSON>", "parent": "833", "test_url": "test.mysmoothwall.net/sphirewall-application-radix"}, "838": {"component": {"domainsurls": 7}, "example_url": "squareup.com", "filename": "sphirewall-application-squareup", "id": "838", "ignoreme": "0", "name": "SquareUp", "new_description": "Point of Sale application used by vendor for business transactions", "newname": "SquareUp", "parent": "833", "test_url": "test.mysmoothwall.net/sphirewall-application-squareup"}, "839": {"component": {}, "example_url": "www.christianaid.org.uk", "filename": "charity-parent", "id": "839", "ignoreme": "0", "name": "Charity and Non-profit", "new_description": "Charity and non-profit organisations", "newname": "Charity and Non-profit", "parent": "437", "test_url": "test.mysmoothwall.net/charity-parent"}, "840": {"component": {"domainsurls": 4}, "example_url": "worldvision.org", "filename": "sphirewall-application-worldvision-org-nz", "id": "840", "ignoreme": "0", "name": "World Vision", "new_description": "", "newname": "World Vision", "parent": "839", "test_url": "test.mysmoothwall.net/sphirewall-application-worldvision-org-nz"}, "841": {"component": {}, "example_url": "www.hsbc.com", "filename": "finance-parent", "id": "841", "ignoreme": "0", "name": "Financial Services", "new_description": "Finance sites including investment advice, but not Online Banking portals", "newname": "Financial Services", "parent": "437", "test_url": "test.mysmoothwall.net/finance-parent"}, "842": {"component": {"domainsurls": 5}, "example_url": "alipay.com", "filename": "sphirewall-application-alipay", "id": "842", "ignoreme": "0", "name": "Alipay", "new_description": "", "newname": "Alipay", "parent": "841", "test_url": "test.mysmoothwall.net/sphirewall-application-alipay"}, "843": {"component": {"domainsurls": 3}, "example_url": "opensea.io", "filename": "sphirewall-application-opensea", "id": "843", "ignoreme": "0", "name": "OpenSea", "new_description": "a peer-to-peer marketplace for nfts, rare digital items and crypto collectibles.", "newname": "OpenSea", "parent": "841", "test_url": "test.mysmoothwall.net/sphirewall-application-opensea"}, "844": {"component": {"domainsurls": 3}, "example_url": "patreon.com", "filename": "sphirewall-application-patreon", "id": "844", "ignoreme": "0", "name": "Patreon", "new_description": "", "newname": "Patreon", "parent": "841", "test_url": "test.mysmoothwall.net/sphirewall-application-patreon"}, "845": {"component": {}, "example_url": "", "filename": "eduref-child", "id": "845", "ignoreme": "1", "name": " Information and Reference", "new_description": "Informational sites and reference materials", "newname": " Information and Reference", "parent": "252", "test_url": "test.mysmoothwall.net/eduref-child"}, "846": {"component": {}, "example_url": "openai.com", "filename": "aitools-parent", "id": "846", "ignoreme": "0", "name": "AI Tools", "new_description": "General AI (AGI) tools are a set of software and technology that aims to simulate or replicate human-like intelligence across a wide range of tasks. These tools are designed to be flexible, adaptable and able to learn and improve on their own. They can be used for autonomous vehicles, chatbots, and virtual assistants. Examples include machine learning frameworks, NLP libraries and deep learning architectures.", "newname": "AI Tools", "parent": "396", "test_url": "test.mysmoothwall.net/aitools-parent"}, "847": {"component": {"domainsurls": 1}, "example_url": "huggingface.co", "filename": "sphirewall-application-huggingface", "id": "847", "ignoreme": "0", "name": "Hugging Face", "new_description": "Hugging Face is a company that provides a platform for natural language processing (NLP) models. They offer a wide range of pre-trained models for tasks such as text classification, language translation, and text generation, along with open-source tools to fine-tune, use and share these models. They also provide a simple API to access the models, making it easy for developers, researchers and data scientists to integrate NLP in their projects and applications.", "newname": "Hugging Face", "parent": "846", "test_url": "test.mysmoothwall.net/sphirewall-application-huggingface"}, "848": {"component": {}, "example_url": "yellowpages.com", "filename": "reference", "id": "848", "ignoreme": "0", "name": "Reference", "new_description": "", "newname": "Reference", "parent": "396", "test_url": "test.mysmoothwall.net/reference"}, "849": {"component": {}, "example_url": "www.bbc.co.uk/news", "filename": "news-parent", "id": "849", "ignoreme": "0", "name": "News", "new_description": "Sites whose primary aim is to present local, national, or international news, or news relating to a specific topic.", "newname": "News", "parent": "396", "test_url": "test.mysmoothwall.net/news-parent"}, "850": {"component": {"domainsurls": 1}, "example_url": "aljazeera.com", "filename": "sphirewall-application-aljazeera", "id": "850", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON><PERSON>", "new_description": "News, analysis from the Middle East & worldwide, multimedia & interactives, opinions, context, documentaries, podcasts, long reads and broadcast schedule...", "newname": "<PERSON><PERSON><PERSON><PERSON>", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-aljazeera"}, "851": {"component": {}, "example_url": "aol.com", "filename": "sphirewall-applicaiton-aol", "id": "851", "ignoreme": "0", "name": "AOL", "new_description": "", "newname": "AOL", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-aol"}, "852": {"component": {"domainsurls": 3}, "example_url": "arirang.com", "filename": "sphirewall-application-arirangtvnewskorea", "id": "852", "ignoreme": "0", "name": "Arirang TV News Korea", "new_description": "Video player for English language news from Korea. ", "newname": "Arirang TV News Korea", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-arirangtvnewskorea"}, "853": {"component": {"domainsurls": 1}, "example_url": "axios.com", "filename": "sphirewall-application-axios", "id": "853", "ignoreme": "0", "name": "A<PERSON>os", "new_description": "Axios is an American news website. Articles are typically brief and matter-of-fact: most are shorter than 300 words and use bullet points so they are easier to scan.", "newname": "A<PERSON>os", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-axios"}, "854": {"component": {"domainsurls": 21}, "example_url": "bbc.com", "filename": "sphirewall-application-bbc", "id": "854", "ignoreme": "0", "name": "BBC", "new_description": "Breaking news, sport, TV, radio and a whole lot more. The BBC informs, educates and entertains - wherever you are, whatever your age.", "newname": "BBC", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-bbc"}, "855": {"component": {"domainsurls": 13}, "example_url": "bloomberg.com", "filename": "sphirewall-application-bloomberg", "id": "855", "ignoreme": "0", "name": "Bloomberg", "new_description": "Bloomberg is a privately held financial, software, data, and media company. They break news, analyze data and share perspectives.", "newname": "Bloomberg", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-bloomberg"}, "856": {"component": {"domainsurls": 10}, "example_url": "buzzfeed.com", "filename": "sphirewall-application-buzzfeed", "id": "856", "ignoreme": "0", "name": "BuzzFeed", "new_description": "BuzzFeed is a cross-platform digital media company delivering news and entertainment content to a global audience. The platform has breaking news, vital journalism, quizzes, videos, and more.", "newname": "BuzzFeed", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-buzzfeed"}, "857": {"component": {"domainsurls": 14}, "example_url": "cbc.ca", "filename": "sphirewall-application-cbc", "id": "857", "ignoreme": "0", "name": "Canadian Broadcasting Corporation", "new_description": "", "newname": "Canadian Broadcasting Corporation", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-cbc"}, "858": {"component": {"domainsurls": 1}, "example_url": "cnbc.com", "filename": "sphirewall-application-cnbc", "id": "858", "ignoreme": "0", "name": "CNBC", "new_description": "CNBC is the world leader in business news and real-time financial market coverage.", "newname": "CNBC", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-cnbc"}, "859": {"component": {"domainsurls": 3}, "example_url": "cnn.com", "filename": "sphirewall-application-cnn", "id": "859", "ignoreme": "0", "name": "CNN", "new_description": "Find the latest breaking news and information on the top stories, weather, business, entertainment, politics, and more.", "newname": "CNN", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-cnn"}, "860": {"component": {"domainsurls": 1}, "example_url": "dailymail.co.uk", "filename": "sphirewall-application-dailymail", "id": "860", "ignoreme": "0", "name": "Daily Mail", "new_description": " The Daily Mail is a British daily middle-market newspaper in a tabloid format. The newspaper's reliability has been called into question, but it has one of the highest circulations in the", "newname": "Daily Mail", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-dailymail"}, "861": {"component": {"domainsurls": 1}, "example_url": "designboom.com", "filename": "sphirewall-application-designboom", "id": "861", "ignoreme": "0", "name": "designboom", "new_description": "designboom is the world’s first online magazine worldwide. ‘designboom is known as the go-to designation for all things architecture,<PERSON> writes forbes magazine. ‘it’s a platform that is crazy popular too, as they’ve built up a cult following over 20 years.’", "newname": "designboom", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-designboom"}, "862": {"component": {"domainsurls": 9}, "example_url": "forbes.com", "filename": "sphirewall-application-forbes", "id": "862", "ignoreme": "0", "name": "Forbes", "new_description": "Forbes is a global media company, focusing on business, investing, technology, entrepreneurship, leadership, and lifestyle.", "newname": "Forbes", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-forbes"}, "863": {"component": {"domainsurls": 4}, "example_url": "fox.com", "filename": "sphirewall-application-foxnews", "id": "863", "ignoreme": "0", "name": "FoxNews", "new_description": "Full Episodes, Clips and the latest information about all of your favorite FOX shows.", "newname": "FoxNews", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-foxnews"}, "864": {"component": {"domainsurls": 11}, "example_url": "gizmodo.com", "filename": "sphirewall-application-gizmodo", "id": "864", "ignoreme": "0", "name": "Gizmodo", "new_description": "Everything Is Technology. A technology news site.", "newname": "Gizmodo", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-gizmodo"}, "865": {"component": {"domainsurls": 3}, "example_url": "huffingtonpost.com", "filename": "sphirewall-application-huffingtonpost-com", "id": "865", "ignoreme": "0", "name": "Huffington Post", "new_description": "", "newname": "Huffington Post", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-huffingtonpost-com"}, "866": {"component": {"domainsurls": 5}, "example_url": "msn.com", "filename": "sphirewall-application-msn", "id": "866", "ignoreme": "0", "name": "MSN", "new_description": "", "newname": "MSN", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-msn"}, "867": {"component": {"domainsurls": 6}, "example_url": "nytimes.com", "filename": "sphirewall-application-newyorktimes", "id": "867", "ignoreme": "0", "name": "New York Times", "new_description": "The New York Times: Find breaking news, multimedia, reviews & opinion on Washington, business, sports, movies, travel, books, jobs...", "newname": "New York Times", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-newyorktimes"}, "868": {"component": {"domainsurls": 3}, "example_url": "nzherald.co.nz", "filename": "sphirewall-application-nzherald", "id": "868", "ignoreme": "0", "name": "NZ Herald", "new_description": "", "newname": "NZ Herald", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-nzherald"}, "869": {"component": {"domainsurls": 4}, "example_url": "rnz.co.nz", "filename": "sphirewall-application-rnz", "id": "869", "ignoreme": "0", "name": "RNZ", "new_description": "RNZ (Radio New Zealand) is New Zealand's independent public service multimedia organisation and is a Crown entity established under the Radio New Zealand Act 1995.", "newname": "RNZ", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-rnz"}, "870": {"component": {"domainsurls": 6}, "example_url": "sina.com.cn", "filename": "sphirewall-application-sina", "id": "870", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "Chinese news website.", "newname": "<PERSON><PERSON>", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-sina"}, "871": {"component": {"domainsurls": 3}, "example_url": "spiegel.de", "filename": "sphirewall-application-spiegel", "id": "871", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "Germanys largest news and current affairs site.", "newname": "<PERSON><PERSON><PERSON>", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-spiegel"}, "872": {"component": {"domainsurls": 3}, "example_url": "stuff.co.nz", "filename": "sphirewall-application-stuff", "id": "872", "ignoreme": "0", "name": "stuff.co.nz", "new_description": "New Zealand's leading online news site.", "newname": "stuff.co.nz", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-stuff"}, "873": {"component": {"domainsurls": 1}, "example_url": "techcrunch.com", "filename": "sphirewall-application-techcrunch", "id": "873", "ignoreme": "0", "name": "TechCrunch", "new_description": "TechCrunch is a leading technology media property, dedicated to obsessively profiling startups, reviewing new Internet products.", "newname": "TechCrunch", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-application-techcrunch"}, "874": {"component": {"domainsurls": 4}, "example_url": "https://www.theverge.com/tech", "filename": "sphirewall-applicaiton-technews", "id": "874", "ignoreme": "0", "name": "Tech News", "new_description": "", "newname": "Tech News", "parent": "849", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-technews"}, "875": {"component": {}, "example_url": "google.com/maps", "filename": "mapping-parent", "id": "875", "ignoreme": "0", "name": "Maps", "new_description": "Sites which provide geographical mapping services including those that promote or provide opportunity for travel planning", "newname": "Maps", "parent": "396", "test_url": "test.mysmoothwall.net/mapping-parent"}, "876": {"component": {"domainsurls": 10}, "example_url": "life360.com", "filename": "sphirewall-application-life360", "id": "876", "ignoreme": "0", "name": "Life 360", "new_description": "Life 360 is a location tracking app. Users are able to see each others location if they are in the circle. Circle's are created through invite codes. The app also contains a messaging and call feature. The app detects when a user is driving and collects data to compile weekly reports. The app also detects when the user has been in an accident and contacts emergency services.", "newname": "Life 360", "parent": "875", "test_url": "test.mysmoothwall.net/sphirewall-application-life360"}, "877": {"component": {"domainsurls": 1}, "example_url": "mapchart.net", "filename": "sphirewall-application-mapchart", "id": "877", "ignoreme": "0", "name": "MapChart", "new_description": "Create your own custom maps", "newname": "MapChart", "parent": "875", "test_url": "test.mysmoothwall.net/sphirewall-application-mapchart"}, "878": {"component": {}, "example_url": "translate.google.com", "filename": "translation-parent", "id": "878", "ignoreme": "0", "name": "Translation", "new_description": "Sites which provide translation services, such as blocks of text or providing entire site translation", "newname": "Translation", "parent": "396", "test_url": "test.mysmoothwall.net/translation-parent"}, "879": {"component": {"domainsurls": 1}, "example_url": "sayhi.com", "filename": "sphirewall-application-sayhi", "id": "879", "ignoreme": "0", "name": "SayHI", "new_description": "SayHI translation application.", "newname": "SayHI", "parent": "878", "test_url": "test.mysmoothwall.net/sphirewall-application-sayhi"}, "880": {"component": {}, "example_url": "www.lrb.co.uk", "filename": "books-parent", "id": "880", "ignoreme": "0", "name": "Books and Literature", "new_description": "Sites selling, reviewing or discussing books or audio books in any format", "newname": "Books and Literature", "parent": "396", "test_url": "test.mysmoothwall.net/books-parent"}, "881": {"component": {"domainsurls": 3}, "example_url": "goodreads.com", "filename": "sphirewall-application-goodreads", "id": "881", "ignoreme": "0", "name": "Goodreads", "new_description": "Book reviews and recommendations online.", "newname": "Goodreads", "parent": "880", "test_url": "test.mysmoothwall.net/sphirewall-application-goodreads"}, "882": {"component": {}, "example_url": "www.sparknotes.com", "filename": "education-parent", "id": "882", "ignoreme": "0", "name": "Education", "new_description": "Sites that provide materials and information that aid in learning", "newname": "Education", "parent": "396", "test_url": "test.mysmoothwall.net/education-parent"}, "883": {"component": {"domainsurls": 3}, "example_url": "abcya.com", "filename": "sphirewall-applicaiton-abcya-com", "id": "883", "ignoreme": "0", "name": "ABCYA", "new_description": "Educational games for students ", "newname": "ABCYA", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-abcya-com"}, "884": {"component": {"domainsurls": 1}, "example_url": "acellus.com", "filename": "sphirewall-application-acellus", "id": "884", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-acellus"}, "885": {"component": {"domainsurls": 13}, "example_url": "achieve3000.com", "filename": "sphirewall-application-achieve3000", "id": "885", "ignoreme": "0", "name": "Achieve3000", "new_description": "", "newname": "Achieve3000", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-achieve3000"}, "886": {"component": {"domainsurls": 1}, "example_url": "activelearnprimary.com.au", "filename": "sphirewall-applicaiton-activelearnprimary-com-au", "id": "886", "ignoreme": "0", "name": "Active Learn Primary", "new_description": "A digital learning space for your pupils and a toolkit for you, so that you can search, plan, allocate and assess all in one place.", "newname": "Active Learn Primary", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-activelearnprimary-com-au"}, "887": {"component": {"domainsurls": 3}, "example_url": "pearsonactivelearn.com", "filename": "sphirewall-application-activeteach", "id": "887", "ignoreme": "0", "name": "Active Teach", "new_description": "", "newname": "Active Teach", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-activeteach"}, "888": {"component": {"domainsurls": 1}, "example_url": "adaptedmind.com", "filename": "sphirewall-application-adaptedmind-com", "id": "888", "ignoreme": "0", "name": "Adapted Mind", "new_description": "", "newname": "Adapted Mind", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-adaptedmind-com"}, "889": {"component": {"domainsurls": 3}, "example_url": "aeseducation.com", "filename": "sphirewall-application-aeseducation", "id": "889", "ignoreme": "0", "name": "Aeseducation", "new_description": "", "newname": "Aeseducation", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-aeseducation"}, "890": {"component": {"domainsurls": 1}, "example_url": "albert.io", "filename": "sphirewall-application-albert", "id": "890", "ignoreme": "0", "name": "<PERSON>", "new_description": "", "newname": "<PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-albert"}, "891": {"component": {"domainsurls": 3}, "example_url": "aleks.com", "filename": "sphirewall-application-aleks", "id": "891", "ignoreme": "0", "name": "Aleks", "new_description": "", "newname": "Aleks", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-aleks"}, "892": {"component": {"domainsurls": 1}, "example_url": "almanac.com", "filename": "sphirewall-application-almanac", "id": "892", "ignoreme": "0", "name": "almanac", "new_description": "", "newname": "almanac", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-almanac"}, "893": {"component": {"domainsurls": 1}, "example_url": "amplify.com", "filename": "sphirewall-application-amplify", "id": "893", "ignoreme": "0", "name": "Amplify", "new_description": "", "newname": "Amplify", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-amplify"}, "894": {"component": {"domainsurls": 1}, "example_url": "apex.com", "filename": "sphirewall-application-apex", "id": "894", "ignoreme": "0", "name": "Apex", "new_description": "", "newname": "Apex", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-apex"}, "895": {"component": {"domainsurls": 1}, "example_url": "arbolabc.com", "filename": "sphirewall-application-arbolabc", "id": "895", "ignoreme": "0", "name": "arbolabc", "new_description": "used by bilingual teachers and students", "newname": "arbolabc", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-arbolabc"}, "896": {"component": {"domainsurls": 3}, "example_url": "arcademics.com", "filename": "sphirewall-application-arcademics-com", "id": "896", "ignoreme": "0", "name": "Arcademic", "new_description": "", "newname": "Arcademic", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-arcademics-com"}, "897": {"component": {"domainsurls": 1}, "example_url": "arkansas.com", "filename": "sphirewall-application-arkansas", "id": "897", "ignoreme": "0", "name": "arkansas", "new_description": "", "newname": "arkansas", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-arkansas"}, "898": {"component": {"domainsurls": 1}, "example_url": "arkansasstateparks.com", "filename": "sphirewall-application-arkansasstateparks", "id": "898", "ignoreme": "0", "name": "arkansas state parks", "new_description": "", "newname": "arkansas state parks", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-arkansasstateparks"}, "899": {"component": {"domainsurls": 3}, "example_url": "ascendertx.com", "filename": "sphirewall-application-ascender", "id": "899", "ignoreme": "0", "name": "Ascender", "new_description": "The TCC has been providing high quality, compliant, comprehensive business and student software and support to Texas LEAs for over 50 years.", "newname": "Ascender", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-ascender"}, "9": {"component": {"domainsurls": 9670, "regexpurls": 1, "searchterms": 513, "weightedphrases": 2664}, "example_url": "www.weedguru.com", "filename": "drugs", "id": "9", "ignoreme": "0", "name": "Drugs", "new_description": "Sites pertaining to the sale, manufacture, promotion or use of recreational and prescription drugs", "newname": "Drugs", "parent": "436", "test_url": "test.mysmoothwall.net/drugs"}, "900": {"component": {"domainsurls": 1}, "example_url": "banqer.co", "filename": "sphirewall-applicaiton-banqer-co", "id": "900", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-banqer-co"}, "901": {"component": {"domainsurls": 3}, "example_url": "teachbanzai.com", "filename": "sphirewall-application-banzai", "id": "901", "ignoreme": "0", "name": "Banzai", "new_description": "", "newname": "Banzai", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-banzai"}, "902": {"component": {"domainsurls": 1}, "example_url": "maths.prototec.co.nz", "filename": "sphirewall-application-maths-prototec", "id": "902", "ignoreme": "0", "name": "Basic Facts Maths Practice", "new_description": "", "newname": "Basic Facts Maths Practice", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-maths-prototec"}, "903": {"component": {"domainsurls": 3}, "example_url": "bfwpub.com", "filename": "sphirewall-application-bfwpub", "id": "903", "ignoreme": "0", "name": "Bedford, Freeman & Worth", "new_description": "", "newname": "Bedford, Freeman & Worth", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-bfwpub"}, "904": {"component": {"domainsurls": 8}, "example_url": "benchmarkuniverse.com", "filename": "sphirewall-application-benchmarkuniverse", "id": "904", "ignoreme": "0", "name": "Benchmark Universe", "new_description": "", "newname": "Benchmark Universe", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-benchmarkuniverse"}, "905": {"component": {"domainsurls": 3}, "example_url": "bigbluebutton.org", "filename": "sphirewall-application-bigbluebutton", "id": "905", "ignoreme": "0", "name": "BigBlueButton", "new_description": "", "newname": "BigBlueButton", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-bigbluebutton"}, "906": {"component": {"domainsurls": 3}, "example_url": "bigideaslearning.com", "filename": "sphirewall-application-bigideaslearning", "id": "906", "ignoreme": "0", "name": "Big Ideas Learning", "new_description": "", "newname": "Big Ideas Learning", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-bigideaslearning"}, "907": {"component": {"domainsurls": 1}, "example_url": "birdbraintechnologies.com", "filename": "sphirewall-application-birdbraintechnologies", "id": "907", "ignoreme": "0", "name": "BirdBrain Technologies", "new_description": "", "newname": "BirdBrain Technologies", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-birdbraintechnologies"}, "908": {"component": {"domainsurls": 3}, "example_url": "blackbaud.com", "filename": "sphirewall-application-blackbaud", "id": "908", "ignoreme": "0", "name": "blackbaud", "new_description": "", "newname": "blackbaud", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-blackbaud"}, "909": {"component": {"domainsurls": 3}, "example_url": "id.blooket.com", "filename": "sphirewall-application-blooket", "id": "909", "ignoreme": "0", "name": "Blooket", "new_description": "Welcome to the World of Blooket: a new take on trivia and review games!\nThe way it works is that a teacher/host picks a question set and a unique game mode. Then, we generate a code that players can use to join the game on their own devices. ", "newname": "Blooket", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-blooket"}, "910": {"component": {"domainsurls": 3}, "example_url": "boomlearning.com", "filename": "sphirewall-application-boomlearning", "id": "910", "ignoreme": "0", "name": "Boom Learning", "new_description": "", "newname": "Boom Learning", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-boomlearning"}, "911": {"component": {"domainsurls": 3}, "example_url": "brainingcamp.com", "filename": "sphirewall-application-brainingcamp", "id": "911", "ignoreme": "0", "name": "Brainingcamp", "new_description": "", "newname": "Brainingcamp", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-brainingcamp"}, "912": {"component": {"domainsurls": 1}, "example_url": "brainpop.com", "filename": "sphirewall-application-brainpop", "id": "912", "ignoreme": "0", "name": "brainpop", "new_description": "", "newname": "brainpop", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-brainpop"}, "913": {"component": {"domainsurls": 1}, "example_url": "learnenglishkids.britishcouncil.org", "filename": "sphirewall-application-britishcouncil", "id": "913", "ignoreme": "0", "name": "British Council", "new_description": "British Council - Learn English - Requested by Vallentuna.se", "newname": "British Council", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-britishcouncil"}, "914": {"component": {"domainsurls": 1}, "example_url": "bpho.org.uk", "filename": "sphirewall-application-britishphysicsolympiad", "id": "914", "ignoreme": "0", "name": "British Physics Olympiad", "new_description": "The BPhO is to encourage the study of physics and to recognise excellence in young physicists through ten annual physics competitions. Why not take the challenge!", "newname": "British Physics Olympiad", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-britishphysicsolympiad"}, "915": {"component": {"domainsurls": 6}, "example_url": "cambiumassessment.com", "filename": "sphirewall-application-cambium", "id": "915", "ignoreme": "0", "name": "Cambium Assessment", "new_description": "Cambium Assessment is a provider of online assessments for students across America.", "newname": "Cambium Assessment", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-cambium"}, "916": {"component": {"domainsurls": 1}, "example_url": "carnegielearning.com", "filename": "sphirewall-application-carnegielearning", "id": "916", "ignoreme": "0", "name": "Carnegie Learning", "new_description": "", "newname": "Carnegie Learning", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-carnegielearning"}, "917": {"component": {"domainsurls": 1}, "example_url": "cellsalive.com", "filename": "sphirewall-application-cellsalive", "id": "917", "ignoreme": "0", "name": "cellsalive", "new_description": "", "newname": "cellsalive", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-cellsalive"}, "918": {"component": {"domainsurls": 3}, "example_url": "chemguide.co.uk", "filename": "sphirewall-application-chemguide", "id": "918", "ignoreme": "0", "name": "chemguide", "new_description": "Helping you to understand Chemistry", "newname": "chemguide", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-chemguide"}, "919": {"component": {"domainsurls": 1}, "example_url": "classcalc.com", "filename": "sphirewall-application-classcalc", "id": "919", "ignoreme": "0", "name": "ClassCalc", "new_description": "", "newname": "ClassCalc", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-classcalc"}, "920": {"component": {"domainsurls": 7}, "example_url": "codecademy.com", "filename": "sphirewall-application-codeacademy", "id": "920", "ignoreme": "0", "name": "Code Academy", "new_description": "Codecademy is an American online interactive platform that offers free coding classes", "newname": "Code Academy", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-codeacademy"}, "921": {"component": {"domainsurls": 1}, "example_url": "codeavengers.com", "filename": "sphirewall-application-codeavengers", "id": "921", "ignoreme": "0", "name": "Code Avengers", "new_description": "", "newname": "Code Avengers", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-codeavengers"}, "922": {"component": {"domainsurls": 3}, "example_url": "codehs.com", "filename": "sphirewall-application-codehs", "id": "922", "ignoreme": "0", "name": "CodeHS", "new_description": "", "newname": "CodeHS", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-codehs"}, "923": {"component": {"domainsurls": 1}, "example_url": "codesters.com", "filename": "sphirewall-application-codesters", "id": "923", "ignoreme": "0", "name": "codesters", "new_description": "", "newname": "codesters", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-codesters"}, "924": {"component": {"domainsurls": 3}, "example_url": "collegeboard.org", "filename": "sphirewall-application-collegeboard", "id": "924", "ignoreme": "0", "name": "CollegeBoard", "new_description": "", "newname": "CollegeBoard", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-collegeboard"}, "925": {"component": {"domainsurls": 1}, "example_url": "commonlit.org", "filename": "sphirewall-application-commonlit", "id": "925", "ignoreme": "0", "name": "commonlit", "new_description": "", "newname": "commonlit", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-commonlit"}, "926": {"component": {"domainsurls": 1}, "example_url": "compass.education", "filename": "sphirewall-application-compass", "id": "926", "ignoreme": "0", "name": "<PERSON>mp<PERSON>", "new_description": "", "newname": "<PERSON>mp<PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-compass"}, "927": {"component": {"domainsurls": 1}, "example_url": "conjuguemos.com", "filename": "sphirewall-application-conjuguemos", "id": "927", "ignoreme": "0", "name": "<PERSON>ju<PERSON><PERSON>", "new_description": "", "newname": "<PERSON>ju<PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-conjuguemos"}, "928": {"component": {"domainsurls": 1}, "example_url": "cpm.org", "filename": "sphirewall-application-cpmorg", "id": "928", "ignoreme": "0", "name": "CPM", "new_description": "", "newname": "CPM", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-cpmorg"}, "929": {"component": {"domainsurls": 1}, "example_url": "cummins.com", "filename": "sphirewall-application-cummins", "id": "929", "ignoreme": "0", "name": "cummins", "new_description": "", "newname": "cummins", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-cummins"}, "930": {"component": {"domainsurls": 1}, "example_url": "dawnsign.com", "filename": "sphirewall-application-dawnsign", "id": "930", "ignoreme": "0", "name": "DawnSign", "new_description": "", "newname": "DawnSign", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-dawnsign"}, "931": {"component": {"domainsurls": 1}, "example_url": "deltamath.com", "filename": "sphirewall-application-deltamath", "id": "931", "ignoreme": "0", "name": "DeltaMath", "new_description": "", "newname": "DeltaMath", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-deltamath"}, "932": {"component": {"domainsurls": 1}, "example_url": "desmos.com", "filename": "sphirewall-application-desmos-com", "id": "932", "ignoreme": "0", "name": "Des<PERSON>", "new_description": "", "newname": "Des<PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-desmos-com"}, "933": {"component": {"domainsurls": 1}, "example_url": "digitalcompass.org", "filename": "sphirewall-application-digitalcompass-org", "id": "933", "ignoreme": "0", "name": "Digital Compass", "new_description": "", "newname": "Digital Compass", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-digitalcompass-org"}, "934": {"component": {"domainsurls": 3}, "example_url": "digitalexpertsacademy.com", "filename": "sphirewall-application-digitalexpertsacademy", "id": "934", "ignoreme": "0", "name": "Digital Experts Academy", "new_description": "The Digital Experts Academy is a program of world-leading digital citizenship and media literacy courses for students.", "newname": "Digital Experts Academy", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-digitalexpertsacademy"}, "935": {"component": {"domainsurls": 1}, "example_url": "dmac-solutions.net", "filename": "sphirewall-application-dmac-solutions", "id": "935", "ignoreme": "0", "name": "dmac-solutions", "new_description": "", "newname": "dmac-solutions", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-dmac-solutions"}, "936": {"component": {"domainsurls": 1}, "example_url": "dogonews.com", "filename": "sphirewall-applicaiton-dogonews-com", "id": "936", "ignoreme": "0", "name": "DOGONews", "new_description": "Short format articles for kids on current events, science, sports and more.", "newname": "DOGONews", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-dogonews-com"}, "937": {"component": {"domainsurls": 4}, "example_url": "dreambox.com", "filename": "sphirewall-application-dreambox", "id": "937", "ignoreme": "0", "name": "Dreambox", "new_description": "", "newname": "Dreambox", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-dreambox"}, "938": {"component": {"domainsurls": 1}, "example_url": "drfrostmaths.com", "filename": "sphirewall-application-drfrostmaths", "id": "938", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-drfrostmaths"}, "939": {"component": {"domainsurls": 1}, "example_url": "duolingo.com", "filename": "sphirewall-application-duolingo-com", "id": "939", "ignoreme": "0", "name": "Duolingo", "new_description": "", "newname": "Duolingo", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-duolingo-com"}, "940": {"component": {"domainsurls": 4}, "example_url": "eadms.com", "filename": "sphirewall-application-eadms", "id": "940", "ignoreme": "0", "name": "EADMS Online Testing", "new_description": "", "newname": "EADMS Online Testing", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-eadms"}, "941": {"component": {"domainsurls": 1}, "example_url": "econlowdown.org", "filename": "sphirewall-application-econlowdown", "id": "941", "ignoreme": "0", "name": "Econlowdown", "new_description": "", "newname": "Econlowdown", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-econlowdown"}, "942": {"component": {"domainsurls": 1}, "example_url": "edia.app", "filename": "sphirewall-application-edia", "id": "942", "ignoreme": "0", "name": "Edia", "new_description": "", "newname": "Edia", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-edia"}, "943": {"component": {"domainsurls": 1}, "example_url": "edify.org", "filename": "sphirewall-application-edify", "id": "943", "ignoreme": "0", "name": "edify", "new_description": "", "newname": "edify", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-edify"}, "944": {"component": {"domainsurls": 3}, "example_url": "edmodo.com", "filename": "sphirewall-application-edmodo-com", "id": "944", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-edmodo-com"}, "945": {"component": {"domainsurls": 3}, "example_url": "educa.co.nz", "filename": "sphirewall-application-educa", "id": "945", "ignoreme": "0", "name": "<PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-educa"}, "946": {"component": {"regexpurls": 52}, "example_url": "https://www.ena.com/", "filename": "sphirewall-application-ena", "id": "946", "ignoreme": "0", "name": "Education Network America", "new_description": "Education Networks of America (ENA) is a private company providing internet services to public schools and libraries.", "newname": "Education Network America", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-ena"}, "947": {"component": {"domainsurls": 9}, "example_url": "educationperfect.com", "filename": "sphirewall-application-educationperfect", "id": "947", "ignoreme": "0", "name": "Education Perfect", "new_description": "", "newname": "Education Perfect", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-educationperfect"}, "948": {"component": {"domainsurls": 1}, "example_url": "educere.net", "filename": "sphirewall-application-educere", "id": "948", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "This URL is part of the Education signature but they would like it to be on its own so they can just allow it instead of the entire education signature. ", "newname": "<PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-educere"}, "949": {"component": {"domainsurls": 1}, "example_url": "educreations.com", "filename": "sphirewall-application-educreations-com", "id": "949", "ignoreme": "0", "name": "Educreations", "new_description": "", "newname": "Educreations", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-educreations-com"}, "950": {"component": {"domainsurls": 1}, "example_url": "eduhero.net", "filename": "sphirewall-application-eduhero", "id": "950", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-eduhero"}, "951": {"component": {"domainsurls": 4}, "example_url": "edulastic.com", "filename": "sphirewall-application-edulastic", "id": "951", "ignoreme": "0", "name": "edulastic", "new_description": "", "newname": "edulastic", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-edulastic"}, "952": {"component": {"domainsurls": 4}, "example_url": "eduphoria.net", "filename": "sphirewall-application-eduphoria", "id": "952", "ignoreme": "0", "name": "Eduphoria", "new_description": "", "newname": "Eduphoria", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-eduphoria"}, "953": {"component": {"domainsurls": 3}, "example_url": "edutyping.com", "filename": "sphirewall-application-edutyping", "id": "953", "ignoreme": "0", "name": "edutyping", "new_description": "", "newname": "edutyping", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-edutyping"}, "954": {"component": {"domainsurls": 1}, "example_url": "k12els.com", "filename": "sphirewall-application-els", "id": "954", "ignoreme": "0", "name": "ELS", "new_description": "", "newname": "ELS", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-els"}, "955": {"component": {"domainsurls": 1}, "example_url": "encyclopediaofarkansas.net", "filename": "sphirewall-application-encyclopediaofarkansas", "id": "955", "ignoreme": "0", "name": "encyclopedia of arkansas", "new_description": "", "newname": "encyclopedia of arkansas", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-encyclopediaofarkansas"}, "956": {"component": {"domainsurls": 1}, "example_url": "enotes.com", "filename": "sphirewall-application-enotes-com", "id": "956", "ignoreme": "0", "name": "E Notes", "new_description": "", "newname": "E Notes", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-enotes-com"}, "957": {"component": {"domainsurls": 3}, "example_url": "esparklearning.com", "filename": "sphirewall-application-esparklearning", "id": "957", "ignoreme": "0", "name": "eSpark learning", "new_description": "", "newname": "eSpark learning", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-esparklearning"}, "958": {"component": {"domainsurls": 1}, "example_url": "etap.co.nz", "filename": "sphirewall-application-etap", "id": "958", "ignoreme": "0", "name": "Etap", "new_description": "", "newname": "Etap", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-etap"}, "959": {"component": {"domainsurls": 1}, "example_url": "ets.org", "filename": "sphirewall-application-etsorg", "id": "959", "ignoreme": "0", "name": "ETS Org", "new_description": "", "newname": "ETS Org", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-etsorg"}, "960": {"component": {"domainsurls": 1}, "example_url": "etv.org.nz", "filename": "sphirewall-applicaiton-etv", "id": "960", "ignoreme": "0", "name": "eTV", "new_description": "Real time, Real life Learning resources", "newname": "eTV", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-etv"}, "961": {"component": {"domainsurls": 1}, "example_url": "everyculture.com", "filename": "sphirewall-application-everyculture", "id": "961", "ignoreme": "0", "name": "Every Culture", "new_description": "World Culture Encyclopedia: North America, Oceania, South Asia, Europe, East / Southeast Asia, Russia - Eurasia / China, South America, Middle America / Caribbean, and Africa / Middle East.", "newname": "Every Culture", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-everyculture"}, "962": {"component": {"domainsurls": 3}, "example_url": "explaineverything.com", "filename": "sphirewall-application-explaineverything", "id": "962", "ignoreme": "0", "name": "ExplainEverything", "new_description": "Explain Everything is a unique interactive screencasting whiteboard app. Create and share ideas as videos and more with easy-to-use tools.", "newname": "ExplainEverything", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-explaineverything"}, "963": {"component": {"domainsurls": 11}, "example_url": "explorelearning.com", "filename": "sphirewall-application-explorelearning", "id": "963", "ignoreme": "0", "name": "explorelearning", "new_description": "", "newname": "explorelearning", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-explorelearning"}, "964": {"component": {"domainsurls": 1}, "example_url": "ezatest.com", "filename": "sphirewall-application-ezatest", "id": "964", "ignoreme": "0", "name": "EzaTest", "new_description": "", "newname": "EzaTest", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-ezatest"}, "965": {"component": {"domainsurls": 5}, "example_url": "factsmgt.com", "filename": "sphirewall-application-factsmgt", "id": "965", "ignoreme": "0", "name": "FACTS", "new_description": "", "newname": "FACTS", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-factsmgt"}, "966": {"component": {"domainsurls": 5}, "example_url": "fastbridge.org", "filename": "sphirewall-application-fastbridge", "id": "966", "ignoreme": "0", "name": "FastBridge", "new_description": "", "newname": "FastBridge", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-fastbridge"}, "967": {"component": {"domainsurls": 5}, "example_url": "fevtutor.com", "filename": "sphirewall-application-fevtutor", "id": "967", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-fevtutor"}, "968": {"component": {"domainsurls": 3}, "example_url": "firstinmath.com", "filename": "sphirewall-application-firstinmath", "id": "968", "ignoreme": "0", "name": "First in Math", "new_description": "", "newname": "First in Math", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-firstinmath"}, "969": {"component": {"domainsurls": 5}, "example_url": "flocabulary.com", "filename": "sphirewall-application-flocabulary", "id": "969", "ignoreme": "0", "name": "flocabulary", "new_description": "", "newname": "flocabulary", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-flocabulary"}, "970": {"component": {"domainsurls": 3}, "example_url": "focuselearning.co.uk", "filename": "sphirewall-application-focuselearning", "id": "970", "ignoreme": "0", "name": "Focus eLearning", "new_description": "High quality online curriculum resources for Science and Design Technology.", "newname": "Focus eLearning", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-focuselearning"}, "971": {"component": {"domainsurls": 1}, "example_url": "fun4thebrain.com", "filename": "sphirewall-applicaiton-fun4thebrain-com", "id": "971", "ignoreme": "0", "name": "Fun4thebrain", "new_description": "", "newname": "Fun4thebrain", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-fun4thebrain-com"}, "972": {"component": {"domainsurls": 7}, "example_url": "galileo.usg.edu", "filename": "sphirewall-application-galileo", "id": "972", "ignoreme": "0", "name": "Galileo", "new_description": "GALILEO is free to all Georgians via schools, colleges and public libraries.", "newname": "Galileo", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-galileo"}, "973": {"component": {"domainsurls": 5}, "example_url": "gallopade.com", "filename": "sphirewall-application-gallopade", "id": "973", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-gallopade"}, "974": {"component": {"domainsurls": 1}, "example_url": "garbanzo.io", "filename": "sphirewall-application-garbanzo", "id": "974", "ignoreme": "0", "name": "Garbanzo", "new_description": "", "newname": "Garbanzo", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-garbanzo"}, "975": {"component": {"domainsurls": 1}, "example_url": "gcsepod.com", "filename": "sphirewall-application-gcsepod", "id": "975", "ignoreme": "0", "name": "GCSEPod", "new_description": "", "newname": "GCSEPod", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-gcsepod"}, "976": {"component": {"domainsurls": 4}, "example_url": "getmoremath.com", "filename": "sphirewall-application-getmoremath", "id": "976", "ignoreme": "0", "name": "Get More Math", "new_description": "", "newname": "Get More Math", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-getmoremath"}, "977": {"component": {"domainsurls": 1}, "example_url": "www.glosor.eu", "filename": "sphirewall-application-glosor", "id": "977", "ignoreme": "0", "name": "Glosor", "new_description": "Vocabulary practice site - requested by Vallentuna.se", "newname": "Glosor", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-glosor"}, "978": {"component": {"domainsurls": 3}, "example_url": "gmetrix.net", "filename": "sphirewall-application-gmetrix", "id": "978", "ignoreme": "0", "name": "gmetrix", "new_description": "", "newname": "gmetrix", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-gmetrix"}, "979": {"component": {"domainsurls": 3}, "example_url": "goedustar.com", "filename": "sphirewall-application-goedustar", "id": "979", "ignoreme": "0", "name": "goedustar", "new_description": "", "newname": "goedustar", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-goedustar"}, "980": {"component": {"domainsurls": 6}, "example_url": "grammarly.com", "filename": "sphirewall-application-grammarly", "id": "980", "ignoreme": "0", "name": "Grammarly", "new_description": "", "newname": "Grammarly", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-grammarly"}, "981": {"component": {"domainsurls": 3}, "example_url": "gseonline.us", "filename": "sphirewall-application-gseonline", "id": "981", "ignoreme": "0", "name": "gseonline", "new_description": "", "newname": "gseonline", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-gseonline"}, "982": {"component": {"domainsurls": 1}, "example_url": "handspeak.com", "filename": "sphirewall-application-handspeak", "id": "982", "ignoreme": "0", "name": "Hand Speak", "new_description": "", "newname": "Hand Speak", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-handspeak"}, "983": {"component": {"domainsurls": 1}, "example_url": "happynumbers.com", "filename": "sphirewall-application-happynumbers", "id": "983", "ignoreme": "0", "name": "Happy Numbers", "new_description": "", "newname": "Happy Numbers", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-happynumbers"}, "984": {"component": {"domainsurls": 1}, "example_url": "heggerty.org", "filename": "sphirewall-application-heggerty", "id": "984", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "Heggerty Phonemic Awareness is a research-based 35-week curriculum of daily phonemic and phonological awareness lesson plans", "newname": "<PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-heggerty"}, "985": {"component": {"domainsurls": 3}, "example_url": "heritage.sa.edu.au", "filename": "sphirewall-application-heritage", "id": "985", "ignoreme": "0", "name": "Heritage College", "new_description": "", "newname": "Heritage College", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-heritage"}, "986": {"component": {"domainsurls": 1}, "example_url": "hinative.com", "filename": "sphirewall-application-hinative", "id": "986", "ignoreme": "0", "name": "HiNative", "new_description": "HiNative is a global Q&A platform for language learners. Ask and answer questions about language and culture with native speakers around the world.", "newname": "HiNative", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-hinative"}, "987": {"component": {"domainsurls": 3}, "example_url": "hoodamath.com", "filename": "sphirewall-application-hoodamath", "id": "987", "ignoreme": "0", "name": "<PERSON><PERSON><PERSON>", "new_description": "", "newname": "<PERSON><PERSON><PERSON>", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-hoodamath"}, "988": {"component": {"domainsurls": 1}, "example_url": "hotmaths.com.au", "filename": "sphirewall-application-hotmaths-com-au", "id": "988", "ignoreme": "0", "name": "Hot Maths", "new_description": "", "newname": "Hot Maths", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-hotmaths-com-au"}, "989": {"component": {"domainsurls": 4}, "example_url": "https://www.howstuffworks.com/", "filename": "sphirewall-application-howstuffworks", "id": "989", "ignoreme": "0", "name": "How Stuff Works", "new_description": " HowStuffWorks explains thousands of topics, ranging from the flu to black holes to conspiracy theories, with video and illustrations so you can learn how everything works.", "newname": "How Stuff Works", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-howstuffworks"}, "990": {"component": {"domainsurls": 13}, "example_url": "e-ieppro.com", "filename": "sphirewall-application-ieppro", "id": "990", "ignoreme": "0", "name": "IEP PRO", "new_description": "Web based special education", "newname": "IEP PRO", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-ieppro"}, "991": {"component": {"domainsurls": 1}, "example_url": "ilclassroom.com", "filename": "sphirewall-application-ilclassroom", "id": "991", "ignoreme": "0", "name": "IL Classroom", "new_description": "This is an educational website ", "newname": "IL Classroom", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-ilclassroom"}, "992": {"component": {"domainsurls": 3}, "example_url": "infinitecampus.com", "filename": "sphirewall-application-infinitecampus", "id": "992", "ignoreme": "0", "name": "Infinite Campus", "new_description": "", "newname": "Infinite Campus", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-infinitecampus"}, "993": {"component": {"domainsurls": 1}, "example_url": "instructables.com", "filename": "sphirewall-application-instructables", "id": "993", "ignoreme": "0", "name": "instructables", "new_description": "We make it easy to learn how to make anything, one step at a time. From the stovetop to the workshop, you are sure to be inspired by the awesome projects that are shared everyday.", "newname": "instructables", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-instructables"}, "994": {"component": {"domainsurls": 1}, "example_url": "beinternetawesome.withgoogle.com", "filename": "sphirewall-application-interland", "id": "994", "ignoreme": "0", "name": "Interland", "new_description": "", "newname": "Interland", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-interland"}, "995": {"component": {"domainsurls": 21}, "example_url": "istation.com", "filename": "sphirewall-applicaiton-istation", "id": "995", "ignoreme": "0", "name": "istation", "new_description": "", "newname": "istation", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-istation"}, "996": {"component": {"domainsurls": 17}, "example_url": "ixl.com", "filename": "sphirewall-application-ixl", "id": "996", "ignoreme": "0", "name": "IXL", "new_description": "IXL is the Web's most comprehensive K-12 practice site. Widely used by schools and families, IXL provides unlimited practice in more than...", "newname": "IXL", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-ixl"}, "997": {"component": {"domainsurls": 1}, "example_url": "jake4maths.com", "filename": "sphirewall-applicaiton-jake4maths-com", "id": "997", "ignoreme": "0", "name": "Jake4maths", "new_description": "NZGrapher is a free web based graphing tool. NZ Grapher was designed for New Zealand Schools by a New Zealand Teacher.", "newname": "Jake4maths", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-applicaiton-jake4maths-com"}, "998": {"component": {"domainsurls": 1}, "example_url": "javalab.org", "filename": "sphirewall-application-javalab", "id": "998", "ignoreme": "0", "name": "JavaLab", "new_description": "Free interactive science simulation written by JavaScript\nSimulates various natural phenomena\nIncludes about Physics, Chemistry, Earth, Astronomy, Biology, Measurment, Mathmatics...", "newname": "JavaLab", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-javalab"}, "999": {"component": {"domainsurls": 1}, "example_url": "k12insight.com", "filename": "sphirewall-application-k12insight", "id": "999", "ignoreme": "0", "name": "K12 Insight", "new_description": "", "newname": "K12 Insight", "parent": "882", "test_url": "test.mysmoothwall.net/sphirewall-application-k12insight"}}
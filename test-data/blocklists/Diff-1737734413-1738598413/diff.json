{"weightedphrases": [{"op": "add", "path": "/concerts", "value": {"catsAndScores": [], "children": [{"catsAndScores": {"category": 190, "score": "15"}, "phrases": ["tickets"]}]}}, {"op": "add", "path": "/line-up", "value": {"catsAndScores": [], "children": [{"catsAndScores": {"category": 190, "score": "15"}, "phrases": ["festival"]}]}}, {"op": "add", "path": "/mailing list", "value": {"catsAndScores": [{"category": 190, "score": "15"}], "children": []}}, {"op": "add", "path": "/event space", "value": {"catsAndScores": [{"category": 190, "score": "10"}], "children": []}}, {"op": "add", "path": "/double header", "value": {"catsAndScores": [{"category": 190, "score": "10"}], "children": []}}, {"op": "add", "path": "/name=\"comicpress\"", "value": {"catsAndScores": [{"category": 474, "score": "50"}], "children": []}}, {"op": "add", "path": "/tickets updates", "value": {"catsAndScores": [], "children": [{"catsAndScores": {"category": 190, "score": "20"}, "phrases": ["festival"]}]}}, {"op": "add", "path": "/link to this comic", "value": {"catsAndScores": [{"category": 474, "score": "60"}], "children": []}}, {"op": "add", "path": "/admission fee ", "value": {"catsAndScores": [{"category": 190, "score": "15"}], "children": []}}, {"op": "add", "path": "/event marketing platform", "value": {"catsAndScores": [{"category": 190, "score": "20"}], "children": []}}, {"op": "add", "path": "/special guest", "value": {"catsAndScores": [], "children": [{"catsAndScores": {"category": 190, "score": "15"}, "phrases": [" event "]}]}}, {"op": "add", "path": "/wedding fair", "value": {"catsAndScores": [{"category": 190, "score": "10"}], "children": []}}, {"op": "add", "path": "/first", "value": {"catsAndScores": [], "children": [{"catsAndScores": {"category": 474, "score": "20"}, "phrases": ["random", "archive", " comic "]}]}}, {"op": "add", "path": "/comic series", "value": {"catsAndScores": [{"category": 474, "score": "15"}], "children": []}}, {"op": "add", "path": "/~1comics~1", "value": {"catsAndScores": [{"category": 474, "score": "15"}], "children": []}}, {"op": "add", "path": "/@type\": \"comicstory\"", "value": {"catsAndScores": [{"category": 474, "score": "100"}], "children": []}}, {"op": "add", "path": "/&lt;", "value": {"catsAndScores": [], "children": [{"catsAndScores": {"category": 474, "score": "20"}, "phrases": ["&gt;", "archive", "comic"]}]}}, {"op": "add", "path": "/weekly webcomic", "value": {"catsAndScores": [{"category": 474, "score": "20"}], "children": []}}, {"op": "add", "path": "/comics publisher", "value": {"catsAndScores": [{"category": 474, "score": "25"}], "children": []}}, {"op": "add", "path": "/tickets onsale", "value": {"catsAndScores": [{"category": 190, "score": "10"}], "children": []}}, {"op": "add", "path": "/headline ", "value": {"catsAndScores": [], "children": [{"catsAndScores": {"category": 190, "score": "15"}, "phrases": ["festival"]}]}}, {"op": "add", "path": "/animated comic", "value": {"catsAndScores": [{"category": 474, "score": "20"}], "children": []}}, {"op": "add", "path": "/multi-day pass", "value": {"catsAndScores": [], "children": [{"catsAndScores": {"category": 190, "score": "15"}, "phrases": ["festival"]}]}}, {"op": "add", "path": "/convention", "value": {"catsAndScores": [], "children": [{"catsAndScores": {"category": 190, "score": "15"}, "phrases": [" event "]}, {"catsAndScores": {"category": 190, "score": "15"}, "phrases": ["games"]}]}}, {"op": "add", "path": "/reading this comic", "value": {"catsAndScores": [{"category": 474, "score": "30"}], "children": []}}, {"op": "add", "path": "/event info", "value": {"catsAndScores": [{"category": 190, "score": "15"}], "children": []}}, {"op": "add", "path": "/tickets on sale", "value": {"catsAndScores": [{"category": 190, "score": "10"}], "children": []}}, {"op": "add", "path": "/webcomic", "value": {"catsAndScores": [{"category": 474, "score": "15"}], "children": []}}, {"op": "add", "path": "/one-day pass", "value": {"catsAndScores": [], "children": [{"catsAndScores": {"category": 190, "score": "15"}, "phrases": ["festival"]}]}}, {"op": "add", "path": "/adventure comic", "value": {"catsAndScores": [{"category": 474, "score": "20"}], "children": []}}, {"op": "add", "path": "/seating map", "value": {"catsAndScores": [], "children": [{"catsAndScores": {"category": 190, "score": "15"}, "phrases": ["event "]}]}}, {"op": "add", "path": "/weekly comic", "value": {"catsAndScores": [{"category": 474, "score": "20"}], "children": []}}, {"op": "add", "path": "/event ticketing", "value": {"catsAndScores": [{"category": 190, "score": "15"}], "children": []}}, {"op": "add", "path": "/support this comic", "value": {"catsAndScores": [{"category": 474, "score": "30"}], "children": []}}, {"op": "move", "from": "/behead", "path": "/ behead"}, {"op": "add", "path": "/hospitality tickets", "value": {"catsAndScores": [{"category": 190, "score": "15"}], "children": []}}, {"op": "add", "path": "/korean comic", "value": {"catsAndScores": [{"category": 474, "score": "20"}], "children": []}}, {"op": "add", "path": "/reserved seating", "value": {"catsAndScores": [{"category": 190, "score": "10"}], "children": []}}, {"op": "add", "path": "/comics i enjoy", "value": {"catsAndScores": [{"category": 474, "score": "30"}], "children": []}}, {"op": "add", "path": "/support the comic", "value": {"catsAndScores": [{"category": 474, "score": "40"}], "children": []}}, {"op": "add", "path": "/japanese manga", "value": {"catsAndScores": [{"category": 474, "score": "25"}], "children": []}}, {"op": "add", "path": "/newest comic", "value": {"catsAndScores": [{"category": 474, "score": "20"}], "children": []}}, {"op": "add", "path": "/lineup news", "value": {"catsAndScores": [{"category": 190, "score": "15"}], "children": []}}, {"op": "add", "path": "/oel comics", "value": {"catsAndScores": [{"category": 474, "score": "30"}], "children": []}}, {"op": "add", "path": "/ comic ", "value": {"catsAndScores": [{"category": 474, "score": "5"}], "children": [{"catsAndScores": {"category": 474, "score": "20"}, "phrases": ["comments", "archive"]}]}}, {"op": "add", "path": "/seating plan", "value": {"catsAndScores": [], "children": [{"catsAndScores": {"category": 190, "score": "15"}, "phrases": ["event "]}]}}, {"op": "add", "path": "/topwebcomics", "value": {"catsAndScores": [{"category": 474, "score": "30"}], "children": []}}, {"op": "add", "path": "/weekly strip", "value": {"catsAndScores": [{"category": 474, "score": "20"}], "children": []}}, {"op": "add", "path": "/seating options", "value": {"catsAndScores": [], "children": [{"catsAndScores": {"category": 190, "score": "15"}, "phrases": ["event "]}]}}, {"op": "add", "path": "/comic", "value": {"catsAndScores": [], "children": [{"catsAndScores": {"category": 474, "score": "25"}, "phrases": ["updates every"]}]}}, {"op": "add", "path": "/funny comic", "value": {"catsAndScores": [{"category": 474, "score": "20"}], "children": []}}, {"op": "add", "path": "/triple bill", "value": {"catsAndScores": [{"category": 190, "score": "10"}], "children": []}}, {"op": "add", "path": "/comic strip", "value": {"catsAndScores": [{"category": 474, "score": "20"}], "children": []}}, {"op": "add", "path": "/ all comics", "value": {"catsAndScores": [{"category": 474, "score": "20"}], "children": []}}, {"op": "add", "path": "/~1comic~1", "value": {"catsAndScores": [{"category": 474, "score": "15"}], "children": []}}, {"op": "add", "path": "/ early bird tickets", "value": {"catsAndScores": [{"category": 190, "score": "15"}], "children": []}}, {"op": "add", "path": "/graphic novel publisher", "value": {"catsAndScores": [{"category": 474, "score": "30"}], "children": []}}, {"op": "add", "path": "/general admission", "value": {"catsAndScores": [{"category": 190, "score": "15"}], "children": []}}, {"op": "add", "path": "/review anonymous", "value": {"catsAndScores": [{"category": 120, "score": "-10"}], "children": []}}, {"op": "add", "path": "/ expo ", "value": {"catsAndScores": [], "children": [{"catsAndScores": {"category": 190, "score": "15"}, "phrases": ["games"]}]}}, {"op": "add", "path": "/ticketing", "value": {"catsAndScores": [], "children": [{"catsAndScores": {"category": 190, "score": "15"}, "phrases": [" events"]}]}}, {"op": "replace", "path": "/ news /children/12/phrases/2", "value": "sport"}, {"op": "replace", "path": "/ news /children/12/catsAndScores/category", "value": 190}, {"op": "replace", "path": "/ news /children/12/catsAndScores/score", "value": "-10"}, {"op": "replace", "path": "/ news /children/13/catsAndScores/category", "value": 512}, {"op": "replace", "path": "/ news /children/13/catsAndScores/score", "value": "-30"}, {"op": "replace", "path": "/ news /children/14/catsAndScores/category", "value": 104}, {"op": "replace", "path": "/ news /children/14/catsAndScores/score", "value": "-15"}, {"op": "add", "path": "/ news /children/15", "value": {"catsAndScores": {"category": 55, "score": "40"}, "phrases": ["business", "science", "sports", "world"]}}, {"op": "add", "path": "/comics/children/1", "value": {"catsAndScores": {"category": 474, "score": "20"}, "phrases": ["genre", "rating"]}}, {"op": "add", "path": "/comics/children/2", "value": {"catsAndScores": {"category": 474, "score": "30"}, "phrases": ["free to read"]}}, {"op": "add", "path": "/exhibition/children/1", "value": {"catsAndScores": {"category": 190, "score": "15"}, "phrases": [" event "]}}, {"op": "replace", "path": "/ bypass /catsAndScores/0/score", "value": "10"}, {"op": "add", "path": "/graphic novel/children/0", "value": {"catsAndScores": {"category": 474, "score": "30"}, "phrases": ["free to read"]}}, {"op": "add", "path": "/archive/children/0", "value": {"catsAndScores": {"category": 474, "score": "30"}, "phrases": ["comic", "characters"]}}, {"op": "add", "path": "/archive/children/1", "value": {"catsAndScores": {"category": 474, "score": "20"}, "phrases": ["comic", "previous", "next"]}}, {"op": "add", "path": "/festival/children/7", "value": {"catsAndScores": {"category": 190, "score": "15"}, "phrases": ["fairs"]}}, {"op": "add", "path": "/festival/children/8", "value": {"catsAndScores": {"category": 190, "score": "-10"}, "phrases": ["music", "byline"]}}, {"op": "add", "path": "/festival/children/9", "value": {"catsAndScores": {"category": 190, "score": "-10"}, "phrases": ["arts", "breaking news"]}}, {"op": "add", "path": "/festival/children/10", "value": {"catsAndScores": {"category": 190, "score": "-10"}, "phrases": ["comedy", "breaking news"]}}, {"op": "add", "path": "/festival/children/11", "value": {"catsAndScores": {"category": 190, "score": "-10"}, "phrases": ["arts", "sport headlines"]}}, {"op": "add", "path": "/festival/children/12", "value": {"catsAndScores": {"category": 190, "score": "-10"}, "phrases": ["comedy", "byline"]}}, {"op": "add", "path": "/festival/children/13", "value": {"catsAndScores": {"category": 190, "score": "-10"}, "phrases": ["sport headlines"]}}, {"op": "add", "path": "/festival/children/14", "value": {"catsAndScores": {"category": 190, "score": "-10"}, "phrases": ["arts", "byline"]}}, {"op": "add", "path": "/festival/children/15", "value": {"catsAndScores": {"category": 190, "score": "-10"}, "phrases": ["music", "sport headlines"]}}, {"op": "add", "path": "/festival/children/16", "value": {"catsAndScores": {"category": 190, "score": "-10"}, "phrases": ["comedy", "sport headlines"]}}, {"op": "add", "path": "/festival/children/17", "value": {"catsAndScores": {"category": 190, "score": "-10"}, "phrases": ["byline"]}}, {"op": "add", "path": "/festival/children/18", "value": {"catsAndScores": {"category": 190, "score": "-10"}, "phrases": ["headline ", "breaking news"]}}, {"op": "add", "path": "/festival/children/19", "value": {"catsAndScores": {"category": 190, "score": "-10"}, "phrases": ["music", "breaking news"]}}], "searchterms": [{"op": "add", "path": "/ sessy woman ", "value": {"catsAndScores": [{"category": 11, "score": 100}], "children": []}}, {"op": "add", "path": "/ sessy women ", "value": {"catsAndScores": [{"category": 11, "score": 100}], "children": []}}], "category_data": [{"op": "replace", "path": "/1043/component/videoids", "value": 680}, {"op": "replace", "path": "/588/component/domainsurls", "value": 118}, {"op": "replace", "path": "/7/component/domainsurls", "value": 145389}, {"op": "replace", "path": "/34/component/domainsurls", "value": 168349}, {"op": "replace", "path": "/758/component/domainsurls", "value": 20085}, {"op": "replace", "path": "/751/component/domainsurls", "value": 30067}, {"op": "replace", "path": "/654/component/domainsurls", "value": 7854}, {"op": "replace", "path": "/187/component/domainsurls", "value": 8694}, {"op": "replace", "path": "/397/component/domainsurls", "value": 20991}, {"op": "replace", "path": "/223/component/domainsurls", "value": 1383}, {"op": "replace", "path": "/214/component/domainsurls", "value": 959}, {"op": "replace", "path": "/1237/component/domainsurls", "value": 35}, {"op": "replace", "path": "/435/component/domainsurls", "value": 7481}, {"op": "replace", "path": "/235/component/domainsurls", "value": 714992}, {"op": "replace", "path": "/227/component/domainsurls", "value": 776}, {"op": "replace", "path": "/433/component/domainsurls", "value": 2602}, {"op": "replace", "path": "/21/component/domainsurls", "value": 12638}, {"op": "replace", "path": "/512/component/domainsurls", "value": 15968}, {"op": "replace", "path": "/747/component/domainsurls", "value": 21702}, {"op": "replace", "path": "/213/component/domainsurls", "value": 71459}, {"op": "replace", "path": "/207/component/domainsurls", "value": 15141}, {"op": "replace", "path": "/52/component/domainsurls", "value": 8469}, {"op": "replace", "path": "/104/component/domainsurls", "value": 12114}, {"op": "replace", "path": "/549/component/domainsurls", "value": 49190}, {"op": "replace", "path": "/753/component/domainsurls", "value": 247758}, {"op": "replace", "path": "/43/component/domainsurls", "value": 135198}, {"op": "replace", "path": "/230/component/domainsurls", "value": 500}, {"op": "replace", "path": "/419/component/domainsurls", "value": 627}, {"op": "replace", "path": "/185/component/domainsurls", "value": 12651}, {"op": "replace", "path": "/2/component/domainsurls", "value": 20665}, {"op": "replace", "path": "/200/component/domainsurls", "value": 20084}, {"op": "replace", "path": "/474/component/weightedphrases", "value": 250}, {"op": "replace", "path": "/474/component/domainsurls", "value": 3524}, {"op": "replace", "path": "/1225/component/domainsurls", "value": 130320}, {"op": "replace", "path": "/1/component/domainsurls", "value": 78267}, {"op": "replace", "path": "/199/component/domainsurls", "value": 18184}, {"op": "replace", "path": "/1213/component/domainsurls", "value": 68}, {"op": "replace", "path": "/53/component/domainsurls", "value": 130798}, {"op": "replace", "path": "/11/component/domainsurls", "value": 532202}, {"op": "replace", "path": "/11/component/searchterms", "value": 109}, {"op": "replace", "path": "/1518/component/domainsurls", "value": 163}, {"op": "replace", "path": "/184/component/domainsurls", "value": 12354}, {"op": "replace", "path": "/438/component/domainsurls", "value": 59213}, {"op": "replace", "path": "/1352/component/domainsurls", "value": 1103}, {"op": "replace", "path": "/1351/component/domainsurls", "value": 148423}, {"op": "replace", "path": "/752/component/domainsurls", "value": 48245}, {"op": "replace", "path": "/242/component/domainsurls", "value": 154187}, {"op": "replace", "path": "/191/component/domainsurls", "value": 37537}, {"op": "replace", "path": "/55/component/domainsurls", "value": 26930}, {"op": "replace", "path": "/182/component/domainsurls", "value": 6942}, {"op": "replace", "path": "/28/component/domainsurls", "value": 14037}, {"op": "replace", "path": "/39/component/domainsurls", "value": 344449}, {"op": "replace", "path": "/756/component/domainsurls", "value": 78268}, {"op": "replace", "path": "/511/component/domainsurls", "value": 1450}, {"op": "replace", "path": "/56/component/domainsurls", "value": 9383}, {"op": "replace", "path": "/606/component/domainsurls", "value": 1196}, {"op": "replace", "path": "/231/component/domainsurls", "value": 628}, {"op": "replace", "path": "/192/component/domainsurls", "value": 3207}, {"op": "replace", "path": "/114/component/domainsurls", "value": 27716}, {"op": "replace", "path": "/1507/component/domainsurls", "value": 10273}, {"op": "replace", "path": "/513/component/domainsurls", "value": 2955}, {"op": "replace", "path": "/24/component/domainsurls", "value": 27717}, {"op": "replace", "path": "/1251/component/domainsurls", "value": 22}, {"op": "replace", "path": "/202/component/domainsurls", "value": 27981}, {"op": "replace", "path": "/205/component/domainsurls", "value": 10273}, {"op": "replace", "path": "/754/component/domainsurls", "value": 1802}, {"op": "replace", "path": "/190/component/weightedphrases", "value": 82}, {"op": "replace", "path": "/190/component/domainsurls", "value": 7211}, {"op": "replace", "path": "/13/component/domainsurls", "value": 18179}, {"op": "replace", "path": "/30/component/domainsurls", "value": 973}, {"op": "replace", "path": "/1238/component/domainsurls", "value": 9841}, {"op": "replace", "path": "/939/component/domainsurls", "value": 2}, {"op": "replace", "path": "/120/component/regexpurls", "value": 45}, {"op": "replace", "path": "/120/component/weightedphrases", "value": 1352}, {"op": "replace", "path": "/120/component/domainsurls", "value": 81107}, {"op": "replace", "path": "/1231/component/domainsurls", "value": 1661}], "loglevelrules": []}
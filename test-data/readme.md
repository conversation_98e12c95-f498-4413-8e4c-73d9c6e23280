# Test data

This folder contains data used for unit tests. It contains the following folders:

## blocklists

This folder contains genuine Smoothwall blocklists generated by <PERSON>man, along with diffs to patch
between them. The purpose is to verify that the extension code correctly loads and patches blocklist
files. To verify patching, a test should load an old blocklist and apply a related diff. It should
then compare the result to the corresponding new blocklist. They should functionally match, even if
the precise representation isn't identical.

The folder names correspond to those generated by Blockman. Folders named `Blocklist-N` contain a
full blocklist, when `N` is the Unix timestamp in seconds (aka epoch) of when the blocklist was
generated. Folders named `Diff-M-N` contain a diff to patch from blocklist epoch `M` to `N`.

Notable:

- `Diff-1737734413-1738598413` = Contains unusually large Unified Diff data.

## invalid-blocklists

This folder contains blocklist data which is deliberately not valid. It should trigger failures on
loading or processing.

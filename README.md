# Cloud Filter Chromium extension

This repository contains the source code of Smoothwall's Cloud Filter extension for Chromium
browsers. It provides web-filtering functionality using a combination of known URLs and heuristic
analysis.

# Supported platforms

The extension can run on the following platforms/browsers:

- Windows:
  - Chrome
  - Edge
- macOS
  - Chrome
- ChromeOS (aka Chromebooks):
  - Chrome

# Quick-start

If you've worked on this extension before, then here's a quick reminder of how to build and run it:

```bash
npm ci
npm run build:dev
# Windows: run the provisioning from an elevated command prompt
npm run provision -- --serial SERIAL [--tenant TENANT] --defaultUser USER
# macOS / Linux: run the provisioning under sudo
sudo npm run provision -- --serial SERIAL [--tenant TENANT] --defaultUser USER
npm start
```

If you're starting from scratch, or the above fails for any reason, then follow the in-depth
instructions below.

# Build instructions

## 1. Prerequisites

Required software:

- **git**
- **[NodeJS](https://nodejs.org/)** v16 or later
  - Ensure npm is installed as part of this
- At least one up-to-date browser:
  - **[Chrome](https://www.google.co.uk/chrome)**
  - **[Edge](https://www.microsoft.com/edge)**
- **[gcloud CLI](https://cloud.google.com/sdk/docs/install)**

Recommended software:

- **[Visual Studio Code](https://code.visualstudio.com/)**
  - [eslint extension](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint)
  - [prettier extension](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)

## 2. Check out the source code

If you haven't already done so, check out the source code using git,

For example, on the command line, navigate to the folder where you want to keep your source code,
and run the following command:

```bash
<NAME_EMAIL>:B-Communications/sw-cldflt-browser-extension.git
```

## 3. Authenticate our package registry

This project depends on libraries from our private NPM registry hosted in GCP. You will need to
authenticate with it before you can install and build the code.

If any of the below commands fail due to permission issues then you may need to ask IT to give you
access to read packages from the following registry: `https://us-npm.pkg.dev/fzo-build/javascript/`

First, you will need to download and install the gcloud CLI (Command Line Interface), if you
haven't already. You can get instructions from here:

- https://cloud.google.com/sdk/docs/install

Next, open a terminal window and run the below command to authenticate with GCP. You should be able
to run this command from anywhere:

```shell
gcloud auth application-default login
```

That should open a browser window to complete the authentication process. If not, follow the link
shown in the console. Ensure you log into GCP using your Qoria email address.

Finally, navigate to the folder containing this repository. Run the following command from there to
authenticate with the npm registry:

```shell
npm run artifactregistry-login
```

You may be prompted to install additional components. Allow this if so.

You should now be able to install dependencies from our private registry.

If you have problems you may need to install Google Artifact Registry Auth.

## 4. Install dependencies

Run the following command to install the dependencies required by this project:

```bash
npm ci
```

This will remove any existing dependencies you already had installed, meaning it needs to
re-download all of them. However, it ensures all the versions will exactly match what's specified in
the existing lock file.

If you need to add or update dependencies later, you can use `npm install` instead.

## 5. Build the extension

For development, build the extension by running this command:

```shell
npm run build:dev
```

For testing purposes, use this command instead:

```shell
npm run build:qa
```

**Other build commands**

This table summarises all the different build commands:

| Command         | Source maps | Code minification | Manifest | Telemetry destination      | Notes         |
| --------------- | ----------- | ----------------- | -------- | -------------------------- | ------------- |
| `build:dev`     | Enabled     | Disabled          | v3       | sw-global-dev-uks-1-appins | Alias for mv3 |
| `build:dev-mv3` | Enabled     | Disabled          | v3       | sw-global-dev-uks-1-appins |               |
| `build:dev-mv2` | Enabled     | Disabled          | v2       | sw-global-dev-uks-1-appins |               |
| `build:qa`      | Disabled    | Enabled           | v3       | sw-global-qa-uks-1-appins  | Alias for mv3 |
| `build:qa-mv3`  | Disabled    | Enabled           | v3       | sw-global-qa-uks-1-appins  |               |
| `build:qa-mv2`  | Disabled    | Enabled           | v2       | sw-global-qa-uks-1-appins  |               |
| `build:prd`     | Disabled    | Enabled           | v3       | sw-global-prd-uks-1-appins | Alias for mv3 |
| `build:prd-mv3` | Disabled    | Enabled           | v3       | sw-global-prd-uks-1-appins |               |
| `build:prd-mv2` | Disabled    | Enabled           | v2       | sw-global-prd-uks-1-appins |               |

**Output**

In all cases, the build output goes into the `dist` sub-folder. As such, you cannot have multiple
builds at the same time as they will overwrite each other.

**Build types**

The build type (dev, qa, or prd) only affects how the extension is built, and where telemetry
is sent. It doesn't affect which back-end environment the extension communicates with. For example,
you can use a QA serial with a dev build of the extension.

**Manifest version**

Unless otherwise specified, the build commands will use the manifest v3 (mv3) standard. This is the
standard which the extension is currently coded in, and will eventually be the standard which most
customers use.

If you use a manifest v2 (mv2) build command, then it will inject a shim which allows the mv3 code
to run in an mv2 environment. This is important for supporting older Chromebooks long-term. There
are a few places in the code where mv3 features cannot be shimmed, such as offscreen documents. In
these cases, there are separate code paths for each version.

## 6. Provision the extension

Before the extension will work, it needs to be provisioned. This is how it knows which customer
account etc to use. Refer to the following page for some existing end-to-end test accounts. It's
usually advisable to use QA accounts if possible when working on the extension:

- https://familyzone.atlassian.net/wiki/spaces/CWF/pages/*************/E2E+Test+Accounts

For a given test customer, you will need the following information:

- **Serial** -- use the one starting `UNCL`
- **Tenant ID** -- only applicable for multi-tenanted customers
- **User** (aka Chromebook account) -- it should be an email address ending in "gedu.demo.smoothwall-dev.com"

The extension can operate in two modes on Windows/macOS. They are "standalone" and "native". In
standalone mode, the extension is running on its own, getting user info from the browser. This is
similar to how it runs on a Chromebook. In native mode, it communicates with a native daemon to get
user info from the OS.

To provision the extension in standalone mode, run the following command with admin/root/sudo
privileges (i.e. in an admin command prompt on Windows, or using `sudo` on other platforms):

```
npm run provision -- --serial SERIAL --tenant TENANT --defaultUser USER
```

Omit the `--tenant` option for untenanted customers.

To provision the extension in native mode, add `--mode native` to the command, e.g.:

```
npm run provision -- --serial SERIAL --tenant TENANT --mode native
```

**Note:** To run in native mode, you will also need to download and install the native client. See
the "Unified Client" section on the following download pages:

- DEV: https://software-dev.smoothwall.cloud/
- QA: https://software-qa.smoothwall.cloud/
- PRD: https://software.smoothwall.com/

The provisioning info will take effect next time you start the browser with the extension loaded.
Alternatively, if the browser is already running, then you can manually reload policies from the
policies page (`chrome://policy` or `edge://policy`), then restart the extension.

**Note:** The browser determines the extension ID based on the path of the build folder, and this
also affects how the provisioning info is stored. If you move your git checkout or build folder,
then you will need to run the provisioning command again.

For more information, including how to specify provisioning info in a local file, refer to the
provisioning command help option:

```
npm run provision -- --help
```

When you have finished working on the extension, you can remove the provisioning info by running the
deprovisioning command (as admin/root/sudo):

```
npm run deprovision
```

## 7. Load and run the extension

This section outlines how to load an extension which you have built locally. This process is called
"load unpacked", or sometimes "side-loading".

Customers should **not** follow this process. Instead, they should install an official release which
has been published to the relevant browser store. For details on this, see the section entitled
_"How to install a published extension"_ later in this document.

### 7.1. How to load on Windows/macOS (automatic)

On Windows/macOS, run this command to load your local build of the extension into the browser:

```bash
npm start
```

It will start a sandboxed instance of the browser with the extension loaded automatically on
start-up. It won't interfere with other browser instances or data on the system. It also whitelists
the extension so that it can use web request blocking.

By default, it will try to find and run Chrome, but will fall-back on Edge.

**Chrome for Testing:** Starting from Chrome v137 (May 27, 2025), the `--load-extension` command line flag will be removed from the public Chrome browser for security reasons. To address this, the start script now automatically uses "Chrome for Testing" instead of the regular Chrome browser. Chrome for Testing is a special version of Chrome designed for testing and automation that retains support for the `--load-extension` flag. The script will automatically download and install Chrome for Testing when needed, so no manual setup is required.

The script allows various options. To see a complete list, run this command:

```bash
npm start -- --help
```

Note that the extra `--` is required before you can specify any options. If you leave it out, then
the options will be processed by `npm` instead of by the `start` script.

Below are some common ways of running the script:

```bash
# Launch the Edge browser instead of Chrome.
npm start -- --edge

# Delete the existing browser data before startup. This resets it to a clean environment, as though
#  the browser and extension were installed for the first time.
npm start -- --clean

# Use a custom data directory. This is useful for running multiple separate instances side-by-side.
# Each instance needs a separate data directory.
npm start -- --data-dir ../some/custom/path

# Put the browser data in a temporary directory which is deleted when finished.
npm start -- --temp

# Prevent any other extensions from being loaded.
npm start -- --disable-other-extensions

# Use a custom cache directory for Chrome for Testing installations.
npm start -- --chrome-cache-dir /path/to/custom/cache

# Use a specific version of Chrome for Testing.
npm start -- --chrome-version 136.0.6776.0

# Deploys the extension alongside the content aware extension, useful for easily testing content aware features.
npm start -- --deployContentAware
```

**Chrome for Testing Options:**

The start script includes additional options for managing Chrome for Testing installations:

- `--chrome-cache-dir <path>`: Specify a custom directory where Chrome for Testing will be downloaded and cached. If not specified, a default directory `.chrome-for-testing-cache` in the project root will be used.
- `--chrome-version <version>`: Install and use a specific version of Chrome for Testing. If not specified, the stable version will be automatically downloaded.

These options only affect Chrome usage. Edge browser usage remains unchanged and will continue to use the system-installed Edge browser.

### 7.2. How to load on Windows/macOS (manual)

You are advised to use the automatic method of loading the extension, outlined above. If that's not
possible, or you need more flexibility, then you can follow the manual method described here.

Due to manifest v3 restrictions, the extension must be whitelisted so that it can access the
web-request blocking permission. To whitelist it, you will first need to determine the extension ID.
The only manual way to do that is to load the extension without whitelisting, then load it again
with whitelisting. Refer to the [Chrome developer documentation](https://developer.chrome.com/docs/extensions/mv3/getstarted/)
for more details.

Ensure all instances of Chrome are closed. Run this command to load the extension into a new
instance of Chrome:

```cmd
"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" --load-extension=PATH_TO_DIST_FOLDER
```

Replace `PATH_TO_DIST_FOLDER` with the absolute path of your local build folder (it is the `dist`
sub-folder in the repository checkout). You may also need to adjust the path of the Chrome
executable to suit your system.

Open the browser's Extensions page (`chrome://extensions`) and enable Developer mode. Find the
Smoothwall extension, and copy its extension ID. It will look something like this:
"aafocgclhjpiidgilmakciigbadekmda".

Close the browser, then open it again with this command:

```cmd
"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" --load-extension=PATH_TO_DIST_FOLDER --whitelisted-extension-id=EXTENSION_ID
```

Replace `EXTENSION_ID` with the ID you copied from Chrome. The extension should now be fully
functional.

Note: The extension ID is generated based on the location you loaded the extension from. That means
it shouldn't change unless you move your build folder somewhere else, so you can run the same
command to whitelist the extension again in future. You may find it useful to create a script
containing the command with the extension ID hard-coded in it.

### 7.3. How to load on ChromeOS (ChromeBook)

You will need to make your local build (i.e. the contents of the `dist` folder) available on the
Chromebook device. The simplest way to do this is to copy it onto a USB storage device.

Connect the storage device to the Chromebook, then load the unpacked extension from there using the
GUI. See the [Chrome developer documentation](https://developer.chrome.com/docs/extensions/mv3/getstarted/)
for details.

**WARNING:** It doesn't seem to be possible to whitelist a locally-built extension on ChromeOS. As
such, the extension will not be fully functional. See the section entitled "Web request blocking
permission" later in this document. The extension will only work fully if it has been published on
the Chrome store (or self-hosted) and force-installed on the device.

# Development notes

## Repository layout

Folders:

- `.browser-data` = Contains temporary browser data when running the extension using `npm start`. Please do not commit this folder to source control.
- `.ci` = Contains all of the files and scripts required for CI using cloud build.
- `.github` = Contains some files used by GitHub.
- `.vscode` = Workspace settings for the Visual Studio Code Editor.
- `.webstore` = Output is written to this folder when building the extension for deployment to browser web-stores. Please do not commit this folder to source control.
- `dist` = Build output goes here. This folder should not be committed to git.
- `scripts` = Developer utilities, such as provisioning and loading the extension. These scripts are not part of the extension.
- `src` = Source code for the extension, including Typescript code, manifest file, and HTML files/assets.

Various configuration files are in the root folder, including:

- `.editorconfig` = Configures some formatting guidelines for editors/IDEs. This is a developer aid and is not directly enforced.
- `.eslintignore` = Specifies files to be skipped during linting.
- `.eslintrc.json` = Linter configuration.
- `.gitignore` = Specifies files to be ignored by version control.
- `.prettierignore` = Specifies files to be skipped during code formatting.
- `.prettierrc` = Configures the code formatting rules for the toolchain. This is enforced by the build pipeline.
- `jest.config.ts` = Unit test configuration.
- `package-lock.json` = Specifies the exact versions of dependencies to be used by npm.
- `package.json` = Configures the npm packages we depend on, and custom commands launched by npm.
- `tsconfig.json` = Configures the Typescript language.
- `webpack.config.js` = Configures the bundler which combines modules into individual Javascript files.

## Unit tests

This project uses the [Jest](https://jestjs.io) framework to implement unit tests, along with the
[jest-extended](https://www.npmjs.com/package/jest-extended) for extra matchers.

Use this command to run tests once:

```bash
npm test
```

Use this command to automatically rerun tests whenever a file changes:

```bash
npm test -- --watch
```

## Code formatting

This project uses [Prettier](https://prettier.io/) to apply non-functional style and format rules to
the code.

If you're using Visual Studio Code, it's helpful to install the [Prettier plugin](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode).
You can optionally configure it to automatically fix any formatting issues every time you save a
file.

You can manually fix formatting across all files by running this command:

```bash
npm run format:fix
```

Alternatively, if you would prefer to fix any issues yourself, then you can run this command to
validate the formatting without making any changes:

```bash
npm run format:check
```

Please ensure all code is properly formatted before being pushed to the remote. The build server
will reject it otherwise.

## Code safety and static analysis (linting)

To help ensure code safety, this project enforces strict rules in the TypeScript configuration.
These will be enforced whenever the software is built. Additionally, we uses [eslint](https://eslint.org/)
for static analysis.

If you're using Visual Studio Code, then the [eslint plugin](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint)
is strongly recommended. It will automatically analyse your code as you type, immediately
highlighting any problems, and often providing quick-fix options.

You can manually run static analysis and fix some minor issues by running this command:

```bash
npm run lint:fix
```

It won't be able to fix all issues so you may have to fix some manually.

Alternatively, if you would prefer to fix all issues yourself, then you can run the following
command to validate the code without making any changes:

```bash
npm run lint:check
```

Please ensure all code passes static analysis before being pushed to the remote. The build server
will reject it otherwise.

## Build process overview

The main components in our build system are:

- [npm](https://www.npmjs.com/) (part of [NodeJS](https://nodejs.org/))
- [webpack](https://webpack.js.org/)

**npm** manages all of our dependencies, and orchestrates our build commands, and other related
operations. It's configured by the `package.json` file in the root of the repository.

**webpack** orchestrates the build. It transpiles our TypeScript code into portable JavaScript, and
bundles together all of the required modules and other resources. It also generates source-maps to
help with debugging during development. It's configuration is found in `webpack.config.ts`.

## Coding guidelines

Formatting:

- Indentation / tab width is 2 spaces.
- Always uses spaces instead of tabs.
- Do not omit semi-colons.
- Prefer `'single quotation marks'` over `"double quotation marks"`.

Naming:

- Class/interface/type names use `UpperCamelCase`.
- Property/variable/function names use `lowerCamelCase`.
- Private properties/functions are prefixed with an underscore, e.g. `_myProperty`.
- If a module has a default export, then the name of the export should match the filename; e.g. a
  class called "MyAwesomeClass" would be defined in a file called "MyAwesomeClass.ts".
- Unit tests should go in a file called "X.test.ts", where "X" is the base name of the file being
  tested; e.g. unit tests for "MyAwesomeClass.ts" would go in "MyAwesomeClass.test.ts".

Language features:

- Use default exports where appropriate.
- Prefer arrow functions over conventional functions.
- Never use `var`.

Structure:

- Where a property is always initialised to the same literal value (like an empty array or map etc.), initialise it in the declaration rather than the constructor.

Comments:

- Treat comments as part of the code -- keep them up-to-date.
- Please add meaningful doc-comments (conforming to [tsdoc](https://tsdoc.org/)) on all new classes,
  functions, and properties, etc.

## Utility scripts

### Serial utilities

The npm package defines a utility script called `serial`. It has a number of sub-commands.

To generate 5 pseudo-random customer serials:

```shell
npm run serial generate 5
```

Note: The generated serials will be technically valid, but the script does not check whether they
actually exist.

To generate serials with a custom prefix:

```shell
npm run serial generate 5 UNCLXXXX
```

To validate one or more serials:

```shell
npm run serial validate UNCL0MBW771NMAHW UNCL3Y7FVLK82NP2 UNCLL2ZLDFUDENJ2
```

To calculate the GLS hashes for one or more serials:

```shell
npm run serial gls UNCL0MBW771NMAHW UNCL3Y7FVLK82NP2 UNCLL2ZLDFUDENJ2
```

To get more detailed usage information about the command or sub-commands:

```shell
npm run serial help

npm run serial help generate
```

# CI/CD

## Build Process

The build process is done using GCP cloud build. A trigger for the process is created using a terrraform variable in the
[infrastructure repo](https://github.com/B-Communications/infrastructure/blob/main/gcp/fzo-build/build/terraform.tfvars)

The cloud build can be tracked from the GCP console in the
[FZO-BUILD project](https://console.cloud.google.com/cloud-build/builds?project=fzo-build&supportedpurview=project)

The build uses the files in the `./.ci` folder.
It will lint and build the code and also package the extension for self hosting if the build branch is main.

To keep the same extension id across builds a certificate is stored in
[GCP Secrets](https://console.cloud.google.com/security/secret-manager/secret/SWCldfltBrowserExtensionSelfHostKey/overview?project=fzo-build&supportedpurview=project)
called `SWCldfltBrowserExtensionSelfHostKey`
and applied when packaging the extension.

The build artifact and self hosting files are uploaded to a
[GCP Storage bucket](<https://console.cloud.google.com/storage/browser/fzo-build-sw-cldflt-browser-extension-artifacts?supportedpurview=project&pageState=(%22StorageObjectListTable%22:(%22f%22:%22%255B%255D%22))&prefix=&forceOnObjectsSortingFiltering=false>)
The path follows this structure:
`fzo-build-sw-cldflt-browser-extension-artifacts/${_BUILD_ENV}/${BRANCH_NAME}/${BUILD_ID}`

## Web Store Deployment

Run the following command to build and zip the extension in various configurations, ready for
deployment to the Chrome or Edge Web Store:

```shell
npm run build-for-webstore
```

By default, it will produce dev, qa, and prd builds for mv2 and mv3. The resulting zip file for each
one will be written to the `.webstore` sub-folder.

The script has various options to customise which configurations are built. For example, if you only
want to build mv2 shimmed versions of the extension, then run the command like this:

```shell
npm run build-for-webstore -- --target mv2
```

Use `--help` for more details about the available options, i.e.:

```shell
npm run build-for-webstore -- --help
```

# Extension permissions

TODO: List permissions and what we use them for

## Web request blocking permission

To be fully functional, our extension requires the "web request blocking" permission. In the
[manifest v3 standard](https://developer.chrome.com/docs/extensions/mv3/intro/), this permission
isn't normally available to extensions. However, it can be made available where the extension is
explicitly white-listed or force-installed.

This is no problem for an official release which has been published to the relevant browser store.
Customers are expected to force-install the extension on all platforms so that students cannot
modify or uninstall it.

Additionally, on Windows and macOS, we can whitelist an unpacked extension (i.e. one that has been
developed locally) by specifying the `--whitelisted-extension-id` argument on the browser's command
line.

A locally-built extension will not be fully functional on ChromeOS. This is because the
extension requires the "web request blocking" permission, which can only be granted by whitelisting
or force-installing it. Currently, neither of these appears to be possible on ChromeOS when loading
an unpacked extension. It should be fully functional when an officially published release is

# Provisioning options

TODO: Outline all provisioning / configuration options.

# How to install a published extension

TODO: List published extension IDs, browser store links, and self-hosting details.

# Version number

Semantic versioning should be used. It should be managed manually the same way as a typical npm
package. During build, the npm package version is copied to the extension manifest file.

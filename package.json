{"name": "sw-cldflt-browser-extension", "version": "3.1.6", "private": true, "description": "Smoothwall Cloud Filter browser extension", "scripts": {"artifactregistry-login": "npx google-artifactregistry-auth", "build:dev": "webpack --mode=development --env backend=dev", "build:dev-mv3": "webpack --mode=development --env backend=dev", "build:dev-mv2": "webpack --mode=development --env backend=dev --env manifest_version=2", "build:qa": "webpack --mode=production --env backend=qa", "build:qa-mv3": "webpack --mode=production --env backend=qa", "build:qa-mv2": "webpack --mode=production --env backend=qa --env manifest_version=2", "build:prd": "webpack --mode=production --env backend=prd", "build:prd-mv3": "webpack --mode=production --env backend=prd", "build:prd-mv2": "webpack --mode=production --env backend=prd --env manifest_version=2", "build-for-webstore": "ts-node ./scripts/build-for-webstore.ts", "build-for-self-hosting-mv2": "ts-node ./scripts/build-for-self-hosting-mv2.ts", "deploy-self-hosted-latest": "ts-node ./.ci/node/deploy-self-hosted-latest.ts", "deprovision": "ts-node ./scripts/deprovision.ts", "lint": "eslint --ext .ts --fix src", "check-format": "prettier --check \"src/**/*.{ts,js,html,json}\"", "format": "prettier --write src", "provision": "ts-node ./scripts/provision.ts", "serial": "ts-node ./scripts/serial.ts", "start": "ts-node ./scripts/start.ts", "test": "jest", "build-iwf-list": "ts-node ./scripts/build-iwf-list.ts build", "build-iwf-list:force": "ts-node ./scripts/build-iwf-list.ts build --force", "iwf-list:info": "ts-node ./scripts/build-iwf-list.ts info", "iwf-list:validate": "ts-node ./scripts/build-iwf-list.ts validate"}, "author": "Smoothwall", "license": "proprietary", "devDependencies": {"@google-cloud/storage": "^7.16.0", "@puppeteer/browsers": "^2.10.5", "@tomjs/unzip-crx": "^1.1.3", "@types/archiver": "^5.3.2", "@types/chrome": "^0.0.326", "@types/cli-color": "^2.0.2", "@types/crypto-js": "^4.1.1", "@types/diff": "^5.0.9", "@types/express": "^4.17.21", "@types/jest": "^29.5.0", "@types/node": "^18.6.3", "@types/pako": "^2.0.0", "@types/uuid": "^9.0.2", "@types/webpack": "^5.28.0", "@types/webpack-env": "^1.17.0", "@types/window-or-global": "^1.0.4", "archiver": "^6.0.0", "cli-color": "^2.0.3", "commander": "^11.0.0", "copy-webpack-plugin": "^11.0.0", "crx": "^5.0.1", "del": "^7.0.0", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "eslint-config-standard-with-typescript": "^36.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.0", "express": "^4.21.2", "jest": "^29.5.0", "jest-environment-jsdom": "^29.7.0", "jest-extended": "^4.0.0", "prettier": "^2.8.8", "source-map-loader": "^4.0.0", "terser-webpack-plugin": "^5.3.9", "ts-jest": "^29.1.0", "ts-loader": "^9.3.0", "ts-node": "^10.8.1", "typescript": "^5.0.4", "webpack": "^5.98.0", "webpack-cli": "^5.1.4"}, "dependencies": {"@connect/common-idl": "2.25.2-CHROME-469", "@microsoft/applicationinsights-web": "^2.8.6", "abortcontroller-polyfill": "^1.7.5", "ahocorasick": "^1.0.2", "bowser": "^2.11.0", "crypto-js": "^4.2.0", "fast-json-patch": "^3.1.1", "firebase": "^11.10.0", "ip-range-check": "^0.2.0", "ip-regex": "^5.0.0", "js-sha256": "^0.9.0", "jwt-decode": "^3.1.2", "lz-string": "^1.4.4", "moment": "^2.29.4", "pako": "^2.1.0", "uuid": "^9.0.0"}, "overrides": {"semver": "^7.7.2"}}
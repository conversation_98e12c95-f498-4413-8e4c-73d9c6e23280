/*
= USAGE =

npm run build-for-self-hosting-mv2

= OVERVIEW =

This is a helper script for manually building and packaging the mv2 filter extension for
self-hosting. It's only intended for temporary use to help us with the mv2 deprecation happening in
June 2024. We don't currently intend to keep the self-hosted mv2 extension up-to-date.

Long-term, self-hosting of mv3 will be handled by an automated pipeline. See the ".ci" folder.

= PREREQUISITES =

Most of the prerequisites are the same as for building the extension any other way.

You will need to have Chrome installed on your system so that the extension can be packaged. This
script will try to find it automatically.

= OUTPUT =

The output from this script will go into a folder called "self-hosting-mv2/output". It will contain
two files:

- update.xml = The update manifest file which tells browsers where to find the extension package.
- sw-cldflt-browser-extension-N-prd-mv2.crx = The extension package file. N is the version number.

= UPLOAD =

All the files from the output folder should be uploaded to the "filter" container in the file host.
The update manifest file (update.xml) will need to be overwritten every time something changes. The
extension package shouldn't need to be overwritten unless you're deliberately replacing an existing
version. (Be careful if you do that as Chrome won't update an existing installation unless the
version number goes up).

= HOST =

We are currently using Azure blob storage for mv2 self-hosting. Storage account: swlegacyextensionssacc

The Filter extension files go into the "filter" container.

The direct URL is: https://swlegacyextensionssacc.blob.core.windows.net

It is covered by a CDN with this base URL: https://software-mv2.smoothwall.com

WARNING: If the CDN URL changes then you MUST change this script to match, then repackage and
re-upload all the files. For security reasons, Chrome requires that the URL of the update manifest
file is included in the extension package. If the URLs don't match then Chrome may silently fail to
install or update the extension.

= DEPLOYMENT =

When a customer installs the extension, they need two pieces of the information: the extension ID,
and the URL of the update manifest file.

The update manifest URL is: https://software-mv2.smoothwall.com/filter/update.xml

The extension ID is: nlcnbnnmbfgekccnapcfjpoofnoblfpk

When deploying a self-hosted extension, it should be configured in the same way as the previous
extension which was hosted on the web-store. The structure of the JSON data can be found on the
Knowledge Base.

= EXTENSION ID =

The extension ID must not change otherwise customer deployments will fail to update. However, we do
not set the extension ID directly. It's determined by the key file specified in the packaging step.
The key file was generated the first time the extension was packaged.
*/

import { execSync } from 'child_process';
import clc from 'cli-color';
import * as fs from 'fs';
import * as path from 'path';

import packageFile from '../package.json';
import * as Browser from './utilities/Browser';

// -------------------------------------------------------------------------------------------------
// Constants.

const baseUrl = 'https://software-mv2.smoothwall.com/filter';
const updateManifestFilename = 'update.xml';
const updateUrl = `${baseUrl}/${updateManifestFilename}`;

const buildType = 'prd';
const buildTarget = 'mv2';

const rootFolder = path.resolve(__dirname, '..');
const buildFolder = path.join(rootFolder, 'dist');
const selfHostingFolder = path.join(rootFolder, 'self-hosting-mv2');
const outputFolder = path.join(selfHostingFolder, 'output');
const keyPath = path.join(selfHostingFolder, 'keys', `${buildType}-${buildTarget}.pem`);
const extensionId = 'nlcnbnnmbfgekccnapcfjpoofnoblfpk';

// -------------------------------------------------------------------------------------------------
// Logging functions.

const logInfo = (...args: unknown[]): void => {
  console.info(...args);
};

const logSuccess = (...args: unknown[]): void => {
  console.info(clc.green(...args));
};

const logWarning = (...args: unknown[]): void => {
  console.warn(clc.yellow(...args));
};

const logError = (...args: unknown[]): void => {
  console.error(clc.red(...args));
};

// -------------------------------------------------------------------------------------------------
// Git checks.

// Report the git branch to the console, and warn if it isn't main/master.
try {
  const gitBranch = execSync('git symbolic-ref --short HEAD', {
    windowsHide: true,
    stdio: 'pipe',
    encoding: 'utf8',
  }).trim();

  if (gitBranch === 'main' || gitBranch === 'master') {
    logInfo(`Current git branch: ${gitBranch}`);
  } else {
    logWarning(`WARNING: Git checkout is not on the main branch. Current branch: ${gitBranch}`);
  }
} catch (e: any) {
  logWarning(
    'WARNING: Failed to check the current git branch. Please ensure you are building from the main/master branch.',
  );
  logWarning(e);
}

// Check if there are any uncommitted changes and log a warning if so.
// Note: This won't detect changes which have been committed but not pushed.
try {
  // This command is necessary to update git's internal index, allowing the next command to check
  //  for changes accurately.
  execSync('git update-index -q --refresh', { windowsHide: true, stdio: 'ignore' });
} catch (e: any) {
  // Ignore the error. This command returns a non-zero exit code if the index was out of date.
}

try {
  execSync('git diff-index --quiet HEAD --', { windowsHide: true, stdio: 'ignore' });
  logInfo('Git working copy looks clean. No uncommitted changes found.');
} catch (e: any) {
  logWarning(
    'WARNING: Local changes detected in git repository. Please ensure you are building from a clean and up-to-date checkout.',
  );
}

// -------------------------------------------------------------------------------------------------
// Build.

logInfo(`Package: ${packageFile.name} ${packageFile.version}`);
logInfo(`Configuration: ${buildType}-${buildTarget}`);

logInfo('Cleaning any previous builds / output...');
fs.rmSync(buildFolder, { recursive: true, force: true });
fs.rmSync(outputFolder, { recursive: true, force: true });
fs.mkdirSync(outputFolder, { recursive: true });

logInfo('Installing dependencies...');
execSync('npm ci');

logInfo('Building extension...');
execSync(`npm run build:${buildType}-${buildTarget}`);

// Sanity-check that the build output is where we expect.
const manifestPath = path.join(buildFolder, 'manifest.json');
if (!fs.existsSync(manifestPath)) {
  logError(
    `ERROR: Build output was not found at the expected location. File not found: ${manifestPath}`,
  );
  process.exit(1);
}

// The URL of the update manifest file needs to be added to the extension manifest file.
const manifest = JSON.parse(fs.readFileSync(manifestPath, { encoding: 'utf-8' }));
manifest.update_url = updateUrl;
fs.writeFileSync(manifestPath, JSON.stringify(manifest, undefined, 2), { encoding: 'utf-8' });

// Pack the extension for self-hosting.
logInfo('Packing extension for self-hosting...');
const pathToChrome = Browser.findBrowserExecutable();
if (pathToChrome === '') {
  logError("ERROR: Could not find the Chrome executable. It's needed for packing the extension.");
  process.exit(1);
}

if (!fs.existsSync(keyPath)) {
  logError(`ERROR: Cannot find extension packing key. Expected it at: ${keyPath}`);
  process.exit(1);
}

const packageCommand = `"${pathToChrome}" --pack-extension="${buildFolder}" --pack-extension-key="${keyPath}"`;
execSync(packageCommand);

// The packaged extension will be in a file beside the build folder. It will have the same name
//  as the build folder, but will have the ".crx" extension. Rename it and move it to the
//  self-hosting output folder.
const packageFilename = `${packageFile.name}-${packageFile.version}-${buildType}-${buildTarget}.crx`;
fs.renameSync(`${buildFolder}.crx`, path.join(outputFolder, packageFilename));

// Generate the update manifest file.
logInfo('Writing update manifest file...');
const updateManifest = `<?xml version='1.0' encoding='UTF-8'?>
<gupdate xmlns='http://www.google.com/update2/response' protocol='2.0'>
  <app appid='${extensionId}'>
    <updatecheck codebase='${baseUrl}/${packageFilename}' version='${packageFile.version}' />
  </app>
</gupdate>
`;

fs.writeFileSync(path.join(outputFolder, updateManifestFilename), updateManifest, {
  encoding: 'utf-8',
});

logInfo();
logSuccess('Build complete. The self-hosting files are at this location: ', outputFolder);
logInfo();
logInfo('Upload them to the filter container in Azure storage account: swlegacyextensionssacc');
logInfo('Overwrite the existing update manifest file (XML).');

import { Command, OptionValues } from 'commander';
import SerialId from '../src/models/SerialId';

/*
Usage examples:

  npm run serial generate 5

  npm run serial validate UNCLGQ24ZNT2EFMQ UNCLJ8A2F8WVQFUS

  npm run serial gls UNCLJ8A2F8WVQFUS UNCLJ8A2F8WVQFUS
*/

const program = new Command();
program.name('serial').description('Utilities for generating and processing customer serials.');

// Define the "generate" sub-command.
program
  .command('generate')
  .description('Pseudo-randomly generate valid serials for test purposes.')
  .argument('[count]', 'Number of serials to generate.', '1')
  .argument('[prefix]', 'Prefix to include in the serial.', 'UNCL')
  .option('-p --pretty', 'If specified, serials will be pretty-printed with spaces.', false)
  .action((count: string, prefix: string, options: OptionValues): void => {
    if (!/\d+/.test(count)) {
      console.error('Expected count argument to be an integer.');
      return;
    }

    let parsedCount = parseInt(count, 10);
    if (parsedCount <= 0) {
      return;
    }

    if (parsedCount > 1000) {
      console.error('Limiting count to 1000 for safety.');
      parsedCount = 1000;
    }

    const pretty = options.pretty === true;

    try {
      for (let i = 0; i < parsedCount; ++i) {
        const serial = SerialId.random(prefix);
        console.log(serial.toString(pretty));
      }
    } catch (e: any) {
      console.error(e);
    }
  });

// Define the "validate" sub-command.
program
  .command('validate')
  .description('Validate one or more serials.')
  .argument('<serials...>', 'The serial(s) to validate.')
  .action((serials: string[]): void => {
    let anyInvalid = false;

    serials.forEach((serial) => {
      const isValid = SerialId.isValid(serial, false);
      console.log(`${SerialId.clean(serial)} = ${isValid ? 'valid' : 'INVALID'}`);
      if (!isValid) {
        anyInvalid = true;
      }
    });

    if (anyInvalid) {
      process.exit(1);
    }
  });

// Define the "gls" sub-command.
program
  .command('gls')
  .description('Calculate the GLS hash for one or more serials.')
  .argument('<serials...>', 'The serial(s) to generate GLS hash(es) for.')
  .action((serials: string[]): void => {
    serials.forEach((serial) => {
      if (!SerialId.isValid(serial, false)) {
        console.error(`ERROR - Invalid serial: ${serial}`);
        process.exit(1);
      }

      const parsedSerial = new SerialId(serial, false);
      console.log(`${parsedSerial.toString()} = ${parsedSerial.getGlsHash()}`);
    });
  });

program.parse(process.argv);

/*
This script automatically finds the browser executable, launches a new sandboxed browser instance,
 and side-loads the local build of the extension. By default, it puts the browser data in a local
 folder and reuses it on subsequent runs. However, command line options let you specify that the
 data is to be cleaned automatically on startup. You can also manually specify a data path, or have
 a temporary one randomly generated.

Standalone usage:

    npx ts-node start.ts [-- [--help] options...]

Alternatively, if the start script is defined in a package.json file:

    npm start [-- [--help] options...]

Use the --help option for detailed information about the other options.
*/

import * as child_process from 'child_process';
import { Command } from 'commander';
import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';

import * as Browser from './utilities/Browser';
import UnpackedExtension from './utilities/UnpackedExtension';
import { writeFileSync } from 'node:fs';
import unzip from '@tomjs/unzip-crx';

// Options to add in future:
//  - Specify path to extension build folder.
//  - Specify path to browser executable.
//  - Automatically tail the browser's debug log.

// -------------------------------------------------------------------------------------------------
// Entry point.

// Define and parse the command line parameters for the script.
const program = new Command();

program.name('npm start --').description('Load the Cloud Filter Browser Extension into a browser.');
program.option(
  '--chrome',
  "Launch the Chrome browser, and do nothing if it can't be found. If no browser option is specified, then any available browser will be used.",
);
program.option(
  '--edge',
  "Launch the Edge browser, and do nothing if it can't be found. If no browser option is specified, then any available browser will be used.",
);
program.option(
  '--disable-other-extensions',
  'Prevent the browser from loading any other extensions.',
);
program.option(
  '-c --clean',
  'Delete the data directory on startup. This resets the browser to a completely new instance with no other data. ' +
    "You should ensure that there isn't another browser instance already running with the same data directory before using this.",
);
program.option(
  '-d --data-dir <path>',
  'Manually specify the path of a directory to use for the browser data. ' +
    'This can be useful if you want multiple separate test instances side-by-side. ' +
    'If not specified, a fixed local path will be used instead.',
);
program.option(
  '-t --temp',
  'Randomly generate a temporary directory to use for browser data, and delete it when the browser closes. ' +
    'This is useful for doing a temporary test which does not interfere with any other instances.' +
    'However, note that this script will stay running in the foreground until the browser has closed.',
);
program.option(
  '--debug',
  'Enable debug logging in the browser. ' +
    'This may cause random terminal windows to appear on screen while the browser is running.',
);
program.option(
  '-n --no-extension',
  'Do not load the local build of the extension into the browser. ' +
    'This is useful if you want to load the extension manually, or just want a sandboxed browser instance for something else.',
);
program.option(
  '--show-extension-id',
  'Display the extension ID and do nothing else. The browser will not be opened.',
);
program.option(
  '--chrome-cache-dir <path>',
  'Specify a custom cache directory for Chrome for Testing installations. ' +
    'If not specified, a default directory in the project root will be used.',
);
program.option(
  '--chrome-version <version>',
  'Specify a specific Chrome for Testing version to install and use. ' +
    'If not specified, the stable version will be used.',
);
// add flag to run with the content aware extension
program.option(
  '--deployContentAware',
  'Deploy the content aware extension in addition to the main extension. ' +
    'This is useful for testing the content aware (image blur) features.',
)

program.parse(process.argv);
const options = program.opts();

export const unpackExtension = async (crxPath: string, outputDir: string): Promise<void> => {
  const absoluteOutputDir = path.resolve(outputDir);
  const absoluteCrxPath = path.resolve(crxPath);
  console.log('Absolute input CRX path:', absoluteCrxPath);
  await unzip(absoluteCrxPath, absoluteOutputDir);
}

// -------------------------------------------------------------------------------------------------
// Main execution function.

const unpackContentAware = async(
  contentAwareCRXUrl: string,
  contentAwareTempFolder: string,
  contentAwareExtensionId: string,
): Promise<void> => {
  console.log('Downloading content aware extension from:', contentAwareCRXUrl);
  if (!fs.existsSync(contentAwareTempFolder)) {
    fs.mkdirSync(contentAwareTempFolder, { recursive: true });
  }
  const response = await fetch(contentAwareCRXUrl);
  if (!response.ok) {
    throw new Error(`Failed to download content aware extension: ${response.statusText}`);
  }
  const contentAwareCrxPath = path.join(
    contentAwareTempFolder,
    contentAwareExtensionId + '.crx',
  );
  writeFileSync(contentAwareCrxPath, Buffer.from(await response.arrayBuffer()), {});
  await unpackExtension(contentAwareCrxPath, contentAwareTempFolder);
  fs.unlinkSync(contentAwareCrxPath);
  console.log('Loading unpacked content aware extension:', contentAwareTempFolder);
}

const main = async (): Promise<void> => {
  // Check for invalid arguments and option combinations.
  if (program.args.length > 0) {
    console.error('Unexpected argument(s):', program.args.join(' '));
    process.exit(1);
  }
  if (options.dataDir !== undefined && options.temp === true) {
    console.error('Options --data-dir and --temp cannot be used together.');
    process.exit(1);
  }
  if (options.temp === true && options.clean === true) {
    console.warn('Option --clean has no effect if --temp is also specified.');
  }

  // Find the extension and determine its ID.
  let extension: UnpackedExtension | undefined;
  // The "--no-extension" flag is implicitly turned into an "--extension" flag with inverted logic.
  if (options.extension === false) {
    console.log('Extension will not be loaded into the browser.');
  } else {
    try {
      extension = UnpackedExtension.findExtension(__dirname, '..', 'dist');
    } catch (e: any) {
      console.error('Extension not found. Please ensure you have built it. Exception: ', e);
      process.exit(2);
    }

    console.log('Extension path: ', extension.path);
    console.log('Extension ID: ', extension.id);

    if (options.showExtensionId === true) {
      process.exit(0);
    }
  }

  // Find a suitable browser to run.
  const browserPath = await Browser.findBrowserExecutableAsync(
    options.chrome,
    options.edge,
    options.chromeCacheDir,
    options.chromeVersion,
  );
  if (browserPath === undefined) {
    console.error('The browser executable could not be found.');
    process.exit(1);
  }
  const isEdge = browserPath.toLowerCase().includes('edge');

  // Determine where to put browser data.
  let baseDataDir: string;
  if (options.dataDir !== undefined) {
    // User has explicitly specified a data directory.
    baseDataDir = path.resolve(options.dataDir as string);

    if (fs.existsSync(path.resolve(baseDataDir, '.git'))) {
      console.error(
        "Path specified in --data-dir is a git repository. That probably isn't a good idea.",
      );
      process.exit(1);
    }
  } else if (options.temp === true) {
    // User wants a temporary directory to be randomly generated.
    baseDataDir = fs.mkdtempSync(path.join(os.tmpdir(), 'browser-data-dir-'));
  } else {
    // Use the default data directory in the root folder of the checkout.
    baseDataDir = path.resolve(__dirname, '..', '.browser-data');
  }

  // The browser data will actually go in a sub-folder of the base data directory.
  // This allows us to keep the data for different browser types separate.
  const browserDataDir = path.resolve(baseDataDir, isEdge ? 'edge' : 'chrome');
  if (options.clean === true) {
    fs.rmSync(browserDataDir, { force: true, recursive: true });
  }

  console.log(`Browser data directory: ${browserDataDir}`);
  const isNewDataDir = !fs.existsSync(browserDataDir);
  fs.mkdirSync(browserDataDir, { recursive: true });

  // If we've just created the data directory and are running Edge then try to trick the browser into
  //  thinking it's already gone through the "First Run Experience". This saves a lot of annoying
  //  clicking through popups.
  // TODO: See if we can do something similar for Chrome on macOS.
  if (isEdge && isNewDataDir) {
    fs.writeFileSync(path.resolve(browserDataDir, 'Local State'), Browser.edgeConfigData);
  }

  // Try to ensure the data directory isn't accidentally committed to git.
  const gitignorePath = path.resolve(baseDataDir, '.gitignore');
  if (!fs.existsSync(gitignorePath)) {
    fs.writeFileSync(gitignorePath, '*');
  }

  // Configure the command line options for the browser.
  const browserArgs: string[] = [
    // Run in our temporary user data directory. This ensures a completely clean environment which
    //  isn't affected by other browser data, and which won't persist after this session.
    '--user-data-dir=' + browserDataDir,

    // Prevent standard extensions like Google Drive from being installed.
    '--disable-default-apps',

    // Skip the initial popup which asks you to sign into the browser and migrate data.
    '--no-first-run',
  ];
  if (extension !== undefined && options.disableOtherExtensions === true) {
    browserArgs.push('--disable-extensions-except=' + extension.path);
  }

  if (options.debug === true) {
    browserArgs.push('--enable-logging', '--v=1');
    console.log(
      `Browser debug logging enabled. See log file at: ${path.resolve(
        browserDataDir,
        'chrome_debug.log',
      )}`,
    );
  }
  if (extension !== undefined) {
    // Load the unpacked extension from the local build.
    const extensionPaths: string[] = []
    extensionPaths.push(extension.path);

    // check if the content aware extension should be deployed
    const CONTENT_AWARE_EXTENSION_ID = 'epkahbohdeajpadlmelfkenmoeblkpem';
    if (options.deployContentAware === true) {
      const CONTENT_AWARE_TEMP_DIR = path.join('.', '.content-aware-temp');
      // check if the content aware extension is already unpacked, check for manifest.json
      if (fs.existsSync(path.join(CONTENT_AWARE_TEMP_DIR, 'manifest.json'))) {
        console.log('Content aware extension already unpacked, using existing unpacked extension...');
      } else {
        console.log('Content aware extension not unpacked yet, unpacking now...');
        const CONTENT_AWARE_CRX_URL = 'https://browser-extensions-self-hosted-builds.linewize.net/content-aware/builds/content-aware-1.0.104.crx';
        await unpackContentAware(
          CONTENT_AWARE_CRX_URL,
          CONTENT_AWARE_TEMP_DIR,
          CONTENT_AWARE_EXTENSION_ID
        );
      }

      extensionPaths.push(path.resolve(CONTENT_AWARE_TEMP_DIR));
    }

    // Add the extension paths to the command line arguments.
    browserArgs.push('--load-extension=' + extensionPaths.join(','));

    // Whitelist our extension to ensure it can access all necessary APIs.
    // This probably isn't necessary on manifest v2, but is necessary on manifest v3.
    browserArgs.push('--allowlisted-extension-id=' + extension.id);

    // Although running without a sandbox isn't secure, it can be okay if done within a container.
    if (os.platform() === 'linux') {
      browserArgs.push('--no-sandbox');
    }
  }

  // Launch the browser and optionally wait for it to finish.
  console.log(`Running browser executable: ${browserPath}`);
  console.debug('Command line arguments:');
  browserArgs.forEach((arg: string) => console.debug('  ' + arg));
  const browserProcessOptions: child_process.SpawnOptions = {
    cwd: browserDataDir,
    windowsHide: false,
  };

  if (options.temp !== true) {
    // By default, ignore the browser I/O and let it run on its own.
    browserProcessOptions.stdio = 'ignore';
    browserProcessOptions.detached = true;
  }

  const browserProcess = child_process.spawn(browserPath, browserArgs, browserProcessOptions);

  if (options.temp === true) {
    // For a temporary instance, wait until the browser closes, and then delete the data.
    browserProcess.on('exit', (code: any): void => {
      console.log('Browser closed with exit code: ', code);
    });

    process.on('exit', (): void => {
      console.log(`Deleting browser data directory: ${browserDataDir}`);
      fs.rmdirSync(browserDataDir, { recursive: true });
    });
    process.on('SIGINT', () => process.exit(1));
    process.on('SIGTERM', () => process.exit(1));
  } else {
    // By default, let the browser run independently after this script has finished.
    browserProcess.unref();
  }
};

// -------------------------------------------------------------------------------------------------
// Execute main function.

main().catch((error) => {
  console.error('Error:', error);
  process.exit(1);
});

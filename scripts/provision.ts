// NOTE: This script must be executed with admin/sudo privileges.

import { Command } from 'commander';
import * as fs from 'fs';
import * as Provisioning from './utilities/Provisioning';
import { ProvisioningMethod } from './utilities/Provisioning';
import UnpackedExtension from './utilities/UnpackedExtension';

// Define the command line parameters for the script.
const program = new Command();

program.name('provision').description('Provision the Cloud Filter Browser Extension.');
program.option(
  '-s --serial <serial>',
  "The customer's UNCL serial ID. This must be specified either as an option or in a separate provisioning file.",
);
program.option(
  '-t --tenant <tenant>',
  "The customer's tenant ID, if applicable. Can be empty or omitted if not applicable.",
);
program.option(
  '-d --defaultUser <username>',
  'The default username to fall-back on if no user is logged-in to the browser. This only applies to standalone mode.',
);
program.option(
  '-f --disableMiniFilter',
  'If specified, the mini-filter will be bypassed. This means that when the full-filter is available, all requests will be allowed.',
);
program.option(
  '-m --mode <mode>',
  'Specifies the operating mode to provision. Defaults to standalone mode if not specified.',
);
program.option(
  '--method <method>',
  `macOS only: Force a specific provisioning method. Options: ${Object.values(ProvisioningMethod)
    .map((m) => `"${m}"`)
    .join(', ')}. Defaults to "${ProvisioningMethod.AUTO}" (try defaults first, fallback to dscl).`,
);
program.argument(
  '[provisioning-file-path]',
  'Optional path of a JSON file to load the provisioning data from',
);
program.addHelpText(
  'afterAll',
  `
The provisioning file can be used instead of, or in addition to, the script parameters.
If both are specified, the script parameters take precedence.

The file structure should be:

{
  "serial": "",
  "tenant": "",
  "defaultUser": "",
  "disableMiniFilter": false,
  "mode": "standalone",
  "method": "${ProvisioningMethod.AUTO}"
}

macOS Provisioning Methods:
- "${ProvisioningMethod.DEFAULTS}": Uses the defaults command (recommended, less likely to be blocked by security software)
- "${ProvisioningMethod.DSCL}": Uses the dscl command (original method, may be blocked by Crowdstrike)
- "${ProvisioningMethod.AUTO}": Try defaults first, fallback to dscl if it fails (default behavior)
`,
);

program.parse(process.argv);

// Try to load a provisioning file, if one was specified.
let provisioningFile: Provisioning.Data = {};
const provisioningFilePath = program.args[0] ?? '';
if (provisioningFilePath !== '') {
  console.debug('Loading provisioning file: ', provisioningFilePath);
  try {
    provisioningFile = JSON.parse(fs.readFileSync(provisioningFilePath).toString());
  } catch (e: any) {
    console.error('Failed to load provisioning file. Exception: ', e);
    process.exit(1);
  }
}

// If script parameters were specified then they override the values in the provisioning file.
const scriptParameters = program.opts();
const provisioningData: Provisioning.Data = {};
provisioningData.serial = scriptParameters.serial ?? provisioningFile.serial;
provisioningData.tenant = scriptParameters.tenant ?? provisioningFile.tenant;
provisioningData.defaultUser = scriptParameters.defaultUser ?? provisioningFile.defaultUser;
provisioningData.disableMiniFilter =
  scriptParameters.disableMiniFilter ?? provisioningFile.disableMiniFilter;
provisioningData.mode = scriptParameters.mode ?? provisioningFile.mode ?? 'standalone'; // <-- default value
provisioningData.method =
  scriptParameters.method ?? provisioningFile.method ?? ProvisioningMethod.AUTO; // <-- default value

const isValidSerialId = (value: any): boolean => /^[a-z0-9]{16}$/i.test(value);
const isValidTenantId = (value: any): boolean =>
  /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value);

const isValidDefaultUser = (value: any): boolean => /^.*@.*$/.test(value);

// Validate the provisioning data.
if (provisioningData.serial === undefined || provisioningData.serial === '') {
  console.error(
    'The serial ID is required. It must be specified in the provisioning file or as a script parameter.',
  );
  process.exit(2);
}
if (!isValidSerialId(provisioningData.serial)) {
  console.error(
    'Invalid serial ID. It must consist of 16 ASCII alphanumeric characters, e.g. UNCLTEST12345678.',
  );
  process.exit(2);
}

if (provisioningData.tenant !== undefined && !isValidTenantId(provisioningData.tenant)) {
  console.error('Invalid tenant ID. It must be a valid UUID.');
  process.exit(2);
}

if (provisioningData.defaultUser !== undefined) {
  if (provisioningData.mode === 'native') {
    console.warn('The defaultUser value has no effect in a native deployment.');
  } else if (!isValidDefaultUser(provisioningData.defaultUser)) {
    console.error('Invalid default user. It must be a valid email address.');
    process.exit(2);
  }
}

if (provisioningData.mode !== 'standalone' && provisioningData.mode !== 'native') {
  console.error('Invalid mode. It must be "standalone" or "native".');
  process.exit(2);
}

const validMethods = Object.values(ProvisioningMethod);
if (
  provisioningData.method &&
  !validMethods.includes(provisioningData.method as ProvisioningMethod)
) {
  console.error(`Invalid method. It must be one of: ${validMethods.join(', ')}.`);
  process.exit(2);
}

console.debug('Using provisioning data:', provisioningData);

// We need to know the extension ID to write the provisioning data.
let extension: UnpackedExtension;
try {
  extension = UnpackedExtension.findExtension(__dirname, '..', 'dist');
} catch (e) {
  console.error(e);
  console.error('Extension not found. Please ensure it has been built.');
  process.exit(3);
}

console.debug('Found extension at: ', extension.path);
console.debug('Extension ID: ', extension.id);

Provisioning.provision(extension.id, provisioningData)
  .then(() => {
    console.log('Provisioned successfully.');
  })
  .catch((e: any) => {
    console.error('Provisioning failed. ', e);
    console.log('Please ensure you run this script with admin/sudo privileges.');
    process.exit(5);
  });

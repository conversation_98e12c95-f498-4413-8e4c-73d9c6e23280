import * as crypto from 'crypto';
import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';

/**
 * Contains information about a locally-built unpacked extension.
 */
export default class UnpackedExtension {
  /**
   * Initialise a new instance of the class.
   *
   * @param path The absolute, normalised path of the extension build directory.
   * @param id The ID which the extension will be assigned when it's loaded into the browser. This should have been inferred from the path.
   * @param manifest The contents of the extension manifest file.
   */
  public constructor(
    public readonly path: string,
    public readonly id: string,
    public readonly manifest: chrome.runtime.Manifest,
  ) {}

  /**
   * Find and load details about an unpacked extension in the given directory, if it exists.
   * A manifest file (called "manifest.json") must be present at the root of the directory.
   *
   * @param pathParts The path of the build directory containing the unpacked extension. It can be
   *  given as a series of path parts to be joined, e.g. findExtension("..", "dist"). The path will
   *  be resolved and normalized.
   * @returns Returns a populated instance of UnpackedExtension, containing about the specified
   *  extension.
   * @throws {Error} The extension directory was not found, or the manifest file was missing,
   *  inaccessible, or invalid.
   */
  public static readonly findExtension = (...pathParts: string[]): UnpackedExtension => {
    // The extension path must be normalized so that a consistent extension ID can be generated.
    const extensionPath = path.normalize(path.resolve(...pathParts));
    const extensionId = UnpackedExtension.determineExtensionId(extensionPath);

    // Load the manifest file.
    const manifestPath = path.resolve(extensionPath, 'manifest.json');
    fs.accessSync(manifestPath, fs.constants.R_OK);
    const manifest = JSON.parse(fs.readFileSync(manifestPath).toString());

    return new UnpackedExtension(extensionPath, extensionId, manifest);
  };

  /**
   * Get the ID which would be assigned to an extension at the specified path.
   * The path does not need to exist.
   * Use findExtension() if you need to ensure the extension actually exists.
   *
   * @param pathParts The path of the build directory which would contain the unpacked extension. It
   *  can be given as a series of path parts to be joined, e.g. findExtensionId("..", "dist"). The
   *  path will be resolved and normalized.
   * @returns A string containing the ID which would be assigned to
   */
  public static readonly findExtensionId = (...pathParts: string[]): string => {
    return this.determineExtensionId(path.normalize(path.resolve(...pathParts)));
  };

  /**
   * Compute the ID for an unpacked extension loaded from the specified path.
   *
   * @param extensionPath Absolute and normalised path to the directory from where the unpacked
   *  extension will be loaded.
   */
  public static readonly determineExtensionId = (extensionPath: string): string => {
    const hash = crypto.createHash('sha256');

    if (os.platform() === 'win32') {
      // On Windows, Chromium explicitly uppercases the drive letter before hashing the path.
      extensionPath = extensionPath.charAt(0).toUpperCase() + extensionPath.slice(1);

      // The file path is encoded as UTF-16 (little endian), and then the byte-by-byte
      //  representation is hashed to get the extension ID.
      hash.update(Buffer.from(extensionPath, 'utf16le'));
    } else {
      // On all other platforms, Chromium seems to use a UTF-8 encoding for the path.
      hash.update(Buffer.from(extensionPath, 'utf-8'));
    }

    // Encode the hash as a hexadecimal array of characters. We only want the first 32.
    const hexDigest = hash.digest('hex').slice(0, 32).split('');

    // Map the hex characters to lower-case alphabetic characters, and return it all as a string.
    // '0' maps onto 'a', '1' maps on to 'b', and so on.
    // Note: 97 is the ASCII/UTF-8 value for 'a'.
    return hexDigest.map((c: string) => String.fromCharCode(parseInt(c, 16) + 97)).join('');
  };
}

/**
 * Unit tests for Provisioning module - focusing on testable logic
 * This test file focuses on the parts that can be easily unit tested
 * without complex exec mocking.
 */

import * as os from 'os';

import * as Provisioning from './Provisioning';
import { ProvisioningMethod } from './Provisioning';

// Mock os module
jest.mock('os');
const mockedOs = os as jest.Mocked<typeof os>;

// Mock process.getuid
const mockGetuid = jest.fn();
Object.defineProperty(process, 'getuid', {
  value: mockGetuid,
  configurable: true,
});

describe('Provisioning - Unit Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockGetuid.mockReturnValue(0); // Default to root

    // Mock console methods to avoid noise
    jest.spyOn(console, 'debug').mockImplementation(() => {});
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Platform Detection', () => {
    it('should throw error for unsupported platforms', async () => {
      mockedOs.platform.mockReturnValue('freebsd' as any);

      await expect(
        Provisioning.provision('test-extension-id', { mode: 'standalone' }),
      ).rejects.toThrow('Platform "freebsd" is not supported.');
    });

    it('should throw error for unsupported platforms in deprovision', async () => {
      mockedOs.platform.mockReturnValue('aix' as any);

      await expect(Provisioning.deprovision('test-extension-id')).rejects.toThrow(
        'Platform "aix" is not supported.',
      );
    });
  });

  describe('Extension ID Validation', () => {
    it('should throw error for empty extension ID in provision', async () => {
      mockedOs.platform.mockReturnValue('darwin');

      await expect(Provisioning.provision('', { mode: 'standalone' })).rejects.toThrow(
        'Extension ID must not be empty.',
      );
    });

    it('should throw error for empty extension ID in deprovision', async () => {
      mockedOs.platform.mockReturnValue('darwin');

      await expect(Provisioning.deprovision('')).rejects.toThrow('Extension ID must not be empty.');
    });
  });

  describe('Permission Validation (macOS)', () => {
    beforeEach(() => {
      mockedOs.platform.mockReturnValue('darwin');
    });

    it('should throw error when not running as root', async () => {
      mockGetuid.mockReturnValue(1000); // Non-root user

      await expect(
        Provisioning.provision('test-extension-id', { mode: 'standalone' }),
      ).rejects.toThrow('This script must be executed as root/sudo.');
    });

    it('should throw error when not running as root for deprovision', async () => {
      mockGetuid.mockReturnValue(501); // Non-root user

      await expect(Provisioning.deprovision('test-extension-id')).rejects.toThrow(
        'This script must be executed as root/sudo.',
      );
    });

    it('should allow root user (uid 0)', async () => {
      mockGetuid.mockReturnValue(0); // Root user

      // This will fail at exec level, but should pass permission check
      try {
        await Provisioning.provision('test-extension-id', { mode: 'standalone' });
      } catch (error: any) {
        // Should not be a permission error
        expect(error.message).not.toContain('root/sudo');
      }
    });
  });

  describe('Data Interface Validation', () => {
    beforeEach(() => {
      mockedOs.platform.mockReturnValue('darwin');
    });

    it('should handle undefined data properties gracefully', async () => {
      const data: Provisioning.Data = {
        serial: undefined,
        tenant: undefined,
        defaultUser: undefined,
        mode: 'standalone',
        method: undefined,
      };

      // Should not throw for undefined properties
      try {
        await Provisioning.provision('test-extension-id', data);
      } catch (error: any) {
        // Should not be a validation error for undefined properties
        expect(error.message).not.toContain('undefined');
        expect(error.message).not.toContain('required');
      }
    });

    it('should handle empty string properties gracefully', async () => {
      const data: Provisioning.Data = {
        serial: '',
        tenant: '',
        defaultUser: '',
        mode: 'standalone',
        method: ProvisioningMethod.DEFAULTS,
      };

      // Should not throw for empty string properties
      try {
        await Provisioning.provision('test-extension-id', data);
      } catch (error: any) {
        // Should not be a validation error for empty strings
        expect(error.message).not.toContain('empty');
        expect(error.message).not.toContain('required');
      }
    });
  });

  describe('Method Parameter Validation', () => {
    beforeEach(() => {
      mockedOs.platform.mockReturnValue('darwin');
    });

    it('should accept valid method values', () => {
      const validMethods = [
        ProvisioningMethod.AUTO,
        ProvisioningMethod.DEFAULTS,
        ProvisioningMethod.DSCL,
        undefined,
      ];

      validMethods.forEach((method) => {
        const data: Provisioning.Data = {
          mode: 'standalone',
          method: method,
        };

        // Should not throw during data creation
        expect(data.method).toBe(method);
      });
    });

    it('should handle method defaulting', () => {
      const data: Provisioning.Data = {
        mode: 'standalone',
        // method not specified
      };

      // Method should be undefined, which will default to ProvisioningMethod.AUTO in implementation
      expect(data.method).toBeUndefined();
    });
  });

  describe('Mode Parameter Validation', () => {
    beforeEach(() => {
      mockedOs.platform.mockReturnValue('darwin');
    });

    it('should accept valid mode values', () => {
      const validModes = ['standalone', 'native'];

      validModes.forEach((mode) => {
        const data: Provisioning.Data = {
          mode: mode,
        };

        // Should not throw during data creation
        expect(data.mode).toBe(mode);
      });
    });
  });

  describe('Cross-Platform Behavior', () => {
    it('should handle Windows platform', async () => {
      mockedOs.platform.mockReturnValue('win32');

      // Windows doesn't require root check
      try {
        await Provisioning.provision('test-extension-id', { mode: 'standalone' });
      } catch (error: any) {
        // Should not be a permission error on Windows
        expect(error.message).not.toContain('root/sudo');
      }
    });

    it('should handle Linux platform', async () => {
      mockedOs.platform.mockReturnValue('linux');

      // Linux doesn't require root check for this operation
      try {
        await Provisioning.provision('test-extension-id', { mode: 'standalone' });
      } catch (error: any) {
        // Should not be a permission error on Linux
        expect(error.message).not.toContain('root/sudo');
      }
    });
  });

  describe('Environment Variable Handling', () => {
    beforeEach(() => {
      mockedOs.platform.mockReturnValue('darwin');
    });

    it('should handle missing SUDO_USER environment variable', () => {
      const originalSudoUser = process.env.SUDO_USER;
      delete process.env.SUDO_USER;

      // Should not throw just because SUDO_USER is missing
      const data: Provisioning.Data = { mode: 'standalone' };
      expect(data).toBeDefined();

      // Restore
      if (originalSudoUser) {
        process.env.SUDO_USER = originalSudoUser;
      }
    });

    it('should handle present SUDO_USER environment variable', () => {
      const originalSudoUser = process.env.SUDO_USER;
      process.env.SUDO_USER = 'testuser';

      // Should not throw with SUDO_USER present
      const data: Provisioning.Data = { mode: 'standalone' };
      expect(data).toBeDefined();

      // Restore
      if (originalSudoUser) {
        process.env.SUDO_USER = originalSudoUser;
      } else {
        delete process.env.SUDO_USER;
      }
    });
  });
});

// Export test utilities for integration tests
export const TestUtils = {
  createTestData: (overrides: Partial<Provisioning.Data> = {}): Provisioning.Data => ({
    serial: 'TESTSERIAL123456',
    tenant: '12345678-1234-1234-1234-123456789abc',
    defaultUser: '<EMAIL>',
    mode: 'standalone',
    method: ProvisioningMethod.AUTO,
    ...overrides,
  }),

  mockMacOSEnvironment: () => {
    mockedOs.platform.mockReturnValue('darwin');
    mockGetuid.mockReturnValue(0);
    process.env.SUDO_USER = 'testuser';
  },

  mockWindowsEnvironment: () => {
    mockedOs.platform.mockReturnValue('win32');
  },

  mockLinuxEnvironment: () => {
    mockedOs.platform.mockReturnValue('linux');
  },
};

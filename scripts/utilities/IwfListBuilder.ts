import fs from 'fs';
import path from 'path';
import LZString from 'lz-string';

/**
 * Utility for building and encoding IWF list data from JSON files.
 * This utility processes JSON files from the _iwflist directory and encodes them
 * using LZ64 compression for use in the mini-blocklist system.
 */
export class IwfListBuilder {
  private readonly sourceDir: string;
  private readonly outputFile: string;
  private readonly emptyStringHash = 'da39a3ee5e6b4b0d3255bfef95601890afd80709';

  constructor(sourceDir: string = 'src/_iwflist', outputFile: string = 'src/mini-blocklist-data/iwflist.ts') {
    this.sourceDir = path.resolve(sourceDir);
    this.outputFile = path.resolve(outputFile);
  }

  /**
   * Find the latest JSON file in the _iwflist directory based on filename date.
   * @returns The path to the latest JSON file or null if none found
   */
  private findLatestJsonFile(): string | null {
    if (!fs.existsSync(this.sourceDir)) {
      console.error(`Source directory does not exist: ${this.sourceDir}`);
      return null;
    }

    const files = fs.readdirSync(this.sourceDir)
      .filter(file => file.endsWith('.json') && file.startsWith('iwflist-'))
      .sort()
      .reverse(); // Sort in descending order to get latest first

    return files.length > 0 ? path.join(this.sourceDir, files[0]) : null;
  }

  /**
   * Extract date from filename (e.g., iwflist-2025-07-09.json -> 2025-07-09)
   * @param filename The filename to extract date from
   * @returns The extracted date string or null if not found
   */
  private extractDateFromFilename(filename: string): string | null {
    const match = filename.match(/iwflist-(\d{4}-\d{2}-\d{2})\.json$/);
    return match ? match[1] : null;
  }

  /**
   * Check if the JSON file has a comment indicating no changes are needed.
   * This looks for a comment at the beginning of the file.
   * @param filePath Path to the JSON file
   * @returns True if encoding should be skipped
   */
  private shouldSkipEncoding(filePath: string): boolean {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      const lines = content.split('\n');
      
      // Check first few lines for skip comment
      for (let i = 0; i < Math.min(5, lines.length); i++) {
        const line = lines[i].trim();
        if (line.includes('// NO_ENCODING_NEEDED') || 
            line.includes('/* NO_ENCODING_NEEDED */') ||
            line.includes('// SKIP_ENCODING')) {
          return true;
        }
      }
      return false;
    } catch (error) {
      console.warn(`Could not read file for skip check: ${error}`);
      return false;
    }
  }

  /**
   * Load and validate JSON data from file.
   * @param filePath Path to the JSON file
   * @returns Parsed JSON data or null if invalid
   */
  private loadJsonData(filePath: string): string[] | null {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      const data = JSON.parse(content);
      
      if (!Array.isArray(data)) {
        console.error('JSON file must contain an array at the top level');
        return null;
      }

      // Validate that all entries are strings and look like SHA-1 hashes
      const sha1Regex = /^[a-f0-9]{40}$/i;
      for (const entry of data) {
        if (typeof entry !== 'string' || !sha1Regex.test(entry)) {
          console.error(`Invalid hash entry found: ${entry}`);
          return null;
        }
      }

      return data;
    } catch (error) {
      console.error(`Failed to load JSON data: ${error}`);
      return null;
    }
  }

  /**
   * Filter out problematic hashes like empty string hash.
   * @param data Array of hash strings
   * @returns Filtered array
   */
  private filterProblematicHashes(data: string[]): string[] {
    const filtered = data.filter(hash => hash !== this.emptyStringHash);
    
    if (filtered.length !== data.length) {
      console.log(`Filtered out ${data.length - filtered.length} problematic hash(es)`);
    }
    
    return filtered;
  }

  /**
   * Compress JSON data using LZString.
   * @param data Array of hash strings
   * @returns Base64 compressed string
   */
  private compressData(data: string[]): string {
    const jsonString = JSON.stringify(data);
    return LZString.compressToBase64(jsonString);
  }

  /**
   * Generate the TypeScript file content with header information.
   * @param compressedData The LZ64 compressed data
   * @param sourceFile Path to the source JSON file
   * @param dataLength Number of entries in the original data
   * @returns TypeScript file content
   */
  private generateTsFileContent(compressedData: string, sourceFile: string, dataLength: number): string {
    const filename = path.basename(sourceFile);
    const dateFromFilename = this.extractDateFromFilename(filename);
    const buildDate = new Date().toISOString();
    
    return `// IWF List - Auto-generated from ${filename}
// Source file: ${sourceFile}
// Build date: ${buildDate}
// Data date: ${dateFromFilename || 'unknown'}
// Entry count: ${dataLength}
// Compression: LZ64 (LZString.compressToBase64)
// 
// This file contains compressed IWF (Internet Watch Foundation) blocklist data.
// The data is compressed using LZString and should be decompressed using:
// LZString.decompressFromBase64(iwflist)

const iwflist =
  '${compressedData}';

export default iwflist;
`;
  }

  /**
   * Check if the current output file is up to date compared to the source.
   * @param sourceFile Path to the source JSON file
   * @returns True if the output file is newer than the source
   */
  private isOutputUpToDate(sourceFile: string): boolean {
    if (!fs.existsSync(this.outputFile)) {
      return false;
    }

    try {
      const sourceStats = fs.statSync(sourceFile);
      const outputStats = fs.statSync(this.outputFile);

      return outputStats.mtime > sourceStats.mtime;
    } catch (error) {
      // If we can't stat either file, assume not up to date
      return false;
    }
  }

  /**
   * Main build method that processes the latest IWF list JSON file.
   * @param force Force rebuild even if output is up to date
   * @returns True if build was successful, false otherwise
   */
  public build(force: boolean = false): boolean {
    console.log('IWF List Builder - Starting build process...');

    // Find the latest JSON file
    const latestJsonFile = this.findLatestJsonFile();
    if (!latestJsonFile) {
      console.error('No IWF list JSON files found in source directory');
      return false;
    }

    console.log(`Found latest JSON file: ${latestJsonFile}`);

    // Check if we should skip encoding based on comments
    if (this.shouldSkipEncoding(latestJsonFile)) {
      console.log('Skipping encoding - file contains skip comment');
      return true;
    }

    // Check if output is up to date (unless forced)
    if (!force && this.isOutputUpToDate(latestJsonFile)) {
      console.log('Output file is up to date - skipping build');
      return true;
    }

    // Load and validate JSON data
    const jsonData = this.loadJsonData(latestJsonFile);
    if (!jsonData) {
      console.error('Failed to load or validate JSON data');
      return false;
    }

    console.log(`Loaded ${jsonData.length} hash entries`);

    // Filter out problematic hashes
    const filteredData = this.filterProblematicHashes(jsonData);
    console.log(`After filtering: ${filteredData.length} hash entries`);

    // Compress the data
    console.log('Compressing data using LZString...');
    const compressedData = this.compressData(filteredData);
    console.log(`Compressed size: ${compressedData.length} characters`);

    // Generate TypeScript file content
    const tsContent = this.generateTsFileContent(compressedData, latestJsonFile, filteredData.length);

    // Write the output file
    try {
      const outputDir = path.dirname(this.outputFile);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      fs.writeFileSync(this.outputFile, tsContent, 'utf-8');
      console.log(`Successfully wrote output file: ${this.outputFile}`);
      return true;
    } catch (error) {
      console.error(`Failed to write output file: ${error}`);
      return false;
    }
  }

  /**
   * Get information about the current state without building.
   * @returns Object with information about source and output files
   */
  public getInfo(): { sourceFile: string | null; outputFile: string; upToDate: boolean } {
    const sourceFile = this.findLatestJsonFile();
    const upToDate = sourceFile ? this.isOutputUpToDate(sourceFile) : false;

    return {
      sourceFile,
      outputFile: this.outputFile,
      upToDate
    };
  }
}

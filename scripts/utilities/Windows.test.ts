import { escapePowerShellArgument } from './Windows';

describe('Windows', () => {
  describe('escapePowerShellArgument()', () => {
    it('escapes quotation marks with a backtick', () => {
      expect(escapePowerShellArgument('testing "123"')).toEqual('testing `"123`"');
    });

    it('escapes dollar signs with a backtick', () => {
      expect(escapePowerShellArgument('testing $123$')).toEqual('testing `$123`$');
    });

    it('escapes backticks with a backtick', () => {
      expect(escapePowerShellArgument('testing `123`')).toEqual('testing ``123``');
    });
  });
});

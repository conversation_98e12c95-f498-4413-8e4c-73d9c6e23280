import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';

import {
  Browser as PuppeteerBrowser,
  BrowserPlatform,
  computeExecutablePath,
  detectBrowserPlatform,
  getInstalledBrowsers,
  install,
  resolveBuildId,
} from '@puppeteer/browsers';

import {
  ensureChromeForTestingInstalled,
  findBrowserExecutable,
  findBrowserExecutableAsync,
} from './Browser';

// Mock the @puppeteer/browsers module
jest.mock('@puppeteer/browsers');

// Mock fs module
jest.mock('fs');

// Mock os module
jest.mock('os');

// Mock path module
jest.mock('path');

describe('Browser utility', () => {
  const mockPlatform = 'mac' as BrowserPlatform;
  const mockCacheDir = '/test/cache';
  const mockExecutablePath = '/test/chrome/executable';
  const mockBuildId = '123.0.4567.89';

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default mocks
    (detectBrowserPlatform as jest.Mock).mockReturnValue(mockPlatform);
    (path.resolve as jest.Mock).mockImplementation((...args) => args.join('/'));
    (os.platform as jest.Mock).mockReturnValue('darwin');
    (fs.accessSync as jest.Mock).mockImplementation(() => {
      // Default to success
    });
  });

  describe('ensureChromeForTestingInstalled', () => {
    it('should install Chrome for Testing when not already installed', async () => {
      // Mock no existing installations
      (getInstalledBrowsers as jest.Mock).mockResolvedValue([]);
      (resolveBuildId as jest.Mock).mockResolvedValue(mockBuildId);
      (install as jest.Mock).mockResolvedValue({
        executablePath: mockExecutablePath,
      });

      const result = await ensureChromeForTestingInstalled(mockCacheDir);

      expect(getInstalledBrowsers).toHaveBeenCalledWith({ cacheDir: mockCacheDir });
      expect(resolveBuildId).toHaveBeenCalledWith(PuppeteerBrowser.CHROME, mockPlatform, 'stable');
      expect(install).toHaveBeenCalledWith({
        browser: PuppeteerBrowser.CHROME,
        buildId: mockBuildId,
        cacheDir: mockCacheDir,
        platform: mockPlatform,
      });
      expect(fs.accessSync).toHaveBeenCalledWith(mockExecutablePath, fs.constants.X_OK);
      expect(result).toBe(mockExecutablePath);
    });

    it('should use existing Chrome for Testing installation when available', async () => {
      const existingBuildId = '122.0.4567.88';
      const existingExecutablePath = '/test/existing/chrome';

      // Mock existing installation
      (getInstalledBrowsers as jest.Mock).mockResolvedValue([
        {
          browser: PuppeteerBrowser.CHROME,
          buildId: existingBuildId,
        },
      ]);
      (computeExecutablePath as jest.Mock).mockReturnValue(existingExecutablePath);

      const result = await ensureChromeForTestingInstalled(mockCacheDir);

      expect(getInstalledBrowsers).toHaveBeenCalledWith({ cacheDir: mockCacheDir });
      expect(computeExecutablePath).toHaveBeenCalledWith({
        browser: PuppeteerBrowser.CHROME,
        buildId: existingBuildId,
        cacheDir: mockCacheDir,
      });
      expect(install).not.toHaveBeenCalled();
      expect(fs.accessSync).toHaveBeenCalledWith(existingExecutablePath, fs.constants.X_OK);
      expect(result).toBe(existingExecutablePath);
    });

    it('should install specific version when buildId is provided', async () => {
      const specificBuildId = '124.0.4567.90';

      (getInstalledBrowsers as jest.Mock).mockResolvedValue([]);
      (install as jest.Mock).mockResolvedValue({
        executablePath: mockExecutablePath,
      });

      const result = await ensureChromeForTestingInstalled(mockCacheDir, specificBuildId);

      expect(install).toHaveBeenCalledWith({
        browser: PuppeteerBrowser.CHROME,
        buildId: specificBuildId,
        cacheDir: mockCacheDir,
        platform: mockPlatform,
      });
      expect(resolveBuildId).not.toHaveBeenCalled();
      expect(result).toBe(mockExecutablePath);
    });

    it('should use default cache directory when not provided', async () => {
      (getInstalledBrowsers as jest.Mock).mockResolvedValue([]);
      (resolveBuildId as jest.Mock).mockResolvedValue(mockBuildId);
      (install as jest.Mock).mockResolvedValue({
        executablePath: mockExecutablePath,
      });

      await ensureChromeForTestingInstalled();

      // Check that getInstalledBrowsers was called with the resolved cache directory
      expect(getInstalledBrowsers).toHaveBeenCalledWith({
        cacheDir: expect.stringContaining('.chrome-for-testing-cache'),
      });
    });

    it('should throw error when platform is unsupported', async () => {
      (detectBrowserPlatform as jest.Mock).mockReturnValue(undefined);

      await expect(ensureChromeForTestingInstalled(mockCacheDir)).rejects.toThrow(
        'Unsupported platform: darwin',
      );
    });

    it('should throw error when executable is not accessible', async () => {
      (getInstalledBrowsers as jest.Mock).mockResolvedValue([]);
      (resolveBuildId as jest.Mock).mockResolvedValue(mockBuildId);
      (install as jest.Mock).mockResolvedValue({
        executablePath: mockExecutablePath,
      });
      (fs.accessSync as jest.Mock).mockImplementation(() => {
        throw new Error('Permission denied');
      });

      await expect(ensureChromeForTestingInstalled(mockCacheDir)).rejects.toThrow(
        `Chrome for Testing executable not accessible: ${mockExecutablePath}`,
      );
    });

    it('should throw error when installation fails', async () => {
      (getInstalledBrowsers as jest.Mock).mockResolvedValue([]);
      (resolveBuildId as jest.Mock).mockResolvedValue(mockBuildId);
      (install as jest.Mock).mockRejectedValue(new Error('Download failed'));

      await expect(ensureChromeForTestingInstalled(mockCacheDir)).rejects.toThrow(
        'Failed to ensure Chrome for Testing: Error: Download failed',
      );
    });
  });

  describe('findBrowserExecutableAsync', () => {
    it('should return Chrome for Testing path when Chrome is requested', async () => {
      (getInstalledBrowsers as jest.Mock).mockResolvedValue([]);
      (resolveBuildId as jest.Mock).mockResolvedValue(mockBuildId);
      (install as jest.Mock).mockResolvedValue({
        executablePath: mockExecutablePath,
      });

      const result = await findBrowserExecutableAsync(true, false);

      expect(result).toBe(mockExecutablePath);
    });

    it('should fall back to Edge when Chrome for Testing fails and Edge is allowed', async () => {
      const edgePath = '/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge';

      // Mock Chrome for Testing failure
      (getInstalledBrowsers as jest.Mock).mockRejectedValue(new Error('Network error'));

      // Mock fs.accessSync to simulate Edge being available
      (fs.accessSync as jest.Mock).mockImplementation((path: string) => {
        if (path.includes('Microsoft Edge')) {
          return; // Success for Edge
        }
        throw new Error('Not found'); // Fail for other paths
      });

      const result = await findBrowserExecutableAsync(true, true);

      // Should return the Edge path since Chrome for Testing failed
      expect(result).toBe(edgePath);
    });

    it('should return empty string when Chrome for Testing fails and Edge is not allowed', async () => {
      (getInstalledBrowsers as jest.Mock).mockRejectedValue(new Error('Network error'));

      const result = await findBrowserExecutableAsync(true, false);

      expect(result).toBeUndefined();
    });

    it('should default to Chrome when no browser is specified', async () => {
      (getInstalledBrowsers as jest.Mock).mockResolvedValue([]);
      (resolveBuildId as jest.Mock).mockResolvedValue(mockBuildId);
      (install as jest.Mock).mockResolvedValue({
        executablePath: mockExecutablePath,
      });

      const result = await findBrowserExecutableAsync(false, false);

      expect(result).toBe(mockExecutablePath);
    });
  });

  describe('findBrowserExecutable (legacy)', () => {
    it('should be available for backward compatibility', () => {
      expect(typeof findBrowserExecutable).toBe('function');
    });
  });
});

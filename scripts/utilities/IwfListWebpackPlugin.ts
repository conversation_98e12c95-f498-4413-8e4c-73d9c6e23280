import { Compiler } from 'webpack';
import { IwfListBuilder } from './IwfListBuilder';

/**
 * Webpack plugin that automatically builds the IWF list during compilation.
 * This ensures that the iwflist.ts file is always up to date when building the extension.
 */
export class IwfListWebpackPlugin {
  private readonly sourceDir: string;
  private readonly outputFile: string;
  private readonly force: boolean;

  constructor(options: {
    sourceDir?: string;
    outputFile?: string;
    force?: boolean;
  } = {}) {
    this.sourceDir = options.sourceDir || 'src/_iwflist';
    this.outputFile = options.outputFile || 'src/mini-blocklist-data/iwflist.ts';
    this.force = options.force || false;
  }

  apply(compiler: Compiler): void {
    compiler.hooks.beforeCompile.tapAsync('IwfListWebpackPlugin', (params, callback) => {
      console.log('IwfListWebpackPlugin: Checking IWF list...');
      
      const builder = new IwfListBuilder(this.sourceDir, this.outputFile);
      const info = builder.getInfo();
      
      // Only build if needed (unless forced)
      if (this.force || !info.upToDate) {
        console.log('IwfListWebpackPlugin: Building IWF list...');
        const success = builder.build(this.force);
        
        if (success) {
          console.log('IwfListWebpackPlugin: ✅ IWF list build completed');
        } else {
          console.error('IwfListWebpackPlugin: ❌ IWF list build failed');
          return callback(new Error('IWF list build failed'));
        }
      } else {
        console.log('IwfListWebpackPlugin: IWF list is up to date');
      }
      
      callback();
    });
  }
}

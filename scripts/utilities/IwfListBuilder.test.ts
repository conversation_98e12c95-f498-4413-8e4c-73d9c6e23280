import fs from 'fs';
import path from 'path';
import LZString from 'lz-string';
import { IwfListBuilder } from './IwfListBuilder';

// Mock fs module
jest.mock('fs');
const mockFs = fs as jest.Mocked<typeof fs>;

describe('IwfListBuilder', () => {
  const testSourceDir = '/test/src/_iwflist';
  const testOutputFile = '/test/src/mini-blocklist-data/iwflist.ts';
  const emptyStringHash = 'da39a3ee5e6b4b0d3255bfef95601890afd80709';

  let builder: IwfListBuilder;

  beforeEach(() => {
    jest.clearAllMocks();
    builder = new IwfListBuilder(testSourceDir, testOutputFile);
  });

  describe('findLatestJsonFile', () => {
    it('should return null if source directory does not exist', () => {
      mockFs.existsSync.mockReturnValue(false);
      
      const result = builder['findLatestJsonFile']();
      
      expect(result).toBeNull();
      expect(mockFs.existsSync).toHaveBeenCalledWith(testSourceDir);
    });

    it('should return the latest JSON file based on filename', () => {
      mockFs.existsSync.mockReturnValue(true);
      mockFs.readdirSync.mockReturnValue([
        'iwflist-2025-07-08.json',
        'iwflist-2025-07-09.json',
        'iwflist-2025-07-07.json',
        'other-file.txt'
      ] as any);

      const result = builder['findLatestJsonFile']();
      
      expect(result).toBe(path.join(testSourceDir, 'iwflist-2025-07-09.json'));
    });

    it('should return null if no JSON files found', () => {
      mockFs.existsSync.mockReturnValue(true);
      mockFs.readdirSync.mockReturnValue(['other-file.txt'] as any);

      const result = builder['findLatestJsonFile']();
      
      expect(result).toBeNull();
    });
  });

  describe('extractDateFromFilename', () => {
    it('should extract date from valid filename', () => {
      const result = builder['extractDateFromFilename']('iwflist-2025-07-09.json');
      expect(result).toBe('2025-07-09');
    });

    it('should return null for invalid filename', () => {
      const result = builder['extractDateFromFilename']('invalid-file.json');
      expect(result).toBeNull();
    });
  });

  describe('shouldSkipEncoding', () => {
    it('should return true if file contains skip comment', () => {
      mockFs.readFileSync.mockReturnValue('// NO_ENCODING_NEEDED\n[\n  "hash1",\n  "hash2"\n]');
      
      const result = builder['shouldSkipEncoding']('/test/file.json');
      
      expect(result).toBe(true);
    });

    it('should return false if file does not contain skip comment', () => {
      mockFs.readFileSync.mockReturnValue('[\n  "hash1",\n  "hash2"\n]');
      
      const result = builder['shouldSkipEncoding']('/test/file.json');
      
      expect(result).toBe(false);
    });

    it('should return false if file cannot be read', () => {
      mockFs.readFileSync.mockImplementation(() => {
        throw new Error('File not found');
      });
      
      const result = builder['shouldSkipEncoding']('/test/file.json');
      
      expect(result).toBe(false);
    });
  });

  describe('loadJsonData', () => {
    it('should load and validate valid JSON data', () => {
      const validHashes = [
        '001acc178f1a4375e554a60a39140da5d74c1b46',
        '00274bfbc11c32db095dfb7267f8f3dab98f205e'
      ];
      mockFs.readFileSync.mockReturnValue(JSON.stringify(validHashes));
      
      const result = builder['loadJsonData']('/test/file.json');
      
      expect(result).toEqual(validHashes);
    });

    it('should return null for invalid JSON', () => {
      mockFs.readFileSync.mockReturnValue('invalid json');
      
      const result = builder['loadJsonData']('/test/file.json');
      
      expect(result).toBeNull();
    });

    it('should return null if data is not an array', () => {
      mockFs.readFileSync.mockReturnValue('{"key": "value"}');
      
      const result = builder['loadJsonData']('/test/file.json');
      
      expect(result).toBeNull();
    });

    it('should return null if array contains invalid hash', () => {
      const invalidData = ['valid_hash_but_wrong_length', 'invalid'];
      mockFs.readFileSync.mockReturnValue(JSON.stringify(invalidData));
      
      const result = builder['loadJsonData']('/test/file.json');
      
      expect(result).toBeNull();
    });
  });

  describe('filterProblematicHashes', () => {
    it('should filter out empty string hash', () => {
      const data = [
        '001acc178f1a4375e554a60a39140da5d74c1b46',
        emptyStringHash,
        '00274bfbc11c32db095dfb7267f8f3dab98f205e'
      ];
      
      const result = builder['filterProblematicHashes'](data);
      
      expect(result).toEqual([
        '001acc178f1a4375e554a60a39140da5d74c1b46',
        '00274bfbc11c32db095dfb7267f8f3dab98f205e'
      ]);
    });

    it('should return original array if no problematic hashes found', () => {
      const data = [
        '001acc178f1a4375e554a60a39140da5d74c1b46',
        '00274bfbc11c32db095dfb7267f8f3dab98f205e'
      ];
      
      const result = builder['filterProblematicHashes'](data);
      
      expect(result).toEqual(data);
    });
  });

  describe('compressData', () => {
    it('should compress data using LZString', () => {
      const data = ['hash1', 'hash2'];
      
      const result = builder['compressData'](data);
      
      // Verify it can be decompressed back to original
      const decompressed = LZString.decompressFromBase64(result);
      expect(JSON.parse(decompressed!)).toEqual(data);
    });
  });

  describe('generateTsFileContent', () => {
    it('should generate valid TypeScript file content', () => {
      const compressedData = 'compressed_data_here';
      const sourceFile = '/test/iwflist-2025-07-09.json';
      const dataLength = 100;
      
      const result = builder['generateTsFileContent'](compressedData, sourceFile, dataLength);
      
      expect(result).toContain('const iwflist =');
      expect(result).toContain(compressedData);
      expect(result).toContain('iwflist-2025-07-09.json');
      expect(result).toContain('Entry count: 100');
      expect(result).toContain('export default iwflist;');
    });
  });

  describe('build', () => {
    it('should return false if no JSON files found', () => {
      mockFs.existsSync.mockReturnValue(false);
      
      const result = builder.build();
      
      expect(result).toBe(false);
    });

    it('should return true if encoding should be skipped', () => {
      mockFs.existsSync.mockReturnValue(true);
      mockFs.readdirSync.mockReturnValue(['iwflist-2025-07-09.json'] as any);
      mockFs.readFileSync.mockReturnValue('// NO_ENCODING_NEEDED\n[]');
      
      const result = builder.build();
      
      expect(result).toBe(true);
    });
  });

  describe('getInfo', () => {
    it('should return correct information', () => {
      mockFs.existsSync.mockReturnValue(true);
      mockFs.readdirSync.mockReturnValue(['iwflist-2025-07-09.json'] as any);
      mockFs.statSync.mockReturnValue({ mtime: new Date() } as any);

      const result = builder.getInfo();

      expect(result.sourceFile).toBe(path.join(testSourceDir, 'iwflist-2025-07-09.json'));
      expect(result.outputFile).toBe(testOutputFile);
      expect(typeof result.upToDate).toBe('boolean');
    });
  });
});

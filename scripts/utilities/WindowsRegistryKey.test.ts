import WindowsRegistryKey, * as helpers from './WindowsRegistryKey';

describe('WindowsRegistryKey', () => {
  // Note: We can't realistically unit test functions which actually touch the registry.

  describe('constructor()', () => {
    it('throws an Error if the specified path is empty', () => {
      expect(() => new WindowsRegistryKey('')).toThrowError();
    });

    it('throws an Error if the specified path contains an unrecognised root key', () => {
      expect(() => new WindowsRegistryKey('HKLMM\\Software\\Foobar')).toThrowError();
    });

    it('throws an Error if the specified path contains any non-printing characters', () => {
      expect(() => new WindowsRegistryKey('HKLM\\Software\b\\Foobar')).toThrowError();
    });

    it('stores the specified key path', () => {
      const key = new WindowsRegistryKey('HKCU\\testing\\123');
      expect(key.path).toEqual('HKCU\\testing\\123');
    });

    it('removes redundant backslashes from the specified path', () => {
      const key = new WindowsRegistryKey('HKCU\\\\\\testing\\\\123\\\\');
      expect(key.path).toEqual('HKCU\\testing\\123');
    });
  });

  describe('getSubKey()', () => {
    it('throws an Error if the specified sub-key path is empty', () => {
      const key = new WindowsRegistryKey('HKCU\\Software\\Example');
      expect(() => key.getSubKey('')).toThrowError();
    });

    it('throws an Error if the specified sub-key path contains any non-printing characters', () => {
      const key = new WindowsRegistryKey('HKCU\\Software\\Example');
      expect(() => key.getSubKey('Foo\b\\Bar')).toThrowError();
    });

    it('returns a new instance referring to the specified sub-key', () => {
      const key = new WindowsRegistryKey('HKCU\\Software\\Example');
      const subKey = key.getSubKey('Configuration\\Data');
      expect(subKey.path).toEqual('HKCU\\Software\\Example\\Configuration\\Data');
    });

    it('removes redundant backslashes from the specified resulting', () => {
      const key = new WindowsRegistryKey('HKCU\\Software\\Example');
      const subKey = key.getSubKey('\\\\Configuration\\\\\\Data\\');
      expect(subKey.path).toEqual('HKCU\\Software\\Example\\Configuration\\Data');
    });
  });

  describe('helpers', () => {
    describe('isNonPrintable()', () => {
      it('returns true if the specified string contains any non-printable characters', () => {
        expect(helpers.isNonPrintable('foo\bbar')).toBeTrue();
      });

      it('returns false if the specified string contains no non-printable characters', () => {
        expect(helpers.isNonPrintable('foo bar')).toBeFalse();
      });

      it('returns false if the specified string is empty', () => {
        expect(helpers.isNonPrintable('')).toBeFalse();
      });
    });

    describe('isValidValueName()', () => {
      it('returns false if the string contains any non printable characters', () => {
        expect(helpers.isValidValueName('Test\bValue')).toBeFalse();
      });

      it('returns true if the string only contains printable characters', () => {
        expect(helpers.isValidValueName('Test Value')).toBeTrue();
      });

      it('returns true if the string is empty', () => {
        expect(helpers.isValidValueName('')).toBeTrue();
      });

      it('allows backslashes', () => {
        expect(helpers.isValidValueName('Test\\Value')).toBeTrue();
      });
    });

    describe('isValidKeyPathFragment()', () => {
      it('returns false if the string contains any non printable characters', () => {
        expect(helpers.isValidKeyPathFragment('Software\b\\Example')).toBeFalse();
      });

      it('returns false if the string is empty', () => {
        expect(helpers.isValidKeyPathFragment('')).toBeFalse();
      });

      it('returns true if the string contains only printable characters', () => {
        expect(helpers.isValidKeyPathFragment('Software\\Example')).toBeTrue();
      });
    });

    describe('isValidAbsoluteKeyPath()', () => {
      it('returns false if the string contains any non printable characters', () => {
        expect(helpers.isValidAbsoluteKeyPath('HKLM\\Software\b\\Example')).toBeFalse();
      });

      it('returns false if the string is empty', () => {
        expect(helpers.isValidAbsoluteKeyPath('')).toBeFalse();
      });

      it('returns false if the string does not start with a recognised root key', () => {
        expect(helpers.isValidAbsoluteKeyPath('HKLMM\\Software\\Example')).toBeFalse();
      });

      it('returns true if the string starts with a recognised root key and contains only valid characters', () => {
        expect(helpers.isValidAbsoluteKeyPath('HKEY_CLASSES_ROOT\\Example')).toBeTrue();
        expect(helpers.isValidAbsoluteKeyPath('HKEY_CURRENT_USER\\Example')).toBeTrue();
        expect(helpers.isValidAbsoluteKeyPath('HKEY_LOCAL_MACHINE\\Example')).toBeTrue();
        expect(helpers.isValidAbsoluteKeyPath('HKEY_PERFORMANCE_DATA\\Example')).toBeTrue();
        expect(helpers.isValidAbsoluteKeyPath('HKEY_USERS\\Example')).toBeTrue();

        expect(helpers.isValidAbsoluteKeyPath('HKCR\\Example')).toBeTrue();
        expect(helpers.isValidAbsoluteKeyPath('HKCU\\Example')).toBeTrue();
        expect(helpers.isValidAbsoluteKeyPath('HKLM\\Example')).toBeTrue();
        expect(helpers.isValidAbsoluteKeyPath('HKU\\Example')).toBeTrue();
      });
    });

    describe('encodeMultiString()', () => {
      it('joins strings together with the PowerShell null character', () => {
        expect(helpers.encodeMultiString(['blah', 'foo', 'bar'])).toEqual('blah`0foo`0bar');
      });

      it('escapes any necessary characters in the input', () => {
        expect(helpers.encodeMultiString(['blah"blah"blah', '$foo', 'bar'])).toEqual(
          'blah`"blah`"blah`0`$foo`0bar',
        );
      });

      it('discards empty entries', () => {
        expect(helpers.encodeMultiString(['blah', '', 'foo', 'bar'])).toEqual('blah`0foo`0bar');
      });

      it('returns an empty string if the input array is empty', () => {
        expect(helpers.encodeMultiString([])).toEqual('');
      });
    });

    describe('isIntegerRegistryValueType()', () => {
      it('returns true for numerical types in PowerShell RegistryValueKind enum', () => {
        expect(helpers.isIntegerRegistryValueType('Dword')).toBeTrue();
        expect(helpers.isIntegerRegistryValueType('Qword')).toBeTrue();
      });

      it('returns true for numerical types in WinAPI format', () => {
        expect(helpers.isIntegerRegistryValueType('REG_DWORD')).toBeTrue();
        expect(helpers.isIntegerRegistryValueType('REG_QWORD')).toBeTrue();
      });

      it('is case insensitive', () => {
        expect(helpers.isIntegerRegistryValueType('dWoRd')).toBeTrue();
      });

      it('returns false for any unrecognised type', () => {
        expect(helpers.isIntegerRegistryValueType('blah')).toBeFalse();
      });
    });

    describe('isStringRegistryValueType()', () => {
      it('returns true for string types in PowerShell RegistryValueKind enum', () => {
        expect(helpers.isStringRegistryValueType('String')).toBeTrue();
        expect(helpers.isStringRegistryValueType('ExpandString')).toBeTrue();
      });

      it('returns true for string types in WinAPI format', () => {
        expect(helpers.isStringRegistryValueType('REG_SZ')).toBeTrue();
        expect(helpers.isStringRegistryValueType('REG_EXPAND_SZ')).toBeTrue();
      });

      it('is case insensitive', () => {
        expect(helpers.isStringRegistryValueType('sTrInG')).toBeTrue();
      });

      it('returns false for any unrecognised type', () => {
        expect(helpers.isStringRegistryValueType('blah')).toBeFalse();
      });
    });

    describe('isMultiStringRegistryValueType()', () => {
      it('returns true for multi-string types in PowerShell RegistryValueKind enum', () => {
        expect(helpers.isMultiStringRegistryValueType('MultiString')).toBeTrue();
      });

      it('returns true for multi-string types in WinAPI format', () => {
        expect(helpers.isMultiStringRegistryValueType('REG_MULTI_SZ')).toBeTrue();
      });

      it('is case insensitive', () => {
        expect(helpers.isMultiStringRegistryValueType('mUlTiStRiNg')).toBeTrue();
      });

      it('returns false for any unrecognised type', () => {
        expect(helpers.isMultiStringRegistryValueType('blah')).toBeFalse();
      });
    });
  });
});

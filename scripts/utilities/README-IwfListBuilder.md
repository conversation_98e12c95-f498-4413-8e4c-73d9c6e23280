# IWF List Builder Utility

This utility handles the encoding and processing of IWF (Internet Watch Foundation) list data from JSON files into compressed TypeScript files for use in the mini-blocklist system.

## Overview

The IWF List Builder processes JSON files containing SHA-1 hashes from the `src/_iwflist/` directory and generates a compressed TypeScript file at `src/mini-blocklist-data/iwflist.ts`. The data is compressed using LZ64 (LZString) compression to reduce file size.

## Features

- **Automatic Latest File Detection**: Finds the most recent JSON file based on filename date
- **Skip Comments**: Supports skip comments to prevent unnecessary encoding
- **Data Validation**: Validates SHA-1 hash format and filters problematic entries
- **Empty String Hash Protection**: Automatically filters out the problematic empty string hash
- **Compression**: Uses LZString.compressToBase64 for efficient compression
- **Version Information**: Adds comprehensive header information to generated files
- **Webpack Integration**: Can be integrated into the build process via webpack plugin

## File Structure

```
src/
├── _iwflist/                          # Source JSON files
│   ├── iwflist-2025-07-08.json       # Older version
│   └── iwflist-2025-07-09.json       # Latest version (auto-detected)
└── mini-blocklist-data/
    └── iwflist.ts                     # Generated compressed file
```

## Usage

### Command Line Interface

```bash
# Build the IWF list from latest JSON file
npm run build-iwf-list

# Force rebuild even if output is up to date
npm run build-iwf-list:force

# Show information about source and output files
npm run iwf-list:info

# Validate the current iwflist.ts file
npm run iwf-list:validate
```

### Direct Script Usage

```bash
# Build command
ts-node ./scripts/build-iwf-list.ts build [options]

# Info command
ts-node ./scripts/build-iwf-list.ts info [options]

# Validate command
ts-node ./scripts/build-iwf-list.ts validate [options]
```

### Options

- `-f, --force`: Force rebuild even if output is up to date
- `-s, --source <dir>`: Source directory containing JSON files (default: `src/_iwflist`)
- `-o, --output <file>`: Output TypeScript file (default: `src/mini-blocklist-data/iwflist.ts`)

### Programmatic Usage

```typescript
import { IwfListBuilder } from './scripts/utilities/IwfListBuilder';

const builder = new IwfListBuilder();

// Build the list
const success = builder.build();

// Get information
const info = builder.getInfo();
console.log(`Source: ${info.sourceFile}`);
console.log(`Up to date: ${info.upToDate}`);
```

## Skip Comments

To prevent encoding of a JSON file, add one of these comments at the beginning:

```json
// NO_ENCODING_NEEDED
[
  "hash1",
  "hash2"
]
```

Or:

```json
/* NO_ENCODING_NEEDED */
[
  "hash1", 
  "hash2"
]
```

Or:

```json
// SKIP_ENCODING
[
  "hash1",
  "hash2"
]
```

## JSON File Format

The JSON files should contain an array of SHA-1 hashes:

```json
[
  "001acc178f1a4375e554a60a39140da5d74c1b46",
  "00274bfbc11c32db095dfb7267f8f3dab98f205e",
  "0054b547eb0285133cc64f60ade6fcd3425c3d10"
]
```

### Requirements

- Must be a valid JSON array at the top level
- Each entry must be a 40-character hexadecimal string (SHA-1 hash)
- Case insensitive (both uppercase and lowercase accepted)

## Generated File Format

The generated TypeScript file includes comprehensive header information:

```typescript
// IWF List - Auto-generated from iwflist-2025-07-09.json
// Source file: /path/to/src/_iwflist/iwflist-2025-07-09.json
// Build date: 2025-07-17T10:30:00.000Z
// Data date: 2025-07-09
// Entry count: 3680
// Compression: LZ64 (LZString.compressToBase64)
// 
// This file contains compressed IWF (Internet Watch Foundation) blocklist data.
// The data is compressed using LZString and should be decompressed using:
// LZString.decompressFromBase64(iwflist)

const iwflist =
  'compressed_data_here...';

export default iwflist;
```

## Webpack Integration

To integrate with webpack builds, add the plugin to your webpack configuration:

```typescript
import { IwfListWebpackPlugin } from './scripts/utilities/IwfListWebpackPlugin';

// In your webpack config
plugins: [
  new IwfListWebpackPlugin({
    sourceDir: 'src/_iwflist',
    outputFile: 'src/mini-blocklist-data/iwflist.ts',
    force: false
  }),
  // ... other plugins
]
```

## Data Processing

The utility performs several data processing steps:

1. **File Detection**: Finds the latest JSON file based on filename date
2. **Skip Check**: Checks for skip comments in the file
3. **Up-to-date Check**: Compares file modification times (unless forced)
4. **Data Loading**: Loads and parses JSON data
5. **Validation**: Validates SHA-1 hash format for all entries
6. **Filtering**: Removes problematic hashes (e.g., empty string hash)
7. **Compression**: Compresses data using LZString.compressToBase64
8. **Generation**: Creates TypeScript file with header information

## Error Handling

The utility includes comprehensive error handling:

- Invalid JSON format
- Non-array data structure
- Invalid SHA-1 hash format
- File system errors
- Compression failures

## Testing

Run the tests with:

```bash
npm test -- scripts/utilities/IwfListBuilder.test.ts
```

The test suite covers:

- File detection logic
- Data validation
- Compression/decompression
- Error handling
- Skip comment detection

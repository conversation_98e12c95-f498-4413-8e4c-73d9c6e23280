import * as child_process from 'child_process';
import * as fs from 'fs';
import * as fsPromises from 'fs/promises';
import * as os from 'os';
import * as path from 'path';
import * as util from 'util';

import * as Windows from './Windows';
import WindowsRegistryKey from './WindowsRegistryKey';

/**
 * Supported provisioning methods for macOS.
 */
export enum ProvisioningMethod {
  AUTO = 'auto',
  DEFAULTS = 'defaults',
  DSCL = 'dscl',
}

/**
 * Describes the provisioning values which the user can specify.
 * This defines the structure we should expect in the provisioning file as well.
 */
export interface Data {
  serial?: string;
  tenant?: string;
  defaultUser?: string;
  disableMiniFilter?: boolean;
  mode?: string;
  method?: ProvisioningMethod | string; // macOS only: ProvisioningMethod enum values
}

/**
 * Provision the specified extension for the current platform.
 *
 * @param extensionId The ID of the extension to provision.
 * @param data The provisioning data to use.
 * @returns A promise which resolves if the extension was provisioned successfully. It will reject
 *  with an Error if provisioning failed.
 */
export const provision = async (extensionId: string, data: Data): Promise<void> => {
  const platform = os.platform();
  if (platform === 'win32') {
    await provisionWindows(extensionId, data);
    return;
  }

  if (platform === 'darwin') {
    await provisionMacOs(extensionId, data);
    return;
  }

  if (platform === 'linux') {
    await provisionLinux(extensionId, data);
    return;
  }

  throw new Error(`Platform "${platform}" is not supported.`);
};

/**
 * Deprovision the specified extension for the current platform.
 *
 * @param extensionId The ID of the extension to deprovision.
 * @returns A promise which resolve if the extension was deprovisioned successfully. It will reject
 *  with an Error if deprovisioning failed.
 */
export const deprovision = async (extensionId: string): Promise<void> => {
  // If the extension ID is empty then the policy won't properly apply
  if (extensionId === '') {
    throw new Error('Extension ID must not be empty.');
  }

  const platform = os.platform();
  if (platform === 'win32') {
    await deprovisionWindows(extensionId);
    return;
  }

  if (platform === 'darwin') {
    await deprovisionMacOs(extensionId);
    return;
  }

  if (platform === 'linux') {
    await deprovisionLinux();
    return;
  }

  throw new Error(`Platform "${platform}" is not supported.`);
};

// -------------------------------------------------------------------------------------------------
// Windows.

const windowsServiceName = 'sw-uc-desktop-client';

const makeChromeExtensionRegistryKey = (extensionId: string): WindowsRegistryKey => {
  if (extensionId === '') {
    throw new Error('Extension ID must not be empty.');
  }

  return new WindowsRegistryKey(
    `HKLM\\SOFTWARE\\Policies\\Google\\Chrome for Testing\\3rdParty\\extensions\\${extensionId}`,
  );
};

const makeEdgeExtensionRegistryKey = (extensionId: string): WindowsRegistryKey => {
  if (extensionId === '') {
    throw new Error('Extension ID must not be empty.');
  }

  return new WindowsRegistryKey(
    `HKLM\\SOFTWARE\\Policies\\Microsoft\\Edge\\3rdParty\\extensions\\${extensionId}`,
  );
};

const makeUnifiedClientRegistryKey = (): WindowsRegistryKey => {
  return new WindowsRegistryKey('HKLM\\SOFTWARE\\Policies\\Smoothwall\\UnifiedClient');
};

/**
 * Write registry entries to provision the specified extension on Windows.
 * For standalone mode, this will write the details to the extension's managed policy in the Windows
 *  registry. This will be done for both Edge and Chrome browsers.
 * For native mode, this will write the details to the native daemon policy in the Windows registry.
 * In both cases, this will overwrite any existing provisioning values for the specified mode. It
 *  will not remove any existing values for the other mode. It is the caller's responsibility to
 *  deprovision the other mode if necessary.
 *
 * @param extensionId The ID of the extension being provisioned. This must not be empty.
 * @param data The provisioning data to use.
 * @returns A promise which resolves when provisioning has been completed successfully. It will
 *  reject with an error if it fails, e.g. due to lack of permission.
 *
 * @note The calling process needs to be running with admin privileges, otherwise this will fail.
 */
const provisionWindows = async (extensionId: string, data: Data): Promise<void> => {
  // If the extension ID is empty then we might accidentally overwrite the wrong registry key.
  if (extensionId === '') {
    throw new Error('Extension ID must not be empty.');
  }

  switch (data.mode) {
    case 'standalone':
      await provisionWindowsChromiumBrowserStandalone(
        makeChromeExtensionRegistryKey(extensionId).getSubKey('policy'),
        data,
      );

      await provisionWindowsChromiumBrowserStandalone(
        makeEdgeExtensionRegistryKey(extensionId).getSubKey('policy'),
        data,
      );
      break;

    case 'native': {
      // If standalone mode is provisioned then it takes priority over native mode. Ensure any
      //  standalone mode provisioning is removed.
      await deprovisionWindows(extensionId);

      // Check if the native client exists on the system.
      const hasService = await Windows.doesServiceExist(windowsServiceName);
      if (hasService && (await Windows.isServiceRunning(windowsServiceName))) {
        console.log(`Stopping service: ${windowsServiceName}`);
        await Windows.stopService(windowsServiceName);
      }

      // We'll only overwrite necessary values. Leave things like IpcPort alone in case they've been
      //  modified for a particular reason.
      const key = makeUnifiedClientRegistryKey();
      await key.createKey(false);

      await key.setValue('SerialId', data.serial ?? '');
      await key.setValue('TenantId', data.tenant ?? '');

      await addExtensionIdToRegistry(key, 'ChromeExtensionId', extensionId);
      await addExtensionIdToRegistry(key, 'EdgeExtensionId', extensionId);

      if (await key.hasValue('EnableAzureAd')) {
        console.log('Keeping existing Azure AD setting in the registry.');
      } else {
        console.log('Inferring Azure AD setting from the current username.');
        await key.setValue('EnableAzureAd', (await Windows.isAzureAd()) ? 1 : 0);
      }

      // Restart the service so that it picks up the provisioning changes.
      if (hasService) {
        console.log(`Starting service: ${windowsServiceName}`);
        await Windows.startService(windowsServiceName);
      } else {
        console.log('');
        console.log('***** WARNING *****');
        console.log(`The native client service (${windowsServiceName}) could not be found.`);
        console.log('You will need to install it before the extension will work in native mode.');
        console.log('You can download it from the "Unified Client" section on one of these pages:');
        console.log('');
        console.log('- DEV: https://software-dev.smoothwall.cloud/');
        console.log('- QA:  https://software-qa.smoothwall.cloud/');
        console.log('- PRD: https://software.smoothwall.com/');
        console.log('**********');
        console.log('');
      }
      break;
    }

    default:
      throw new Error('Unrecognised operating mode. Expected "standalone" or "native".');
  }
};

/**
 * Provision the extension to run standalone in a Chromium browser on Windows.
 *
 * @param extensionPolicyKey The registry key where the extension's policies should be stored.
 * @param data The provisioning data to use.
 * @returns A promise which resolves when provisioning has been completed successfully. It will
 *  reject with an error if it fails, e.g. due to lack of permission.
 */
const provisionWindowsChromiumBrowserStandalone = async (
  extensionPolicyKey: WindowsRegistryKey,
  data: Data,
): Promise<void> => {
  // Destroy any previous policies which exist for the extension.
  await extensionPolicyKey.createKey(true);

  // Older policies are under a "Smoothwall" sub-key.
  const smoothwallKey = await extensionPolicyKey.createSubKey('Smoothwall');
  await smoothwallKey.setValue('Serial', data.serial ?? '');
  if (data.tenant !== undefined) {
    await smoothwallKey.setValue('TenantId', data.tenant ?? '');
  }
  await smoothwallKey.setValue('ForceOS', 'chromeos');

  // Newer policies are under the main policy key.
  if (data.defaultUser !== undefined) {
    await extensionPolicyKey.setValue('DefaultUser', data.defaultUser ?? '');
  }
  if (data.disableMiniFilter !== undefined) {
    await extensionPolicyKey.setValue('DisableMiniFilter', data.disableMiniFilter ? 1 : 0);
  }
};

/**
 * Remove any existing provisioning information for the specified extension.
 * This will remove all registry entries relating to standalone mode for the specified extension. It
 *  will do this for Chrome and Edge browsers.
 * It will not remove the serial and tenant IDs from the native provisioning values because that
 *  would prevent the daemon from running.
 *
 * @param extensionId The ID of the extension to deprovision.
 * @returns A promise which resolves if deprovisioning was completed successfully. It will reject
 *  with an Error if deprovisioning failed.
 */
const deprovisionWindows = async (extensionId: string): Promise<void> => {
  // Remove all standalone policies for the extension.
  await makeChromeExtensionRegistryKey(extensionId).deleteKey();
  await makeEdgeExtensionRegistryKey(extensionId).deleteKey();

  // Remove the extension ID from the daemon registry values, if it's present.
  const key = makeUnifiedClientRegistryKey();
  await removeExtensionIdFromRegistry(key, 'ChromeExtensionId', extensionId);
  await removeExtensionIdFromRegistry(key, 'EdgeExtensionId', extensionId);
};

/**
 * Add an extension ID to the native client provisioning, alongside any existing IDs.
 *
 * @param key The registry key where the provisioning data is stored.
 * @param name The name of the registry value containing extension IDs.
 * @param extensionId The ID of the extension to add to the registry value.
 */
const addExtensionIdToRegistry = async (
  key: WindowsRegistryKey,
  name: string,
  extensionId: string,
): Promise<void> => {
  const oldValue = await key.getValue(name);

  if (oldValue === extensionId || (Array.isArray(oldValue) && oldValue.includes(extensionId))) {
    // The value already contains our extension ID. There's nothing to do.
    return;
  }

  if (typeof oldValue === 'string') {
    // The value contains another extension ID as a single string. Turn it into a multi-string and
    //  add our extension ID.
    await key.setValue(name, [oldValue, extensionId]);
    return;
  }

  if (Array.isArray(oldValue)) {
    // The value may contain any number of other extension IDs. Add our extension ID to them.
    await key.setValue(name, [...oldValue, extensionId]);
    return;
  }

  // If we reach here, the value either doesn't exist, or contains invalid data. Create/replace it
  //  with our extension ID.
  await key.setValue(name, extensionId);
};

/**
 * Remove an extension ID from the native client provisioning, leaving other IDs alone.
 *
 * @param key The registry key where the provisioning data is stored.
 * @param name The name of the registry value containing extension IDs.
 * @param extensionId The ID of the extension to remove from the registry value.
 */
const removeExtensionIdFromRegistry = async (
  key: WindowsRegistryKey,
  name: string,
  extensionId: string,
): Promise<void> => {
  const oldValue = await key.getValue(name);
  if (oldValue === undefined) {
    // The value doesn't exist. Nothing to do.
    return;
  }

  if (oldValue === extensionId) {
    // The value only contains our extension ID. Delete the whole thing.
    await key.deleteValue(name);
    return;
  }

  if (Array.isArray(oldValue) && oldValue.includes(extensionId)) {
    // Our extension ID is potentially one of multiple. Just remove ours and leave the rest alone.
    await key.setValue(
      name,
      oldValue.filter((id) => id !== extensionId),
    );
  }
};

// -------------------------------------------------------------------------------------------------
// Linux.

const provisionLinux = async (extensionId: string, data: Data): Promise<void> => {
  // If the extension ID is empty then the policy won't properly apply
  if (extensionId === '') {
    throw new Error('Extension ID must not be empty.');
  }

  switch (data.mode) {
    case 'standalone':
      fs.mkdirSync('/etc/opt/chrome/policies/managed/', { recursive: true });
      fs.writeFileSync(
        '/etc/opt/chrome/policies/managed/smoothwall_policies.json',
        JSON.stringify(
          {
            '3rdparty': {
              extensions: {
                [extensionId]: {
                  Smoothwall: {
                    Serial: data.serial ?? '',
                    TenantId: data.tenant ?? '',
                    ForceOS: 'chromeos',
                  },
                  DisableMiniFilter: data.disableMiniFilter ?? '',
                  DefaultUser: data.defaultUser ?? '',
                },
              },
            },
          },
          null,
          2,
        ),
      );
      break;

    case 'native':
      throw new Error('native provisioning script has not been implemented yet');

    default:
      throw new Error('Unrecognised operating mode. Expected "standalone" or "native".');
  }
};

const deprovisionLinux = async (): Promise<void> => {
  fs.unlinkSync('/etc/opt/chrome/policies/managed/smoothwall_policies.json');
};

// -------------------------------------------------------------------------------------------------
// macOS.

// Default path of the configuration script created by the native client installer.
const macOsConfigurationScriptPath =
  '/Library/Application Support/Smoothwall/Unified Client/configure.sh';

// Returns the content of a plist file containing extension policy for standalone mode on macOS.
const generateMacOsStandalonePolicy = (extensionId: string, data: Data): string => {
  return `
    <?xml version="1.0" encoding="UTF-8"?>
    <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
    <plist version="1.0">
      <dict>
        <key>com.google.Chrome.extensions.${extensionId}</key>
        <dict>
    
          <key>Smoothwall</key>
          <dict>
            <key>state</key>
            <string>always</string>
            <key>value</key>
            <dict>
              <key>Serial</key>
              <string>${data.serial ?? ''}</string>
              <key>TenantId</key>
              <string>${data.tenant ?? ''}</string>
              <key>ForceOS</key>
              <string>chromeos</string>
            </dict>
          </dict>
    
          <key>DisableMiniFilter</key>
          <dict>
            <key>state</key>
            <string>always</string>
            <key>value</key>
            ${data.disableMiniFilter === true ? '<true/>' : '<false/>'}
          </dict>
    
          <key>DefaultUser</key>
          <dict>
            <key>state</key>
            <string>always</string>
            <key>value</key>
            <string>${data.defaultUser ?? ''}</string>
          </dict>
        </dict>
      
      </dict>
    </plist>
  `;
};

const exec = util.promisify(child_process.exec);

// Try to determine the name of the user actually running the current command.
const getMacOsActualUsername = (): string => {
  // If the user is running the command as sudo, try to figure out who invoked the sudo command.
  if (process.env.SUDO_USER !== undefined && process.env.SUDO_USER !== '') {
    return process.env.SUDO_USER;
  }

  // Fall-back on the normal way of getting the username. This will probably just return 'root'.
  return os.userInfo().username;
};

/**
 * Provision the extension using the defaults command approach.
 * This method is less likely to be blocked by security software like Crowdstrike.
 */
const provisionMacOsUsingDefaults = async (extensionId: string, data: Data): Promise<void> => {
  const chromeExtensionDomain = `com.google.Chrome.extensions.${extensionId}`;

  console.debug(`Setting Chrome extension policies for domain: ${chromeExtensionDomain}`);

  // Set the Smoothwall configuration
  const smoothwallConfig = {
    Serial: data.serial ?? '',
    TenantId: data.tenant ?? '',
    ForceOS: 'chromeos',
  };

  // Convert the config to a plist-compatible format for defaults
  const smoothwallPlistArgs = Object.entries(smoothwallConfig)
    .map(([key, value]) => `${key} -string "${value}"`)
    .join(' ');

  await exec(`defaults write "${chromeExtensionDomain}" Smoothwall -dict ${smoothwallPlistArgs}`);
  console.debug('Set Smoothwall configuration');

  // Set the DefaultUser configuration if provided
  if (data.defaultUser !== undefined && data.defaultUser !== '') {
    await exec(
      `defaults write "${chromeExtensionDomain}" DefaultUser -string "${data.defaultUser}"`,
    );
    console.debug('Set DefaultUser configuration');
  }

  // Verify the settings were applied
  try {
    const { stdout } = await exec(`defaults read "${chromeExtensionDomain}"`);
    console.debug('Current extension preferences:', stdout);
  } catch (error) {
    console.warn('Could not verify extension preferences:', error);
  }

  console.debug('Extension provisioning completed using defaults command');
};

/**
 * Provision the extension using the original dscl command approach.
 * This method may be blocked by security software like Crowdstrike.
 */
const provisionMacOsUsingDscl = async (extensionId: string, data: Data): Promise<void> => {
  // Before we can import policy, we need to ensure a suitable node exists to import it into.
  console.debug('Creating dscl node for local computer (if it does not already exist)');
  const nodeName = '/Computers/local_computer';

  // Get UUID and ethernet address properly
  const { stdout: uuid } = await exec('uuidgen');
  const { stdout: ethernet } = await exec("ifconfig en0 | awk '/ether/ {print $2}'");

  // First check if the node exists
  try {
    await exec(`dscl /Local/Default -read "${nodeName}"`);
    console.debug('Computer node already exists');
  } catch (error) {
    // Node doesn't exist, create it
    console.debug('Creating new computer node');
    await exec(`dscl /Local/Default -create "${nodeName}"`);
    await exec(`dscl /Local/Default -create "${nodeName}" RealName "Local Computer"`);
    await exec(`dscl /Local/Default -create "${nodeName}" GeneratedUID "${uuid.trim()}"`);
    await exec(`dscl /Local/Default -create "${nodeName}" ENetAddress "${ethernet.trim()}"`);
  }

  // Write the provisioning data to a temporary file.
  // We don't need the file after it's been imported.
  const policyFilePath = path.join(os.tmpdir(), `swcf-policy-${extensionId}.plist`);
  console.debug(`Generating policy file: ${policyFilePath}`);
  await fsPromises.writeFile(policyFilePath, generateMacOsStandalonePolicy(extensionId, data));

  try {
    // Import the policy file to mcx.
    console.debug('Importing policy file');
    await exec(`dscl /Local/Default -mcximport "${nodeName}" "${policyFilePath}"`);

    // We need to explicitly propagate the change to the current user.
    const username = getMacOsActualUsername();
    console.debug('Propagating policy to user: ' + username);
    await exec(`mcxrefresh -n ${username}`);

    // Wait a moment for the policy to be fully imported
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Try to refresh the computer policy, but don't fail if it doesn't work
    try {
      await exec(`dscl /Local/Default -read "${nodeName}"`);
      console.debug('Refreshing computer policy');
      await exec('mcxrefresh -n /Computers/local_computer');
    } catch (error) {
      // This is expected on some macOS versions - the computer policy refresh isn't critical
      console.debug('Computer policy refresh skipped - this is normal on some macOS versions');
    }
  } finally {
    // Clean up the temporary file
    try {
      await fsPromises.unlink(policyFilePath);
    } catch (error) {
      console.warn('Failed to clean up temporary policy file:', error);
    }
  }

  console.debug('Extension provisioning completed using dscl command');
};

const provisionMacOs = async (extensionId: string, data: Data): Promise<void> => {
  // If the extension ID is empty then the policy won't properly apply
  if (extensionId === '') {
    throw new Error('Extension ID must not be empty.');
  }

  if (process.getuid?.() !== 0) {
    throw new Error('This script must be executed as root/sudo.');
  }

  switch (data.mode) {
    case 'standalone': {
      const method = data.method ?? ProvisioningMethod.AUTO;

      if (method === ProvisioningMethod.DEFAULTS) {
        console.debug('Using defaults command (forced by --method parameter)');
        await provisionMacOsUsingDefaults(extensionId, data);
        console.log('Successfully provisioned using defaults command');
      } else if (method === ProvisioningMethod.DSCL) {
        console.debug('Using dscl command (forced by --method parameter)');
        await provisionMacOsUsingDscl(extensionId, data);
        console.log('Successfully provisioned using dscl command');
      } else {
        // Auto mode: try defaults first, fallback to dscl
        try {
          console.debug('Attempting to provision using defaults command (recommended)');
          await provisionMacOsUsingDefaults(extensionId, data);
          console.log('Successfully provisioned using defaults command');
        } catch (error) {
          console.warn('Failed to provision using defaults command:', error);
          console.debug('Falling back to dscl method...');

          console.debug('Attempting to provision using dscl command (fallback)');
          await provisionMacOsUsingDscl(extensionId, data);
          console.log('Successfully provisioned using dscl command');
        }
      }
      break;
    }

    case 'native': {
      // If standalone mode is provisioned then it takes priority over native mode. Ensure any
      //  standalone mode provisioning is removed.
      await deprovisionMacOs(extensionId);

      if (!fs.existsSync(macOsConfigurationScriptPath)) {
        console.log('');
        console.log('***** ERROR *****');
        console.log(`The native client configuration script could not be found.`);
        console.log('You will need to install the client before you can provision the extension.');
        console.log('You can download it from the "Unified Client" section on one of these pages:');
        console.log('');
        console.log('- DEV: https://software-dev.smoothwall.cloud/');
        console.log('- QA:  https://software-qa.smoothwall.cloud/');
        console.log('- PRD: https://software.smoothwall.com/');
        console.log('**********');
        console.log('');
        throw new Error('Native client not found.');
      }

      console.debug('Running the configuration script for the native client.');
      await exec(
        `"${macOsConfigurationScriptPath}" install ` +
          `--serial "${data.serial ?? ''}" ` +
          (data.tenant === undefined ? '' : `--tenant "${data.tenant}" `) +
          `--chrome-extension-id "${extensionId}" ` +
          '--env qa', // <-- this only determines where App Insights logs go
      );
      break;
    }

    default:
      throw new Error('Unrecognised operating mode. Expected "standalone" or "native".');
  }
};

const deprovisionMacOs = async (extensionId: string): Promise<void> => {
  if (process.getuid?.() !== 0) {
    throw new Error('This script must be executed as root/sudo.');
  }

  // Try to remove using defaults command first
  try {
    console.debug('Attempting to deprovision using defaults command');
    await deprovisionMacOsUsingDefaults(extensionId);
    console.debug('Successfully deprovisioned using defaults command');
  } catch (error) {
    console.warn('Failed to deprovision using defaults command:', error);
  }

  // Also try to remove using dscl command (in case it was provisioned that way)
  try {
    console.debug('Attempting to deprovision using dscl command');
    await deprovisionMacOsUsingDscl(extensionId);
    console.debug('Successfully deprovisioned using dscl command');
  } catch (error) {
    console.warn('Failed to deprovision using dscl command:', error);
  }

  // Remove the native client provisioning and stop it from running.
  if (fs.existsSync(macOsConfigurationScriptPath)) {
    await exec(`"${macOsConfigurationScriptPath}" uninstall`);
  }
};

/**
 * Remove extension policies using the defaults command.
 */
const deprovisionMacOsUsingDefaults = async (extensionId: string): Promise<void> => {
  const chromeExtensionDomain = `com.google.Chrome.extensions.${extensionId}`;

  console.debug(`Removing Chrome extension policies for domain: ${chromeExtensionDomain}`);

  // Remove the entire extension domain from defaults
  await exec(`defaults delete "${chromeExtensionDomain}"`);

  console.debug('Extension deprovisioning completed using defaults command');
};

/**
 * Remove extension policies using the dscl command.
 */
const deprovisionMacOsUsingDscl = async (extensionId: string): Promise<void> => {
  // Remove the policy we've imported for the specified extension.
  console.debug('Removing extension policy using dscl.');
  try {
    await exec(
      `dscl /Local/Default -mcxdelete /Computers/local_computer "com.google.Chrome.extensions.${extensionId}"`,
    );
  } catch (e: any) {
    // The command always fails if the policy didn't exist.
    console.warn('Extension policy removal failed: ', e);
  }

  // We need to explicitly propagate the change to the current user.
  const username = getMacOsActualUsername();
  console.debug('Propagating policy removal to user: ' + username);
  await exec(`mcxrefresh -n ${username}`);

  console.debug('Extension deprovisioning completed using dscl command');
};

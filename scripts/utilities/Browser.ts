import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';

import {
  <PERSON>rows<PERSON> as P<PERSON>peteer<PERSON><PERSON><PERSON>,
  computeExecutablePath,
  detectBrowserPlatform,
  getInstalledBrowsers,
  install,
  resolveBuildId,
} from '@puppeteer/browsers';

/**
 * Configuration data used when launching Edge.
 * This tries to skip the First Run Experience (which invites you to migrate data), and prevents it
 *  from automatically signing you in.
 */
export const edgeConfigData = `
{
 "fre": {
   "autoimport_spartan_non_visible_item_completed": true,
   "autoimport_spartan_visible_item_completed": true,
   "has_user_completed_fre": true,
   "has_user_seen_fre": true
 },
 "profile": {
   "info_cache": {
     "Default": {
       "active_time": **********.032277,
       "background_apps": false,
       "edge_account_first_name": "",
       "edge_account_last_name": "",
       "edge_account_oid": "",
       "edge_account_sovereignty": 0,
       "edge_account_tenant_id": "",
       "edge_account_type": 0,
       "edge_kids_mode": false,
       "edge_test_on_premises": false,
       "edge_wam_aad_for_app_account_type": 0,
       "gaia_id": "",
       "is_consented_primary_account": false,
       "is_ephemeral": true,
       "is_guest": true,
       "is_using_default_avatar": true,
       "is_using_default_name": true,
       "managed_user_id": "",
       "metrics_bucket_index": 1,
       "name": "Profile 1",
       "user_name": ""
     }
   },
   "last_active_profiles": ["Default"]
 },
 "profiles": {
   "edge_implicitly_signed_in": [
     {
       "edge_account_type": 0,
       "id": ""
     }
   ],
   "edge_sso_info": {
     "aad_sso_algo_state": 1,
     "first_profile_key": "Default"
   }
 }
}
`;

/**
 * Search for the path of an Edge browser executable.
 * This will search various common installation paths, and return the first one it finds which is
 * executable by the current user.
 *
 * @returns Returns the absolute path of the Edge browser executable, if one was found. Returns
 *  undefined if no suitable or accessible Edge executable could be found.
 */
export const findBrowserExecutable = (): string | undefined => {
  const browserPathsToSearch: string[] = [];

  if (os.platform() === 'win32') {
    const relativePath = 'Microsoft\\Edge\\Application\\msedge.exe';

    // The browsers are typically installed in one of these system folders on Windows:
    const systemDirectories: Array<string | undefined> = [
      process.env.ProgramFiles,
      process.env['ProgramFiles(x86)'],
      process.env.LocalAppData,
      process.env.AppData,
    ];

    // Generate every combination of system directory and browser path.
    for (const systemDirectory of systemDirectories) {
      if (systemDirectory !== undefined) {
        browserPathsToSearch.push(path.join(systemDirectory, relativePath));
      }
    }
  } else if (os.platform() === 'darwin') {
    // TODO: Also search the per-user installation locations.
    browserPathsToSearch.push('/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge');
  } else {
    throw new Error(`Platform not supported: ${os.platform()}`);
  }

  // Try each browser path until we find one which we can execute.
  for (const browserPath of browserPathsToSearch) {
    try {
      // This will throw if we can't execute the file:
      fs.accessSync(browserPath, fs.constants.X_OK);
      return browserPath;
    } catch (e) {
      // Ignore the error and try another browser path.
    }
  }

  return undefined;
};

/**
 * Default cache directory for Chrome for Testing installations.
 * This will be in the project root under .chrome-for-testing-cache
 */
const getDefaultCacheDir = (): string => {
  return path.resolve(__dirname, '..', '..', '.chrome-for-testing-cache');
};

/**
 * Ensures Chrome for Testing is installed and returns the path to its executable.
 * This function will automatically download and install Chrome for Testing if it's not already present.
 *
 * @param cacheDir Optional custom cache directory for Chrome for Testing installations.
 *                 If not provided, uses a default directory in the project root.
 * @param buildId Optional specific Chrome version to install. If not provided, installs the stable version.
 * @returns Promise that resolves to the absolute path of the Chrome for Testing executable.
 * @throws {Error} If Chrome for Testing cannot be installed or the executable cannot be found.
 */
export const ensureChromeForTestingInstalled = async (
  cacheDir?: string,
  buildId?: string,
): Promise<string> => {
  const actualCacheDir = cacheDir ?? getDefaultCacheDir();
  const platform = detectBrowserPlatform();

  if (platform === undefined) {
    throw new Error(`Unsupported platform: ${os.platform()}`);
  }

  try {
    // Check if Chrome for Testing is already installed
    const installedBrowsers = await getInstalledBrowsers({ cacheDir: actualCacheDir });
    const chromeInstallation = installedBrowsers.find(
      (browser) => browser.browser === PuppeteerBrowser.CHROME,
    );

    let executablePath: string;

    if (chromeInstallation !== undefined && buildId === undefined) {
      // Use existing installation if no specific version requested
      executablePath = computeExecutablePath({
        browser: PuppeteerBrowser.CHROME,
        buildId: chromeInstallation.buildId,
        cacheDir: actualCacheDir,
      });
    } else {
      // Install Chrome for Testing
      console.log('Installing Chrome for Testing...');

      // Resolve the build ID if not provided or if 'stable' is requested
      let resolvedBuildId = buildId;
      if (resolvedBuildId === undefined || resolvedBuildId === 'stable') {
        resolvedBuildId = await resolveBuildId(PuppeteerBrowser.CHROME, platform, 'stable');
      }

      const installedBrowser = await install({
        browser: PuppeteerBrowser.CHROME,
        buildId: resolvedBuildId,
        cacheDir: actualCacheDir,
        platform,
      });

      executablePath = installedBrowser.executablePath;
      console.log(`Chrome for Testing installed at: ${executablePath}`);
    }

    // Verify the executable exists and is accessible
    try {
      fs.accessSync(executablePath, fs.constants.X_OK);
      return executablePath;
    } catch (e) {
      throw new Error(`Chrome for Testing executable not accessible: ${executablePath}`);
    }
  } catch (error) {
    throw new Error(`Failed to ensure Chrome for Testing: ${error}`);
  }
};

/**
 * Enhanced browser executable finder that uses Chrome for Testing when Chrome is requested.
 * This function replaces the legacy findBrowserExecutable for Chrome, while maintaining
 * backward compatibility for Edge and other browsers.
 *
 * @param includeChrome If true, Chrome for Testing will be installed and used.
 * @param includeEdge If true, typical Edge installation paths will be included in the search.
 * @param cacheDir Optional custom cache directory for Chrome for Testing installations.
 * @param buildId Optional specific Chrome version to install.
 * @returns Promise that resolves to the absolute path of the browser executable, or undefined if not found.
 */
export const findBrowserExecutableAsync = async (
  includeChrome: boolean,
  includeEdge: boolean,
  cacheDir?: string,
  buildId?: string,
): Promise<string | undefined> => {
  // If no browser was specified then allow any of them.
  // We'll try Chrome for Testing first, then fall back to Edge.
  if (!includeChrome && !includeEdge) {
    includeChrome = true;
    includeEdge = true;
  }

  // Try Chrome for Testing first if requested
  if (includeChrome) {
    try {
      return await ensureChromeForTestingInstalled(cacheDir, buildId);
    } catch (error) {
      console.warn(`Failed to use Chrome for Testing: ${error}`);
      // If Chrome for Testing fails and we're not specifically requesting Chrome only,
      // fall through to try Edge
      if (!includeEdge) {
        return undefined;
      }
    }
  }

  // Fall back to Edge using the original logic
  if (includeEdge) {
    return findBrowserExecutable();
  }

  return undefined;
};

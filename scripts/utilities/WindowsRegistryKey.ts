import { executePowerShell, escapePowerShellArgument } from './Windows';

/**
 * An object-oriented wrapper around some basic functions to read and write the Windows registry.
 * An instance of this class refers to a specific registry key, and provides various ways to operate
 *  on it.
 *
 * Internally, this class uses PowerShell commands to access the registry. This means it can only
 *  handle simple data types, such as strings and numbers. Currently, it also doesn't provide any
 *  access control mechanisms. It will operate with the default permissions of the running process.
 *
 * @note This class does not store any state information about the keys or values stored in the
 *  registry. The data could be modified while an instance of the class exists.
 */
export default class WindowsRegistryKey {
  // -----------------------------------------------------------------------------------------------
  // Construction.

  /**
   * Construct a new instance referring to the specified registry key on the local computer.
   * This does not check that the key actually exists or is accessible. The caller will need to call
   *  a method afterwards to do that, e.g keyExists().
   *
   * @param keyPath The absolute path of the registry key to refer to, including the root key, e.g.
   *  HKLM.
   * @throws {Error} The specified key path is invalid.
   */
  public constructor(keyPath: string) {
    // Condense multiple consecutive backslashes down to just one in case the caller was
    //  over-zealous with escaping. Also remove any unnecessary trailing backslashes.
    keyPath = keyPath.replace(/\\+/g, '\\').replace(/\\+$/, '');

    if (!isValidAbsoluteKeyPath(keyPath)) {
      throw new Error('Invalid absolute key path.');
    }

    this.path = keyPath;
    this._escapedPath = `"registry::${escapePowerShellArgument(keyPath)}"`;
  }

  // -----------------------------------------------------------------------------------------------
  // Key operations.

  /**
   * Check if the registry key referenced by this instance exists.
   * This does not check whether the contents of the key can actually be accessed.
   *
   * @returns A promise that resolves to true if the key exists, or false if not. The promise will
   *  reject with an Error if the operation failed.
   */
  public readonly exists = async (): Promise<boolean> => {
    const output = await executePowerShell(
      `Test-Path -ErrorAction SilentlyContinue ${this._escapedPath}`,
    );
    return output.toLowerCase().includes('true');
  };

  /**
   * Create the key represented by this instance.
   * This will create a key at the path specified in the constructor, if necessary. All intermediate
   *  keys in the path will be created as well, meaning you can create a deeply nested sub-key in a
   *  single step.
   *
   * @note It isn't always necessary to call this directly. The key will be created automatically if
   *  necessary when you call other functions, such as setValue().
   *
   * @param overwrite Determines what happens if the key already exists. If true, the existing key
   *  will be replaced, effectively deleting all values it contained. If false, the existing key
   *  will be left as-is. This parameter has no effect if the key doesn't already exist.
   * @returns A promise that resolves to true if the key was successfully created or overwritten, or
   *  false if the key already existed and was not overwritten (i.e. overwrite was false). The
   *  promise rejects with an Error if the operation failed, e.g. due to lack of permission.
   */
  public readonly createKey = async (overwrite: boolean = false): Promise<boolean> => {
    if (!overwrite && (await this.exists())) {
      return false;
    }
    await executePowerShell(`New-Item -Force -Path ${this._escapedPath}`);
    return true;
  };

  /**
   * Deletes the registry key represented by this instance.
   * This will recursively remove all values and child keys below this key.
   * This does nothing if the key doesn't exist.
   *
   * @returns A promise that resolves to true if the key has been successfully deleted, or false if
   *  it didn't exists. The promise rejects with an Error if the operation failed, e.g. due to lack
   *  of permission.
   */
  public readonly deleteKey = async (): Promise<boolean> => {
    if (!(await this.exists())) {
      return false;
    }
    await executePowerShell(`Remove-Item -Force -Recurse -Path ${this._escapedPath}`);
    return true;
  };

  // -----------------------------------------------------------------------------------------------
  // Sub-key operations.

  /**
   * Check if the specified sub-key exists.
   * This is a convenience function to make it easier to work at multiple levels of the registry
   *  without having to repeat the same path.
   *
   * @param relativePath The path of the sub-key to get, relative to the current instance.
   * @returns A promise which resolves to true if the specified sub-key exists, or false if not. The
   *  promise will reject if the operation fails, e.g. due to lack of permission.
   */
  public readonly hasSubKey = async (relativePath: string): Promise<boolean> => {
    const subKey = this.getSubKey(relativePath);
    return await subKey.exists();
  };

  /**
   * Get a new instance referring to a sub-key of this key.
   * This is a convenience function to make it easier to work at multiple levels of the registry
   *  without having to repeat the same path.
   * This doesn't attempt to create the specified sub-key, nor does it ensure that it actually
   *  exists or is accessible. See createSubKey() for that.
   *
   * For example, the child instance below refers to key 'HKLM\Software\Example\Configuration'.
   *
   * @code
   *   const parent = new WindowsRegistryKey('HKLM\\Software\\Example');
   *   const child = parent.getSubKey('Configuration');
   * @endcode
   *
   * @param relativePath The path of the sub-key to get, relative to the current instance. This can
   *  be a multi-part path, allowing you to descend multiple levels in one step.
   * @returns A new instance of WindowsRegistryKey, referencing the specified sub-key of this
   *  instance.
   * @throws {Error} The specified key path is empty or invalid.
   *
   * @see createSubKey()
   */
  public readonly getSubKey = (relativePath: string): WindowsRegistryKey => {
    if (!isValidKeyPathFragment(relativePath)) {
      throw new Error('Invalid registry key path.');
    }
    return new WindowsRegistryKey(this.path + '\\' + relativePath);
  };

  /**
   * Get the names of all sub-keys directly under this key.
   * This does not descend recursively.
   *
   * @returns A promise resolving to an array containing the names of all sub-keys directly under
   *  this key. The array will be empty if there are no sub-keys, or this key doesn't exist. The
   *  promise will reject with an Error if the operation fails.
   */
  public readonly getSubKeyNames = async (): Promise<string[]> => {
    if (!(await this.exists())) {
      return [];
    }

    const output = await executePowerShell(
      `(Get-Item -Path ${this._escapedPath}).GetSubKeyNames()`,
    );
    return output.split('\r\n').filter((s) => s.length > 0);
  };

  /**
   * Construct a new instance of this class for each sub-key, returning them as an array.
   * This doesn't check whether we actually have access to the sub-keys.
   *
   * @returns A promise resolving to an array of newly populated instances of this class. Each
   *  instance will refer to one sub-key of this key. The array will be empty if this key doesn't
   *  exists or it doesn't have any sub-keys. The promise will reject with an Error if the operation
   *  failed.
   */
  public readonly getSubKeys = async (): Promise<WindowsRegistryKey[]> => {
    return (await this.getSubKeyNames()).map((name) => this.getSubKey(name));
  };

  /**
   * Create a sub-key relative to this instance, and return an object referencing it.
   * Unlike getSubkey(), this attempts to create the sub-key in the registry before returning.
   *
   * For example, this will create key 'HKLM\Software\Example\Configuration' if it doesn't already
   *  exist:
   *
   * @code
   *   const parent = new WindowsRegistryKey('HKLM\\Software\\Example');
   *   const child = parent.createSubKey('Configuration');
   * @endcode
   *
   * @param relativePath The path of the sub-key to create, relative to the current instance.
   * @param overwrite Determines what happens if the specified sub-key already exists. If true, the
   *  existing sub-key will be replaced, effectively deleting all values it contained. If false,
   *  the existing sub-key will be left as-is. This parameter has no effect if the sub-key doesn't
   *  already exist.
   * @returns A promise which will resolve to a new instance of this class, referencing the newly
   *  created sub-key (or the existing sub-key if it already existed). The promise will reject with
   *  an Error if the operation failed, e.g. due to lack of permission.
   *
   * @see getSubKey()
   */
  public readonly createSubKey = async (
    relativePath: string,
    overwrite: boolean = false,
  ): Promise<WindowsRegistryKey> => {
    const subKey = this.getSubKey(relativePath);
    await subKey.createKey(overwrite);
    return subKey;
  };

  /**
   * Delete the specified sub-key.
   * This is a convenience function to make it easier to work at multiple levels of the registry
   *  without having to repeat the same path.
   *
   * @param relativePath The path of the sub-key to delete, relative to the current instance.
   * @returns A promise which resolve to true if the sub-key was deleted, or false if it didn't
   *  exist. The promise rejects with an Error if the operation failed, e.g. due to lack of
   *  permission.
   */
  public readonly deleteSubKey = async (relativePath: string): Promise<boolean> => {
    const subKey = this.getSubKey(relativePath);
    return await subKey.deleteKey();
  };

  // -----------------------------------------------------------------------------------------------
  // Value operations.

  /**
   * Check if a value exists with the given name in the current key.
   *
   * @param name The name of the value to look for. An empty name refers to the key's default value.
   * @returns A promise which resolves to true if the named value exists, or false if not. The
   *  promise will reject with an Error if the operation failed.
   */
  public readonly hasValue = async (name: string): Promise<boolean> => {
    if (!isValidValueName(name)) {
      throw new Error('Invalid value name.');
    }

    // No point querying the value if the key doesn't exist.
    if (!(await this.exists())) {
      return false;
    }

    // This approach looks weird, but it's deliberately designed to ensure the command doesn't fail
    //  just because the value doesn't exist. We only want the command to fail if there's an actual
    //  problem.
    const escapedName = `"${escapePowerShellArgument(name)}"`;
    const command = `(Get-Item -Path ${this._escapedPath}).GetValue(${escapedName})`;
    const output = await executePowerShell(command);

    // If the value doesn't exist then the output will be completely empty.
    // If the value exists and is null then it will consist only of whitespace, so it's important
    //  not to trim the output.
    return output !== '';
  };

  /**
   * Get the names of all the values in this key.
   *
   * @returns A promise resolving to an array containing the names of all values in this key. It
   *  will resolve to an empty array if the key doesn't exist. The promise will reject with an Error
   *  if the operation fails.
   */
  public readonly getValueNames = async (): Promise<string[]> => {
    if (!(await this.exists())) {
      return [];
    }

    const output = await executePowerShell(`(Get-Item -Path ${this._escapedPath}).GetValueNames()`);
    return output.split('\r\n').filter((s) => s.length > 0);
  };

  /**
   * Get the raw type of the specified value under this key.
   *
   * @param name The name of the value to query. If this is empty, it will refer to the key's
   *  default value.
   * @returns A promise resolving to a string describing the value type, such as 'DWord'. These
   *  correspond to the RegistryValueKind enumeration in .NET. The string will be empty if the value
   *  does not exist or could not be accessed. The promise will reject with an Error if the
   *  operation failed, e.g. due to lack of permissions.
   *
   * @see https://docs.microsoft.com/en-us/dotnet/api/microsoft.win32.registryvaluekind?view=net-6.0
   */
  public readonly getRawValueType = async (name: string): Promise<string> => {
    if (!(await this.hasValue(name))) {
      return '';
    }

    const escapedName = `"${escapePowerShellArgument(name)}"`;
    const command = `(Get-Item -Path ${this._escapedPath}).GetValueKind(${escapedName})`;
    return (await executePowerShell(command)).trim();
  };

  /**
   * Get the contents of the specified value under the key referenced by this instance.
   *
   * @param name The name of the value to query. If this is empty, it will refer to the key's
   *  default value.
   * @returns A promise resolving to the value contents, if possible. Registry types REG_DWORD and
   *  REG_QWORD will be returned as a number. Registry types  REG_SZ and REG_EXPAND_SZ will be
   *  returned as a string. Registry type REG_MULTI_SZ will be returned as a string array. The
   *  promise will resolve to undefined if the registry value does not exist, could not be accessed,
   *  or was an incompatible type. The promise will reject with an Error if the operation failed.
   */
  public readonly getValue = async (
    name: string,
  ): Promise<undefined | string | string[] | number> => {
    // Don't query anything if the value doesn't exist.
    if (!(await this.hasValue(name))) {
      return undefined;
    }

    const rawType = await this.getRawValueType(name);

    const escapedName = `"${escapePowerShellArgument(name)}"`;
    const command = `(Get-Item -Path ${this._escapedPath}).GetValue(${escapedName})`;
    let rawData = await executePowerShell(command);

    // If there was no output at all then the value probably didn't exist.
    if (rawData === '') {
      return undefined;
    }

    // There will be a trailing line-break if the value did exist, even if the value is empty.
    // Remove it before parsing or returning anything.
    rawData = rawData.replace(/(\r?\n?)$/, '');

    // Parse the data appropriately, based on the type.
    if (isStringRegistryValueType(rawType)) {
      return rawData;
    }

    if (isMultiStringRegistryValueType(rawType)) {
      // Multi-strings are returned with one entry per line.
      return rawData.split('\r\n').filter((s) => s.length > 0);
    }

    if (isIntegerRegistryValueType(rawType)) {
      return parseInt(rawData);
    }

    // The type is not recognised so don't try to parse it.
    return undefined;
  };

  /**
   * Automatically set the contents of a registry value in the key represented by this instance.
   * This will create the key and value if they don't already exist.
   * The registry value type is automatically inferred from the type of data passed-in.
   *
   * The type of registry value is inferred from the data type passed into this function as follows:
   *
   *  - string => REG_SZ
   *  - string[] => REG_MULTI_SZ
   *  - number => REG_DWORD
   *
   * @warning Numerical types in the Windows registry can only store positive integers. If you need
   *  to store a fractional and/or negative number then convert it to a string before passing it in
   *  here.
   *
   * @param name The name of the value to set.
   * @param data The contents of the value to set. The registry value type is inferred from the type
   *  of this value. If this is a number then it will be truncated to an integer (floored). Negative
   *  numbers will be set to 0.
   * @returns A promise which will resolve when the value is set successfully. It will reject with
   *  an Error if the operation fails, e.g. due to lack of permissions.
   */
  public readonly setValue = async (
    name: string,
    data: string | string[] | number,
  ): Promise<void> => {
    const escapedName = `"${escapePowerShellArgument(name)}"`;

    // Infer the appropriate registry type, and escape the data as needed.
    let escapedData = '';
    let type = '';
    if (typeof data === 'string') {
      escapedData = `"${escapePowerShellArgument(data)}"`;
      type = 'String';
    } else if (Array.isArray(data)) {
      escapedData = `"${encodeMultiString(data)}"`;
      type = 'MultiString';
    } else if (typeof data === 'number') {
      // Negative and fractional numbers cannot be stored in the registry.
      if (data < 0) {
        data = 0;
      }
      data = Math.floor(data);
      escapedData = `"${data.toString()}"`;
      type = 'DWord';
    } else {
      throw new Error('Cannot set registry value. Data type is not recognised.');
    }

    // Ensure the parent key exists before trying to add the value.
    await this.createKey(false);
    await executePowerShell(
      `Set-ItemProperty -Path ${this._escapedPath} -Name ${escapedName} -Value ${escapedData} -Type ${type}`,
    );
  };

  /**
   * Delete the named value from this registry key, if it exists.
   *
   * @param name The name of the value to delete.
   * @return A promise which will resolve when deletion has finished, or if the value didn't exist
   *  in the first place. It will reject if an error occurred.
   */
  public readonly deleteValue = async (name: string): Promise<void> => {
    if (!(await this.hasValue(name))) {
      return;
    }

    await executePowerShell(
      `Remove-ItemProperty -Path ${this._escapedPath} -Name "${escapePowerShellArgument(name)}"`,
    );
  };

  // -----------------------------------------------------------------------------------------------
  // Data.

  /**
   * The absolute path of the registry key this instance refers to.
   * This will have undergone basic validation on construction, but there is no guarantee that this
   *  key actually exists, or could exist, or that we can access it.
   *
   * @see _escapedPath
   */
  public readonly path: string;

  /**
   * The fully qualified and escaped key path, ready for use as a PowerShell argument.
   * This will include a "registry::" prefix and surrounding quotation marks.
   *
   * @see path
   */
  private readonly _escapedPath: string;
}

// -------------------------------------------------------------------------------------------------
// Helpers.

/**
 * Check if the given string contains any non-printable characters.
 * Non-printable characters include things like backspace and bell. However, they do not include
 *  non-visible characters such as spaces and newlines.
 *
 * @param s The string to check.
 * @returns True if the string contains one or more non-printable characters. False otherwise.
 */
export const isNonPrintable = (s: string): boolean => {
  // eslint-disable-next-line no-control-regex
  return /[\x00\x08\x0B\x0C\x0E-\x1F]/.test(s);
};

/**
 * Check if the given string is valid as a value name in the Windows registry.
 * Value names must be no more 16,383 characters long, and must not contain any non-printable
 *  characters. All other names are valid, including an empty string, or a string consisting
 *  entirely of spaces.
 *
 * @param s The string to validate.
 * @returns True if the string is valid value name. False if not.
 *
 * @todo Consider making validation functions which throw an Error containing an explanation of
 *  why validation failed.
 */
export const isValidValueName = (s: string): boolean => {
  return s.length < 16384 && !isNonPrintable(s);
};

/**
 * Check if the given string is valid as a relative or absolute key path in the Windows registry.
 * This only checks for valid characters and length. It does not check that the string contains a
 *  valid root key.
 * A valid key path must not be empty, must be no more than 255 characters long, and must not
 *  contain any non-printable characters.
 *
 * @param s The string to validate.
 * @returns True if the string is valid key path. False if not.
 *
 * @todo Consider making validation functions which throw an Error containing an explanation of
 *  why validation failed.
 */
export const isValidKeyPathFragment = (s: string): boolean => {
  return s.length > 0 && s.length < 256 && !isNonPrintable(s);
};

/**
 * Check if the given string is valid as an absolute key path in the Windows registry.
 * This checks for valid characters and length, and ensures that the first part of the path is a
 *  valid root key.
 * For example, "Software\Example" is valid as a relative path, but it is not a valid absolute
 *  path as it doesn't contain a root key. A valid absolute path could be "HKLM\Software\Example"
 *  or "HKEY_LOCAL_MACHINE\Software\Example".
 *
 * @param s The string to validate.
 * @returns True if the string is a valid absolute key path. False if not.
 *
 * @todo Consider making validation functions which throw an Error containing an explanation of
 *  why validation failed.
 */
export const isValidAbsoluteKeyPath = (s: string): boolean => {
  if (!isValidKeyPathFragment(s)) {
    return false;
  }

  // Ensure the key starts with a valid root key.
  const root = s.split('\\')[0].toUpperCase();
  return validRootKeys.includes(root);
};

/**
 * All known valid root keys for the Windows registry.
 * These must be uppercase to facilitate case-insensitive comparison.
 */
const validRootKeys: string[] = [
  'HKEY_CLASSES_ROOT',
  'HKEY_CURRENT_USER',
  'HKEY_LOCAL_MACHINE',
  'HKEY_PERFORMANCE_DATA',
  'HKEY_USERS',

  'HKCR',
  'HKCU',
  'HKLM',
  'HKU',
];

/**
 * Escape and encode an array of string to pass as a multi-string registry value in PowerShell.
 * This will escape each individual string, then concatenate them, using a PowerShell null character
 *  between each one.
 * This will not enclose the resulting string in quotation. The call should do that to ensure
 *  spaces do not break command interpretation.
 *
 * @note Multi-string registry values are not allowed to contain any empty strings. Any empty
 *  strings will be removed by this function.
 *
 * @param data The array of strings to be encoded.
 * @returns A single string containing the encode multi-string data.
 */
export const encodeMultiString = (data: string[]): string => {
  return data
    .filter((s) => s.length > 0)
    .map(escapePowerShellArgument)
    .join('`0');
};

/**
 * Check if the specified registry value type can be interpreted as an integer.
 *
 * @type A string describing the registry value type. This can be a RegistryValueKind name as used
 *  by PowerShell, e.g. 'Qword'. It can also be a Windows API style type name, e.g. "REG_QWORD".
 * @returns True if the type can be interpreted as an integer. False if not.
 */
export const isIntegerRegistryValueType = (type: string): boolean => {
  return ['DWORD', 'QWORD', 'REG_DWORD', 'REG_QWORD'].includes(type.toUpperCase());
};

/**
 * Check if the specified registry value type can be interpreted as a string.
 *
 * @type A string describing the registry value type. This can be a RegistryValueKind name as used
 *  by PowerShell, e.g. 'String'. It can also be a Windows API style type name, e.g. "REG_SZ".
 * @returns True if the type can be interpreted as a string. False if not. This will return false
 *  for multi-string.
 */
export const isStringRegistryValueType = (type: string): boolean => {
  return ['STRING', 'EXPANDSTRING', 'REG_SZ', 'REG_EXPAND_SZ'].includes(type.toUpperCase());
};

/**
 * Check if the specified registry value type can be interpreted as a multi-string.
 *
 * @type A string describing the registry value type. This can be a RegistryValueKind name as used
 *  by PowerShell, e.g. 'MultiString'. It can also be a Windows API style type name, e.g.
 *  "REG_MULTI_SZ".
 * @returns True if the type can be interpreted as a multi-string. False if not. This will return
 *  false for individual string types.
 */
export const isMultiStringRegistryValueType = (type: string): boolean => {
  return ['MULTISTRING', 'REG_MULTI_SZ'].includes(type.toUpperCase());
};

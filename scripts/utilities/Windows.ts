import * as child_process from 'child_process';

/**
 * Execute a command in PowerShell.
 *
 * @param command The command to execute, including any arguments to pass to it.
 * @returns A promise which resolves when the command completes successfully; i.e. with exit code 0.
 *  The resolved value will be the console output of the command (stdout). The promise will reject
 *  with an Error if the command failed.
 */
export const executePowerShell = async (command: string): Promise<string> => {
  const options: child_process.ExecOptions = {
    env: {},
    shell: 'powershell.exe',
    windowsHide: true,
  };
  return await new Promise<string>((resolve, reject) => {
    child_process.exec(command, options, (error, stdout) => {
      if (error !== null) {
        reject(error);
      } else {
        resolve(stdout?.toString() ?? '');
      }
    });
  });
};

/**
 * Escape special characters in a string so that it's safe to pass as a PowerShell argument.
 *
 * @param s The string to escape.
 * @returns The escaped string.
 */
export const escapePowerShellArgument = (s: string): string => {
  return s.replace(/(["`$])/g, '`$1');
};

/**
 * Check if a Windows service with the given name exists.
 * @param name The name of the service to look for.
 * @returns True if the service exists or false if not.
 */
export const doesServiceExist = async (name: string): Promise<boolean> => {
  try {
    // This command will fail if the service doesn't exist.
    await executePowerShell(`Get-Service -Name "${escapePowerShellArgument(name)}"`);
    return true;
  } catch {
    return false;
  }
};

/**
 * Check if a Windows service with the given name exists and is running.
 * @param name The name of the service to look for.
 * @returns True if the service exists and is running. False if the service doesn't exist or has
 *  any other status.
 */
export const isServiceRunning = async (name: string): Promise<boolean> => {
  try {
    // This command will fail if the service doesn't exist.
    const status = await executePowerShell(
      `(Get-Service -Name "${escapePowerShellArgument(name)}").Status`,
    );
    return status.toLowerCase().includes('running');
  } catch {
    // The service probably does not exist.
    return false;
  }
};

/**
 * Attempt to start the named Windows service running.
 * This does nothing if the service is already running.
 * @param name The name of the service to start.
 * @returns Returns true if the service start command was sent successfully. This does not wait for
 *  the service to finish initialising though so there is no guarantee that it will actually reach
 *  the running status. Returns false if the service start command could not be sent, e.g. because
 *  the named service doesn't exist. An error message will be logged to the console in this case.
 */
export const startService = async (name: string): Promise<boolean> => {
  try {
    await executePowerShell(`Start-Service -Name "${escapePowerShellArgument(name)}"`);
    return true;
  } catch (e: any) {
    console.warn(`Failed to start service, "${name}".`, e);
    return false;
  }
};

/**
 * Attempt the stop the named Windows service running.
 * This does nothing if the service is already stopped.
 * @param name The name of the service to stop.
 * @returns Returns true if the service stop command was sent successfully. This does not wait for
 *  the service to stop though; it could take a while to shut down, e.g. if the process is hung.
 *  Returns false if the service stop command could not be sent, e.g. because the named service
 *  doesn't exist. An error message will be logged to the console in this case.
 */
export const stopService = async (name: string): Promise<boolean> => {
  try {
    await executePowerShell(`Stop-Service -Name "${escapePowerShellArgument(name)}"`);
    return true;
  } catch (e: any) {
    console.warn(`Failed to stop service, "${name}".`, e);
    return false;
  }
};

/**
 * Get the full username of the currently logged-in user.
 * This will include the domain.
 */
export const getUsername = async (): Promise<string> => {
  return (
    await executePowerShell('[System.Security.Principal.WindowsIdentity]::GetCurrent().Name')
  ).trim();
};

/**
 * Check if the current user is signed-in using Azure Active Directory.
 * This is a very naive check. It could be improved, but is probably good enough.
 */
export const isAzureAd = async (): Promise<boolean> => {
  return (await getUsername()).toLowerCase().startsWith('azuread');
};

/*
This script builds the extension in various configurations, and archives each one in a zip file
ready for deployment to the Chrome or Edge Web Store. This does not pack or sign the extension
so it isn't suitable for self-hosting.

Note: This will build the software using the current working copy of the code. It is your
responsibility to ensure you have the correct version cleanly checked out.

Usage:

  npm run build-for-webstore -- [options]

If no options are specified then all build configurations will be generated by default.
Use the --help option for more detailed usage information.
*/

import archiver from 'archiver';
import * as child_process from 'child_process';
import { Command } from 'commander';
import clc from 'cli-color';
import * as fs from 'fs';
import packageFile from '../package.json';
import * as path from 'path';

// -------------------------------------------------------------------------------------------------
// Constants.

const validTargets = ['mv2', 'mv3'];
const validEnvironments = ['dev', 'qa', 'prd'];
const validSuffixPattern = /^[a-zA-Z0-9\-_.]*$/i;
const rootFolder = path.resolve(__dirname, '..');
const buildFolder = path.join(rootFolder, 'dist');
const defaultOutputFolder = path.join(rootFolder, '.webstore');

// -------------------------------------------------------------------------------------------------
// Script options.

// Define and parse the command line parameters for the script.
const program = new Command();

program
  .name('npm run build-for-webstore --')
  .description(
    'Build and archive one or more configurations of the extension for deployment to a browser web store.',
  );

program.option(
  '-t, --target <target>',
  'Comma separated list of the extension standard(s) to build. Valid values are "mv2" and "mv3".',
  validTargets.join(','),
);

program.option(
  '-e, --environment <backend>',
  'Comma separated list of the backend environments to build for. This only affects where telemetry is sent. Valid values are "dev", "qa", and "prd".',
  validEnvironments.join(','),
);

program.option(
  '--verbose',
  'If specified, verbose output will be enabled. This is useful for debugging build issues etc.',
);

program.option(
  '-s, --suffix <name>',
  'Optional name which will be appended to the names of the zip files produced by this script. Valid characters are ASCII letters, numbers, dots, dashes, and underscores.',
);

program.option(
  '-o, --output <path>',
  'Path of the directory which the zip files will be written to. It will be created if it does not exist. If not specified, a subdirectory called ".webstore" within the root package directory will be used.',
  defaultOutputFolder,
);

program.option(
  '--skip-install',
  'Skip the npm installation step, meaning existing copies of dependencies will be used. If not specified the script will do "npm ci" before building anything.',
);

program.parse(process.argv);
const options = program.opts();
const verbose = options.verbose === true;
const skipInstall = options.skipInstall === true;

// -------------------------------------------------------------------------------------------------
// Functions.

const logVerbose = (...args: unknown[]): void => {
  if (verbose) {
    console.debug(clc.blackBright(...args));
  }
};

const logInfo = (...args: unknown[]): void => {
  console.info(...args);
};

const logSuccess = (...args: unknown[]): void => {
  console.info(clc.green(...args));
};

const logWarning = (...args: unknown[]): void => {
  console.warn(clc.yellow(...args));
};

const logError = (...args: unknown[]): void => {
  console.error(clc.red(...args));
};

// -------------------------------------------------------------------------------------------------
// Option validation.

if (program.args.length > 0) {
  logError('ERROR: Unexpected argument(s):', program.args.join(' '));
  process.exit(1);
}

// Ensure only valid targets were specified.
const targets = [
  ...new Set(
    (options.target as string)
      .split(',')
      .map((t) => t.toLocaleLowerCase().trim())
      .filter((t) => t !== ''),
  ),
];
if (targets.length === 0) {
  logError('ERROR: At least one target must be specified. Omit the option to use all.');
  process.exit(1);
}
targets.forEach((target: string): void => {
  if (!validTargets.includes(target)) {
    logError(
      `ERROR: Invalid target: "${target}". The valid targets are: ${validTargets.join(', ')}`,
    );
    process.exit(1);
  }
});

// Ensure only valid environments were specified.
const environments = [
  ...new Set(
    (options.environment as string)
      .split(',')
      .map((e) => e.toLocaleLowerCase().trim())
      .filter((e) => e !== ''),
  ),
];
if (environments.length === 0) {
  logError('ERROR: At least one environment must be specified. Omit the option to use all.');
  process.exit(1);
}
environments.forEach((environment: string): void => {
  if (!validEnvironments.includes(environment)) {
    logError(
      `ERROR: Invalid environment: "${environment}". The valid environments are: ${validEnvironments.join(
        ', ',
      )}`,
    );
    process.exit(1);
  }
});

// Limit the suffix to safe ASCII characters.
const suffix = ((options.suffix as string) ?? '').trim();
if (!validSuffixPattern.test(suffix)) {
  logError(
    `ERROR: Invalid suffix: "${suffix}". Only ASCII letters, numbers, dots, dashes, and underscores are allowed.`,
  );
  process.exit(1);
}

// If an output path was specified, ensure it's a folder, or that we can create a folder there.
const outputPath = path.resolve((options.output as string).trim());
if (fs.existsSync(outputPath)) {
  if (!fs.statSync(outputPath).isDirectory()) {
    logError(`ERROR: Specified output path already exists but is not a folder: ${outputPath}`);
    process.exit(1);
  }
} else {
  fs.mkdirSync(outputPath, { recursive: true });
}

// -------------------------------------------------------------------------------------------------
// Git checks.

// Report the git branch to the console, and warn if it isn't main/master.
try {
  const gitBranch = child_process
    .execSync('git symbolic-ref --short HEAD', {
      windowsHide: true,
      stdio: 'pipe',
      encoding: 'utf8',
    })
    .trim();

  if (gitBranch === 'main' || gitBranch === 'master') {
    logVerbose(`Current git branch: ${gitBranch}`);
  } else {
    logWarning(`WARNING: Git checkout is not on the main branch. Current branch: ${gitBranch}`);
  }
} catch (e: any) {
  logWarning(
    'WARNING: Failed to check the current git branch. Please ensure you are building from the main/master branch.',
  );
  if (verbose) {
    logWarning(e);
  }
}

// Check if there are any uncommitted changes and log a warning if so.
// Note: This won't detect changes which have been committed but not pushed.
try {
  // This command is necessary to update git's internal index, allowing the next command to check
  //  for changes accurately.
  child_process.execSync('git update-index -q --refresh', { windowsHide: true, stdio: 'ignore' });
} catch (e: any) {
  // Ignore the error. This command returns a non-zero exit code if the index was out of date.
}

try {
  child_process.execSync('git diff-index --quiet HEAD --', { windowsHide: true, stdio: 'ignore' });
  logVerbose('Git working copy looks clean. No uncommitted changes found.');
} catch (e: any) {
  logWarning(
    'WARNING: Local changes detected in git repository. Please ensure you are building from a clean and up-to-date checkout.',
  );
}

// -------------------------------------------------------------------------------------------------
// Dependencies

if (skipInstall) {
  logWarning(
    'WARNING: Skipping dependency installation. Please ensure the npm modules are clean and up-to-date.',
  );
} else {
  try {
    logInfo('Installing dependencies...');
    child_process.execSync('npm ci', {
      windowsHide: true,
      stdio: verbose ? 'inherit' : 'ignore', // echo output directly to console in verbose mode
    });
  } catch (e: any) {
    logError('ERROR: Failed to install dependencies.');
    process.exit(1);
  }
}

// -------------------------------------------------------------------------------------------------
// Builds.

logInfo(`Package: ${packageFile.name} ${packageFile.version}`);

// We need to wrap the build in a function so that we can use async/await.
const build = async (): Promise<void> => {
  // Go through each combination of target and environment.
  for (const target of targets) {
    for (const environment of environments) {
      if (verbose) {
        logInfo(`\n===== Configuration: ${environment}-${target} =====`);
      } else {
        logInfo(`Building configuration: ${environment}-${target}`);
      }

      // If there's an existing build then ensure it's completely removed.
      if (fs.existsSync(buildFolder)) {
        logVerbose(`Deleting existing build directory: ${buildFolder}`);
        fs.rmSync(buildFolder, { recursive: true, force: true });
      }

      try {
        logVerbose('Building extension...');
        child_process.execSync(`npm run build:${environment}-${target}`, {
          windowsHide: true,
          stdio: verbose ? 'inherit' : 'ignore', // echo output directly to console in verbose mode
        });
        logVerbose();
      } catch (e: any) {
        logError('ERROR: Failed to build extension.');
        process.exit(1);
      }

      // Sanity-check that the build output is where we expect.
      const manifestPath = path.join(buildFolder, 'manifest.json');
      if (!fs.existsSync(manifestPath)) {
        logError(
          `ERROR: Build output was not found at the expected location. File not found: ${manifestPath}`,
        );
        process.exit(1);
      }

      // Compress the contents of the build folder into a zip file.
      logVerbose('Archiving build output...');
      const archiveName = `${packageFile.name}-${packageFile.version}-${environment}-${target}${
        suffix.length > 0 ? '-' : ''
      }${suffix}.zip`;
      const archivePath = path.join(outputPath, archiveName);

      const archive = archiver('zip');
      archive.pipe(fs.createWriteStream(archivePath));
      archive.directory(buildFolder, false);
      try {
        await archive.finalize();
        logSuccess(`Created archive: ${archivePath}`);
      } catch (e: any) {
        logError(`ERROR: Failed to create archive: ${archivePath}`);
        if (verbose) {
          logError(e);
        }
        process.exit(1);
      }
    }
  }
};

build().catch(logError);

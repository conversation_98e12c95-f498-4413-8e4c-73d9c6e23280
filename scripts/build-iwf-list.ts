#!/usr/bin/env ts-node

import { program } from 'commander';
import { IwfListBuilder } from './utilities/IwfListBuilder';

/**
 * Command-line script for building IWF list data.
 * This script processes JSON files from the _iwflist directory and generates
 * the compressed TypeScript file used by the mini-blocklist system.
 */

program
  .name('build-iwf-list')
  .description('Build and encode IWF list data from JSON files')
  .version('1.0.0');

program
  .command('build')
  .description('Build the IWF list from the latest JSON file')
  .option('-f, --force', 'Force rebuild even if output is up to date')
  .option('-s, --source <dir>', 'Source directory containing JSON files', 'src/_iwflist')
  .option('-o, --output <file>', 'Output TypeScript file', 'src/mini-blocklist-data/iwflist.ts')
  .action((options) => {
    const builder = new IwfListBuilder(options.source, options.output);
    const success = builder.build(options.force);
    
    if (success) {
      console.log('✅ IWF list build completed successfully');
      process.exit(0);
    } else {
      console.error('❌ IWF list build failed');
      process.exit(1);
    }
  });

program
  .command('info')
  .description('Show information about source and output files')
  .option('-s, --source <dir>', 'Source directory containing JSON files', 'src/_iwflist')
  .option('-o, --output <file>', 'Output TypeScript file', 'src/mini-blocklist-data/iwflist.ts')
  .action((options) => {
    const builder = new IwfListBuilder(options.source, options.output);
    const info = builder.getInfo();
    
    console.log('IWF List Builder - File Information');
    console.log('=====================================');
    console.log(`Source directory: ${options.source}`);
    console.log(`Latest JSON file: ${info.sourceFile || 'None found'}`);
    console.log(`Output file: ${info.outputFile}`);
    console.log(`Output up to date: ${info.upToDate ? '✅ Yes' : '❌ No'}`);
    
    if (info.sourceFile) {
      const fs = require('fs');
      const stats = fs.statSync(info.sourceFile);
      console.log(`Source file date: ${stats.mtime.toISOString()}`);
      
      if (fs.existsSync(info.outputFile)) {
        const outputStats = fs.statSync(info.outputFile);
        console.log(`Output file date: ${outputStats.mtime.toISOString()}`);
      }
    }
  });

program
  .command('validate')
  .description('Validate the current iwflist.ts file')
  .option('-o, --output <file>', 'Output TypeScript file to validate', 'src/mini-blocklist-data/iwflist.ts')
  .action((options) => {
    const fs = require('fs');
    const LZString = require('lz-string');
    
    try {
      // Import the current iwflist
      const iwflistPath = require('path').resolve(options.output);
      if (!fs.existsSync(iwflistPath)) {
        console.error(`❌ Output file not found: ${iwflistPath}`);
        process.exit(1);
      }
      
      // Read the file content to extract the compressed data
      const content = fs.readFileSync(iwflistPath, 'utf-8');
      const match = content.match(/const iwflist =\s*'([^']+)';/);
      
      if (!match) {
        console.error('❌ Could not extract compressed data from file');
        process.exit(1);
      }
      
      const compressedData = match[1];
      console.log(`Compressed data length: ${compressedData.length} characters`);
      
      // Try to decompress
      const decompressed = LZString.decompressFromBase64(compressedData);
      if (!decompressed) {
        console.error('❌ Failed to decompress data');
        process.exit(1);
      }
      
      // Parse as JSON
      const jsonData = JSON.parse(decompressed);
      if (!Array.isArray(jsonData)) {
        console.error('❌ Decompressed data is not an array');
        process.exit(1);
      }
      
      console.log('✅ Validation successful');
      console.log(`Entry count: ${jsonData.length}`);
      console.log(`Decompressed size: ${decompressed.length} characters`);
      console.log(`Compression ratio: ${(compressedData.length / decompressed.length * 100).toFixed(1)}%`);
      
      // Check for empty string hash
      const emptyStringHash = 'da39a3ee5e6b4b0d3255bfef95601890afd80709';
      const hasEmptyStringHash = jsonData.includes(emptyStringHash);
      console.log(`Contains empty string hash: ${hasEmptyStringHash ? '❌ Yes' : '✅ No'}`);
      
    } catch (error) {
      console.error(`❌ Validation failed: ${error}`);
      process.exit(1);
    }
  });

// Parse command line arguments
program.parse();

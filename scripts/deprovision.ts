// NOTE: This script must be executed with admin/sudo privileges.

import * as Provisioning from './utilities/Provisioning';
import UnpackedExtension from './utilities/UnpackedExtension';

// We need to know the extension ID to remove the correct provisioning data.
const extensionId = UnpackedExtension.findExtensionId(__dirname, '..', 'dist');
console.debug('Extension ID:', extensionId);

Provisioning.deprovision(extensionId)
  .then(() => {
    console.log('Deprovisioning completed successfully.');
  })
  .catch((e: any) => {
    console.error('Deprovisioning failed:', e);
    console.log('Please ensure you run this script with admin/sudo privileges.');
    process.exit(2);
  });

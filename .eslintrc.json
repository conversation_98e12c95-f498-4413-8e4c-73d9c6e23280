{"env": {"browser": true, "es6": true}, "extends": ["standard-with-typescript", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint"], "rules": {"n/no-callback-literal": "off", "node/no-callback-literal": "off", "@typescript-eslint/consistent-type-imports": "off", "prefer-arrow-callback": "warn", "func-style": ["warn", "expression"], "@typescript-eslint/naming-convention": ["warn", {"selector": "variable", "modifiers": ["const"], "format": ["camelCase", "UPPER_CASE"], "leadingUnderscore": "allow"}, {"selector": "classProperty", "modifiers": ["readonly"], "format": ["camelCase", "UPPER_CASE"], "leadingUnderscore": "allow"}, {"selector": "property", "modifiers": ["static", "readonly"], "format": ["camelCase", "UPPER_CASE"], "leadingUnderscore": "allow"}, {"selector": "accessor", "format": ["camelCase"], "leadingUnderscore": "allow"}, {"selector": "memberLike", "modifiers": ["private"], "format": ["camelCase"], "leadingUnderscore": "require"}]}}
/*
 * All in one build and deployment script to build the latest version of the browser extension and publish it to Google Cloud Storage in a ready to use self hosted format.
 * Files will be uploaded in the following structure:
 *
 * automated-self-hosted-deployments/
 *  └── <branch-name>/
 *     ├── cldflt-browser-extension-mv3-<version>.crx
 *     └── updates.xml
 *
 */
import path from 'path';
import { Storage, Bucket, File } from '@google-cloud/storage';
import fs from 'fs';
import { createCRXFile, generateUpdateXml, getPrivateKeyPathForEnv, updateManifestUpdateUrl } from './cli-utils';
import { Command } from 'commander';

// read package.json to get the version number
const packageJson = require('../../package.json');

if (!packageJson || !packageJson.version) {
  throw new Error('Could not read version from package.json. Please ensure the file exists and contains a valid version.');
}

const EXTENSION_ID = process.env.EXTENSION_ID;
const EXTENSION_VERSION = packageJson.version;
const BRANCH_NAME = process.env.BRANCH_NAME;
const CLOUD_STORAGE_BUCKET = 'fzo-build-sw-cldflt-browser-extension-artifacts';

// perform some sense checks
if (!EXTENSION_VERSION) {
  throw new Error('VERSION environment variable is not set. Please set it before running the build script.');
}

if (!BRANCH_NAME) {
  throw new Error('BRANCH_NAME environment variable is not set. Please set it before running the build script.');
}

if (!EXTENSION_ID) {
  throw new Error('EXTENSION_ID environment variable is not set. Please set it before running the build script.');
}

const readVersionFromXml = (xmlContent: string): string | null => {
  // find the version from the chrome update xml
  const versionMatch = xmlContent.match(/<updatecheck[^>]+version=['"]([^'"]+)['"]/);
  return versionMatch ? versionMatch[1] : null;
};

/**
 * Compares two version numbers and checks if the new version is later than the old version.
 * Supports values that look like A.B.C.D where A, B, C, and D are integers.
 * @param newVersionNumber
 * @param oldVersionNumber
 */
const checkVersionIsLaterThan = (newVersionNumber: string, oldVersionNumber: string): boolean => {
  const newVersionParts = newVersionNumber.split('.').map(Number);
  const oldVersionParts = oldVersionNumber.split('.').map(Number);

  // Compare each part of the version number
  for (let i = 0; i < Math.max(newVersionParts.length, oldVersionParts.length); i++) {
    const newPart = newVersionParts[i] || 0;
    const oldPart = oldVersionParts[i] || 0;

    if (newPart > oldPart) {
      return true;
    } else if (newPart < oldPart) {
      return false;
    }
  }

  return false;
};

/**
 *
 * @param bucket {import('@google-cloud/storage').Bucket}
 * @param filePath {string}
 */
const getCloudStorageFileContent = async (
  bucket: Bucket,
  filePath: string
): Promise<string | undefined> => {
  const file = bucket.file(filePath);

  try {
    const [content] = await file.download();
    return content.toString('utf8');
  } catch (error: any) {
    // if it's a 404 error, return undefined
    if (error.code === 404) {
      return undefined;
    }
    throw error;
  }
};

const program = new Command();

program
  .name('deploy-self-hosted-latest')
  .description(
    'Builds the latest version of the browser extension for a given branch and publishes it to Google Cloud Storage in a ready to use self hosted format.'
  );

program.option(
  '--force',
  'Force the deployment even if the version is not later than the existing version. Use with caution!',
  false
);



const main = async (): Promise<void> => {
  program.parse(process.argv);
  const options = program.opts();
  const forceDeployment: boolean = (options.force)
  const storage = new Storage();
  const bucket = storage.bucket(CLOUD_STORAGE_BUCKET);

  const rootSubdirectory = `automated-self-hosted-deployments/${BRANCH_NAME}`;
  const extensionFileName = `cldflt-browser-extension-mv3-${EXTENSION_VERSION}.crx`;
  const updateXmlFileName = 'updates.xml';

  // check if the updates.xml file already exists in the bucket
  const updatesXmlPath = `${rootSubdirectory}/updates.xml`;
  console.log('Checking for existing updates.xml file in the bucket...');
  const existingUpdatesXmlContent = await getCloudStorageFileContent(bucket, updatesXmlPath);
  const existingVersion = existingUpdatesXmlContent ? readVersionFromXml(existingUpdatesXmlContent) : null;
  if (existingVersion && !checkVersionIsLaterThan(EXTENSION_VERSION, existingVersion) && !forceDeployment) {
    console.log(
      `Skipping deployment. The current version ${EXTENSION_VERSION} is not later than the existing version ${existingVersion}. Did you forget to update the version in package.json?`
    );
    return;
  }

  const selfHostedBuildsDir = path.resolve(__dirname, '../../self-host-builds');

  if (!fs.existsSync(selfHostedBuildsDir)) {
    fs.mkdirSync(selfHostedBuildsDir, { recursive: true });
  }

  console.log('Generating updates.xml file...');
  const publicUpdatesXMLURL = `https://storage.googleapis.com/${CLOUD_STORAGE_BUCKET}/${rootSubdirectory}/${updateXmlFileName}`;
  const publicUpdatesCRXURL = `https://storage.googleapis.com/${CLOUD_STORAGE_BUCKET}/${rootSubdirectory}/${extensionFileName}`;
  generateUpdateXml(
    publicUpdatesCRXURL,
    EXTENSION_ID,
    EXTENSION_VERSION
  );

  // update the update_url present in the manifest.json file to match the public URL of the updates.xml file
  const manifestPath = path.resolve(__dirname, '../../dist/manifest.json');
  updateManifestUpdateUrl(manifestPath,publicUpdatesXMLURL)

  console.log('Creating CRX file...');
  const privateKeyPath = getPrivateKeyPathForEnv(process.env._BUILD_ENV);
  if (!privateKeyPath || !fs.existsSync(privateKeyPath)) {
    throw new Error(`Private key file not found at path: ${privateKeyPath}`);
  }

  await createCRXFile(privateKeyPath, path.resolve(__dirname, '../../dist.crx'));

  console.log(`Uploading CRX file to ${CLOUD_STORAGE_BUCKET}/${rootSubdirectory}/${extensionFileName}...`);
  await bucket.upload('./dist.crx', {
    destination: `${rootSubdirectory}/${extensionFileName}`,
    metadata: {
      cacheControl: 'no-store',
    },
    public: true,
  });

  console.log(`Uploading updates.xml to ${CLOUD_STORAGE_BUCKET}/${rootSubdirectory}/updates.xml...`);
  await bucket.upload(`./self-host-builds/${updateXmlFileName}`, {
    destination: `${rootSubdirectory}/updates.xml`,
    metadata: {
      cacheControl: 'no-store',
    },
    public: true,
  });

  // collect all older crx files in this subfolder
  const [files] = await bucket.getFiles({ prefix: `${rootSubdirectory}/` });
  const oldCrxFiles = files.filter(
    (file: File) =>
      file.name.endsWith('.crx') &&
      file.name.startsWith(`${rootSubdirectory}/cldflt-browser-extension-mv3`) &&
      file.name !== `${rootSubdirectory}/${extensionFileName}`
  );

  // delete all older crx files in this subfolder (now that we can be sure clients will be sent to the new version)
  if (oldCrxFiles.length > 0) {
    console.log(`Deleting old CRX files: ${oldCrxFiles.map((file: File) => file.name).join(', ')}`);
    await Promise.all(oldCrxFiles.map((file: File) => file.delete()));
  }

  console.log("Deployment completed successfully! You can now use the self-hosted extension at: "+publicUpdatesXMLURL);
};

main().catch((err: unknown) => {
  console.error('Deployment failed:', err);
  process.exit(1);
});

// example usage with all environment variables set:
// EXTENSION_ID=fbmnfnjfffejoednljheoooljhbfgafb BRANCH_NAME=release/******* VERSION=******* ts-node .ci/node/deploy-self-hosted-latest.ts
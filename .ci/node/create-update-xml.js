/*
 * Creates an updates xml file with the correct information for the new extension build.
 *
 * The new version needs to be set in the user environment first.
 */
const fs = require('fs');
const { extensionId, cloudStorageBucket } = require('../data/self-hosting.json');

const createUpdateXmlFile = (newVersion) => {
  fs.writeFileSync(
    './self-host-builds/updates.xml',
    "<?xml version='1.0' encoding='UTF-8'?>\n" +
      "<gupdate xmlns='http://www.google.com/update2/response' protocol='2.0'>\n" +
      `  <app appid='${extensionId}'>\n` +
      `    <updatecheck codebase='https://storage.googleapis.com/${cloudStorageBucket}/dev/cldflt-browser-extension-${newVersion}.crx' version='${newVersion}' />\n` +
      '  </app>\n' +
      '</gupdate>',
    'utf8',
  );
};

createUpdateXmlFile(process.env.VERSION);

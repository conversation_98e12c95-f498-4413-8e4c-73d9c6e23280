import path from 'path';
// @ts-ignore no types are available for this package
import ChromeExtension from 'crx';
import fs from 'fs';

export const generateUpdateXml = (
  updateUrl: string,
  extensionId: string,
  versionNumber: string,
): void => {
  fs.writeFileSync(
    './self-host-builds/updates.xml',
    "<?xml version='1.0' encoding='UTF-8'?>\n" +
      "<gupdate xmlns='http://www.google.com/update2/response' protocol='2.0'>\n" +
      `  <app appid='${extensionId}'>\n` +
      `    <updatecheck codebase='${updateUrl}' version='${versionNumber}' />\n` +
      '  </app>\n' +
      '</gupdate>',
    'utf8',
  );
};

export const getPrivateKeyPathForEnv = (env: string | undefined): string => {
  let privateKeyPath = path.resolve(__dirname, '../../cldflt-extension-pk.pem');
  if (env === 'qa') {
    privateKeyPath = path.resolve(__dirname, '../../cldflt-extension-pk.pem');
  }

  return privateKeyPath;
}

export const createCRXFile = async (privateKeyPath: string, outputPath: string): Promise<void> => {
  const crx = new ChromeExtension({
    privateKey: fs.readFileSync(privateKeyPath),
  });

  await crx.load(path.resolve(__dirname, '../../dist'));
  const crxBuffer = await crx.pack();
  fs.writeFileSync(outputPath, crxBuffer);
};

export const updateManifestUpdateUrl = (pathToUpdateXML: string, updateUrl: string): void => {
  const manifest = JSON.parse(fs.readFileSync(pathToUpdateXML, { encoding: 'utf-8' }));
  manifest.update_url = updateUrl;
  fs.writeFileSync(pathToUpdateXML, JSON.stringify(manifest, undefined, 2), { encoding: 'utf-8' });
}
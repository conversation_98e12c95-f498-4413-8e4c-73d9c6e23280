/*
 * Reads the self-hosting.json file and the version number from the package.json.
 * It outputs the data into separate files that can be used in the build process.
 */

const fs = require('fs');
const configJson = require('../data/self-hosting.json');

const configKeys = Object.keys(configJson);

for (let index = 0; index < configKeys.length; index++) {
  const configKey = configKeys[index];

  fs.mkdirSync('./.ci/temp/data', { recursive: true });
  fs.writeFileSync(`./.ci/temp/data/${configKey}`, `${configJson[configKey]}`);
}

// Also get the version number.
const newPackage = require('../../package.json');
fs.mkdirSync('./.ci/temp/data', { recursive: true });
fs.writeFileSync(`./.ci/temp/data/version`, newPackage.version);

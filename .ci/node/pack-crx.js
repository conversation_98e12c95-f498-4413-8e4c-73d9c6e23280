/*
 * Packs the dist folder into a crx.
 * Uses the self host pem we got from GCP secret storage to keep the same extension id.
 */
const path = require('path');
const fs = require('fs');
const ChromeExtension = require('crx');

const crx = new ChromeExtension({
  privateKey: fs.readFileSync(path.resolve(__dirname, '../../cldflt-extension-pk.pem')),
});

crx
  .load(path.resolve(__dirname, '../../dist'))
  .then((crx) => crx.pack())
  .then((crxBuffer) => {
    fs.writeFileSync(path.resolve(__dirname, '../../dist.crx'), crxBuffer);
  })
  .catch((err) => {
    console.log('Extension packing failed: ', err);
  });

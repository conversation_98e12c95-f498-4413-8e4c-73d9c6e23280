substitutions:
  _BUILD_ENV: prd
  _NODE_VERSION: '18'
  _UPLOAD_GS_PATH: gs://fzo-build-sw-cldflt-browser-extension-artifacts/${_BUILD_ENV}/${BRANCH_NAME}/${BUILD_ID}
  _EXTENSION_ID: 'fbmnfnjfffejoednljheoooljhbfgafb'

steps:
  - id: Authenticate with Google Artifact Registry
    name: node:$_NODE_VERSION
    entrypoint: npm
    args: ['run', 'artifactregistry-login']

  - id: Install dependencies
    name: node:$_NODE_VERSION
    entrypoint: npm
    args: ['ci']

  - id: Check formatting
    name: node:$_NODE_VERSION
    entrypoint: npm
    args: ['run', 'check-format']

  - id: Lint code
    name: node:$_NODE_VERSION
    entrypoint: npm
    args: ['run', 'lint']

  # We might want to move this step into a custom container to help manage the prerequisites.
  - id: Run tests
    name: ubuntu
    script: |
      #!/usr/bin/env bash
      apt-get update
      apt-get install -y default-jre curl sudo nodejs npm
      curl -sL https://firebase.tools | bash
      export FIREBASE_TOKEN="1//033noPd_DYRCWCgYIARAAGAMSNwF-L9Ir8OVi3FWGcaRPRjikkcFSdPvZlwxqKjktM6TOVeDs3yQJdnVAhiuFqKfmugJo2xaADus"
      npm run test

  - id: Build Extension
    name: node:$_NODE_VERSION
    entrypoint: npm
    args: ['run', 'build:$_BUILD_ENV']

  # Set up self hosting data
  - id: 'Get selfhost key'
    name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: '/bin/bash'
    args:
      - '-c'
      - |
        gcloud secrets versions access --secret=SWCldfltBrowserExtensionSelfHostKey latest > ./cldflt-extension-pk.pem

  - id: Deploy self hosted extension to latest
    name: node:$_NODE_VERSION
    entrypoint: /bin/bash
    env:
      - "BRANCH_NAME=$BRANCH_NAME"
      - "EXTENSION_ID=$_EXTENSION_ID"
    args:
      - '-c'
      - |
        if [[ "$BRANCH_NAME" == "main" || "$BRANCH_NAME" == "develop" || "$BRANCH_NAME" == release/* ]]; then
          npm run deploy-self-hosted-latest
        else
          echo "Skipping deployment to latest for branch $BRANCH_NAME"
        fi

  - id: 'Extract self host data'
    name: node
    entrypoint: /bin/bash
    args:
      - '-c'
      - |
        node ./.ci/node/extract-self-host-data.js

  - id: Package extension
    name: node
    entrypoint: /bin/bash
    args:
      - '-c'
      - |
        ./.ci/scripts/PackExtension.sh "$BRANCH_NAME"

  - id: Upload packed extension
    name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: /bin/bash
    args:
      - '-c'
      - |
        ./.ci/scripts/UploadPackedExtension.sh "$_UPLOAD_GS_PATH"

  - id: Zip dist folder
    name: ubuntu
    script: |
      #!/usr/bin/env bash
      apt-get update
      apt-get install -y zip
      zip -r dist.zip dist/

artifacts:
  objects:
    location: $_UPLOAD_GS_PATH
    paths: ['./dist.zip']

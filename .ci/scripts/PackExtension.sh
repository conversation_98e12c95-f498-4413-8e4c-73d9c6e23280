#!/bin/bash

# Packs the extension if the build is running on a release branch.
# The crx and updates.xml file will be created into ./self-host-builds.

RELEASE_BRANCH_NAME="$(cat ./.ci/temp/data/releaseBranch)"

BRANCH_NAME="$1"

if [[ "$BRANCH_NAME" == "$RELEASE_BRANCH_NAME" ]]; then
	NEW_VERSION="$(cat ./.ci/temp/data/version)"

	echo "Packing extension with the version $NEW_VERSION"
	# Package the extension into a crx file. Uses the pem we got from GCP secret storage.
	node ./.ci/node/pack-crx.js

	mkdir ./self-host-builds

	BUILD_FILE_NAME=cldflt-browser-extension-$NEW_VERSION

	echo "Creating updates xml file."
	VERSION=$NEW_VERSION node ./.ci/node/create-update-xml.js

	# Move the crx into the folder with the updates xml file.
	mv ./dist.crx ./self-host-builds/$BUILD_FILE_NAME.crx
	echo "Extension has been packaged succesfully"
else
	echo "Not the release branch, skipping packaging the extension\n"
fi

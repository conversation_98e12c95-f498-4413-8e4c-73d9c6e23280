#!/bin/bash

# Uploads the crx and updates.xml files to the GCP storage bucket.

UPLOAD_GS_PATH=$1

if [ -d ./self-host-builds ]; then
	VERSION="$(cat ./.ci/temp/data/version)"
	UPDATES_GS_PATH="$UPLOAD_GS_PATH/self-host/updates.xml"
	CRX_GS_PATH="$UPLOAD_GS_PATH/self-host/cldflt-browser-extension-$VERSION.crx"

	echo "Starting to upload the self host files to $UPLOAD_GS_PATH."
	gcloud storage cp ./self-host-builds/updates.xml $UPDATES_GS_PATH
	gcloud storage cp ./self-host-builds/cldflt-browser-extension-$VERSION.crx $CRX_GS_PATH

	# Update the cache-control of the uploaded files to no-store
	# gcloud storage objects update
	gcloud storage objects update $UPDATES_GS_PATH --cache-control=no-store
	gcloud storage objects update $CRX_GS_PATH --cache-control=no-store
else
	echo "No package found, skipping uploading the packed extension."
fi

# compiled output
/dist
/tmp
/out-tsc
/.webstore
/.self-hosting
dist.crx
dist.pem
/self-hosting-mv2/output

# dependencies
/node_modules

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

/.vs

# System Files
.DS_Store
Thumbs.db

.provisioning/
.browser-data/
.chrome-for-testing-cache/

firestore-debug.log
.vscode/settings.json

coverage/
/.content-aware-temp

import CopyPlugin from 'copy-webpack-plugin';
import path from 'path';
import TerserPlugin from 'terser-webpack-plugin';
import webpack from 'webpack';

import npmPackageFile from './package.json';
import telemetryConnectionStrings from './telemetry-connection-strings.json';
import { IwfListWebpackPlugin } from './scripts/utilities/IwfListWebpackPlugin';

const sourceDir = path.resolve(__dirname, 'src').replace(/\\/g, '/');
const modulesDir = path.resolve(__dirname, 'node_modules').replace(/\\/g, '/');
const outputDir = path.resolve(__dirname, 'dist').replace(/\\/g, '/');

/**
 * Convert an array of permissions from mv3 to mv2.
 */
const convertPermissionsToMv2 = (input: string[]): string[] => {
  // Extension permissions which aren't valid in mv2.
  // This is not an exhaustive list and it may need to be updated.
  const incompatiblePermissions = [
    'declarativeNetRequest',
    'declarativeNetRequestFeedback',
    'declarativeNetRequestWithHostAccess',
    'scripting',
    'offscreen',
  ];

  const output: string[] = [];
  input.forEach((permission: string) => {
    // Omit permissions which aren't compatible with mv2 at all.
    if (incompatiblePermissions.includes(permission)) {
      return;
    }

    // Convert permissions to an mv2 equivalent where appropriate.
    if (permission === 'action') {
      output.push('browser_action');
      return;
    }

    // Keep all other permissions unmodified.
    output.push(permission);
  });
  return output;
};

/**
 * Take an mv3 manifest file object and convert it to be mv2 compatible.
 * @param manifest The manifest file object to convert. It is modified in place.
 */
const convertManifestToMv2 = (manifest: chrome.runtime.Manifest): void => {
  // To make type wrangling easier, use type aliases for each manifest version.
  // Note that these both refer to the original underlying manifest object.
  const mv2 = manifest as chrome.runtime.ManifestV2;
  const mv3 = manifest as chrome.runtime.ManifestV3;

  if (manifest.manifest_version === 2) {
    // Nothing to do.
    return;
  }

  if (manifest.manifest_version !== 3) {
    throw new Error('Manifest file has unrecognised manifest_version. Cannot convert it to mv2');
  }

  mv2.manifest_version = 2;

  // Allow the mv2 build to run on any Chrome version.
  delete mv2.minimum_chrome_version;

  if (mv2.permissions !== undefined) {
    mv2.permissions = convertPermissionsToMv2(mv3.permissions as string[]);
  }

  if (mv2.optional_permissions !== undefined) {
    mv2.optional_permissions = convertPermissionsToMv2(mv3.optional_permissions as string[]);
  }

  // "host_permissions" were part of "permissions" in mv2.
  if (mv3.host_permissions !== undefined && mv3.host_permissions.length > 0) {
    if (mv2.permissions === undefined) {
      mv2.permissions = [];
    }

    mv3.host_permissions.forEach((p) => {
      mv2.permissions?.push(p as chrome.runtime.ManifestPermissions);
    });

    delete mv3.host_permissions;
  }

  // The service worker entry point was specified as a background script in mv2.
  if (mv3.background !== undefined) {
    mv2.background = {
      scripts: [mv3.background.service_worker],
      // Note: We're just assuming it needs to be persistent.
      persistent: true,
    };
  }

  // "browser_action" in mv2 became "action" in mv3.
  if (mv3.action !== undefined) {
    mv2.browser_action = mv3.action;
    delete mv3.action;
  }

  // mv3 added more information to "content_security_policy".
  if (mv3.content_security_policy !== undefined) {
    mv2.content_security_policy = mv3.content_security_policy.extension_pages ?? '';
  }

  // mv3 added more information to "web_accessible_resources".
  if (mv3.web_accessible_resources !== undefined) {
    const mv3Resources = mv3.web_accessible_resources;
    mv2.web_accessible_resources = [];
    mv3Resources.forEach((element) => {
      element.resources.forEach((resource) => mv2.web_accessible_resources?.push(resource));
    });
  }
};

/**
 * Look up the telemetry connection string which corresponds to the given backend.
 *
 * @param backend Identifies the backend we're connecting to. Expected: 'dev', 'qa', or 'prd'.
 * @returns The connection string for the specified backend.
 * @see telemetry-connection-string.json
 */
const getTelemetryConnectionString = (backend: string): string => {
  if (typeof backend !== 'string' || backend === '') {
    console.warn('Backend not specified. Defaulting to dev.');
    backend = 'dev';
  }

  if (!Object.prototype.hasOwnProperty.call(telemetryConnectionStrings, backend)) {
    throw new Error('Telemetry connection string not found for specified backend.');
  }

  const tcs = (telemetryConnectionStrings as any)[backend];
  if (typeof tcs !== 'string' || tcs === '') {
    throw new Error('Telemetry connection string for specified backend is empty or invalid.');
  }

  return tcs;
};

module.exports = (env: any, argv: any) => {
  // Our common configuration used in development and production.
  const config: webpack.Configuration = {
    node: false,
    context: sourceDir,
    entry: {
      // The entry point for the extension service worker:
      main: 'main.ts',

      // The content script which is injected into each web-page:
      'content-script': 'content-script.ts',

      // Scripts used by the internal webpages.
      'views/block-page': 'views/block-page.ts',
      'views/diagnostics': 'views/diagnostics.ts',
      'views/realtime-filtering-logs': 'views/realtime-filtering-logs.ts',
      'views/offscreen-document': 'views/offscreen-document.ts',
    },

    module: {
      rules: [
        {
          test: /\.js$/,
          enforce: 'pre',
          use: ['source-map-loader'],
          exclude: /node_modules/,
        },
        // Transpile Typescript files.
        {
          test: /\.ts?$/,
          use: {
            loader: 'ts-loader',
            options: {
              onlyCompileBundledFiles: true,
            },
          },
          exclude: /node_modules/,
        },
      ],
    },
    resolve: {
      extensions: ['.tsx', '.ts', '.js'],
      modules: [sourceDir, modulesDir],
      fallback: {
        crypto: false,
      },
    },
    // Put the transpiled output in the "dist" folder, retaining the original name.
    output: {
      filename: '[name].js',
      path: outputDir,
      clean: true,
    },

    plugins: [
      // Build IWF list before compilation starts
      new IwfListWebpackPlugin({
        sourceDir: 'src/_iwflist',
        outputFile: 'src/mini-blocklist-data/iwflist.ts',
        force: false,
      }),
      new webpack.DefinePlugin({
        // These values are stringified because they are used as literal replacements.
        // See the documentation for Webpack DefinePlugin.
        EXPOSE_APP_IN_CONSOLE: JSON.stringify(argv.mode === 'development' || env.backend === 'qa'),
        TELEMETRY_CONNECTION_STRING: JSON.stringify(getTelemetryConnectionString(env.backend)),
      }),
      new CopyPlugin({
        patterns: [
          // Copy non-code files over to the output folder.
          {
            from: '**/*',
            globOptions: {
              ignore: [
                // Ignore code files
                '**/*.js',
                '**/*.mjs',
                '**/*.ts',

                // Ignore dot files, such as .gitignore.
                '**/.*',

                // Ignore the extension manifest file as that's handled separately below.
                'manifest.json',

                // Ignore the extension config file. It's imported directly into code files at build
                //  time so it doesn't need to be copied to the build output separately.
                'config.json',
              ],
            },
          },

          // Copy the manifest file to the output folder.
          // While doing that, set the extension version number to the same value as the npm package
          //  version.
          {
            from: 'manifest.json',
            transform: (content) => {
              const manifest = JSON.parse(content.toString());
              manifest.version = npmPackageFile.version;

              // For non-production builds, append the environment identifier to the extension name.
              if (env.backend !== 'prd') {
                manifest.name = `${manifest.name as string} (${(
                  env.backend as string
                ).toUpperCase()})`;
              }

              if (env.manifest_version === '2') {
                console.log('Converting manifest file to mv2.');
                convertManifestToMv2(manifest);
              }
              return JSON.stringify(manifest, undefined, 2);
            },
          },
        ],
      }),
    ],
    optimization: {
      minimize: true,
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            keep_classnames: true,
            keep_fnames: true,
          },
        }),
      ],
    },

    // Prevent Webpack from generating warnings about the sizes of assets and entry-points. They
    //  aren't relevant to an extension.
    performance: { hints: false },
  };

  // Customise the configuration depending on build mode.
  if (argv.mode === 'development') {
    config.devtool = 'source-map';
    if (config.optimization === undefined) {
      config.optimization = {};
    }
    config.optimization.minimize = false;
  } else if (argv.mode === 'production') {
    config.devtool = false;
  }

  return config;
};

import type { Config } from '@jest/types';
import path from 'path';

const config: Config.InitialOptions = {
  // This allows us to resolve modules relative to the root of the source folder.
  modulePaths: [path.resolve(__dirname, 'src')],
  preset: 'ts-jest/presets/js-with-ts',
  resetMocks: false,
  clearMocks: true,
  setupFilesAfterEnv: [
    'jest-extended/all',
    '<rootDir>/src/test-helpers/chrome-api.ts',
    '<rootDir>/src/test-helpers/global-mocks.ts',
  ],
  testEnvironment: 'node',
  verbose: false,
  transformIgnorePatterns: ['node_modules/(?!ip-regex/)'],
  globals: {
    // The port number here must match the one specified in "global-setup.ts".
    TEST_SERVER_URL: 'http://127.0.0.1:3000',
  },
  globalSetup: '<rootDir>/src/test-helpers/global-setup.ts',
  globalTeardown: '<rootDir>/src/test-helpers/global-teardown.ts',
};

export default config;

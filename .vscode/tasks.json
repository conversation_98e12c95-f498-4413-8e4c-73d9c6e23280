{
  // See https://go.microsoft.com/fwlink/?LinkId=733558 
  // for the documentation about the tasks.json format
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Build DEV configuration",
      "type": "npm",
      "script": "build:dev",
      "problemMatcher": "$tsc",
      "group": {
        "kind": "build",
        "isDefault": true
      }
    },
    {
      "label": "Build DEV configuration (mv3)",
      "type": "npm",
      "script": "build:dev-mv3",
      "problemMatcher": "$tsc",
      "group": "build",
      "hide": true
    },
    {
      "label": "Build DEV configuration (mv2)",
      "type": "npm",
      "script": "build:dev-mv2",
      "problemMatcher": "$tsc",
      "group": "build",
      "hide": true
    },
    {
      "label": "Build QA configuration",
      "type": "npm",
      "script": "build:qa",
      "problemMatcher": "$tsc",
      "group": {
        "kind": "build",
        "isDefault": false
      }
    },
    {
      "label": "Build QA configuration (mv3)",
      "type": "npm",
      "script": "build:qa-mv3",
      "problemMatcher": "$tsc",
      "group": "build",
      "hide": true
    },
    {
      "label": "Build QA configuration (mv2)",
      "type": "npm",
      "script": "build:qa-mv2",
      "problemMatcher": "$tsc",
      "group": "build",
      "hide": true
    },
    {
      "label": "Build PRD configuration",
      "type": "npm",
      "script": "build:prd",
      "problemMatcher": "$tsc",
      "group": {
        "kind": "build",
        "isDefault": false
      }
    },
    {
      "label": "Build PRD configuration (mv3)",
      "type": "npm",
      "script": "build:prd-mv3",
      "problemMatcher": "$tsc",
      "group": "build",
      "hide": true
    },
    {
      "label": "Build PRD configuration (mv2)",
      "type": "npm",
      "script": "build:prd-mv2",
      "problemMatcher": "$tsc",
      "group": "build",
      "hide": true
    },
    {
      "label": "Run unit tests",
      "type": "npm",
      "script": "test",
      "problemMatcher": "$tsc",
      "group": {
        "kind": "test",
        "isDefault": true
      }
    },
    {
      "label": "Fix code formatting",
      "type": "npm",
      "script": "format",
      "group": "none"
    },
    {
      "label": "Run linter",
      "type": "npm",
      "script": "lint",
      "problemMatcher": "$eslint-stylish",
      "group": "none"
    }
  ]
}